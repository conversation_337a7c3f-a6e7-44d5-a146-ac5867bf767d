ALTER TABLE `iris`.`user` ADD COLUMN `firstname` VA<PERSON><PERSON><PERSON>(50) DEFAULT 'NA' NOT NULL AFTER `alternate_phone`, 
ADD COLUMN `lastname` VARCHAR(50) DEFAULT 'NA' NOT NULL AFTER `firstname`, 
ADD COLUMN `zipcode` VARCHAR(30) DEFAULT 'NA' NOT NULL AFTER `lastname`,
ADD COLUMN `signuptoken` VARCHAR(150) DEFAULT 'NA' NOT NULL AFTER `zipcode`, 
ADD COLUMN `signuptype_id` BIGINT(20)  DEFAULT '1' NOT NULL AFTER `signuptoken`, 
ADD COLUMN `city` VARCHAR(30) DEFAULT 'NA' NOT NULL AFTER `signuptype_id`, 
ADD COLUMN `state` VARCHAR(30) DEFAULT 'NA' NOT NULL AFTER `city`; 

ALTER TABLE `iris`.`user` ADD COLUMN `country` VARCHAR(30) DEFAULT 'NA' NOT NULL AFTER `state`;

alter table `iris`.`user`     add column `createdon` datetime DEFAULT '1753-01-01 00:00:00' NOT NULL after `state`,     add column `updatedon` datetime DEFAULT '1753-01-01 00:00:00' NOT NULL after `createdon`,     add column `isverified` tinyint(1) DEFAULT '0' NOT NULL after `updatedon`;
alter table `iris`.`user`     add column `imageurl` varchar(255) DEFAULT 'NA' NOT NULL after `isverified`;
ALTER TABLE `iris`.`user` ADD CONSTRAINT fk_signuptype FOREIGN KEY (signuptype_id) REFERENCES signuptype(id);

DROP TABLE `test_db`;
CREATE TABLE `test_db` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `check_service` varchar(50) NOT NULL DEFAULT 'NA',
  `server_ip` varchar(50) NOT NULL DEFAULT 'NA',
  PRIMARY KEY (`id`)
);

CREATE TABLE `device_replaced` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL DEFAULT '0',
  `meid` varchar(30) NOT NULL DEFAULT 'NA',
  `action` tinyint(4) NOT NULL DEFAULT '0',
  `inserted_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `updated_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `Unique` (`user_id`,`meid`)
);

CREATE TABLE `device_replaced_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL DEFAULT '0',
  `old_meid` varchar(30) NOT NULL DEFAULT 'NA',
  `new_meid` varchar(30) NOT NULL DEFAULT 'NA',
  `registered_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `is_return_received` tinyint(1) NOT NULL DEFAULT '0',
  `tracking_no` varchar(100) NOT NULL DEFAULT '0',
  `device_received_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `action` varchar(20) NOT NULL DEFAULT 'NA',
  PRIMARY KEY (`id`),
  UNIQUE KEY `Unique` (`user_id`,`old_meid`,`new_meid`)
);



insert into `oauth_client_details` (`client_id`, `resource_ids`, `client_secret`, `scope`, `authorized_grant_types`, `web_server_redirect_uri`, `authorities`, `access_token_validity`, `refresh_token_validity`, `additional_information`, `autoapprove`) values('app','oauth2-resource','app','read','authorization_code,refresh_token,password','http://localhost:8090/showdata/,https://layla.amazon.com/api/skill/link/M18BLXX6V6826P,https://pitangui.amazon.com/api/skill/link/M18BLXX6V6826P,https://alexa.amazon.co.jp/api/skill/link/M18BLXX6V6826P,https://inapp.mywaggle.com/,https://wagglepet.test-app.link/alexa/,https://wagglepet.app.link/alexa/','ROLE_read','1080','-1',NULL,NULL);
insert into `oauth_client_details` (`client_id`, `resource_ids`, `client_secret`, `scope`, `authorized_grant_types`, `web_server_redirect_uri`, `authorities`, `access_token_validity`, `refresh_token_validity`, `additional_information`, `autoapprove`) values('web','oauth2-resource','web','read,write,delete','authorization_code,refresh_token,password','http://localhost:8090/showdata/,https://layla.amazon.com/api/skill/link/M18BLXX6V6826P,https://pitangui.amazon.com/api/skill/link/M18BLXX6V6826P,https://alexa.amazon.co.jp/api/skill/link/M18BLXX6V6826P,https://inapp.mywaggle.com/,https://wagglepet.test-app.link/alexa/,https://wagglepet.app.link/alexa/','ROLE_read','600','-1',NULL,NULL);



CREATE TABLE `old_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `meid` varchar(20) NOT NULL DEFAULT 'NA',
  `qrc` varchar(20) NOT NULL DEFAULT 'NA',
  PRIMARY KEY (`id`),
  UNIQUE KEY `meid,qrc` (`meid`,`qrc`),
  KEY `QRC` (`qrc`)
)

CREATE TABLE `evalidation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `entity_id` varchar(100) NOT NULL DEFAULT 'NA',
  `entity_password` varchar(100) NOT NULL DEFAULT 'NA',
  `updated_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_key` (`entity_id`)
)





Scalability Release QRY  - Execute once verified by Dev team 

ALTER TABLE `iris`.`companyconfig` ADD COLUMN `custom_plan_enable` TINYINT(1) DEFAULT 0 NOT NULL ;

CREATE ALGORITHM=UNDEFINED DEFINER=`nimble`@`%` SQL SECURITY DEFINER VIEW `iris`.`v_feedback_rating` AS 
select `UT`.`id` AS `id`,`F`.`link` AS `link`,`UT`.`show_form` AS `show_form`,`UT`.`close_form` AS `close_form`,`UT`.`retry_count` AS `retry_count`,`F`.`next_show_interval` AS `next_show_interval`,`UT`.`updatedon` AS `updatedon`,`F`.`id` AS `formid`,`F`.`createdon` AS `createdon`,`F`.`expiredon` AS `expiredon`,`F`.`category` AS `category`,`UF`.`userid` AS `userid`,`F`.`enable` AS `enable` from ((`iris`.`user_feedback` `UF` left join `iris`.`user_feedback_transaction` `UT` on(((`UT`.`userid` = `UF`.`userid`) and (`UT`.`formid` = `UF`.`formid`)))) join `iris`.`feedback_form` `F` on((`F`.`id` = `UF`.`formid`))) union select `UT`.`id` AS `id`,`F`.`link` AS `link`,`UT`.`show_form` AS `show_form`,`UT`.`close_form` AS `close_form`,`UT`.`retry_count` AS `retry_count`,`F`.`next_show_interval` AS `next_show_interval`,`UT`.`updatedon` AS `updatedon`,`F`.`id` AS `formid`,`F`.`createdon` AS `createdon`,`F`.`expiredon` AS `expiredon`,`F`.`category` AS `category`,`UT`.`userid` AS `userid`,`F`.`enable` AS `enable` from (`iris`.`feedback_form` `F` left join `iris`.`user_feedback_transaction` `UT` on((`UT`.`formid` = `F`.`id`))) where ((`F`.`enable` = 1) and (`F`.`show_alluser` = 1));


CREATE ALGORITHM=UNDEFINED DEFINER=`nimble`@`%` SQL SECURITY DEFINER VIEW `niom`.`v_valid_meids_ordermap` AS (
select  `I`.`id` AS `id`,  `I`.`meid` AS `meid`,  `I`.`mac_id` AS `mac_id`,  `I`.`mdn` AS `mdn`,  `I`.`sensoravailable` AS `sensoravailable`,  `I`.`probetype` AS `probetype`,  `I`.`qrc` AS `qrc`,  `I`.`serialnumber` AS `serialnumber`,  `I`.`devicemodelnumber` AS `devicemodelnumber`,  `I`.`devicestate_id` AS `devicestate_id`,  `I`.`location_id` AS `location_id`,  `I`.`order_id` AS `order_id`,  `I`.`datetime` AS `datetime`,  `I`.`sim_no` AS `sim_no`,  `I`.`device_status` AS `device_status`,  `I`.`action` AS `action`,  `I`.`plan_id` AS `plan_id`,  `I`.`sim_vendor` AS `sim_vendor`,  `I`.`firmware_version` AS `firmware_version`,  `I`.`hardware_version` AS `hardware_version`,  `I`.`fota_version` AS `fota_version`,  `I`.`curr_fota_version` AS `curr_fota_version` from (`niom`.`inventory` `I`  left join `niom`.`ordermap` `OM`  on ((`OM`.`meid` = `I`.`meid`))) where ((`I`.`devicestate_id` in('2','8','9'))  and isnull(`OM`.`meid`)));

ALTER TABLE `pet_profile`  ADD  KEY `user_id` (`user_id`);
ALTER TABLE `alert`  ADD  KEY `cmp_ack` (`ack`, `cmp_id`);
ALTER TABLE `userpushnotifications`  ADD  KEY `IDX_userid` (`userId`);
ALTER TABLE `reminder_views_datatils`   ADD  KEY `IDX_userid` (`userId`, `viewcomplete`);
ALTER TABLE `badge_stage` ADD  KEY `IDX_badgeid` (`badge_id`);
ALTER TABLE `feature` ADD  UNIQUE INDEX `UK_featurecode` (`feature_code`);
ALTER TABLE `user_feedback_transaction` ADD  KEY `IDX_uft_userid` (`userid`);
ALTER TABLE `ordermappingdetails` ADD  KEY `IDX_om_userid` (`user_id`);
ALTER TABLE `feedback_form`  ADD  KEY `IDX_category` (`category`);
ALTER TABLE `iris`.`healthreport_history` ADD  KEY `IDX_hr_userid` (`user_id`);
CREATE INDEX idx_remindername ON `reminder_details` (reminder_name);
CREATE INDEX idx_userid ON `reminder_details` (userId);
CREATE INDEX idx_enable ON `reminder_repeattype` (`enable`);

