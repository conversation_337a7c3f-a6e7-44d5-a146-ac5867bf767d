ALTER TABLE `iris`.`assetmodel` ADD COLUMN `battery_offset` FLOAT DEFAULT 0 NOT NULL , ADD COLUMN `charging_offset` FLOAT DEFAULT 0 NOT NULL , ADD COLUMN `fullcharge_offset` FLOAT DEFAULT 0 NOT NULL ;
ALTER TABLE `iris`.`gateway` ADD COLUMN `default_temp_calib` FLOAT DEFAULT 0 NOT NULL , ADD COLUMN `default_battery_offset` FLOAT DEFAULT 0 NOT NULL , ADD COLUMN `default_charging_offset` FLOAT DEFAULT 0 NOT NULL , ADD COLUMN `default_fullcharge_offset` FLOAT DEFAULT 0 NOT NULL ;
ALTER TABLE `iris`.`temp_calib_status` ADD COLUMN `battery_offset_status` VARCHAR(100) DEFAULT 'NA' NOT NULL , ADD COLUMN `charging_offset_status` VARCHAR(100) DEFAULT 'NA' NOT NULL , ADD COLUMN `fullcharge_offset_status` VARCHAR(100) DEFAULT 'NA' NOT NULL ;


UPDATE `iris`.`assetmodel` SET `temp_calib` = '-1' , `battery_offset` = '0.0' , `charging_offset` = '-1.5' , `fullcharge_offset` = '-1.5' WHERE `id` = '32'; 
UPDATE `iris`.`assetmodel` SET `temp_calib` = '-0.8' , `battery_offset` = '0.3' , `charging_offset` = '-1.2' , `fullcharge_offset` = '-1.4' WHERE `id` = '31'; 
UPDATE `iris`.`assetmodel` SET `temp_calib` = '-1.2' , `battery_offset` = '-0.5' , `charging_offset` = '1.9' , `fullcharge_offset` = '-2.3' WHERE `id` = '27'; 
UPDATE `iris`.`assetmodel` SET `temp_calib` = '-0.8' , `battery_offset` = '0.0' , `charging_offset` = '2.6' , `fullcharge_offset` = '-2.9' WHERE `id` = '29'; 
UPDATE `iris`.`assetmodel` SET `temp_calib` = '-0.6' ,`battery_offset` = '0.3' , `charging_offset` = '1.6', `fullcharge_offset` = '-2.4' WHERE `id` = '28';