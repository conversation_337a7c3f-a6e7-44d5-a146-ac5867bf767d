ALTER TABLE `plan_feature`  DROP COLUMN monitortype_id;

ALTER TABLE `plan_feature`  DROP COLUMN device_config;
  
ALTER TABLE `plan_feature`   DROP COLUMN `plan_period_id`;

ALTER TABLE `user_feature`  DROP COLUMN monitortype_id;

ALTER TABLE `user_feature`  DROP COLUMN device_config;

  ALTER TABLE `pushnotifications` ADD KEY `IDX_expiryon` (`expiryon`); 
  
  ALTER TABLE `plan_to_period` ADD  KEY `IDX_cb_planid` (`chargebee_planid`);
  
  drop TABLE `plan_offer`; 
CREATE TABLE `plan_offer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `plan_period_id` bigint(20) NOT NULL,
  `coupon_id` varchar(100) NOT NULL DEFAULT 'NA',
  `offer_desc1` text NOT NULL,
  `offer_desc2` text NOT NULL,
  `enable` tinyint(4) NOT NULL DEFAULT '1',
  `expiry_date` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_coupon` (`plan_period_id`,`coupon_id`)
)

insert into `plan_offer` (`id`, `plan_period_id`, `coupon_id`, `offer_desc1`, `offer_desc2`, `enable`, `expiry_date`) values('1','50','KALAI-TEST','<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;}</style><div class=\'container\' style=\'padding-left: 0px;padding-right: 0px;\'><div class=\'row custm_cont_2\'><div class=\'col-md-12 col-12 col-xs-12\'><table class=\'table\'><tbody><tr><td><p style=\'color:#000;font-size: 12px;\'><b>Half Yearly:</b>10%off</p><p style=\'color:#6a6a6a;font-size: 12px; margin-top: -10px;\'>Use Coupon:<b style=\'color:#ec5757;\'>WAGGLE10</b></p></td></tr></tbody></table></div></div></div>','<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;}</style><div class=\'container\' style=\'padding-left: 0px;padding-right: 0px;\'><div class=\'row custm_cont_2\'><div class=\'col-md-12 col-12 col-xs-12\'><table class=\'table\'><tbody><tr><td><center><p style=\'color:#000;font-size: 12px;padding-left: 5px;\'><b>Coupon Applied Successfully</b></p><p style=\'color:#6a6a6a;font-size: 12px; margin-top: -10px;padding-left: 5px;\'>Waggler Half-Yearly with 10% offer</p></center></td></tr></tbody></table></div></div></div>','1','2022-12-30 16:32:04');
insert into `plan_offer` (`id`, `plan_period_id`, `coupon_id`, `offer_desc1`, `offer_desc2`, `enable`, `expiry_date`) values('2','51','KALAI-TEST','<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;}</style><div class=\'container\' style=\'padding-left: 0px;padding-right: 0px;\'><div class=\'row custm_cont_2\'><div class=\'col-md-12 col-12 col-xs-12\'><table class=\'table\'><tbody><tr><td><p style=\'color:#000;font-size: 12px;\'><b>Yearly:</b>10%off</p><p style=\'color:#6a6a6a;font-size: 12px; margin-top: -10px;\'>Use Coupon:<b style=\'color:#ec5757;\'>WAGGLE20</b></p></td></tr></tbody></table></div></div></div>','<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;}</style><div class=\'container\' style=\'padding-left: 0px;padding-right: 0px;\'><div class=\'row custm_cont_2\'><div class=\'col-md-12 col-12 col-xs-12\'><table class=\'table\'><tbody><tr><td><center><p style=\'color:#000;font-size: 12px;padding-left: 5px;\'><b>Coupon Applied Successfully</b></p><p style=\'color:#6a6a6a;font-size: 12px; margin-top: -10px;padding-left: 5px;\'>Waggler Yearly with 20% offer</p></center></td></tr></tbody></table></div></div></div>','1','2022-12-30 16:32:04');
insert into `plan_offer` (`id`, `plan_period_id`, `coupon_id`, `offer_desc1`, `offer_desc2`, `enable`, `expiry_date`) values('3','52','KALAI-TEST','<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;}</style><div class=\'container\' style=\'padding-left: 0px;padding-right: 0px;\'><div class=\'row custm_cont_2\'><div class=\'col-md-12 col-12 col-xs-12\'><table class=\'table\'><tbody><tr><td><p style=\'color:#000;font-size: 12px;\'><b>2 Year:</b>30%off</p><p style=\'color:#6a6a6a;font-size: 12px; margin-top: -10px;\'>Use Coupon:<b style=\'color:#ec5757;\'>WAGGLE30</b></p></td></tr></tbody></table></div></div></div>','<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;}</style><div class=\'container\' style=\'padding-left: 0px;padding-right: 0px;\'><div class=\'row custm_cont_2\'><div class=\'col-md-12 col-12 col-xs-12\'><table class=\'table\'><tbody><tr><td><center><p style=\'color:#000;font-size: 12px;padding-left: 5px;\'><b>Coupon Applied Successfully</b></p><p style=\'color:#6a6a6a;font-size: 12px; margin-top: -10px;padding-left: 5px;\'>Waggler 2 Year with 30% offer</p></center></td></tr></tbody></table></div></div></div>','1','2022-12-30 16:32:04');

scalability qry:

ALTER TABLE `pet_profile`  ADD  KEY `user_id` (`user_id`); 
 
 ALTER TABLE `alert`  ADD  KEY `cmp_ack` (`ack`, `cmp_id`); - 68 after 1
  
 ALTER TABLE `userpushnotifications`  ADD  KEY `IDX_userid` (`userId`);  
  
  ALTER TABLE `reminder_views_datatils`   ADD  KEY `IDX_userid` (`userId`, `viewcomplete`); -11573 after 5
  
  ALTER TABLE `badge_stage` ADD  KEY `IDX_badgeid` (`badge_id`);

  ALTER TABLE `feature` ADD  UNIQUE INDEX `UK_featurecode` (`feature_code`);

  ALTER TABLE `user_feedback_transaction` ADD  KEY `IDX_uft_userid` (`userid`);
  
  ALTER TABLE `ordermappingdetails` ADD  KEY `IDX_om_userid` (`user_id`);
  
  ALTER TABLE `feedback_form`  ADD  KEY `IDX_category` (`category`);
 
  ALTER TABLE `healthreport_history`   ADD  KEY `IDX_hr_userid` (`user_id`);
  
  
  INSERT INTO `iris`.`transport_type` (`id`, `name`, `enable`) VALUES (NULL, 'Amazon PinPoint', '0');