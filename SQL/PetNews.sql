CREATE TABLE `petnews` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `android_shortnews` text NOT NULL,
  `iphone_shortnews` text NOT NULL,
  `ipad_shortnews` text NOT NULL,
  `android_longnews` text NOT NULL,
  `iphone_longnews` text NOT NULL,
  `ipad_longnews` text NOT NULL,
  `android_imgurl` varchar(250) NOT NULL DEFAULT 'NA',
  `iphone_imgurl` varchar(250) NOT NULL DEFAULT 'NA',
  `ipad_imgurl` varchar(250) NOT NULL DEFAULT 'NA',
  `enable` tinyint(1) NOT NULL DEFAULT '1',
  `start_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `expired_on` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `created_on` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `updated_on` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `dmf_snews` text NOT NULL,
  `dmf_lnews` text NOT NULL,
  `f_snews` text NOT NULL,
  `f_lnews` text NOT NULL,
  PRIMARY KEY (`id`)
)

ALTER TABLE `iris`.`petnews` ADD COLUMN `show_CTA` TINYINT(1) DEFAULT 0 NOT NULL; 