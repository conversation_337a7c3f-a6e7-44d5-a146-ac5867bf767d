ALTER TABLE `plan_to_period`  ADD COLUMN `cb_addon_id` VARCHAR(255) DEFAULT 'NA' NOT NULL ;

ALTER TABLE `plan_to_period` ADD COLUMN `cb_coupon_id` VARCHAR(100) DEFAULT 'NA' NOT NULL ;

CREATE TABLE `device_replaced` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL DEFAULT '0',
  `meid` varchar(30) NOT NULL DEFAULT 'NA',
  `is_replaced` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `Unique` (`user_id`,`meid`)
);

CREATE TABLE `device_replaced_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL DEFAULT '0',
  `old_meid` varchar(30) NOT NULL DEFAULT 'NA',
  `new_meid` varchar(30) NOT NULL DEFAULT 'NA',
  `registered_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `is_return_received` tinyint(1) NOT NULL DEFAULT '0',
  `tracking_no` varchar(100) NOT NULL DEFAULT '0',
  `device_received_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `Unique` (`user_id`,`old_meid`,`new_meid`)
);	