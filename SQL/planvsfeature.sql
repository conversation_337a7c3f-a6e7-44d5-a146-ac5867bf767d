insert into `feature_type` (`id`, `type_name`) values('3','ALERT');
insert into `feature_type` (`id`, `type_name`) values('4','GENERAL');

ALTER TABLE `feature` CHANGE `type_id` `type_id` INT(11) DEFAULT 1 NOT NULL,
	ADD COLUMN `feature_code` VARCHAR(50) NOT NULL ;	
ALTER TABLE `feature`  ADD COLUMN `alerttype_id` BIGINT DEFAULT 0 NOT NULL;

UPDATE feature SET feature_code='VPM' WHERE id=1;
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('2','no_mobile','No of Mobile Configured','1','4','N_MOBILE','0');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('3','no_email','No of Email Configured','1','4','N_EMAIL','0');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('4','no_device','No of Device Configured','1','4','N_DEVICE','0');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('5','rpt_interval','Report Interval','1','4','RPT_INTERVAL','0');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('6','push_notification','Push Notification','1','4','PUSH_NOTIFY','0');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('7','temp_mail_count','Temperature Mail Alert','1','3','N_TEMP_MAIL','1');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('8','temp_sms_count','Temperature SMS Alert','1','3','N_TEMP_SMS','1');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('9','battery_mail_count_1','Battery Alert Mail Count','1','3','N_BAT_MAIL','2');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('10','battery__sms_count1','Battery Alert SMS Count','1','3','N_BAT_SMS','2');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('11','onbattery_mail_count','OnBattery Mail Count','1','3','N_ONBAT_MAIL','3');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('12','onbattery_sms_count','OnBattery SMS Count','1','3','N_ONBAT_SMS','3');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('13','geofence_mail_count','Geofence Mail Count','1','3','N_GEO_MAIL','4');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('14','geofence_sms_count','Geofence SMS Count','1','3','N_GEO_SMS','4');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('15','network_mail_count','Network Alert Mail Count','1','3','N_NETWORK_MAIL','11');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('16','network_sms_count','Network Alert SMS Count','1','3','N_NETWORK_SMS','11');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('17','humidity_mail_count','Humidity Alert Mail Count','1','3','N_HUMIDITY_MAIL','14');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('18','humidity_sms_count','Humidity Alert SMS','1','3','N_HUMIDITY_SMS','14');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('19','powerrecovery_mail_count','PowerRecovery Alert Mail Count','1','3','N_POWERRECOVERY_MAIL','17');
insert into `feature` (`id`, `feature_name`, `description`, `enable`, `type_id`, `feature_code`, `alerttype_id`) values('20','powerrecovery_sms_count','PowerRecovery Alert SMS Count','1','3','N_POWERRECOVERY_SMS','17');
	
ALTER TABLE `plan` 	ADD COLUMN `plan_ver` VARCHAR(10) DEFAULT 'V1' NOT NULL ;
	
ALTER TABLE `plan`  ADD COLUMN `feature_list` TEXT NULL ;

ALTER TABLE `plan`  ADD COLUMN `device_cnt` INT DEFAULT 0 NOT NULL;

ALTER TABLE `plan` CHANGE `plan_type` `plan_type` ENUM('Data-Plan','VPM-Plan','Add-On')  DEFAULT 'Data-Plan' NOT NULL; 

ALTER TABLE `plan_feature` ADD COLUMN `unlimited_cr` BOOLEAN DEFAULT 0 NOT NULL;  

ALTER TABLE `plan_feature`   ADD  UNIQUE INDEX `uk_plan_feature1` (`plan_id`, `feature_id`);

ALTER TABLE `user_feature` 	ADD COLUMN `remaining_limit` INT(11) DEFAULT 0 NOT NULL,
	ADD COLUMN `last_reset_on` DATETIME DEFAULT '1753-01-01 00:00:00' NOT NULL;
	
ALTER TABLE `user_feature` ADD COLUMN `feature_code` VARCHAR(50) NOT NULL ;

ALTER TABLE `user_feature`  ADD  UNIQUE INDEX `UK_featurecode` (`user_id`, `feature_code`);
	
ALTER TABLE `user_feature`  ADD COLUMN `unlimited_cr` BOOLEAN DEFAULT 0 NOT NULL ;

ALTER TABLE `user_feature`  ADD COLUMN `addon_limit` INT(11) DEFAULT 0 NOT NULL ;

ALTER TABLE `user` 	ADD COLUMN `plan_ver` VARCHAR(10) DEFAULT 'V1' NOT NULL ;

ALTER TABLE `plan_to_period` ADD COLUMN `plan_price` TEXT NULL;

ALTER TABLE `version_mapping` ADD COLUMN `plan_version` VARCHAR(5) DEFAULT 'V1' NOT NULL;

CREATE TABLE `addon` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cb_addonid` varchar(20) NOT NULL DEFAULT 'NA',
  `enable` tinyint(1) NOT NULL DEFAULT '1',
  `is_resetable` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_addon` (`cb_addonid`)
);
insert into `addon` (`id`, `cb_addonid`, `enable`, `is_resetable`) values('1','vetchat-monthly','1','1');


CREATE TABLE `addon_feature` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `addon_id` bigint(20) NOT NULL DEFAULT '0',
  `feature_id` bigint(20) NOT NULL,
  `enable` tinyint(1) NOT NULL DEFAULT '1',
  `txn_limit` int(11) NOT NULL DEFAULT '0',
  `extra_txn_limit` int(11) NOT NULL DEFAULT '0',
  `is_resetable` tinyint(1) NOT NULL DEFAULT '1',
  `unlimited_cr` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_addon_feature` (`addon_id`,`feature_id`)
);

insert into `addon_feature` (`id`, `addon_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `is_resetable`, `unlimited_cr`) values('1','1','1','1','99999999','0','1','1');

CREATE TABLE `credit_schedule_history` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `job_name` varchar(50) NOT NULL DEFAULT 'NA',
  `group_id` varchar(50) NOT NULL DEFAULT 'NA',
  `plan_name` varchar(255) NOT NULL DEFAULT 'NA',
  `features` varchar(50) NOT NULL DEFAULT 'NA',
  `Trigger_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `nextupdate_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `lastupdate_date` datetime DEFAULT '1753-01-01 00:00:00',
  `status` tinyint(1) NOT NULL DEFAULT '0',
  `msg` varchar(50) NOT NULL DEFAULT 'NA',
  PRIMARY KEY (`id`),
  UNIQUE KEY `Trigger_date` (`nextupdate_date`,`lastupdate_date`)
);

ALTER TABLE schedule_alert ADD COLUMN  `featurecode_MAIL` VARCHAR(100) DEFAULT 'NA' NOT NULL;

ALTER TABLE schedule_alert ADD COLUMN  `featurecode_SMS` VARCHAR(100) DEFAULT 'NA' NOT NULL;

ALTER TABLE schedule_alert ADD COLUMN `plan_ver` VARCHAR(20)  DEFAULT 'V1' NOT NULL;

insert into `plan` (`id`, `plan_name`, `description`, `enable`, `custom`, `orderno`, `display_msg`, `alert_setting`, `pricecompare_period`, `plan_type`, `plan_ver`, `feature_list`, `device_cnt`) values('23','Rover','1-Pet Monitor','1','0','3','NA','1','1','Data-Plan','V2','<center><table style="width: 100%; border-radius: 20px;-webkit-touch-callout: none;-webkit-user-select: none;"><tr style="background-color: #ece3e34d;  border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Alert</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">Temperature Alert</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Email & In-App Notifications</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">Unlimited</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Text Notifications</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">30 Temperature Alerts</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">No of Monitors</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">Up to 1</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Family & Friends</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">Up to 1
member</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Dashboard</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">Real-Time</p></div></div></td></tr></table></center>','1');
insert into `plan` (`id`, `plan_name`, `description`, `enable`, `custom`, `orderno`, `display_msg`, `alert_setting`, `pricecompare_period`, `plan_type`, `plan_ver`, `feature_list`, `device_cnt`) values('24','Wanderer','2-Pet Monitors','1','0','2','NA','1','1','Data-Plan','V2','<center><table style="width: 100%; border-radius: 20px;-webkit-touch-callout: none;-webkit-user-select: none;"><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Alerts</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: 5px;">Temperature & Humidity alerts</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Email & In-App Notifications</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">Unlimited</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Text Notifications</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: 6px;">30 Temperature Alerts & 30 Humidity Alerts</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">No of Monitors</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">Up to 2
</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Family & Friends</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">Add up to 2
members</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Dashboard</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">Real-Time</p></div></div></td></tr></table></center>','2');
insert into `plan` (`id`, `plan_name`, `description`, `enable`, `custom`, `orderno`, `display_msg`, `alert_setting`, `pricecompare_period`, `plan_type`, `plan_ver`, `feature_list`, `device_cnt`) values('25','Waggler','5-Pet Monitors','1','0','1','NA','1','1','Data-Plan','V2','<center><table style="width: 100%; border-radius: 20px;-webkit-touch-callout: none;-webkit-user-select: none;"><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Alerts</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">Temperature & Humidity Alert, </p><p style="color: #808080; margin-bottom: -16px;">Power Loss & Power Recovery Alert</p><p style="color: #808080; margin-bottom: -16px;">Low
Battery Alert &</p><p style="color: #808080;">Network Alert</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Text Notifications</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">Unlimited
(Text, Email</p><p style="color: #808080;"> & In-App Alerts)</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">No of Monitors</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">Up to 5</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Family & Friends</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;"> Add up to 5
members</p></div></div></td></tr><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;">Dashboard</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -16px;">Real-Time</p></div></div></td></tr></table><table style="width: 100%;  border-radius: 20px;-webkit-touch-callout: none;-webkit-user-select: none;"><tr style="background-color: #ece3e34d; border-radius: 20px;"><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 100%;"><p style="color: #E85B59; font-weight: bold; float: left;margin-left: 5px;">Additional
Features</p></div></div><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 33%;"><img
src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/planvsfeature/warranty.png"
style="height: 25px;"><p
style="color: #000; font-weight: bold; margin-left: 30px; margin-top: -23px; font-size: 12px;">Extended
Warranty</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 33%; margin-left: -10px;"><img
src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/planvsfeature/geofence.png"
style="height: 25px;"><p
style="color: #000; font-weight: bold; margin-left: 30px; margin-top: -23px; font-size: 12px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 34%; margin-left: 10px;"><img
src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/planvsfeature/vetchat.png"
style="height: 25px;"><p
style="color: #000; font-weight: bold; margin-left: 30px; margin-top: -23px; font-size: 12px;">WaggleVet</p></div></div><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 100%;"><p style="color: #808080; font-size: 10px; float: left; margin-top: -3px;margin-left: 7px;"><b>Note: </b>Geofencing Alert only applicable for Waggle Pet Monitor (GPS) device.</p></div></div></td></tr></table></center>','5');

insert into `plan_to_period` (`id`, `plan_id`, `sub_period_id`, `chargebee_planid`, `custom`, `enable`, `strike_price`, `ios_planid`, `ios_price`, `ios_showplan`, `free_trial_days`, `offer_desc`, `offer_id`, `offer_label`, `offer_content`, `cb_addon_id`, `cb_coupon_id`, `plan_price`) values('44','23','3','rover-half-yearly','0','1','0','NA','0','0','0',NULL,'0','NA',NULL,'NA','NA','<center ><p style="font-size: 32px;margin-bottom: -10px;color: #717171;font-family: Poppins;">$90</p><p style="font-size: 17px;margin-bottom: -10px;color:#004551;font-family: Poppins;font-weight: bolder;">Half Yearly</p><p style="font-size: 17px;margin-bottom: -10px;color: #717171;font-family: Poppins;font-weight: light;">$15 per month</p></center>');
insert into `plan_to_period` (`id`, `plan_id`, `sub_period_id`, `chargebee_planid`, `custom`, `enable`, `strike_price`, `ios_planid`, `ios_price`, `ios_showplan`, `free_trial_days`, `offer_desc`, `offer_id`, `offer_label`, `offer_content`, `cb_addon_id`, `cb_coupon_id`, `plan_price`) values('45','23','4','rover-yearly','0','1','0','NA','0','0','0',NULL,'0','NA',NULL,'NA','NA','<center ><p style="font-size: 32px;margin-bottom: -10px;color: #717171;font-family: Poppins;">$150</p><p style="font-size: 17px;margin-bottom: -10px;color:#004551;font-family: Poppins;font-weight: bolder;">Yearly</p><p style="font-size: 17px;margin-bottom: -10px;color: #717171;font-family: Poppins;font-weight: light;">$12.5 per month</p></center>');
insert into `plan_to_period` (`id`, `plan_id`, `sub_period_id`, `chargebee_planid`, `custom`, `enable`, `strike_price`, `ios_planid`, `ios_price`, `ios_showplan`, `free_trial_days`, `offer_desc`, `offer_id`, `offer_label`, `offer_content`, `cb_addon_id`, `cb_coupon_id`, `plan_price`) values('46','23','5','rover-2year','0','1','0','NA','0','0','0',NULL,'0','NA',NULL,'NA','NA','<center ><p style="font-size: 32px;margin-bottom: -10px;color: #717171;font-family: Poppins;">$240</p><p style="font-size: 17px;margin-bottom: -10px;color:#004551;font-family: Poppins;font-weight: bolder;">2 Years</p><p style="font-size: 17px;margin-bottom: -10px;color: #717171;font-family: Poppins;font-weight: light;">$10 per month</p></center>');
insert into `plan_to_period` (`id`, `plan_id`, `sub_period_id`, `chargebee_planid`, `custom`, `enable`, `strike_price`, `ios_planid`, `ios_price`, `ios_showplan`, `free_trial_days`, `offer_desc`, `offer_id`, `offer_label`, `offer_content`, `cb_addon_id`, `cb_coupon_id`, `plan_price`) values('47','24','3','wanderer-half-yearly','0','1','0','NA','0','0','0',NULL,'0','NA',NULL,'NA','NA','<center ><p style="font-size: 32px;margin-bottom: -10px;color: #717171;font-family: Poppins;">$120</p><p style="font-size: 17px;margin-bottom: -10px;color:#004551;font-family: Poppins;font-weight: bolder;">Half Yearly</p><p style="font-size: 17px;margin-bottom: -10px;color: #717171;font-family: Poppins;font-weight: light;">$20 per month</p></center>');
insert into `plan_to_period` (`id`, `plan_id`, `sub_period_id`, `chargebee_planid`, `custom`, `enable`, `strike_price`, `ios_planid`, `ios_price`, `ios_showplan`, `free_trial_days`, `offer_desc`, `offer_id`, `offer_label`, `offer_content`, `cb_addon_id`, `cb_coupon_id`, `plan_price`) values('48','24','4','wanderer-yearly','0','1','0','NA','0','0','0',NULL,'0','NA',NULL,'NA','NA','<center ><p style="font-size: 32px;margin-bottom: -10px;color: #717171;font-family: Poppins;">$210</p><p style="font-size: 17px;margin-bottom: -10px;color:#004551;font-family: Poppins;font-weight: bolder;">Yearly</p><p style="font-size: 17px;margin-bottom: -10px;color: #717171;font-family: Poppins;font-weight: light;">$17.5 per month</p></center>');
insert into `plan_to_period` (`id`, `plan_id`, `sub_period_id`, `chargebee_planid`, `custom`, `enable`, `strike_price`, `ios_planid`, `ios_price`, `ios_showplan`, `free_trial_days`, `offer_desc`, `offer_id`, `offer_label`, `offer_content`, `cb_addon_id`, `cb_coupon_id`, `plan_price`) values('49','24','5','wanderer-2year','0','1','0','NA','0','0','0',NULL,'0','NA',NULL,'NA','NA','<center ><p style="font-size: 32px;margin-bottom: -10px;color: #717171;font-family: Poppins;">$360</p><p style="font-size: 17px;margin-bottom: -10px;color:#004551;font-family: Poppins;font-weight: bolder;">2 Years</p><p style="font-size: 17px;margin-bottom: -10px;color: #717171;font-family: Poppins;font-weight: light;">$15 per month</p></center>');
insert into `plan_to_period` (`id`, `plan_id`, `sub_period_id`, `chargebee_planid`, `custom`, `enable`, `strike_price`, `ios_planid`, `ios_price`, `ios_showplan`, `free_trial_days`, `offer_desc`, `offer_id`, `offer_label`, `offer_content`, `cb_addon_id`, `cb_coupon_id`, `plan_price`) values('50','25','3','waggler-half-yearly','0','1','0','NA','0','0','0',NULL,'0','NA',NULL,'vetchat-monthly','NA','<center ><p style="font-size: 32px;margin-bottom: -10px;color: #717171;font-family: Poppins;">$180</p><p style="font-size: 17px;margin-bottom: -10px;color:#004551;font-family: Poppins;font-weight: bolder;">Half Yearly</p><p style="font-size: 17px;margin-bottom: -10px;color: #717171;font-family: Poppins;font-weight: light;">$30 per month</p></center>');
insert into `plan_to_period` (`id`, `plan_id`, `sub_period_id`, `chargebee_planid`, `custom`, `enable`, `strike_price`, `ios_planid`, `ios_price`, `ios_showplan`, `free_trial_days`, `offer_desc`, `offer_id`, `offer_label`, `offer_content`, `cb_addon_id`, `cb_coupon_id`, `plan_price`) values('51','25','4','waggler-yearly','0','1','0','NA','0','0','0',NULL,'0','NA',NULL,'vetchat-monthly','NA','<center ><p style="font-size: 32px;margin-bottom: -10px;color: #717171;font-family: Poppins;">$300</p><p style="font-size: 17px;margin-bottom: -10px;color:#004551;font-family: Poppins;font-weight: bolder;">Yearly</p><p style="font-size: 17px;margin-bottom: -10px;color: #717171;font-family: Poppins;font-weight: light;">$25 per month</p></center>');
insert into `plan_to_period` (`id`, `plan_id`, `sub_period_id`, `chargebee_planid`, `custom`, `enable`, `strike_price`, `ios_planid`, `ios_price`, `ios_showplan`, `free_trial_days`, `offer_desc`, `offer_id`, `offer_label`, `offer_content`, `cb_addon_id`, `cb_coupon_id`, `plan_price`) values('52','25','5','waggler-2year','0','1','0','NA','0','0','0',NULL,'0','NA',NULL,'vetchat-monthly','NA','<center ><p style="font-size: 32px;margin-bottom: -10px;color: #717171;font-family: Poppins;">$480</p><p style="font-size: 17px;margin-bottom: -10px;color:#004551;font-family: Poppins;font-weight: bolder;">2 Years</p><p style="font-size: 17px;margin-bottom: -10px;color: #717171;font-family: Poppins;font-weight: light;">$20 per month</p></center>');


UPDATE `plan_to_period` SET `cb_addon_id` = 'vetchat-monthly' WHERE `id` = '41'; 


 INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','45','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','46','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','47','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','46','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','47','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('1','44','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('1','45','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('1','46','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('1','47','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('1','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('1','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('1','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('1','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('1','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('47','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('47','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('47','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('47','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('47','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('48','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('48','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('48','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('48','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('50','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('50','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('51','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('4','44','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('4','45','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('4','46','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('4','47','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('4','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('4','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('4','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('4','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('4','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('5','45','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('5','46','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('5','47','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('5','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('5','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('5','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('5','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('5','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('7','47','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('7','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('7','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('7','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('7','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('7','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('8','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('8','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('8','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('8','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('24','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('24','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('24','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('24','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('24','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('6','46','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('6','47','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('6','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('6','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('6','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('6','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('6','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('29','47','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('29','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('29','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('29','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('29','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('29','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('11','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('11','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('11','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('12','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('37','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('37','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('37','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('42','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('42','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('42','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('43','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('43','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('20','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('20','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('20','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('27','47','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('27','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('27','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('27','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('27','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('27','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('46','47','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('46','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('46','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('46','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('46','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('46','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('49','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('49','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('49','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('28','44','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('28','45','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('28','46','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('28','47','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('28','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('28','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('28','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('28','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('28','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('35','47','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('35','48','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('35','49','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('35','50','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('35','51','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('35','52','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','6','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','7','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','8','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','12','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','24','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','29','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','35','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','37','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('44','38','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','8','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','12','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','24','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','29','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','35','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','37','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('45','38','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('46','8','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('46','12','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('46','24','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('46','29','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('46','37','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('46','38','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('47','8','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('47','12','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('47','37','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('47','38','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('48','12','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('48','37','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('48','38','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('49','12','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('49','38','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('50','12','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('50','38','0','NA');
INSERT INTO `plan_to_upgrade` ( `plan_to_period_id`, `upgradeplan_id`, `price`, `description`) VALUES('51','38','0','NA');

ALTER TABLE `plan_feature` CHANGE `plan_period_id` `plan_period_id` BIGINT(20) DEFAULT 0 NOT NULL;

insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('3','23','2','1','1','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('4','23','3','1','1','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('5','23','4','1','1','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('6','23','5','1','15','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('7','23','6','1','999999999','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('8','23','7','1','999999999','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('9','23','8','1','30','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('10','24','2','1','2','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('11','24','3','1','2','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('12','24','4','1','2','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('13','24','5','1','15','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('14','24','6','1','999999999','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('15','24','7','1','999999999','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('16','24','8','1','30','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('17','24','17','1','999999999','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('18','24','18','1','30','0','NA','NA','1','0','0');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('19','25','2','1','5','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('20','25','3','1','5','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('21','25','4','1','5','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('22','25','5','1','15','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('23','25','6','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('24','25','7','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('25','25','8','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('26','25','9','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('27','25','10','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('28','25','11','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('29','25','12','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('30','25','13','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('31','25','14','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('32','25','15','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('33','25','16','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('34','25','17','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('35','25','18','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('36','25','19','1','99999999','0','NA','NA','1','0','1');
insert into `plan_feature` (`id`, `plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `monitortype_id`, `device_config`, `resettype_id`, `plan_period_id`, `unlimited_cr`) values('37','25','20','1','99999999','0','NA','NA','1','0','1');


UPDATE user_feature SET remaining_limit=txn_limit ,feature_code='VPM' WHERE feature_id=1;

 UPDATE `plan` SET `device_cnt` = '1' WHERE `id` = '3'; 
 UPDATE `plan` SET `device_cnt` = '2' WHERE `id` = '4'; 
 UPDATE `plan` SET `device_cnt` = '5' WHERE `id` = '6'; 
 UPDATE `plan` SET `device_cnt` = '3' WHERE `id` = '7'; 
 UPDATE `plan` SET `device_cnt` = '4' WHERE `id` = '8'; 
 UPDATE `plan` SET `device_cnt` = '5' WHERE `id` = '9'; 
 UPDATE `plan` SET `device_cnt` = '5' WHERE `id` = '11'; 
 UPDATE `plan` SET `device_cnt` = '5' WHERE `id` = '13'; 
 UPDATE `plan` SET `device_cnt` = '1' WHERE `id` = '16'; 
 UPDATE `plan` SET `device_cnt` = '5' WHERE `id` = '18'; 
 UPDATE `plan` SET `device_cnt` = '1' WHERE `id` = '19'; 
 UPDATE `plan` SET `device_cnt` = '5' WHERE `id` = '21'; 

 INSERT INTO `plan_to_monitortype` (`plan_id`, `monitortype_id`, `no_cnt`) VALUES ('23', '1', '1'); 
INSERT INTO `plan_to_monitortype` (`plan_id`, `monitortype_id`, `no_cnt`) VALUES ('24', '1', '2'); 
 INSERT INTO `plan_to_monitortype` (`plan_id`, `monitortype_id`, `no_cnt`) VALUES ('25', '1', '5'); 
 
 
 UPDATE `badges` SET `badge_name` = 'Explorer' WHERE `id` = '2'; 
UPDATE `badges` SET `badge_name` = 'Adventurer' WHERE `id` = '3'; 
 UPDATE `badges` SET `desc1` = 'Howdy, Adventurer!' WHERE `id` = '4'; 
 UPDATE `badges` SET `desc2` = 'It\'s a time to become a Adventurer' , `desc3` = 'You ready? Get. Set. Let’s become a Adventurer.'   WHERE   `id` = '3'; 
 UPDATE `badges` SET `desc2` = 'It\'s a time to become a Explorer' , `desc3` = 'Becoming a Explorer is as easy as 1-2-3 !'   WHERE   `id` = '2'; 
 UPDATE `badge_stage` SET `stage_name` = 'I\'m an Explorer'   WHERE   `id` = '6'; 
 UPDATE `badge_stage` SET `stage_name` = 'I\'m a Adventurer!'   WHERE   `id` = '9'; 
 
  UPDATE `badge_stage` SET `stage_name` = 'Subscribe & enable endless safety' WHERE `id` = '5'; 
UPDATE `badge_stage` SET `stage_name` = 'Subscribe to a 2-year plan!' WHERE `id` = '10'; 
 