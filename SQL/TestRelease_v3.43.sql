
CREATE TABLE `cb_cancelsub_req` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `cb_subid` varchar(30) NOT NULL,
  `reason_type` varchar(100) NOT NULL,
  `reason_desc` text,
  `user_id` bigint(20) NOT NULL,
  `created_on` datetime NOT NULL,
  PRIMARY KEY (`id`)
);


CREATE TABLE `plan_benefits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `period_id` bigint(20) NOT NULL,
  `benefits` varchar(255) NOT NULL DEFAULT 'NA',
  `enable` tinyint(4) NOT NULL DEFAULT '1',
  `expiry_date` datetime NOT NULL,
  `order_no` int(4) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_coupon` (`period_id`,`benefits`)
);