CREATE TABLE `user_dlt` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(30) DEFAULT NULL,
  `dlt_reason` varchar(200) NOT NULL DEFAULT 'NA',
  `dlt_description` varchar(200) NOT NULL DEFAULT 'NA',
  `updated_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_userid` (`user_id`)
)
CREATE TABLE `user_dlt_info` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(30) DEFAULT NULL,
  `meid` varchar(200) DEFAULT NULL,
  `cmp_id` bigint(30) NOT NULL DEFAULT '0',
  `gateway_id` bigint(30) NOT NULL DEFAULT '0',
  `qrcode` varchar(100) NOT NULL DEFAULT 'NA',
  `username` varchar(200) NOT NULL DEFAULT 'NA',
  `billing_email` varchar(200) NOT NULL DEFAULT 'NA',
  `order_id` bigint(50) NOT NULL DEFAULT '0',
  `order_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `order_channel` varchar(100) NOT NULL DEFAULT 'NA',
  `updated_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `meid` (`meid`)
)
ALTER TABLE `user` ADD COLUMN `delete_user` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `user` ADD COLUMN `delete_time` DATETIME NOT NULL DEFAULT '1753-01-01 00:00:00';
ALTER TABLE `version_mapping` ADD COLUMN `enable_delete` TINYINT(1) NOT NULL DEFAULT '0';