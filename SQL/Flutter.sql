ALTER TABLE `iris`.`plan` ADD COLUMN `feature_list_flutter` TEXT NOT NULL ;
ALTER TABLE `iris`.`plan_to_period` ADD COLUMN `is_best_deal` TINYINT(1) DEFAULT 0 NOT NULL, ADD COLUMN `img_url` VARCHAR(100) DEFAULT 'NA' NOT NULL;

ALTER TABLE `iris`.`app_image` ADD COLUMN `img_path_flutter_light` VARCHAR(200) DEFAULT 'NA' NOT NULL AFTER `img_path`, CHANGE `img_path_dark` `img_path_flutter_dark` VARCHAR(200) CHARSET latin1 COLLATE latin1_swedish_ci DEFAULT 'NA' NOT NULL;

UPDATE iris`.`app_image  img_path='https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/REmobile_13.png' WHERE img_name='referandearn' AND TYPE IN('android','iphone'); 
UPDATE `iris`.`app_image` SET `img_path` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/iosappimages/REIpad17.png' WHERE img_name='referandearn' AND TYPE='ipad';
UPDATE `iris`.`app_image` SET `img_path_flutter_light` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/REmobile_14.png' WHERE img_name='referandearn'; 
UPDATE `iris`.`app_image` SET `img_path_flutter_dark` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/REmobile_bt_1.png' WHERE img_name='referandearn'; 
UPDATE `iris`.`app_image` SET `img_path_flutter_light` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/waggle_freqalertcontent_light.png',`img_path_flutter_dark` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/waggle_freqalertcontent_dark.png' WHERE img_name='notify-frequency'; 
UPDATE `iriiris`.`app_image  `img_path_flutter_light` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/waggle_powerlosscontent_light.png',`img_path_flutter_dark` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/waggle_powerlosscontent_dark.png' WHERE img_name='powerloss-msg';

#Refer And Earn content
UPDATE referral_credits SET advocate_msg='You get $30 Waggle Gift Card,when your friends purchase' ,referral_msg='Your friends get 50% Discount,on Waggle Pet Monitor' WHERE ENABLE=1 AND DATE(expiry_date)>=CURRENT_DATE ORDER BY createdon DESC LIMIT 1;

#Feature list added for flutter
ALTER TABLE `iris`.`plan` ADD COLUMN `feature_list_dark_flutter` TEXT NOT NULL AFTER `feature_list_flutter`;

UPDATE `iris`.`plan` SET `feature_list_flutter` = '<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><script src=\'https://code.jquery.com/jquery-3.2.1.slim.min.js\' integrity=\'sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN\' crossorigin=\'anonymous\'></script><link href=\'https://fonts.googleapis.com/css?family=Nunito\' rel=\'stylesheet\'><script src=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js\' integrity=\'sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl\' crossorigin=\'anonymous\'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: \'Nunito\';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class=\'container\'><div class=\'row custm_cont_2\'><div class=\'col-md-12 col-12 col-xs-12\'><table class=\'table\'><tbody><tr><td class=\'left\'><img src=\"https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg\" alt=\"\"></td><td ><p style=\"color: #000; font-weight: bold; margin-bottom: 0px;\">Alerts</p><p style=\"color: #808080; margin-bottom: -5px;\">Temperature Alerts, Humidity Alerts, Power Loss Alerts, Power Recovery Alerts, Network Alerts and Low Battery Alerts.</p></td></tr><tr><td class=\'left\'><img src=\"https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg\" alt=\"\"></td><td ><p style=\"color: #000; font-weight: bold; margin-bottom: 0px;\">Alert Notifications</p><p style=\"color: #808080; margin-bottom: -5px;\">Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class=\'left\'><img src=\"https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg\" alt=\"\"></td><td ><p style=\"color: #000; font-weight: bold; margin-bottom: 0px;\">Add Monitors</p><p style=\"color: #808080; margin-bottom: -5px;\">Upto 2</p></td></tr><tr><td class=\'left\'><img src=\"https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg\" alt=\"\"></td><td ><p style=\"color: #000; font-weight: bold; margin-bottom: 0px;\">Friends & Family Sharing</p><p style=\"color: #808080; margin-bottom: -5px;\">Add up to 4 members</p></td></tr><tr><td class=\'left\'><img src=\"https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg\" alt=\"\"></td><td ><p style=\"color: #000; font-weight: bold; margin-bottom: 0px;\">GeoFencing</p><p style=\"color: #808080; margin-bottom: -5px;\">Only on Pet Monitor Pro*</p></td></tr></tbody></table></div></div></div>' WHERE `id` = '31';


UPDATE iris`.`plan  SET feature_list_dark_flutter  = '<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><script src=\'https://code.jquery.com/jquery-3.2.1.slim.min.js\' integrity=\'sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN\' crossorigin=\'anonymous\'></script><link href=\'https://fonts.googleapis.com/css?family=Nunito\' rel=\'stylesheet\'><script src=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js\' integrity=\'sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl\' crossorigin=\'anonymous\'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: \'Nunito\';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class=\'container\'><div class=\'row custm_cont_2\'><div class=\'col-md-12 col-12 col-xs-12\'><table class=\'table\'><tbody style=\'background-color' where id  = '35';


#waggle shop

create table shop_banner id anner_url` varchar (600),
	`action` tinyint (1),
	`action_url` varchar (600),
	`external_redirect` tinyint (1)
); 
insert into `shop_banner` (`id`, `banner_url`, `action`, `action_url`, `external_redirect`) values('1','https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/valentine_shop.png','1','https://mywaggle.com/','1');
insert into `shop_banner` (`id`, `banner_url`, `action`, `action_url`, `external_redirect`) values('2','https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/waggfluence_shop_1.png','1','https://waggfluence.com/en-in','1');
insert into `shop_banner` (`id`, `banner_url`, `action`, `action_url`, `external_redirect`) values('3','https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/refernow_store_1.png','1','NA','0');


create table `shop_feature` (
	`id` bigint (20),
	`title` varchar (300),
	`btn_name` varchar (300),
	`action_url` varchar (600),
	`external_redirect` tinyint (1)
); 
insert into `shop_feature` (`id`, `title`, `btn_name`, `action_url`, `external_redirect`) values('1','Waggle','More','https://mywaggle.com/','1');
insert into `shop_feature` (`id`, `title`, `btn_name`, `action_url`, `external_redirect`) values('2','Wagglefluence','More','https://waggfluence.com/en-in','1');
insert into `shop_feature `id`shop_feature n_name`, `action_url`, `external_redirect`) values('3','Refer & Earn','More','NA','0');

Plan offers 

ALTER TABLE iris`.`plan_offer offer_desc1_dark  ADD COLUMN offer_desc2_dark  


UPDATE `iris`.`plan_offer` SET `offer_desc1_dark` = '<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;padding: 0px;position: absolute;}</style><div class=\'container\'><table class=\'table\'><tbody><tr><td><p style=\'color:#ffffff;font-size: 15px;margin-bottom: 2px;\'><b>Half Yearly : </b>$10 OFF</p><p style=\'color:#6a6a6a;font-size: 15px; margin-bottom: 0px;\'>Use Coupon : <b style=\'color:#ec5757;\'>WAGGLE10</b></p></td></tr></tbody></table></div>',     
`offer_desc2_dark` =	'<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;padding: 0px;}</style><div class=\'container\' style=\'padding-left: 0px;padding-right: 0px;\'><table class=\'table\'><tbody><tr><td><center><p style=\'color:#ffffff;font-size: 18px;padding-left: 5px;\'><b>Coupon Applied Successfully</b></p><p style=\'color:#6a6a6a;font-size: 17px; margin-top: -10px;padding-left: 5px;\'>Waggler Half-Yearly with 10% offer</p></center></td></tr></tbody></table></div>' where `id` = '1';

UPDATE `iris`.`plan_offer` SET `offer_desc1_dark` = '<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;padding: 0px;position: absolute;}</style><div class=\'container\'><table class=\'table\'><tbody><tr><td><p style=\'color:#ffffff;font-size: 15px;margin-bottom: 2px;\'><b>Yearly : </b>$20 OFF</p><p style=\'color:#6a6a6a;font-size: 15px; margin-bottom: 0px;\'>Use Coupon : <b style=\'color:#ec5757;\'>WAGGLE20</b></p></td></tr></tbody></table></div>',   
`offer_desc2_dark` =	'<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;padding: 0px;}</style><div class=\'container\' style=\'padding-left: 0px;padding-right: 0px;\'><table class=\'table\'><tbody><tr><td><center><p style=\'color:#ffffff;font-size: 18px;padding-left: 5px;\'><b>Coupon Applied Successfully</b></p><p style=\'color:#6a6a6a;font-size: 17px; margin-top: -10px;padding-left: 5px;\'>Waggler Yearly with 20% offer</p></center></td></tr></tbody></table></div>' where `id` = '2';

UPDATE `iris`.`plan_offer` SET `offer_desc1_dark` = '<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;padding: 0px;position: absolute;}</style><div class=\'container\'><table class=\'table\'><tbody><tr><td><p style=\'color:#ffffff;font-size: 15px;margin-bottom: 2px;\'><b>2 Year : </b>$30 OFF</p><p style=\'color:#6a6a6a;font-size: 15px; margin-bottom: 0px;\'>Use Coupon : <b style=\'color:#ec5757;\'>WAGGLE30</b></p></td></tr></tbody></table></div>',   
`offer_desc2_dark` =	'<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;padding: 0px;}</style><div class=\'container\' style=\'padding-left: 0px;padding-right: 0px;\'><table class=\'table\'><tbody><tr><td><center><p style=\'color:#ffffff;font-size: 18px;padding-left: 5px;\'><b>Coupon Applied Successfully</b></p><p style=\'color:#6a6a6a;font-size: 17px; margin-top: -10px;padding-left: 5px;\'>Waggler 2 Year with $30 offer</p></center></td></tr></tbody></table></div>' where `id` = '3';

UPDATE `iris`.`plan_offer` SET `offer_desc1_dark` = '<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;padding: 0px;position: absolute;}</style><div class=\'container\'><table class=\'table\'><tbody><tr><td><p style=\'color:#ffffff;font-size: 15px;margin-bottom: 2px;\'><b>2 Year : </b>$30 OFF</p><p style=\'color:#6a6a6a;font-size: 15px; margin-bottom: 0px;\'>Use Coupon : <b style=\'color:#ec5757;\'>WAGGLE30</b></p></td></tr></tbody></table></div>',   
`offer_desc2_dark` =	'<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;padding: 0px;}</style><div class=\'container\' style=\'padding-left: 0px;padding-right: 0px;\'><table class=\'table\'><tbody><tr><td><center><p style=\'color:#ffffff;font-size: 18px;padding-left: 5px;\'><b>Coupon Applied Successfully</b></p><p style=\'color:#6a6a6a;font-size: 17px; margin-top: -10px;padding-left: 5px;\'>Waggler 2 Year with $30 offer</p></center></td></tr></tbody></table></div>' where `id` = '4';

UPDATE `iris`.`plan_offer` SET `offer_desc1_dark` = '<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;padding: 0px;position: absolute;}</style><div class=\'container\'><table class=\'table\'><tbody><tr><td><p style=\'color:#ffffff;font-size: 15px;margin-bottom: 2px;\'><b>2 Year : </b>$30 OFF</p><p style=\'color:#6a6a6a;font-size: 15px; margin-bottom: 0px;\'>Use Coupon : <b style=\'color:#ec5757;\'>WAGGLE30</b></p></td></tr></tbody></table></div>',   
offer_desc2_daoffer_desc2_dark 'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;padding: 0px;}</style><div class=\'container\' style=\'padding-left: 0px;padding-right: 0px;\'><table class=\'table\'><tbody><tr><td><center><p style=\'color:#ffffff;font-size: 18px;padding-left: 5px;\'><b>Coupon Applied Successfully</b></p><p style=\'color:#6a6a6a;font-size: 17px; margin-top: -10px;padding-left: 5px;\'>Waggler 2 Year with $30 offer</p></center></td></tr></tbody></table></div>' where `id  = '5';


#Dingo
UPDATE iris.plan SET 
cur_feature='<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,200;1,200;1,300&display=swap" rel="stylesheet">

<style>
@media only screen and (max-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);
}
}

@media only screen and (min-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);
}
}
</style>

<center>
<img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img">
<p style="color:#ed5f5f;font-size:18px; font-weight:bolder;font-family:Inter, sans-serif;">Dingo</p>
<table style="width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="color: #808080; margin-bottom: -10px;">Low
Battery, </p><p style="color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>
               <div class="col-md-2 col-2 plan_right"
                  style="float: left; width: 50%;">
                  <p style="color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts
                  </p>
				  <p style="color: #808080;">500 - Text Alerts / mo*
                  </p>
               </div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">Yes</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">2</p></div></div></td></tr>
<tr style="margin-top: 5px;">
<td>
            <div class="row">
               <div class="col-md-10 col-10 plan_left" style="float: left; width: 100%;">
			   <p style="color: #000;margin-left: 5px;"><b>Note:</b>
                  </p>
				  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).
                  </p>
               </div>
                  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; Geofence Alert only applicable for Pet
					Monitor GPS.
                  </p>
            </div>
         </td>
	</tr>
</table></center>',
feature_list_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 2</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #000; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
feature_list_dark_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12' style='background-color: #110F1C;'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 2</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #fff; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
cur_feature_flutter='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;font-family: Nunito;">Dingo</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #808080;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">2</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #000;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></center></html>',
cur_feature_flutter_dark='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><div style="background-color: #1B1826;"><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;">Dingo</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #fff;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #fff; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #fff;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">2</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #fff;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p>               </div>                  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></div></center></html>' WHERE id='29';

#waggler
UPDATE iris.plan SET 
cur_feature='<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,200;1,200;1,300&display=swap" rel="stylesheet">

<style>
@media only screen and (max-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);
}
}

@media only screen and (min-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);
}
}
</style>

<center>
<img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img">
<p style="color:#ed5f5f;font-size:18px; font-weight:bolder;font-family:Inter, sans-serif;">Waggler</p>
<table style="width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="color: #808080; margin-bottom: -10px;">Low
Battery, </p><p style="color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>
               <div class="col-md-2 col-2 plan_right"
                  style="float: left; width: 50%;">
                  <p style="color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts
                  </p>
				  <p style="color: #808080;">500 - Text Alerts / mo*
                  </p>
               </div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">Yes</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">2</p></div></div></td></tr>
<tr style="margin-top: 5px;">
<td>
            <div class="row">
               <div class="col-md-10 col-10 plan_left" style="float: left; width: 100%;">
			   <p style="color: #000;margin-left: 5px;"><b>Note:</b>
                  </p>
				  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).
                  </p>
               </div>
                  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; Geofence Alert only applicable for Pet
					Monitor GPS.
                  </p>
            </div>
         </td>
	</tr>
</table></center>',
feature_list_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 2</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #000; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
feature_list_dark_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12' style='background-color: #110F1C;'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 2</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #fff; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
cur_feature_flutter='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;font-family: Nunito;">Waggler</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #808080;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">2</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #000;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></center></html>',
cur_feature_flutter_dark='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><div style="background-color: #1B1826;"><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;">Waggler</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #fff;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #fff; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #fff;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">2</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #fff;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p>               </div>                  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></div></center></html>' WHERE id='25';


#waggler pawful
UPDATE iris.plan SET 
cur_feature='<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,200;1,200;1,300&display=swap" rel="stylesheet">

<style>
@media only screen and (max-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);
}
}

@media only screen and (min-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);
}
}
</style>

<center>
<img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img">
<p style="color:#ed5f5f;font-size:18px; font-weight:bolder;font-family:Inter, sans-serif;">Waggler Pawful</p>
<table style="width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="color: #808080; margin-bottom: -10px;">Low
Battery, </p><p style="color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>
               <div class="col-md-2 col-2 plan_right"
                  style="float: left; width: 50%;">
                  <p style="color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts
                  </p>
				  <p style="color: #808080;">500 - Text Alerts / mo*
                  </p>
               </div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">Yes</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">3</p></div></div></td></tr>
<tr style="margin-top: 5px;">
<td>
            <div class="row">
               <div class="col-md-10 col-10 plan_left" style="float: left; width: 100%;">
			   <p style="color: #000;margin-left: 5px;"><b>Note:</b>
                  </p>
				  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).
                  </p>
               </div>
                  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; Geofence Alert only applicable for Pet
					Monitor GPS.
                  </p>
            </div>
         </td>
	</tr>
</table></center>',
feature_list_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 3</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #000; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
feature_list_dark_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12' style='background-color: #110F1C;'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 3</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #fff; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
cur_feature_flutter='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;font-family: Nunito;">Waggler Pawful</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #808080;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">3</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #000;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></center></html>',
cur_feature_flutter_dark='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><div style="background-color: #1B1826;"><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;">Waggler Pawful</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #fff;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #fff; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #fff;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">3</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #fff;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p>               </div>                  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></div></center></html>' WHERE id='26';

#Waggler Furwell
UPDATE iris.plan SET 
cur_feature='<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,200;1,200;1,300&display=swap" rel="stylesheet">

<style>
@media only screen and (max-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);
}
}

@media only screen and (min-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);
}
}
</style>

<center>
<img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img">
<p style="color:#ed5f5f;font-size:18px; font-weight:bolder;font-family:Inter, sans-serif;">Waggler Furwell</p>
<table style="width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="color: #808080; margin-bottom: -10px;">Low
Battery, </p><p style="color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>
               <div class="col-md-2 col-2 plan_right"
                  style="float: left; width: 50%;">
                  <p style="color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts
                  </p>
				  <p style="color: #808080;">500 - Text Alerts / mo*
                  </p>
               </div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">Yes</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">4</p></div></div></td></tr>
<tr style="margin-top: 5px;">
<td>
            <div class="row">
               <div class="col-md-10 col-10 plan_left" style="float: left; width: 100%;">
			   <p style="color: #000;margin-left: 5px;"><b>Note:</b>
                  </p>
				  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).
                  </p>
               </div>
                  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; Geofence Alert only applicable for Pet
					Monitor GPS.
                  </p>
            </div>
         </td>
	</tr>
</table></center>',
feature_list_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 4</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #000; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
feature_list_dark_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12' style='background-color: #110F1C;'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 4</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #fff; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
cur_feature_flutter='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;font-family: Nunito;">Waggler Furwell</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #808080;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">4</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #000;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></center></html>',
cur_feature_flutter_dark='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><div style="background-color: #1B1826;"><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;">Waggler Furwell</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #fff;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #fff; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #fff;">500 - Text Alerts / mo* </p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">4</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #fff;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></div></center></html>' WHERE id='27';

#Waggler Furever
UPDATE iris.plan SET 
cur_feature='<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,200;1,200;1,300&display=swap" rel="stylesheet">

<style>
@media only screen and (max-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);
}
}

@media only screen and (min-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);
}
}
</style>

<center>
<img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img">
<p style="color:#ed5f5f;font-size:18px; font-weight:bolder;font-family:Inter, sans-serif;">Waggler Furever</p>
<table style="width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="color: #808080; margin-bottom: -10px;">Low
Battery, </p><p style="color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>
               <div class="col-md-2 col-2 plan_right"
                  style="float: left; width: 50%;">
                  <p style="color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts
                  </p>
				  <p style="color: #808080;">500 - Text Alerts / mo*
                  </p>
               </div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">Yes</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">5</p></div></div></td></tr>
<tr style="margin-top: 5px;">
<td>
            <div class="row">
               <div class="col-md-10 col-10 plan_left" style="float: left; width: 100%;">
			   <p style="color: #000;margin-left: 5px;"><b>Note:</b>
                  </p>
				  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).
                  </p>
               </div>
                  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; Geofence Alert only applicable for Pet
					Monitor GPS.
                  </p>
            </div>
         </td>
	</tr>
</table></center>',
feature_list_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 5</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #000; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
feature_list_dark_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12' style='background-color: #110F1C;'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 5</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #fff; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
cur_feature_flutter='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;font-family: Nunito;">Waggler Furever</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #808080;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">5</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #000;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></center></html>',
cur_feature_flutter_dark='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><div style="background-color: #1B1826;"><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;">Waggler Furever</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #fff;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #fff; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #fff;">500 - Text Alerts / mo* </p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">5</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #fff;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></div></center></html>' WHERE id='28';

#Waggler Plus
UPDATE iris.plan SET 
cur_feature='<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,200;1,200;1,300&display=swap" rel="stylesheet">

<style>
@media only screen and (max-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);
}
}

@media only screen and (min-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);
}
}
</style>

<center>
<img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img">
<p style="color:#ed5f5f;font-size:18px; font-weight:bolder;font-family:Inter, sans-serif;">Waggler Plus</p>
<table style="width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="color: #808080; margin-bottom: -10px;">Low
Battery, </p><p style="color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>
               <div class="col-md-2 col-2 plan_right"
                  style="float: left; width: 50%;">
                  <p style="color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts
                  </p>
				  <p style="color: #808080;">500 - Text Alerts / mo*
                  </p>
               </div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">Yes</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">2</p></div></div></td></tr>
<tr style="margin-top: 5px;">
<td>
            <div class="row">
               <div class="col-md-10 col-10 plan_left" style="float: left; width: 100%;">
			   <p style="color: #000;margin-left: 5px;"><b>Note:</b>
                  </p>
				  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).
                  </p>
               </div>
                  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; Geofence Alert only applicable for Pet
					Monitor GPS.
                  </p>
            </div>
         </td>
	</tr>
</table></center>',
feature_list_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 2</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #000; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
feature_list_dark_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12' style='background-color: #110F1C;'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 2</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #fff; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
cur_feature_flutter='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;font-family: Nunito;">Waggler Plus</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #808080;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">2</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #000;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></center></html>',
cur_feature_flutter_dark='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><div style="background-color: #1B1826;"><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;">Waggler Plus</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #fff;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #fff; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #fff;">500 - Text Alerts / mo* </p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">2</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #fff;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></div></center></html>' WHERE id='31';

#Waggler Pup
UPDATE iris.plan SET 
cur_feature='<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,200;1,200;1,300&display=swap" rel="stylesheet">

<style>
@media only screen and (max-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);
}
}

@media only screen and (min-device-width: 768px){
#dog_img {
content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);
}
}
</style>

<center>
<img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img">
<p style="color:#ed5f5f;font-size:18px; font-weight:bolder;font-family:Inter, sans-serif;">Waggler Pup</p>
<table style="width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="color: #808080; margin-bottom: -10px;">Low
Battery, </p><p style="color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>
               <div class="col-md-2 col-2 plan_right"
                  style="float: left; width: 50%;">
                  <p style="color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts
                  </p>
				  <p style="color: #808080;">500 - Text Alerts / mo*
                  </p>
               </div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">Yes</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr>
<tr><td><div class="row"><div class="col-md-10 col-10 plan_left"
style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"
style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;">1</p></div></div></td></tr>
<tr style="margin-top: 5px;">
<td>
            <div class="row">
               <div class="col-md-10 col-10 plan_left" style="float: left; width: 100%;">
			   <p style="color: #000;margin-left: 5px;"><b>Note:</b>
                  </p>
				  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).
                  </p>
               </div>
                  <p style="color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">
                     <b style="color:#000;">* </b>&nbsp; Geofence Alert only applicable for Pet
					Monitor GPS.
                  </p>
            </div>
         </td>
	</tr>
</table></center>',
feature_list_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 1</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #000; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #000; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px; '><b style='color:#000;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
feature_list_dark_flutter="<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Nunito';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12' style='background-color: #110F1C;'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alerts</p><p style='color: #808080; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alert Notifications</p><p style='color: #808080; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo*</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #808080; margin-bottom: -5px;'>Upto 1</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #808080; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #808080; margin-bottom: -5px;'>Only on Pet Monitor Pro*</p></td></tr></tbody></table><p style='color: #fff; font-size: 13px;'><b>Note:</b></p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).</p><p style='color: #808080; font-size: 12px;'><b style='color:#fff;'>* </b>&nbsp; All plans will be auto-renewed.</p></div></div></div></html>",
cur_feature_flutter='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;font-family: Nunito;">Waggler Pup</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #808080; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #808080;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #808080; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #808080;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #808080; margin-bottom: -5px;">1</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #000;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Nunito; color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#000;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></center></html>',
cur_feature_flutter_dark='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><div style="background-color: #1B1826;"><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Nunito; color:#ed5f5f;font-size:18px; font-weight:bolder;">Waggler Pup</p><table style="font-family: Nunito; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Nunito; color: #fff; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Nunito; color: #fff;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Nunito; float: left; width: 50%;">                  <p style="font-family: Nunito; color: #fff; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Nunito; color: #fff;">500 - Text Alerts / mo* </p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Nunito; float: left; width: 50%;"><p style="font-family: Nunito; color: #fff; margin-bottom: -5px;">1</p></div></div></td></tr><tr style="font-family: Nunito; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Nunito; float: left; width: 100%;">   <p style="font-family: Nunito; color: #fff;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Nunito; color: #fff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Nunito; color:#ed5f5f;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></div></center></html>' WHERE id='32';



ALTER TABLE `iris`.`shop_feature` ADD COLUMN `img_url` VARCHAR(200) DEFAULT 'NA' NOT NULL AFTER `external_redirect`; 

UPDATE `iris`.`shop_feature` SET `img_url` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/waggle.png' WHERE `id` = '1'; 
UPDATE `iris`.`shop_feature` SET `img_url` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/wf.png' WHERE `id` = '2';

UPDATE `iris`.`referral_credits` SET `advocate_msg` = 'You get $50 Amazon Gift Card@#when your friends purchase',`referral_msg` = 'Your friends get 50% discount@#on Waggle Pet Monitor' WHERE ENABLE=1 AND DATE(expiry_date)>=CURRENT_DATE ORDER BY createdon DESC LIMIT 1;

UPDATE `shop_feature` SET title='Wagg Store' WHERE title='Wagglefluence';

DELETE FROM `shop_feature` WHERE title='Refer & Earn';

UPDATE `petnews` SET ipad_imgurl='https://nimbleapi-images.s3.us-west-2.amazonaws.com/petnews/prod/pet_news_50_2.png',
android_imgurl='https://nimbleapi-images.s3.us-west-2.amazonaws.com/petnews/prod/pet_news_50_2.png',
iphone_imgurl='https://nimbleapi-images.s3.us-west-2.amazonaws.com/petnews/prod/pet_news_50_2.png' WHERE id=33;

UPDATE `iris`.`app_image` SET `img_path_flutter_light` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/dashboard10.png' , `img_path_flutter_dark` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/dashboard10_dark.png' WHERE `img_name` = 'petdashboard';

insert into `shop_banner` (`id`, `banner_url`, `action`, `action_url`, `external_redirect`) values('1','https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/avatars.png','1','https://waggfluence.com/collections/pet-ai-avatars-by-waggle','1');
insert into `shop_banner` (`id`, `banner_url`, `action`, `action_url`, `external_redirect`) values('2','https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/ebooks.png','1','https://waggfluence.com/collections/waggfluence-pet-ebooks','1');
insert into `shop_banner` (`id`, `banner_url`, `action`, `action_url`, `external_redirect`) values('3','https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/waggfluence_shop_1.png','1','https://waggfluence.com/en-in','1');

UPDATE plan_to_period SET img_url='https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/2year_plan.png' WHERE id IN(46,49,52,55,57,59,65,70,75,80);

UPDATE plan_to_period SET img_url='https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/yearly_plan.png' WHERE id IN(45,48,51,54,56,58,64,69,74,79);

UPDATE plan_to_period SET img_url='https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/monthly_plan_1.png' WHERE id IN(60,61,66,71,76);

UPDATE plan_to_period SET img_url='https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/halfyearly_plan_1.png' WHERE id IN(44,47,50,63,68,73,78);

UPDATE plan SET cur_feature_flutter='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" id="dog_img" alt="Rover"><p style="color:#ed5f5f;font-size:18px; font-weight:bolder;font-family: Nunito;">Rover</p><table style="width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom:-5px;font-family: Nunito;">Types of Alert</p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;font-family: Nunito;">Temperature</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Text Alerts & Email Alerts</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #808080; margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Temperature </p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #808080;margin-bottom: -5px;font-family: Nunito;">30 Email & 30 Text alerts</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;font-family: Nunito;">0</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Add Monitor Upto</p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;font-family: Nunito;">1</p></div></div></td></tr></table></center></html>',
cur_feature_flutter_dark='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style></head><div style="background-color: #1B1826;"><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" id="dog_img" alt="Rover"><p style="color:#ed5f5f;font-size:18px; font-weight:bolder;font-family: Nunito;">Rover</p><table style="width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="font-weight: bold;margin-left: 5px;margin-bottom:-5px;font-family: Nunito;color: #fff;">Types of Alert</p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #fff; margin-bottom: -5px;font-family: Nunito;">Temperature</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Text Alerts & Email Alerts</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #fff; margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Temperature </p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #fff;margin-bottom: -5px;font-family: Nunito;">30 Email & 30 Text alerts</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #fff; margin-bottom: -5px;font-family: Nunito;">0</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #fff; font-weight: bold;margin-left: 5px;font-family: Nunito;margin-bottom: 0px;">Add Monitor Upto</p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #fff; font-family: Nunito;margin-bottom: 0px;">1</p></div></div></td></tr></table></center></div></html>' WHERE id=23;

UPDATE plan SET cur_feature_flutter='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" id="dog_img" alt="Rover"><p style="color:#ed5f5f;font-size:18px; font-weight:bolder;font-family: Nunito;">Wanderer</p><table style="width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom:-5px;font-family: Nunito;">Types of Alert</p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;font-family: Nunito;">Temperature & Humidity Alerts</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Text Alerts & Email Alerts</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #808080; margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Humidity </p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #808080;margin-bottom: -5px;font-family: Nunito;">60 Email & 60 Text alerts</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #808080; margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Temperature </p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #808080;margin-bottom: -5px;font-family: Nunito;">60 Email & 60 Text alerts</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;font-family: Nunito;">1</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Add Monitor Upto</p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #808080; margin-bottom: -5px;font-family: Nunito;">1</p></div></div></td></tr></table></center></html>',
cur_feature_flutter_dark='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style></head><div style="background-color: #1B1826;"><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" id="dog_img" alt="Rover"><p style="color:#ed5f5f;font-size:18px; font-weight:bolder;font-family: Nunito;">Wanderer</p><table style="width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="font-weight: bold;margin-left: 5px;margin-bottom:-5px;font-family: Nunito;color: #fff;">Types of Alert</p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #fff; margin-bottom: -5px;font-family: Nunito;">Temperature & Humidity Alerts</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Text Alerts & Email Alerts</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #fff; margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Humidity </p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #fff;margin-bottom: -5px;font-family: Nunito;">60 Email & 60 Text alerts</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #fff; margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Temperature </p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #fff;margin-bottom: -5px;font-family: Nunito;">60 Email & 60 Text alerts</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;font-family: Nunito;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #fff; margin-bottom: -5px;font-family: Nunito;">1</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="float: left; width: 50%;"><p style="color: #fff; font-weight: bold;margin-left: 5px;font-family: Nunito;margin-bottom: 0px;">Add Monitor Upto</p></div><div class="col-md-2 col-2 plan_right"style="float: left; width: 50%;"><p style="color: #fff; font-family: Nunito;margin-bottom: 0px;">1</p></div></div></td></tr></table></center></div></html>' WHERE id=24;


#14-06-2023 updated
UPDATE `plan_to_period` SET content_1 = CONCAT(content_1,'s') WHERE content_1='2 Year' OR content_1='5 Year';

