
ALTER TABLE `iris`.`monitortype` ADD COLUMN `product_link` VARCHAR(250) DEFAULT 'NA' NULL; 
CREATE TABLE `user_location` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `gateway_id` bigint NOT NULL,
  `latitude` double DEFAULT '0',
  `longitude` double DEFAULT '0',
  `weather_id` int DEFAULT '0',
  `weather_condition` varchar(50) DEFAULT 'NA',
  `weather_description` varchar(100) DEFAULT 'NA',
  `area` varchar(100) DEFAULT 'NA',
  `updated_on` datetime DEFAULT '1753-01-01 00:00:00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ug_loc_ux_id` (`user_id`,`gateway_id`)
);

#-------------New Release Queries-----------------------

ALTER TABLE `iris`.`plan` ADD COLUMN `feature_list_ui_new` TEXT NOT NULL AFTER `country_code`, ADD COLUMN `cur_feature_ui_new` TEXT NOT NULL;

insert into `monitortype` (`id`, `name`, `display_name`, `description`, `imageurl`, `category`, `devicemaxcount`, `enable`, `infourl`, `order`, `product_link`) values('7','WaggleCamUltra','Waggle Cam Ultra','Register Waggle Cam Ultra','https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/logo/wagglecam_1.png','PetHub','5','0','https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/shop_logo/shop_wagglecampro.png','7','NA');
insert into `monitortype` (`id`, `name`, `display_name`, `description`, `imageurl`, `category`, `devicemaxcount`, `enable`, `infourl`, `order`, `product_link`) values('8','RVSolarCam','RV Solar Cam','RV Solar Cam','https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/shop_logo/shop_wagglecampro.png','RvHub','5','0','https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/shop_logo/shop_wagglecampro.png','6','NA');

UPDATE `iris`.`monitortype` SET `order` = '7' WHERE `id` = '7'; 
UPDATE `iris`.`monitortype` SET `order` = '5' WHERE `id` = '3';
UPDATE `iris`.`monitortype` SET `order` = '2' WHERE `id` = '5';
UPDATE `iris`.`monitortype` SET `order` = '7' WHERE `id` = '4';
UPDATE `iris`.`monitortype` SET `order` = '3' WHERE `id` = '6';
UPDATE `iris`.`monitortype` SET `order` = '0' WHERE `id` = '2';

UPDATE `iris`.`monitortype` SET `enable` = '0' WHERE `id` = '8';
UPDATE `iris`.`monitortype` SET `product_link` = 'https://www.wagglemerch.com/products/smart-pet-bowl?pr_prod_strat=e5_desc&pr_rec_id=74e275639&pr_rec_pid=7440471752756&pr_ref_pid=7154173018164&pr_seq=uniform' WHERE `id` = '3';
UPDATE `iris`.`monitortype` SET `imageurl` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/logo/wagglecam_1.png' WHERE `id` = '7'; 
UPDATE `iris`.`monitortype` SET `product_link` = 'NA' WHERE `id` = '9'; 
UPDATE `iris`.`monitortype` SET `infourl` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/qrc/wagglecam_new.png',product_link='https://mywaggle.com/pages/wagglecam-pro' WHERE `id` = '4';
UPDATE `iris`.`monitortype` SET `infourl` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/logo/mini_cam_new.png',product_link='https://mywaggle.com/pages/rv-mini-cam' WHERE `id` = '5'; 
UPDATE `iris`.`monitortype` SET `infourl` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/shop_logo/shop_wagglecampro_new.png',product_link='https://mywaggle.com/pages/wagglecam-pro' WHERE `id` = '6';
UPDATE `iris`.`monitortype` SET `imageurl` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/logo/pet_monitor_1.png' WHERE `id` = '1'; 
UPDATE `iris`.`monitortype` SET `imageurl` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/logo/pet_bowl_1.png' WHERE `id` = '3'; 
UPDATE `iris`.`monitortype` SET `imageurl` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/logo/ns_sensorlogo_1.png' WHERE `id` = '9'; 
UPDATE `iris`.`monitortype` SET `imageurl` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/logo/mini_cam_1.png' WHERE `id` = '5'; 
UPDATE `iris`.`monitortype` SET `product_link` = 'https://mywaggle.com/products/pet-monitor?variant=44067494166769' WHERE `id` = '1'; 
UPDATE `iris`.`monitortype` SET `product_link` = 'NA' WHERE `id` = '7';
UPDATE `iris`.`monitortype` SET `display_name` = 'RV Smart Sensors' WHERE `id` = '9';

ALTER TABLE `iris`.`product_sub_cat` ADD COLUMN `display_order` INT NOT NULL;

#wag
UPDATE `iris`.`plan` SET `feature_list_ui_new` = "<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>body {background-color: transparent;} .table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Montserrat';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><body><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alerts Available</p><p style='color: #fff; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alert Limits</p><p style='color: #fff; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo* (Temperature: 250, Power Loss: 140, Power Back: 30, Humidity: 50, Battery: 15, Gateway not reporting: 10, Geofence: 5)</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #fff; margin-bottom: -5px;'>Upto 1</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #fff; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #fff; margin-bottom: -5px;'>Only on Pet Monitor GPS*</p></td></tr></tbody></table></div></div></div></body></html>" where `id` = '36';
update iris.plan set cur_feature_ui_new='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Montserrat; color:#fff;font-size:18px; font-weight:bolder;font-family: Montserrat;">Wag</p><table style="font-family: Montserrat; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Montserrat; color: #ffffff;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Montserrat; float: left; width: 50%;">                  <p style="font-family: Montserrat; color: #ffffff; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Montserrat; color: #ffffff;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">Yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">Up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">1</p></div></div></td></tr><tr style="font-family: Montserrat; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Montserrat; float: left; width: 100%;">   <p style="font-family: Montserrat; color: #fff;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Montserrat; color: #ffffff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Montserrat; color:#fff;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Montserrat; color: #ffffff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Montserrat; color:#fff;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></center></html>' where id=36;
update iris.plan set `feature_list_ui_new` = '[{    "id": 1,    "title": "Alerts Available",    "body": "Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery."  },  {    "id": 2,    "title": "Alert Limits",    "body": "Email Alerts - Unlimited | Text Alerts - 500 / mo* (Temperature: 250, Power Loss: 140, Power Back: 30, Humidity: 50, Battery: 15, Gateway not reporting: 10, Geofence: 5)"  },  {    "id": 3,    "title": "Add Monitors",    "body": "Upto 1"  },  {    "id": 4,    "title": "Friends & Family Sharing",    "body": "Add up to 4 members"  },  {    "id": 5,    "title": "GeoFencing",    "body": "Only on Pet Monitor GPS*"  }]' where id=36;

#wag-2
UPDATE `iris`.`plan` SET `feature_list_ui_new` = "<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>body {background-color: transparent;} .table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Montserrat';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><body><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alerts Available</p><p style='color: #fff; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alert Limits</p><p style='color: #fff; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo* (Temperature: 250, Power Loss: 140, Power Back: 30, Humidity: 50, Battery: 15, Gateway not reporting: 10, Geofence: 5)</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #fff; margin-bottom: -5px;'>Upto 2</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #fff; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #fff; margin-bottom: -5px;'>Only on Pet Monitor GPS*</p></td></tr></tbody></table></div></div></div></body></html>" where `id` = '37';
update iris.plan set cur_feature_ui_new='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Montserrat; color:#fff;font-size:18px; font-weight:bolder;font-family: Montserrat;">Wag</p><table style="font-family: Montserrat; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Montserrat; color: #ffffff;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Montserrat; float: left; width: 50%;">                  <p style="font-family: Montserrat; color: #ffffff; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Montserrat; color: #ffffff;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">Yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">Up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">2</p></div></div></td></tr><tr style="font-family: Montserrat; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Montserrat; float: left; width: 100%;">   <p style="font-family: Montserrat; color: #fff;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Montserrat; color: #ffffff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Montserrat; color:#fff;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Montserrat; color: #ffffff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Montserrat; color:#fff;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></center></html>' where id=37;
update iris.plan set `feature_list_ui_new` = '[{    "id": 1,    "title": "Alerts Available",    "body": "Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery."  },  {    "id": 2,    "title": "Alert Limits",    "body": "Email Alerts - Unlimited | Text Alerts - 500 / mo* (Temperature: 250, Power Loss: 140, Power Back: 30, Humidity: 50, Battery: 15, Gateway not reporting: 10, Geofence: 5)"  },  {    "id": 3,    "title": "Add Monitors",    "body": "Upto 2"  },  {    "id": 4,    "title": "Friends & Family Sharing",    "body": "Add up to 4 members"  },  {    "id": 5,    "title": "GeoFencing",    "body": "Only on Pet Monitor GPS*"  }]' where id=37;

#wag-3
UPDATE `iris`.`plan` SET `feature_list_ui_new` = "<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>body {background-color: transparent;} .table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Montserrat';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><body><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alerts Available</p><p style='color: #fff; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alert Limits</p><p style='color: #fff; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo* (Temperature: 250, Power Loss: 140, Power Back: 30, Humidity: 50, Battery: 15, Gateway not reporting: 10, Geofence: 5)</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #fff; margin-bottom: -5px;'>Upto 3</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #fff; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #fff; margin-bottom: -5px;'>Only on Pet Monitor GPS*</p></td></tr></tbody></table></div></div></div></body></html>" where `id` = '38';
update iris.plan set cur_feature_ui_new='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Montserrat; color:#fff;font-size:18px; font-weight:bolder;font-family: Montserrat;">Wag</p><table style="font-family: Montserrat; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Montserrat; color: #ffffff;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Montserrat; float: left; width: 50%;">                  <p style="font-family: Montserrat; color: #ffffff; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Montserrat; color: #ffffff;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">Yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">Up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">3</p></div></div></td></tr><tr style="font-family: Montserrat; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Montserrat; float: left; width: 100%;">   <p style="font-family: Montserrat; color: #fff;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Montserrat; color: #ffffff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Montserrat; color:#fff;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Montserrat; color: #ffffff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Montserrat; color:#fff;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></center></html>' where id=38;
update iris.plan set `feature_list_ui_new` = '[{    "id": 1,    "title": "Alerts Available",    "body": "Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery."  },  {    "id": 2,    "title": "Alert Limits",    "body": "Email Alerts - Unlimited | Text Alerts - 500 / mo* (Temperature: 250, Power Loss: 140, Power Back: 30, Humidity: 50, Battery: 15, Gateway not reporting: 10, Geofence: 5)"  },  {    "id": 3,    "title": "Add Monitors",    "body": "Upto 3"  },  {    "id": 4,    "title": "Friends & Family Sharing",    "body": "Add up to 4 members"  },  {    "id": 5,    "title": "GeoFencing",    "body": "Only on Pet Monitor GPS*"  }]' where id=38;

#wag-4
UPDATE `iris`.`plan` SET `feature_list_ui_new` = "<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>body {background-color: transparent;} .table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Montserrat';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><body><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alerts Available</p><p style='color: #fff; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alert Limits</p><p style='color: #fff; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo* (Temperature: 250, Power Loss: 140, Power Back: 30, Humidity: 50, Battery: 15, Gateway not reporting: 10, Geofence: 5)</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #fff; margin-bottom: -5px;'>Upto 4</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #fff; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #fff; margin-bottom: -5px;'>Only on Pet Monitor GPS*</p></td></tr></tbody></table></div></div></div></body></html>" where `id` = '39';
update iris.plan set cur_feature_ui_new='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Montserrat; color:#fff;font-size:18px; font-weight:bolder;font-family: Montserrat;">Wag</p><table style="font-family: Montserrat; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Montserrat; color: #ffffff;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Montserrat; float: left; width: 50%;">                  <p style="font-family: Montserrat; color: #ffffff; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Montserrat; color: #ffffff;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">Yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">Up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">4</p></div></div></td></tr><tr style="font-family: Montserrat; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Montserrat; float: left; width: 100%;">   <p style="font-family: Montserrat; color: #fff;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Montserrat; color: #ffffff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Montserrat; color:#fff;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Montserrat; color: #ffffff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Montserrat; color:#fff;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></center></html>' where id=39;
update iris.plan set `feature_list_ui_new` = '[{    "id": 1,    "title": "Alerts Available",    "body": "Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery."  },  {    "id": 2,    "title": "Alert Limits",    "body": "Email Alerts - Unlimited | Text Alerts - 500 / mo* (Temperature: 250, Power Loss: 140, Power Back: 30, Humidity: 50, Battery: 15, Gateway not reporting: 10, Geofence: 5)"  },  {    "id": 3,    "title": "Add Monitors",    "body": "Upto 4"  },  {    "id": 4,    "title": "Friends & Family Sharing",    "body": "Add up to 4 members"  },  {    "id": 5,    "title": "GeoFencing",    "body": "Only on Pet Monitor GPS*"  }]' where id=39;

#wag-5
UPDATE `iris`.`plan` SET `feature_list_ui_new` = "<!DOCTYPE html><html><head><meta name='viewport' content='width=device-width, initial-scale=1.0'></head><link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'><script src='https://code.jquery.com/jquery-3.2.1.slim.min.js' integrity='sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN' crossorigin='anonymous'></script><link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js' integrity='sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl' crossorigin='anonymous'></script><style>body {background-color: transparent;} .table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} .left {width:5%;} p {font-family: 'Montserrat';font-size: 13px;} .col-md-12 {float:left;} img {vertical-align: text-top;}</style><body><div class='container'><div class='row custm_cont_2'><div class='col-md-12 col-12 col-xs-12'><table class='table'><tbody><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alerts Available</p><p style='color: #fff; margin-bottom: -5px;'>Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery.</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Alert Limits</p><p style='color: #fff; margin-bottom: -5px;'>Email Alerts - Unlimited | Text Alerts - 500 / mo* (Temperature: 250, Power Loss: 140, Power Back: 30, Humidity: 50, Battery: 15, Gateway not reporting: 10, Geofence: 5)</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Add Monitors</p><p style='color: #fff; margin-bottom: -5px;'>Upto 5</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>Friends & Family Sharing</p><p style='color: #fff; margin-bottom: -5px;'>Add up to 4 members</p></td></tr><tr><td class='left'><img src='https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/white_tick.png' alt=''></td><td ><p style='color: #fff; font-weight: bold; margin-bottom: 0px;'>GeoFencing</p><p style='color: #fff; margin-bottom: -5px;'>Only on Pet Monitor GPS*</p></td></tr></tbody></table></div></div></div></body></html>" where `id` = '40';
update iris.plan set cur_feature_ui_new='<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet"><style>@media only screen and (max-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);}}@media only screen and (min-device-width: 768px){#dog_img {content:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);}}</style><center><img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png" alt="Waggler" id="dog_img"><p style="font-family: Montserrat; color:#fff;font-size:18px; font-weight:bolder;font-family: Montserrat;">Wag</p><table style="font-family: Montserrat; width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;"><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Types of Alerts</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">Temperature, Humidity, </p><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">Power Loss, Power Recovery,</p><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -10px;">LowBattery, </p><p style="font-family: Montserrat; color: #ffffff;">Network</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Alert Notifications</p></div>               <div class="col-md-2 col-2 plan_right"                  style="font-family: Montserrat; float: left; width: 50%;">                  <p style="font-family: Montserrat; color: #ffffff; margin-bottom: -13px;">Unlimited - Email Alerts                  </p>  <p style="font-family: Montserrat; color: #ffffff;">500 - Text Alerts / mo*                  </p>               </div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Geofencing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">Yes</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Friends & Family Sharing</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">Up to 4 members</p></div></div></td></tr><tr><td><div class="row"><div class="col-md-10 col-10 plan_left"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #fff; font-weight: bold;margin-left: 5px;margin-bottom: -5px;">Monitor</p></div><div class="col-md-2 col-2 plan_right"style="font-family: Montserrat; float: left; width: 50%;"><p style="font-family: Montserrat; color: #ffffff; margin-bottom: -5px;">5</p></div></div></td></tr><tr style="font-family: Montserrat; margin-top: 5px;"><td>            <div class="row">               <div class="col-md-10 col-10 plan_left" style="font-family: Montserrat; float: left; width: 100%;">   <p style="font-family: Montserrat; color: #fff;margin-left: 5px;"><b>Note:</b>                  </p>  <p style="font-family: Montserrat; color: #ffffff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Montserrat; color:#fff;">* </b>&nbsp; 500 Text Alerts - (Temperature: 250, Power Loss: 140, Power Recovery: 30, Humidity: 50, Battery: 15, Network: 10, Geofence: 5).                  </p>               </div>                  <p style="font-family: Montserrat; color: #ffffff; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;">                     <b style="font-family: Montserrat; color:#fff;">* </b>&nbsp; Geofence Alert only applicable for Waggle PetMonitor (GPS) device.                  </p>            </div>         </td></tr></table></center></html>' where id=40;
update iris.plan set `feature_list_ui_new` = '[{    "id": 1,    "title": "Alerts Available",    "body": "Temperature, Humidity, Power Loss, Power Recovery, Network and Low Battery."  },  {    "id": 2,    "title": "Alert Limits",    "body": "Email Alerts - Unlimited | Text Alerts - 500 / mo* (Temperature: 250, Power Loss: 140, Power Back: 30, Humidity: 50, Battery: 15, Gateway not reporting: 10, Geofence: 5)"  },  {    "id": 3,    "title": "Add Monitors",    "body": "Upto 5"  },  {    "id": 4,    "title": "Friends & Family Sharing",    "body": "Add up to 4 members"  },  {    "id": 5,    "title": "GeoFencing",    "body": "Only on Pet Monitor GPS*"  }]' where id=40;


UPDATE `iris`.`product_sub_cat` SET `display_order` = '1' WHERE `id` = '9'; 
UPDATE `iris`.`product_sub_cat` SET `display_order` = '2' WHERE `id` = '1'; 
UPDATE `iris`.`product_sub_cat` SET `display_order` = '3' WHERE `id` = '3'; 
UPDATE `iris`.`product_sub_cat` SET `display_order` = '4' WHERE `id` = '6'; 
UPDATE `iris`.`product_sub_cat` SET `display_order` = '5' WHERE `id` = '7'; 
UPDATE `iris`.`product_sub_cat` SET `display_order` = '6' WHERE `id` = '5'; 
UPDATE `iris`.`product_sub_cat` SET `display_order` = '7' WHERE `id` = '8'; 
UPDATE `iris`.`product_sub_cat` SET `display_order` = '8' WHERE `id` = '10'; 
UPDATE `iris`.`product_sub_cat` SET `display_order` = '9' WHERE `id` = '4'; 

UPDATE `iris`.`night_vision_mode` SET `title` = 'Day mode' , `discription` = 'This mode streams in color and is recommended for day time use.' WHERE `id` = '2'; 
UPDATE `iris`.`night_vision_mode` SET `discription` = 'Switch to Day/Night mode based on the amount of light.' WHERE `id` = '1'; 
UPDATE `iris`.`night_vision_mode` SET `title` = 'Night mode' , `discription` = 'This mode streams in black and white and is recommended for night time use' WHERE `id` = '3';

ALTER TABLE `iris`.`country_code` ADD COLUMN `min_zip_code` INT DEFAULT 5 NULL; 

UPDATE `iris`.`country_code` SET `min_zip_code` = '6' WHERE `id` = '3'; 
UPDATE `iris`.`country_code` SET `min_zip_code` = '4' WHERE `id` = '4'; 
UPDATE `iris`.`country_code` SET `min_zip_code` = '6' WHERE `id` = '5';
UPDATE `iris`.`country_code` SET `min_zip_code` = '4' WHERE `id` = '8';

UPDATE `iris`.`shop_feature` SET `title` = 'Latest arrivals' WHERE `id` = '25';

ALTER TABLE `iris`.`gateway_status` ADD COLUMN `show_vehicle_detection` TINYINT(1) DEFAULT 0 NULL;

UPDATE `iris`.`shop_feature` SET `img_url` = 'https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/shop_logo/shop_miniCam_1.png' WHERE `id` = '10';

CREATE TABLE `iris`.`askfeature_list` ( `id` BIGINT NOT NULL AUTO_INCREMENT, `feature_name` VARCHAR(255), `enable` TINYINT(1) NOT NULL DEFAULT 1, PRIMARY KEY (`id`) );

insert into `askfeature_list` (`id`, `feature_name`, `enable`) values('1','Any Feature request','1');
insert into `askfeature_list` (`id`, `feature_name`, `enable`) values('2','Temperature Humidity History','1');
insert into `askfeature_list` (`id`, `feature_name`, `enable`) values('3','Summer & Winter mode','1');
insert into `askfeature_list` (`id`, `feature_name`, `enable`) values('4','Temperature value in Widget','1');
	
CREATE TABLE `gateway_parksafe` (
  `gateway_id` bigint NOT NULL,
  `enable` tinyint(1) NOT NULL DEFAULT '0',
  `last_lat` double NOT NULL DEFAULT '0',
  `last_lon` double NOT NULL DEFAULT '0',
  `radius` double NOT NULL DEFAULT '0',
  `gps_address` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'NA',
  PRIMARY KEY (`gateway_id`)
);	