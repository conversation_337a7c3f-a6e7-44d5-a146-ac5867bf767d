ALTER TABLE `plan_feature`  DROP COLUMN monitortype_id;

ALTER TABLE `plan_feature`  DROP COLUMN device_config;
  
  ALTER TABLE `plan_feature`   DROP COLUMN `plan_period_id`,   DROP INDEX `uk_plan_feature`;

ALTER TABLE `user_feature`  DROP COLUMN monitortype_id;

ALTER TABLE `user_feature`  DROP COLUMN device_config;

CREATE TABLE `user_subscription` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `chargebeeid` varchar(50) NOT NULL,
  `first_planid` varchar(100) NOT NULL DEFAULT 'NA',
  `sub_status` varchar(50) NOT NULL DEFAULT 'NA',
  `first_plan_dt` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `cur_planid` varchar(100) NOT NULL DEFAULT 'NA',
  `cur_plan_dt` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `free_trial_applied` tinyint(1) NOT NULL DEFAULT '0',
  `vpmfirst_planid` varchar(100) NOT NULL DEFAULT 'NA',
  `vpmfirst_plan_dt` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `vpmcur_planid` varchar(100) NOT NULL DEFAULT 'NA',
  `vpmcur_plan_dt` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  `vpmfree_trial_applied` tinyint(1) NOT NULL DEFAULT '0',
  `plan_info` text,
  `addon_info` text,
  PRIMARY KEY (`id`,`chargebeeid`),
  KEY `UK_user` (`user_id`)
);

CREATE TABLE `evalidation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `entity_id` varchar(100) NOT NULL DEFAULT 'NA',
  `entity_password` varchar(100) NOT NULL DEFAULT 'NA',
  `updated_date` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_key` (`entity_id`));
  
  
CREATE TABLE `plan_addon` (
  `plan_id` bigint(20) NOT NULL,
  `addon_id` bigint(20) NOT NULL,  
  `plan_name` varchar(100) NOT NULL,
  `price` varchar(20) NOT NULL,
  KEY `UK_plan_addon` (`plan_id`,`addon_id`)
);

ALTER TABLE `alertcfg`   ADD COLUMN `first_notifyfreq` INT DEFAULT 0 NOT NULL;

ALTER TABLE `alerttype`  ADD COLUMN `min_notifyfreq` INT DEFAULT 0 NOT NULL;

UPDATE `alerttype` SET min_notifyfreq = 3600;
UPDATE `alerttype` SET min_notifyfreq = 1800 WHERE id=1;

CREATE TABLE `old_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `meid` varchar(20) NOT NULL DEFAULT 'NA',
  `qrc` varchar(20) NOT NULL DEFAULT 'NA',
  PRIMARY KEY (`id`),
  UNIQUE KEY `meid,qrc` (`meid`,`qrc`),
  KEY `QRC` (`qrc`)
 );
insert into `oauth_client_details` (`client_id`, `resource_ids`, `client_secret`, `scope`, `authorized_grant_types`, `web_server_redirect_uri`, `authorities`, `access_token_validity`, `refresh_token_validity`, `additional_information`, `autoapprove`) values('app','oauth2-resource','$2a$10$YsvUm0Eq4ZGF5mzPaH5V5u0S0CQABgfFw2DHtSFy5ASnxaWTuFuAe','read','authorization_code,refresh_token,password','http://localhost:8090/showdata/,https://layla.amazon.com/api/skill/link/M18BLXX6V6826P,https://pitangui.amazon.com/api/skill/link/M18BLXX6V6826P,https://alexa.amazon.co.jp/api/skill/link/M18BLXX6V6826P,https://inapp.mywaggle.com/,https://wagglepet.test-app.link/alexa/,https://wagglepet.app.link/alexa/','ROLE_read','3600','-1',NULL,NULL);

insert into `oauth_client_details` (`client_id`, `resource_ids`, `client_secret`, `scope`, `authorized_grant_types`, `web_server_redirect_uri`, `authorities`, `access_token_validity`, `refresh_token_validity`, `additional_information`, `autoapprove`) values('web','oauth2-resource','$2a$10$cSqEuFgvDssxyP6Hdo/Q0edgy31nWW6RpRKWqCUEN01lOMo9Uc/rO','read,write,delete','authorization_code,refresh_token,password','http://localhost:8090/showdata/,https://layla.amazon.com/api/skill/link/M18BLXX6V6826P,https://pitangui.amazon.com/api/skill/link/M18BLXX6V6826P,https://alexa.amazon.co.jp/api/skill/link/M18BLXX6V6826P,https://inapp.mywaggle.com/,https://wagglepet.test-app.link/alexa/,https://wagglepet.app.link/alexa/','ROLE_read','18000','-1',NULL,NULL);

UPDATE alertcfg SET notificationtype = CONCAT(notificationtype, '0');

CREATE TABLE `forceupdate` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) NOT NULL,
  `android_version` varchar(20) NOT NULL DEFAULT 'NA',
  `ios_version` varchar(20) NOT NULL DEFAULT 'NA',
  `enable` tinyint(4) NOT NULL DEFAULT '1',
  `createOn` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `Unique` (`userid`)
);

ALTER TABLE `user` ADD COLUMN `password_ver` VARCHAR(10) DEFAULT 'V1';
ALTER TABLE `user` CHANGE `password` `password` VARCHAR(255);

ALTER TABLE `version_mapping` ADD COLUMN `enable_appnotify` TINYINT(1) DEFAULT 1 NOT NULL , ADD COLUMN `enable_getsocial` TINYINT(1) DEFAULT 1 NOT NULL ; 

ALTER TABLE `plan` ADD COLUMN `cur_feature` TEXT NULL;

ALTER TABLE `schedule_alert` ADD COLUMN `featurecode_alert` VARCHAR(100) DEFAULT 'NA' NOT NULL;

ALTER TABLE `plan_to_period`   
	ADD COLUMN `content_1` VARCHAR(100) DEFAULT 'NA' NOT NULL ,
	ADD COLUMN `content_2` VARCHAR(100) DEFAULT 'NA' NOT NULL ,
	ADD COLUMN `content_3` VARCHAR(100) DEFAULT 'NA' NOT NULL ,
	ADD COLUMN `content_4` VARCHAR(100) DEFAULT 'NA' NOT NULL ;
	
UPDATE plan_to_period SET content_1 = 'Half-Yearly',content_2='$105',content_3='$17.5 / mon', content_4='NA' WHERE id = 44;
UPDATE plan_to_period SET content_1 = 'Yearly',content_2='$180',content_3='$15 / mon', content_4='1 day trial' WHERE id = 45;
UPDATE plan_to_period SET content_1 = '2 Year',content_2='$300',content_3='$12.5 / mon', content_4='2 days trial' WHERE id = 46;

UPDATE plan_to_period SET content_1 = 'Half-Yearly',content_2='$135',content_3='$22.5 / mon', content_4='NA' WHERE id = 47;
UPDATE plan_to_period SET content_1 = 'Yearly',content_2='$210',content_3='$17.5 / mon', content_4='1 day trial' WHERE id = 48;
UPDATE plan_to_period SET content_1 = '2 Year',content_2='$360',content_3='$15 / mon', content_4='2 days trial' WHERE id = 49;

UPDATE plan_to_period SET content_1 = 'Half-Yearly',content_2='$150',content_3='$25 / mon', content_4='NA' WHERE id = 50;
UPDATE plan_to_period SET content_1 = 'Yearly',content_2='$270',content_3='$22.5 / mon', content_4='1 day trial' WHERE id = 51;
UPDATE plan_to_period SET content_1 = '2 Year',content_2='$420',content_3='$17.5 / mon', content_4='2 days trial' WHERE id = 52;


ALTER TABLE `plan_to_period` ADD COLUMN `country_code` VARCHAR(25) NOT NULL DEFAULT 'US';


INSERT INTO `plan` (`plan_name`, `description`, `enable`, `custom`, `orderno`, `display_msg`, `alert_setting`, `pricecompare_period`, `plan_type`, `plan_ver`, `feature_list`, `device_cnt`, `cur_feature`, `feature_list_flutter`) VALUES('Waggler Canada','5-Pet Monitor','1','0','1','NA','1','1','Data-Plan','V2','<center>\r\n   <table style=\"width: 100%;font-size: 14px;\">\r\n      <tr style=\"background-color: #ece3e34d;\">\r\n         <td style=\"border-top-right-radius: 10px;  border-top-left-radius: 10px;\">\r\n            <div class=\"row\">\r\n               <div class=\"col-md-10 col-10 plan_left\"\r\n                  style=\"float: left; width: 50%;\">\r\n                  <p style=\"color: #000; font-weight: bold;margin-left: 5px;\">Alerts</p>\r\n               </div>\r\n               <div class=\"col-md-2 col-2 plan_right\"\r\n                  style=\"float: left; width: 50%;\">\r\n                  <p style=\"color: #808080;\">Temperature alerts, Humidity alerts, Power Loss alerts, Power Recovery alerts, Network alerts and Low Battery alerts</p>\r\n               </div>\r\n            </div>\r\n         </td>\r\n      </tr>\r\n      <tr style=\"background-color: #ece3e34d;\">\r\n         <td>\r\n            <div class=\"row\">\r\n               <div class=\"col-md-10 col-10 plan_left\"\r\n                  style=\"float: left; width: 50%;\">\r\n                  <p style=\"color: #000; font-weight: bold;margin-left: 5px;\">Alert Notifications</p>\r\n               </div>\r\n               <div class=\"col-md-2 col-2 plan_right\"\r\n                  style=\"float: left; width: 50%;\">\r\n                  <p style=\"color: #808080; margin-bottom: -13px;\">Unlimited - Email Alerts\r\n                  </p>\r\n				  <p style=\"color: #808080;\">1000 - Text Alerts / mo*\r\n                  </p>\r\n               </div>\r\n            </div>\r\n         </td>\r\n      </tr>\r\n      <tr style=\"background-color: #ece3e34d;\">\r\n         <td>\r\n            <div class=\"row\">\r\n               <div class=\"col-md-10 col-10 plan_left\"\r\n                  style=\"float: left; width: 50%;\">\r\n                  <p style=\"color: #000; font-weight: bold;margin-left: 5px;\">Add Monitor upto</p>\r\n               </div>\r\n               <div class=\"col-md-2 col-2 plan_right\"\r\n                  style=\"float: left; width: 50%;\">\r\n                  <p style=\"color: #808080; margin-bottom: -13px;\">2</p>\r\n               </div>\r\n            </div>\r\n         </td>\r\n      </tr>\r\n      <tr style=\"background-color: #ece3e34d;\">\r\n         <td>\r\n            <div class=\"row\">\r\n               <div class=\"col-md-10 col-10 plan_left\"\r\n                  style=\"float: left; width: 50%;\">\r\n                  <p style=\"color: #000; font-weight: bold;margin-left: 5px;\">Friends & Family </p>\r\n                  <p style=\"color: #000; font-weight: bold;margin-left: 5px;margin-top: -10px;\">Sharing</p>\r\n               </div>\r\n               <div class=\"col-md-2 col-2 plan_right\"\r\n                  style=\"float: left; width: 50%;\">\r\n                  <p style=\"color: #808080; margin-bottom: -13px;\"> Add up to 4\r\n                     members\r\n                  </p>\r\n               </div>\r\n            </div>\r\n         </td>\r\n      </tr>\r\n      <tr style=\"background-color: #ece3e34d;\">\r\n         <td style=\"border-bottom-right-radius: 10px;  border-bottom-left-radius: 10px;\">\r\n            <div class=\"row\">\r\n               <div class=\"col-md-10 col-10 plan_left\"style=\"float: left; width: 50%;\">\r\n                  <p style=\"color: #000; font-weight: bold;margin-left: 5px;\">Geofencing</p>\r\n               </div>\r\n               <div class=\"col-md-2 col-2 plan_right\"\r\n                  style=\"float: left; width: 50%;\">\r\n                  <p style=\"color: #808080; margin-bottom: -13px;\">Only on Pet Monitor GPS</p>\r\n               </div>\r\n            </div>\r\n         </td>\r\n      </tr>\r\n   </table>\r\n   <table style=\"width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;\">\r\n      <tr style=\"border-radius: 20px;\">\r\n         <td>\r\n            <div class=\"row\">\r\n               <div class=\"col-md-10 col-10 plan_left\" style=\"float: left; width: 100%;\">\r\n			   <p style=\"color: #000; font-size: 13px;\"><b>Note:</b>\r\n                  </p>\r\n				  <p style=\"color: #808080; font-size: 12px; float: left; margin-top: -6px;\">\r\n                     <b style=\"color:#000;\">* </b>&nbsp; 1000 Text Alerts - (Temperature: 450, Power Loss: 300, Power Recovery: 90, Humidity: 90, Battery: 30, Network: 20, Geofence: 20).\r\n                  </p>\r\n               </div>\r\n                  <p style=\"color: #808080; font-size: 12px; float: left; margin-top: -6px;\">\r\n                     <b style=\"color:#000;\">* </b>&nbsp; All plans will be auto-renewed.\r\n                  </p>\r\n            </div>\r\n         </td>\r\n      </tr>\r\n   </table>\r\n</center>','5','<link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\r\n<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\r\n<link href=\"https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,200;1,200;1,300&display=swap\" rel=\"stylesheet\">\r\n\r\n<style>\r\n@media only screen and (max-device-width: 768px){\r\n#dog_img {\r\ncontent:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png);\r\n}\r\n}\r\n\r\n@media only screen and (min-device-width: 768px){\r\n#dog_img {\r\ncontent:url(https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog_ipad.png);\r\n}\r\n}\r\n</style>\r\n\r\n<center>\r\n<img src=\"https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png\" alt=\"Waggler\" id=\"dog_img\">\r\n<p style=\"color:#ed5f5f;font-size:18px; font-weight:bolder;font-family: \'Inter\', sans-serif;\">Waggler</p>\r\n<table style=\"width: 100%; -webkit-touch-callout: none;-webkit-user-select: none;\"><tr><td><div class=\"row\"><div class=\"col-md-10 col-10 plan_left\"\r\nstyle=\"float: left; width: 50%;\"><p style=\"color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;\">Types of Alerts</p></div><div class=\"col-md-2 col-2 plan_right\"\r\nstyle=\"float: left; width: 50%;\"><p style=\"color: #808080; margin-bottom: -10px;\">Temperature, Humidity Alerts, </p><p style=\"color: #808080; margin-bottom: -10px;\">Power Loss & Power Recovery Alerts,</p><p style=\"color: #808080; margin-bottom: -10px;\">Low\r\nBattery Alerts and </p><p style=\"color: #808080;\">Network Alerts</p></div></div></td></tr><tr><td><div class=\"row\"><div class=\"col-md-10 col-10 plan_left\"\r\nstyle=\"float: left; width: 50%;\"><p style=\"color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;\">Alert Notifications</p></div>\r\n               <div class=\"col-md-2 col-2 plan_right\"\r\n                  style=\"float: left; width: 50%;\">\r\n                  <p style=\"color: #808080; margin-bottom: -13px;\">Unlimited - Email Alerts\r\n                  </p>\r\n				  <p style=\"color: #808080;\">1000 - Text Alerts / mo*\r\n                  </p>\r\n               </div></div></td></tr>\r\n<tr><td><div class=\"row\"><div class=\"col-md-10 col-10 plan_left\"\r\nstyle=\"float: left; width: 50%;\"><p style=\"color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;\">Geofencing</p></div><div class=\"col-md-2 col-2 plan_right\"\r\nstyle=\"float: left; width: 50%;\"><p style=\"color: #808080; margin-bottom: -5px;\">yes</p></div></div></td></tr>\r\n<tr><td><div class=\"row\"><div class=\"col-md-10 col-10 plan_left\"\r\nstyle=\"float: left; width: 50%;\"><p style=\"color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;\">Friends & Family Sharing</p></div><div class=\"col-md-2 col-2 plan_right\"\r\nstyle=\"float: left; width: 50%;\"><p style=\"color: #808080; margin-bottom: -5px;\">4</p></div></div></td></tr>\r\n<tr><td><div class=\"row\"><div class=\"col-md-10 col-10 plan_left\"\r\nstyle=\"float: left; width: 50%;\"><p style=\"color: #000; font-weight: bold;margin-left: 5px;margin-bottom: -5px;\">Add Monitor Upto</p></div><div class=\"col-md-2 col-2 plan_right\"\r\nstyle=\"float: left; width: 50%;\"><p style=\"color: #808080; margin-bottom: -5px;\">2</p></div></div></td></tr>\r\n<tr style=\"margin-top: 5px;\">\r\n<td>\r\n            <div class=\"row\">\r\n               <div class=\"col-md-10 col-10 plan_left\" style=\"float: left; width: 100%;\">\r\n			   <p style=\"color: #000;margin-left: 5px;\"><b>Note:</b>\r\n                  </p>\r\n				  <p style=\"color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;\">\r\n                     <b style=\"color:#000;\">* </b>&nbsp; 1000 Text Alerts - (Temperature: 450, Power Loss: 300, Power Recovery: 90, Humidity: 90, Battery: 30, Network: 20, Geofence: 20).\r\n                  </p>\r\n               </div>\r\n                  <p style=\"color: #808080; float: left; margin-top: -6px;margin-left: 5px;font-size:14px;\">\r\n                     <b style=\"color:#000;\">* </b>&nbsp; Geofencing Alert only applicable for Waggle Pet\r\n					Monitor (GPS) device.\r\n                  </p>\r\n            </div>\r\n         </td>\r\n	</tr>\r\n</table></center>','<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><script src=\'https://code.jquery.com/jquery-3.2.1.slim.min.js\' integrity=\'sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMV/GpGFF93hXpG5KkN\' crossorigin=\'anonymous\'></script><link href=\'https://fonts.googleapis.com/css?family=Nunito  Sans\' rel=\'stylesheet\'><script src=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js\' integrity=\'sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl\' crossorigin=\'anonymous\'></script><style>.table td, .table th {border-top: none;}.table thead th {border-bottom: none;}.container {overflow-x: hidden;margin-left: 0px;} p {font-family: \'Nunito Sans\';font-size: 16px;} img {width: 20px;height: 20px;}</style><div class=\'container\'><div class=\'row custm_cont_2\'><div class=\'col-md-12 col-12 col-xs-12\'><table class=\'table\'><tbody><tr><td class=\'left\'><img style=\"margin-top: 10%;\" src=\"https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg\" alt=\"\"></td><td class=\'head\'><p style=\"color: #000; font-weight: bold;\">Alerts</p></td></tr><tr><td class=\'left_empty\'></td><td class=\'right\'><p style=\"color: #808080;margin-top: -3%;font-size: 14px;\">Temperature alerts, Humidity alerts, Power Loss alerts,Power Recovery alerts, Network alerts and Low Battery alerts.</p></td></tr><tr><td class=\'left\'><img style=\"margin-top: -47%;\" style=\"margin-top: -47%;\"src=\"https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg\" alt=\"\"></td><td class=\'head\'><p style=\"color: #000;  margin-top: -3%;margin-top: -3%;font-weight: bold;\">Alert Notifications</p></td></tr><tr><td class=\'left_empty\'></td><td class=\'right\'><p style=\"color: #808080;margin-top: -3%;font-size: 14px;\">Unlimited - Email Alerts | 500 - Text Alerts / mo*</p></td></tr><tr><td class=\'left\'><img style=\"margin-top: -47%;\" src=\"https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg\" alt=\"\"></td><td class=\'head\'><p style=\"color: #000;  margin-top: -3%;font-weight: bold;\">Add Monitors</p></td></tr><tr><td class=\'left_empty\'></td><td class=\'right\'><p style=\"color: #808080;margin-top: -3%;font-size: 14px;\">Upto 4</p></td></tr><tr><td class=\'left\'><img style=\"margin-top: -47%;\" src=\"https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg\" alt=\"\"></td><td class=\'head\'><p style=\"color: #000; margin-top: -3%;font-weight: bold;\">Friends & Family Sharing</p></td></tr><tr><td class=\'left_empty\'></td><td class=\'right\'><p style=\"color: #808080;margin-top: -3%;font-size: 14px;\">Add up to 4 members</p></td></tr><tr><td class=\'left\'><img style=\"margin-top: -47%;\" src=\"https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/green_tick.svg\" alt=\"\"></td><td class=\'head\'><p style=\"color: #000; margin-top: -3%; font-weight: bold;\">Geofencing</p></td></tr><tr><td class=\'left_empty\'></td><td class=\'right\'><p style=\"color: #808080;margin-top: -3%;font-size: 14px;\">Only on Pet Monitor GPS*</p></td></tr></tbody></table></div></div></div>');


INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','2','1','5','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','3','1','5','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','4','1','5','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','5','1','15','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','6','0','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','7','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','8','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','9','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','10','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','11','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','12','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','13','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','14','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','15','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','16','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','17','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','18','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','19','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','20','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','21','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','22','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','23','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','24','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','25','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','26','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','27','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','28','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','29','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','30','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','31','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','32','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','33','1','500','0','1','0');
INSERT INTO `plan_feature` (`plan_id`, `feature_id`, `enable`, `txn_limit`, `extra_txn_limit`, `resettype_id`, `unlimited_cr`) VALUES('33','34','1','500','0','1','0');

INSERT INTO `plan_to_period` (`plan_id`, `sub_period_id`, `chargebee_planid`, `custom`, `enable`, `strike_price`, `ios_planid`, `ios_price`, `ios_showplan`, `free_trial_days`, `offer_desc`, `offer_id`, `offer_label`, `offer_content`, `cb_addon_id`, `cb_coupon_id`, `plan_price`, `content_1`, `content_2`, `content_3`, `content_4`, `is_best_deal`, `img_url`, `country_code`, `is_recharge_plan`) VALUES('33','1','waggler-monthly-cad','0','1','0','NA','0','0','0','NA','0','NA','NA','NA','NA','NA','Monthly','$49','NA','NA','0','https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/monthly_plan.png','CA','0');
INSERT INTO `plan_to_period` (`plan_id`, `sub_period_id`, `chargebee_planid`, `custom`, `enable`, `strike_price`, `ios_planid`, `ios_price`, `ios_showplan`, `free_trial_days`, `offer_desc`, `offer_id`, `offer_label`, `offer_content`, `cb_addon_id`, `cb_coupon_id`, `plan_price`, `content_1`, `content_2`, `content_3`, `content_4`, `is_best_deal`, `img_url`, `country_code`, `is_recharge_plan`) VALUES('33','3','waggler-half-yearly-cad','0','1','0','NA','0','0','0',NULL,'0','NA',NULL,'NA','NA','<center ><p style=\"font-size: 17px;margin-bottom: -30px;color:#000;font-family: \'Poppins\', sans-serif;font-weight: 600;\">Half-Yearly</p><p style=\"font-size: 32px;margin-bottom: -20px;color: #000;font-family: \'Poppins\', sans-serif;font-weight: bolder\">$150</p><p style=\"font-size: 17px;margin-bottom: -5px;color: #717171;font-family: \'Poppins\', sans-serif;font-weight: 600;\">$25 / mon</p></center>','Half-Yearly','$199','$25 / mon','2 days trial','0','https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/current_plan_dog.png','CA','0');
INSERT INTO `plan_to_period` (`plan_id`, `sub_period_id`, `chargebee_planid`, `custom`, `enable`, `strike_price`, `ios_planid`, `ios_price`, `ios_showplan`, `free_trial_days`, `offer_desc`, `offer_id`, `offer_label`, `offer_content`, `cb_addon_id`, `cb_coupon_id`, `plan_price`, `content_1`, `content_2`, `content_3`, `content_4`, `is_best_deal`, `img_url`, `country_code`, `is_recharge_plan`) VALUES('33','4','waggler-yearly-cad','0','1','0','NA','0','0','1',NULL,'0','NA',NULL,'NA','NA','<center ><p style=\"font-size: 17px;margin-bottom: -30px;color:#000;font-family: \'Poppins\', sans-serif;font-weight: 600;\">Yearly</p><p style=\"font-size: 32px;margin-bottom: -15px;color: #000;font-family: \'Poppins\', sans-serif;font-weight: bolder\">$270</p><p style=\"font-size: 17px;margin-bottom: -5px;color: #717171;font-family: \'Poppins\', sans-serif;font-weight: 600;\">$22.5 / mon</p></center>','Yearly','$299','$22.5 / mon','1 day trial','0','https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/yearly_plan.png','CA','0');
INSERT INTO `plan_to_period` (`plan_id`, `sub_period_id`, `chargebee_planid`, `custom`, `enable`, `strike_price`, `ios_planid`, `ios_price`, `ios_showplan`, `free_trial_days`, `offer_desc`, `offer_id`, `offer_label`, `offer_content`, `cb_addon_id`, `cb_coupon_id`, `plan_price`, `content_1`, `content_2`, `content_3`, `content_4`, `is_best_deal`, `img_url`, `country_code`, `is_recharge_plan`) VALUES('33','5','waggler-2year-cad','0','1','0','NA','0','0','2',NULL,'0','NA',NULL,'NA','NA','<center ><p style=\"font-size: 17px;margin-bottom: -30px;color:#000;font-family: \'Poppins\', sans-serif;font-weight: 600;\">2 Year</p><p style=\"font-size: 32px;margin-bottom: -15px;color: #000;font-family: \'Poppins\', sans-serif;font-weight: bolder\">$420</p><p style=\"font-size: 17px;margin-bottom: -5px;color: #717171;font-family: \'Poppins\', sans-serif;font-weight: 600;\">$17.5 / mon</p></center>','2 Year','$499','$17.5 / mon','2 days trial','1','https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/2year_plan.png','CA','0');

INSERT INTO `iris`.`plan_offer` (`plan_period_id`, `coupon_id`, `offer_desc1`, `offer_desc2`, `enable`, `expiry_date`) VALUES ('91', 'CAN10','<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;padding: 0px;position: absolute;}</style><div class=\'container\'><table class=\'table\'><tbody><tr><td><p style=\'color:#000;font-size: 15px;margin-bottom: 2px;\'><b>2 Year : </b>$30 OFF</p><p style=\'color:#6a6a6a;font-size: 15px; margin-bottom: 0px;\'>Use Coupon : <b style=\'color:#ec5757;\'>WAGGLE30</b></p></td></tr></tbody></table></div>','<link rel=\'stylesheet\' href=\'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\' integrity=\'sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\' crossorigin=\'anonymous\'><style>.table td,.table th{border-top:none;padding: 0px;}</style><div class=\'container\' style=\'padding-left: 0px;padding-right: 0px;\'><table class=\'table\'><tbody><tr><td><center><p style=\'color:#000;font-size: 18px;padding-left: 5px;\'><b>Coupon Applied Successfully</b></p><p style=\'color:#6a6a6a;font-size: 17px; margin-top: -10px;padding-left: 5px;\'>Waggler 2 Year with $30 offer</p></center></td></tr></tbody></table></div>',1,'2023-12-30 00:00:00');



