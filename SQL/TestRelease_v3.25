ALTER TABLE `iris`.`version_mapping` ADD COLUMN `show_cancel_sub` TINYINT(1) DEFAULT 0 NOT NULL;

ALTER TABLE gateway DROP COLUMN PL_PB_delay;
ALTER TABLE `assetmodel` DROP COLUMN is_PLPB_delay;

ALTER TABLE `iris`.`user_metadata` CHANGE `vpm_id` `vpm_id` VARCHAR(100) DEFAULT 'NA' NOT NULL;
ALTER TABLE `iris`.`user_metadata` ADD COLUMN `show_marketing_notif` TINYINT(1) DEFAULT 1 NOT NULL;
ALTER TABLE `iris`.`user_metadata` DROP INDEX `UK_vpmid`, ADD UNIQUE INDEX `UK_vpmid` (`vpm_id` , `user_id`); 

DELETE  FROM external_config WHERE parametername IN ( 'livetrackreportinterval',
'enablelivetrackwithota',
'disablelivetrackwithota',
'livetrackingreportlimit',
'subscriptionmonth',
'getordermapcounturl',
'saveorupdateorder',
'ordermapurl',
'updateirisaccount',
'deleteordermap',
'assignsubscription',
'buynowfurbit',
'buynowpetsafety',
'freeplan',
'customplan',
'omitplan',
'vpmplan',
'supportcontactnumber',
'supportcontactemail',
'niomip',
'niomauthkey');

CREATE TABLE `pl_threshold_status` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `gateway_id` bigint(20) NOT NULL DEFAULT '0',
  `pl_threshold` int(5) NOT NULL DEFAULT '0',
  `status` varchar(250) NOT NULL DEFAULT 'NA',
  `updated_on` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_gateway_id` (`gateway_id`)
);

ALTER TABLE `iris`.`assetmodel` ADD COLUMN `default_pl_threshold` INT(5) DEFAULT 0 NOT NULL;
ALTER TABLE `iris`.`assetmodel` ADD COLUMN `is_pl_threshold` TINYINT(1) DEFAULT 0 NOT NULL;
ALTER TABLE `iris`.`gateway` ADD COLUMN `pl_threshold` INT(5) DEFAULT 0 NOT NULL;
