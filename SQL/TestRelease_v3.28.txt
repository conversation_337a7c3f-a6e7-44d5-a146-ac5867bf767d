ALTER TABLE `plan_to_period` ADD COLUMN `is_recharge_plan` TINYINT(1) DEFAULT 0 NOT NULL;

ALTER TABLE `user` 	ADD COLUMN `recharge_custid` VARCHAR(100) DEFAULT 'NA' NOT NULL ;
	
CREATE TABLE `recharge_cancel_customer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `re_cust_id` varchar(50) NOT NULL,
  `created_on` datetime NOT NULL DEFAULT '1753-01-01 00:00:00',
  PRIMARY KEY (`id`)
);

CREATE TABLE `recharge_cb_webhook_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cb_id` varchar(50) NOT NULL,
  `cb_subid` varchar(50) NOT NULL,
  `re_subid` varchar(50) NOT NULL,
  `event_status` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_re_sub` (`re_subid`)
);

CREATE TABLE `recharge_latest_sub_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(100) NOT NULL,
  `sub_id` varchar(100) NOT NULL,
  `customer_id` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL DEFAULT 'NA',
  `sku` varchar(100) NOT NULL,
  `nextrenewal_at` datetime DEFAULT NULL,
  `status` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `cancelled_at` datetime DEFAULT NULL,
  `event_type` varchar(50) NOT NULL DEFAULT 'NA',
  `cb_plan` varchar(50) NOT NULL DEFAULT 'NA',
  `is_sub_activated` tinyint(1) NOT NULL DEFAULT '0',
  `enable` tinyint(1) NOT NULL DEFAULT '1',
  `cb_sub_status` varchar(100) NOT NULL DEFAULT 'NA',
  `price` float NOT NULL DEFAULT '0',
  `order_count` int(11) NOT NULL DEFAULT '0',
  `mapped_count` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_order_customer` (`order_id`,`customer_id`),
  KEY `IDX_orderid` (`order_id`)
);

CREATE TABLE `recharge_sub_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(100) NOT NULL,
  `sub_id` varchar(100) NOT NULL,
  `customer_id` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL DEFAULT 'NA',
  `sku` varchar(100) NOT NULL,
  `nextrenewal_at` datetime DEFAULT NULL,
  `status` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `cancelled_at` datetime DEFAULT NULL,
  `event_type` varchar(50) NOT NULL DEFAULT 'NA',
  `cb_plan` varchar(50) NOT NULL DEFAULT 'NA',
  `price` float NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `IDX_orderid` (`order_id`)
);