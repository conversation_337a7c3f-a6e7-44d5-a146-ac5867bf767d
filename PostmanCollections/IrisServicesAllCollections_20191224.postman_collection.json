{"info": {"_postman_id": "503f75a7-5be3-4519-b037-da281f44e50e", "name": "IrisServicesAllCollections", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "AccountController", "item": [{"name": "v3.0/createapi", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"id\":9,\n\t\"serviceType\" : \"irisV4Test\",\n\t\"apiType\" : \"irisV4Test\",\n\t\"description\" : \"irisV4Test\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/createapi/6a6dd5969a0a72b1f39abd2809d4d00998742d60", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "<PERSON><PERSON><PERSON>", "6a6dd5969a0a72b1f39abd2809d4d00998742d60"]}}, "response": []}, {"name": "createapiaccount", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\n\t\"apidetails\" : 4,\n\t\"company\" : 792,\n\t\"totalTransaction\" : *********,\n\t\"remainingTransaction\" : *********,\n\t\"totalCount\" : 5\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/createapiaccount/6a6dd5969a0a72b1f39abd2809d4d00998742d60", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "createapiaccount", "6a6dd5969a0a72b1f39abd2809d4d00998742d60"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "AlertCfgController", "item": [{"name": "Get AlertCfg", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/alertcfg/88a8a647ccc49bad69bfd68b2b56f073b2568c10?alertcfgid=&alerttypeid=&assetid=", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "alertcfg", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "alertcfgid", "value": ""}, {"key": "alerttypeid", "value": ""}, {"key": "assetid", "value": ""}]}}, "response": []}, {"name": "Save/ Update AlertCfg v4", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/alertcfg/88a8a647ccc49bad69bfd68b2b56f073b2568c10?  id  =1&name=battery alert&minval=0&maxval=0&rule=null&lat=0&lon=0&radius=0&fencetype=0&severity=0&enable=false&levelpattern=null&notifyfreq=0&mobilenos=null&country=null&emailids=null&alertmsg=null&intermittentfreq=1&notificationtype=null&alertstarttime=null&alertendtime=null&voicealertstarttime=null&voicealertstoptime=null&alerttypeid=1&assetids=2", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "alertcfg", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "  id  ", "value": "1"}, {"key": "name", "value": "battery alert"}, {"key": "min<PERSON>", "value": "0"}, {"key": "maxval", "value": "0"}, {"key": "rule", "value": "null"}, {"key": "lat", "value": "0"}, {"key": "lon", "value": "0"}, {"key": "radius", "value": "0"}, {"key": "fencetype", "value": "0"}, {"key": "severity", "value": "0"}, {"key": "enable", "value": "false"}, {"key": "levelpattern", "value": "null"}, {"key": "notifyfreq", "value": "0"}, {"key": "mobilenos", "value": "null"}, {"key": "country", "value": "null"}, {"key": "emailids", "value": "null"}, {"key": "alertmsg", "value": "null"}, {"key": "intermittentfreq", "value": "1"}, {"key": "notificationtype", "value": "null"}, {"key": "alertstarttime", "value": "null"}, {"key": "alertendtime", "value": "null"}, {"key": "voicealerts<PERSON>ttime", "value": "null"}, {"key": "voicealertstoptime", "value": "null"}, {"key": "alerttypeid", "value": "1"}, {"key": "assetids", "value": "2"}]}}, "response": []}, {"name": "enabledisablealertcfg", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/enabledisablealertcfg/88a8a647ccc49bad69bfd68b2b56f073b2568c10?alertcfgids=1&isenable=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "enabledisablealertcfg", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "alertcfgids", "value": "1"}, {"key": "isenable", "value": "1"}]}}, "response": []}, {"name": "v4-alertcfg", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v4.0/alertcfg/2c645697ee35d47f6591b349ac304cb85779d71c?alertcfgid&alerttypeid&assetid=9563", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v4.0", "alertcfg", "2c645697ee35d47f6591b349ac304cb85779d71c"], "query": [{"key": "alertcfgid", "value": null}, {"key": "alerttypeid", "value": null}, {"key": "assetid", "value": "9563"}]}}, "response": []}, {"name": "updateemailphone", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/updateemailphone/88a8a647ccc49bad69bfd68b2b56f073b2568c10?alertcfgids=3441,1140,1154,1171&phonenos=1-6304618742,09659428385&emails=rick<PERSON><PERSON><PERSON>@me.com,kalaisel<PERSON>@nimblewireless.com", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "updateemailphone", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "alertcfgids", "value": "3441,1140,1154,1171"}, {"key": "phonenos", "value": "1-6304618742,09659428385"}, {"key": "emails", "value": "rick<PERSON><PERSON><EMAIL>,kalai<PERSON><PERSON>@nimblewireless.com"}]}}, "response": []}, {"name": "v4-alertcfg", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v4.0/alertcfg/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v4.0", "alertcfg", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "alertcfgid", "value": "1116", "disabled": true}, {"key": "notifyfreq", "value": "700", "disabled": true}, {"key": "min<PERSON>", "value": "5", "disabled": true}, {"key": "alerttypeid", "value": "", "disabled": true}, {"key": "assetid", "value": "", "disabled": true}]}}, "response": []}, {"name": "v4-alertcfg-update", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"alertcfgids\": \"10872,10873,10874,10875\",\r\n    \"minval\": 23,\r\n    \"maxval\": 100,\r\n    \"notifyfreq\": 800,\r\n    \"mobilenos\": \"1234567890\",\r\n\t\"emailids\": \"<EMAIL>\",\r\n    \"assetids\": \"10477\",\r\n    \"alerttypeids\": \"1\",\r\n    \"updatefor\": \"updatenotifyfreq\",\r\n    \"enable\": false,\r\n    \"os\": \"ios\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8080/irisservices/v4.0/alertcfg/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8080", "path": ["irisservices", "v4.0", "alertcfg", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "alertcfgid", "value": "5412", "disabled": true}, {"key": "gatewayid", "value": "3353", "disabled": true}, {"key": "min<PERSON>", "value": "21", "disabled": true}, {"key": "maxval", "value": "81", "disabled": true}, {"key": "alerttypeid", "value": "1", "disabled": true}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item": [{"name": "alert", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/alert/88a8a647ccc49bad69bfd68b2b56f073b2568c10?groupid=1&subgroupid=2&gatewayid=123&alerttypeid=2&deliquencystatus&nodeid=11&id=234", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "alert", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "groupid", "value": "1"}, {"key": "subgroupid", "value": "2"}, {"key": "gatewayid", "value": "123"}, {"key": "alerttypeid", "value": "2"}, {"key": "deliquencystatus", "value": null}, {"key": "nodeid", "value": "11"}, {"key": "id", "value": "234"}]}}, "response": []}, {"name": "alertV2", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/alertV2/88a8a647ccc49bad69bfd68b2b56f073b2568c10?groupid=1&subgroupid=2&gatewayid=123&alerttypeid=2&deliquencystatus&nodeid=11&id=234&fromtime=&totime=", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "alertV2", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "groupid", "value": "1"}, {"key": "subgroupid", "value": "2"}, {"key": "gatewayid", "value": "123"}, {"key": "alerttypeid", "value": "2"}, {"key": "deliquencystatus", "value": null}, {"key": "nodeid", "value": "11"}, {"key": "id", "value": "234"}, {"key": "fromtime", "value": ""}, {"key": "totime", "value": ""}]}}, "response": []}, {"name": "alertV3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/alertV3/88a8a647ccc49bad69bfd68b2b56f073b2568c10?groupid=1&subgroupid=2&gatewayid=403&alerttypeid=2&deliquencystatus=true", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "alertV3", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "groupid", "value": "1"}, {"key": "subgroupid", "value": "2"}, {"key": "gatewayid", "value": "403"}, {"key": "alerttypeid", "value": "2"}, {"key": "deliquencystatus", "value": "true"}]}}, "response": []}, {"name": "v4.0-alertV3", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v4.0/alertV3/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v4.0", "alertV3", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "alertcfgid", "value": "", "disabled": true}, {"key": "alerttypeid", "value": "", "disabled": true}, {"key": "assetid", "value": "", "disabled": true}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "CcpController", "item": [{"name": "ccpchecklist", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/ccpchecklist/88a8a647ccc49bad69bfd68b2b56f073b2568c10?typeid=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "ccpchecklist", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "typeid", "value": "1"}]}}, "response": []}, {"name": "ccptype", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/ccptype/88a8a647ccc49bad69bfd68b2b56f073b2568c10?typeid=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "ccptype", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "typeid", "value": "1"}]}}, "response": []}, {"name": "ccp", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/ccp//88a8a647ccc49bad69bfd68b2b56f073b2568c10?userid=430", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "ccp", "", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "userid", "value": "430"}]}}, "response": []}, {"name": "saveOrUpdateccp", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": " {\r\n                \"id\": 2,\r\n                \"template_id\": 4,\r\n                \"user_id\": \"519\",\r\n                \"description\": \"Chicken1\",\r\n                \"type_id\": \"1\",\r\n                \"min\": null,\r\n                \"max\": null,\r\n                \"corrective_action\": \"Corrective actionCorrective actionCorrective actionCorrective action\",\r\n                \"category\": null,\r\n                \"comments\": null\r\n \t\r\n }      ", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/saveOrUpdateccp/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "saveOrUpdateccp", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "saveorupdateccplist", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "[\r\n\t{\r\n                \"id\": 1,\r\n                \"template_id\": 4,\r\n                \"user_id\": \"519\",\r\n                \"description\": \"Mutton1\",\r\n                \"type_id\": \"1\",\r\n                \"min\": null,\r\n                \"max\": null,\r\n                \"corrective_action\": \"Corrective action Corrective action Corrective action Corrective action \",\r\n                \"category\": null,\r\n                \"comments\": null\r\n            },\r\n            {\r\n                \"id\": 2,\r\n                \"template_id\": 4,\r\n                \"user_id\": \"519\",\r\n                \"description\": \"Chicken1\",\r\n                \"type_id\": \"1\",\r\n                \"min\": null,\r\n                \"max\": null,\r\n                \"corrective_action\": \"Corrective actionCorrective actionCorrective actionCorrective action\",\r\n                \"category\": null,\r\n                \"comments\": null\r\n            }      \r\n\t]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/saveOrUpdateccpList/88a8a647ccc49bad69bfd68b2b56f073b2568c10?userid=519", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "saveOrUpdateccpList", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "userid", "value": "519"}]}}, "response": []}, {"name": "Save or update CCP summary", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/saveorupdateccpsummary/88a8a647ccc49bad69bfd68b2b56f073b2568c10?template_id=6&start_time=10:33:00&status=2&actual_start_datetime=2018-01-05 10:33:00&freqenable=false ", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "saveorupdateccpsummary", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "template_id", "value": "6"}, {"key": "start_time", "value": "10:33:00"}, {"key": "status", "value": "2"}, {"key": "actual_start_datetime", "value": "2018-01-05 10:33:00"}, {"key": "freqenable", "value": "false "}]}}, "response": []}, {"name": "deleteccp", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/deleteccp/141/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deleteccp", "141", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "ccpreport", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/ccpreport/88a8a647ccc49bad69bfd68b2b56f073b2568c10?fromtime=12 02 2015&offset=23&limit=2&totime=12 02 2016", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "ccpreport", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "fromtime", "value": "12 02 2015"}, {"key": "offset", "value": "23"}, {"key": "limit", "value": "2"}, {"key": "totime", "value": "12 02 2016"}]}}, "response": []}, {"name": "ccpreport -Post", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "[\n\t{\n\t\"template_id\":123,\n\t\"description\":\"afdzf\",\n\t\"type_id\":234,\n\t\"min\":124,\n\t\"max\":126,\n\t\"corrective_action\":\"zsfz\",\n\t\"datetime\":\"12:02:20\",\n\t\"value\":124,\n\t\"temp_value\":124,\n\t\"ccp_id\":124,\n\t\"monitor_id\":124,\n\t\"monitor\":\"zxgfzg\",\n\t\"category\":\"zdgsdg\",\n\t\"comments\":\"asfaf\",\n\t\"image_url\":\"asfasfasf\"\n}\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/ccpreport/88a8a647ccc49bad69bfd68b2b56f073b2568c10/12:02:20/12:02:20/12 03 16/12 03 16 12:02:20/true", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "ccpreport", "88a8a647ccc49bad69bfd68b2b56f073b2568c10", "12:02:20", "12:02:20", "12 03 16", "12 03 16 12:02:20", "true"]}}, "response": []}, {"name": "ccplastreport", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/ccplastreport/88a8a647ccc49bad69bfd68b2b56f073b2568c10?userid=23423", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "ccplastreport", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "userid", "value": "23423"}]}}, "response": []}, {"name": "f5monitor", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"id\":0,\n\t\"user_id\":2343,\n\t\"meid\":\"235252525\",\n\t\"name\":\"234234\"\t\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/f5monitor/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "f5monitor", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "f5monitor - get", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/f5monitorByName/88a8a647ccc49bad69bfd68b2b56f073b2568c10?name=akshasf", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "f5monitorByName", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "name", "value": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "response": []}, {"name": "f5monitorByName", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/f5monitorByName/88a8a647ccc49bad69bfd68b2b56f073b2568c10?name=akshasf", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "f5monitorByName", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "name", "value": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "response": []}, {"name": "nimbleemailCCPreport", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/nimbleemailCCPreport/88a8a647ccc49bad69bfd68b2b56f073b2568c10?emailids=<EMAIL>&subject=KIUShdsf&content=sdihdsfih&month=april&year=2019", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "nimbleemailCCPreport", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "emailids", "value": "<EMAIL>"}, {"key": "subject", "value": "KIUShdsf"}, {"key": "content", "value": "sdihdsfih"}, {"key": "month", "value": "april"}, {"key": "year", "value": "2019"}]}}, "response": []}, {"name": "editCCPTemplate", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"userId\":1243,\n\t\"templateId\":23,\n\t\"name\":\"adfdaf\",\n\t\"templateEnable\":\"true\",\n\t\"freqEnable\":\"true\",\n\t\"startTime1\":\"12:02:12\",\n\t\"endTime1\":\"12:05:18\",\n\t\"startTime2\":\"12:12:15\",\n\t\"endTime2\":\"12:12:18\",\n\t\"startTime3\":\"12:12:16\",\n\t\"endTime3\":\"12:12:18\",\n\t\"startTime4\":\"12:12:17\",\n\t\"startTime4\":\"12:12:19\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/editCCPTemplate/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "editCCPTemplate", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "ccpTemplateConfigs", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/ccpTemplateConfigs/88a8a647ccc49bad69bfd68b2b56f073b2568c10?userid=3452", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "ccpTemplateConfigs", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "userid", "value": "3452"}]}}, "response": []}, {"name": "ccpsummary", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/ccpsummary/88a8a647ccc49bad69bfd68b2b56f073b2568c10?startdate=2342234&starttime=234234&enddate=234234&endtime=234342", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "ccpsummary", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "startdate", "value": "2342234"}, {"key": "starttime", "value": "234234"}, {"key": "enddate", "value": "234234"}, {"key": "endtime", "value": "234342"}]}}, "response": []}, {"name": "ccpsummarystatus", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/ccpsummarystatus/88a8a647ccc49bad69bfd68b2b56f073b2568c10?date=2342234&template_id=234234&start_time=234234&end_time=234342", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "ccpsummarystatus", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "date", "value": "2342234"}, {"key": "template_id", "value": "234234"}, {"key": "start_time", "value": "234234"}, {"key": "end_time", "value": "234342"}]}}, "response": []}, {"name": "saveorupdatecalibration", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\t\"id\" \t\t\t: 0,\n\t\t\"user_id\"\t\t: 2345,\n\t\t\"monitor_id\" \t: 234,\n\t\t\"temperature\" : 56,\n\t\t\"date_time\" \t: 23423,\n\t\t\"status\" \t\t: \"PASS\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/saveorupdatecalibration/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "saveorupdatecalibration", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "getcalibrationdetails", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/getcalibrationdetails/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getcalibrationdetails", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "initializTemplateSlots", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/initializTemplateSlots", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "initializTemplateSlots"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "CompanyController", "item": [{"name": "company", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/company/d0fe6c74b7f2310dce5a004eaf9124d3edb6e837", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "company", "d0fe6c74b7f2310dce5a004eaf9124d3edb6e837"]}}, "response": []}, {"name": "company - Post", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n            \"id\": 1932,\r\n            \"name\": \"iris<PERSON><PERSON>\",\r\n            \"supervisor\": \"<PERSON><PERSON>\",\r\n            \"email\": \"<EMAIL>\",\r\n            \"phoneno\": \"9884324535\",\r\n            \"mobileno\": \"9884324535\",\r\n            \"address\": \"Chennai\",\r\n            \"companytype\": {\r\n                \"id\": 1,\r\n                \"name\": \"Monitoring\"\r\n            },\r\n            \"throtsettings\": {\r\n                \"id\": 1,\r\n                \"name\": \"Basic\",\r\n                \"logins\": 5,\r\n                \"rptsperday\": 100,\r\n                \"smsalerts\": 40,\r\n                \"voicealerts\": 20,\r\n                \"fencealerts_c\": 10,\r\n                \"api\": 0,\r\n                \"webapp\": 1,\r\n                \"mobileapp\": 0,\r\n                \"price\": 99,\r\n                \"credits\": 100,\r\n                \"extra_credits\": 0,\r\n                \"mobileNos\": 1,\r\n                \"emailIds\": 1,\r\n                \"isCustomized\": 0\r\n            },\r\n            \"throtsettingsid\": \"1\",\r\n            \"cmptype_id\": \"1\"\r\n        }\r\n    \r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/company/d0fe6c74b7f2310dce5a004eaf9124d3edb6e837", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "company", "d0fe6c74b7f2310dce5a004eaf9124d3edb6e837"]}}, "response": []}, {"name": "company -Delete", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/company/33/d0fe6c74b7f2310dce5a004eaf9124d3edb6e837", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "company", "33", "d0fe6c74b7f2310dce5a004eaf9124d3edb6e837"]}}, "response": []}, {"name": "companyconfig", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/companyconfig/d0fe6c74b7f2310dce5a004eaf9124d3edb6e837", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "companyconfig", "d0fe6c74b7f2310dce5a004eaf9124d3edb6e837"]}}, "response": []}, {"name": "companyconfig - post", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [], "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/companyconfig/5a44efcf5cd361e46e2fffba85fd711c538df30f?id=1300&vgroupname=Group&gatewayname=Gateway&nodename=node&notreportinginterval=60&incursionenable=1&realtimemonitor=true&mapView=true&wirelessSensorView=false&dashboardSensorView=false&appnotifyenable=true&temperatureunit=T&humidityunit=%25&pressureunit=Pa&speedunit=MPH&lightunit=lx&moistureunit=%25&memstype=motion+alert&panic_tamper=Panic&reportenable=11100000&sensorenable=01100001&alertenable=11100000001000&cellIdEnable=true&httpredirenable=false&tcpredirenable=false&tcpredirIP=**************&tcpredirPORT=9876&httpredirenable_SAND=false&httpredirURL_SAND=null&interfreqenable=false&petservice_enable=1&pethealth_enable=1&heatindex_enable=1&livetrack_enable=0&cmpid=1300", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "companyconfig", "5a44efcf5cd361e46e2fffba85fd711c538df30f"], "query": [{"key": "id", "value": "1300"}, {"key": "vgroupname", "value": "Group"}, {"key": "gatewayname", "value": "Gateway"}, {"key": "nodename", "value": "node"}, {"key": "notreportinginterval", "value": "60"}, {"key": "incursionenable", "value": "1"}, {"key": "realtimemonitor", "value": "true"}, {"key": "mapView", "value": "true"}, {"key": "wirelessSensorView", "value": "false"}, {"key": "dashboardSensorView", "value": "false"}, {"key": "appnotifyenable", "value": "true"}, {"key": "temperatureunit", "value": "T"}, {"key": "humidityunit", "value": "%25"}, {"key": "pressureunit", "value": "Pa"}, {"key": "speedunit", "value": "MPH"}, {"key": "lightunit", "value": "lx"}, {"key": "moistureunit", "value": "%25"}, {"key": "memstype", "value": "motion+alert"}, {"key": "panic_tamper", "value": "Panic"}, {"key": "reportenable", "value": "11100000"}, {"key": "sensorenable", "value": "01100001"}, {"key": "alertenable", "value": "11100000001000"}, {"key": "cellIdEnable", "value": "true"}, {"key": "httpredirenable", "value": "false"}, {"key": "tcpredirenable", "value": "false"}, {"key": "tcpredirIP", "value": "**************"}, {"key": "tcpredirPORT", "value": "9876"}, {"key": "httpredirenable_SAND", "value": "false"}, {"key": "httpredirURL_SAND", "value": "null"}, {"key": "interfreqenable", "value": "false"}, {"key": "petservice_enable", "value": "1"}, {"key": "pethealth_enable", "value": "1"}, {"key": "heatindex_enable", "value": "1"}, {"key": "livetrack_enable", "value": "0"}, {"key": "cmpid", "value": "1300"}]}}, "response": []}, {"name": "http://localhost:8080/irisservices/v3.0/companylist/d0fe6c74b7f2310dce5a004eaf9124d3edb6e837", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/companylist/d0fe6c74b7f2310dce5a004eaf9124d3edb6e837", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "companylist", "d0fe6c74b7f2310dce5a004eaf9124d3edb6e837"]}}, "response": []}, {"name": "http://localhost:8080/irisservices/v3.1/companyconfig/d0fe6c74b7f2310dce5a004eaf9124d3edb6e837?cmpid=14", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.1/companyconfig/d0fe6c74b7f2310dce5a004eaf9124d3edb6e837?cmpid=14", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.1", "companyconfig", "d0fe6c74b7f2310dce5a004eaf9124d3edb6e837"], "query": [{"key": "cmpid", "value": "14"}]}}, "response": []}, {"name": "v4-companyconfig", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v4.0/companyconfig/2c645697ee35d47f6591b349ac304cb85779d71c", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v4.0", "companyconfig", "2c645697ee35d47f6591b349ac304cb85779d71c"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "CompanyTypeController", "item": [{"name": "companytype", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/companytype/d0fe6c74b7f2310dce5a004eaf9124d3edb6e837?cmptypeid", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "companytype", "d0fe6c74b7f2310dce5a004eaf9124d3edb6e837"], "query": [{"key": "cmptypeid", "value": null}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "DeviceSubscriptionController", "item": [{"name": "create - devicesubscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "[{\n\t\"id\":0,\n\t\"niomSubId\":234,\n\t\"woocomSubId\":2345,\n\t\"orderId\":9243423,\n\t\"meid\":92384723984234,\n\t\"status\":\"active\",\n\t\"billingFirstname\":\"sivasiva\",\n\t\"billingLastname\":\"aslkjd\",\n\t\"billingEmail\":\"<EMAIL>\",\n\t\"billingPhone\":\"1-**********\",\n\t\"planName\":\"plan_name\",\n\t\"sku\":\"N5-503-H-000005\",\n\t\"quantity\":1,\n\t\"price\":75,\n\t\"billingPeriod\":\"Half-Yearly\",\n\t\"billingStartDate\":\"2019-03-23 00:00:00\",\n\t\"trialEndDate\":\"1753-01-01 00:00:00\",\n\t\"nextPaymentDate\":\"1753-01-01 00:00:00\",\n\t\"lastPaymentDate\":\"2019-04-13 14:51:23\",\n\t\"subscriptionCreated\":\"2019-04-13 15:35:53\",\n\t\"endDate\":\"2019-09-26 00:00:00\",\n\t\"accountType\":\"rv\",\n\t\"externalOrderId\":\"111-19-111\",\n\t\"hassleEndDate\":\"2019-09-26 00:00:00\",\n\t\"orderChannel\":\"Amazon\"\n}\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/devicesubscription/d0fe6c74b7f2310dce5a004eaf9124d3edb6e837", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "devicesubscription", "d0fe6c74b7f2310dce5a004eaf9124d3edb6e837"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "DropdownController", "item": [{"name": "assetmodel", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/irisservices/v3.0/assetmodel/8977b8da604d9779d51baf4aacabef5af2771bf7", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "assetmodel", "8977b8da604d9779d51baf4aacabef5af2771bf7"]}}, "response": []}, {"name": "alerttype", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/irisservices/v3.0/alerttype/8977b8da604d9779d51baf4aacabef5af2771bf7", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "alerttype", "8977b8da604d9779d51baf4aacabef5af2771bf7"]}}, "response": []}, {"name": "reporttype", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/irisservices/v3.0/reporttype/8977b8da604d9779d51baf4aacabef5af2771bf7", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "reporttype", "8977b8da604d9779d51baf4aacabef5af2771bf7"]}}, "response": []}, {"name": "messagetype", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/irisservices/v3.0/messagetype/8977b8da604d9779d51baf4aacabef5af2771bf7", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "messagetype", "8977b8da604d9779d51baf4aacabef5af2771bf7"]}}, "response": []}, {"name": "cmpalerttype", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/irisservices/v3.0/cmpalerttype/8977b8da604d9779d51baf4aacabef5af2771bf7", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "cmpalerttype", "8977b8da604d9779d51baf4aacabef5af2771bf7"]}}, "response": []}, {"name": "cmpreporttype", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/irisservices/v3.0/cmpreporttype/8977b8da604d9779d51baf4aacabef5af2771bf7", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "cmpreporttype", "8977b8da604d9779d51baf4aacabef5af2771bf7"]}}, "response": []}, {"name": "orderchannel", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/irisservices/v3.0/orderchannel", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "orderchannel"]}}, "response": []}, {"name": "notificationtype", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/irisservices/v3.0/notificationtype", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "notificationtype"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "ExternalConfigController", "item": [{"name": "externalConfig", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"parametername\":\"testetsetxstetstets\",\n\t\"value\":\"ksajfhaskufh\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8080/irisservices/v3.0/externalConfig/d0fe6c74b7f2310dce5a004eaf9124d3edb6e837", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "externalConfig", "d0fe6c74b7f2310dce5a004eaf9124d3edb6e837"]}}, "response": []}, {"name": "getallexternalconfig", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"parametername\":\"testetsetxstetstets\",\n\t\"value\":\"ksajfhaskufh\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8080/irisservices/v3.0/getallexternalconfig", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getallexternalconfig"]}}, "response": []}, {"name": "getexternalconfig", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"parametername\":\"testetsetxstetstets\",\n\t\"value\":\"ksajfhaskufh\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8080/irisservices/v3.0/getexternalconfig/niomip", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getexternalconfig", "niomip"]}}, "response": []}, {"name": "deleteexternalconfig", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"parametername\":\"testetsetxstetstets\",\n\t\"value\":\"ksajfhaskufh\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8080/irisservices/v3.0/deleteexternalconfig/8977b8da604d9779d51baf4aacabef5af2771bf7?name=testetsetxstetstets", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deleteexternalconfig", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "name", "value": "testetsetxstetstets"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "GatewayController", "item": [{"name": "Get Gateway", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/3.0/gateway/88a8a647ccc49bad69bfd68b2b56f073b2568c10?assetgroupid=&groupid=&subgroupid=&gatewayid=&userid=&meid=", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "3.0", "gateway", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "assetgroupid", "value": ""}, {"key": "groupid", "value": ""}, {"key": "subgroupid", "value": ""}, {"key": "gatewayid", "value": ""}, {"key": "userid", "value": ""}, {"key": "meid", "value": ""}]}}, "response": []}, {"name": "Save/Update Gateway", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n        \"id\": 1, \r\n        \"name\": \"gateway-1\",\r\n        \"meid\": \"35540000000001\",\r\n        \"mdn\": \"8056157557\",\r\n        \"carrier\": \"INDIA\",\r\n        \"enable\": true,\r\n        \"alive\": true,\r\n        \"location\": \"valasai\",\r\n        \"description\": \"NA\",\r\n        \"owner\": null,\r\n        \"sensorEnable\": null,\r\n        \"modelid\": 1,\r\n        \"model\": \"N5-503-C-000002\",\r\n        \"assetgroupid\": 1,\r\n        \"groupid\": 1,\r\n        \"stopreport\": false,\r\n        \"starttime\": \"0\",\r\n        \"stoptime\": \"0\",\r\n        \"webapp\": true  \r\n      }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/3.0/gateway/88a8a647ccc49bad69bfd68b2b56f073b2568c10?id=1&name=gateway-1&meid=35540000000001&mdn=8056157557&carrier=INDIA&enable =true&alive=true&location=valasai&description=NA&owner=null&sensorEnable=null&modelid=1&model=N5-503-C-000002&assetgroupid=1&groupid=1&stopreport=false&starttime=0&stoptime=0&webapp=true&subgroupid=2&gatewayid=113&userid=3452", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "3.0", "gateway", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "id", "value": "1"}, {"key": "name", "value": "gateway-1"}, {"key": "meid", "value": "35540000000001"}, {"key": "mdn", "value": "8056157557"}, {"key": "carrier", "value": "INDIA"}, {"key": "enable ", "value": "true"}, {"key": "alive", "value": "true"}, {"key": "location", "value": "vala<PERSON>"}, {"key": "description", "value": "NA"}, {"key": "owner", "value": "null"}, {"key": "sensorEnable", "value": "null"}, {"key": "modelid", "value": "1"}, {"key": "model", "value": "N5-503-C-000002"}, {"key": "assetgroupid", "value": "1"}, {"key": "groupid", "value": "1"}, {"key": "stopreport", "value": "false"}, {"key": "starttime", "value": "0"}, {"key": "stoptime", "value": "0"}, {"key": "webapp", "value": "true"}, {"key": "subgroupid", "value": "2"}, {"key": "gatewayid", "value": "113"}, {"key": "userid", "value": "3452"}]}}, "response": []}, {"name": "http://localhost:8080/irisservices/v3.0/usergateway/88a8a647ccc49bad69bfd68b2b56f073b2568c10?userid=14&gatewayids=13,14,15", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/usergateway/88a8a647ccc49bad69bfd68b2b56f073b2568c10?userid=14&gatewayids=13,14,15", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "usergateway", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "userid", "value": "14"}, {"key": "gatewayids", "value": "13,14,15"}]}}, "response": []}, {"name": "Delete - gateway", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/gateway/10/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "gateway", "10", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "Delete - gateway", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/allgateways/88a8a647ccc49bad69bfd68b2b56f073b2568c10?cmptype_id=3", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "allgateways", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "cmptype_id", "value": "3"}]}}, "response": []}, {"name": "saveorupdatePetProfile", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "[\n\t{\n\t\t\"id\" : 0,\n\t\t\"gateway_id\" :234,\n\t\t\"name\" :\"sdfsdf\",\n\t\t\"age\":\"23\",\n\t\t\"sex\":\"Male\",\n\t\t\"breed\":\"Hiuman\",\n\t\t\"height\":\"234\",\n\t\t\"weight\":\"69\",\n\t\t\"remarks\":\"no\",\n\t\t\"imageurl\":\"askfchjsafckusafc\",\n\t\t\"specieName\":\"Heiggg\"\n\t}]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/saveorupdatePetProfile/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "saveorupdatePetProfile", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "getPetProfiles", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getPetProfiles/88a8a647ccc49bad69bfd68b2b56f073b2568c10?userid=345&gatewayid=3452&id=58&name=sdfsdaf&age=22&sex=male&breed=dog", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getPetProfiles", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "userid", "value": "345"}, {"key": "gatewayid", "value": "3452"}, {"key": "id", "value": "58"}, {"key": "name", "value": "sdfsdaf"}, {"key": "age", "value": "22"}, {"key": "sex", "value": "male"}, {"key": "breed", "value": "dog"}]}}, "response": []}, {"name": "getPetProfile", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getPetProfile/88a8a647ccc49bad69bfd68b2b56f073b2568c10?userid=1207&gatewayid=&monitortype=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getPetProfile", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "userid", "value": "1207"}, {"key": "gatewayid", "value": ""}, {"key": "monitortype", "value": "1"}, {"key": "name", "value": "sdfsdaf", "disabled": true}, {"key": "age", "value": "22", "disabled": true}, {"key": "sex", "value": "male", "disabled": true}, {"key": "breed", "value": "dog", "disabled": true}]}}, "response": []}, {"name": "getgateway", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getgateway?meid=359486065751024", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getgateway"], "query": [{"key": "meid", "value": "359486065751024"}]}}, "response": []}, {"name": "getdevicesummary", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getdevicesummary/Default/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getdevicesummary", "<PERSON><PERSON><PERSON>", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "Gateway - On/Off", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/onoff/88a8a647ccc49bad69bfd68b2b56f073b2568c10?gatewayid=46&enable=false", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "onoff", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "gatewayid", "value": "46"}, {"key": "enable", "value": "false"}]}}, "response": []}, {"name": "enableordisableGateway", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/enableordisableGateway/88a8a647ccc49bad69bfd68b2b56f073b2568c10?gatewayid=24&userid=12&isenable=true", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "enableordisableGateway", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "gatewayid", "value": "24"}, {"key": "userid", "value": "12"}, {"key": "isenable", "value": "true"}]}}, "response": []}, {"name": "updategoalsettings", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/updategoalsettings/88a8a647ccc49bad69bfd68b2b56f073b2568c10?gatewayid=404&goalSetting=10000", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "updategoalsettings", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "gatewayid", "value": "404"}, {"key": "goalSetting", "value": "10000"}]}}, "response": []}, {"name": "checkqrcexist", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/checkqrcexist/88a8a647ccc49bad69bfd68b2b56f073b2568c10?qrcode=198713&monitortypeid=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "checkqrcexist", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "qrcode", "value": "198713"}, {"key": "monitortypeid", "value": "1"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "GeneralController", "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"offerOne\": \"sdf\",\n\t\"messageContent\":\"sdvfsdfsdf\",\n\t\"referalLink\":\"zxzvzxvzxv\",\n\t\"offerTwo\":\"sdfdzv\",\n\t\"createdOn\":\"34 23 23 \"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/referearn//88a8a647ccc49bad69bfd68b2b56f073b2568c10?gatewayid=24&userid=12&isenable=true", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "<PERSON><PERSON><PERSON>", "", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "gatewayid", "value": "24"}, {"key": "userid", "value": "12"}, {"key": "isenable", "value": "true"}]}}, "response": []}, {"name": "getAllReferEarn", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"offerOne\": \"sdf\",\n\t\"messageContent\":\"sdvfsdfsdf\",\n\t\"referalLink\":\"zxzvzxvzxv\",\n\t\"offerTwo\":\"sdfdzv\",\n\t\"createdOn\":\"34 23 23 \"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/getAllReferEarn", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getAllReferEarn"]}}, "response": []}, {"name": "getgeneraldata", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"offerOne\": \"sdf\",\n\t\"messageContent\":\"sdvfsdfsdf\",\n\t\"referalLink\":\"zxzvzxvzxv\",\n\t\"offerTwo\":\"sdfdzv\",\n\t\"createdOn\":\"34 23 23 \"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/getgeneraldata?userid=2000&os=Android", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getgeneraldata"], "query": [{"key": "userid", "value": "2000"}, {"key": "os", "value": "Android"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "GooglePetServiceController", "item": [{"name": "getnearbyservices", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/nodeoverview/8977b8da604d9779d51baf4aacabef5af2771bf7?groupid=3&subgroupid=32", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "nodeoverview", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "groupid", "value": "3"}, {"key": "subgroupid", "value": "32"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "GroupController", "item": [{"name": "Get assetgroup", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/assetgroup/8977b8da604d9779d51baf4aacabef5af2771bf7?assetgroupid=", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "assetgroup", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "assetgroupid", "value": ""}]}}, "response": []}, {"name": "group", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/group/8977b8da604d9779d51baf4aacabef5af2771bf7?groupid=13", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "group", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "groupid", "value": "13"}]}}, "response": []}, {"name": "subgroup", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/subgroup/8977b8da604d9779d51baf4aacabef5af2771bf7?groupid=13&subgroupid=234", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "subgroup", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "groupid", "value": "13"}, {"key": "subgroupid", "value": "234"}]}}, "response": []}, {"name": "Save/ update assetgroup", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/assetgroup/8977b8da604d9779d51baf4aacabef5af2771bf7?id=0&name=ggggamil", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "assetgroup", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "id", "value": "0"}, {"key": "name", "value": "ggggamil"}]}}, "response": []}, {"name": "saveorUpdateGroupOld", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/groupold/8977b8da604d9779d51baf4aacabef5af2771bf7?name=afdsdfsdf&company=34", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "groupold", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "name", "value": "afdsdfsdf"}, {"key": "company", "value": "34"}]}}, "response": []}, {"name": "subgroup", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/subgroup/8977b8da604d9779d51baf4aacabef5af2771bf7?group=23&name=sfsdfsdf&company=11", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "subgroup", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "group", "value": "23"}, {"key": "name", "value": "sfsdfsdf"}, {"key": "company", "value": "11"}]}}, "response": []}, {"name": "usergroup", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/usergroup/8977b8da604d9779d51baf4aacabef5af2771bf7/3/?groupid=23&topgroupid=", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "usergroup", "8977b8da604d9779d51baf4aacabef5af2771bf7", "3", ""], "query": [{"key": "groupid", "value": "23"}, {"key": "topgroupid", "value": ""}]}}, "response": []}, {"name": "group", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/group/8977b8da604d9779d51baf4aacabef5af2771bf7/3/?groupid=23&topgroupid", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "group", "8977b8da604d9779d51baf4aacabef5af2771bf7", "3", ""], "query": [{"key": "groupid", "value": "23"}, {"key": "topgroupid", "value": null}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "LiveTrackController", "item": [{"name": "enablelivetrack", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/enablelivetrack/8977b8da604d9779d51baf4aacabef5af2771bf7/3/?gatewayid=23&minutes=4", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "enablelivetrack", "8977b8da604d9779d51baf4aacabef5af2771bf7", "3", ""], "query": [{"key": "gatewayid", "value": "23"}, {"key": "minutes", "value": "4"}]}}, "response": []}, {"name": "disablelivet<PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/disablelivetrack/8977b8da604d9779d51baf4aacabef5af2771bf7/3/?gatewayid=23", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "disablelivet<PERSON>", "8977b8da604d9779d51baf4aacabef5af2771bf7", "3", ""], "query": [{"key": "gatewayid", "value": "23"}]}}, "response": []}, {"name": "disablelivetrackv2", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/disablelivetrackv2/8977b8da604d9779d51baf4aacabef5af2771bf7/3/?gatewayid=23", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "disablelivetrackv2", "8977b8da604d9779d51baf4aacabef5af2771bf7", "3", ""], "query": [{"key": "gatewayid", "value": "23"}]}}, "response": []}, {"name": "livetracksummary", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/livetracksummary/8977b8da604d9779d51baf4aacabef5af2771bf7/3/?groupid=23&subgroupid=2424&assetgroupid=2&gatewayid=243&offset=23423&limit=333", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "livetracksummary", "8977b8da604d9779d51baf4aacabef5af2771bf7", "3", ""], "query": [{"key": "groupid", "value": "23"}, {"key": "subgroupid", "value": "2424"}, {"key": "assetgroupid", "value": "2"}, {"key": "gatewayid", "value": "243"}, {"key": "offset", "value": "23423"}, {"key": "limit", "value": "333"}]}}, "response": []}, {"name": "livetrackgatewayreport", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/livetrackgatewayreport/8977b8da604d9779d51baf4aacabef5af2771bf7/3/?fromtime=3243245&timezone=12431324&gatewayid=3w4535", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "livetrackgatewayreport", "8977b8da604d9779d51baf4aacabef5af2771bf7", "3", ""], "query": [{"key": "fromtime", "value": "3243245"}, {"key": "timezone", "value": "12431324"}, {"key": "gatewayid", "value": "3w4535"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "MessagingController", "item": [{"name": "get message", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/message/8977b8da604d9779d51baf4aacabef5af2771bf7?gatewayid&transporttype&message&subgroupid&groupid", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "message", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "gatewayid", "value": null}, {"key": "transporttype", "value": null}, {"key": "message", "value": null}, {"key": "subgroupid", "value": null}, {"key": "groupid", "value": null}]}}, "response": []}, {"name": "Post - message", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/message/8977b8da604d9779d51baf4aacabef5af2771bf7?gatewayid=345&transporttype=3245&message=sdgfsdgsgd", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "message", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "gatewayid", "value": "345"}, {"key": "transporttype", "value": "3245"}, {"key": "message", "value": "sdgfsdgsgd"}]}}, "response": []}, {"name": "nimbleemail", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/nimbleemail/8977b8da604d9779d51baf4aacabef5af2771bf7?emailids=<EMAIL>&subject=Hey its Subject&content=sadkjfcdsaf&isRvEmailId=true", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "nimbleemail", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "emailids", "value": "<EMAIL>"}, {"key": "subject", "value": "Hey its Subject"}, {"key": "content", "value": "sadkjfcdsaf"}, {"key": "isRvEmailId", "value": "true"}]}}, "response": []}, {"name": "nimblesms", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/nimblesms?phoneno=2398742394&msg=szkjdvckdsaj&cmpid=111&cmpname=sdfsadf&appname=assaasfd&type=good type", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "nimblesms"], "query": [{"key": "phoneno", "value": "2398742394"}, {"key": "msg", "value": "szkjdvckdsaj"}, {"key": "cmpid", "value": "111"}, {"key": "cmpname", "value": "sdfsadf"}, {"key": "appname", "value": "assaasfd"}, {"key": "type", "value": "good type"}]}}, "response": []}, {"name": "texttoSpeech", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/texttoSpeech?msg=&phoneno=&cmpid=&cmpname=&appname=&type=", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "texttoSpeech"], "query": [{"key": "msg", "value": ""}, {"key": "phoneno", "value": ""}, {"key": "cmpid", "value": ""}, {"key": "cmpname", "value": ""}, {"key": "appname", "value": ""}, {"key": "type", "value": ""}]}}, "response": []}, {"name": "twilio_texttoSpeech", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/twilio_texttoSpeech?msg=Hello", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "twilio_texttoSpeech"], "query": [{"key": "msg", "value": "Hello"}]}}, "response": []}, {"name": "messageV2", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/messageV2?gatewayid=64802&transporttype=1&message=wccombo=w&seqno=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "messageV2"], "query": [{"key": "gatewayid", "value": "64802"}, {"key": "transporttype", "value": "1"}, {"key": "message", "value": "wccombo=w"}, {"key": "seqno", "value": "1"}]}}, "response": []}, {"name": "nimblevoice", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/nimblevoice?msg=szdvzdv&phoneno=2342342342&cmpid=234324&cmpname=dfdfgdsg&appname=swfsgsdsg&type=agesag&ip=************", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "nimblevoice"], "query": [{"key": "msg", "value": "szdvzdv"}, {"key": "phoneno", "value": "2342342342"}, {"key": "cmpid", "value": "234324"}, {"key": "cmpname", "value": "dfdfgdsg"}, {"key": "appname", "value": "swfsgsdsg"}, {"key": "type", "value": "agesag"}, {"key": "ip", "value": "************"}]}}, "response": []}, {"name": "http://localhost:8080/irisservices/v3.0/verizonservice?activityType=suspend&meid=356726101287670&iccid=89148000004702128896", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/verizonservice?activityType=suspend&meid=356726101287670&iccid=89148000004702128896", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "verizonservice"], "query": [{"key": "activityType", "value": "suspend"}, {"key": "meid", "value": "356726101287670"}, {"key": "iccid", "value": "89148000004702128896"}, {"key": "cmpname", "value": "sdfsadf", "disabled": true}, {"key": "appname", "value": "assaasfd", "disabled": true}, {"key": "type", "value": "sms", "disabled": true}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "NodeController", "item": [{"name": "node", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/node/?assetgroupid=2&groupid=3&subgroupid=2&gatewayid=342&nodeid=3", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "node", ""], "query": [{"key": "assetgroupid", "value": "2"}, {"key": "groupid", "value": "3"}, {"key": "subgroupid", "value": "2"}, {"key": "gatewayid", "value": "342"}, {"key": "nodeid", "value": "3"}]}}, "response": []}, {"name": "post - node", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\":\"<PERSON><PERSON><PERSON>\",\n\t\"macaddr\":\"asf\",\n\t\"enable\":\"true\",\n\t\"alive\":\"false\",\n\t\"location\":\"good location\",\n\t\"description\":\"Nthiungalkdja\",\n\t\"sensorEnable\":\"false\",\n\t\"modelid\":23,\n\t\"model\":\"N1\",\n\t\"gatewayid\":234,\n\t\"gatewayname\":\"GHGH\",\n\t\"assetgroupid\":345,\n\t\"assetgroupname\":\"sadfsaf\",\n\t\"groupid\":234,\n\t\"groupname\":\"asfnjf\",\n\t\"mdn\":\"2134324324\",\n\t\"lastrptdatetime\":2234234234\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/node/8977b8da604d9779d51baf4aacabef5af2771bf7", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "node", "8977b8da604d9779d51baf4aacabef5af2771bf7"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "OverviewController", "item": [{"name": "gatewayoverview", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/gatewayoverview/8977b8da604d9779d51baf4aacabef5af2771bf7?groupid=3&subgroupid=23&levelid=23", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "gatewayoverview", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "groupid", "value": "3"}, {"key": "subgroupid", "value": "23"}, {"key": "levelid", "value": "23"}]}}, "response": []}, {"name": "nodeoverview", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/nodeoverview/8977b8da604d9779d51baf4aacabef5af2771bf7?groupid=3&subgroupid=32", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "nodeoverview", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "groupid", "value": "3"}, {"key": "subgroupid", "value": "32"}]}}, "response": []}, {"name": "alertoverview", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/alertoverview/8977b8da604d9779d51baf4aacabef5af2771bf7?groupid=3&subgroupid=32", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "alertoverview", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "groupid", "value": "3"}, {"key": "subgroupid", "value": "32"}]}}, "response": []}, {"name": "sreigatewayoverview", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/sreigatewayoverview/8977b8da604d9779d51baf4aacabef5af2771bf7?groupid=3&subgroupid=32&levelid=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "sreigatewayoverview", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "groupid", "value": "3"}, {"key": "subgroupid", "value": "32"}, {"key": "levelid", "value": "1"}]}}, "response": []}, {"name": "alertoverviewV2", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/alertoverviewV2/8977b8da604d9779d51baf4aacabef5af2771bf7?groupid=3&subgroupid=32", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "alertoverviewV2", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "groupid", "value": "3"}, {"key": "subgroupid", "value": "32"}]}}, "response": []}, {"name": "assetalertrange", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/assetalertrange/8977b8da604d9779d51baf4aacabef5af2771bf7?assetid=2", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "assetalertrange", "8977b8da604d9779d51baf4aacabef5af2771bf7"], "query": [{"key": "assetid", "value": "2"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "PetSpeciesController", "item": [{"name": "petspecies", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"id\":0,\n\t\"speciesname\":\"asd\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/petspecies/8977b8da604d9779d51baf4aacabef5af2771bf7", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "petspecies", "8977b8da604d9779d51baf4aacabef5af2771bf7"]}}, "response": []}, {"name": "petbreeds", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"id\":0,\n\t\"petSpecies\":\"asd\",\n\t\"breedName\":\"sadfa\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/petbreeds/8977b8da604d9779d51baf4aacabef5af2771bf7", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "petbreeds", "8977b8da604d9779d51baf4aacabef5af2771bf7"]}}, "response": []}, {"name": "getspecies", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"id\":0,\n\t\"petSpecies\":\"asd\",\n\t\"breedName\":\"sadfa\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/getspecies", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getspecies"]}}, "response": []}, {"name": "getbreeds", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"id\":0,\n\t\"petSpecies\":\"asd\",\n\t\"breedName\":\"sadfa\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/getbreeds", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getbreeds"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "PushNotificatonController", "item": [{"name": "CreateNotification", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n\"notificationTypeId\":2,\r\n\"title\" : \"Get offer for you pet!!\",\r\n\"shortDescription\" : \"You can get offer for your subscription.\",\r\n\"message\" : \"You can get offer for your subscription. Offer valid till next month. Note: currentUser might also be null because the auth object has not finished initializing. If you use an observer to keep track of the user's sign-in status, you don't need to handle this case.\",\r\n\"imageUrl\":\"NA\",\r\n\"bannerImageUrl\":\"https://s3.us-west-2.amazonaws.com/pushnotification.nimblewireless.com/1557990731.png\",\r\n\"expiryOn\":\"2019-08-08 00:00:000\",\r\n\"hyperLink\":\"NA\"\r\n}"}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/createnotification/8977b8da604d9779d51baf4aacabef5af2771bf7", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "createnotification", "8977b8da604d9779d51baf4aacabef5af2771bf7"]}}, "response": []}, {"name": "UpdateNotifications", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\n                \"id\": 16,\n                \"shortDescription\": \"Test Save your lover with our Pet Monitor\",\n                \"title\": \"Test9\",\n                \"message\": \"NA\",\n                \"bannerImageUrl\": \"https://rvpet.com\",\n                \"imageUrl\": \"NA\",\n                \"createdOn\": \"2019-04-30 09:56:03\",\n                \"expiryOn\": \"2020-05-05 00:00:00\",\n                \"hyperLink\": \"https://rvpetsafety.com\",\n                \"notificationTypeID\":2\n\n}"}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/updatenotification/8977b8da604d9779d51baf4aacabef5af2771bf7", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "updatenotification", "8977b8da604d9779d51baf4aacabef5af2771bf7"]}}, "response": []}, {"name": "DeleteUserNotifications - Post", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/deleteusernotification/ade4e02baaa219dcaa5ba7682e4e407bf94f8749/3", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deleteusernotification", "ade4e02baaa219dcaa5ba7682e4e407bf94f8749", "3"]}}, "response": []}, {"name": "deletenotification", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/deletenotification/ade4e02baaa219dcaa5ba7682e4e407bf94f8749/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deletenotification", "ade4e02baaa219dcaa5ba7682e4e407bf94f8749", "1"]}}, "response": []}, {"name": "getnotifications", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getnotifications/ade4e02baaa219dcaa5ba7682e4e407bf94f8749?id=2", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getnotifications", "ade4e02baaa219dcaa5ba7682e4e407bf94f8749"], "query": [{"key": "id", "value": "2"}]}}, "response": []}, {"name": "usernotifications", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/usernotifications/ade4e02baaa219dcaa5ba7682e4e407bf94f8749?userid=342&status=true", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "usernotifications", "ade4e02baaa219dcaa5ba7682e4e407bf94f8749"], "query": [{"key": "userid", "value": "342"}, {"key": "status", "value": "true"}]}}, "response": []}, {"name": "listnotifications", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/listnotifications/ade4e02baaa219dcaa5ba7682e4e407bf94f8749?status=active", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "listnotifications", "ade4e02baaa219dcaa5ba7682e4e407bf94f8749"], "query": [{"key": "status", "value": "active"}]}}, "response": []}, {"name": "sendnotifications", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"pushNotificationId\":22,\n\t\"userID\" :[2807]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/sendnotifications/5b2e8dedfcd99ae8153d89c4f3470e7e6f6dfaad?userid=2807&status=active", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "sendnotifications", "5b2e8dedfcd99ae8153d89c4f3470e7e6f6dfaad"], "query": [{"key": "userid", "value": "2807"}, {"key": "status", "value": "active"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "ReportController", "item": [{"name": "gatewaysummary", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/gatewaysummary/6a6dd5969a0a72b1f39abd2809d4d00998742d60?groupid=&subgroupid&assetgroupid=&gatewayid=1&offset=&limit=&zip=", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "gatewaysummary", "6a6dd5969a0a72b1f39abd2809d4d00998742d60"], "query": [{"key": "groupid", "value": ""}, {"key": "subgroupid", "value": null}, {"key": "assetgroupid", "value": ""}, {"key": "gatewayid", "value": "1"}, {"key": "offset", "value": ""}, {"key": "limit", "value": ""}, {"key": "zip", "value": ""}]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/nodesummary/6a6dd5969a0a72b1f39abd2809d4d00998742d60?groupid=&subgroupid&assetgroupid=&gatewayid=1&nodeid&offset=&limit=&zip=", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "<PERSON><PERSON><PERSON><PERSON>", "6a6dd5969a0a72b1f39abd2809d4d00998742d60"], "query": [{"key": "groupid", "value": ""}, {"key": "subgroupid", "value": null}, {"key": "assetgroupid", "value": ""}, {"key": "gatewayid", "value": "1"}, {"key": "nodeid", "value": null}, {"key": "offset", "value": ""}, {"key": "limit", "value": ""}, {"key": "zip", "value": ""}]}}, "response": []}, {"name": "gatewayreport", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/gatewayreport/6a6dd5969a0a72b1f39abd2809d4d00998742d60?fromtime=2015-12-31  11:00:00&totime=2015-12-3112:40:00&assetgroupid=1&gatewayid=1&offset=1&limit=3&zip=", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "gatewayreport", "6a6dd5969a0a72b1f39abd2809d4d00998742d60"], "query": [{"key": "fromtime", "value": "2015-12-31  11:00:00"}, {"key": "totime", "value": "2015-12-3112:40:00"}, {"key": "assetgroupid", "value": "1"}, {"key": "gatewayid", "value": "1"}, {"key": "offset", "value": "1"}, {"key": "limit", "value": "3"}, {"key": "zip", "value": ""}]}}, "response": []}, {"name": "sreigatewaysummary", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/73436297b4b30e7b106f?groupid=63&subgroupid&assetgroupid=&gatewayid=&\ndeliquencystatus=&levelid=3&offset=&limit=&zip=", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["73436297b4b30e7b106f"], "query": [{"key": "groupid", "value": "63"}, {"key": "subgroupid", "value": null}, {"key": "assetgroupid", "value": ""}, {"key": "gatewayid", "value": ""}, {"key": "\ndeliquencystatus", "value": ""}, {"key": "levelid", "value": "3"}, {"key": "offset", "value": ""}, {"key": "limit", "value": ""}, {"key": "zip", "value": ""}]}}, "response": []}, {"name": "cmpsreigatewaysummary", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/73436297b4b30e7b106f?groupid=63&subgroupid&assetgroupid=&gatewayid=&deliquencystatus=&levelid=3", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["73436297b4b30e7b106f"], "query": [{"key": "groupid", "value": "63"}, {"key": "subgroupid", "value": null}, {"key": "assetgroupid", "value": ""}, {"key": "gatewayid", "value": ""}, {"key": "deliquencystatus", "value": ""}, {"key": "levelid", "value": "3"}]}}, "response": []}, {"name": "cmpsreigatewaysummary", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/73436297b4b30e7b106f?groupid=63&subgroupid&assetgroupid=&gatewayid=&deliquencystatus&levelid=3", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["73436297b4b30e7b106f"], "query": [{"key": "groupid", "value": "63"}, {"key": "subgroupid", "value": null}, {"key": "assetgroupid", "value": ""}, {"key": "gatewayid", "value": ""}, {"key": "deliquencystatus", "value": null}, {"key": "levelid", "value": "3"}]}}, "response": []}, {"name": "gatewaysummaryTest", "request": {"method": "GET", "header": [], "url": {"raw": "http://stage-furbitser.nimblewireless.com/irisservices/v3.0/gatewaysummaryTest/982401de7e7bda7ecf92f403117e71c8f3c6477f?assetgroupid=&groupid=&subgroupid=&gatewayid=&offset=&limit=&deliquencystatus=", "protocol": "http", "host": ["stage-furbitser", "nimblewireless", "com"], "path": ["irisservices", "v3.0", "gatewaysummaryTest", "982401de7e7bda7ecf92f403117e71c8f3c6477f"], "query": [{"key": "assetgroupid", "value": ""}, {"key": "groupid", "value": ""}, {"key": "subgroupid", "value": ""}, {"key": "gatewayid", "value": ""}, {"key": "offset", "value": ""}, {"key": "limit", "value": ""}, {"key": "deliquencystatus", "value": ""}]}}, "response": []}, {"name": "gatewayreportTest", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/73436297b4b30e7b106f?fromtime=213213&totime=123213&assetgroupid=213&gatewayid=231&offset=dffdsg&limit=12", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["73436297b4b30e7b106f"], "query": [{"key": "fromtime", "value": "213213"}, {"key": "totime", "value": "123213"}, {"key": "assetgroupid", "value": "213"}, {"key": "gatewayid", "value": "231"}, {"key": "offset", "value": "dffdsg"}, {"key": "limit", "value": "12"}]}}, "response": []}, {"name": "testHttpConnection", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/testHttpConnection?reports=sdsd", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "testHttpConnection"], "query": [{"key": "reports", "value": "sdsd"}]}}, "response": []}, {"name": "grptcnt", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/grptcnt/9acb8ba75c3084662b8b73436297b4b30e7b106f?fromtime=232323&totime=353535&assetgroupid=124&gatewayid=234", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "grptcnt", "9acb8ba75c3084662b8b73436297b4b30e7b106f"], "query": [{"key": "fromtime", "value": "232323"}, {"key": "totime", "value": "353535"}, {"key": "assetgroupid", "value": "124"}, {"key": "gatewayid", "value": "234"}]}}, "response": []}, {"name": "nrptcnt", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/nrptcnt/9acb8ba75c3084662b8b73436297b4b30e7b106f?fromtime=232323&totime=353535&assetgroupid=124&gatewayid=234&nodeid=2", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "nrptcnt", "9acb8ba75c3084662b8b73436297b4b30e7b106f"], "query": [{"key": "fromtime", "value": "232323"}, {"key": "totime", "value": "353535"}, {"key": "assetgroupid", "value": "124"}, {"key": "gatewayid", "value": "234"}, {"key": "nodeid", "value": "2"}]}}, "response": []}, {"name": "getdevicesummaryv3", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getdevicesummaryv3/Default/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getdevicesummaryv3", "<PERSON><PERSON><PERSON>", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "getsubscription", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getsubscription/88a8a647ccc49bad69bfd68b2b56f073b2568c10?meid=akjlsfasflkak", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getsubscription", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "meid", "value": "akjlsfasflkak"}]}}, "response": []}, {"name": "gatewaysummaryV2", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/gatewaysummaryV2/88a8a647ccc49bad69bfd68b2b56f073b2568c10?groupid=232&subgroupid=221&assetgroupid=12&gatewayid=232&offset=2&limit=3", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "gatewaysummaryV2", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "groupid", "value": "232"}, {"key": "subgroupid", "value": "221"}, {"key": "assetgroupid", "value": "12"}, {"key": "gatewayid", "value": "232"}, {"key": "offset", "value": "2"}, {"key": "limit", "value": "3"}]}}, "response": []}, {"name": "gatewayreportV2", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/gatewayreportV2/88a8a647ccc49bad69bfd68b2b56f073b2568c10?groupid=232&subgroupid=221&assetgroupid=12&gatewayid=232&offset=2&limit=3&fromtime=123131&totime=214214", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "gatewayreportV2", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "groupid", "value": "232"}, {"key": "subgroupid", "value": "221"}, {"key": "assetgroupid", "value": "12"}, {"key": "gatewayid", "value": "232"}, {"key": "offset", "value": "2"}, {"key": "limit", "value": "3"}, {"key": "fromtime", "value": "123131"}, {"key": "totime", "value": "214214"}]}}, "response": []}, {"name": "pushgatewayReport", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\t\"assettype\" :234,\r\n\t\"meid\" :2234324324,\r\n\t\"assetid\" :23,\r\n\t\"reportdate\" :234324,  \r\n\t \"reporttime\" : 234324,\r\n\t\"timeoffset\" :234234,\r\n\t\"gpsstatus\" :\"true\",\r\n\t\"lat\" : 123213,\r\n\t\"latdir\" :123213,\r\n\t\"lon\" :123213,\r\n\t\"londir\" :123213,\r\n\t\"heading\" :\"uhfaiuf\",\r\n\t\"battery\" :\"sdfd\",\r\n\t\"probetype\" :\"asfasf\",\r\n\t\"temperature\" :23,\r\n\t\"speed\" :2143,\r\n\t\"distance\" :12323,\r\n\t\"tdisplayunit\" :123,\r\n\t\"eventid\" :123,\r\n\t\"signalstrength\" :123,\r\n\t\"heat_index\" :132\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/pushgatewayReport/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "pushgatewayReport", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "getfitpetreport", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\t\"assettype\" :234,\r\n\t\"meid\" :2234324324,\r\n\t\"assetid\" :23,\r\n\t\"reportdate\" :234324,  \r\n\t \"reporttime\" : 234324,\r\n\t\"timeoffset\" :234234,\r\n\t\"gpsstatus\" :\"true\",\r\n\t\"lat\" : 123213,\r\n\t\"latdir\" :123213,\r\n\t\"lon\" :123213,\r\n\t\"londir\" :123213,\r\n\t\"heading\" :\"uhfaiuf\",\r\n\t\"battery\" :\"sdfd\",\r\n\t\"probetype\" :\"asfasf\",\r\n\t\"temperature\" :23,\r\n\t\"speed\" :2143,\r\n\t\"distance\" :12323,\r\n\t\"tdisplayunit\" :123,\r\n\t\"eventid\" :123,\r\n\t\"signalstrength\" :123,\r\n\t\"heat_index\" :132\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/getfitpetreport", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getfitpetreport"]}}, "response": []}, {"name": "http://************:9090/irisservices/v3.0/gatewaysummaryTest/9acb8ba75c3084662b8b73436297b4b30e7b106f?groupid=63&subgroupid=23&assetgroupid=23&gatewayid=4545&offset=0&limit=3", "request": {"method": "GET", "header": [], "url": {"raw": "http://************:9090/irisservices/v3.0/gatewaysummaryTest/9acb8ba75c3084662b8b73436297b4b30e7b106f?groupid=63&subgroupid=23&assetgroupid=23&gatewayid=4545&offset=0&limit=3", "protocol": "http", "host": ["54", "149", "0", "113"], "port": "9090", "path": ["irisservices", "v3.0", "gatewaysummaryTest", "9acb8ba75c3084662b8b73436297b4b30e7b106f"], "query": [{"key": "groupid", "value": "63"}, {"key": "subgroupid", "value": "23"}, {"key": "assetgroupid", "value": "23"}, {"key": "gatewayid", "value": "4545"}, {"key": "offset", "value": "0"}, {"key": "limit", "value": "3"}]}}, "response": []}, {"name": "Create Advertisement", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v4.0/gatewaysummaryTest/2c645697ee35d47f6591b349ac304cb85779d71c?assetgroupid=&groupid=&subgroupid=&gatewayid=&offset=&limit=&deliquencystatus=", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v4.0", "gatewaysummaryTest", "2c645697ee35d47f6591b349ac304cb85779d71c"], "query": [{"key": "assetgroupid", "value": ""}, {"key": "groupid", "value": ""}, {"key": "subgroupid", "value": ""}, {"key": "gatewayid", "value": ""}, {"key": "offset", "value": ""}, {"key": "limit", "value": ""}, {"key": "deliquencystatus", "value": ""}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "ThrottlingController", "item": [{"name": "throttlingsettings", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/throttlingsettings/9acb8ba75c3084662b8b73436297b4b30e7b106f?throttleid=234", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "throttlingsettings", "9acb8ba75c3084662b8b73436297b4b30e7b106f"], "query": [{"key": "throttleid", "value": "234"}]}}, "response": []}, {"name": "throttlingsettings - POST", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/throttlingsettings/9acb8ba75c3084662b8b73436297b4b30e7b106f?throttleid=234", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "throttlingsettings", "9acb8ba75c3084662b8b73436297b4b30e7b106f"], "query": [{"key": "throttleid", "value": "234"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "UserController", "item": [{"name": "user", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/user/9acb8ba75c3084662b8b73436297b4b30e7b106f?userid=2343", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "user", "9acb8ba75c3084662b8b73436297b4b30e7b106f"], "query": [{"key": "userid", "value": "2343"}]}}, "response": []}, {"name": "GetUserBy Id", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/getuserbyid?userid=59619", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "get<PERSON><PERSON><PERSON>"], "query": [{"key": "userid", "value": "59619"}]}}, "response": []}, {"name": "UserUpdate", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"id\": 7120,\r\n    \"firstname\": \"kalai\",\r\n    \"lastname\": \"selvi\",\r\n    \"mobileno\": \"9659428888\",\r\n    \"email\":\"<EMAIL>\",\r\n    \"zipcode\": 1,\r\n    \"city\": \"cbe\",\r\n    \"state\": \"tn\",\r\n    \"country\":\"us\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/userupdate/c8a99e03d1783cea508edbfbcac115d044513a4c", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "userupdate", "c8a99e03d1783cea508edbfbcac115d044513a4c"]}}, "response": []}, {"name": "PasswordUpdate", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://stage-furbitser.nimblewireless.com/irisservices/v3.0/pwdupdate/818cb7d50dd3a32d436517ac4ba9c4e63da48459?password=9659428380", "protocol": "http", "host": ["stage-furbitser", "nimblewireless", "com"], "path": ["irisservices", "v3.0", "pwdupdate", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "password", "value": "9659428380"}]}}, "response": []}, {"name": "getall<PERSON>", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/getalluser/9acb8ba75c3084662b8b73436297b4b30e7b106f", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getall<PERSON>", "9acb8ba75c3084662b8b73436297b4b30e7b106f"]}}, "response": []}, {"name": "Signup", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": " {\r\n\r\n      \"username\": \"nimble\",\r\n      \"password\" : \"nimble\",\r\n      \"companyname\":\"NIMBLE\",\r\n      \"supervisor\": \"SIVA\",\r\n      \"phoneno\":\"2463577\",\r\n      \"mobileno\":\"9874561478\",\r\n      \"email\":\"<EMAIL>\",\r\n      \"address\":\"Chennai\",\r\n      \"throtsettingsid\":\"2\"\r\n\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/signup", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "signup"]}}, "response": []}, {"name": "user -Post", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [], "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://stage-furbitser.nimblewireless.com/irisservices/v3.0/user/818cb7d50dd3a32d436517ac4ba9c4e63da48459?alternateemail=NA&alternatephone=NA&authKey=c8a99e03d1783cea508edbfbcac115d044513a4c&email=<EMAIL>&enable=true&enableid=2290&id=7122& mobileappid=3&amp;mobileno=9876543210&amp;password=ReneKlaassen1&amp;roleid=2&amp;username=<EMAIL>&webappid=2&firstname=kalai&lastname=selvi&zipcode=1&city=cbe&state=tn&country=us&chargebeeid=", "protocol": "http", "host": ["stage-furbitser", "nimblewireless", "com"], "path": ["irisservices", "v3.0", "user", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "alternateemail", "value": "NA"}, {"key": "alternatephone", "value": "NA"}, {"key": "auth<PERSON><PERSON>", "value": "c8a99e03d1783cea508edbfbcac115d044513a4c"}, {"key": "email", "value": "<EMAIL>"}, {"key": "enable", "value": "true"}, {"key": "<PERSON><PERSON>", "value": "2290"}, {"key": "id", "value": "7122"}, {"key": " mobileappid", "value": "3"}, {"key": "amp;mobileno", "value": "9876543210"}, {"key": "amp;password", "value": "ReneKlaassen1"}, {"key": "amp;roleid", "value": "2"}, {"key": "amp;username", "value": "<EMAIL>"}, {"key": "webappid", "value": "2"}, {"key": "firstname", "value": "kalai"}, {"key": "lastname", "value": "selvi"}, {"key": "zipcode", "value": "1"}, {"key": "city", "value": "cbe"}, {"key": "state", "value": "tn"}, {"key": "country", "value": "us"}, {"key": "chargebeeid", "value": ""}]}}, "response": []}, {"name": "usergateway", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/usergateway/234/9acb8ba75c3084662b8b73436297b4b30e7b106f?gatewayids=2345", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "usergateway", "234", "9acb8ba75c3084662b8b73436297b4b30e7b106f"], "query": [{"key": "gatewayids", "value": "2345"}]}}, "response": []}, {"name": "user -Delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/user/244/9acb8ba75c3084662b8b73436297b4b30e7b106f", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "user", "244", "9acb8ba75c3084662b8b73436297b4b30e7b106f"]}}, "response": []}, {"name": "deleteuser", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/deleteuser/244/9acb8ba75c3084662b8b73436297b4b30e7b106f", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deleteuser", "244", "9acb8ba75c3084662b8b73436297b4b30e7b106f"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/login?username=acnimble&password=acnimble&mobileid=&mobiletype=&mobileid=&mobiletype=", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "login"], "query": [{"key": "username", "value": "acnimble"}, {"key": "password", "value": "acnimble"}, {"key": "mobileid", "value": ""}, {"key": "mobiletype", "value": ""}, {"key": "mobileid", "value": ""}, {"key": "mobiletype", "value": ""}]}}, "response": []}, {"name": "config", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/config", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "config"]}}, "response": []}, {"name": "usertoken - No res", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/27c28278?token=2904912414&enable=true", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["27c28278"], "query": [{"key": "token", "value": "2904912414"}, {"key": "enable", "value": "true"}]}}, "response": []}, {"name": "UserDeviceInfo", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\n\"os\": \"Andriod\",\n\n\t\n\"version\" : \"9.0\",\n\t\n\n   \"devicemodel\" : \"Nokia 6.1\",\n\n\t\n\"deviceid\": \"abcadbsbslgjsdfkjfsdjflkdsfjlk\",\n\n\"appversion\":\"7.0.2\"\n\t\n\t\n}"}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/userdeviceinfo/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "userdeviceinfo", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "getuserdeviceinfo", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\n\"os\": \"Andriod\",\n\n\t\n\"version\" : \"9.0\",\n\t\n\n   \"devicemodel\" : \"Nokia 6.1\",\n\n\t\n\"deviceid\": \"abcadbsbslgjsdfkjfsdjflkdsfjlk\",\n\n\"appversion\":\"7.0.2\"\n\t\n\t\n}"}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/getuserdeviceinfo/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getuserdeviceinfo", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "loginV3", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/loginV3/88a8a647ccc49bad69bfd68b2b56f073b2568c10?username=zakfhsaf&mobiletype=2&mobileid=3&password=sdfsdf&webappid=3&mobileappid=2", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "loginV3", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "username", "value": "zakfhsaf"}, {"key": "mobiletype", "value": "2"}, {"key": "mobileid", "value": "3"}, {"key": "password", "value": "sdfsdf"}, {"key": "webappid", "value": "3"}, {"key": "mobileappid", "value": "2"}]}}, "response": []}, {"name": "userV2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/userV2/88a8a647ccc49bad69bfd68b2b56f073b2568c10?userid=345&cmpid=3", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "userV2", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "userid", "value": "345"}, {"key": "cmpid", "value": "3"}]}}, "response": []}, {"name": "resetpassword", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/resetpassword/88a8a647ccc49bad69bfd68b2b56f073b2568c10?userid=345&cmpid=3", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "resetpassword", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"], "query": [{"key": "userid", "value": "345"}, {"key": "cmpid", "value": "3"}]}}, "response": []}, {"name": "username", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/username/88a8a647ccc49bad69bfd68b2b56f073b2568c10/nimble", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "username", "88a8a647ccc49bad69bfd68b2b56f073b2568c10", "nimble"]}}, "response": []}, {"name": "getUserByUsernameV2", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getUserByUsernameV2?name=nimble", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getUserByUsernameV2"], "query": [{"key": "name", "value": "nimble"}]}}, "response": []}, {"name": "forceUpdate", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/forceUpdate?app=1&version=1.0&mobiletype=2&userid=2000&os=Android", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "forceUpdate"], "query": [{"key": "app", "value": "1"}, {"key": "version", "value": "1.0"}, {"key": "mobiletype", "value": "2"}, {"key": "userid", "value": "2000"}, {"key": "os", "value": "Android"}]}}, "response": []}, {"name": "checklicensefordevice", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/checklicensefordevice/442faadcccf4ed5afccafc5d0371f43f743e59f6?deviceName=unique&deviceType=N5", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "checklicensefordevice", "442faadcccf4ed5afccafc5d0371f43f743e59f6"], "query": [{"key": "deviceName", "value": "unique"}, {"key": "deviceType", "value": "N5"}]}}, "response": []}, {"name": "getPetSafetyUrls", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getPetSafetyUrls/442faadcccf4ed5afccafc5d0371f43f743e59f6", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getPetSafetyUrls", "442faadcccf4ed5afccafc5d0371f43f743e59f6"]}}, "response": []}, {"name": "getpetSafetyblogurls", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getpetSafetyblogurls/442faadcccf4ed5afccafc5d0371f43f743e59f6", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getpetSafetyblogurls", "442faadcccf4ed5afccafc5d0371f43f743e59f6"]}}, "response": []}, {"name": "deleteblog", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\n\t{\n\t\t\"userId\":2324,\n\t\t\"rvBlogId\":345\n\t}\n\t", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/deleteblog/3/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deleteblog", "3", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "usersignup", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": " {\r\n\r\n      \"username\": \"nimble\",\r\n      \"password\" : \"nimble\",\r\n      \"companyname\":\"NIMBLE\",\r\n      \"supervisor\": \"SIVA\",\r\n      \"phoneno\":\"2463577\",\r\n      \"mobileno\":\"9874561478\",\r\n      \"email\":\"<EMAIL>\",\r\n      \"address\":\"Chennai\",\r\n      \"throtsettingsid\":\"2\"\r\n\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/usersignup", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "usersignup"]}}, "response": []}, {"name": "resendverificationlink", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"userid\":2134\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/resendverificationlink", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "resendverificationlink"]}}, "response": []}, {"name": "verify-email", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/verify-email?code=asdflka", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "verify-email"], "query": [{"key": "code", "value": "asdflka"}]}}, "response": []}, {"name": "ActivateUser", "request": {"method": "POST", "header": [], "url": {"raw": "http://************:9090/irisservices/v3.0/activateuser?firstname=test&lastname=order&gatewayName=pet9&address=&email=<EMAIL>&phoneno=1-1234567891&mobileno=1-4382322323&supervisor=&modelid=&model=&qrcCode=294346&alreadyuser=false&cmptype_id=3&throtsettingsid=5&cmpsubtype_id=&purchasedfrom=amazon&orderid=1010366&remarks=&username=294346&mobilepage=home", "protocol": "http", "host": ["54", "149", "0", "113"], "port": "9090", "path": ["irisservices", "v3.0", "activateuser"], "query": [{"key": "firstname", "value": "test"}, {"key": "lastname", "value": "order"}, {"key": "gatewayName", "value": "pet9"}, {"key": "address", "value": ""}, {"key": "email", "value": "<EMAIL>"}, {"key": "phoneno", "value": "1-1234567891"}, {"key": "mobileno", "value": "1-4382322323"}, {"key": "supervisor", "value": ""}, {"key": "modelid", "value": ""}, {"key": "model", "value": ""}, {"key": "qrcCode", "value": "294346"}, {"key": "alreadyuser", "value": "false"}, {"key": "cmptype_id", "value": "3"}, {"key": "throtsettingsid", "value": "5"}, {"key": "cmpsubtype_id", "value": ""}, {"key": "purchasedfrom", "value": "amazon"}, {"key": "orderid", "value": "1010366"}, {"key": "remarks", "value": ""}, {"key": "username", "value": "294346"}, {"key": "mobilepage", "value": "home"}]}}, "response": []}, {"name": "UserDeviceInfo", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\n\"os\": \"Andriod\",\n\n\t\n\"version\" : \"9.0\",\n\t\n\n   \"devicemodel\" : \"Nokia 6.1\",\n\n\t\n\"deviceid\": \"abcadbsbslgjsdfkjfsdjflkdsfjlk\",\n\n\"appversion\":\"7.0.2\"\n\t\n\t\n}"}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/userdeviceinfo/88a8a647ccc49bad69bfd68b2b56f073b2568c10", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "userdeviceinfo", "88a8a647ccc49bad69bfd68b2b56f073b2568c10"]}}, "response": []}, {"name": "saveorupdateRvPetsafety", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "[\n    {\n        \"blogUrl\": \"testblogUrl\",\n        \"imageUrl\": \"testImageUrl\",\n        \"title\": \"testTitle\"\n    }\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://staging-iris.nimblewireless.com/irisservices/v3.0/saveorupdateRvPetsafety/", "protocol": "http", "host": ["staging-iris", "nimblewireless", "com"], "path": ["irisservices", "v3.0", "saveorupdateRvPetsafety", ""]}}, "response": []}, {"name": "user Copy", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/user/9acb8ba75c3084662b8b73436297b4b30e7b106f?userid=2343", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "user", "9acb8ba75c3084662b8b73436297b4b30e7b106f"], "query": [{"key": "userid", "value": "2343"}]}}, "response": []}, {"name": "userupdate-old", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "<EMAIL>", "type": "text"}, {"key": "alternateemail", "value": "<EMAIL>", "type": "text"}, {"key": "alternatephone", "value": "na", "type": "text"}, {"key": "auth<PERSON><PERSON>", "value": "818cb7d50dd3a32d436517ac4ba9c4e63da48459", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "enable", "value": "true", "type": "text"}, {"key": "enable", "value": "checked", "type": "text"}, {"key": "id", "value": "59558", "type": "text"}, {"key": "mobileappid", "value": "3", "type": "text"}, {"key": "mobileno", "value": "1234567890", "type": "text"}, {"key": "password", "value": "1234567890", "type": "text"}, {"key": "roleid", "value": "2", "type": "text"}, {"key": "webappid", "value": "2", "type": "text"}, {"key": "firstname", "value": "ranith", "type": "text"}, {"key": "lastname", "value": "sri", "type": "text"}, {"key": "zipcode", "value": "123456", "type": "text"}, {"key": "city", "value": "cbe", "type": "text"}, {"key": "state", "value": "tn", "type": "text"}, {"key": "country", "value": "india", "type": "text"}], "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://stage-furbitser.nimblewireless.com/irisservices/v3.0/user/818cb7d50dd3a32d436517ac4ba9c4e63da48459?", "protocol": "http", "host": ["stage-furbitser", "nimblewireless", "com"], "path": ["irisservices", "v3.0", "user", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": null, "value": null}]}}, "response": []}, {"name": "forgetpassword", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/irisservices/v3.0/forgetpassword?name=rvtest", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "forgetpassword"], "query": [{"key": "name", "value": "rvtest"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "ActivateUser Set", "item": [{"name": "CreateInventory", "request": {"method": "POST", "header": [], "url": {"raw": "http://************:8080/niomservices/v1.0/inventory/ded40f28c9f220b82ec8233615a8e4c966d296ea?qrc=112237&meid=************17&mac_id=NA223211117&mdn=44333322111117&sensoravailable= NA&probetype=NA&serialnumber=23423423117&devicemodelnumber=N1-503-ML-000007&datetime=2019-04-19 17:00&sim_no=3563242345717&devicestateid=9&locationid=1&orderid=&device_status=available&action=active&sim_vendor=verizon&planid=0", "protocol": "http", "host": ["54", "149", "0", "113"], "port": "8080", "path": ["niomservices", "v1.0", "inventory", "ded40f28c9f220b82ec8233615a8e4c966d296ea"], "query": [{"key": "qrc", "value": "112237"}, {"key": "meid", "value": "************17"}, {"key": "mac_id", "value": "NA223211117"}, {"key": "mdn", "value": "44333322111117"}, {"key": "sensoravailable", "value": " NA"}, {"key": "probetype", "value": "NA"}, {"key": "serialnumber", "value": "23423423117"}, {"key": "devicemodelnumber", "value": "N1-503-ML-000007"}, {"key": "datetime", "value": "2019-04-19 17:00"}, {"key": "sim_no", "value": "3563242345717"}, {"key": "devicestateid", "value": "9"}, {"key": "locationid", "value": "1"}, {"key": "orderid", "value": ""}, {"key": "device_status", "value": "available"}, {"key": "action", "value": "active"}, {"key": "sim_vendor", "value": "verizon"}, {"key": "planid", "value": "0"}]}}, "response": []}, {"name": "ActivateUser", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/activateuser?firstname=testtset123&lastname=ordering&gatewayName=pet1010&address=11659 LAKE RIDE DR&email=<EMAIL>&phoneno=1-1234567891&mobileno=1-4382322323&supervisor=&modelid=&model=&qrcCode=112237&alreadyuser=false&cmptype_id=3&throtsettingsid=5&cmpsubtype_id=&purchasedfrom=amazon&orderid=*********&remarks=&mobilepage=login&username", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "activateuser"], "query": [{"key": "firstname", "value": "testtset123"}, {"key": "lastname", "value": "ordering"}, {"key": "gatewayName", "value": "pet1010"}, {"key": "address", "value": "11659 LAKE RIDE DR"}, {"key": "email", "value": "<EMAIL>"}, {"key": "phoneno", "value": "1-1234567891"}, {"key": "mobileno", "value": "1-4382322323"}, {"key": "supervisor", "value": ""}, {"key": "modelid", "value": ""}, {"key": "model", "value": ""}, {"key": "qrcCode", "value": "112237"}, {"key": "alreadyuser", "value": "false"}, {"key": "cmptype_id", "value": "3"}, {"key": "throtsettingsid", "value": "5"}, {"key": "cmpsubtype_id", "value": ""}, {"key": "purchasedfrom", "value": "amazon"}, {"key": "orderid", "value": "*********"}, {"key": "remarks", "value": ""}, {"key": "mobilepage", "value": "login"}, {"key": "username", "value": null}]}}, "response": []}, {"name": "saveexternal order", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "[{\r\n        \"external_order_id\": \"*********\",\r\n        \"purchase_date\": \"2019-11-18 12:56:59\",\r\n        \"order_status\": \"processing\",\r\n        \"order_channel\": \"amazon\",\r\n        \"sku\": \"BD-G9RQ-ZMG0\",\r\n        \"quantity\": \"1\",\r\n        \"item_tax\": \"0.00\",\r\n        \"shipping_price\": \"139.00\",\r\n        \"shipping_tax\": \"0\",\r\n        \"item_promotion_discount\": \"0.00\",\r\n        \"ship_promotion_discount\": \"0\",\r\n        \"shipping_address_1\": \"11659 LAKE RIDE DR\",\r\n        \"shipping_address_2\": \"NA\",\r\n        \"ship_city\": \"JacksonJacky\",\r\n        \"ship_state\": \"FL\",\r\n        \"ship_postal_code\": \"32223-7413\",\r\n        \"ship_country\": \"US\",\r\n        \"total_price\": \"139.00\",\r\n        \"billing_address_1\": \"11659 LAKE RIDE DR\",\r\n        \"billing_address_2\": \"NA\",\r\n        \"billing_firstname\": \"\",\r\n        \"billing_lastname\": \"<PERSON><PERSON>\",\r\n        \"billing_phoneno\": \"NA\",\r\n        \"billing_email\": \"NA\",\r\n        \"billing_city\": \"NA\",\r\n        \"billing_state\": \"NA\",\r\n        \"billing_postal_code\": \"NA\",\r\n        \"billing_country\": \"NA\"\r\n    }]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://************:8080/niomservices/v1.0/saveexternalorderlist/ded40f28c9f220b82ec8233615a8e4c966d296ea\n", "protocol": "http", "host": ["54", "149", "0", "113"], "port": "8080", "path": ["niomservices", "v1.0", "saveexternalorderlist", "ded40f28c9f220b82ec8233615a8e4c966d296ea\n"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "ChargeBeeController", "item": [{"name": "SubscriptionCreation", "request": {"method": "POST", "header": [], "url": {"raw": "http://stage-furbitser.nimblewireless.com/irisservices/v3.0/chargebeecreatesubscription?fName=Ranjith&lName=kumar&email=<EMAIL>&subPlanId=reactivation-half-yearly-subscription&sQuantity=1&addonId=free-mounting-brackets,free-shipping&aQuantity=1,1&phone=9659428380", "protocol": "http", "host": ["stage-furbitser", "nimblewireless", "com"], "path": ["irisservices", "v3.0", "chargebeecreatesubscription"], "query": [{"key": "fName", "value": "<PERSON><PERSON><PERSON>"}, {"key": "lName", "value": "<PERSON>uma<PERSON>"}, {"key": "email", "value": "<EMAIL>"}, {"key": "subPlanId", "value": "reactivation-half-yearly-subscription"}, {"key": "sQuantity", "value": "1"}, {"key": "addonId", "value": "free-mounting-brackets,free-shipping"}, {"key": "aQuantity", "value": "1,1"}, {"key": "phone", "value": "9659428380"}]}}, "response": []}, {"name": "SSOLogin", "request": {"method": "POST", "header": [], "url": {"raw": "http://{{url}}}:{port}/irisservices/v3.0/ssoLogin?username=<EMAIL>&password=9659428380&redirecturl=https://nimblewireless.chargebeeportal.com/portal/login", "protocol": "http", "host": ["{{url}}}"], "port": "{port}", "path": ["irisservices", "v3.0", "sso<PERSON><PERSON>in"], "query": [{"key": "username", "value": "<EMAIL>"}, {"key": "password", "value": "9659428380"}, {"key": "redirecturl", "value": "https://nimblewireless.chargebeeportal.com/portal/login"}]}}, "response": []}, {"name": "getcurrentsubscriptionplan", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getcurrentsubscriptionplan/43761de09169294f680b2e88efa088a6e81c712f?userid=2000", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getcurrentsubscriptionplan", "43761de09169294f680b2e88efa088a6e81c712f"], "query": [{"key": "period", "value": "1", "disabled": true}, {"key": "userid", "value": "2000"}]}}, "response": []}, {"name": "getavailableupgradeplan", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getavailableupgradeplan/69e7b50b1710babbf5a6783e56d16e7b6099e8f9?planid=1&period=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getavailableupgradeplan", "69e7b50b1710babbf5a6783e56d16e7b6099e8f9"], "query": [{"key": "planid", "value": "1"}, {"key": "period", "value": "1"}]}}, "response": []}, {"name": "http://localhost:8080/irisservices/v3.0/updatesubscriptionplan/818cb7d50dd3a32d436517ac4ba9c4e63da48459?planid=2&periodid=2&userid=7127", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/updatesubscriptionplan/818cb7d50dd3a32d436517ac4ba9c4e63da48459?planid=2&periodid=2&userid=7000", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "updatesubscriptionplan", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "planid", "value": "2"}, {"key": "periodid", "value": "2"}, {"key": "userid", "value": "7000"}]}}, "response": []}, {"name": "getsubsplanbymonitortype", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getsubsplanbymonitortype/69e7b50b1710babbf5a6783e56d16e7b6099e8f9?userid=3858&monitortype=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getsubsplanbymonitortype", "69e7b50b1710babbf5a6783e56d16e7b6099e8f9"], "query": [{"key": "userid", "value": "3858"}, {"key": "monitortype", "value": "1"}]}}, "response": []}, {"name": "<PERSON><PERSON> Config", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/configuredevice/50c928790c89a71a4d6851b378e809c82e4fc14c?userid=59616&monitortype=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "configuredevice", "50c928790c89a71a4d6851b378e809c82e4fc14c"], "query": [{"key": "userid", "value": "59616"}, {"key": "monitortype", "value": "1"}]}, "description": "device config"}, "response": []}, {"name": "SubscriptionCreation", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/chargebeecreatesubscription?fName=kalai&lName=selvi&email=<EMAIL>&subPlanId=stop-subscription&sQuantity=1&addonId=&aQuantity=&phone=9659428380", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "chargebeecreatesubscription"], "query": [{"key": "fName", "value": "kalai"}, {"key": "lName", "value": "selvi"}, {"key": "email", "value": "kalaisel<PERSON>@nimblewireless.com"}, {"key": "subPlanId", "value": "stop-subscription"}, {"key": "sQuantity", "value": "1"}, {"key": "addonId", "value": ""}, {"key": "aQuantity", "value": ""}, {"key": "phone", "value": "9659428380"}]}}, "response": []}, {"name": "cb test api", "request": {"auth": {"type": "apikey", "apikey": [{"key": "value", "value": "Basic dGVzdF9IdXB5OHRkNjNjWXlQM21vVEdlWVpvdUw0b05OWVpRTA==", "type": "string"}, {"key": "key", "value": "Authorization", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://nimblepetapp-test.chargebee.com/api/v2/subscriptions/169yC6RqMVK1W2yL\n", "protocol": "https", "host": ["nimblepetapp-test", "chargebee", "com"], "path": ["api", "v2", "subscriptions", "169yC6RqMVK1W2yL\n"]}, "description": "cb"}, "response": []}, {"name": "update subscription", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/updatesubscriptionplan/ee625fc7fc4a7f24a5ff3fd5d6ba2251b5557a57?planid=4&periodid=1&userid=2753", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "updatesubscriptionplan", "ee625fc7fc4a7f24a5ff3fd5d6ba2251b5557a57"], "query": [{"key": "planid", "value": "4"}, {"key": "periodid", "value": "1"}, {"key": "userid", "value": "2753"}]}, "description": "update subscription"}, "response": []}, {"name": "send device cmd", "request": {"method": "POST", "header": [], "url": {"raw": "http://stage-furbitser.nimblewireless.com/irisservices/v3.0/messageV2?transporttype=1&message=cmd=startfota&seqno=0&gatewayid=64759", "protocol": "http", "host": ["stage-furbitser", "nimblewireless", "com"], "path": ["irisservices", "v3.0", "messageV2"], "query": [{"key": "transporttype", "value": "1"}, {"key": "message", "value": "cmd=startfota"}, {"key": "seqno", "value": "0"}, {"key": "gatewayid", "value": "64759"}]}}, "response": []}, {"name": "SubscriptionCreationv2", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/chargebeecreatesubscriptionv2?fName=nandhu&lName=sri&email=<EMAIL>&subPlanId=pal-monthly&sQuantity=1&addonId=&aQuantity=&phone=9659428380", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "chargebeecreatesubscriptionv2"], "query": [{"key": "fName", "value": "nandhu"}, {"key": "lName", "value": "sri"}, {"key": "email", "value": "<EMAIL>"}, {"key": "subPlanId", "value": "pal-monthly"}, {"key": "sQuantity", "value": "1"}, {"key": "addonId", "value": ""}, {"key": "aQuantity", "value": ""}, {"key": "phone", "value": "9659428380"}]}, "description": "SubscriptionCreationv2"}, "response": []}, {"name": "chargebeeloginv2", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/chargebeeloginv2?username=<EMAIL>&password=9659428380&subPlanId=reactivation-half-yearly-subscription&sQuantity=1&addonId=free-mounting-bracket,free-shipping&aQuantity=1,1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "chargebeeloginv2"], "query": [{"key": "username", "value": "<EMAIL>"}, {"key": "password", "value": "9659428380"}, {"key": "subPlanId", "value": "reactivation-half-yearly-subscription"}, {"key": "sQuantity", "value": "1"}, {"key": "addonId", "value": "free-mounting-bracket,free-shipping"}, {"key": "aQuantity", "value": "1,1"}]}, "description": "chargebeeloginv2"}, "response": []}, {"name": "chargebeeloginv3", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/chargebeeloginv3?username=<EMAIL>&password=1234567890&subPlanId=reactivation-half-yearly-subscription&sQuantity=1&addonId=free-shipping&aQuantity=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "chargebeeloginv3"], "query": [{"key": "username", "value": "<EMAIL>"}, {"key": "password", "value": "1234567890"}, {"key": "subPlanId", "value": "reactivation-half-yearly-subscription"}, {"key": "sQuantity", "value": "1"}, {"key": "addonId", "value": "free-shipping"}, {"key": "aQuantity", "value": "1"}]}}, "response": []}, {"name": "chargebeesignupv3", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/chargebeesignupv3?fName=kalai&lName=selvi&email=<EMAIL>&subPlanId=stop-subscription&sQuantity=1&addonId=&aQuantity=&phone=9659428380", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "chargebeesignupv3"], "query": [{"key": "fName", "value": "kalai"}, {"key": "lName", "value": "selvi"}, {"key": "email", "value": "kalaisel<PERSON>@nimblewireless.com"}, {"key": "subPlanId", "value": "stop-subscription"}, {"key": "sQuantity", "value": "1"}, {"key": "addonId", "value": ""}, {"key": "aQuantity", "value": ""}, {"key": "phone", "value": "9659428380"}]}}, "response": []}, {"name": "chargebeesignupv2", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/chargebeesignupv2?fName=kalai&lName=selvi&email=<EMAIL>&subPlanId=stop-subscription&sQuantity=1&addonId=&aQuantity=&phone=9659428380", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "chargebeesignupv2"], "query": [{"key": "fName", "value": "kalai"}, {"key": "lName", "value": "selvi"}, {"key": "email", "value": "kalaisel<PERSON>@nimblewireless.com"}, {"key": "subPlanId", "value": "stop-subscription"}, {"key": "sQuantity", "value": "1"}, {"key": "addonId", "value": ""}, {"key": "aQuantity", "value": ""}, {"key": "phone", "value": "9659428380"}]}}, "response": []}, {"name": "chargebeeloginnewv2", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/chargebeeloginnewv2?username=ka<PERSON><PERSON><PERSON>@nimblewireless.com&password=9659428380&subPlanId=pal-monthly&sQuantity=1&addonId=free-mounting-bracket,free-shipping&aQuantity=1,1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "chargebeeloginnewv2"], "query": [{"key": "username", "value": "kalaisel<PERSON>@nimblewireless.com"}, {"key": "password", "value": "9659428380"}, {"key": "subPlanId", "value": "pal-monthly"}, {"key": "sQuantity", "value": "1"}, {"key": "addonId", "value": "free-mounting-bracket,free-shipping"}, {"key": "aQuantity", "value": "1,1"}]}}, "response": []}, {"name": "chargebeeloginv2 referral", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/chargebeeloginv2referral?username=ka<PERSON><PERSON><PERSON>@nimblewireless.com&password=9659428380&subPlanId=pal-monthly&sQuantity=1&addonId=free-mounting-bracket,free-shipping&aQuantity=1,1&refcode=11901", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "chargebeeloginv2referral"], "query": [{"key": "username", "value": "kalaisel<PERSON>@nimblewireless.com"}, {"key": "password", "value": "9659428380"}, {"key": "subPlanId", "value": "pal-monthly"}, {"key": "sQuantity", "value": "1"}, {"key": "addonId", "value": "free-mounting-bracket,free-shipping"}, {"key": "aQuantity", "value": "1,1"}, {"key": "refcode", "value": "11901"}]}}, "response": []}, {"name": "chargebeesignupnewv2", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/chargebeesignupnewv2?fName=kalai&lName=selvi&email=<EMAIL>&subPlanId=pal-monthly&sQuantity=1&addonId=&aQuantity=&phone=9659428380", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "chargebeesignupnewv2"], "query": [{"key": "fName", "value": "kalai"}, {"key": "lName", "value": "selvi"}, {"key": "email", "value": "<EMAIL>"}, {"key": "subPlanId", "value": "pal-monthly"}, {"key": "sQuantity", "value": "1"}, {"key": "addonId", "value": ""}, {"key": "aQuantity", "value": ""}, {"key": "phone", "value": "9659428380"}]}}, "response": []}, {"name": "chargebeesignupnewv2 referral", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/chargebeesignupv2referral?fName=kalai&lName=selvi&email=<EMAIL>&subPlanId=pal-monthly&sQuantity=1&addonId=&aQuantity=&phone=9659428380&refcode=11901", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "chargebeesignupv2referral"], "query": [{"key": "fName", "value": "kalai"}, {"key": "lName", "value": "selvi"}, {"key": "email", "value": "<EMAIL>"}, {"key": "subPlanId", "value": "pal-monthly"}, {"key": "sQuantity", "value": "1"}, {"key": "addonId", "value": ""}, {"key": "aQuantity", "value": ""}, {"key": "phone", "value": "9659428380"}, {"key": "refcode", "value": "11901"}]}}, "response": []}, {"name": "https://nimblepetapp-test.chargebee.com/pages/v2/3dIVJBDLCutRyAbEoH8sbDuSImPOGPQt/checkout", "request": {"method": "GET", "header": [], "url": {"raw": "https://nimblepetapp-test.chargebee.com/pages/v2/3dIVJBDLCutRyAbEoH8sbDuSImPOGPQt/checkout", "protocol": "https", "host": ["nimblepetapp-test", "chargebee", "com"], "path": ["pages", "v2", "3dIVJBDLCutRyAbEoH8sbDuSImPOGPQt", "checkout"]}}, "response": []}, {"name": "get subscription details", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getsubscriptiondetails/fd623492a20df516d8fbc07ff75b64f05a26bcab/?subscripid=2QkUcroztucdhlQv5jSHL43kTO36UN0Tcd", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getsubscriptiondetails", "fd623492a20df516d8fbc07ff75b64f05a26bcab", ""], "query": [{"key": "subscripid", "value": "2QkUcroztucdhlQv5jSHL43kTO36UN0Tcd"}]}}, "response": []}, {"name": "updatereferraldetails", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/updatereferraldetails?hostedpageid=VmsWNWz7Fo2R6aeCH8ueE5z2S66Y2ldj", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "updatereferraldetails"], "query": [{"key": "hostedpageid", "value": "VmsWNWz7Fo2R6aeCH8ueE5z2S66Y2ldj"}, {"key": "refcode", "value": "AzqgsERsRLn2N96E", "disabled": true}]}}, "response": []}, {"name": "updatecbcustomercredits", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/updatecbcustomercredits?customerid=16CHMGRyVB6vz5Fl&amount=10000&desc=referral credit", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "updatecbcustomercredits"], "query": [{"key": "customerid", "value": "16CHMGRyVB6vz5Fl"}, {"key": "amount", "value": "10000"}, {"key": "desc", "value": "referral credit"}]}}, "response": []}, {"name": "upgradesubscriptionplan", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v4.0/upgradesubscriptionplan/49397894c42844fd6ec02cef49444599fc952f0f?planid=3&periodid=1&userid=59733", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v4.0", "upgradesubscriptionplan", "49397894c42844fd6ec02cef49444599fc952f0f"], "query": [{"key": "planid", "value": "3"}, {"key": "periodid", "value": "1"}, {"key": "userid", "value": "59733"}]}, "description": "upgradesubscriptionplan"}, "response": []}, {"name": "chargebeepurchase", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/chargebeepurchase?subPlanId=product-only&sQuantity=1&addonId=pet-monitor-4g&aQuantity=1&source=google", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "chargebeepurchase"], "query": [{"key": "username", "value": "<EMAIL>", "disabled": true}, {"key": "password", "value": "product-only", "disabled": true}, {"key": "subPlanId", "value": "product-only"}, {"key": "sQuantity", "value": "1"}, {"key": "addonId", "value": "pet-monitor-4g"}, {"key": "aQuantity", "value": "1"}, {"key": "source", "value": "google"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "ZipCode", "item": [{"name": "ZipCode", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getzipcodedetails/baa7c2fa1402b6afa056c0e32b6daa156b4bd89d?zipcode=74001&countrycode=US", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getzipcodedetails", "baa7c2fa1402b6afa056c0e32b6daa156b4bd89d"], "query": [{"key": "zipcode", "value": "74001"}, {"key": "countrycode", "value": "US"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "MonitorType Controller", "item": [{"name": "getmonitortypebyid", "request": {"method": "GET", "header": [], "url": {"raw": "http://************:9090/irisservices/v3.0/getmonitortypebyid?id=1", "protocol": "http", "host": ["54", "149", "0", "113"], "port": "9090", "path": ["irisservices", "v3.0", "getmonitortypebyid"], "query": [{"key": "id", "value": "1"}]}}, "response": []}, {"name": "updatemonitortype", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\":\"google\",\n\t\"description\":\"ggggggg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/updatemonitortype", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "updatemonitortype"]}}, "response": []}, {"name": "deleteMonitortype", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/deleteMonitortype?id=3", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deleteMonitortype"], "query": [{"key": "id", "value": "3"}]}}, "response": []}, {"name": "getallmonitortypes", "request": {"method": "GET", "header": [], "url": {"raw": "http://************:9090/irisservices/v3.0/getallmonitortypes", "protocol": "http", "host": ["54", "149", "0", "113"], "port": "9090", "path": ["irisservices", "v3.0", "getallmonitortypes"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "CreditSystem", "item": [{"name": "PlanBasedFeature", "item": [{"name": "createfeature", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\n\"feature_name\":\"feature_name3\",\n\"description\":\"fit pal\",\n\"enable\":\"true\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/createfeature/818cb7d50dd3a32d436517ac4ba9c4e63da48459", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "createfeature", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "ptp_id", "value": "1", "disabled": true}]}, "description": "Create feature"}, "response": []}, {"name": "deletefeature", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/deletefeature/818cb7d50dd3a32d436517ac4ba9c4e63da48459?id=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deletefeature", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "1"}]}, "description": "delete feature"}, "response": []}, {"name": "deleteplantofeature", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/deleteplantofeature/818cb7d50dd3a32d436517ac4ba9c4e63da48459?id=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deleteplantofeature", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "1"}]}, "description": "delete feature"}, "response": []}, {"name": "deleteusertofeature", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/deleteusertofeature/818cb7d50dd3a32d436517ac4ba9c4e63da48459?id=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deleteusertofeature", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "1"}]}, "description": "delete feature"}, "response": []}, {"name": "createusertofeature", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "[{\n\t\t\"enable\": 1,\n\t\t\"txn_limit\": 20000,\n\t\t\"extra_txn_limit\": 100,\n\t\t\"resettype\": \"1\",\n\t\t\"user_id\": 891,\n\t\t\"featureid\": 9,\n\t\t\"device_config\":\"0\",\n\t\t\"monitortype_id\":\"1,2\"\n\n\t},\n\t{\n\t\t\"enable\": 1,\n\t\t\"txn_limit\": 200,\n\t\t\"extra_txn_limit\": 100,\n\t\t\"resettype\": \"1\",\n\t\t\"user_id\": 891,\n\t\t\"featureid\": 10,\n\t\t\"device_config\":\"NA\",\n\t\t\"monitortype_id\":\"NA\"\n\t}\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/createusertofeature/ea3243132d653b39025a944e70f3ecdf70ee3994?featureid=1,2", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "createusertofeature", "ea3243132d653b39025a944e70f3ecdf70ee3994"], "query": [{"key": "featureid", "value": "1,2"}, {"key": "userid", "value": "3", "disabled": true}]}, "description": "Plan to monitor"}, "response": []}, {"name": "Listplantofeature", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/listplantofeature/818cb7d50dd3a32d436517ac4ba9c4e63da48459?planid=2", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "listplantofeature", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "planid", "value": "2"}]}}, "response": []}, {"name": "Listusertofeature", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/listusertofeature/6a6dd5969a0a72b1f39abd2809d4d00998742d60?userid=3118", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "listusertofeature", "6a6dd5969a0a72b1f39abd2809d4d00998742d60"], "query": [{"key": "userid", "value": "3118"}]}}, "response": []}, {"name": "Listfeature", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/listfeature/818cb7d50dd3a32d436517ac4ba9c4e63da48459", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "listfeature", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "11", "disabled": true}]}}, "response": []}, {"name": "getfeaturebyuser", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getfeaturebyuser/43761de09169294f680b2e88efa088a6e81c712f", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getfeaturebyuser", "43761de09169294f680b2e88efa088a6e81c712f"], "query": [{"key": "userid", "value": "7000", "disabled": true}, {"key": "monitortype", "value": "1", "disabled": true}]}}, "response": []}, {"name": "updateUserTransaction", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/updateUserTransaction/43761de09169294f680b2e88efa088a6e81c712f?feature=feature_name3", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "updateUserTransaction", "43761de09169294f680b2e88efa088a6e81c712f"], "query": [{"key": "feature", "value": "feature_name3"}, {"key": "userid", "value": "3", "disabled": true}]}}, "response": []}, {"name": "createplantofeature", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "[{\n\t\t\"enable\": 1,\n\t\t\"txn_limit\": 2000,\n\t\t\"extra_txn_limit\": 100,\n\t\t\"resettype\": \"1\",\n\t\t\"planid\": 2,\n\t\t\"featureid\": 1,\n\t\t\"device_config\":\"NA\",\n\t\t\"monitortype_id\":\"NA\"\n\t},\n\t{\n\t\t\"enable\": 1,\n\t\t\"txn_limit\": 1,\n\t\t\"extra_txn_limit\": 0,\n\t\t\"resettype\": \"2\",\n\t\t\"planid\": 2,\n\t\t\"featureid\": 2,\n\t\t\"device_config\":\"0\",\n\t\t\"monitortype_id\":\"1,2\"\n\t}\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/createplantofeature/818cb7d50dd3a32d436517ac4ba9c4e63da48459", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "createplantofeature", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "planid", "value": "2", "disabled": true}, {"key": "featureid", "value": "3,4", "disabled": true}]}}, "response": []}], "protocolProfileBehavior": {}, "_postman_isSubFolder": true}, {"name": "create plan", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"id\":2,\n\"plan_name\"\t:\"FIT pal\",\n\"description\":\"fit pal\",\n\"enable\":\"true\",\n\"custom\":\"false\",\n\"orderno\":1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/createplan/818cb7d50dd3a32d436517ac4ba9c4e63da48459", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "createplan", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "", "value": "1", "disabled": true}]}, "description": "Create Plan"}, "response": []}, {"name": "create plantomonitor", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\"monitortype_id\"\t:\"2\",\n\"no_cnt\":1,\n\"device_config\":\"2/0\",\n\"custom\":0,\n\"enable\":true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/createplantomonitor/818cb7d50dd3a32d436517ac4ba9c4e63da48459?splanid=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "createplantomonitor", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "splanid", "value": "1"}]}, "description": "Plan to monitor"}, "response": []}, {"name": "create plantoperiod", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\"chargebee_planid\"\t:\"nimble-chum\",\n\"custom\":0\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/createplantoperiod/818cb7d50dd3a32d436517ac4ba9c4e63da48459?planid=1&periodid=3", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "createplantoperiod", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "planid", "value": "1"}, {"key": "periodid", "value": "3"}]}, "description": "Plan to monitor"}, "response": []}, {"name": "http://localhost:8080/irisservices/v3.0/updateThrottlingSettings/818cb7d50dd3a32d436517ac4ba9c4e63da48459?thrid=1&cmpid=1", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/updateplanforcompany/818cb7d50dd3a32d436517ac4ba9c4e63da48459?planid=2&cmpid=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "updateplanforcompany", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "planid", "value": "2"}, {"key": "cmpid", "value": "1"}]}}, "response": []}, {"name": "delete plan", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/deletePlan/818cb7d50dd3a32d436517ac4ba9c4e63da48459?id=9", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deletePlan", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "9"}]}, "description": "delete plan"}, "response": []}, {"name": "delete plantomonitortype", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/deleteplantomonitortype/818cb7d50dd3a32d436517ac4ba9c4e63da48459?id=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deleteplantomonitortype", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "1"}]}, "description": "delete plan to monitor"}, "response": []}, {"name": "delete plantoperiod", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/deleteplantoperiod/818cb7d50dd3a32d436517ac4ba9c4e63da48459?id=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deleteplantoperiod", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "1"}]}, "description": "delete plan to period"}, "response": []}, {"name": "list Plan", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/listplan/818cb7d50dd3a32d436517ac4ba9c4e63da48459", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "listplan", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "2", "disabled": true}]}, "description": "view plan"}, "response": []}, {"name": "list Period", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/listsubperiod/818cb7d50dd3a32d436517ac4ba9c4e63da48459", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "listsubperiod", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "2", "disabled": true}]}, "description": "list period"}, "response": []}, {"name": "List plantomonitortype", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/listplantomonitortype/818cb7d50dd3a32d436517ac4ba9c4e63da48459", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "listplantomonitortype", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "11", "disabled": true}]}}, "response": []}, {"name": "List plantoperiod", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/listplantoperiod/818cb7d50dd3a32d436517ac4ba9c4e63da48459", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "listplantoperiod", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "11", "disabled": true}]}}, "response": []}, {"name": "createplantoupgrade", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n\"price\"\t:\"2\",\n\"description\":\"1\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/createplantoupgrade/818cb7d50dd3a32d436517ac4ba9c4e63da48459?ptpid=2&upgradeid=3", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "createplantoupgrade", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "ptpid", "value": "2"}, {"key": "upgradeid", "value": "3"}, {"key": "email", "value": "kalaisel<PERSON>@nimblewireless.com", "disabled": true}, {"key": "subPlanId", "value": "stop-subscription", "disabled": true}, {"key": "sQuantity", "value": "1", "disabled": true}, {"key": "addonId", "value": "", "disabled": true}, {"key": "aQuantity", "value": "", "disabled": true}, {"key": "phone", "value": "9659428380", "disabled": true}]}}, "response": []}, {"name": "listplantoupgrade", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://staging-ris.nimblewireless.com/irisservices/v3.0/listplantoupgrade/818cb7d50dd3a32d436517ac4ba9c4e63da48459", "protocol": "https", "host": ["staging-ris", "nimblewireless", "com"], "path": ["irisservices", "v3.0", "listplantoupgrade", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "upgradeid", "value": "42,45", "disabled": true}, {"key": "email", "value": "kalaisel<PERSON>@nimblewireless.com", "disabled": true}, {"key": "subPlanId", "value": "stop-subscription", "disabled": true}, {"key": "sQuantity", "value": "1", "disabled": true}, {"key": "addonId", "value": "", "disabled": true}, {"key": "aQuantity", "value": "", "disabled": true}, {"key": "phone", "value": "9659428380", "disabled": true}, {"key": "id", "value": "15", "disabled": true}]}}, "response": []}, {"name": "deleteplantoupgrade", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n\"price\"\t:\"2\",\n\"description\":\"1\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/deleteplantoupgrade/818cb7d50dd3a32d436517ac4ba9c4e63da48459?id=72", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deleteplantoupgrade", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "72"}, {"key": "upgradeid", "value": "42,45", "disabled": true}, {"key": "email", "value": "kalaisel<PERSON>@nimblewireless.com", "disabled": true}, {"key": "subPlanId", "value": "stop-subscription", "disabled": true}, {"key": "sQuantity", "value": "1", "disabled": true}, {"key": "addonId", "value": "", "disabled": true}, {"key": "aQuantity", "value": "", "disabled": true}, {"key": "phone", "value": "9659428380", "disabled": true}]}}, "response": []}, {"name": "http://dev-api.nimblepetapp.com/irisservices/v3.0/listresettype/818cb7d50dd3a32d436517ac4ba9c4e63da48459", "request": {"method": "GET", "header": [], "url": {"raw": "http://dev-api.nimblepetapp.com/irisservices/v3.0/listresettype/818cb7d50dd3a32d436517ac4ba9c4e63da48459", "protocol": "http", "host": ["dev-api", "nimblepetapp", "com"], "path": ["irisservices", "v3.0", "listresettype", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "11", "disabled": true}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "File Upload", "item": [{"name": "UploadMultipleFiles", "request": {"method": "GET", "header": [], "url": {"raw": ""}, "description": "localhost:8080/irisservices/uploadMultipleFiles"}, "response": []}, {"name": "download file", "request": {"method": "GET", "header": [], "url": {"raw": ""}, "description": "http://localhost:8080/irisservices/downloadFile/sslnimble.png"}, "response": []}, {"name": "upload file", "request": {"method": "GET", "header": [], "url": {"raw": ""}, "description": "localhost:8080/irisservices/uploadFile"}, "response": []}], "protocolProfileBehavior": {}}, {"name": "wifi", "item": [{"name": "https://staging-iris.nimblewireless.com/irisservices/v3.0/savewifiinfo/7fc74fdced96a0f03e0de04f172659c2848031c1", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n\t\"gatewayid\":\"10052\",\n\t\"userid\":\"2518\",\n\t\"ssid\":\"newSSID\",\n\t\"password\": \"NEWPAssword\",\n\t\"status\":1,\n\t\"createdOn\":\"2019-03-19T03:33:33\",\n\t\"updatedOn\":\"2019-03-19T03:33:33\",\n\t\"ssidCategory\":\"other\",\n\t\"lat\":\"33.00\",\n\t\"lon\":\"34.00\",\n\t\"meid\":\"ddd_352753097708869\",\n\t\"ssidName\":\"SSIDTEST3\",\n\t\"bleVersion\":\"v10\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://staging-iris.nimblewireless.com/irisservices/v3.0/savewifiinfo/7fc74fdced96a0f03e0de04f172659c2848031c1", "protocol": "https", "host": ["staging-iris", "nimblewireless", "com"], "path": ["irisservices", "v3.0", "savewifiinfo", "7fc74fdced96a0f03e0de04f172659c2848031c1"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "Advertisement", "item": [{"name": "http://localhost:8080/irisservices/v3.0/createAdvertisement", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": 5,\r\n    \"imgpath\": \"gggfhfg\",\r\n    \"url\": \"gfhjhgvcbjhjdg\",\r\n    \"title\": \"dg\",\r\n    \"enable\": true,\r\n    \"createdon\": \"2020-03-20 00:00:00.0\",\r\n    \"updatedon\": \"2020-03-22 00:00:00.0\",\r\n    \"expiredon\": \"2020-04-09 21:08:40.0\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/createAdvertisement", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "createAdvertisement"]}}, "response": []}, {"name": "http://localhost:8080/irisservices/v3.0/getAdvertisements", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getAdvertisements", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getAdvertisements"]}}, "response": []}, {"name": "getalladvertisements", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getalladvertisements", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getalladvertisements"]}}, "response": []}, {"name": "http://localhost:8080/irisservices/v3.0/getappimage?imgname=leaderboard&type=iphone&ptp_id=2", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getappimage?imgname=leaderboard&type=iphone&ptp_id=2", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getappimage"], "query": [{"key": "im<PERSON>me", "value": "leaderboard"}, {"key": "type", "value": "iphone"}, {"key": "ptp_id", "value": "2"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "Trending video", "item": [{"name": "create video link", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"video_Status\": 1,\r\n    \"title\" : \"installation\",\r\n    \"url\": \"https://www.youtube.com/watch?v=_j8hOuLaNvI/\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/createvideoinfo/5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd\n", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "createvideoinfo", "5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd\n"]}}, "response": []}, {"name": "updatevideostatus", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/updatevideostatus/5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd?url=https://www.youtube.com/watch?v=_j8hOuLaNvI&like=1&viewcount=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "updatevideostatus", "5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd"], "query": [{"key": "url", "value": "https://www.youtube.com/watch?v=_j8hOuLaNvI"}, {"key": "like", "value": "1"}, {"key": "viewcount", "value": "1"}]}}, "response": []}, {"name": "gettrendingvideoslist", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/trendingvideoslist/5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "trendingvideoslist", "5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "Leader Board", "item": [{"name": "get user leader board", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getuserleaderboard/49397894c42844fd6ec02cef49444599fc952f0f?rpttype=calories&rptperiod=last30days", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getuserleaderboard", "49397894c42844fd6ec02cef49444599fc952f0f"], "query": [{"key": "date", "value": "2020-04-17", "disabled": true}, {"key": "timezone", "value": "-05:00", "disabled": true}, {"key": "limit", "value": "10", "disabled": true}, {"key": "rpttype", "value": "calories"}, {"key": "rptperiod", "value": "last30days"}]}}, "response": []}, {"name": "get leader board", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/getleaderboard/49397894c42844fd6ec02cef49444599fc952f0f?limit=10&rpttype=calories&rptperiod=last30days", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "getleaderboard", "49397894c42844fd6ec02cef49444599fc952f0f"], "query": [{"key": "date", "value": "2020-04-17", "disabled": true}, {"key": "timezone", "value": "-05:00", "disabled": true}, {"key": "limit", "value": "10"}, {"key": "rpttype", "value": "calories"}, {"key": "rptperiod", "value": "last30days"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "ReferAndEarn", "item": [{"name": "createrefcredits", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\"title\" : \"Refer your friend and get\",\r\n\"advocate_msg\" : \"$199\",\r\n\"referral_msg\" : \"offer on $60 \",\r\n\"referral_successfull_days\" : 10,\r\n\"advocate_successfull_days\" : 10,\r\n\"enable\" : 1,\r\n\"referral_credit_value\" : 10,\r\n\"advocate_credit_value\" : 10\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/irisservices/v3.0/createrefcredits/818cb7d50dd3a32d436517ac4ba9c4e63da48459?advid=1&refid=1&expired=2020-12-30 00:00:00", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "createrefcredits", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "advid", "value": "1"}, {"key": "refid", "value": "1"}, {"key": "expired", "value": "2020-12-30 00:00:00"}]}, "description": "create referral credit"}, "response": []}, {"name": "listrefcredits", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/listrefcredits/818cb7d50dd3a32d436517ac4ba9c4e63da48459", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "listrefcredits", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"]}, "description": "list refer credit"}, "response": []}, {"name": "listcredittype", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/listcredittype/818cb7d50dd3a32d436517ac4ba9c4e63da48459", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "listcredittype", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"]}}, "response": []}, {"name": "generate referral link", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/generatereferrallink/3074b38a7eb16a26c04edeea1cb1cb0ccd4f6524?type=android&imgname=referandearn", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "generatereferrallink", "3074b38a7eb16a26c04edeea1cb1cb0ccd4f6524"], "query": [{"key": "type", "value": "android"}, {"key": "im<PERSON>me", "value": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "response": []}, {"name": "updatehostedpagdetails", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/updatehostedpagdetails?hostedpageid=SiMg2WgmKMfa6Ny6PmgtzcuyrLg143a8P", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "updatehostedpagdetails"], "query": [{"key": "hostedpageid", "value": "SiMg2WgmKMfa6Ny6PmgtzcuyrLg143a8P"}, {"key": "refcode", "value": "AzqgsERsRLn2N96E", "disabled": true}]}, "description": "updatehostedpagdetails"}, "response": []}, {"name": "deleterefcredits", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/deleterefcredits/818cb7d50dd3a32d436517ac4ba9c4e63da48459?id=2", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "deleterefcredits", "818cb7d50dd3a32d436517ac4ba9c4e63da48459"], "query": [{"key": "id", "value": "2"}]}}, "response": []}, {"name": "http://localhost:8080/irisservices/v3.0/generatereferrallink/3074b38a7eb16a26c04edeea1cb1cb0ccd4f6524", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/generatereferrallink/3074b38a7eb16a26c04edeea1cb1cb0ccd4f6524", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "generatereferrallink", "3074b38a7eb16a26c04edeea1cb1cb0ccd4f6524"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "Furbit Report", "item": [{"name": "furbitreport-last 30 days", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/furbitreport/5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd?date=2020-02-25&gatewayId=64755&hour=&timezone=+00:00&days=30&reportDays=7", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "furbitreport", "5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd"], "query": [{"key": "date", "value": "2020-02-25"}, {"key": "gatewayId", "value": "64755"}, {"key": "hour", "value": ""}, {"key": "timezone", "value": "+00:00"}, {"key": "days", "value": "30"}, {"key": "reportDays", "value": "7"}]}}, "response": []}, {"name": "furbitreport-last 7days", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/furbitreport/5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd?date=2020-02-25&gatewayId=64755&hour=&timezone=+00:00&days=7&reportDays=1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "furbitreport", "5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd"], "query": [{"key": "date", "value": "2020-02-25"}, {"key": "gatewayId", "value": "64755"}, {"key": "hour", "value": ""}, {"key": "timezone", "value": "+00:00"}, {"key": "days", "value": "7"}, {"key": "reportDays", "value": "1"}]}}, "response": []}, {"name": "furbitdailyreport - hourly", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/furbitdailyreport/5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd?date=2020-02-25&gatewayId=64755&hour=3&timezone=-05:00", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "furbitdailyreport", "5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd"], "query": [{"key": "date", "value": "2020-02-25"}, {"key": "gatewayId", "value": "64755"}, {"key": "hour", "value": "3"}, {"key": "timezone", "value": "-05:00"}]}}, "response": []}, {"name": "Furbit daily summary report by user - 24hrs", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/furbitdailyreport/5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd?date=2020-02-25&gatewayId=&hour=24&timezone=-05:00", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "furbitdailyreport", "5aaf8905f32a43e8dd1a2ea0cbacb689428ccecd"], "query": [{"key": "date", "value": "2020-02-25"}, {"key": "gatewayId", "value": ""}, {"key": "hour", "value": "24"}, {"key": "timezone", "value": "-05:00"}]}}, "response": []}, {"name": "furbit lastgateway report", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/furbitlastgatewayreport/971a24fb7f49ac6a6cbf1068912158e5b9ab1e22", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "furbitlastgatewayreport", "971a24fb7f49ac6a6cbf1068912158e5b9ab1e22"], "query": [{"key": "userid", "value": "59721", "disabled": true}]}}, "response": []}, {"name": "checkwifistatusv2", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/irisservices/v3.0/checkwifistatusv2/87fde94dbad02c2103bae97c4a99ae23ed740aec?&gatewayId=10695&timezone=+05:30", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["irisservices", "v3.0", "checkwifistatusv2", "87fde94dbad02c2103bae97c4a99ae23ed740aec"], "query": [{"key": null, "value": null}, {"key": "gatewayId", "value": "10695"}, {"key": "timezone", "value": "+05:30"}]}}, "response": []}], "protocolProfileBehavior": {}}], "protocolProfileBehavior": {}}