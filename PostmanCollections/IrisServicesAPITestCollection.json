{"info": {"_postman_id": "b48747e3-9f45-402e-8c21-b8b92ab2062c", "name": "IrisServicesAPITestCollection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "ListPlanToPeriod - Get", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"ListPlanToPeriod - Status Check\", function () {", "    pm.response.to.have.status(200);", "});", "", "const jsonData = pm.response.json();", "pm.test(\"ListPlanToPeriod - Response Status\", function () {", "    pm.expect(jsonData.response.Status).to.eql(1);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "https://{{url}}/{{service}}/v3.0/listplantoperiod/{{authToken}}", "protocol": "https", "host": ["{{url}}"], "path": ["{{service}}", "v3.0", "listplantoperiod", "{{authToken}}"]}}, "response": []}, {"name": "LoginV2 - Post", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"LoginV2 v3.0 POST - Status Check\", function () {", "    pm.response.to.have.status(200);", "});", "", "const jsonData = pm.response.json();", "pm.test(\"LoginV2 v3.0 POST - Response Status\", function () {", "    pm.expect(jsonData.response.Status).to.eql(1);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "https://{{url}}/{{service}}/v3.0/loginV2?username=nimble&password=nimble&mobileid=&mobiletype=&webappid=2&mobileappid=", "protocol": "https", "host": ["{{url}}"], "path": ["{{service}}", "v3.0", "loginV2"], "query": [{"key": "username", "value": "nimble"}, {"key": "password", "value": "nimble"}, {"key": "mobileid", "value": ""}, {"key": "mobiletype", "value": ""}, {"key": "webappid", "value": "2"}, {"key": "mobileappid", "value": ""}]}}, "response": []}, {"name": "gatewaysummaryTest v4.0", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"gatewaysummaryTest v4.0 - Status Check\", function () {", "    pm.response.to.have.status(200);", "});", "", "const jsonData = pm.response.json();", "pm.test(\"gatewaysummaryTest v4.0 - Response Status\", function () {", "    pm.expect(jsonData.response.Status).to.eql(1);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "https://{{url}}/{{service}}/v4.0/gatewaysummaryTest/{{authToken}}?groupid=&subgroupid=&assetgroupid=&gatewayid=&deliquencystatus=&offset=&limit=", "protocol": "https", "host": ["{{url}}"], "path": ["{{service}}", "v4.0", "gatewaysummaryTest", "{{authToken}}"], "query": [{"key": "groupid", "value": ""}, {"key": "subgroupid", "value": ""}, {"key": "assetgroupid", "value": ""}, {"key": "gatewayid", "value": ""}, {"key": "deliquencystatus", "value": ""}, {"key": "offset", "value": ""}, {"key": "limit", "value": ""}]}}, "response": []}, {"name": "getgeneraldata v4.0", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"getgeneraldata v4.0 - Status Check\", function () {", "    pm.response.to.have.status(200);", "});", "", "const jsonData = pm.response.json();", "pm.test(\"getgeneraldata v4.0 - Response Status\", function () {", "    pm.expect(jsonData.response.Status).to.eql(1);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "https://{{url}}/{{service}}/v4.0/getgeneraldata", "protocol": "https", "host": ["{{url}}"], "path": ["{{service}}", "v4.0", "getgeneraldata"]}}, "response": []}]}