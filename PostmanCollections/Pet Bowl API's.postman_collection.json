{"info": {"_postman_id": "a1d1ae06-200a-477f-87e2-9c68d707947b", "name": "Pet Bowl API's", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "18530836"}, "item": [{"name": "Get WiFi Info", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "password", "value": "12345", "type": "string"}, {"key": "accessTokenUrl", "value": "https://dev-api.nimblepetapp.com/waggleauth/oauth/token", "type": "string"}, {"key": "clientSecret", "value": "WagglePetAPP", "type": "string"}, {"key": "clientId", "value": "app", "type": "string"}, {"key": "grant_type", "value": "password_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [{"key": "auth", "value": "d6207055b39288c86b74b1da564faa7fc00c201a", "type": "text"}], "url": {"raw": "{{l80}}/irisservices/app/v4.0/getwifiinfolist?os&app_ver&type=android", "host": ["{{l80}}"], "path": ["irisservices", "app", "v4.0", "getwifiinfolist"], "query": [{"key": "os", "value": null}, {"key": "app_ver", "value": null}, {"key": "type", "value": "android"}]}}, "response": []}, {"name": "Save Wifi Info", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "password", "value": "12345", "type": "string"}, {"key": "accessTokenUrl", "value": "https://dev-api.nimblepetapp.com/waggleauth/oauth/token", "type": "string"}, {"key": "clientSecret", "value": "WagglePetAPP", "type": "string"}, {"key": "clientId", "value": "app", "type": "string"}, {"key": "grant_type", "value": "password_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "POST", "header": [{"key": "auth", "value": "d6207055b39288c86b74b1da564faa7fc00c201a", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"meid\": \"PB_799899988123029\",\r\n    \"ssidName\": \"Home\",\r\n    \"subnet\": \"0\",\r\n    \"default_gateway\": \"0\",\r\n    \"rssi\": \"-33\",\r\n    \"address\": \"NA\",\r\n    \"timezone\": \"+05:30\",\r\n    \"staticip\": \"0\",\r\n    \"dns\": \"0\",\r\n    \"lon\": 0.0,\r\n    \"ip_type\": \"Dynamic\",\r\n    \"ssid\": \"PQA\",\r\n    \"userid\": \"9506\",\r\n    \"nearby\": true,\r\n    \"manual_wifi\": false,\r\n    \"password\": \"12457546\",\r\n    \"ssidCategory\": \"SSID_1\",\r\n    \"bleVersion\": \"UNKNOWN\",\r\n    \"gatewayid\": 14853,\r\n    \"lat\": 0.0,\r\n    \"status\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{l80}}/irisservices/app/v4.0/savewifiinfo?os&app_ver", "host": ["{{l80}}"], "path": ["irisservices", "app", "v4.0", "savewifiinfo"], "query": [{"key": "os", "value": null}, {"key": "app_ver", "value": null}]}}, "response": []}, {"name": "<PERSON> Feed Details", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "password", "value": "12345", "type": "string"}, {"key": "accessTokenUrl", "value": "https://dev-api.nimblepetapp.com/waggleauth/oauth/token", "type": "string"}, {"key": "clientSecret", "value": "WagglePetAPP", "type": "string"}, {"key": "clientId", "value": "app", "type": "string"}, {"key": "grant_type", "value": "password_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "POST", "header": [{"key": "auth", "value": "d6207055b39288c86b74b1da564faa7fc00c201a", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"gateway_id\": 1,\r\n    \"user_id\": 2,\r\n    \"pet_food_id\" : 1,\r\n    \"meals_per_day_id\" : 1,\r\n    \"meal_times\" : \"05:00,05:30\",\r\n    \"timezone\": \"+05:30\",\r\n    \"remainder\": true,\r\n    \"meal_time_updated\" : true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{l80}}/irisservices/app/v4.0/petfeeddetails?os&app_ver", "host": ["{{l80}}"], "path": ["irisservices", "app", "v4.0", "petfeeddetails"], "query": [{"key": "os", "value": null}, {"key": "app_ver", "value": null}]}}, "response": []}, {"name": "Pet Food", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "password", "value": "12345", "type": "string"}, {"key": "accessTokenUrl", "value": "https://dev-api.nimblepetapp.com/waggleauth/oauth/token", "type": "string"}, {"key": "clientSecret", "value": "WagglePetAPP", "type": "string"}, {"key": "clientId", "value": "app", "type": "string"}, {"key": "grant_type", "value": "password_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "POST", "header": [{"key": "auth", "value": "d6207055b39288c86b74b1da564faa7fc00c201a", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"food_name\" : \"test2\",\r\n    \"calories\" : 1000.9\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev}}/irisservices/app/v4.0/petfood?os&app_ver", "host": ["{{dev}}"], "path": ["irisservices", "app", "v4.0", "petfood"], "query": [{"key": "os", "value": null}, {"key": "app_ver", "value": null}]}}, "response": []}, {"name": "List Bowl Pet Profile", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "password", "value": "12345", "type": "string"}, {"key": "accessTokenUrl", "value": "https://dev-api.nimblepetapp.com/waggleauth/oauth/token", "type": "string"}, {"key": "clientSecret", "value": "WagglePetAPP", "type": "string"}, {"key": "clientId", "value": "app", "type": "string"}, {"key": "grant_type", "value": "password_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [{"key": "auth", "value": "d6207055b39288c86b74b1da564faa7fc00c201a", "type": "text"}], "url": {"raw": "{{dev}}/irisservices/app/v4.0/listbowlpetprofile?os&app_ver&gateway_id=14853", "host": ["{{dev}}"], "path": ["irisservices", "app", "v4.0", "listbowlpetprofile"], "query": [{"key": "os", "value": null}, {"key": "app_ver", "value": null}, {"key": "gateway_id", "value": "14853"}]}}, "response": []}, {"name": "<PERSON> Feed Details", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "password", "value": "12345", "type": "string"}, {"key": "accessTokenUrl", "value": "https://dev-api.nimblepetapp.com/waggleauth/oauth/token", "type": "string"}, {"key": "clientSecret", "value": "WagglePetAPP", "type": "string"}, {"key": "clientId", "value": "app", "type": "string"}, {"key": "grant_type", "value": "password_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [{"key": "auth", "value": "d6207055b39288c86b74b1da564faa7fc00c201a", "type": "text"}], "url": {"raw": "{{l80}}/irisservices/app/v4.0/petfeeddetails?os&app_ver&gateway_id=67", "host": ["{{l80}}"], "path": ["irisservices", "app", "v4.0", "petfeeddetails"], "query": [{"key": "os", "value": null}, {"key": "app_ver", "value": null}, {"key": "gateway_id", "value": "67"}]}}, "response": []}, {"name": "Pet Food", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "password", "value": "12345", "type": "string"}, {"key": "accessTokenUrl", "value": "https://dev-api.nimblepetapp.com/waggleauth/oauth/token", "type": "string"}, {"key": "clientSecret", "value": "WagglePetAPP", "type": "string"}, {"key": "clientId", "value": "app", "type": "string"}, {"key": "grant_type", "value": "password_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [{"key": "auth", "value": "d6207055b39288c86b74b1da564faa7fc00c201a", "type": "text"}], "url": {"raw": "{{l80}}/irisservices/app/v4.0/petfood?os&app_ver", "host": ["{{l80}}"], "path": ["irisservices", "app", "v4.0", "petfood"], "query": [{"key": "os", "value": null}, {"key": "app_ver", "value": null}]}}, "response": []}, {"name": "Create Bowl Pet Profile", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "password", "value": "12345", "type": "string"}, {"key": "accessTokenUrl", "value": "https://dev-api.nimblepetapp.com/waggleauth/oauth/token", "type": "string"}, {"key": "clientSecret", "value": "WagglePetAPP", "type": "string"}, {"key": "clientId", "value": "app", "type": "string"}, {"key": "grant_type", "value": "password_credentials", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "POST", "header": [{"key": "auth", "value": "d6207055b39288c86b74b1da564faa7fc00c201a", "type": "text"}], "body": {"mode": "raw", "raw": "[\r\n    {\r\n        \"breed\": \"American Bulldog\",\r\n        \"id\": \"0\",\r\n        \"intact\": \"Intact Adult\",\r\n        \"weight\": \"1208\",\r\n        \"structure\": \"Very thin\",\r\n        \"specieName\": \"Dog\",\r\n        \"gatewayId\": \"14853\",\r\n        \"user_id\": \"9507\",\r\n        \"name\" : \"sb_14853\"\r\n    }\r\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev}}/irisservices/app/v4.0/createbowlpetprofile/?os=iOS&app_type=flutter&app_ver=7.3.0&userid=9506", "host": ["{{dev}}"], "path": ["irisservices", "app", "v4.0", "createbowlpetprofile", ""], "query": [{"key": "os", "value": "iOS"}, {"key": "app_type", "value": "flutter"}, {"key": "app_ver", "value": "7.3.0"}, {"key": "userid", "value": "9506"}]}}, "response": []}]}