package com.nimble.irisservices.controller;

import static org.assertj.core.api.Assertions.assertThat; 

import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

import javax.transaction.Transactional;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonParser;
import com.nimble.irisservices.Application;
import com.nimble.irisservices.entity.User;

@WebMvcTest(Application.class)
class UserControllerV4Test {

//	@Autowired
//	private MockMvc mvc;
//
//	@ParameterizedTest(name = "{index} {2}")
//	@CsvSource({
//		"<EMAIL>,123,Success,1",
//		"' ',123,Empty Username,2",
//		"<EMAIL>,' ',Empty Password,3",
//		"<EMAIL>,123,Invalid Username,4",
//		"<EMAIL>,12345,Invalid Password,5",
//		"<EMAIL>,123,Invalid Username or Password,6",
//		"' ',' ',Empty Username and password,7"
//	})
//	@Transactional
//	public void getUserByName(String username,String password,String testCase,int caseNo) {
//		try {
//				String url = "/v4.0/loginV2?username="+username+"&password="+password+"&mobiletype=&mobileid=&webappid=&mobileappid=";
//				 MockHttpServletResponse response = mvc
//			                .perform(post(url) 
//			                .contentType(APPLICATION_JSON))
//			                .andReturn()
//			                .getResponse();
//				 assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
//				 JSONObject jObj = new JSONObject(response.getContentAsString());
//				 JSONObject res = jObj.getJSONObject("response");
//				 Gson gson = new GsonBuilder().setPrettyPrinting().create();
//				 String Msg = res.getString("Msg");
//				 JsonParser jp = new JsonParser();
//				 System.out.println("\n\n"+caseNo +" "+testCase+"\n");
//				 System.out.println(gson.toJson(jp.parse((response.getContentAsString()))));
//				 String[] caseRes = { "success","Please enter username","Please enter Password","Invalid username!","Invalid password","Account Disabled. Please contact support!","Please enter Username & Password" };
//				 assertThat(caseRes[caseNo-1]).isEqualToIgnoringCase(Msg);
//		} catch( Exception e ) {
//			e.printStackTrace();
//		}
//
//	}
	

//	@ParameterizedTest(name = "{index} {0}")
//	@CsvSource({
//		"Success,1",
//		"Email exist,2",
//		"Invalid auth key,3",
//		"UnExpected error,4"
//	})
//	@Transactional
//	public void userUpdateV4(String testCase,int caseNo) {
//		try {
//				User[] user = userObjs();
//				String auth = user[caseNo-1].getAuthKey();
//				byte[] content = new Gson().toJson(user[caseNo-1]).getBytes();
//				String url = "/v4.0/userupdate/"+auth;
//				 MockHttpServletResponse response = mvc
//			                .perform(post(url) 
//			                .content(content)
//			                .contentType(APPLICATION_JSON))
//			                .andReturn()
//			                .getResponse();
//				 assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
//				 JSONObject jObj = new JSONObject(response.getContentAsString());
//				 JSONObject res = jObj.getJSONObject("response");
//				 Gson gson = new GsonBuilder().setPrettyPrinting().create();
//				 String Msg = res.getString("Msg");
//				 JsonParser jp = new JsonParser();
//				 System.out.println("\n\n"+caseNo +" "+testCase+"\n");
//				 System.out.println(gson.toJson(jp.parse((response.getContentAsString()))));
//				 String[] caseRes = { "Success","Email already exist. Please enter alternate Email","invalid authentication key","UnExcepted Error in User Updation" };
//				 assertThat(caseRes[caseNo-1]).isEqualToIgnoringCase(Msg);
//		} catch( Exception e ) {
//			e.printStackTrace();
//		}
//
//	}
//
//
//	private User[] userObjs() {
//		
//		User userSuccess = new User();
//		userSuccess.setUsername("<EMAIL>");
//		userSuccess.setEmail("<EMAIL>");
//		userSuccess.setMobileno("9876543210");
//		userSuccess.setAuthKey("91b8dbd67737c3566f9d4118314f71a97b3bedcf");
//		
//		User userEmailExis = new User();
//		userEmailExis.setUsername("<EMAIL>");
//		userEmailExis.setEmail("<EMAIL>");
//		userEmailExis.setAuthKey("5af68afc74ac145dfa2e4a2540a655bc5b597bc9");
//		
//		User invalidAuth = new User();
//		invalidAuth.setUsername("<EMAIL>");
//		invalidAuth.setEmail("<EMAIL>");
//		invalidAuth.setAuthKey("5af68afc74ac145222232323a2e4a2540a655bc5b597bc9");
//		
//		User userUnExpected = new User();
//		userUnExpected.setUsername("<EMAIL>");
//		userUnExpected.setEmail("<EMAIL>");
//		userUnExpected.setAuthKey("91b8dbd67737c3566f9d4118314f71a97b3bedcf");
//		
//		
//		User[] user = { userSuccess,userEmailExis,invalidAuth,userUnExpected };
//		return user;
//	}
	
	
}
