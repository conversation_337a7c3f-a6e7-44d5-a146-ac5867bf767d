package com.nimble.irisservices.appcontroller;

import com.fasterxml.jackson.databind.JsonNode;
import com.nimble.irisservices.BaseControllerTest;
import com.nimble.irisservices.dto.JValidateString;
import com.nimble.irisservices.dto.UserPlanPreference;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.error.OAuth2Error;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IUserServiceV4;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.ResultActions;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class UserControllerV4AppTest extends BaseControllerTest {

    @MockBean
    IUserServiceV4 mockUserService;

    private static final String USERNAME = "NIMBLE";

    @Test
    public void testUserUpdateV6() throws Exception {

        UserV4 mockUser = getMockUser();
        User user = new User();
        user.setUsername(USERNAME);
        user.setId(1L);
        user.setFirstname("Firstname");
        user.setLastname("Lastname");

        JValidateString jValidateString = new JValidateString(true, "NA");

        when(mockUserService.verifyAuthV4("authkey", "authkey")).thenReturn(mockUser);
        when(mockUserService.checkAlphabetOnly(anyString(), anyString())).thenReturn(jValidateString);
        when(mockUserService.updateFullUserName(any(), anyLong())).thenReturn(true);

        ResultActions resultActions = mockMvc.perform(post("/app/v6.0/userupdate")
                                            .param("os", "Android")
                                            .param("app_ver", "1.0.1")
                                            .header("auth", "authkey")
                                            .header("Authorization", "Bearer " + getBearerToken())
                                            .contentType(MediaType.APPLICATION_JSON)
                                            .content(objectMapper.writeValueAsString(user)))
                                      .andExpect(status().isOk());

        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(1, jsonNode.get("Status").asInt());
        assertEquals("Success", jsonNode.get("Msg").asText());

        verify(mockUserService).verifyAuthV4(anyString(), anyString());
        verify(mockUserService).checkAlphabetOnly(anyString(), anyString());
        verify(mockUserService).updateFullUserName(any(), anyLong());

    }

    @Test
    public void testUpdateUserV6WithInvalidUser() throws Exception {

        UserV4 mockUser = new UserV4();
        mockUser.setUsername("mockUserName");
        User user = new User();
        user.setUsername(USERNAME);
        user.setId(1L);
        user.setFirstname("Firstname");
        user.setLastname("Lastname");

        when(mockUserService.verifyAuthV4("authkey", "authkey")).thenReturn(mockUser);

        ResultActions resultActions = mockMvc.perform(post("/app/v6.0/userupdate")
                        .param("os", "Android")
                        .param("app_ver", "1.0.1")
                        .header("auth", "authkey")
                        .header("Authorization", "Bearer " + getBearerToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(user)))
                .andExpect(status().isOk());

        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(-2, jsonNode.get("Status").asInt());
        assertEquals(OAuth2Error.FORCELOGOUT, jsonNode.get("Msg").asText());

        verify(mockUserService).verifyAuthV4(anyString(), anyString());
    }

    @Test
    public void testUpdateUserV6WithInvalidAuth() throws Exception {

        User user = new User();
        user.setUsername(USERNAME);
        user.setId(1L);
        user.setFirstname("Firstname");
        user.setLastname("Lastname");

        when(mockUserService.verifyAuthV4("authkey", "authkey")).thenThrow(new InvalidAuthoException());

        ResultActions resultActions = mockMvc.perform(post("/app/v6.0/userupdate")
                        .param("os", "Android")
                        .param("app_ver", "1.0.1")
                        .header("auth", "authkey")
                        .header("Authorization", "Bearer " + getBearerToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(user)))
                .andExpect(status().isOk());

        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(0, jsonNode.get("Status").asInt());
        assertEquals("invalid authkey", jsonNode.get("Msg").asText());

        verify(mockUserService).verifyAuthV4(anyString(), anyString());

    }

    @Test
    public void testUpdateUserV6ThrowsInternalServerError() throws Exception {

        UserV4 mockUser = getMockUser();
        User user = new User();
        user.setUsername(USERNAME);
        user.setId(1L);
        user.setFirstname("Firstname");
        user.setLastname("Lastname");

        when(mockUserService.verifyAuthV4("authkey", "authkey")).thenReturn(mockUser);

        ResultActions resultActions = mockMvc.perform(post("/app/v6.0/userupdate")
                        .param("os", "Android")
                        .param("app_ver", "1.0.1")
                        .header("auth", "authkey")
                        .header("Authorization", "Bearer " + getBearerToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(user)))
                .andExpect(status().isOk());

        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(0, jsonNode.get("Status").asInt());
        assertEquals("UnExcepted Error in User Updation", jsonNode.get("Msg").asText());

        verify(mockUserService).verifyAuthV4(anyString(), anyString());
    }

    @Test
    public void testUpdateUserV6WithInvalidFirstAndLastName() throws Exception{

        UserV4 mockUser = getMockUser();
        User user = new User();
        user.setUsername(USERNAME);
        user.setId(1L);
        user.setFirstname("Firs$tname");
        user.setLastname("Last$name");

        JValidateString jValidateString = new JValidateString(false, "No special characters allowed for Firstname and Lastname.");

        when(mockUserService.verifyAuthV4("authkey", "authkey")).thenReturn(mockUser);
        when(mockUserService.checkAlphabetOnly(anyString(), anyString())).thenReturn(jValidateString);

        ResultActions resultActions = mockMvc.perform(post("/app/v6.0/userupdate")
                        .param("os", "Android")
                        .param("app_ver", "1.0.1")
                        .header("auth", "authkey")
                        .header("Authorization", "Bearer " + getBearerToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(user)))
                .andExpect(status().isOk());

        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(0, jsonNode.get("Status").asInt());
        assertEquals("No special characters allowed for Firstname and Lastname.", jsonNode.get("Msg").asText());

        verify(mockUserService).verifyAuthV4(anyString(), anyString());
        verify(mockUserService).checkAlphabetOnly(anyString(), anyString());
    }

    @Test
    public void testUpdateUserV6WithFailedUpdation() throws Exception {

        UserV4 mockUser = getMockUser();
        User user = new User();
        user.setUsername(USERNAME);
        user.setId(1L);
        user.setFirstname("Firstname");
        user.setLastname("Lastname");

        JValidateString jValidateString = new JValidateString(true, "NA");

        when(mockUserService.verifyAuthV4("authkey", "authkey")).thenReturn(mockUser);
        when(mockUserService.checkAlphabetOnly(anyString(), anyString())).thenReturn(jValidateString);
        when(mockUserService.updateFullUserName(any(), anyLong())).thenReturn(false);

        ResultActions resultActions = mockMvc.perform(post("/app/v6.0/userupdate")
                        .param("os", "Android")
                        .param("app_ver", "1.0.1")
                        .header("auth", "authkey")
                        .header("Authorization", "Bearer " + getBearerToken())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(user)))
                .andExpect(status().isOk());

        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(0, jsonNode.get("Status").asInt());
        assertEquals("UnExcepted Error in User Updation", jsonNode.get("Msg").asText());

        verify(mockUserService).verifyAuthV4(anyString(), anyString());
        verify(mockUserService).checkAlphabetOnly(anyString(), anyString());
        verify(mockUserService).updateFullUserName(any(), anyLong());
    }

    private UserV4 getMockUser() {
        UserV4 mockUser = new UserV4();
        mockUser.setUsername(USERNAME);
        mockUser.setId(1L);
        return mockUser;
    }

    @Test
    public void testUserPlanPreferenceUpdate() throws Exception {

        UserV4 mockUser = getMockUser();

        when(mockUserService.verifyAuthV4("authkey", "authkey")).thenReturn(mockUser);
        when(mockUserService.updateUserPlanPreference(any())).thenReturn(true);

        ResultActions resultActions = mockMvc.perform(post("/app/v5.0/updateplanpreference")
                .param("os", "web")
                .param("app_ver", "1.0.1")
                .param("gateway_id", "1")
                .param("userpref", "PlanPref")
                .header("auth", "authkey")
                .header("Authorization", "Bearer " + getBearerToken())
                .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isOk());

        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(1, jsonNode.get("Status").asInt());
        assertEquals("Success", jsonNode.get("Msg").asText());

        verify(mockUserService).verifyAuthV4(anyString(), anyString());
        verify(mockUserService).updateUserPlanPreference(any());
    }

    @Test
    public void testUserPlanPreferenceUpdateWithFailedUpdate() throws Exception {

        UserV4 mockUser = getMockUser();

        when(mockUserService.verifyAuthV4("authkey", "authkey")).thenReturn(mockUser);
        when(mockUserService.updateUserPlanPreference(any())).thenReturn(false);

        ResultActions resultActions = mockMvc.perform(post("/app/v5.0/updateplanpreference")
                .param("os", "web")
                .param("app_ver", "1.0.1")
                .param("gateway_id", "1")
                .param("userpref", "PlanPref")
                .header("auth", "authkey")
                .header("Authorization", "Bearer " + getBearerToken())
                .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isOk());

        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(0, jsonNode.get("Status").asInt());
        assertEquals("UnExcepted Error in update plan preference", jsonNode.get("Msg").asText());

        verify(mockUserService).verifyAuthV4(anyString(), anyString());
        verify(mockUserService).updateUserPlanPreference(any());
    }

    @Test
    public void testUserPlanPreferenceWithInvalidUser() throws Exception {

        UserV4 mockUser = getMockUser();
        mockUser.setUsername("mockUserName");

        when(mockUserService.verifyAuthV4("authkey", "authkey")).thenReturn(mockUser);

        ResultActions resultActions = mockMvc.perform(post("/app/v5.0/updateplanpreference")
                .param("os", "web")
                .param("app_ver", "1.0.1")
                .param("gateway_id", "1")
                .param("userpref", "PlanPref")
                .header("auth", "authkey")
                .header("Authorization", "Bearer " + getBearerToken())
                .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isOk());


        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(-2, jsonNode.get("Status").asInt());
        assertEquals(OAuth2Error.FORCELOGOUT, jsonNode.get("Msg").asText());

        verify(mockUserService).verifyAuthV4(anyString(), anyString());
    }

    @Test
    public void testUserPlanPreferenceWithInvalidAuth() throws  Exception {

        when(mockUserService.verifyAuthV4("authkey", "authkey")).thenThrow(new InvalidAuthoException());

        ResultActions resultActions = mockMvc.perform(post("/app/v5.0/updateplanpreference")
                .param("os", "web")
                .param("app_ver", "1.0.1")
                .param("gateway_id", "1")
                .param("userpref", "PlanPref")
                .header("auth", "authkey")
                .header("Authorization", "Bearer " + getBearerToken())
                .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isOk());

        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(0, jsonNode.get("Status").asInt());
        assertEquals("invalid authkey", jsonNode.get("Msg").asText());

        verify(mockUserService).verifyAuthV4(anyString(), anyString());
    }

    @Test
    public void testUserPlanPreferenceWithInternalServerError() throws Exception {

        UserV4 mockUser = getMockUser();

        when(mockUserService.verifyAuthV4("authkey", "authkey")).thenReturn(mockUser);
        when(mockUserService.updateUserPlanPreference(any())).thenThrow(new RuntimeException());

        ResultActions resultActions = mockMvc.perform(post("/app/v5.0/updateplanpreference")
                .param("os", "web")
                .param("app_ver", "1.0.1")
                .param("gateway_id", "1")
                .param("userpref", "PlanPref")
                .header("auth", "authkey")
                .header("Authorization", "Bearer " + getBearerToken())
                .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isOk());

        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(0, jsonNode.get("Status").asInt());
        assertEquals("UnExcepted Error in update plan preference", jsonNode.get("Msg").asText());

        verify(mockUserService).verifyAuthV4(anyString(), anyString());
    }

}
