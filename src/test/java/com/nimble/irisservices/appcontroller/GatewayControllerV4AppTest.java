package com.nimble.irisservices.appcontroller;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.api.client.json.Json;
import com.nimble.irisservices.BaseControllerTest;
import com.nimble.irisservices.dto.JProductWithSubCategory;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.error.OAuth2Error;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.ICommonService;
import com.nimble.irisservices.service.IUserServiceV4;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.ResultActions;

import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class GatewayControllerV4AppTest extends BaseControllerTest {

    @MockBean
    IUserServiceV4 mockUserServiceV4;

    @MockBean
    ICommonService mockCommonService;

    private static final String USERNAME = "NIMBLE";

    @Test
    void testGetProductList() throws Exception {

        UserV4 mockUser = getMockUser();

        ArrayList<JProductWithSubCategory> mockProductList = getMockProductList();

        when(mockUserServiceV4.verifyAuthV4("authkey", "authkey")).thenReturn(mockUser);
        when(mockCommonService.getProductList()).thenReturn(mockProductList);


        ResultActions resultActions = mockMvc.perform(get("/app/v6.0/productcategory")
                        .param("os", "android")
                        .param("app_ver", "1.0.0")
                        .header("Accept", "application/json")
                        .header("auth", "authkey")
                        .header("Authorization", "Bearer " + getBearerToken()))
                .andExpect(status().isOk());

        String responseBody = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseBody).path("response");

        assertEquals(1, jsonNode.get("Status").asInt());
        assertEquals("Success", jsonNode.get("Msg").asText());
        assertNotNull(jsonNode.get("product_list"));
        assertEquals(5, jsonNode.get("product_list").size());

        verify(mockUserServiceV4).verifyAuthV4(anyString(), anyString());
        verify(mockCommonService).getProductList();

    }

    @Test
    void testGetProductListWithInvalidUser() throws Exception {

        UserV4 mockUser = new UserV4();
        mockUser.setUsername("mockUserName");

        when(mockUserServiceV4.verifyAuthV4("authkey", "authkey")).thenReturn(mockUser);
        ResultActions resultActions = mockMvc.perform(get("/app/v6.0/productcategory")
                .param("os", "android")
                .param("app_ver", "1.0.0")
                .header("Accept", "application/json")
                .header("auth", "authkey")
                .header("Authorization", "Bearer " + getBearerToken()))
                .andExpect(status().isUnauthorized());

        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(-2, jsonNode.get("Status").asInt());
        assertEquals(OAuth2Error.FORCELOGOUT, jsonNode.get("Msg").asText());

        verify(mockUserServiceV4).verifyAuthV4(anyString(), anyString());
    }

    @Test
    void testGetProductListWithInvalidAuth() throws Exception {

        when(mockUserServiceV4.verifyAuthV4("authkey", "authkey")).thenThrow(new InvalidAuthoException());

        ResultActions resultActions = mockMvc.perform(get("/app/v6.0/productcategory")
                .param("os", "android")
                .param("app_ver", "1.0.0")
                .header("Accept", "application/json")
                .header("auth", "authkey")
                .header("Authorization", "Bearer " + getBearerToken()))
                .andExpect(status().isUnauthorized());

        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(0, jsonNode.get("Status").asInt());
        assertEquals("Invalid Session, Please try again", jsonNode.get("Msg").asText());

        verify(mockUserServiceV4).verifyAuthV4(anyString(), anyString());

    }

    @Test
    public void testGetProductListWithInternalServerError() throws Exception {

        UserV4 mockUser = getMockUser();

        when(mockUserServiceV4.verifyAuthV4("authkey", "authkey")).thenReturn(mockUser);
        when(mockCommonService.getProductList()).thenThrow(new RuntimeException());


        ResultActions resultActions = mockMvc.perform(get("/app/v6.0/productcategory")
                        .param("os", "android")
                        .param("app_ver", "1.0.0")
                        .header("Accept", "application/json")
                        .header("auth", "authkey")
                        .header("Authorization", "Bearer " + getBearerToken()))
                .andExpect(status().isInternalServerError());

        String response = resultActions.andReturn().getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(response).path("response");

        assertEquals(0, jsonNode.get("Status").asInt());
        assertEquals("Error in getProductList", jsonNode.get("Msg").asText());

        verify(mockUserServiceV4).verifyAuthV4(anyString(), anyString());
        verify(mockCommonService).getProductList();

    }

    private UserV4 getMockUser() {
        UserV4 mockUser = new UserV4();
        mockUser.setUsername(USERNAME);
        mockUser.setId(1L);
        return mockUser;
    }

    private ArrayList<JProductWithSubCategory> getMockProductList() {

        ArrayList<JProductWithSubCategory> mockProductList = new ArrayList<>();

        JProductWithSubCategory product1 = new JProductWithSubCategory(1, "Product_1", "product_1.jpg");
        JProductWithSubCategory product2 = new JProductWithSubCategory(2, "Product_2", "product_2.jpg");
        JProductWithSubCategory product3 = new JProductWithSubCategory(3, "Product_3", "product_3.jpg");
        JProductWithSubCategory product4 = new JProductWithSubCategory(4, "Product_4", "product_4.jpg");
        JProductWithSubCategory product5 = new JProductWithSubCategory(5, "Product_5", "product_5.jpg");

        mockProductList.add(product1);
        mockProductList.add(product2);
        mockProductList.add(product3);
        mockProductList.add(product4);
        mockProductList.add(product5);

        return mockProductList;
    }

}
