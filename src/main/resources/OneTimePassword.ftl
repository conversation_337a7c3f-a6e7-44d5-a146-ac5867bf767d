<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Waggle OTP</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    @media only screen and (max-width: 600px) {
      .container {
        width: 100% !important;
        border-width: 3px !important;
      }
      .content {
        padding: 20px 15px !important;
        font-size: 16px !important;
      }
      img.logo {
        width: 140px !important;
        height: auto !important;
      }
    }
  </style>
</head>
<body style="margin:0; padding:0; background-color:#f5f5f5; font-family: Arial, sans-serif;">

  <!-- Wrapper -->
  <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color:#f5f5f5; padding:20px;">
    <tr>
      <td align="center">

        <!-- Main Container with Border -->
        <table border="0" cellpadding="0" cellspacing="0" width="100%" class="container"
               style="max-width:600px; background-color:#ffffff; border:4px solid #444444; border-radius:10px;">

          <!-- Logo -->
          <tr>
            <td align="center" style="padding:20px 15px 10px; border-bottom:1px solid #dddddd;">
              <img src="https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/waggle_logodark.png"
                   alt="Waggle Logo" width="200" class="logo" style="display:block;">
            </td>
          </tr>

          <!-- Content -->
          <tr>
            <td class="content" style="padding:25px 30px; color:#333333; font-size:15px; line-height:1.6; text-align:left;padding-left:100px">
              <p>Dear Waggle Customer ,</p>
              <p>Your Waggle OTP verification code is: </p>
			  <p style="font-size:30px;margin-top: 1px;margin-bottom: 1px; padding-left: 110px;"><strong>${OTP}</strong></p>
              <p>Please enter this code in the app or website to complete your verification.</p>
              <p>This code is valid for the next ${valid_minutes} only.</p>
              <p>If you didn’t request this code, please ignore this message or contact us immediately at
                <a href="mailto:<EMAIL>" style="color:#007bff; text-decoration:none;"><EMAIL></a>.
              </p>
              <p>Stay safe,<br>Team Waggle</p>
            </td>
          </tr>

          <!-- Footer -->
          <tr>
            <td align="center" style="padding:20px 10px; background:#f9f9f9; font-size:14px; color:#666; border-top:1px solid #dddddd;">
              <p style="margin:0 0 8px; font-size:16px; font-weight:bold; color:#007bff;">Join our Community</p>
              <p style="margin:10px 0;">
                <a href="https://www.instagram.com/waggletv/" target="_blank">
                  <img src="https://cdn-icons-png.flaticon.com/512/1384/1384063.png" width="32" style="margin:0 5px;">
                </a>
                <a href="https://www.facebook.com/animaltalesbywaggle" target="_blank">
                  <img src="https://cdn-icons-png.flaticon.com/512/733/733547.png" width="32" style="margin:0 5px;">
                </a>
                <a href="https://www.youtube.com/user/petsami" target="_blank">
                  <img src="https://cdn-icons-png.flaticon.com/512/1384/1384060.png" width="34" style="margin:0 5px;">
                </a>
              </p>
              <p style="margin:5px 0;">
                <a href="https://mywaggle.com" style="color:#007bff; text-decoration:none;">mywaggle.com</a> |
                <a href="mailto:<EMAIL>" style="color:#007bff; text-decoration:none;">Support</a>
              </p>
              <p style="margin:5px 0;">1423 Broadway, OAKLAND, CA 94612 US</p>
              <p style="margin:5px 0; font-size:13px;">
                &copy; <span id="year"></span> Waggle. All rights reserved.<br>
                <a href="https://mywaggle.com/policies/terms-of-service" style="color:#007bff; text-decoration:none;">Terms and Conditions</a>
              </p>
            </td>
          </tr>

        </table>
      </td>
    </tr>
  </table>

  <script>
    document.getElementById("year").textContent = new Date().getFullYear();
  </script>
</body>
</html>
