#  Hibernate Configuration
fileuploaddir=/Users/<USER>/Desktop/MyDownloads/
####################################################################### ************ dev/ 54.71.69.27- st 52.39.150.127-pro
#  MySQL Database Configuration
database.driverClassName=com.mysql.cj.jdbc.Driver
#database.url=************************************
#database.url=***************************************************************************************************
database.url=jdbc:mysql://************:3307/iris?useUnicode=yes&characterEncoding=UTF-8
#database.url=**********************************
database.username=nimble
database.password=flamingo2010

#######################################################################
#Read Replica DB 1
1.read.database.url=jdbc:mysql://************:3307/iris
1.read.database.username=iris_read_1
1.read.database.password=inu@play51

#######################################################################
#Read Replica DB 2
2.read.database.url=jdbc:mysql://************:3307/iris
2.read.database.username=iris_read_2
2.read.database.password=inu@play52

#######################################################################
#Read Replica DB 3
3.read.database.url=jdbc:mysql://************:3307/iris
3.read.database.username=iris_read_3
3.read.database.password=inu@play53

#######################################################################
#Read Replica DB 4
4.read.database.url=jdbc:mysql://************:3307/iris
4.read.database.username=iris_read_4
4.read.database.password=inu@play54

#######################################################################
#Read Replica DB 5
5.read.database.url=jdbc:mysql://************:3307/iris
5.read.database.username=iris_read_5
5.read.database.password=inu@play55

#######################################################################
#DB 2
database.niom.url=jdbc:mysql://************:3307/niom?useSSL=false
database.niom.username=nimble
database.niom.password=flamingo2010

#######################################################################
#  Hibernate Configuration
hibernate.dialect=org.hibernate.dialect.MySQLDialect

#######################################################################
# Hibernate General Configuration
hibernate.show_sql=false
hibernate.format_sql=false
hibernate.generate_statistics=false

############################################################
#Asyn Thread Pool Configuration
aysnc.corePoolSize=30
aysnc.maxPoolSize=100
aysnc.queueCapacity=150

#########################################################
#supportcontactnumber={'US':'+1(855)983-5566','AU':'+61868457214','CA':'+1(855)983-5566'}
#supportemail={'US':'<EMAIL>','AU':'<EMAIL>','CA':'<EMAIL>'}
supportcontactnumber={'US':'+1(855)983-5566','AU':'+61868457214','CA':'+1(855)983-5566','GB':'+1(855)983-5566','IT':'+1(855)983-5566','DE':'+1(855)983-5566'}
supportemail={'US':'<EMAIL>','AU':'<EMAIL>','CA':'<EMAIL>','GB':'<EMAIL>','IT':'<EMAIL>','DE':'<EMAIL>'}


#Niom Details
niomip=http://************:9090/niomservices/
niomauthkey=ded40f28c9f220b82ec8233615a8e4c966d296ea

#Pet Guide URL Details
petguideurl=https://mywaggle.com/pages/rving-with-dogs?


#mapboxtoken
#old
#mapboxtoken=pk.eyJ1IjoicmFiZWVuYSIsImEiOiJjangwYXI5azUxNXcxM3lvNTZlZDRhajRhIn0.-bpfvED7ivET23GJ0u7wkA
#new as on 13/04/2021
mapboxtoken=pk.eyJ1Ijoid2FnZ2xlLWRldmVsb3BlciIsImEiOiJja25nMHRkYmIyNm00MnhtdWQyc2hsc3l6In0.srySxeQZz7CDHBmaQDFNgw


#Product Token
#pk.eyJ1IjoibmltYmxld2lyZWxlc3MiLCJhIjoiY2preHBjcnV3MGE0MjNwcjBucmNnZzd5MSJ9.iEpIubbVkS8Wc8t4UlIybQ

#Dev Token
#pk.eyJ1IjoicmFiZWVuYSIsImEiOiJjangwYXI5azUxNXcxM3lvNTZlZDRhajRhIn0.-bpfvED7ivET23GJ0u7wkA
##########################################################
google.api.key=AIzaSyDP5b-xeE-dcTjj-gq3Y8urGj5Z_a9SKgU
################################################
# Verification API URL
#verificationAPIURL=https://staging-iris.nimblewireless.com/v3.0/verify-email?code=
verificationAPIURL=https://dev-api.nimblepetapp.com/v3.0/verify-email?code=
#verificationAPIURL=http://localhost:8080/irisservices/v3.0/verify-email?code=

#################################################
#Youtube API
#youtubeStatisticsAPI=https://www.googleapis.com/youtube/v3/videos?part=statistics&id=VIDEOID&key=AIzaSyCVOhh02wJ0-854e874eOwMwDIwzMzRf3E
youtubeStatisticsAPI=false

#Open Weather API
openWeatherAPI=https://api.openweathermap.org/data/2.5/weather?lat=LATITUDE&lon=LONGTITUDE&APPID=6424ab3fce53b92487035cc460f49486&units=

##############ChargeBee Configuration #test
chargebee.site.name=nimblepetapp-test
chargebee.site.key=test_Hupy8td63cYyP3moTGeYZouL4oNNYZQL

##############ChargeBee Configuration #live
#chargebee.site.name=nimblepetapp
#chargebee.site.key=live_URhJcEF1Yl6WqjcdSx7Ev8TUcd6XcddduZL
chargebee.addonid={'US':'setup_charges','AU':'setup-charges-aud','CA':'setup_charges_cad','GB':'setup-charges','DE':'setup-charges','IT':'setup-charges'}
chargebee.updateaddonid={'US':'upgrade_charges','AU':'upgrade-charges-aud','CA':'upgrade_charges_cad','GB':'upgrade_charges','DE':'upgrade_charges','IT':'upgrade_charges'}
chargebee.reactivationid={'US':'reactivation-charges-onetime','AU':'reactivation-charges-aud','CA':'reactivation_charges_cad','GB':'reactivation-charges-onetime','DE':'reactivation-charges-onetime','IT':'reactivation-charges-onetime'}
chargebee.downgradeaddonid={'US':'downgrade-charges','AU':'downgrade-charges-aud','CA':'downgrade-charges-cad','GB':'downgrade-charges','DE':'downgrade-charges','IT':'downgrade-charges'}
chargebee.retainaddonid={'US':'retain-charges-usd','AU':'retain-charges-aud','CA':'retain-charges-cad','GB':'retain-charges-usd','DE':'retain-charges-usd','IT':'retain-charges-usd'}
chargebee.trialaddonid={'US':'trial-charges-us','AU':'trial-charges-aud','CA':'trial-charges-cad','GB':'trial-charges-us','DE':'trial-charges-us','IT':'trial-charges-us'}

chargebee.coupon=PRODtest100
chargebee.customerid=CBID123
chargebee.restrictdomain=nim.com
cb.recharge.coupon=REC100
##############Mail verification time 
verificationtime=12

#################################################
nimbleauthkey=8977b8da604d9779d51baf4aacabef5af2771bf7

##################################################
weathericonUrl=http://openweathermap.org/img/wn/

#########################################################
fb_live=https://fb.com/animaltalesbywaggle/live
Facebook=https://www.facebook.com/animaltalesbywaggle
Twitter=https://twitter.com/WagglePetApp
Instagram=https://www.instagram.com/waggletv/
Pinterest=https://www.pinterest.com/nimblepet
support=<EMAIL>
Support_Appstore=https://support.mywaggle.com
Marketing_Appstore={'US':'https://info.mywaggle.com/how-waggle-works','AU':'https://mywaggle.com.au/pages/how-waggle-works','CA':'https://info.mywaggle.com/how-waggle-works','GB':'https://info.mywaggle.com/how-waggle-works','DE':'https://info.mywaggle.com/how-waggle-works','IT':'https://info.mywaggle.com/how-waggle-works'}
Privacy_policy={'US':'https://mywaggle.com/policies/privacy-policy','AU':'https://mywaggle.com.au/policies/privacy-policy','CA':'https://mywaggle.com/policies/privacy-policy','GB':'https://mywaggle.com/policies/privacy-policy','DE':'https://mywaggle.com/policies/privacy-policy','IT':'https://mywaggle.com/policies/privacy-policy'}
buynowfurbit=https://shop.furbit.us/
buynowpetsafety={'US':'https://shop.mywaggle.com?','AU':'https://mywaggle.com.au','CA':'https://shop.mywaggle.com?','GB':'https://shop.mywaggle.com?','IT':'https://shop.mywaggle.com?','DE':'https://shop.mywaggle.com?','GB':'https://shop.mywaggle.com?','DE':'https://shop.mywaggle.com?','IT':'https://shop.mywaggle.com?'}
redirtPetUrl =https://info.mywaggle.com/thank-you
redirtFurbitUrl =https://www.furbit.us/thank-you
redirtVpmUrl=https://info.mywaggle.com/thank-you-vet
ordersuccessurl=https://info.mywaggle.com/thank-you
orderfailedurl=https://info.mywaggle.com/order-failure
blogUrl=https://blog.mywaggle.com/category/all/pet-travel/?
faqUrl={'US':'https://support.mywaggle.com/faq','AU':'https://mywaggle.com.au/pages/how-waggle-works','CA':'https://support.mywaggle.com/faq','GB':'https://support.mywaggle.com/faq','IT':'https://support.mywaggle.com/faq','DE':'https://support.mywaggle.com/faq'}
referralurl=https://waggle.referralcandy.com/?
#referralurl=https://mywaggle.refr.cc/join/default
#terms_conditions={'US':'https://support.mywaggle.com/terms-and-conditions','AU':'https://mywaggle.com.au/policies/terms-of-service','CA':'https://support.mywaggle.com/terms-and-conditions'}
terms_conditions={'US':'https://mywaggle.com/policies/terms-of-service','AU':'https://mywaggle.com.au/policies/terms-of-service','CA':'https://mywaggle.com/policies/terms-of-service','GB':'https://mywaggle.com/policies/terms-of-service','DE':'https://mywaggle.com/policies/terms-of-service','IT':'https://mywaggle.com/policies/terms-of-service'}
enablegoogle=1
enablefb=1
enableapple=1
paymentupdateurl=https://www.wagglepet.io/
vpmbuynow_url=https://info.mywaggle.com/vetchat?
####################################################
google.oauth2.api=https://oauth2.googleapis.com/tokeninfo?id_token=
graph.fb_api=https://graph.facebook.com/me/?access_token=

############### wifi connection check lastgateway report(hours)
lastrpt_max=1
##############wifi connection check wifiinfo report(min)
lastwifirpt_max=15

###############
showpopupregisteration=false

############
inapp.verifyreceipt.password=82d22f979f7f484ca55a12ceb3c8ec47
inapp.verifyreceipt.url=https://sandbox.itunes.apple.com/verifyReceipt

### verizon ###
cust_name=NIMBLE WIRELESS
acc_name=0642109876-00001
plan=4G M2M 1MB
verizonactivation=false

embedv2=false
embedv3=true
embedupdate=false

##Plivo Configurations
plivono=13308221830
enablePowerBack=false
powerBackUUID=291e241c-ef21-45f9-81e6-16f00d7da641

#aws_accessKey and aws_secretKey for S3 bucket
profileimages_url=https://s3-us-west-2.amazonaws.com/
profileimages_bucketname=profileimages.nimblewireless.com
mapbox_folder_name=development/mapbox

### Amplitude ###
amplitude_andriod=********************************
amplitude_ios=********************************

# Delete CB customer flag
customerdelete=false

# Feature vs Plan flag
feature_plan_flag=false

#Microservice SQS configurations
send_registeruseremail_simactivation_to_microservice=false
iris.services.amazonSQS.microserviceQueue.url=https://sqs.us-west-2.amazonaws.com/************/staging-microservice

#Refer n Earn 
weblinkflag=false
weblinkurl=https://waggle.referralcandy.com/?

#pet profile max create count
maxpetprofile=10

#find address by lat, lon using map box for save wifi info
getAddressFromMapBox=true

# Skip Apple Complete SetUp
skipapplecompletesetup=true

#verizon sim reactivation count value
retrycount=3

#Referral Candy Access and Secret Key
usereferralcandy=true
referralcandyAccessKey=prcfqho5kqjtaokpvc54qrk1b
referralcandysecretkey=0fab1be920248827e52b6b274210f1c8

#BMI related changes
note_msg=*Health report is not live for Cats yet.
hr_rateus=true
rateuscount=15
bmi_uw=Underweight: Your Pet's BMI seems to be on the low side. You can consider increasing it's daily feed and also consult a Vet for medical advice.
bmi_cw=Normal:Your Pet's BMI is normal. Give a hug and a treat!
bmi_ow=Obese:Your Pet's BMI is on the higher side. You may consider decreasing daily feed and/or increasing activity levels. Please consult a vet as obesity may lead to other complications.
injury_lr=Your Pet may be well suited for a wide range of activity levels.
injury_mr=Your Pet may be prone to falls so please ensure the activity happens on soft & non slip surfaces.
injury_hr=You may want to consult your vet and pay more attention to your pet's activity type and surface, in order to avoid injuries.
calories_msg=This is an approximate range based on the data you provided. You can target higher/lower calories depending on your pet's specific needs.
disclaimer_msg=Please note that this health report is provided as a guidance only and not intended as medical advice/diagnosis. You should consult a Vet for a formal medical advice/diagnosis.
#Reset Password Link Validity Time in hour
rstPwdLinkValidTime=10
resetpasswordweblink=http://dev-api.nimblepetapp.com

#VPM
#vpm_api_key=28C51E271EBCAB9DD59F9462DCCF9
#vpm_url=https://www.24vetsupport.com/api/public/api/nimble/live/auth/user
#vpm_refresh=https://www.24vetsupport.com/api/public/api/nimble/live/refresh_token
vpm_ver=vpm2
show_vpmdialog=true
vpm_api_key=2477219FB18C611A77B4E43D3F787
vpm_url=http://**************/api_staging/public/api/nimble/auth/user
vpm_refresh=http://**************/api_staging/public/api/nimble/refresh_token

#STAGING
vpm_api_key2=WYlALYCWt7TLA7yy
vpm_url2=https://dev.itmedicalvetsolutions.com/api/nimble/user/handshake
vpm_refresh2=https://dev.itmedicalvetsolutions.com/api/nimble/user/refresh?
vpm_user_pwd=nimble:nimble##

#PROD
#vpm_api_key2=WYlALYCWt7TLA7yy
#vpm_url2=https://itmedicalvetsolutions.com/api/nimble/user/handshake
#vpm_refresh2=https://itmedicalvetsolutions.com/api/nimble/user/refresh?
#vpm_user_pwd=nimble_admin:wD@78t,&Fkz&H^pe

vpm_freecall=false
#chat bot
chatbot=false
checkoutversion=v3
#Amazon
amazonrateuscount=50
#amazonredirecturl=https://www.amazon.com/product-reviews/B07SGCYMGN/ref=acr_dp_hist_5?ie=UTF8&filterByStar=five_star&reviewerType=all_reviews#reviews-filter-bar
amazonredirecturl=https://www.amazon.com/Temperature-Humidity-Cellular-Wireless-compatible/dp/B07SGCYMGN/
walmartredirecturl=https://www.walmart.com/ip/Waggle-RV-Dog-Safety-Temperature-Humidity-Sensor-4G-Verizon-Cellular/940765799
showamazonrateus=true
redirectamazon=false
showfeedback=true
amazonlaunchpadfreetrialenable=false
reportcount_rating=200

# 1 free vpm for user registration done before x days
add_free_vpm=false
vpm_freedays=15

#upgrade
upgrade_msg=Upgrade to 4G pet monitor for reliable pet monitoring.
upgrade4G_url=https://shop.mywaggle.com?

#common subscription hide
hide_subscription=false
sub_title=Billing/Subscription

#ios_subscription hide
ios_hide_subscription=false
ios_sub_title=Billing

# Waggle Authserver Details
waggleauth_url=https://dev-api.nimblepetapp.com/waggleauth

# Waggle Pet Monitor Details
amazon.alexa.lwa_fallback_url= https://www.amazon.com/ap/oa
amazon.alexa.accesstoken_url=https://api.amazon.com/auth/o2/token
amazon.alexa.app_url=https://alexa.amazon.com/spa/skill-account-linking-consent
amazon.alexa.skill_enablement_url=https://api.amazonalexa.com/v1/users/~current/skills
amazon.alexa.clientid=amzn1.application-oa2-client.91d8919da77d4c60b91e454e99215f08
amazon.alexa.clientsecret=99b04cb8d9acd1fcc358015c4eb5b11c6b7316b2efd81cfddd948aba9c34371f
amazon.alexa.skillid=amzn1.ask.skill.2db65fbd-ba6a-4f20-980c-df9224eefc3b
#with respect to skill state kindly use : statedevelopment or live
amazon.alexa.state=development
#waggle.universal_url=https://inapp.mywaggle.com/
#waggle.redirect_uri=https://inapp.mywaggle.com
waggle.universal_url=https://wagglepet.app.link/alexa/
waggle.redirect_uri=https://wagglepet.app.link/alexa/

#Waggle Generic Email SMTP Configurations
email_host=email-smtp.us-west-2.amazonaws.com
waggle_from_address=<EMAIL>
waggle_cc_address=<EMAIL>
waggle_bcc_address=<EMAIL>
show_alexa=false
show_user_story=true

#alert setting pg info content
notify_freq_msg=<p style=text-align:center>Monitoring the temperature 24x7 and Instant alerts when it crosses the limits you set.</p>
notifyfreq_info_url={'US':'https://mywaggle.com','AU':'https://mywaggle.com.au/pages/how-waggle-works','CA':'https://mywaggle.com','GB':'https://info.mywaggle.com/how-waggle-works','IT':'https://info.mywaggle.com/how-waggle-works','DE':'https://info.mywaggle.com/how-waggle-works'}
petdashboard_url={'US':'https://mywaggle.com','AU':'https://mywaggle.com.au/pages/how-waggle-works','CA':'https://mywaggle.com','GB':'https://info.mywaggle.com/how-waggle-works','IT':'https://info.mywaggle.com/how-waggle-works','DE':'https://info.mywaggle.com/how-waggle-works'}

#ios-inapp purchase
enable_ios_inapp_purchase=false

#Reminder config
reminderLimit=10
enable_reminder=true

#Tips config
enable_tips=false

#Event config
enable_event=true

#microservice
microservice_url=https://dev-api.nimblepetapp.com/microservice
microservice_api_call=false

#gps not supported
gps_info=Geofence doesn't support your device.

#order id popup
show_orderid_popup_while_register=false
warranty_msg=<p>Make the <b>Value Combo</b> (Waggle Pet Monitor & Subscription) your's Now! Register your Warranty immediately.</p>
warranty_msg_v2=<html><body><center><p><b>Almost there!</b></p><img style="width: 60%;height: auto;" src=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/warranty.png></br></br>You're just one step away to</br>complete the registration</p></center></body></html>
#<p>Register your One-Year Warranty</p> 


popup_orderid_feedback=true

vpm_channel_retrive_minutes=60
vpm_end_chat_url=https://dev.itmedicalvetsolutions.com/api/nimble/user/chat/end
vpm_continue_chat=false

vpm_continue_chat=true

valid_minutes_for_OTP=10

#CB plans
freeplan=chum,bowl-free,mini-free,wc-free
omitplan=stop-subscription,stop-subscription-non,product-accessories,furbit-pre-order,reseller,product-only
vpmplan=vetchat-monthly-unlimited,vet-chat,vetchat-monthly,vetchat-yearly,emergency-fund-monthly,emergency-fund-yearly
addonplan=sms-addon

#plan vs feature
show_upgrade_msg=false
show_addon_button=false

#validation authkey
validation_authkey=8977b8da604d9779d51baf4aacabef5af2771bf7

#2G & 3G device Plan Purchase Permission
oldDevicePlanPurchase=true
oldDevicePlanPurchaseMSG=<div style="text-align: center"><h3>Why Upgrade to 4G?</h3><p>The era of 2G networks has ended, and major networks like AT&T and T-Mobile will sunset their 3G service in early 2022.</p><p style="margin-top: -10px;">Upgrade to the latest <b>4G Pet Monitor</b> with <b>52%</b> Off now.</p><p>Use Coupon: <b>PET4G</b></p></div>

#spring security config
spring.security.login.url=http://dev-api.nimblepetapp.com/irisngp
config.oauth2.clientid.app=app
config.oauth2.clientsecret.app=WagglePetAPP
config.oauth2.clientid.web=web
config.oauth2.clientsecret.web=web
config.oauth2.accesstoken.validation=43200

#Force logout Key
config.oauth2.forcelogout=false

#Backing key validation time in minutes
backingkey.time.diff=2

#Spring security unauthorized URL error page
service.url=https://dev-api.nimblepetapp.com/irisservices

#return login username for adding recall & replaced devices
return.login.username=<EMAIL>
return.gateway.name=returnDevice

#Buy Now popup while register product
subscription.buynow.popup=true
subscription.buynow.content=<html><body><center><p>You're just a step</br>away from the Ultimate Pet</br>Protection</p><img style="width: 60%;height: auto;" src=https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/pet_on_moon.png><p>Subscribe & activate the</br> Alerts Now!</p></center></body></html>

#Check non fota device & content
check.recall.device.qrc=true

#URL for accessories
#accessories.url=https://waggfluence.com/?utm_source=App&utm_medium=Waggle&utm_campaign=Banner?
accessories.url=https://waggfluence.com/pages/waggle-apps?utm_source=App&utm_medium=Waggle&utm_campaign=Banner?

#claim warranty page
redirect.payment.page=true
redirect.payment.page.content=<html><body><center><p>Save your card details securely,</br> so your pet's safety is always on.</p><img style="width: 60%;height: auto;" src=https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/paymentdetailsadd.png><p style="color:#9494b8">Tap 'More',  Select Payment Method &</br> Save your Card</p></center></body></html>
redirect.checkout.page.content=<html><body><center><p><b>Woohoo!</b></p><p>You're almost there!</p><img style="width: 60%;height: auto;" src=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/check_out.png></br>Your Warranty is successfully registered.</br>Your <b>$$ subscription</b> is one step away.</br></br>Please enter your payment details for Uninterrupted</br> Pet Protection.</br></br>You will not be charged anything until your</br>next renewal*.</p></center></body></html>
show.checkout.page.popup=true

orderid.later.popup=true
orderid.later.popup.content=<html><body><center><img style="width: 60%;height: auto;" src=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/warranty_later.png></br></br>If you've already purchased your</br> Subscription, provide your Order ID to</br>activate it</p></center></body></html>
show.subs.page.first.config=false
product_subs_enable=true
show_offer=true

enable_callus=true

#Amazon PinPoint SMS
pinpoint_aws_access_key_id=********************
pinpoint_aws_secret_access_key=r8notgKMfkMbcKh0kWW7c5DEKfS9cZ3ofPAPDSzN 
region=us-west-2
origination_number=+13254252667
appid=1414217bffbf450f99e56b3407e2a306
message_type=TRANSACTIONAL
registered_keyword=KEYWORD_************
sender_id=1414217bffbf450f99e56b3407e2a306

zip_code_map={1:'10001',2:'90001',3:'75216',4:'78112',5:'88901'}

#device count for show custom plan
device_count_config=1

enable_sku_based_vsim_activation=false
retry_remainder_enable=true

#Schedule wgtxn service Url (don't use / in last) 
schedule_url=https://dev-api.nimblepetapp.com/wgtxnsvc

#deleteUserContent
delete_user_msg=Are you sure to delete your account? Your Profile and Data will be lost permanently.
delete_reason={1:'Currently not using',2:'Not happy with the service',3:'Other'}
delete_user_confirmation=<html><head><link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><style>b{font-family: 'Montserrat'; font-size: 15px;color: #FFFFFF;}span{font-family: 'Montserrat';font-size: 12px;color: #FFFFFF;}</style></head><body><center><b>Account Deleted successfully</b><br><span>Your account will be signed out automatically</span></center></body></html>
delete_img_url=https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/delete_account_white_1.png
delete_img_url_dark=https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/delete_account_black_1.png
delete_email_to=<EMAIL>
delete_email_cc=<EMAIL>

#slackurl
slackurl=*******************************************************************************
#productionurl -> sw-internal-alerts
#slackurl=*******************************************************************************

#show purchased_from while register product
purchased_from=true

show_later_btn_warranty_popup=false

nav_to_warranty_scr=true
warranty_success=<html><body><center><p><b>Hooray!</b></br></br>Your warranty is registered successfully.</p><img style=\"width: 60%;height: auto;\" src=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/subscription_dog_1.svg></br></center></body></html>
warranty_success_subs=<html><body><center><p><b>Hooray!</b></br></br>Your warranty is registered successfully.</p><img style="width: 60%;height: auto;" src=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/subscription_dog_1.svg></br></br><b>Subscribe & Activate</b></br>your alerts now!</p></center></body></html>
warranty_success_sec_dev=<html><body><center><p><b>Hooray!</b></br>Your warranty is registered successfully.</p><img style="width: 60%;height: auto;" src=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/subscription_dog_1.svg></br></br>Get one for your other device too!</p></center></body></html>

show_download_popup=false
later_download=false

#Cancel subscription URL/ Stop service URL
cancel_sub_url={'US':'https://support.mywaggle.com/stop-service-private','AU':'https://support.mywaggle.com/stop-service','CA':'https://support.mywaggle.com/stop-service','GB':'NA','IT':'NA','DE':'NA'}

mixpanel_token=d4e6dbe9742c13f01aa5f6c073190247

petnews_pagecount=10

#Eseye SIM Web-Portal Login Credentials
eseye_username=<EMAIL>
eseye_password=cSramasub1@c
eseye_portfolioid=2d5fd9fa0af4e0de54f369a19e29fd88
eseye_tariffid=25875
eseye_tigrillo_url=https://siam.eseye.com/Japi/Tigrillo

ip_locator_domain=http://ip-api.com/json/
edit_country=false

alert_msg={'US':'Upgrade to Wag plan to unlock all alerts','AU':'Upgrade to Dingo plan to unlock all alerts','CA':'Upgrade to Wag plan to unlock all alerts','GB':'Upgrade to Wag plan to unlock all alerts','IT':'Upgrade to Wag plan to unlock all alerts','DE':'Upgrade to Wag plan to unlock all alerts'}

delete_img_url_dark=https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/delete_account_black_1.png

#Showing Unlimited if txn limit > 1000
ul_check_limit=1000

#cancel redirect true= inapp redirect / false=website redirect
cancel_inapp_redirect=true

#set default temperature range offset to set min max in alert config
default_temp_range_offset=3

#Return Login username
return_username=<EMAIL>
#Upgrade Login username
upgrade_username=<EMAIL>

#Backing key time - Niom
backingkey.time.diff.niom=10

#Refund minus amount in cents (Amount * 100 = cents)
minus_refund_amount=1000

#Refund on prorated
refund_by_prorated=true

nimbleservices.url=https://dev-api.nimblepetapp.com/nimbleservices

#cancel Subscriprion
immediate_cancel_note=Note: If you choose to cancel immediately, the plan will be refunded on a pro-rated basis.
upcomingrenewal_cancel_note=Note: Your plan will be canceled at the end of this billing cycle.

#sending email from
from_email=<EMAIL>
alert_email=<EMAIL>

#new cancel flow
stay_with_us_content=<html><head></head><body><h1>Here's what you'll lose</h1><h3>you have saved</h3></body></html>
stay_with_us_img=https://nimbleapi-images.s3-us-west-2.amazonaws.com/mobileappimages/Othersorder.png alert_email=<EMAIL>
addition_benefits_cancel_reward_title=Waggle Merch 20% Off
price_too_high_success_content_title=Your request has been processed successfully.
price_too_high_success_content_body=New pricing effective from next renewal.

#WaggleMerch coupon percent
cancelCouponPercent=20
cancel_reward_url=https://www.wagglemerch.com
#subsription_period_id for waggle merch exp time
waggle_merch_coupon_exp_time=2

show_cancel_basedon_user_cancel_feedback=true

switch_plan_note_content_within_7_days=Your plan will be changed, and a refund will be processed to your account within next 2-3 business days.
switch_plan_note_content_after_7_days=Your plan will be updated & credited will be added to your wallet.

#remove gateway content
show_remove_gateway_greater_than_device_cnt=0
remove_gateway_valid_hours_to_show=48
remove_gateway_upgrade_msg=Your device will be automatically removed while upgrading monitor
remove_gateway_title_msg=Your request has been processed successfully.
remove_gateway_common_msg=Our support team will contact you within 24Hrs
remove_gateway_confirmation_title=Are you Sure?
remove_gateway_without_subscription_content=Your $$ will be permanently removed.
remove_gateway_confirmation_body_without_subscription=Your $$ is removed.
remove_gateway_confirmation_body_with_subscription=Your $$ will be permanently removed. Please note that the subscription is still active.

#shopify order close
wpm_shopify_order_get_url=https://waggle-app.myshopify.com/admin/api/2023-01/orders.json?query=name:$$&status=any
wpm_shopify_order_post_url=https://waggle-app.myshopify.com/admin/api/2024-01/orders/$$/close.json
wpm_shopify_order_valid_min_cancel=60
wpm_shopify_username=e1d2a270ca179047f2af8b489c90a80f
wpm_shopify_password=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
wpm_shopify_order_cancel_url=https://waggle-app.myshopify.com/admin/api/2024-01/orders/$$/cancel.json
wpm_shopify_cancel_order_refund=false

#N13 content
n13_registration_content=Ensure the pet monitor is On. If it isn't, hold the reset button for 5 seconds to turn it On.
lowbatteryvalue=10

warranty_msg_v2_image=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/warranty.png
warranty_msg_v2_content=You're just one step away from completing the registration

#New OrderMap contents
warranty_success_v2_image=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warranty_success_light.png
warranty_success_v2_image_dark=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warranty_success_dark.png
warranty_success_subs_v2_image=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warranty_success_nav_to_subs_light.png
warranty_success_subs_v2_image_dark=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warranty_success_nav_to_subs_dark.png
warranty_success_sec_dev_v2_image=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warranty_success_second_device_light.png
warranty_success_sec_dev_v2_image_dark=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warranty_success_second_device_dark.png

redirect.checkout.page.image=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warrently_sub_onestepaway_light.png
redirect.checkout.page.image.dark=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warrently_sub_onestepaway_dark_1.png

others_warranty_success_v2_image=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warranty_success_othes_light.png
others_warranty_success_v2_image_dark=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warranty_success_others_dark.png
others_warranty_success_subs_v2_image=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warranty_success_nav_to_subs_others_light.png
others_warranty_success_subs_v2_image_dark=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warranty_success_nav_to_subs_others_dark.png
others_warranty_success_sec_dev_v2_image=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warranty_success_second_device_others_light.png
others_warranty_success_sec_dev_v2_image_dark=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/warranty_success_second_device_others_dark.png

#activate user success msg for pet bowl
register.product.content.pet.bowl=Registration Successful - Please check your email for login details. Please keep your bowl on charge for at least 6 hours to get the latest data.


#WaggleCam
wc_ssid_password=12345
wc.show.hd.all.freaquency=true

#maximum device to play stream
wc_maximum_device_play_count=2

#limit for creating sub user
wc_maximum_sub_user_count=3

#wagglecam listener 
wagglecamlistener.url=http://localhost:8090/wagglecamlistener
alexa.toss.url=/v1.0/toss
custom.ping.duration.minutes.alexa=3

show_lan_permission=false

sheduled_feed_remainder_pushnoti_id=56
sheduled_feed_remainder_time=5

path.prefix=wc2_
path.postfix.device=device2app
path.postfix.mobile=app2device

wowza.api.admin.dns=api.wow.wagglecamapp.com
wowza.admin.passcode=roxopadmin:6pLB4JIz4se
wowza.thumbnail.url=http://wowzapasscode@wowzaadmindns/thumbnail?application=meid&streamname=path&size=320x240&fitmode=letterbox&format=png

kcalUsername=waggle
kcalPassword=Waggle@123

#Recharge
recharge.access-token=***********************************************************************
recharge.url=https://api.rechargeapps.com
cancel_recharge=false

#Motion detection Pushnotification ID
motion_detection_push_notify_id=4

#Motion Detection S3
motion.S3.bucketname=wc.notify.assets

#AWS S3 image presigned URL valid min
s3.presigned.valid.min=3

petbowl_shop_url=https://www.wagglemerch.com/products/smart-ai-bowl

#power mode popup title text
power_mode_popup_title=Important Notes on Power save modes:

show_upgrade_popup=true
wc_continuous_playback=false

show_ai_popup=false
ai_content=Free Alerts Features Available Until End of Day on July 15!

show_nextrenewal_popup=true
days_tohandle_nextrenewal=6000
show_markettingbanner=true
automate_warranty_push_notification=75
minicam_max_count=4

video_alert_pushnotification_id=72
image_alert_pushnotification_id=4

show_inapp_markettingpopup=false
inapp_gif_url=https://nimbleapi-images.s3.us-west-2.amazonaws.com/others/shopnow/shop_now_wagglecampro.gif
inapp_navigation_url=https://mywaggle.com/products/wagglecam?variant=45260378603761&utm_source=gif&utm_medium=inapp&utm_campaign=WaggleCam+Pro
event_id=pet_parent
#for waggle cam web rtc
web_rtc_url=wss://658cf041de093.streamlock.net/webrtc-session.json
wowza.playback.hash.timing=60

minicam_plan_content=Give your pet the care they deserve with our exclusive Woof plan
minicam_btn_content=Let's Woof-ty!
wcpro_plan_content=Give your pet the care they deserve with our exclusive Paw plan
wcpro_btn_content=Let's Paw-ty!
wcultra_plan_content=Please activate your plan today to start $~live streaming and monitoring.$
wcultra_btn_content=Let's Bark-ty!

textalert_infocontent=By providing your contact information, you agree to receive communications from Waggle, including emails/text/WhatsApp.

#wowza video recording and S3 credencials
wowza_video_record_passcode=prod_record_tos3:mchYo1gE6V@3
video_recording_s3_bucketname=wc.storevid.record.assets
video_recording_s3_access_key=********************
video_recording_s3_secret_key=1bta1lmuipvsDHEF5D3gg+8U5UD1MlLSgqXAZcGz
enable_video_recording_wowza_put_m=https://passcode@wowza_dns/v2/servers/Wowza%20Streaming%20Engine/vhosts/_defaultVHost_/applications/meid/adv
enable_video_recording_wowza_put_m_body={\"restURI\":\"https://wowza_dns/v2/servers/Wowza%20Streaming%20Engine/vhosts/_defaultVHost_/applications/meid/adv\",\"modules\":[{\"order\":0,\"name\":\"base\",\"description\":\"Base\",\"class\":\"com.wowza.wms.module.ModuleCore\"},{\"order\":1,\"name\":\"logging\",\"description\":\"ClientLogging\",\"class\":\"com.wowza.wms.module.ModuleClientLogging\"},{\"order\":2,\"name\":\"flvplayback\",\"description\":\"FLVPlayback\",\"class\":\"com.wowza.wms.module.ModuleFLVPlayback\"},{\"order\":3,\"name\":\"ModuleCoreSecurity\",\"description\":\"CoreSecurityModuleforApplications\",\"class\":\"com.wowza.wms.security.ModuleCoreSecurity\"},{\"order\":4,\"name\":\"ModuleS3Upload\",\"description\":\"UploadsrecordingstoAmazonS3\",\"class\":\"com.wowza.wms.plugin.s3upload.ModuleS3Upload\"}],\"advancedSettings\":[{\"enabled\":true,\"canRemove\":false,\"name\":\"s3UploadAwsProfilePath\",\"value\":\"/usr/local/WowzaStreamingEngine/.aws/credentials\",\"defaultValue\":null,\"type\":\"String\",\"sectionName\":\"Application\",\"section\":\"/Root/Application\",\"documented\":false},{\"enabled\":true,\"canRemove\":false,\"name\":\"s3UploadAwsProfile\",\"value\":\"default\",\"defaultValue\":null,\"type\":\"String\",\"sectionName\":\"Application\",\"section\":\"/Root/Application\",\"documented\":false},{\"enabled\":true,\"canRemove\":false,\"name\":\"s3UploadFilePrefix\",\"value\":\"meid\",\"defaultValue\":null,\"type\":\"String\",\"sectionName\":\"Application\",\"section\":\"/Root/Application\",\"documented\":false},{\"enabled\":true,\"canRemove\":false,\"name\":\"s3UploadDeleteOriginalFiles\",\"value\":\"true\",\"defaultValue\":null,\"type\":\"String\",\"sectionName\":\"Application\",\"section\":\"/Root/Application\",\"documented\":false},{\"enabled\":true,\"canRemove\":false,\"name\":\"s3UploadBucketName\",\"value\":\"video_recording_s3_bucketname\",\"defaultValue\":null,\"type\":\"String\",\"sectionName\":\"Application\",\"section\":\"/Root/Application\",\"documented\":false}]}
start_video_recording_wowza_post_m=https://passcode@wowza_dns/v2/servers/Wowza%20Streaming%20Engine/vhosts/_defaultVHost_/applications/meid/instances/_definst_/streamrecorders
start_video_recording_wowza_post_m_body={\n  \"instanceName\": \"_definst_\",\n  \"fileVersionDelegateName\": \"\",\n  \"serverName\": \"_defaultServer_\",\n  \"recorderName\": \"pathprefix\",\n  \"currentSize\": 0,\n  \"segmentSchedule\": \"\",\n  \"startOnKeyFrame\": true,\n  \"outputPath\": \"\",\n  \"currentFile\": \"\",\n  \"recordData\": false,\n  \"applicationName\": \"\",\n  \"moveFirstVideoFrameToZero\": false,\n  \"recorderErrorString\": \"\",\n  \"segmentSize\": 0,\n  \"defaultRecorder\": false,\n  \"splitOnTcDiscontinuity\": false,\n  \"version\": \"\",\n  \"segmentDuration\": 0,\n  \"recordingStartTime\": \"\",\n  \"fileTemplate\": \"mp4\",\n  \"backBufferTime\": 0,\n  \"segmentationType\": \"\",\n  \"currentDuration\": 0,\n  \"fileFormat\": \"\",\n  \"recorderState\": \"\",\n  \"option\": \"\"\n}
stop_video_recording_wowza_put_m=https://passcode@wowza_dns/v2/servers/Wowza%20Streaming%20Engine/vhosts/_defaultVHost_/applications/meid/instances/_definst_/streamrecorders/device2app/actions/stopRecording

show_abandoned_cart=true
referral_title=Help your friend's pet too!

wc_ultra_msg=To ensure a smooth pairing process, please charge the device $~continuously for at least two hours$ beforehand.


sheduled_feed_remainder_id=76

sheduled_feed_after_id=77

sheduled_remainder_id=78

sheduled_feed_kcalval_id=79

reportemail=<EMAIL>

hangup_battery_percentage=20

#cancel subscription contents
title_content_mini_cam=Get a free RV Cam AI Mini!
device_prize_mini_cam=Worth- $79
image_url_mini_cam=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/wagglecam_mini_free.png
offer_prize_mini_cam=10% off $$ your pet monitor plan
offer_description_mini_cam=Stick around and enjoy
popup_title_mini_cam=Get your \nfree RV Cam AI Mini!
popup_description_mini_cam=Please email your shipping details to $$ and we'll send your free RV Cam AI Mini in 3-5 biz days.
cancellation_note_content_mini_cam=Complete payment to unlock your free Waggle RV Mini Cam!

title_content_wc_pro=Get a free WaggleCam Pro
device_prize_wc_pro=Worth- $249
image_url_wc_pro=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/wagglecam_pro_free.png
offer_prize_wc_pro=10% off $$ your pet monitor plan
offer_description_wc_pro=Stick around and enjoy
popup_title_wc_pro=Get your \nfree WaggleCam Pro
popup_description_wc_pro=Please email your shipping details to $$ and we'll send your free WaggleCam Pro in 3-5 biz days.
cancellation_note_content_wc_pro=Complete payment to unlock your free WaggleCam Pro!

apply_coupon_cancel_sub=true

show_ask_feature=true

returnsubuser=CKylAteN
returnsubpass=ErShiCkleg

askfeatureto_address=<EMAIL>,<EMAIL>
askfeaturecc_address=<EMAIL>,<EMAIL>
orderdate_trial=08-10-2024:31-10-2024
offer_days=90
#bundled subscription contact us content
bundle_contact_us_content=It might be your existing plan \nor multiple monitors.
#Amazon bundle end date
amazonBundleEndDate=2024-12-31 23:59:59
freeplanactivation_contentmc=Free plan activated!
paidplanactivation_contentmc=Your paid plan is activated!
trialplanactivation_contentmc=Your trail plan is activated!\nExplore premium features for 30 days!

amazonredirectsburl=https://www.amazon.com/product-reviews/B07SGCYMGN/ref=acr_dp_hist_5?ie=UTF8&filterByStar=five_star&reviewerType=all_reviews#reviews-filter-bar
amazonredirectwcurl=https://www.amazon.com/product-reviews/B07SGCYMGN/ref=acr_dp_hist_5?ie=UTF8&filterByStar=five_star&reviewerType=all_reviews#reviews-filter-bar
amazonredirectmcurl=https://www.amazon.com/Waggle-RV-Mini-Camera-Control/dp/B0D5VLMHPX/ref=sr_1_1?crid=2GHM2OFHA1O8U&dib=eyJ2IjoiMSJ9.KkL6iEBApTZzuFD-gc4TJFVVWrNJ5PKjaUi_Yh_y_-t-yurij4ufuWtjK0H1eh4TR3uZLzAPhKvE7hVLWvFHSLYy4GC3CdPdMHmCJhiXgFsEsAKA8shjKYmukwWyS435BxcwiktA4GXLCrBlqUeD96VyZinv3CYJsPGSM-Rj6Zg2p0qOi2GcoLt3XAiZsw8DvRlOo-8McvYzUudRqwrWZw.-lcE1e_VVkFCKTG9h7KClvYAvCngngNaRJTt8G0mBHM&dib_tag=se&keywords=Waggle%2BRV%2Bmini%2Bcam&qid=1730802724&sprefix=waggle%2Brv%2Bmini%2Bcam%2Caps%2C446&sr=8-1&th=1
amazonredirectwcpurl=https://www.amazon.com/Waggle-Dispenser-Monitoring-Smartphone-Two-Way/dp/B0D7HX4NMF/ref=sr_1_5?crid=22SMHMDB6D1K2&dib=eyJ2IjoiMSJ9.CPURCOmMOmgTZ8Xa_XpbadgSGcQG7OTeBbG6dgqF1CpWrB7dIBzLCoURv6JSxLaGIf3IkGMRY1OsmcXf5RiFVAKq0TW4WLFIA3DIhDNVaV4.prDs-2fxN8v3HWyFI9rUYEQo5bV0WU-BtDIec3p9peA&dib_tag=se&keywords=Waggle+Cam+pro&qid=1730802768&sprefix=waggle+cam+pro%2Caps%2C383&sr=8-5
amazonredirectnsurl=https://www.amazon.com/product-reviews/B07SGCYMGN/ref=acr_dp_hist_5?ie=UTF8&filterByStar=five_star&reviewerType=all_reviews#reviews-filter-bar
#Weather API credentials

weather_url=https://api.openweathermap.org/data/2.5/weather?
app_id=********************************
weather_group_ids={'2':1,'3':2,'5':3,'6':4,'7':5,'8':6}

resume_notify=93
show_chat=true

ev3_devices={'N13-503-M NT3K','N13-502-M NT3K','N13-502-M NT3KG','N13G-503-M NT3K','N13G-502-M NT3K','N13-503-M NT3KA'}

priorityemail=<EMAIL>
priorityphone=+1(845)403-0030

pause_enable=true

show_vetchat=true
vetchat_chosen_main_title=Congratulations!
vetchat_chosen_description=Thanks for being an amazing Waggler! Claim your exclusive offer before it's gone!
vetchat_start_main_title=Hooray!
vetchat_start_description=Start your chat and consult within 24 hours
vetchat_noneligible_free=Thank you for joining the waitlist! We're thrilled to bring Vet Chat to you\u2014stay tuned for updates!
vetchat_noneligible_paid=You're on the Emergency Fund waitlist! We're excited to bring this feature to you soon\u2014stay tuned!

vetchat_homepage_title=Claim
vetchat_homepage_description=Emergency Fund
vetchat_homepage_price=$750

vetchat_terms_and_conditions={'Vet Chat is a complimentary feature included in your plan for general advice only.', 'Waggle is not responsible for any issues, outcomes, or decisions resulting from Vet Chat use.', 'Always use prescribed medications provided by a licensed veterinarian.', 'This service is for consultation only and not a medical diagnosis or prescription. Please visit a vet if advised.', 'Waggle may modify or discontinue Vet Chat at any time without prior notice.'}

nottravel_pm_content=Renew now & get 1 year of $$FREE$$ Vet consulting
nottravel_pm_title_content=Wait! don't leave your pet unprotected!
nottravel_pm_title_content_note=Get $750 Emergency Fund & Unlimited VetChat FREE
show_alertlimit=true

renewal_content_note=Your plan will resume on \nthe selected date

//flexi_plan_details={'6-month plan with 2 pauses','Each activation lasts 30 days','Flexible 3 months of usage','Tailored for seasonal adventurers'}
flexi_plan_details={'Ideal for Occasional RVers.','Pause and Resume as needed.','Each activation lasts 30 days*.','Upto 3 activations*.'}
flexi_plan_activate_content_1=Your Flexiplan is ready! Activate it anytime by tapping 'Start Now.' Until then, your pet monitor updates will remain paused.
flexi_plan_activate_content_2=Your Flexiplan is paused! Reactivate it anytime by tapping 'Resume Now.' Until then, your pet monitor updates will remain paused.
flexi_plan_start_content=You can activate your plan anytime with 'Start Now.' Once activated, pausing won't be available for the next 30 days.
flexi_plan_pause_content=Need a break?

reset_minits=5
txnservice_url=http://dev-api.nimblepetapp.com/wgtxnsvc

show_smart_light_setting=true

solarcam_plan_content=Your trail plan has ended ! Activate the spot plan to keep streaming
solarcam_btn_content=Activate Now

referral_popup_image_url=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/icon/referal_earn.png
referral_popup_content1=Amazing pet parents stick together!
referral_popup_content2=Refer a friend,
referral_popup_content3=Earn $50
referral_popup_content4=It's that simple!
referral_popup_cta1=Refer Now
referral_popup_cta2=Not Interested

show_instant_help=true
vetchat_displayname=VetChat

solarminicam_plan_content= Your Spot Mini Plan has ended! Reactivate now to keep streaming.


debug_otp=2704

compare_image_other_products=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/combo_compare_img2.png


vetchat_free_activation_days=90
shop_link=https://mywaggle.com/pages/store?utm_source=Waggle_App&utm_medium=inapp&utm_campaign=Store

fota_update_file_url=https://s3-us-west-2.amazonaws.com/fota.nimblewireless.com/

swagger.enable=true

remove_gateway_confirmation_note=Removing the device will also end your subscription.

# Hologram SIM activation API details
hologram_service_url=https://dashboard.hologram.io/api/1/devices/
hologram_api_password=95O09pxICBHgT9ziNgR9zIrZb7gcWc
hologram_org_id=91587

remove_gateway_confirmation_note=Removing the device will also end your subscription.

#waggle hooks URL
wagglehooks_url=http://dev-api.nimblepetapp.com/wagglehooks/

amazon_order_id_buy_link=https://www.amazon.in/
amazon_order_history=https://www.amazon.com/gp/your-account/order-history
hide_warranty_popup=false
free_minicam_startdate=2025-05-14 00:00:00

n12_5_fotacommand=FOTAUPG=UPGRADE$N12.5.E6.L1.26$1280
coupon_img_new=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/new_activate_coupon_1.png
coupon_desc_new=Your pet's journey starts here \n - don't forget to use:
coupon_code_new=WELCOME05
coupon_img_update=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/reactivate_coupon_1.png
coupon_desc_update=Use code at checkout
coupon_code_update=WELCOME30
coupon_img_subs=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/coupon_img.png
coupon_desc_subs=Activate Now & Get 10% OFF Your Plan!
coupon_code_subs=THANKYOU
coupon_img_order=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/coupon_banner_img.png
isN12_5_fotacommandUpdateNeeded=false

coupon_img_upgrade=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/reactivate_coupon_1.png
coupon_desc_upgrade=Use code at checkout
coupon_code_upgrade=UPGRADE3

start_saving_img_subs=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/wge_start_saving.png
grab_my_offer_img_subs=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/wge_grab_my_offer.png

isFreeDeviceShippingAvailable=true

show_pet_monitor_location=false

combo_compare_4g_Img=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/combo_compare_4g_img.png

#Secret Manager Cache Timeout
aws_sm_cache_expire_minutes = 10

#Secret Names Of Secret Manager
aws_s3_secret_name=prod/svc/s3svc
aws_sqs_secret_name=prod/svc/sqssvc
aws_ses_secret_name=/prod/svc/awsses
jwt_key_secret_name=prod/jwt/key

appVerFromConfig=8.0.7

flexi_plan_activation_count=Each activation lasts 30 days*.

plan_purchase_site_url=https://dev-api.nimblepetapp.com/subs_outside/index.html?token=

offer_success_content_title=We have received your request!
offer_success_content_body=Our support team will get reach you within shortly via call or email.

enable_call_support=false
redirectWebSubs=true

#For get product subscription plan
vet_chat_img_url=https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/logo/vetchat.png

vet_chat.cancel.title=Here's everything "Pet's Name" loses.
vet_chat.cancel.content=24/7 Vet Access - Instant help anytime|Free Follow-Ups - No extra cost for same issue|$750 Emergency Fund - For life-threatening emergencies|Still want to cancel?


ble_fota_debug_s3_path_prefix=ble_fota_debug/
ble_fota_debug_s3_bucket_name=fota.nimblewireless.com