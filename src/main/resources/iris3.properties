ip=http://*************:8080
rv_android_V=4.1.0
rv_android_force=1
rv_android_force_V=7.0.38
rv_ios_V=6.1.8
rv_ios_force=1
rv_ios_force_V=7.0.39
iris3_android_V=3.0.20
iris3_android_force=0
iris3_android_force_V=3.0.20
iris3_ios_V=5.1.0
iris3_ios_force=1
iris3_ios_force_V=5.1.4
iris3_wl_android_V=3.0.24
iris3_wl_android_force=1
iris3_wl_android_force_V=3.0.24
iris3_wl_ios_V=5.1.5
iris3_wl_ios_force=1
iris3_wl_ios_force_V=5.1.5
webservice_V=3.2.38
listener_V=3.1.40
webapp_V=3.1.35
database_V=3.2.37
rv_hotline_AppId=abd2b01a-fb6e-451a-81b8-85a497346bd4
rv_hotline_AppKey=871c161a-3b4f-4557-9134-815982413710
rm_android_V=1.1.2
rm_android_force=0
rm_android_force_V=1.1.5
rm_ios_V=1.1.2
rm_ios_force=1
rm_ios_force_V=1.1.2
rm_bg_ios_V=1.0.2
rm_bg_ios_force=1.0.2
rm_bg_android_V=1.0.1
rm_bg_android_force=1.0.1
rv_petprofile_force=1
s3bucketname_iris=iris3.nimblewireless.com
s3_key=********************
s3_secret=4GidEgA09FO97x7EvIOEbFoRJ/DNkEdtWiAUY+LC
total_unlimited=false

#Get Subscritpion URL 
#Note : Exclude "/" at the end  of the URL
#niomGetSubscriptionURL=http://localhost:8080/niomservices/v1.0/getdevicesubscription

#Get Inventory URL
#Note : Include "/" at the end  of the URL
#niomInventoryURL=http://localhost:8080/niomservices/v1.0/inventory/ded40f28c9f220b82ec8233615a8e4c966d296ea/

#Get Ismeidmapped URL
#Note : Include "/" at the end  of the URL
#niomCheckMappedOrderURL=http://localhost:8080/niomservices/v1.0/ismeidmapped/ded40f28c9f220b82ec8233615a8e4c966d296ea/

#Activate ESEYE SIM thru NIOM URL
#Note : Exclude "/" at the end  of the URL
#niomActivateEseyeSimURL=http://localhost:8080/niomservices/v1.0/inventory/ded40f28c9f220b82ec8233615a8e4c966d296ea/activate/eseye


#Post offline order to OrderMap table  URL
#Note : Exclude "/" at the end  of the URL
niomPostOrderMappedURL=http://localhost:8080/niomservices/v1.0/offlineordermap/ded40f28c9f220b82ec8233615a8e4c966d296ea


activateSim=false
rvpetPasswordType=1
resMonitoringPasswordType=3
whiteLabelPasswordType=3

#Model ID Configuration
isManualModelIdConfiguration=false
rvmodelID=6
nimblemodelID=6

#Email Configuration
to_address=<EMAIL>
cc_address=<EMAIL>,<EMAIL>
bcc_address=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

#Subscritpion URL
rvPetSubsURL=https://rvpetsafety.com/subscriptions/
nimbleSubsURL=https://rvpetsafety.com/subscriptions/
whiteLabel=https://rvpetsafety.com/subscriptions/

#Google API Photo API URL
googlePhotoApiURL=https://maps.googleapis.com/maps/api/place/photo?photoreference=PHOTO_REFERENCE&sensor=false&maxheight=100&maxwidth=100&key=AIzaSyCtcw9q_Bo1XlHGecRcaJtRy0UUFpLiBCs

#Google Search API URL's

googlenearbysearch_doggrooming=https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=LATLON&radius=50000&type=petservice&keyword=doggrooming&key=AIzaSyCtcw9q_Bo1XlHGecRcaJtRy0UUFpLiBCs

googlenearbysearch_dogveterinarycare=https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=LATLON&radius=25000&type=petservice&keyword=vet&key=AIzaSyCtcw9q_Bo1XlHGecRcaJtRy0UUFpLiBCs

googlenearbysearch_dogtraining=https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=LATLON&radius=50000&type=petservice&keyword=dogboarding&key=AIzaSyCtcw9q_Bo1XlHGecRcaJtRy0UUFpLiBCs

googlenearbysearch_dogboarding=https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=LATLON&radius=50000&type=petservice&keyword=Dogadoption&key=AIzaSyCtcw9q_Bo1XlHGecRcaJtRy0UUFpLiBCs

googlenearbysearch_dogadoption=https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=LATLON&radius=50000&type=petservice&keyword=dogtraining&key=AIzaSyCtcw9q_Bo1XlHGecRcaJtRy0UUFpLiBCs

welcomeavatarsub=Welcome to Waggle Avatar
successavatarsub=Avatar success mail
ccemail_avatar=<EMAIL>
bccemail_avatar=<EMAIL>
alert_bcc=<EMAIL>
