<!DOCTYPE html>
<html lang="en">
<head>
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=no">
<title>Freshbots Chat</title>
<style>
html, body {
    height: 100%;
}
html {
    display: table;
    margin: auto;
}
body {
    display: table-cell;
    vertical-align: middle;
}
.blink{
	width:200px;
	height: 50px;
	padding: 15px;	
	text-align: center;
	line-height: 50px;
	animation: blink 1s linear infinite;
	
}

@keyframes blink{
0%{opacity: 0;}
50%{opacity: .5;}
100%{opacity: 1;}
}
</style>
 
 <body>
 				
<div id="loader" class="blink"><span style="font-size: 25px;color: black;">Hang in there....</span></div>
<div id="errormsg" ><span style="font-size: 25px;color: black;">Sorry, We are not available at this moment . Questions? Reach us at

<EMAIL> or ************.  </span></div>
</body>

<script type="text/javascript">
  var diverror = document.getElementById('errormsg');
diverror.style.visibility = 'hidden';
  var div = document.getElementById('loader');
div.style.visibility = 'visible';


  (function (d, w, c) {
	if (!d.getElementById("spd-busns-spt")) {
		var n = d.getElementsByTagName('script')[0],
		s = d.createElement('script');
		var loaded = false;
		s.id = "spd-busns-spt";
		s.async = "async";
		s.setAttribute("data-self-init", "false");
		s.setAttribute("data-init-type", "opt");
		s.src = 'https://cdn.freshbots.ai/assets/share/js/freshbots.min.js';
		s.setAttribute("data-client", "df3f310c6d5520cac4c4a411e5f376291030f866");
		s.setAttribute("data-bot-hash", "2eef08d011f445c72b4fde8be5a122f2e7896243");
		s.setAttribute("data-env", "prod");
		s.setAttribute("data-region", "us");
		if (c) {
			s.onreadystatechange = s.onload = function () {
				if (!loaded) {
					c();
				}
				loaded = true;
			};
		}
		n.parentNode.insertBefore(s, n);
	}
})(document, window, function () {
    Freshbots.initiateWidget({
      autoInitChat: true,
      getClientParams: function () {
        return {
				"sn::cstmr::id": "${UNAME}",
				"cstmr::eml": "${EMAIL}",
				"cstmr::phn": "${PHONENO}",
				"cstmr::nm": "${FNAME}"
			};
      }
    }, function (successResponse) {
      console.log("Yay!!");
      Freshbots.showWidget(true);
var div = document.getElementById('loader');
div.style.visibility = 'hidden';
    }, function (errorResponse) {
      console.log("Alas!!");
var div = document.getElementById('loader');
     div.style.visibility = 'hidden';
	  var diverror = document.getElementById('errormsg');
	  diverror.style.visibility = 'visible';
    });
  });

</script>

</head>
</html>
