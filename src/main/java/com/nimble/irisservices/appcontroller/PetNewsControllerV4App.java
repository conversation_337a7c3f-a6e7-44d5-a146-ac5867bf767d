package com.nimble.irisservices.appcontroller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JPetNews;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.PetNews;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IPetNewsServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class PetNewsControllerV4App {

	private static final Logger log = LogManager.getLogger(PetNewsControllerV4App.class);

	@Autowired
	IPetNewsServiceV4 pnService;

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";

	@Autowired
	IUserServiceV4 userServiceV4;

	@Value("${petnews_pagecount}")
	private int petnews_pagecount;

	@Value("#{${buynowpetsafety}}")
	private Map<String,String> buynowpetsafety;
	
	@RequestMapping(value = "v5.0/getAppPetNews", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAppPetNewsV5(@RequestParam("type") String type, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("is_device") boolean is_device, @RequestParam("is_subscription") boolean is_subscription,
			@RequestParam("id") long id) {

		log.info("Entered getAppPetNews");
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				if (!validation_authkey.equals(auth))
					user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			PetNews pn = pnService.getPetNews(id);
			String short_news = "";
			String long_news = "";
			String imgpath = "";
			String dm_short_news = "";
			String dm_long_news = "";
			boolean show_CTA = false;
			Map cta_map = new HashMap();

			if (pn != null) {
				if (type.equalsIgnoreCase("flutter")) {
					imgpath = pn.getIphone_imgurl();
					short_news = pn.getF_snews();
					long_news = pn.getF_lnews();
					dm_short_news = pn.getDmf_snews();
					dm_long_news = pn.getDmf_lnews();
				} else if (type.equalsIgnoreCase("iphone")) {
					imgpath = pn.getIphone_imgurl();
					short_news = pn.getIphone_shortnews();
					long_news = pn.getIphone_longnews();
					dm_short_news = "NA";
					dm_long_news = "NA";
				} else if (type.equalsIgnoreCase("ipad")) {
					imgpath = pn.getIpad_imgurl();
					short_news = pn.getIpad_shortnews();
					long_news = pn.getIpad_longnews();
					dm_short_news = "NA";
					dm_long_news = "NA";
				} else {
					imgpath = pn.getAndroid_imgurl();
					short_news = pn.getAndroid_shortnews();
					long_news = pn.getAndroid_longnews();
					dm_short_news = "NA";
					dm_long_news = "NA";
				}

				if (pn.isShow_CTA()) {
					show_CTA = pn.isShow_CTA();

					if (is_device && is_subscription) {
						cta_map.put("CTA_Action", "Refer&Earn");
						cta_map.put("CTA_Title", "Refer Your Friends");
					} else if (is_device && !is_subscription) {
						cta_map.put("CTA_Action", "Upgrade");
						cta_map.put("CTA_Title", "Unlock Pet Protection");
					} else if (!is_device && !is_subscription) {
						cta_map.put("CTA_Action", "BuyNow");
						cta_map.put("CTA_Title", "Get Peace of Mind");
					} else {
						show_CTA = false;
						cta_map.put("CTA_Action", "NA");
						cta_map.put("CTA_Title", "NA");
					}
				} else {
					show_CTA = pn.isShow_CTA();
					cta_map.put("CTA_Action", "NA");
					cta_map.put("CTA_Title", "NA");
				}

				JPetNews jpn = new JPetNews(id, short_news, long_news, imgpath, dm_short_news, dm_long_news, false,
						cta_map);
				
				jpn.setShow_CTA(show_CTA);
				
				String country = user.getCountry().toUpperCase();
				if(country == null || country.isEmpty() || country.equalsIgnoreCase("US")|| country.equalsIgnoreCase("NA")
						|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")
						) {
					country = "US";
				}
				
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("petnews", jpn);
				response.put("buy_now_url", buynowpetsafety.get(country));
			} else {
				response.put("Status", 0);
				response.put("Msg", "Please try after sometime!");
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Unable to retrive PetNews");
			log.error("Error in getPetNews : " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}
	
	@RequestMapping(value = "v5.0/listAppPetNews", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listAppPetNewsV5(@RequestParam("type") String type, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestParam("index") int index,
			@RequestParam("is_device") boolean is_device, @RequestParam("is_subscription") boolean is_subscription,
			@RequestHeader HttpHeaders header, Authentication authentication) {

		log.info("Entered listAppPetNews");
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				if (!validation_authkey.equals(auth))
					user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			int pnTotcount = pnService.getPetNewsCount();

			ArrayList<JPetNews> pnList = pnService.listAppPetNews(type, index, petnews_pagecount, is_device,
					is_subscription);
			int lastRecordCnt = index + pnList.size();

			String country = user.getCountry().toUpperCase();
			if(country == null || country.isEmpty() || country.equalsIgnoreCase("US")|| country.equalsIgnoreCase("NA")
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")
					) {
				country = "US";
			}
			
			response.put("Status", 1);
			response.put("Msg", "success");
			response.put("pnList", pnList);
			response.put("pnIndex", lastRecordCnt);
			response.put("pnPagecount", petnews_pagecount);
			response.put("pnTotcount", pnTotcount);
			response.put("buy_now_url", buynowpetsafety.get(country));

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "failure");
			log.error("Error in listAppPetNews : " + e.getLocalizedMessage());
		}

		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/listAppPetNews", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listAppPetNews(@RequestParam("type") String type, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestParam("index") int index,
			@RequestParam("is_device") boolean is_device, @RequestParam("is_subscription") boolean is_subscription,
			@RequestHeader HttpHeaders header, Authentication authentication) {

		log.info("Entered listAppPetNews");
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				if (!validation_authkey.equals(auth))
					user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			int pnTotcount = pnService.getPetNewsCount();

			ArrayList<JPetNews> pnList = pnService.listAppPetNews(type, index, petnews_pagecount, is_device,
					is_subscription);
			int lastRecordCnt = index + pnList.size();

			String country = user.getCountry().toUpperCase();
			if(country == null || country.isEmpty() || country.equalsIgnoreCase("US")|| country.equalsIgnoreCase("NA")
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")
					) {
				country = "US";
			}
			
			response.put("Status", 1);
			response.put("Msg", "success");
			response.put("pnList", pnList);
			response.put("pnIndex", lastRecordCnt);
			response.put("pnPagecount", petnews_pagecount);
			response.put("pnTotcount", pnTotcount);
			response.put("buy_now_url", buynowpetsafety.get(country));

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "failure");
			log.error("Error in listAppPetNews : " + e.getLocalizedMessage());
		}

		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/getAppPetNews", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAppPetNews(@RequestParam("type") String type, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam("is_device") boolean is_device, @RequestParam("is_subscription") boolean is_subscription,
			@RequestParam("id") long id) {

		log.info("Entered getAppPetNews");
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				if (!validation_authkey.equals(auth))
					user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			PetNews pn = pnService.getPetNews(id);
			String short_news = "";
			String long_news = "";
			String imgpath = "";
			String dm_short_news = "";
			String dm_long_news = "";
			boolean show_CTA = false;
			Map cta_map = new HashMap();

			if (pn != null) {
				if (type.equalsIgnoreCase("flutter")) {
					imgpath = pn.getIphone_imgurl();
					short_news = pn.getF_snews();
					long_news = pn.getF_lnews();
					dm_short_news = pn.getDmf_snews();
					dm_long_news = pn.getDmf_lnews();
				} else if (type.equalsIgnoreCase("iphone")) {
					imgpath = pn.getIphone_imgurl();
					short_news = pn.getIphone_shortnews();
					long_news = pn.getIphone_longnews();
					dm_short_news = "NA";
					dm_long_news = "NA";
				} else if (type.equalsIgnoreCase("ipad")) {
					imgpath = pn.getIpad_imgurl();
					short_news = pn.getIpad_shortnews();
					long_news = pn.getIpad_longnews();
					dm_short_news = "NA";
					dm_long_news = "NA";
				} else {
					imgpath = pn.getAndroid_imgurl();
					short_news = pn.getAndroid_shortnews();
					long_news = pn.getAndroid_longnews();
					dm_short_news = "NA";
					dm_long_news = "NA";
				}

				if (pn.isShow_CTA()) {
					show_CTA = pn.isShow_CTA();

					if (is_device && is_subscription) {
						cta_map.put("CTA_Action", "Refer&Earn");
						cta_map.put("CTA_Title", "Refer Your Friends");
					} else if (is_device && !is_subscription) {
						cta_map.put("CTA_Action", "Upgrade");
						cta_map.put("CTA_Title", "Unlock Pet Protection");
					} else if (!is_device && !is_subscription) {
						cta_map.put("CTA_Action", "BuyNow");
						cta_map.put("CTA_Title", "Get Peace of Mind");
					} else {
						show_CTA = false;
						cta_map.put("CTA_Action", "NA");
						cta_map.put("CTA_Title", "NA");
					}
				} else {
					show_CTA = pn.isShow_CTA();
					cta_map.put("CTA_Action", "NA");
					cta_map.put("CTA_Title", "NA");
				}

				JPetNews jpn = new JPetNews(id, short_news, long_news, imgpath, dm_short_news, dm_long_news, false,
						cta_map);
				
				jpn.setShow_CTA(show_CTA);
				
				String country = user.getCountry().toUpperCase();
				if(country == null || country.isEmpty() || country.equalsIgnoreCase("US")|| country.equalsIgnoreCase("NA")
						|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")
						) {
					country = "US";
				}
				
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("petnews", jpn);
				response.put("buy_now_url", buynowpetsafety.get(country));
			} else {
				response.put("Status", 0);
				response.put("Msg", "Please try after sometime!");
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Unable to retrive PetNews");
			log.error("Error in getPetNews : " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}
}
