package com.nimble.irisservices.appcontroller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AppImage;
import com.nimble.irisservices.entity.ReferralCredits;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IReferAndEarnService;
import com.nimble.irisservices.service.IReferAndEarnServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class ReferAndEarnControllerV4App {

	private static final Logger log = LogManager.getLogger(ReferAndEarnControllerV4App.class);

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	IReferAndEarnService referAndEarnService;

	@Autowired
	IReferAndEarnServiceV4 referAndEarnServiceV4;

	@Value("${referralurl}")
	private String referralurl;
	
	@Value("${referral_title}")
	private String referral_title;

	@Value("${weblinkurl}")
	private String weblinkurl;

	@Value("${weblinkflag}")
	private String weblinkflag;

	@Value("${usereferralcandy}")
	private boolean useReferralCandy;

	@Value("${referralcandyAccessKey}")
	private String referralCandyAccessKey;

	@Value("${referralcandysecretkey}")
	private String referralCandySecretKey;

	@Autowired
	Helper helper;
	
	// kalai
	@RequestMapping(value = "v5.0/generatereferrallink/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse generateReferralLinkV5(
			@RequestParam(value = "type", defaultValue = "", required = false) String type,
			@RequestParam(value = "imgname", defaultValue = "", required = false) String imgname,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering  generateReferralLink : " + auth);
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);

				JResponse errResponse = helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("generateReferralLinkV5 :Exception while getting user for authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (user != null) {
				String ref_url = referralurl + user.getEmail();
				ReferralCredits ref = referAndEarnService.getLatestReferralCredits();

				if (useReferralCandy) {
					for (int i = 0; i < 3; i++) {
						try {
							String referralCandyResponse = helper.signUpInReferralCandy(user.getFirstname(),
									user.getLastname(), user.getEmail(), referralCandySecretKey,
									referralCandyAccessKey);
							JSONObject JObj = new JSONObject(referralCandyResponse.toString());
							String status = (String) JObj.get("message");
							String referralcorner_url = (String) JObj.get("referralcorner_url");
							ref_url = (String) JObj.get("referral_link");
							log.info("status : " + status + "\n referralcorner_url : " + referralcorner_url
									+ "\n referral_link : " + ref_url);
							break;
						} catch (Exception e) {
							log.error("Error while getting sign Up In Referral Candy : " + e.getLocalizedMessage());
						}
					}
				}
				if (ref != null) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("referallink", ref_url);
					response.put("offerone", ref.getAdvocate_msg());
					response.put("offeronedesc", ref.getAdvocate_desc());

					response.put("offertwo", ref.getReferral_msg());
					response.put("offertwodesc", ref.getReferral_desc());

					response.put("messagecontent", ref.getTitle());
					response.put("referalcode", user.getEmail());
					response.put("weblinkflag", weblinkflag);
					response.put("weblinkurl", weblinkurl);
					
					response.put("referal_title", referral_title);
					
				} else {
					response.put("Status", 0);
					response.put("Msg", "Referral Credits not found");
				}
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while generating referral link");
			response.put("Error", e.getLocalizedMessage());
			log.error("generateReferralLinkV5 : Exception : " + e.getMessage());
		}

		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// v4.0/generatereferrallink/ - SIV
	@RequestMapping(value = "v4.0/generatereferrallink/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse generateReferralLinkV4(
			@RequestParam(value = "type", defaultValue = "", required = false) String type,
			@RequestParam(value = "imgname", defaultValue = "", required = false) String imgname,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering  generateReferralLink : " + auth);
		try {
			UserV4 user = null;
			String imgpath = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/REmobile_13.png";
			String imgpathFlutterLight = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/REmobile_15.png";
			String imgpathFlutterDark = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/REmobile_bt_2.png";
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);

				JResponse errResponse = helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("generateReferralLinkV4 :Exception while getting user for authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (user != null) {
				String ref_url = referralurl + user.getEmail();
				ReferralCredits ref = referAndEarnService.getLatestReferralCredits();

				if (ref != null && !imgname.trim().isEmpty() && !type.trim().isEmpty()) {
					AppImage appimage = referAndEarnServiceV4.getAppImages(type, imgname);
					imgpath = appimage.getImg_path();
					imgpathFlutterLight = appimage.getImg_path_flutter_light();
					imgpathFlutterDark = appimage.getImg_path_flutter_dark();
				}
				if (useReferralCandy) {
					for (int i = 0; i < 3; i++) {
						try {
							String referralCandyResponse = helper.signUpInReferralCandy(user.getFirstname(),
									user.getLastname(), user.getEmail(), referralCandySecretKey,
									referralCandyAccessKey);
							JSONObject JObj = new JSONObject(referralCandyResponse.toString());
							String status = (String) JObj.get("message");
							String referralcorner_url = (String) JObj.get("referralcorner_url");
							ref_url = (String) JObj.get("referral_link");
							log.info("status : " + status + "\n referralcorner_url : " + referralcorner_url
									+ "\n referral_link : " + ref_url);
							break;
						} catch (Exception e) {
							log.error("Error while getting sign Up In Referral Candy : " + e.getLocalizedMessage());
						}
					}
				}
				if (ref != null) {
					response.put("Status", 1);
					response.put("referallink", ref_url);
					response.put("offerone", ref.getAdvocate_msg()+ref.getAdvocate_desc());
					response.put("offertwo", ref.getReferral_msg()+ref.getReferral_desc());
					response.put("messagecontent", ref.getTitle());
					response.put("referalcode", user.getEmail());
					response.put("imgpath", imgpath);
					response.put("imgpathflutterlight", imgpathFlutterLight);
					response.put("imgpathflutterdark", imgpathFlutterDark);
					response.put("weblinkflag", weblinkflag);
					response.put("weblinkurl", weblinkurl);
				} else {
					response.put("Status", 0);
					response.put("Msg", "Referral Credits not found");
				}
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while generating referral link");
			response.put("Error", e.getLocalizedMessage());
			log.error("generateReferralLinkV4 : Exception : " + e.getMessage());
		}

		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

}
