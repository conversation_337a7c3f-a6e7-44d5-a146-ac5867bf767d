package com.nimble.irisservices.appcontroller;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.irisservices.dto.HttpResponse;
import com.nimble.irisservices.dto.JCancelOrderForm;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.helper.Helper;


@RestController
public class ExternalMarketingController {

	private static final Logger log = LogManager.getLogger(ExternalMarketingController.class);
	
	@Autowired
	Helper _helper;
	
	@Value("${wpm_shopify_order_get_url}")
	private String getOrderAPI;
	
	@Value("${wpm_shopify_order_post_url}")
	private String closeOrderAPI;
	
	@Value("${wpm_shopify_order_cancel_url}")
	private String cancelOrderAPI;
	
	@Value("${wpm_shopify_order_valid_min_cancel}")
	private long cancelOrderInMinutes;
	
	@Value("${wpm_shopify_username}")
	private String wpm_shopify_username;
	
	@Value("${wpm_shopify_password}")
	private String wpm_shopify_password;
	
	@Value("${wpm_shopify_cancel_order_refund}")
	private boolean shopify_cancel_order_refund;
	
	
	@PostMapping("v4.0/cancelordershopify")
	public JResponse cancelOrderShopify(@RequestBody JCancelOrderForm cancelOrderForm) {
		log.info("Entered into cancelOrderShopify "+ cancelOrderForm.printAllValues());
		JResponse response = new JResponse();
		try {
			
			String url = getOrderAPI;
			url = url.replace("$$", cancelOrderForm.getOrder_id());
			String basicAuth =  wpm_shopify_username+":"+wpm_shopify_password;
			
			basicAuth = Base64.getEncoder().encodeToString(basicAuth.getBytes());
			
			Map<String, String> headers = new HashMap<>();
			headers.put("Authorization", "Basic "+basicAuth);
			
			HttpResponse httpResponse = httpReq("Get Orders", "GET", url, headers, null);
			
			if( httpResponse == null ) {
				log.error("Error while getting shopify API for orders");
				response.put("Status", 0);
				response.put("Msg", "failed");
				return response;
			}
			
			JSONObject jsonObject = httpResponse.getJsonObject();
			
			JSONObject ordersObj = null;
			if( !jsonObject.has("orders") ) {
				log.error("No order found in shopify get order API :: shopify response : "+ jsonObject.toString());
				response.put("Status", 0);
				response.put("Msg", "failed");
				return response;
			}
			
			ordersObj = jsonObject.getJSONArray("orders").getJSONObject(0);
			
			if( !ordersObj.has("created_at") ) {
				log.error("order created at not found in shopify get order API :: shopify response : "+ jsonObject.toString());
				response.put("Status", 0);
				response.put("Msg", "failed");
				return response;
			}
			
			long orderId = ordersObj.getLong("id");
			log.info("order id : "+ orderId);
			
			double current_total_price = 0;
			if( ordersObj.has("current_total_price") ) {
				current_total_price = ordersObj.getDouble("current_total_price");
			}
			
			String currency = "USD";
			if( ordersObj.has("currency") ) {
				currency = ordersObj.getString("currency");
			}
			
			String createdDate = ordersObj.get("created_at").toString();
			log.info("order_created_on : "+createdDate); 
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
			sdf.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
			Date orderDate = sdf.parse(createdDate);
			
			SimpleDateFormat sdfToFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			sdfToFormat.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
			String dt = sdfToFormat.format(orderDate);
			SimpleDateFormat sdfConvertDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			
			orderDate = sdfConvertDate.parse( dt );
			log.info("After converting PST to UTC :: order_created_on : "+orderDate);
			
			Date curDate = sdfConvertDate.parse( _helper.getCurrentTimeinUTC() );
			log.info("Current time : "+ curDate);
			
			long diffInMinutes = TimeUnit.MILLISECONDS.toMinutes( curDate.getTime() - orderDate.getTime() );
			log.info( "Minutes Diffrence : "+diffInMinutes );
			
			
			String cancelURL = cancelOrderAPI;
			cancelURL = cancelURL.replace("$$", String.valueOf( orderId ));
			if( cancelOrderInMinutes >= diffInMinutes ) {
				
				Map<String, Object> body = new HashMap<>();
				if( shopify_cancel_order_refund ) {
					body.put("amount", current_total_price);
					body.put("currency_code", currency);
				}
				
				headers.remove("Authorization");
				headers.put("X-Shopify-Access-Token", wpm_shopify_password);
				
				log.info("refund_amount : "+ current_total_price+" :: currency_code : "+currency);
				httpResponse = httpReq("Cancel Orders", "POST", cancelURL, headers, body);
				
				if( httpResponse == null || httpResponse.getResponse_code() != 200 ) {
					log.error("Error while calling shopify for cancel order :: response_code : "+httpResponse.getResponse_code() +" :: response : "+ httpResponse.getJsonObject().toString());
					response.put("Status", 0);
					response.put("Msg", "failed");
					return response;
				}
				
				jsonObject = httpResponse.getJsonObject();
				if( !jsonObject.has("order") ) {
					log.error("No order found in shopify cancel order API :: shopify response : "+ jsonObject.toString());
					response.put("Status", 0);
					response.put("Msg", "failed");
					return response;
				}
				
				ordersObj = jsonObject.getJSONObject("order");
				
				if( !ordersObj.has("cancelled_at") || ordersObj.get("cancelled_at") == null ) {
					log.error("order not closed :: response : "+ jsonObject.toString());
					response.put("Status", 0);
					response.put("Msg", "failed");
					return response;
				}
				
				// Old order close flow
//				JSONObject jsonBody = new JSONObject();
//				JSONObject productObj = new JSONObject();
//				productObj.put("id", orderId);
//				productObj.put("status", "archived");
//				
//				Map<String, String> body = new HashMap<>();
//				body.put("product", productObj.toString());
//				
//				httpResponse = httpReq("Close Orders", "POST", closeURL, headers, body);
//				
//				if( httpResponse == null || httpResponse.getResponse_code() != 200 ) {
//					log.error("Error while calling shopify for close order :: response_code : "+httpResponse.getResponse_code() +" :: response : "+ httpResponse.getJsonObject().toString());
//					response.put("Status", 0);
//					response.put("Msg", "failed");
//					return response;
//				}
//				
//				jsonObject = httpResponse.getJsonObject();
//				if( !jsonObject.has("order") ) {
//					log.error("No order found in shopify close order API :: shopify response : "+ jsonObject.toString());
//					response.put("Status", 0);
//					response.put("Msg", "failed");
//					return response;
//				}
//				
//				ordersObj = jsonObject.getJSONObject("order");
//				
//				if( !ordersObj.has("closed_at") || ordersObj.get("closed_at") == null ) {
//					log.error("order not closed :: response : "+ jsonObject.toString());
//					response.put("Status", 0);
//					response.put("Msg", "failed");
//					return response;
//				}
				
				
			} else {
				log.info("Order not cancel. Order created time greater than "+ cancelOrderInMinutes + " Minutes");
				response.put("Status", 0);
				response.put("Msg", "failed");
				return response;
			}
			
			log.info("Success order csncel");
			response.put("Status", 1);
			response.put("Msg", "success");
			return response;
		} catch (Exception e) {
			log.error("Error in cancelOrderShopify :: Error : "+ e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "failed");
			return response;
		}
	}
	
	
	public HttpResponse httpReq(String reqName, String method, String URL, Map<String, String> headers,
			Map<String, Object> bodyData) {
		log.info("Entered into httpReq :: reqName : "+reqName+" :: method : "+ method +" :: url : "+ URL);
		try {log.info("header : "+ headers.toString());} catch (Exception e) {}
		try {log.info("body data : "+ bodyData.toString());} catch (Exception e) {}
		String response = null;
		HttpResponse httpResponse = new HttpResponse();
		try {

			URL url_ = new URL(URL);
			HttpURLConnection http = (HttpURLConnection) url_.openConnection();
			http.setRequestMethod(method.toUpperCase());
			http.setDoOutput(true);
			http.setInstanceFollowRedirects(false);
			http.setUseCaches(false);

			if (headers != null) {
				headers.forEach((headerName, headerValue) -> {
					http.setRequestProperty(headerName, headerValue);
				});
			}

			boolean isJsonBody = false;
			if (headers != null && !headers.containsKey("Content-Type")) {
				http.setRequestProperty("Content-Type", "application/json");
				isJsonBody = true;
			}

			if (bodyData != null && !bodyData.isEmpty()) {
				String urlParams = "";
				boolean first = true;

				if (!isJsonBody) {
					for (Map.Entry<String, Object> entry : bodyData.entrySet()) {
						if (first)
							first = false;
						else
							urlParams += "&";
						urlParams += URLEncoder.encode(entry.getKey(), "UTF-8") + "="
								+ URLEncoder.encode(String.valueOf( entry.getValue() ), "UTF-8");
					}
				} else {
					urlParams = new JSONObject(bodyData).toString();
				}

				byte[] reqBody = urlParams.getBytes(StandardCharsets.UTF_8);
				http.setRequestProperty("Content-Length", Integer.toString(reqBody.length));
				DataOutputStream wr = new DataOutputStream(http.getOutputStream());
				wr.write(reqBody);
			}

			int responseCode = http.getResponseCode();
			log.info("reaponse_code : "+ responseCode);

			InputStream content = (InputStream) http.getInputStream();
			InputStreamReader inpStrmRdr = new InputStreamReader(content);
			BufferedReader bufRdr = new BufferedReader(inpStrmRdr);
			String line;
			StringBuilder sb = new StringBuilder();
			while ((line = bufRdr.readLine()) != null) {
				sb.append(line);
			}
			
			httpResponse.setResponse_code(responseCode);
			httpResponse.setJsonObject( new JSONObject(sb.toString()) );
			
			return httpResponse;
		} catch (Exception e) {
			log.info("Error in httpReq : " + e.getLocalizedMessage());
			return null;
		}
	}
	
}
