package com.nimble.irisservices.appcontroller;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dao.IReportDao;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSensorType;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.DeviceReplaced;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.EmailContent;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.IDeviceSubscriptionService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IRVCentricDetailsService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class GatewayControllerApp {

	private static final Logger log = LogManager.getLogger(GatewayControllerApp.class);

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;
	
	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IMessagingService messagingService;

	@Autowired
	@Lazy
	IReportService reportService;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IDeviceSubscriptionService deviceSubscription;

	@Autowired
	@Lazy
	IReportDao reportDao;

	@Autowired
	@Lazy
	IAlertCfgService alertCfgService;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Value("${niomip}")
	private String niomIP;

	@Value("${niomauthkey}")
	private String niomAuthKey;

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Value("${freeplan}")
	private String freeplan;

	@Value("${omitplan}")
	private String omitplan;

	@Value("${vpmplan}")
	private String vpmplan;

	@Value("${addonplan}")
	private String addonplan;

	Helper _helper = new Helper();

	@Autowired
	IRVCentricDetailsService rvcentricServ;

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;

	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;
	
	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;
	
	@Value("${show_upgrade_popup}")
	private boolean show_upgrade_popup;

	@RequestMapping(value = "v5.0/validateorderid/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse validateOrderIdV5(@RequestParam("orderid") String orderId,
			@RequestParam("orderchannel") String orderChannel,
			@RequestParam("os") String os,@RequestParam("app_ver") String app_ver,
			Authentication authentication,
			@RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		log.info("Entered validateOrderId, orderId : " + orderId + " orderChannel : " + orderChannel);
		JResponse response = new JResponse();
		try {
			String autho = header.getFirst("auth");
			int orderIdLength = orderId.length();
			if (!autho.equalsIgnoreCase("NA")) {
				try {
					UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}

				} catch (InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for username : " + autho);
					return response;
				}
			}
			
			if (orderChannel.equalsIgnoreCase("rv")) {
				Pattern numbers = Pattern.compile("^[0-9]+$");
				Matcher hasNumbers = numbers.matcher(orderId);
				if (hasNumbers.find() && orderIdLength >= 5 && orderIdLength <= 10) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					log.info(orderChannel + " Order ID validated " + orderId);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "Order ID should be 5 to 10 digits and should contains only numerics.");
					log.info(orderChannel + " Order ID should be 5 to 10 digits and should contains only numerics."
							+ orderId);
				}
			} else if (orderChannel.equalsIgnoreCase("amazon")) {

				Pattern numbersWithHypen = Pattern.compile("^[0-9,-]+$");
				Matcher hasNumbersWithHypen = numbersWithHypen.matcher(orderId);

				Pattern numbers = Pattern.compile("^[0-9]+$");
				Matcher hasNumbers = numbers.matcher(orderId);
				if ((hasNumbers.find() || hasNumbersWithHypen.find()) && orderIdLength >= 7) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					log.info(orderChannel + " Order ID validation success : " + orderId);
				} else {
					response.put("Status", 0);
					response.put("Msg",
							"Order ID should be min 7 characters and should not contains any alphabets or special characters except '-' (hypen)");
					log.info(orderChannel + " Order ID should contains only numerics : " + orderId);
				}
				response.put("Return Time", System.currentTimeMillis());
				return response;
			} else if (orderChannel.equalsIgnoreCase("walmart") || orderChannel.equalsIgnoreCase("others")) {
				Pattern numbers = Pattern.compile("^[0-9]+$");
				Matcher hasNumbers = numbers.matcher(orderId);

				if (hasNumbers.find()) {

					if (orderId.length() >= 10) {
						response.put("Status", 1);
						response.put("Msg", "Success");
						log.info(orderChannel + " Order id validated " + orderId);
					} else {
						response.put("Status", 0);
						response.put("Msg",
								"Order ID should be min 10 characters and should not contains any alphabets or special characters for walmart orders");
						log.info(orderChannel
								+ " Order ID should be min 10 characters and should not contains any alphabets or special characters for walmart orders");
					}

				} else {
					if(device_country == null || device_country.isEmpty() || device_country.equalsIgnoreCase("US")|| device_country.equalsIgnoreCase("NA")
							|| device_country.toLowerCase().contains("india") || device_country.equalsIgnoreCase("in")
							) {
						device_country = "US";
					}
					String msg = "Please contact us at "+supportContactNumber.get(device_country)+"  or email to "+supportContactEmail.get(device_country)+" to register your product";

					response.put("Status", 1);
					response.put("Msg",msg);
					log.info(orderChannel+ msg);
				}
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}else if (orderChannel.equalsIgnoreCase("technorv")) {

				Pattern numbersWithHypen = Pattern.compile("^[a-zA-Z0-9,-]+$");
				Matcher hasNumbersWithHypen = numbersWithHypen.matcher(orderId);

				Pattern numbers = Pattern.compile("^[a-zA-Z0-9]+$");
				Matcher hasNumbers = numbers.matcher(orderId);
				if ((hasNumbers.find() || hasNumbersWithHypen.find()) && orderIdLength >= 5) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					log.info(orderChannel + " Order ID validation success : " + orderId);
				} else {
					response.put("Status", 0);
					response.put("Msg",
							"Order ID should be min 5 characters and should not contains any special characters except '-' (hypen)");
					log.info(orderChannel + " Order ID should contains only numerics : " + orderId);
				}
				response.put("Return Time", System.currentTimeMillis());
				return response;
			} 
//					else if(orderChannel.equalsIgnoreCase("others"))  {
////						Pattern numbers  = Pattern.compile("^[0-9]+$");
////						Matcher hasNumbers  = numbers.matcher(orderId);
////						
////						if(hasNumbers.find() && orderId.length() >= 10 ) {
////							response.put("Status", 1);
////							response.put("Msg", "Success");
////							log.info(orderChannel+" Order id validated "+orderId);					
////						}else {
//							response.put("Status", 0);
//							response.put("Msg", "Please contact us at +1 (855)983-5566  or <NAME_EMAIL> to register your product");
//							log.info(orderChannel+" Please contact us at +1 (855)983-5566  or <NAME_EMAIL> to register your product");
//						//}
//						return response;
//					}
		} catch (Exception e) {
			log.error("Exception occured while validating order id : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unknown error");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/checkqrcexist/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse checkQrcExistV5(@RequestParam(value = "qrcode") String qrcode,
			@RequestParam(value = "monitortypeid") String monitortypeid,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "req_ver", defaultValue = "v1", required = false) String req_ver,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country,
			@RequestParam(value = "register_type", defaultValue = "qrc", required = false) String register_type) {
		log.info("Entered checkQrcExistv5 :: auth : "+ header.getFirst("auth"));
		JResponse response = new JResponse();
		UserV4 user = null;
		try {

			String autho = header.getFirst("auth");
			Properties prop = new Properties();
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			prop.load(new FileInputStream(file));
			String errorResponse = "No Error";
			String errorCode = "ER000";
			boolean isExist = false;

			if(register_type.equalsIgnoreCase("snnumber") || (qrcode != null && qrcode.length() == 9)) {
				String inventoryqrc = niomDbservice.getInventoryBySerialNo(qrcode);
				if(inventoryqrc != null)
					qrcode = inventoryqrc;
			}
			response.put("qrcode", qrcode != null ? qrcode : "");
			if (!autho.equalsIgnoreCase("NA")) {
				try {
					if (!validation_authkey.equals(autho))
						user = userServiceV4.verifyAuthV3("authkey", autho);

					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}

				} catch (InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for auth : " + ex.getLocalizedMessage());
					return response;
				}
				
				ArrayList<JSensorType> stype_list = gatewayServiceV4.getSensorType();			
				response.put("stype_list", stype_list);
				if (register_type.equalsIgnoreCase("qrc") || register_type.equalsIgnoreCase("snnumber")) {
					if (!(qrcode.matches("[a-zA-Z0-9]+"))
							|| !(qrcode.length() == 6
									|| (qrcode.length() == 18 && qrcode.substring(0, 4).equalsIgnoreCase("BSCN")))
							|| qrcode.startsWith("8")) {
						log.info("Invalid QRCode . " + qrcode);
						response.put("Status", 0);
						response.put("Msg", RegisterUserError.ER048);
						errorCode = "ER048";
						errorResponse = RegisterUserError.ER048;
						String subject = "External User QRC Activation Status";
						String to_address = prop.getProperty("to_address");
						String cc_address = ""; // prop.getProperty("cc_address");
						String bcc_address = prop.getProperty("bcc_address");
						String statusEmailContent = new EmailContent().externalUserQrcActivationEmailContent(user,
								qrcode, errorResponse);
						async.externalQrcActivationStatus(qrcode, errorCode, to_address, cc_address, bcc_address,
								subject, statusEmailContent);
						return response;
					}
				}else {
					log.info("Received macid:"+qrcode);
					String macid = qrcode.substring(4,qrcode.length() );
					qrcode = macid;
				}
				List<Inventory> inventoryList = niomDbservice.getInventory(qrcode);
				if( inventoryList == null || inventoryList.isEmpty() ) {
					log.error("Inventory not found for QRC : "+ qrcode);
					response.put("Status", 0);
					response.put("Msg", "QR code not found");
					response.put("monitor_type_id", 0);
					return response;
				}else {
					qrcode = inventoryList.get(0).getQrc();
				}
				
				AssetModel assetModel = gatewayService.getAssetModelByName( inventoryList.get(0).getDevicemodelnumber() );
				if( assetModel == null ) {
					log.error("Asset Model not found for Meid : "+ inventoryList.get(0).getMeid() );
					response.put("Status", 0);
					response.put("Msg", "QR code not found");
					response.put("monitor_type_id", 0);
					return response;
				}
				
				if( Long.parseLong( monitortypeid ) != 0 && Long.parseLong( monitortypeid ) != assetModel.getMonitor_type().getId() ) {
					log.error("user register a different product :" + monitortypeid);
					response.put("Status", 0);
					response.put("Msg", "Invalid QR Code. Please select valid product.");
					response.put("monitor_type_id", 0);
					return response;
				}
				long monitorTypeId = assetModel.getMonitor_type().getId();
				isExist = gatewayService.checkQrcExist(user.getId(), qrcode);
				boolean isQRCUser = false;

				if (user.getUsername().equalsIgnoreCase(qrcode)) {
					isQRCUser = true;
				}
				response.put("serial_no", inventoryList.get(0).getSerialnumber());
				boolean isReg = false;
				if (!isExist) {
					isReg = gatewayService.checkQrcRegistered(qrcode); // to check already registered
					if (isReg) {
						String username = gatewayService.getUsernameByQrc(qrcode);
						if (!isQRCUser) {
							response.put("Status", 0);
							if(username != null){
								StringBuilder hiddenEmail = new StringBuilder();
								for (int i = 0; i < username.length(); i++)
									hiddenEmail.append((i >= 2 && i < username.indexOf('@')) ? '*' : username.charAt(i));

								response.put("Msg", "This QRC is already linked to [" +hiddenEmail+"]. Please sign in with this email or reach out to support for help.");
							}else {
								if (monitorTypeId == 1) {
									response.put("Msg", "A Monitor with this QR code already registered. Please contact support!");
								} else if (monitorTypeId == 3) {
									response.put("Msg", "A Smart AI Bowl with this QR code already registered. Please contact support!");
								} else if (monitorTypeId == 4) {
									response.put("Msg", "A WaggleCam with this QR code already registered. Please contact support!");
								} else if (monitorTypeId == 5) {
									response.put("Msg", "A RV Cam AI with this QR code already registered. Please contact support!");
								} else if (monitorTypeId == 6) {
									response.put("Msg", "A WaggleCam Pro with this QR code already registered. Please contact support!");
								} else if (monitorTypeId == 8) {
									response.put("Msg", "A RV 4G Camera with this QR code already registered. Please contact support!");
								} else if (monitorTypeId == 9) {
									response.put("Msg", "A RV Smart Sensor with this QR code already registered. Please contact support!");
								} else {
									response.put("Msg", "A Monitor with this QR code already registered. Please contact support!");
								}
							}
							response.put("qrcavail", isReg);
							response.put("monitor_type_id", monitorTypeId);
							return response;
						}
					}
				}
				
				
				response.put("show_upgrade_popup", false);
				response.put("upgrade_gateway_id", 0);
				if( show_upgrade_popup ) {
//					RemoveGatewayRequest removeGatewayRequest = gatewayServiceV4.getRemoveGatewayRequest(user.getId());
//					if( removeGatewayRequest != null && removeGatewayRequest.getRemove_gateway_type_id() == 1) {
//						response.put("show_upgrade_popup", true);
//						response.put("upgrade_gateway_id", removeGatewayRequest.getGateway_id());
//					}
					
					List<JGateway> userGateway = gatewayService.getGateway(null, null, null, null, user.getId(),
							null);
					
					List<JGateway> gatewayTypeOne = userGateway.stream().filter( g -> g.getMonitorTypeId() == 1 )
					.collect(Collectors.toList());
					
					DeviceReplaced deviceReplaced = userService.getRecallDeviceDetails(user.getId());
					
					if( deviceReplaced == null && userGateway != null ) {
						if( req_ver.equalsIgnoreCase("v2") && gatewayTypeOne.size() >= 1 ) {
							response.put("show_upgrade_popup", true);
							response.put("upgrade_gateway_id", gatewayTypeOne.get(0).getId());
						} else if( gatewayTypeOne.size() == 1 ) {
							response.put("show_upgrade_popup", true);
							response.put("upgrade_gateway_id", gatewayTypeOne.get(0).getId());	
						}
					}
					
				}
					
				if (isExist) {

					if (isQRCUser) {
						response.put("Status", 1);
						response.put("Msg", "Valid QR code");
						response.put("monitor_type_id", monitorTypeId);
						response.put("qrc", qrcode);

					} else {
						response.put("Status", 0);
						if( monitorTypeId == 1 ){
							response.put("Msg", "A Monitor with this QR code already exists in your account");	
						}else if( monitorTypeId == 3 ){
							response.put("Msg", "A Smart AI Bowl with this QR code already exists in your account");
						} else if( monitorTypeId == 4 ){
							response.put("Msg", "A Waggle Cam with this QR code already exists in your account");
						}else if( monitorTypeId == 5 ){
							response.put("Msg", "A RV Cam AI with this QR code already exists in your account");
						} else {
							response.put("Msg", "A Monitor with this QR code already exists in your account");	
						}
						response.put("monitor_type_id", monitorTypeId);
					}

				} else {
					response.put("Status", 1);
					response.put("Msg", "Qrcode not exist!");
					response.put("monitor_type_id", monitorTypeId);
					response.put("qrc", qrcode);

				}
				response.put("qrcavail", isExist);

			} else {
				log.info("invalid authkey received so do qrcode validation only and send if qrcode is valid");
				if (!(qrcode.matches("[a-zA-Z0-9]+")) || !(qrcode.length() == 6 || (qrcode.length() == 18 && qrcode.substring(0, 4).equalsIgnoreCase("BSCN"))) || qrcode.startsWith("8")) {
					log.info("Valid QRCode . " + qrcode);
					response.put("Status", 0);
					response.put("Msg", RegisterUserError.ER048);
					response.put("monitor_type_id", 0);
					return response;
				} else {
					boolean isReg = gatewayService.checkQrcRegistered(qrcode); // to check already registered
					if (isReg) {
						response.put("Status", 1);
						response.put("Msg","A monitor with this QR code already registered. Please contact support!");
						response.put("qrcavail", isReg);
						response.put("monitor_type_id", 0);
						response.put("qrc", qrcode);
						return response;
					}else {
						response.put("Status", 1);
						response.put("Msg", "Valid QR code");
						response.put("qrcavail", isExist);
						response.put("monitor_type_id", 0);
						response.put("qrc", qrcode);
					}
				}
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "invalid authkey");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception checkQrcExistv5 : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	
	// On-Off Feature
	@RequestMapping(value = "v3.0/onoff/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse assignGatewaysToUser(@RequestParam String gatewayid, @RequestParam boolean enable,
			Authentication authentication, @RequestHeader HttpHeaders header,@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try {
			User user = userService.verifyAuthKey(autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			String message = null;

			Gateway gateway = gatewayService.getGateway(Long.valueOf(gatewayid));
			if (enable) {
				message = "lpm=iomode,reportinterval=" + gateway.getOnSleepTime() + ",maxsleeptime="
						+ gateway.getOnSleepTime();
			} else {
				message = "lpm=hibernate,reportinterval=" + gateway.getOffSleepTime() + ",maxsleeptime="
						+ gateway.getOffSleepTime();
			}

			boolean updateGatewayOnOffStatus = gatewayService.gatewayOnOff(gatewayid, enable, message);
			// userService.assignGatewaysToUser(userid, gatewayids);

			if (updateGatewayOnOffStatus) {

				String[] msg = message.split(",");

				for (String _msg : msg) {
					messagingService.saveMessageV2(gatewayid, _msg, 0L);
				}

				response.put("Status", 1);
				response.put("Msg", "success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed to On/Off the device");
			}

		} catch (InvalidAuthoException e) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authentication");
			log.error("Invalid Authentication:" + e.getMessage());

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in ON OFF the device");
			log.error("On/OFF Device:" + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}



	@RequestMapping(value = "v3.0/checkqrcexist/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse checkQrcExist(@RequestParam(value = "qrcode") String qrcode,
			@RequestParam(value = "monitortypeid") String monitortypeid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		log.info("Entered checkQrcExist");
		JResponse response = new JResponse();
		UserV4 user = null;
		try {

			String autho = header.getFirst("auth");
			Properties prop = new Properties();
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			prop.load(new FileInputStream(file));
			String errorResponse = "No Error";
			String errorCode = "ER000";
			boolean isExist = false;
			if (!autho.equalsIgnoreCase("NA")) {
				try {
					if (!validation_authkey.equals(autho))
						user = userServiceV4.verifyAuthV3("authkey", autho);

					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}

				} catch (InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for auth : " + ex.getLocalizedMessage());
					return response;
				}

				if (!(qrcode.matches("[a-zA-Z0-9]+")) || !(qrcode.length() == 6 || (qrcode.length() == 18 && qrcode.substring(0, 4).equalsIgnoreCase("BSCN"))) || qrcode.startsWith("8")) {
					log.info("Invalid QRCode . " + qrcode);
					response.put("Status", 0);
					response.put("Msg", RegisterUserError.ER048);
					errorCode = "ER048";
					errorResponse = RegisterUserError.ER048;
					String subject = "External User QRC Activation Status";
					String to_address = prop.getProperty("to_address");
					String cc_address = ""; // prop.getProperty("cc_address");
					String bcc_address = prop.getProperty("bcc_address");
					String statusEmailContent = new EmailContent().externalUserQrcActivationEmailContent(user, qrcode,
							errorResponse);
					async.externalQrcActivationStatus(qrcode, errorCode, to_address, cc_address, bcc_address, subject,
							statusEmailContent);
					return response;
				}
				isExist = gatewayService.checkQrcExist(user.getId(), qrcode);
				boolean isQRCUser = false;

				if (user.getUsername().equalsIgnoreCase(qrcode)) {
					isQRCUser = true;
				}

				boolean isReg = false;
				if (!isExist) {
					isReg = gatewayService.checkQrcRegistered(qrcode); // to check already registered
					if (isReg) {
						if (!isQRCUser) {
							response.put("Status", 0);
							response.put("Msg","A monitor with this QR code already registered. Please contact support!");
							response.put("qrcavail", isReg);
							return response;
						}
					}
				}
				if (isExist) {

					if (isQRCUser) {
						response.put("Status", 1);
						response.put("Msg", "Valid QR code");
					} else {
						response.put("Status", 0);
						response.put("Msg", "A monitor with this QR code already exists in your account");
					}

				} else {
					response.put("Status", 1);
					response.put("Msg", "Qrcode not exist!");
				}
				response.put("qrcavail", isExist);

			} else {
				log.info("invalid authkey received so do qrcode validation only and send if qrcode is valid");
				if (!(qrcode.matches("[a-zA-Z0-9]+")) || !(qrcode.length() == 6 || (qrcode.length() == 18 && qrcode.substring(0, 4).equalsIgnoreCase("BSCN"))) || qrcode.startsWith("8")) {
					log.info("Valid QRCode . " + qrcode);
					response.put("Status", 0);
					response.put("Msg", RegisterUserError.ER048);
					return response;
				} else {
					boolean isReg = gatewayService.checkQrcRegistered(qrcode); // to check already registered
					if (isReg) {
						response.put("Status", 1);
						response.put("Msg","A monitor with this QR code already registered. Please contact support");
						response.put("qrcavail", isReg);
						return response;
					}else {
						response.put("Status", 1);
						response.put("Msg", "Valid QR code");
						response.put("qrcavail", isExist);
					}
				}
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "invalid authkey");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception checkQrcExist : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

}
