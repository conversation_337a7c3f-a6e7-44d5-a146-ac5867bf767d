package com.nimble.irisservices.appcontroller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.UserMetaData;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IUserMetaDataService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class UserMetaDataControllerApp {
	
	private static final Logger log = LogManager.getLogger(UserMetaDataControllerApp.class);

	@Autowired
	IUserMetaDataService userMetaDataservice;
	
	@Autowired
	IUserServiceV4 userServiceV4;
	
	@Autowired
	Helper _helper;
	
	@RequestMapping(value = "v5.0/app_info", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse userMetaDataV5(@RequestBody UserMetaData userMetaData,
			@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam String os,
			@RequestParam String app_ver){
		log.info("Entered into userMetaData :: user_id : "+userMetaData.getUser_id());
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");
			
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			
			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			UserMetaData existUserMetaData = userMetaDataservice.getUserMetaData(userMetaData.getUser_id());
			
			if( existUserMetaData != null && !existUserMetaData.getCreated_on().contains("1753-01-01 00:00:00")) {
				
				userMetaData.setCreated_on( existUserMetaData.getCreated_on() );		
				
			} else {
				String cur_utc = IrisservicesUtil.getCurrentTimeUTC();
				userMetaData.setCreated_on( cur_utc );
			}
				
			
			userMetaData = userMetaDataservice.saveUserMetaData(userMetaData);
			
			if( userMetaData == null ) {
				log.info("app info not saved");
				response.put("Status", 0);
				response.put("Msg", "Failed");
				return response;
			}
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			
		}catch(Exception e) {
			log.error("Error in userMetaData :: Error : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		return response;
	}

	
	@RequestMapping(value = "v4.0/app_info", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse userMetaData(@RequestBody UserMetaData userMetaData,
			@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam String os,
			@RequestParam String app_ver){
		log.info("Entered into userMetaData :: user_id : "+userMetaData.getUser_id());
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");
			
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			
			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			UserMetaData existUserMetaData = userMetaDataservice.getUserMetaData(userMetaData.getUser_id());
			
			if( existUserMetaData != null && !existUserMetaData.getCreated_on().contains("1753-01-01 00:00:00")) {
				
				userMetaData.setCreated_on( existUserMetaData.getCreated_on() );		
				
			} else {
				String cur_utc = IrisservicesUtil.getCurrentTimeUTC();
				userMetaData.setCreated_on( cur_utc );
			}
				
			
			userMetaData = userMetaDataservice.saveUserMetaData(userMetaData);
			
			if( userMetaData == null ) {
				log.info("app info not saved");
				response.put("Status", 0);
				response.put("Msg", "Failed");
				return response;
			}
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			
		}catch(Exception e) {
			log.error("Error in userMetaData :: Error : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		return response;
	}
}
