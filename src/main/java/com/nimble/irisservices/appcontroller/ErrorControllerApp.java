package com.nimble.irisservices.appcontroller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class ErrorControllerApp {

	private static final Logger log = LogManager.getLogger(ErrorControllerApp.class);
	
	@GetMapping(value = "/error")
	public String showError_404() {
		log.info("Entered into error ");
		return "error";
	}

	
}
