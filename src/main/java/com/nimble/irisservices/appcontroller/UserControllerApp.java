package com.nimble.irisservices.appcontroller;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.zip.GZIPOutputStream;

import javax.validation.Valid;

import com.nimble.irisservices.Util.SecretManagerService;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.util.ResourceUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.nimble.irisservices.dao.IGatewayDao;
import com.nimble.irisservices.dto.ActivateUser;
import com.nimble.irisservices.dto.Configuration;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.ForceUpdate;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserVerification;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.exception.InvalidUsernameException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.service.IActivateUserService;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAlertCfgServiceV4;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.ICcplistService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICompanyTypeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IFetchDropdownService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IGroupServices;
import com.nimble.irisservices.service.IMailService;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IRVCentricDetailsService;
import com.nimble.irisservices.service.IRegisterUserEmailService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IThrottlingService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IZipCodeDetailsService;

@RestController
@RequestMapping("/app")
public class UserControllerApp {

	private static final Logger log = LogManager.getLogger(UserControllerApp.class);

	@Autowired
	SecretManagerService secretManagerService;

	@Value("${aws_s3_secret_name}")
	private String S3_SECRET_NAME;

	@Autowired
	ICompanyTypeService companyTypeServ;

	@Autowired
	IThrottlingService throttlingService;
	
	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IUserServiceV4 userServV4;

	@Autowired
	@Lazy
	IGatewayDao gatewayDao;

	@Autowired
	@Lazy
	IMessagingService ImsgService;

	@Autowired
	@Lazy
	ICcplistService ccpservice;

	@Autowired
	@Lazy
	IGroupServices groupservices;

	@Autowired
	@Lazy
	IFetchDropdownService fetchDropdownService;

	@Autowired
	@Lazy
	IReportService reportService;

	@Autowired
	@Lazy
	IAlertCfgService alertCfgService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IRegisterUserEmailService registerEmailStatus;

	@Autowired
	@Lazy
	IZipCodeDetailsService iZipCodeDetailsService;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	IMailService mailService;

	@Autowired
	IRVCentricDetailsService rvServ;

	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;

	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;

	@Value("${niomip}")
	private String niomIP;

	@Value("${niomauthkey}")
	private String niomAuthKey;

	@Value("${verificationtime}")
	private String verificationtime;

	@Value("${google.api.key}")
	private String googleAPIkey;

	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	@Autowired
	@Lazy
	private IAsyncService async;

	Helper _helper = new Helper();

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Value("${send_registeruseremail_simactivation_to_microservice}")
	private boolean sendActivateUserDataToSQS_Microservice;

	@Value("${add_free_vpm}")
	private boolean addFreeVpmFlag;

	@Value("${vpm_freedays}")
	private String freeVPM_days;

	@Value("${show_orderid_popup_while_register}")
	private boolean show_warranty_popup_config;

	@Value("${warranty_msg}")
	private String warranty_msg;

	@Value("${return.login.username}")
	private String returnLoginUsername;

	@Value("${subscription.buynow.popup}")
	private boolean subscriptionBuyNowPopUp_config;

	@Value("${subscription.buynow.content}")
	private String subscriptionBuyNowContent;

	@Autowired
	IRVCentricDetailsService rvcentricServ;

	@Autowired
	@Lazy
	IChargebeeService cbService;

	@Autowired
	freemarker.template.Configuration templates;

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";

	@Value("${check.recall.device.qrc}")
	private boolean checkRecallQRC;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;

	@Autowired
	@Lazy
	IAlertCfgServiceV4 alertCfgServiceV4;

	@Value("${show.subs.page.first.config}")
	private boolean show_subs_page_first_config = true;

	@Value("${redirect.payment.page}")
	private boolean redirectPaymentPage_config = false;

	@Value("${redirect.payment.page.content}")
	private String redirectPaymentPageContent;

	@Value("${orderid.later.popup}")
	private boolean orderid_later_popup_config = false;

	@Value("${orderid.later.popup.content}")
	private String orderid_later_popup_content;

	@Autowired
	@Lazy
	IChargebeeService chargebeeService;

	@Value("${product_subs_enable}")
	private boolean product_subs_enable;	
	
	@Autowired
	@Lazy
	IActivateUserService activateUserService;
	
	@Value("${show_download_popup}")
	private String show_download_popup;
	
	@Value("${later_download}")
	private String later_download;

	@RequestMapping(value = "v5.0/forceUpdate", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse isForceUpdateV5(@RequestParam("app") String app, @RequestParam("version") String version,
			@RequestParam("mobiletype") String mobiletype,
			@RequestParam(value = "userid", defaultValue = "", required = false) String userid,
			@RequestParam("os") String os,
			@RequestParam(value = "backing", defaultValue = "MT", required = false) String backing,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");

		UserV4 user = null;
		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (InvalidAuthoException e) {
			log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			return response;
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		if (app.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Application Type Should Not Be Empty!");
			return response;
		} else if (version.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Application verison Should Not Be Empty!");
			return response;
		} else if (mobiletype.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Mobile Type Should Not Be Empty!");
			return response;
		}

		// App id
		// 5 RFS_BG - Background
		// 4 RM-Restaurant Monitoring

		int app_i = Integer.parseInt(app);
		int version_i = Integer.parseInt(version.replaceAll("\\.", ""));
		int mobiletype_i = Integer.parseInt(mobiletype);

		String ip = "***************";

		String rv_android_V = "4.0.8";
		String rv_android_force = "0";
		String rv_android_force_V = "4.0.8";

		String rv_ios_V = "6.0.8";
		String rv_ios_force = "0";
		String rv_ios_force_V = "6.0.8";

		String iris3_android_V = "3.0.6";
		String iris3_android_force = "0";
		String iris3_android_force_V = "3.0.6";

		String iris3_ios_V = "5.0.8";
		String iris3_ios_force = "0";
		String iris3_ios_force_V = "5.0.8";

		String iris3_wl_android_V = "3.0.6";
		String iris3_wl_android_force = "0";
		String iris3_wl_android_force_V = "3.0.6";

		String iris3_wl_ios_V = "5.0.8";
		String iris3_wl_ios_force = "0";
		String iris3_wl_ios_force_V = "5.0.8";

		String rm_android_V = "1.0.4";
		String rm_android_force = "1";
		String rm_android_force_V = "1.0.2";

		String rm_ios_V = "1.0.4";
		String rm_ios_force = "1";
		String rm_ios_force_V = "1.0.2";

		String webservice_V = "3.2.0";
		String listener_V = "3.1.19";
		String webapp_V = "3.1.13";
		String database_V = "3.1.19";
		/*
		 * hotline username - mmarimut
		 * 
		 * @ nimblewireless . com
		 */
		String rv_hotline_AppId = "cedda3bc-c628-4bac-8f65-9b408d437614_1";
		/*
		 * hotline username - mmarimut
		 * 
		 * @ nimblewireless . com
		 */

		String rv_hotline_AppKey = "3ef7a41f-9419-4bbd-ba18-437f8eb83341_1";
		String rm_bg_ios_V = "1.0.2";
		String rm_bg_ios_force = "1.0.2";

		String rm_bg_android_V = "1.0.1";
		String rm_bg_android_force = "1.0.1";

		String rv_petprofile_force = "1";

		String s3bucketname_iris = "iris3.nimblewireless.com";
		String s3_key = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_access_key");
		String s3_secret = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_secret_key");

		Configuration config = new Configuration(ip, rv_android_V, rv_android_force, rv_android_force_V, rv_ios_V,
				rv_ios_force, rv_ios_force_V, iris3_android_V, iris3_android_force, iris3_android_force_V, iris3_ios_V,
				iris3_ios_force, iris3_ios_force_V, iris3_wl_android_V, iris3_wl_android_force,
				iris3_wl_android_force_V, iris3_wl_ios_V, iris3_wl_ios_force, iris3_wl_ios_force_V, rm_android_V,
				rm_android_force, rm_android_force_V, rm_ios_V, rm_ios_force, rm_ios_force_V, listener_V, webservice_V,
				webapp_V, database_V, rv_hotline_AppId, rv_hotline_AppKey, rm_bg_ios_V, rm_bg_ios_force,
				rm_bg_android_V, rm_bg_android_force, rv_petprofile_force, s3bucketname_iris, s3_key, s3_secret);

		/*
		 * Configuration config = new Configuration(ip, rv_android_V,
		 * rv_ios_V,iris3_android_V,webservice_V,listener_V,webapp_V,database_V,
		 * iris3_ios_V,rv_android_force,rv_ios_force,iris3_android_force,
		 * iris3_ios_force,rv_hotline_AppId,rv_hotline_AppKey,rm_android_V,
		 * rm_android_force,rm_ios_V, rm_ios_force);
		 */

		response.put("Status", 1);
		response.put("Msg", "success");

		try {
			Properties prop = new Properties();
			try {
				/* load a properties file */
				// prop.load(new FileInputStream("iris3.properties"));
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));
				ip = prop.getProperty("ip");
				rv_android_V = prop.getProperty("rv_android_V");
				rv_android_force = prop.getProperty("rv_android_force");
				rv_android_force_V = prop.getProperty("rv_android_force_V");

				rv_ios_V = prop.getProperty("rv_ios_V");
				rv_ios_force = prop.getProperty("rv_ios_force");
				rv_ios_force_V = prop.getProperty("rv_ios_force_V");

				iris3_android_V = prop.getProperty("iris3_android_V");
				iris3_android_force = prop.getProperty("iris3_android_force");
				iris3_android_force_V = prop.getProperty("iris3_android_force_V");

				iris3_ios_V = prop.getProperty("iris3_ios_V");
				iris3_ios_force = prop.getProperty("iris3_ios_force");
				iris3_ios_force_V = prop.getProperty("iris3_ios_force_V");

				iris3_wl_android_V = prop.getProperty("iris3_wl_android_V");
				iris3_wl_android_force = prop.getProperty("iris3_wl_android_force");
				iris3_wl_android_force_V = prop.getProperty("iris3_wl_android_force_V");

				iris3_wl_ios_V = prop.getProperty("iris3_wl_ios_V");
				iris3_wl_ios_force = prop.getProperty("iris3_wl_ios_force");
				iris3_wl_ios_force_V = prop.getProperty("iris3_wl_ios_force_V");

				rm_android_V = prop.getProperty("rm_android_V");
				rm_android_force = prop.getProperty("rm_android_force");
				rm_android_force_V = prop.getProperty("rm_android_force_V");

				rm_ios_V = prop.getProperty("rm_ios_V");
				rm_ios_force = prop.getProperty("rm_ios_force");
				rm_ios_force_V = prop.getProperty("rm_ios_force_V");

				webservice_V = prop.getProperty("webservice_V");
				listener_V = prop.getProperty("listener_V");
				webapp_V = prop.getProperty("webapp_V");
				database_V = prop.getProperty("database_V");

				rv_hotline_AppId = prop.getProperty("rv_hotline_AppId");
				rv_hotline_AppKey = prop.getProperty("rv_hotline_AppKey");

				rm_bg_ios_V = prop.getProperty("rm_bg_ios_V");
				rm_bg_ios_force = prop.getProperty("rm_bg_ios_force");

				rm_bg_android_V = prop.getProperty("rm_bg_android_V");
				rm_bg_android_force = prop.getProperty("rm_bg_android_force");

				rv_petprofile_force = prop.getProperty("rv_petprofile_force");

				s3bucketname_iris = prop.getProperty("s3bucketname_iris");
				s3_key = prop.getProperty("s3_key");
				s3_secret = prop.getProperty("s3_secret");

				ForceUpdate forceUpt = userService.getForceUpdate(userid);
				if (forceUpt != null) {
					if (0 < versionCompare(forceUpt.getAndroidVersion(), rv_android_force_V))
						rv_android_force_V = forceUpt.getAndroidVersion();
					if (0 < versionCompare(forceUpt.getIosVersion(), rv_ios_force_V))
						rv_ios_force_V = forceUpt.getIosVersion();
				}

				config = new Configuration(ip, rv_android_V, rv_android_force, rv_android_force_V, rv_ios_V,
						rv_ios_force, rv_ios_force_V, iris3_android_V, iris3_android_force, iris3_android_force_V,
						iris3_ios_V, iris3_ios_force, iris3_ios_force_V, iris3_wl_android_V, iris3_wl_android_force,
						iris3_wl_android_force_V, iris3_wl_ios_V, iris3_wl_ios_force, iris3_wl_ios_force_V,
						rm_android_V, rm_android_force, rm_android_force_V, rm_ios_V, rm_ios_force, rm_ios_force_V,
						listener_V, webservice_V, webapp_V, database_V, rv_hotline_AppId, rv_hotline_AppKey,
						rm_bg_ios_V, rm_bg_ios_force, rm_bg_android_V, rm_bg_android_force, rv_petprofile_force,
						s3bucketname_iris, s3_key, s3_secret);

				/*
				 * config = new Configuration(ip, rv_android_V,
				 * rv_ios_V,iris3_android_V,webservice_V,listener_V,webapp_V, database_V,
				 * iris3_ios_V,rv_android_force,rv_ios_force,iris3_android_force
				 * ,iris3_ios_force,rv_hotline_AppId,rv_hotline_AppKey,
				 * rm_android_V,rm_android_force, rm_ios_V, rm_ios_force);
				 */

				if (app_i == 6) // White label App
				{
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(iris3_wl_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(iris3_wl_ios_force_V, version));
					}
				} else if (app_i == 5) // Background App
				{
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(rm_bg_android_force, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(rm_bg_ios_force, version));
					}
				} else if (app_i == 4) {
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(rm_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(rm_ios_force_V, version));
					}
				} else if (app_i == 3) {
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(rv_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(rv_ios_force_V, version));
					}
				} else if (app_i == 2) {
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(iris3_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(iris3_ios_force_V, version));
					}
				} else {
					response.put("forceUpdate", "Invalid Version");
				}

			} catch (IOException ex) {
				log.error(ex.getLocalizedMessage());
			}

			response.put("show_download_popup", Boolean.parseBoolean(show_download_popup));
			response.put("show_download_content", "<center><div><p style=\"color: #000;font-size:18px;font-weight:bolder;\">A new version is available.</p><p style=\"color: #808080;font-size:15px;\">Enhance your pet monitoring experience by updating the app now.</p></div></center>");
			response.put("show_download_content_flutter_light", "<center><div><p style=\"color: #000;font-size:18px;font-weight:bolder;\">A new version is available.</p><p style=\"color: #808080;font-size:15px;\">Enhance your pet monitoring experience by updating the app now.</p></div></center>");
			response.put("show_download_content_flutter_dark", "<center><div><p style=\"color: #fff;font-size:18px;font-weight:bolder;\">A new version is available.</p><p style=\"color: #fff;font-size:15px;\">Enhance your pet monitoring experience by updating the app now.</p></div></center>");

			
			if(os.equalsIgnoreCase("iOS"))
				response.put("download_link", "https://itunes.apple.com/in/app/rv-petsafety/id1143805780?platform=iphone&preserveScrollPosition=true#platform/iphone");
			else
				response.put("download_link", "https://play.google.com/store/apps/details?id=com.nimble.petsafety");
			
			response.put("later_download", Boolean.parseBoolean(later_download));
			
			response.put("config", config);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			log.error("GetVersion::::::" + e.getMessage());
			response.put("Status", 1);
			response.put("Msg", "success");
			return response;
		}
	}

	// ========force version================
	@RequestMapping(value = "v3.0/forceUpdate", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse isForceUpdate(@RequestParam("app") String app, @RequestParam("version") String version,
			@RequestParam("mobiletype") String mobiletype,
			@RequestParam(value = "userid", defaultValue = "", required = false) String userid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "backing", defaultValue = "MT", required = false) String backing,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");

		UserV4 user = null;
		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (InvalidAuthoException e) {
			log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			return response;
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		if (app.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Application Type Should Not Be Empty!");
			return response;
		} else if (version.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Application verison Should Not Be Empty!");
			return response;
		} else if (mobiletype.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Mobile Type Should Not Be Empty!");
			return response;
		}

		// App id
		// 5 RFS_BG - Background
		// 4 RM-Restaurant Monitoring

		int app_i = Integer.parseInt(app);
		int version_i = Integer.parseInt(version.replaceAll("\\.", ""));
		int mobiletype_i = Integer.parseInt(mobiletype);

		String ip = "***************";

		String rv_android_V = "4.0.8";
		String rv_android_force = "0";
		String rv_android_force_V = "4.0.8";

		String rv_ios_V = "6.0.8";
		String rv_ios_force = "0";
		String rv_ios_force_V = "6.0.8";

		String iris3_android_V = "3.0.6";
		String iris3_android_force = "0";
		String iris3_android_force_V = "3.0.6";

		String iris3_ios_V = "5.0.8";
		String iris3_ios_force = "0";
		String iris3_ios_force_V = "5.0.8";

		String iris3_wl_android_V = "3.0.6";
		String iris3_wl_android_force = "0";
		String iris3_wl_android_force_V = "3.0.6";

		String iris3_wl_ios_V = "5.0.8";
		String iris3_wl_ios_force = "0";
		String iris3_wl_ios_force_V = "5.0.8";

		String rm_android_V = "1.0.4";
		String rm_android_force = "1";
		String rm_android_force_V = "1.0.2";

		String rm_ios_V = "1.0.4";
		String rm_ios_force = "1";
		String rm_ios_force_V = "1.0.2";

		String webservice_V = "3.2.0";
		String listener_V = "3.1.19";
		String webapp_V = "3.1.13";
		String database_V = "3.1.19";
		/*
		 * hotline username - mmarimut
		 * 
		 * @ nimblewireless . com
		 */
		String rv_hotline_AppId = "cedda3bc-c628-4bac-8f65-9b408d437614_1";
		/*
		 * hotline username - mmarimut
		 * 
		 * @ nimblewireless . com
		 */

		String rv_hotline_AppKey = "3ef7a41f-9419-4bbd-ba18-437f8eb83341_1";
		String rm_bg_ios_V = "1.0.2";
		String rm_bg_ios_force = "1.0.2";

		String rm_bg_android_V = "1.0.1";
		String rm_bg_android_force = "1.0.1";

		String rv_petprofile_force = "1";

		String s3bucketname_iris = "iris3.nimblewireless.com";
		String s3_key = "********************";
		String s3_secret = "4GidEgA09FO97x7EvIOEbFoRJ/DNkEdtWiAUY+LC";

		Configuration config = new Configuration(ip, rv_android_V, rv_android_force, rv_android_force_V, rv_ios_V,
				rv_ios_force, rv_ios_force_V, iris3_android_V, iris3_android_force, iris3_android_force_V, iris3_ios_V,
				iris3_ios_force, iris3_ios_force_V, iris3_wl_android_V, iris3_wl_android_force,
				iris3_wl_android_force_V, iris3_wl_ios_V, iris3_wl_ios_force, iris3_wl_ios_force_V, rm_android_V,
				rm_android_force, rm_android_force_V, rm_ios_V, rm_ios_force, rm_ios_force_V, listener_V, webservice_V,
				webapp_V, database_V, rv_hotline_AppId, rv_hotline_AppKey, rm_bg_ios_V, rm_bg_ios_force,
				rm_bg_android_V, rm_bg_android_force, rv_petprofile_force, s3bucketname_iris, s3_key, s3_secret);

		/*
		 * Configuration config = new Configuration(ip, rv_android_V,
		 * rv_ios_V,iris3_android_V,webservice_V,listener_V,webapp_V,database_V,
		 * iris3_ios_V,rv_android_force,rv_ios_force,iris3_android_force,
		 * iris3_ios_force,rv_hotline_AppId,rv_hotline_AppKey,rm_android_V,
		 * rm_android_force,rm_ios_V, rm_ios_force);
		 */

		response.put("Status", 1);
		response.put("Msg", "success");

		try {
			Properties prop = new Properties();
			try {
				/* load a properties file */
				// prop.load(new FileInputStream("iris3.properties"));
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));
				ip = prop.getProperty("ip");
				rv_android_V = prop.getProperty("rv_android_V");
				rv_android_force = prop.getProperty("rv_android_force");
				rv_android_force_V = prop.getProperty("rv_android_force_V");

				rv_ios_V = prop.getProperty("rv_ios_V");
				rv_ios_force = prop.getProperty("rv_ios_force");
				rv_ios_force_V = prop.getProperty("rv_ios_force_V");

				iris3_android_V = prop.getProperty("iris3_android_V");
				iris3_android_force = prop.getProperty("iris3_android_force");
				iris3_android_force_V = prop.getProperty("iris3_android_force_V");

				iris3_ios_V = prop.getProperty("iris3_ios_V");
				iris3_ios_force = prop.getProperty("iris3_ios_force");
				iris3_ios_force_V = prop.getProperty("iris3_ios_force_V");

				iris3_wl_android_V = prop.getProperty("iris3_wl_android_V");
				iris3_wl_android_force = prop.getProperty("iris3_wl_android_force");
				iris3_wl_android_force_V = prop.getProperty("iris3_wl_android_force_V");

				iris3_wl_ios_V = prop.getProperty("iris3_wl_ios_V");
				iris3_wl_ios_force = prop.getProperty("iris3_wl_ios_force");
				iris3_wl_ios_force_V = prop.getProperty("iris3_wl_ios_force_V");

				rm_android_V = prop.getProperty("rm_android_V");
				rm_android_force = prop.getProperty("rm_android_force");
				rm_android_force_V = prop.getProperty("rm_android_force_V");

				rm_ios_V = prop.getProperty("rm_ios_V");
				rm_ios_force = prop.getProperty("rm_ios_force");
				rm_ios_force_V = prop.getProperty("rm_ios_force_V");

				webservice_V = prop.getProperty("webservice_V");
				listener_V = prop.getProperty("listener_V");
				webapp_V = prop.getProperty("webapp_V");
				database_V = prop.getProperty("database_V");

				rv_hotline_AppId = prop.getProperty("rv_hotline_AppId");
				rv_hotline_AppKey = prop.getProperty("rv_hotline_AppKey");

				rm_bg_ios_V = prop.getProperty("rm_bg_ios_V");
				rm_bg_ios_force = prop.getProperty("rm_bg_ios_force");

				rm_bg_android_V = prop.getProperty("rm_bg_android_V");
				rm_bg_android_force = prop.getProperty("rm_bg_android_force");

				rv_petprofile_force = prop.getProperty("rv_petprofile_force");

				s3bucketname_iris = prop.getProperty("s3bucketname_iris");
				s3_key = prop.getProperty("s3_key");
				s3_secret = prop.getProperty("s3_secret");

				ForceUpdate forceUpt = userService.getForceUpdate(userid);
				if (forceUpt != null) {
					if (0 < versionCompare(forceUpt.getAndroidVersion(), rv_android_force_V))
						rv_android_force_V = forceUpt.getAndroidVersion();
					if (0 < versionCompare(forceUpt.getIosVersion(), rv_ios_force_V))
						rv_ios_force_V = forceUpt.getIosVersion();
				}

				config = new Configuration(ip, rv_android_V, rv_android_force, rv_android_force_V, rv_ios_V,
						rv_ios_force, rv_ios_force_V, iris3_android_V, iris3_android_force, iris3_android_force_V,
						iris3_ios_V, iris3_ios_force, iris3_ios_force_V, iris3_wl_android_V, iris3_wl_android_force,
						iris3_wl_android_force_V, iris3_wl_ios_V, iris3_wl_ios_force, iris3_wl_ios_force_V,
						rm_android_V, rm_android_force, rm_android_force_V, rm_ios_V, rm_ios_force, rm_ios_force_V,
						listener_V, webservice_V, webapp_V, database_V, rv_hotline_AppId, rv_hotline_AppKey,
						rm_bg_ios_V, rm_bg_ios_force, rm_bg_android_V, rm_bg_android_force, rv_petprofile_force,
						s3bucketname_iris, s3_key, s3_secret);

				/*
				 * config = new Configuration(ip, rv_android_V,
				 * rv_ios_V,iris3_android_V,webservice_V,listener_V,webapp_V, database_V,
				 * iris3_ios_V,rv_android_force,rv_ios_force,iris3_android_force
				 * ,iris3_ios_force,rv_hotline_AppId,rv_hotline_AppKey,
				 * rm_android_V,rm_android_force, rm_ios_V, rm_ios_force);
				 */

				if (app_i == 6) // White label App
				{
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(iris3_wl_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(iris3_wl_ios_force_V, version));
					}
				} else if (app_i == 5) // Background App
				{
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(rm_bg_android_force, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(rm_bg_ios_force, version));
					}
				} else if (app_i == 4) {
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(rm_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(rm_ios_force_V, version));
					}
				} else if (app_i == 3) {
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(rv_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(rv_ios_force_V, version));
					}
				} else if (app_i == 2) {
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(iris3_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(iris3_ios_force_V, version));
					}
				} else {
					response.put("forceUpdate", "Invalid Version");
				}

			} catch (IOException ex) {
				log.error(ex.getLocalizedMessage());
			}

			response.put("show_download_popup", Boolean.parseBoolean(show_download_popup));
			response.put("show_download_content", "<center><div><p style=\"color: #000;font-size:18px;font-weight:bolder;\">New Version Available</p><p style=\"color: #808080;font-size:15px;\">Experience the new way of Pet Monitoring. Please Download the app & explore.</p></div></center>");
			response.put("show_download_content_flutter_light", "<center><div><p style=\"color: #000;font-size:18px;font-weight:bolder;\">New Version Available</p><p style=\"color: #808080;font-size:15px;\">Experience the new way of Pet Monitoring. Please Download the app & explore.</p></div></center>");
			response.put("show_download_content_flutter_dark", "<center><div><p style=\"color: #fff;font-size:18px;font-weight:bolder;\">New Version Available</p><p style=\"color: #fff;font-size:15px;\">Experience the new way of Pet Monitoring. Please Download the app & explore.</p></div></center>");

			
			if(os.equalsIgnoreCase("iOS"))
				response.put("download_link", "https://itunes.apple.com/in/app/rv-petsafety/id1143805780?platform=iphone&preserveScrollPosition=true#platform/iphone");
			else
				response.put("download_link", "https://play.google.com/store/apps/details?id=com.nimble.petsafety");
			
			response.put("later_download", Boolean.parseBoolean(later_download));
			
			response.put("config", config);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			log.error("GetVersion::::::" + e.getMessage());
			response.put("Status", 1);
			response.put("Msg", "success");
			return response;
		}
	}

	public boolean checkForForceupdate(String forceVersion, String appVersion) {
		log.info("checkForForceupdate : forceVersion = " + forceVersion + "appVersion = " + appVersion);
		/*
		 * int a_version = Integer.parseInt(appVersion.replaceAll("\\.","")); int
		 * f_version = Integer.parseInt(forceVersion.replaceAll("\\.",""));
		 * 
		 * if(f_version > a_version) { log.info("checkForForceupdate:f_version: "
		 * +f_version+" is greater than app-version:" +a_version+" Force update needed"
		 * ); return true; }
		 */
		int result = versionCompare(forceVersion, appVersion);
		if (result > 0)// forceVersion >appVersion
			return true;

		return false;
	}

	/*
	 * The result is a negative integer if str1 is numerically less than str2. The
	 * result is a positive integer if str1 is numerically greater than str2. The
	 * result is zero if the strings are numerically equal
	 */
	public static int versionCompare(String str1, String str2) {
		String[] vals1 = str1.split("\\.");
		String[] vals2 = str2.split("\\.");
		int i = 0;
		// set index to first non-equal ordinal or length of shortest version
		// string
		while (i < vals1.length && i < vals2.length && vals1[i].equals(vals2[i])) {
			i++;
		}

		// compare first non-equal ordinal number
		if (i < vals1.length && i < vals2.length) {

			int diff = Integer.valueOf(vals1[i]).compareTo(Integer.valueOf(vals2[i]));

			return Integer.signum(diff);
		}

		// the strings are equal or one string is a substring of the other
		// e.g. "1.2.3" = "1.2.3" or "1.2.3" < "*******"

		// compare e.g."1.2.3" == "*******"
		if (vals1.length - vals2.length == -1) {

			if (vals2[i].equals("0"))
				return 0;
		} else if (vals1.length - vals2.length == 1) { // compare e.g."*******"
			// == "1.2.3"

			if (vals1[i].equals("0"))
				return 0;
		}

		return Integer.signum(vals1.length - vals2.length);
	}

	public static byte[] zipContent(Object obj) throws IOException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		GZIPOutputStream gzipOut = new GZIPOutputStream(baos);

		Gson gson = new Gson();
		String json = gson.toJson(obj);
		gzipOut.write(json.getBytes());

		gzipOut.close();
		// objectOut.close();
		byte[] bytes = baos.toByteArray();

		return bytes;
	}

	// Controller to call NIOM DB directly for activation
	@RequestMapping(value = "v3.0/activateuser", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse activateUserV2(@ModelAttribute @Valid ActivateUser activateUser,
			BindingResult result, Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info("Activate user:" + activateUser.getEmail() + " OrderId:" + activateUser.getOrderid() + " MobilePage:"
				+ activateUser.getMobilepage() + "QRC : " + activateUser.getQrcCode() + " lat:" + activateUser.getLat()
				+ " lon:" + activateUser.getLon() + " user time zone : " + activateUser.getTime_zone() + " AlreadyUser : "+activateUser.getAlreadyuser());

		String auth = header.getFirst("auth");
		JResponse response = new JResponse();
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", auth);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
		} catch (InvalidAuthoException e) {
			log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			return response;
		}

		return activateUserService.registerProduct(activateUser,result,false);
	}

	public boolean updateOrdersData(String niomGetOrderCountUrl, Orders order, Inventory inventory) {

		boolean status = false;
		Gson gson = new Gson();

		JSONObject jorderIdCheckResponse = new JSONObject();

		String orderIdCheckResponse;
		try {
			orderIdCheckResponse = _helper.getURL(niomGetOrderCountUrl);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(e.getLocalizedMessage());
			return false;
		}

		jorderIdCheckResponse = _helper.getJSONObject(orderIdCheckResponse);

		if (jorderIdCheckResponse != null) {

			int orderIdCheckStatus;
			try {
				orderIdCheckStatus = jorderIdCheckResponse.getInt("Status");
			} catch (JSONException e) {
				// TODO Auto-generated catch block
				log.error(e.getLocalizedMessage());
				return false;
			}

			if (orderIdCheckStatus > 0 ? true : false) {

				try {
					order = gson.fromJson(jorderIdCheckResponse.getJSONObject("Order").toString(), Orders.class);
				} catch (JsonSyntaxException e) {
					// TODO Auto-generated catch block
					log.error(e.getLocalizedMessage());
					return false;
				} catch (JSONException e) {
					// TODO Auto-generated catch block
					log.error(e.getLocalizedMessage());
					return false;
				}
				order.setProvision_status(1);
				order.setEmail_status("111");
				order.setTracking_summary("NA");
				order.setStatus("completed");
				order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
				order.setWelcome_status(order.getWelcome_status());
				if (inventory.getDevicestate().getName().toLowerCase().contains("amazon")) {
					order.setFulfillmentchannel("amazon");
				} else {
					order.setFulfillmentchannel("rv");
				}

				JResponse updateExternalOrderData = updateExternalOrders(order);
				Object updateExternalOrderResponseStatus = updateExternalOrderData.getResponse().get("Status");
				String _updateAmazonOrderMsg = (String) updateExternalOrderData.getResponse().get("Msg");
				String _updateAmazonOrderError = (String) updateExternalOrderData.getResponse().get("ErrorCode");

				if (updateExternalOrderResponseStatus.toString().equals("1")) {
					status = true;
				} else {
					status = false;
				}
			} else {
				return false;
			}
		}

		return status;
	}

	public boolean updateProvisionStaus(String niomIP, String niomAuthKey, User user, Orders order) {

		boolean status = false;

		// String niomUpdateIrisaccountURL = niomIP
		// + _helper.getExternalConfigValue("updateirisaccount",
		// externalConfigService) + niomAuthKey;

		String niomUpdateIrisaccountURL = niomIP + "v1.0/saveorupdateirisuseraccount/" + niomAuthKey;

		niomUpdateIrisaccountURL = niomUpdateIrisaccountURL + "?order_id=" + order.getOrder_id() + "&username="
				+ URLEncoder.encode(user.getUsername()) + "&userid=" + user.getId() + "&pswd="
				+ URLEncoder.encode(user.getPassword());

		JResponse updateIrisAccountResponse = postData(niomUpdateIrisaccountURL);
		Object updateIrisAccountStatus = updateIrisAccountResponse.getResponse().get("Status");

		if (updateIrisAccountStatus.toString().equals("1")) {
			status = true;
		} else {
			status = false;
		}

		return status;
	}

	public JResponse postData(String postURL) {

		JResponse response = new JResponse();

		Gson gson = new Gson();
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpPost post = new HttpPost(postURL);

		post.setHeader("Content-type", "application/json");
		try {
			HttpResponse niomresponse = httpClient.execute(post);
			String niomRes = EntityUtils.toString(niomresponse.getEntity());
			JSONObject _response = new JSONObject();
			Gson _gson = new Gson();
			try {
				JSONObject _res = new JSONObject(niomRes);
				_response = _res.getJSONObject("response");
				int _status = _response.getInt("Status");
				response.put("Status", _status);
				response.put("ErrorCode", "0");
			} catch (JSONException e) {
				response.put("Status", 0);
				response.put("Msg", "Error while getting response from NIOM during pushing order ");
				response.put("ErrorCode", "ER023");
				log.error(e.getLocalizedMessage());
			}
		} catch (ClientProtocolException e) {
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during pushing order ");
			response.put("ErrorCode", "ER022");
			log.error(e.getLocalizedMessage());
		} catch (IOException e) {
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during pushing order ");
			response.put("ErrorCode", "ER024");
			log.error(e.getLocalizedMessage());
		}

		return response;
	}

	public JResponse updateExternalOrders(Orders order) {

		JResponse response = new JResponse();

		String niomPostOrderURL = niomIP + "v1.0/orderV2/" + niomAuthKey;

		Gson gson = new Gson();
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpPost post = new HttpPost(niomPostOrderURL);
		StringEntity postingString = null;
		try {
			postingString = new StringEntity(gson.toJson(order));
		} catch (UnsupportedEncodingException e1) {
			// TODO Auto-generated catch block
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during updating ordermap data. ");

			log.error("Error while getting response from NIOM during updating ordermap data. Exception : -->"
					+ e1.getMessage() + "  Exception --> " + e1.getLocalizedMessage());

			log.error("Response Status : " + 0);
			return response;
		} // gson.tojson() converts your pojo to json
		post.setEntity(postingString);
		post.setHeader("Content-type", "application/json");
		try {
			HttpResponse niomresponse = httpClient.execute(post);
			String niomRes = EntityUtils.toString(niomresponse.getEntity());
			JSONObject _response = new JSONObject();
			Gson _gson = new Gson();
			try {
				JSONObject _res = new JSONObject(niomRes);
				_response = _res.getJSONObject("response");
				int _status = _response.getInt("Status");
				response.put("Status", _status);

				response.put("ErrorCode", "0");
			} catch (JSONException e) {
				response.put("Status", 0);
				response.put("Msg", "Error while updating amazon order");
				response.put("ErrorCode", "ER037");
				log.error(e.getLocalizedMessage());
			}
		} catch (ClientProtocolException e) {
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during pushing order ");
			response.put("ErrorCode", "ER038");
			log.error(e.getLocalizedMessage());
		} catch (IOException e) {
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during pushing order ");
			response.put("ErrorCode", "ER039");
			log.error(e.getLocalizedMessage());
		}

		return response;
	}

	public JResponse activateEseyeSim(String niomActivateEseyeSimURL, String iccids, String tariff, String groupname) {
		JResponse response = new JResponse();

		try {
			niomActivateEseyeSimURL = niomActivateEseyeSimURL + "?iccids=" + URLEncoder.encode(iccids.trim(), "UTF-8")
					+ "&tariff=" + tariff + "&groupname=" + groupname;
			HttpClient httpClient = HttpClientBuilder.create().build();
			HttpPost post = new HttpPost(niomActivateEseyeSimURL);

			post.setHeader("Content-type", "application/json");
			try {
				HttpResponse niomresponse = httpClient.execute(post);
				String niomRes = EntityUtils.toString(niomresponse.getEntity());
				JSONObject _response = new JSONObject();
				Gson _gson = new Gson();
				try {
					JSONObject _res = new JSONObject(niomRes);
					_response = _res.getJSONObject("response");
					int _status = _response.getInt("Status");
					String msg = _response.getString("Msg");
					response.put("Status", _status);
					response.put("Msg", msg);
					response.put("ErrorCode", "0");
				} catch (JSONException e) {
					response.put("Status", 0);
					response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
					response.put("ErrorCode", "ER026");
					log.error(e.getLocalizedMessage());
				}

			} catch (ClientProtocolException e) {
				response.put("Status", 0);
				response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
				response.put("ErrorCode", "ER027");
				log.error("Error " + e.getLocalizedMessage());
			} catch (IOException e) {
				response.put("Status", 0);
				response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
				response.put("ErrorCode", "ER028");
				log.error(e.getLocalizedMessage());
			}

		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
			response.put("ErrorCode", "ER029");
			log.error("Error " + ex.getLocalizedMessage());
		}
		return response;
	}

	public JResponse activateUserValidation(BindingResult result) {
		JResponse response = new JResponse();

		response.put("Status", 0);
		if (result.getFieldError("firstname") != null)
			response.put("Msg", result.getFieldError("name").getDefaultMessage());
		else if (result.getFieldError("lastname") != null)
			response.put("Msg", result.getFieldError("cmptype_id").getDefaultMessage());
		else if (result.getFieldError("cmptype_id") != null)
			response.put("Msg", result.getFieldError("cmptype_id").getDefaultMessage());
		else if (result.getFieldError("isAlreadyUser") != null)
			response.put("Msg", "isAlreadyUser must be true or false");
		else if (result.getFieldError("qrcCode") != null)
			response.put("Msg", "QR Code should not be empty");
		else if (result.getFieldError("throtsettingsid") != null)
			response.put("Msg", result.getFieldError("throtsettingsid").getDefaultMessage());
		else if (result.getFieldError("phoneno") != null)
			response.put("Msg", "Phone Number should not be empty");
		else if (result.getFieldError("email") != null)
			response.put("Msg", "Email should not be empty");
		else if (result.getFieldError("gatewayName") != null) {
			response.put("Msg", "Gateway Name should not be empty");
		} else if (result.getFieldError("purchasedfrom") != null) {
			response.put("Msg", "Purchasedfrom should not be empty");
		}
		return response;
	}

	public long modelID(String deviceModel) {

		int count = 0;

		long modelId = 7;
		int devTempSensorModel = Integer.parseInt(deviceModel.substring(deviceModel.length() - 1));

		String[] device = Arrays.copyOf(deviceModel.split("-"), 2);

		String _deviceModel = device[0] + "-" + device[1];

		List<AssetModel> assetmodelList = fetchDropdownService.getAssetModel();
		for (AssetModel assetModel : assetmodelList) {
			int assetTempSensorModel = Integer
					.parseInt(assetModel.getSensoravailable().substring(assetModel.getSensoravailable().length() - 1));
			if (devTempSensorModel == assetTempSensorModel) {
				if (assetModel.getModel().toLowerCase().trim().contains(_deviceModel.toLowerCase().trim())) {
					modelId = assetModel.getId();
					count++;
				}
			}
		}
		return modelId;
	}

	public boolean nameCheck(Orders order, ActivateUser activateUser) {
		if (!order.getBilling_first_name().equalsIgnoreCase(activateUser.getFirstname().toLowerCase())
				|| !order.getBilling_last_name().equalsIgnoreCase(activateUser.getLastname().toLowerCase())) {
			return true;
		} else {
			return false;
		}

	}

	// updated Service ==========User Signup========
	@RequestMapping(value = "v3.0/resendverificationlink", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse resendVerificationLink(@RequestParam("userid") String userid,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		log.info("Entered resendVerificationLink!");
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");
			try {
				List<User> users = userService.getUserInRole(userid, 2, 2, "");
				User user = null;
				if (users != null && users.size() > 0) {
					user = users.get(0);

					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}

				} else {
					response.put("Status", 0);
					response.put("Msg", "No user found for given ID. Please contact support.");
					return response;
				}
				if (user.isVerified()) {
					response.put("Status", 0);
					response.put("Msg", "User is already verified.");
					return response;
				}

				if (user != null) {
					UserVerification userVerificationToken = userService.createEmailVerificationToken(user);

					mailService.sendVerificationMail(user, userVerificationToken.getToken());
				}
				String msg = "Email verification mail has been sent to " + user.getEmail() + " successfully !";
				response.put("Status", 1);
				response.put("Msg", msg);
			} catch (InvalidUsernameException ex) {
				log.error(ex.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Unable to resend the verification link . Please contact support.");
				return response;
			}
		} catch (ConstraintViolationException ce) {
			log.error("resendVerificationLink: ConstraintViolationException:\n" + ce.getStackTrace());
			response.put("Status", 0);
			response.put("Msg", "Username/Company name already exists");
		} catch (DataIntegrityViolationException ce) {
			log.error("resendVerificationLink: DataIntegrityViolationException:\n" + ce.getStackTrace());
			response.put("Status", 0);
			response.put("Msg", "Username/Company name already exists");
		} catch (Exception e) {
			log.error("resendVerificationLink:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to process the request at this time. Please contact our support team.");

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

}
