package com.nimble.irisservices.appcontroller;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JOrderChannel;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.OrderChannel;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IDropdownServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class DropdownControllerV4App {

	private static final Logger log = LogManager.getLogger(DropdownControllerV4App.class);

	@Autowired
	IUserServiceV4 userServiceV4;

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";

	@Autowired
	@Lazy
	IDropdownServiceV4 dropdownServiceV4;

	@Autowired
	Helper _helper;

	@RequestMapping(value = "v5.0/orderchannelv2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getOrderChannelV5_V2(
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestHeader HttpHeaders header, Authentication authentication,@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver) {
		JResponse response = new JResponse();
		log.info("Entering getOrderChannelV5_V2 : ");
		String auth = header.getFirst("auth");
		try {

			UserV4 user = null;
			try {
				if (!validation_authkey.equals(auth)) {
					user = userServiceV4.verifyAuthV4("authkey", auth);

					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}
				}

			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			List<OrderChannel> orderChannels = dropdownServiceV4.getOrderChannelV4();
			if (orderChannels != null && !orderChannels.isEmpty()) {
				List<OrderChannel> orderChannelListWithSub = orderChannels.stream()
						.filter(OrderChannel::isSub_key).collect(Collectors.toList());

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("orderchannel_withsub", orderChannelListWithSub);
				response.put("orderchannel_withoutsub", orderChannels);

				response.put("Return Time", System.currentTimeMillis());
				return response;

			} else {
				response.put("Status", 0);
				response.put("Msg", "No Order channel found.");
				log.error("No Order channel listed.");

				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting orderchannels.");
			response.put("Error", e.getLocalizedMessage());
			log.error("getOrderChannelV5 : Exception : " + e.getLocalizedMessage());

			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}

	@RequestMapping(value = "v5.0/orderchannel", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getOrderChannelV5(
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestHeader HttpHeaders header, Authentication authentication,@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver) {
		JResponse response = new JResponse();
		log.info("Entering getOrderChannelV5 : ");
		String auth = header.getFirst("auth");
		try {

			UserV4 user = null;
			try {
				if (!validation_authkey.equals(auth)) {
					user = userServiceV4.verifyAuthV4("authkey", auth);

					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}

				}

			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			List<JOrderChannel> jOrderChannelsList = new ArrayList<JOrderChannel>();

			List<OrderChannel> orderChannels = dropdownServiceV4.getOrderChannelV4();

			if (orderChannels != null && orderChannels.size() > 0) {

				for (OrderChannel orderChannel : orderChannels) {

					JOrderChannel _orderChannel = new JOrderChannel();

					_orderChannel.setOrderChannel(orderChannel.getOrderchannel());
					_orderChannel.setShortDescription(orderChannel.getShortdescription());

					jOrderChannelsList.add(_orderChannel);
				}
				response.put("Status", 1);
				response.put("Msg", "Success");

				// response.put("orderChannel", jOrderChannelsList);
				response.put("Orderchannel", orderChannels);

				response.put("Return Time", System.currentTimeMillis());
				return response;

			} else {
				response.put("Status", 0);
				response.put("Msg", "No Order channel found.");
				log.error("No Order channel listed.");

				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting orderchannels.");
			response.put("Error", e.getLocalizedMessage());
			log.error("getOrderChannelV5 : Exception : " + e.getLocalizedMessage());

			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

	}


}
