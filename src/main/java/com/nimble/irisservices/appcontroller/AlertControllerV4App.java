package com.nimble.irisservices.appcontroller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.ocpsoft.prettytime.PrettyTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JAlertV4;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSensorAlert;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AlertWC;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAlertServiceV4;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class AlertControllerV4App {

	private static final Logger log = LogManager.getLogger(AlertControllerV4App.class);

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	IAlertServiceV4 alertServiceV4;
	
	@Autowired
	IAlertCfgService alertCfgService;
	
	@Autowired
	IGatewayService gatewayService;

	@Autowired
	Helper _helper;
	
	PrettyTime prettyTime = new PrettyTime();
	
	@RequestMapping(value = "v5.0/alertV3/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertV3_V5(@RequestParam("monitortype") String monitortype,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "plan_ver", defaultValue = "V2", required = false) String planver,
			@RequestParam(value = "gatewayid", defaultValue = "0", required = false) String gatewayid,
			Authentication authentication, @RequestHeader HttpHeaders header
			) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering getAlertV3_V4 : " + autho);
		try {
			Map<String, String> resMaps = new HashMap<String, String>();

			try {
				resMaps = userServiceV4.getUserId_cmpIdByAuth(autho);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("getAlertV3_V4 : Exception : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(resMaps.get("username"), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (resMaps != null || !resMaps.isEmpty()) {
				long userId = Long.valueOf(resMaps.get("user_id"));
				String tempUnit = resMaps.get("tempunit");

				if(planver != null && planver.equalsIgnoreCase("V3") && Long.valueOf(gatewayid) > 0) {
					List<JAlertV4> alerts = userServiceV4.getUnackAlertsByGateway(userId, tempUnit, monitortype,Long.valueOf(gatewayid));
					response.put("alerts", alerts);
				}else {
					List<JAlertV4> alerts = userServiceV4.getUnackAlerts(userId, tempUnit, monitortype);
					response.put("alerts", alerts);
				}
				

				response.put("Status", 1);
				response.put("Msg", "Success");
				
				response.put("Return Time", System.currentTimeMillis());
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			log.error("getAlertV3_V4 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", " Exception occured ");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}
	
	@GetMapping(value = "v5.0/alertswc", headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertsWC(
			@RequestParam(required = false, defaultValue = "0") long gateway_id,
			@RequestParam String os,
			@RequestParam String app_ver, Authentication authentication,
			@RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		log.info("Entered into getAlertsWC :: auth : "+ header.getFirst("auth") +" :: gateway_id : "+ gateway_id);
		try {
			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session, Please try again");
				log.error("Invalid authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			ArrayList<AlertWC> alertWCList = alertServiceV4.getAlertsWC( gateway_id, user.getId() );
			List<JGateway> userGateway = gatewayService.getGateway(null, null, null, null, user.getId(),
					null);
			
			if( userGateway == null ) {
				log.error("error while getting gateway for user_id : "+ user.getId());
				response.put("Status", 0);
				response.put("Msg", "Invalid Session, Please try again");
				return response;
			}
			
			HashMap< Long, String> gatewayMap = new HashMap<>();
			userGateway.forEach( gatewayInfo -> gatewayMap.put( gatewayInfo.getId() , gatewayInfo.getName()) );
			
			for( AlertWC alertWC : alertWCList) {
				alertWC.setPretty_time( prettyTime.format(_helper.getDate( alertWC.getCreated_on() )) );
				alertWC.setGateway_name( gatewayMap.get( alertWC.getGateway_id() ) );
			}
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("alert_list", alertWCList);
		} catch (Exception e) {
			log.error("Error in getAlertsWC :: Error : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Session, Please try again");
		}
		return response;
	}
		
	@RequestMapping(value = "v5.0/sensoralert/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSensorAlert(@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering getSensorAlert : " + autho);
		try {
			Map<String, String> resMaps = new HashMap<String, String>();

			try {
				resMaps = userServiceV4.getUserId_cmpIdByAuth(autho);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("getSensorAlert : Exception : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(resMaps.get("username"), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (resMaps != null || !resMaps.isEmpty()) {
				long userId = Long.valueOf(resMaps.get("user_id"));

				List<JSensorAlert> alerts = userServiceV4.getSensorAlerts(userId);

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("alerts", alerts);
				response.put("Return Time", System.currentTimeMillis());
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			log.error("getSensorAlert : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", " Exception occured ");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}

}
