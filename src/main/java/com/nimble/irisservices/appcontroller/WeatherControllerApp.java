package com.nimble.irisservices.appcontroller;

import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dto.HttpResponse;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.UserLocation;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.helper.HttpRequest;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;

@RestController
@RequestMapping("/app")
public class WeatherControllerApp {

	private static final Logger log = LogManager.getLogger(WeatherControllerApp.class);

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Autowired
	Helper _helper;

	@Autowired
	HttpRequest httpRequest;
	
	@Autowired
	IGatewayService iGatewayService;
	
	@Autowired
	IGatewayServiceV4 iGatewayServicev4;

	@Value("${weather_url}")
	private String weather_url;

	@Value("${app_id}")
	private String app_id;

	@Value("#{${weather_group_ids}}")
	private Map<Character, Integer> weather_group_ids;

	@GetMapping("v5.0/weathercondition")
	public JResponse getWeatherCondition(@RequestParam(required = false) String os,
			@RequestParam(required = false) String app_ver, @RequestParam(defaultValue = "0.0") String lat,
			@RequestParam(defaultValue = "0.0") String lon, Authentication authentication,
			@RequestHeader HttpHeaders header,
			@RequestParam(defaultValue = "0", required = false) long gateway_id) {

		JResponse response = new JResponse();
		response.put("gateway_id", gateway_id);
		String auth = header.getFirst("auth");
		log.info("Entering getWeatherCondition : ");
		int weather_id = 0;
		String weather_condition = "NA";
		String weather_description = "NA";
		String area = "NA";
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			double latitude = Double.parseDouble(lat);
			double longitude = Double.parseDouble(lon);
			UserLocation userLocation = null;
			boolean gpsDevice = iGatewayServicev4.isGpsDevice(gateway_id);
			if (gpsDevice && gateway_id != 0) {
				double[] latLon = iGatewayService.getDeviceLatLon(gateway_id);
				if (latLon != null && (latLon[0] != 270.0 && latLon[1] != 270.0)) {
					latitude = latLon[0];
					longitude = latLon[1];
				} else {
					latitude = 270.0;
					longitude = 270.0;
				}
			} else {
				gateway_id = 0;
			}
			
			userLocation = userServiceV4.getUserLatLon(user.getId(), gateway_id);
			if (userLocation != null) {
				boolean within24Hrs = _helper.compareDateFor24Hrs(userLocation.getUpdated_on());
				if (within24Hrs) {
					response.put("Status", 1);
					response.put("Msg", "Already sent for today");
					response.put("weather_id", userLocation.getWeather_id());
					response.put("weather_condition", userLocation.getWeather_condition());
					response.put("weather_description", userLocation.getWeather_description());
					response.put("area", userLocation.getArea());
					return response;
				} else {
					if (latitude == 0 && longitude == 0) {
						latitude = userLocation.getLatitude();
						longitude = userLocation.getLongitude();
					}
				}
			} else {
				userLocation = new UserLocation();
			}
			
			if (latitude == 270.0 || longitude == 270.0) {
				response.put("Status", 1);
				response.put("Msg", "Failed");
				response.put("weather_id", weather_id);
				response.put("weather_condition", weather_condition);
				response.put("weather_description", weather_description);
				response.put("area", area);
				return response;
			}

			String weatherUrl = weather_url + "lat=" + latitude + "&lon=" + longitude + "&appid=" + app_id;
			HttpResponse resp = httpRequest.httpReq("Weather API", "GET", weatherUrl, null, null);
			if (resp.getResponse_code() == 200) {
				JSONObject weatherResp = resp.getJsonObject();
				JSONObject sysObj = weatherResp.getJSONObject("sys");
				String country = sysObj.get("country") != null ? sysObj.getString("country") : "US";
				JSONArray weatherArray = weatherResp.getJSONArray("weather");
				if (weatherArray.length() > 0) {
					JSONObject weatherJson = (JSONObject) weatherArray.get(0);
					char idStartChar = weatherJson.get("id").toString().charAt(0);
					if (weatherJson.getInt("id") == 800) {
						weather_id = 7;
					} else {
						weather_id = weather_group_ids.get(idStartChar);
					}

					weather_condition = weatherJson.getString("main");
					weather_description = weatherJson.getString("description");
					area = weatherResp.getString("name");

					if(!country.equalsIgnoreCase("IN")) {
						userLocation.setLatitude(latitude);
						userLocation.setLongitude(longitude);
						userLocation.setUpdated_on(IrisservicesUtil.getCurrentTimeUTC());
						userLocation.setUser_id(user.getId());
						userLocation.setWeather_id(weather_id);
						userLocation.setWeather_condition(weather_condition);
						userLocation.setWeather_description(weather_description);
						userLocation.setArea(area);
						userLocation.setGateway_id(gateway_id);

						async.updateUserLocation(userLocation);
					}

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("weather_id", weather_id);
					response.put("weather_condition", weather_condition);
					response.put("weather_description", weather_description);
					response.put("area", area);
					return response;
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed");
				response.put("weather_id", weather_id);
				response.put("weather_condition", weather_condition);
				response.put("weather_description", weather_description);
				response.put("area", area);
			}

		} catch (Exception e) {
			log.error("Error in getWeatherCondition : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
			response.put("weather_id", weather_id);
			response.put("weather_condition", weather_condition);
			response.put("weather_description", weather_description);
			response.put("area", area);
		}
		return response;
	}

}
