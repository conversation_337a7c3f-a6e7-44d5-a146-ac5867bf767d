package com.nimble.irisservices.appcontroller;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.google.gson.JsonObject;
import com.nimble.irisservices.dto.AlexaTokenSet;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAlexaService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IOAuth2Service;
import com.nimble.irisservices.service.IUserServiceV4;

@RestController
@RequestMapping("/app")
public class AlexaControllerApp {

	private static final Logger log = LogManager.getLogger(AlexaControllerApp.class);

	@Autowired
	IUserServiceV4 userServiceV4;

	@Value("${waggleauth_url}")
	private String waggleAuthUrl;

	@Value("${amazon.alexa.skillid}")
	private String waggleSkillId;

	@Autowired
	Helper _helper = new Helper();

	@Value("${amazon.alexa.state}")
	private String amazonAlexaState;

	@Value("${waggle.universal_url}")
	private String waggleUniversalUrl;

	@Value("${waggle.redirect_uri}")
	private String waggleRedirectUri;

	@Autowired
	IAlexaService alexaService;

	@Autowired
	IOAuth2Service oAuth2Service;
	
	@Autowired
	IAsyncService asyncService;
	
	@RequestMapping(value = "v5.0/enablealexaskill/", method = RequestMethod.POST, headers = "Accept=application/json")
	public JResponse enableAlexaSkillAndLinkAccountV5(@RequestParam String amazonauthcode, @RequestParam String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) throws Exception {
		String autho = header.getFirst("auth");
		log.info("Entered into enableAlexaSkillAndLinkAccount : authkey : " + autho);
		JResponse response = new JResponse();
		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV3("authkey", autho);

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				String username = usr.getUsername(); // AES.encrypt(usr.getUsername());
				String password = usr.getPassword(); // AES.encrypt(usr.getPassword());
				String url = waggleAuthUrl + "/getauthcode?username=" + username + "&password=" + password;

//				OAuth2Authentication oAuth2 = oAuth2Service.getOAuth2(username, password);
//				String waggleAuthCode = oAuth2Service.saveOauth2Code(oAuth2);
				String waggleAuthCode = _helper.httpPOSTRequest(url, null,null);
				log.info("waggle user's auth code : " + waggleAuthCode);

				if (waggleAuthCode == null) {
					log.info("Could not get waggle user authcode : " + waggleAuthCode);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				org.json.JSONObject authCodeObj = new org.json.JSONObject(waggleAuthCode);
				org.json.JSONObject authCodeRes = authCodeObj.getJSONObject("response");

				int status = authCodeRes.getInt("Status");
				if (status == 1 && !authCodeRes.getString("authcode").equalsIgnoreCase("NA")) {
					waggleAuthCode = authCodeRes.getString("authcode");
				} else {
					log.info("Could not get waggle user authcode : " + waggleAuthCode);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				String accessToken = _helper.getAwsAccessToken(amazonauthcode, usr.getId());
				log.info("Amazon access token : " + accessToken);
				if (accessToken == null) {
					log.info("Could not get amazon access token : " + accessToken);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				String alexaUserEndpointResponse = _helper.getAmazonUserEndpointArray(accessToken);
				if (alexaUserEndpointResponse == null) {
					log.info("Could not get user endpoint based on location : " + alexaUserEndpointResponse);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				org.json.JSONObject alexaUserEndpointObj = new org.json.JSONObject(alexaUserEndpointResponse);
				if (!alexaUserEndpointObj.has("endpoints")) {
					log.info("Could not get user endpoint based on location : " + alexaUserEndpointResponse);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				JSONArray alexaUserEndpointArray = alexaUserEndpointObj.getJSONArray("endpoints");
				for (Object baseEndPoint : alexaUserEndpointArray) {
					try {
						String skillActivationApi = "https://" + baseEndPoint + "/v1/users/~current/skills/"
								+ waggleSkillId + "/enablement";

						JsonObject postBody = new JsonObject();
						postBody.addProperty("stage", amazonAlexaState);
						JsonObject accountLinkRequest = new JsonObject();
						accountLinkRequest.addProperty("redirectUri", waggleRedirectUri);
						accountLinkRequest.addProperty("authCode", waggleAuthCode);
						accountLinkRequest.addProperty("type", "AUTH_CODE");
						postBody.add("accountLinkRequest", accountLinkRequest);
						StringEntity postingString = new StringEntity(postBody.toString());
						HttpClient httpclient = HttpClients.createDefault();
						HttpPost httppost = new HttpPost(skillActivationApi);
						httppost.setEntity(postingString);
						httppost.setHeader("Content-type", "application/json");
						httppost.setHeader("Authorization", "Bearer " + accessToken);
						HttpResponse response1 = httpclient.execute(httppost);
						String serviceResponse = EntityUtils.toString(response1.getEntity());
						String enablementResponse = serviceResponse.toString();
						log.info("Skill enablement service response : " + enablementResponse);
						if (enablementResponse.contains("Invalid") || enablementResponse
								.contains("Could not contact provider of account linking credentials")) {
							response.put("Status", 0);
							response.put("Msg", "Alexa linking failed, please try again");
							response.put("is_linked", false);
							response.put("Return Time", System.currentTimeMillis());
							return response;
						} else if (enablementResponse.contains("Incorrect endpoint for customer")) {
							response.put("Status", 0);
							response.put("Msg", "Alexa linking failed, please try again");
							response.put("is_linked", false);
							continue;
						}

						org.json.JSONObject enablementResponseObj = new org.json.JSONObject(enablementResponse);
						if (enablementResponseObj.has("skill")) {
							org.json.JSONObject skillObj = (org.json.JSONObject) enablementResponseObj.get("skill");
							String stage = skillObj.getString("stage");
							log.info("skillStage : " + stage);
							String id = skillObj.getString("id");
							log.info("skillId : " + id);
							org.json.JSONObject userObj = (org.json.JSONObject) enablementResponseObj.get("user");
							String userId = userObj.getString("id");
							log.info("userId : " + userId);
							org.json.JSONObject accountLinkObj = (org.json.JSONObject) enablementResponseObj
									.get("accountLink");
							String accountLinkStatus = accountLinkObj.getString("status");
							log.info("accountLinkStatus : " + accountLinkStatus);

							asyncService.checkAndUpdateGatewayToAlexa( usr.getId() );
							response.put("Status", 1);
							if (accountLinkStatus.equalsIgnoreCase("linked")) {
								response.put("Msg", "Alexa linked successfully");
								response.put("is_linked", true);
							} else {
								response.put("Msg", "Alexa linking failed, please try again");
								response.put("is_linked", false);

							}

							String enableStatus = enablementResponseObj.getString("status");
							log.info("enableStatus : " + enableStatus);
							break;
						} else {
							response.put("Status", 1);
							response.put("Msg", "Alexa linking failed, please try again");
							response.put("is_linked", false);
							log.error("Enablement response doesn't have expected data");
						}
					} catch (Exception e) {
						log.error("Exception occured at enableAlexaSkillAndLinkAccount : " + e.getLocalizedMessage());
						response.put("Status", 0);
						response.put("Msg", "Alexa linking failed, please try again");
						response.put("is_linked", false);
						response.put("Error", e.getLocalizedMessage());
					}
				}
			} catch (InvalidAuthoException e) {
				log.error("InvalidAuthoException :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Alexa linking failed, please try again");
				response.put("is_linked", false);
				response.put("Error", e.getLocalizedMessage());
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Alexa linking failed, please try again");
			response.put("is_linked", false);
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception occured while authorizing user : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/disablealexaskill", method = RequestMethod.POST, headers = "Accept=application/json")
	public JResponse disableAlexaSkillV5(@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) throws Exception {
		log.info("Entered into disableAlexaSkill");
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");

		UserV4 user = null;
		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (InvalidAuthoException e) {
			log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		long userid = user.getId();
		String userName = user.getUsername();

		JResponse errResponse = _helper.validateUser(userName, authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		AlexaTokenSet ats = alexaService.getAlexaTokens(userid);

		if (ats == null) {
			log.info("Could not find user's alexa credentials");
			response.put("Status", 1);
			response.put("Msg", "Alexa not linked, please link");
			response.put("is_linked", false);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		String alexaUserEndpointResponse = _helper.getAmazonUserEndpointArray(ats.getAccessToken());
		if (alexaUserEndpointResponse == null) {
			log.info("Could not get user endpoint based on location : " + alexaUserEndpointResponse);
			response.put("Status", 0);
			response.put("Msg", "Alexa still linked, please unlink");
			response.put("is_linked", true);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		org.json.JSONObject alexaUserEndpointObj = new org.json.JSONObject(alexaUserEndpointResponse);
		if (!alexaUserEndpointObj.has("endpoints")) {
			log.info("Something is wrong while try to get amazon user's endpoint");
			response.put("Status", 0);
			response.put("Msg", "Alexa still linked, please unlink");
			response.put("is_linked", true);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		JSONArray alexaUserEndpointArray = alexaUserEndpointObj.getJSONArray("endpoints");

		for (Object baseEndPoint : alexaUserEndpointArray) {
			HttpURLConnection request = null;
			BufferedReader in = null;
			String disableAlexaSkillUrl = "https://" + baseEndPoint + "/v1/users/~current/skills/" + waggleSkillId
					+ "/enablement";
			try {
				URL url = new URL(disableAlexaSkillUrl);
				request = (HttpURLConnection) url.openConnection();
				request.setRequestMethod("DELETE");
				request.setRequestProperty("Accept", "application/json");
				request.setRequestProperty("Authorization", "Bearer " + ats.getAccessToken());
				request.setDoOutput(true);
				int responseCode = request.getResponseCode();
				StringBuilder sb = new StringBuilder();
				String inputLine;
				if (responseCode >= 400) {
					in = new BufferedReader(new InputStreamReader(request.getErrorStream(), "UTF-8"));
					while ((inputLine = in.readLine()) != null) {
						sb.append(inputLine);
						sb.append("\n");
					}
					in.close();
				} else {
					in = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
					while ((inputLine = in.readLine()) != null) {
						sb.append(inputLine);
						sb.append("\n");
					}
					in.close();
				}
				log.info("HTTP Response received : responseCode : " + responseCode);
				request.disconnect();
				if (responseCode == 204) {
					response.put("Status", 1);
					response.put("Msg", "Alexa unlinked successfully");
					response.put("is_linked", false);
					log.info("Disable skill and unlink account successful.");
				} else {
					response.put("Status", 1);
					response.put("Msg", "Alexa still linked, please unlink");
					response.put("is_linked", true);
					log.info("Failed to disable skill and unlink account.");
				}
			} catch (IOException e) {
				log.error("Exception occured at disableAlexaSkill : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Process failed, try again!");
				response.put("is_linked", true);
			}
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	
	@RequestMapping(value = "v5.0/getaccountlinkingstatus", method = RequestMethod.GET, headers = "Accept=application/json")
	public JResponse getAccountLinkingStatusV5(@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) throws Exception {
		log.info("Entered into getAccountLinkingStatus ");
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			long userid = user.getId();
			log.info(" userId : " + userid);
			AlexaTokenSet ats = alexaService.getAlexaTokens(userid);
			if (ats == null) {
				log.info("Could not find user's alexa credentials");
				response.put("Status", 1);
				response.put("Msg", "Alexa not linked, please link");
				response.put("is_linked", false);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			log.info("Access token : " + ats.getAccessToken());

			String alexaApiEndpoint = "https://api.eu.amazonalexa.com/v1/alexaApiEndpoint";
			String alexaUserEndpointResponse = _helper.httpGETRequest(alexaApiEndpoint,
					"Bearer " + ats.getAccessToken());
			if (alexaUserEndpointResponse == null) {
				log.info("Could not get user endpoint based on location : " + alexaUserEndpointResponse);
				response.put("Status", 0);
				response.put("Msg", "Alexa not linked, please link");
				response.put("is_linked", false);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			org.json.JSONObject alexaUserEndpointObj = new org.json.JSONObject(alexaUserEndpointResponse);

			if (!alexaUserEndpointObj.has("endpoints")) {
				log.info("Something is wrong while try to get amazon user's endpoint");
				response.put("Status", 0);
				response.put("Msg", "Alexa not linked, please link");
				response.put("is_linked", false);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			JSONArray alexaUserEndpointArray = alexaUserEndpointObj.getJSONArray("endpoints");

			for (Object baseEndPoint : alexaUserEndpointArray) {
				try {
					String accLinkingStatusUrl = "https://" + baseEndPoint + "/v1/users/~current/skills/"
							+ waggleSkillId + "/enablement";
					String endPointResponse = _helper.httpGETRequest(accLinkingStatusUrl,
							"Bearer " + ats.getAccessToken());

//				{"skill":{"stage":"development","id":"amzn1.ask.skill.4a03c200-fa57-4976-a5d6-b0526c6fac3f"},"user":{"id":"amzn1.ask.account.AHV5WHOYTVVK7P3YG6EITK5VHZZU4GEBAC2TM5AOPRZJJYIIBYSTCRHI673LGW2PI3VLWCP2UIQHWDWFGXXFRIATJK33POHSHRPPD3LASIU2YDBISFUS445SKILHORN2QL76WOMH2ZDMOTQ4LLVQGZUTUZB4TP3YPXTI52D2RZVCGNI7FNJTRYYYORRAU5JNBLPC66GUHLB64AI"},"accountLink":{"status":"LINKED"},"status":"ENABLED"}
					org.json.JSONObject enablementResponseObj = new org.json.JSONObject(endPointResponse);

					if (enablementResponseObj.has("skill")) {
						org.json.JSONObject skillObj = (org.json.JSONObject) enablementResponseObj.get("skill");
						String stage = skillObj.getString("stage");
						log.info("skillStage : " + stage);
						String id = skillObj.getString("id");
						log.info("skillId : " + id);
						org.json.JSONObject userObj = (org.json.JSONObject) enablementResponseObj.get("user");
						String userId = userObj.getString("id");
						log.info("userId : " + userId);
						org.json.JSONObject accountLinkObj = (org.json.JSONObject) enablementResponseObj
								.get("accountLink");
						String accountLinkStatus = accountLinkObj.getString("status");
						log.info("accountLinkStatus : " + accountLinkStatus);
						response.put("Status", 1);
						if (accountLinkStatus.equalsIgnoreCase("linked")) {
							response.put("Msg", "Alexa linked successfully");
							response.put("is_linked", true);
						} else {
							response.put("Msg", "Alexa not linked, please link");
							response.put("is_linked", false);
						}
						String enableStatus = enablementResponseObj.getString("status");
						log.info("enableStatus : " + enableStatus);
						response.put("Return Time", System.currentTimeMillis());
						return response;
					} else if (enablementResponseObj.has("message")) {
						String message = enablementResponseObj.getString("message");
						log.info("Response from alexa about skill status, message : " + message);
						response.put("Status", 1);
						response.put("Msg", "Alexa not linked, please link");
						response.put("is_linked", false);
						response.put("Return Time", System.currentTimeMillis());
						return response;
					}
				} catch (Exception e) {
					log.error("Error while getting Account Linking Status : " + e.getLocalizedMessage());
				}
			}
			response.put("Status", 0);
			response.put("Msg", "Alexa not linked, please link");
			response.put("is_linked", false);
			log.error("Enablement response doesn't have expected data");
		} catch (Exception e) {
			log.error("Error occured at getAccountLinkingStatus : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/enablealexaskill/", method = RequestMethod.POST, headers = "Accept=application/json")
	public JResponse enableAlexaSkillAndLinkAccount(@RequestParam String amazonauthcode, @RequestParam String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) throws Exception {
		String autho = header.getFirst("auth");
		log.info("Entered into enableAlexaSkillAndLinkAccount : authkey : " + autho);
		JResponse response = new JResponse();
		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV3("authkey", autho);

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				String username = usr.getUsername(); // AES.encrypt(usr.getUsername());
				String password = usr.getPassword(); // AES.encrypt(usr.getPassword());
				String url = waggleAuthUrl + "/getauthcode?username=" + username + "&password=" + password;

//				OAuth2Authentication oAuth2 = oAuth2Service.getOAuth2(username, password);
//				String waggleAuthCode = oAuth2Service.saveOauth2Code(oAuth2);
				String waggleAuthCode = _helper.httpPOSTRequest(url, null,null);
				log.info("waggle user's auth code : " + waggleAuthCode);

				if (waggleAuthCode == null) {
					log.info("Could not get waggle user authcode : " + waggleAuthCode);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				org.json.JSONObject authCodeObj = new org.json.JSONObject(waggleAuthCode);
				org.json.JSONObject authCodeRes = authCodeObj.getJSONObject("response");

				int status = authCodeRes.getInt("Status");
				if (status == 1 && !authCodeRes.getString("authcode").equalsIgnoreCase("NA")) {
					waggleAuthCode = authCodeRes.getString("authcode");
				} else {
					log.info("Could not get waggle user authcode : " + waggleAuthCode);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				String accessToken = _helper.getAwsAccessToken(amazonauthcode, usr.getId());
				log.info("Amazon access token : " + accessToken);
				if (accessToken == null) {
					log.info("Could not get amazon access token : " + accessToken);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				String alexaUserEndpointResponse = _helper.getAmazonUserEndpointArray(accessToken);
				if (alexaUserEndpointResponse == null) {
					log.info("Could not get user endpoint based on location : " + alexaUserEndpointResponse);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				org.json.JSONObject alexaUserEndpointObj = new org.json.JSONObject(alexaUserEndpointResponse);
				if (!alexaUserEndpointObj.has("endpoints")) {
					log.info("Could not get user endpoint based on location : " + alexaUserEndpointResponse);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				JSONArray alexaUserEndpointArray = alexaUserEndpointObj.getJSONArray("endpoints");
				for (Object baseEndPoint : alexaUserEndpointArray) {
					try {
						String skillActivationApi = "https://" + baseEndPoint + "/v1/users/~current/skills/"
								+ waggleSkillId + "/enablement";

						JsonObject postBody = new JsonObject();
						postBody.addProperty("stage", amazonAlexaState);
						JsonObject accountLinkRequest = new JsonObject();
						accountLinkRequest.addProperty("redirectUri", waggleRedirectUri);
						accountLinkRequest.addProperty("authCode", waggleAuthCode);
						accountLinkRequest.addProperty("type", "AUTH_CODE");
						postBody.add("accountLinkRequest", accountLinkRequest);
						StringEntity postingString = new StringEntity(postBody.toString());
						HttpClient httpclient = HttpClients.createDefault();
						HttpPost httppost = new HttpPost(skillActivationApi);
						httppost.setEntity(postingString);
						httppost.setHeader("Content-type", "application/json");
						httppost.setHeader("Authorization", "Bearer " + accessToken);
						HttpResponse response1 = httpclient.execute(httppost);
						String serviceResponse = EntityUtils.toString(response1.getEntity());
						String enablementResponse = serviceResponse.toString();
						log.info("Skill enablement service response : " + enablementResponse);
						if (enablementResponse.contains("Invalid") || enablementResponse
								.contains("Could not contact provider of account linking credentials")) {
							response.put("Status", 0);
							response.put("Msg", "Alexa linking failed, please try again");
							response.put("is_linked", false);
							response.put("Return Time", System.currentTimeMillis());
							return response;
						} else if (enablementResponse.contains("Incorrect endpoint for customer")) {
							response.put("Status", 0);
							response.put("Msg", "Alexa linking failed, please try again");
							response.put("is_linked", false);
							continue;
						}

						org.json.JSONObject enablementResponseObj = new org.json.JSONObject(enablementResponse);
						if (enablementResponseObj.has("skill")) {
							org.json.JSONObject skillObj = (org.json.JSONObject) enablementResponseObj.get("skill");
							String stage = skillObj.getString("stage");
							log.info("skillStage : " + stage);
							String id = skillObj.getString("id");
							log.info("skillId : " + id);
							org.json.JSONObject userObj = (org.json.JSONObject) enablementResponseObj.get("user");
							String userId = userObj.getString("id");
							log.info("userId : " + userId);
							org.json.JSONObject accountLinkObj = (org.json.JSONObject) enablementResponseObj
									.get("accountLink");
							String accountLinkStatus = accountLinkObj.getString("status");
							log.info("accountLinkStatus : " + accountLinkStatus);

							response.put("Status", 1);
							if (accountLinkStatus.equalsIgnoreCase("linked")) {
								response.put("Msg", "Alexa linked successfully");
								response.put("is_linked", true);
							} else {
								response.put("Msg", "Alexa linking failed, please try again");
								response.put("is_linked", false);

							}

							String enableStatus = enablementResponseObj.getString("status");
							log.info("enableStatus : " + enableStatus);
							break;
						} else {
							response.put("Status", 1);
							response.put("Msg", "Alexa linking failed, please try again");
							response.put("is_linked", false);
							log.error("Enablement response doesn't have expected data");
						}
					} catch (Exception e) {
						log.error("Exception occured at enableAlexaSkillAndLinkAccount : " + e.getLocalizedMessage());
						response.put("Status", 0);
						response.put("Msg", "Alexa linking failed, please try again");
						response.put("is_linked", false);
						response.put("Error", e.getLocalizedMessage());
					}
				}
			} catch (InvalidAuthoException e) {
				log.error("InvalidAuthoException :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Alexa linking failed, please try again");
				response.put("is_linked", false);
				response.put("Error", e.getLocalizedMessage());
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Alexa linking failed, please try again");
			response.put("is_linked", false);
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception occured while authorizing user : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/getaccountlinkingstatus", method = RequestMethod.GET, headers = "Accept=application/json")
	public JResponse getAccountLinkingStatus(@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) throws Exception {
		log.info("Entered into getAccountLinkingStatus ");
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			long userid = user.getId();
			log.info(" userId : " + userid);
			AlexaTokenSet ats = alexaService.getAlexaTokens(userid);
			if (ats == null) {
				log.info("Could not find user's alexa credentials");
				response.put("Status", 1);
				response.put("Msg", "Alexa not linked, please link");
				response.put("is_linked", false);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			log.info("Access token : " + ats.getAccessToken());

			String alexaApiEndpoint = "https://api.eu.amazonalexa.com/v1/alexaApiEndpoint";
			String alexaUserEndpointResponse = _helper.httpGETRequest(alexaApiEndpoint,
					"Bearer " + ats.getAccessToken());
			if (alexaUserEndpointResponse == null) {
				log.info("Could not get user endpoint based on location : " + alexaUserEndpointResponse);
				response.put("Status", 0);
				response.put("Msg", "Alexa not linked, please link");
				response.put("is_linked", false);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			org.json.JSONObject alexaUserEndpointObj = new org.json.JSONObject(alexaUserEndpointResponse);

			if (!alexaUserEndpointObj.has("endpoints")) {
				log.info("Something is wrong while try to get amazon user's endpoint");
				response.put("Status", 0);
				response.put("Msg", "Alexa not linked, please link");
				response.put("is_linked", false);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			JSONArray alexaUserEndpointArray = alexaUserEndpointObj.getJSONArray("endpoints");

			for (Object baseEndPoint : alexaUserEndpointArray) {
				try {
					String accLinkingStatusUrl = "https://" + baseEndPoint + "/v1/users/~current/skills/"
							+ waggleSkillId + "/enablement";
					String endPointResponse = _helper.httpGETRequest(accLinkingStatusUrl,
							"Bearer " + ats.getAccessToken());

//				{"skill":{"stage":"development","id":"amzn1.ask.skill.4a03c200-fa57-4976-a5d6-b0526c6fac3f"},"user":{"id":"amzn1.ask.account.AHV5WHOYTVVK7P3YG6EITK5VHZZU4GEBAC2TM5AOPRZJJYIIBYSTCRHI673LGW2PI3VLWCP2UIQHWDWFGXXFRIATJK33POHSHRPPD3LASIU2YDBISFUS445SKILHORN2QL76WOMH2ZDMOTQ4LLVQGZUTUZB4TP3YPXTI52D2RZVCGNI7FNJTRYYYORRAU5JNBLPC66GUHLB64AI"},"accountLink":{"status":"LINKED"},"status":"ENABLED"}
					org.json.JSONObject enablementResponseObj = new org.json.JSONObject(endPointResponse);

					if (enablementResponseObj.has("skill")) {
						org.json.JSONObject skillObj = (org.json.JSONObject) enablementResponseObj.get("skill");
						String stage = skillObj.getString("stage");
						log.info("skillStage : " + stage);
						String id = skillObj.getString("id");
						log.info("skillId : " + id);
						org.json.JSONObject userObj = (org.json.JSONObject) enablementResponseObj.get("user");
						String userId = userObj.getString("id");
						log.info("userId : " + userId);
						org.json.JSONObject accountLinkObj = (org.json.JSONObject) enablementResponseObj
								.get("accountLink");
						String accountLinkStatus = accountLinkObj.getString("status");
						log.info("accountLinkStatus : " + accountLinkStatus);
						response.put("Status", 1);
						if (accountLinkStatus.equalsIgnoreCase("linked")) {
							response.put("Msg", "Alexa linked successfully");
							response.put("is_linked", true);
						} else {
							response.put("Msg", "Alexa not linked, please link");
							response.put("is_linked", false);
						}
						String enableStatus = enablementResponseObj.getString("status");
						log.info("enableStatus : " + enableStatus);
						response.put("Return Time", System.currentTimeMillis());
						return response;
					} else if (enablementResponseObj.has("message")) {
						String message = enablementResponseObj.getString("message");
						log.info("Response from alexa about skill status, message : " + message);
						response.put("Status", 1);
						response.put("Msg", "Alexa not linked, please link");
						response.put("is_linked", false);
						response.put("Return Time", System.currentTimeMillis());
						return response;
					}
				} catch (Exception e) {
					log.error("Error while getting Account Linking Status : " + e.getLocalizedMessage());
				}
			}
			response.put("Status", 0);
			response.put("Msg", "Alexa not linked, please link");
			response.put("is_linked", false);
			log.error("Enablement response doesn't have expected data");
		} catch (Exception e) {
			log.error("Error occured at getAccountLinkingStatus : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/disablealexaskill", method = RequestMethod.POST, headers = "Accept=application/json")
	public JResponse disableAlexaSkill(@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) throws Exception {
		log.info("Entered into disableAlexaSkill");
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");

		UserV4 user = null;
		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (InvalidAuthoException e) {
			log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		long userid = user.getId();
		String userName = user.getUsername();

		JResponse errResponse = _helper.validateUser(userName, authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		AlexaTokenSet ats = alexaService.getAlexaTokens(userid);

		if (ats == null) {
			log.info("Could not find user's alexa credentials");
			response.put("Status", 1);
			response.put("Msg", "Alexa not linked, please link");
			response.put("is_linked", false);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		String alexaUserEndpointResponse = _helper.getAmazonUserEndpointArray(ats.getAccessToken());
		if (alexaUserEndpointResponse == null) {
			log.info("Could not get user endpoint based on location : " + alexaUserEndpointResponse);
			response.put("Status", 0);
			response.put("Msg", "Alexa still linked, please unlink");
			response.put("is_linked", true);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		org.json.JSONObject alexaUserEndpointObj = new org.json.JSONObject(alexaUserEndpointResponse);
		if (!alexaUserEndpointObj.has("endpoints")) {
			log.info("Something is wrong while try to get amazon user's endpoint");
			response.put("Status", 0);
			response.put("Msg", "Alexa still linked, please unlink");
			response.put("is_linked", true);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		JSONArray alexaUserEndpointArray = alexaUserEndpointObj.getJSONArray("endpoints");

		for (Object baseEndPoint : alexaUserEndpointArray) {
			HttpURLConnection request = null;
			BufferedReader in = null;
			String disableAlexaSkillUrl = "https://" + baseEndPoint + "/v1/users/~current/skills/" + waggleSkillId
					+ "/enablement";
			try {
				URL url = new URL(disableAlexaSkillUrl);
				request = (HttpURLConnection) url.openConnection();
				request.setRequestMethod("DELETE");
				request.setRequestProperty("Accept", "application/json");
				request.setRequestProperty("Authorization", "Bearer " + ats.getAccessToken());
				request.setDoOutput(true);
				int responseCode = request.getResponseCode();
				StringBuilder sb = new StringBuilder();
				String inputLine;
				if (responseCode >= 400) {
					in = new BufferedReader(new InputStreamReader(request.getErrorStream(), "UTF-8"));
					while ((inputLine = in.readLine()) != null) {
						sb.append(inputLine);
						sb.append("\n");
					}
					in.close();
				} else {
					in = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
					while ((inputLine = in.readLine()) != null) {
						sb.append(inputLine);
						sb.append("\n");
					}
					in.close();
				}
				log.info("HTTP Response received : responseCode : " + responseCode);
				request.disconnect();
				if (responseCode == 204) {
					response.put("Status", 1);
					response.put("Msg", "Alexa unlinked successfully");
					response.put("is_linked", false);
					log.info("Disable skill and unlink account successful.");
				} else {
					response.put("Status", 1);
					response.put("Msg", "Alexa still linked, please unlink");
					response.put("is_linked", true);
					log.info("Failed to disable skill and unlink account.");
				}
			} catch (IOException e) {
				log.error("Exception occured at disableAlexaSkill : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Process failed, try again!");
				response.put("is_linked", true);
			}
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

}
