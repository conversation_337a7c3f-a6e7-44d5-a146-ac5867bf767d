package com.nimble.irisservices.appcontroller;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.nimble.irisservices.Util.SecretManagerService;
import com.nimble.irisservices.entity.*;
import com.nimble.irisservices.helper.Email;
import com.nimble.irisservices.service.SMSInterface;
import com.nimble.irisservices.service.impl.Plivo;
import freemarker.template.Template;
import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.model.AddressComponent;
import com.google.maps.model.AddressComponentType;
import com.google.maps.model.ComponentFilter;
import com.google.maps.model.GeocodingResult;
import com.nimble.irisservices.constant.CountryCode;
import com.nimble.irisservices.dao.IGatewayDao;
import com.nimble.irisservices.dto.Configuration;
import com.nimble.irisservices.dto.ExternalLogin;
import com.nimble.irisservices.dto.JBacking;
import com.nimble.irisservices.dto.JCategory;
import com.nimble.irisservices.dto.JOrderChannel;
import com.nimble.irisservices.dto.JPetprofile;
import com.nimble.irisservices.dto.JRegisterDevice;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSensorType;
import com.nimble.irisservices.dto.JValidateString;
import com.nimble.irisservices.dto.SignUp;
import com.nimble.irisservices.dto.UpdatePassword;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.CompanyType;
import com.nimble.irisservices.entity.CountryCodeV4;
import com.nimble.irisservices.entity.ForceUpdate;
import com.nimble.irisservices.entity.Groups;
import com.nimble.irisservices.entity.OrderChannel;
import com.nimble.irisservices.entity.SignupType;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserVerification;
import com.nimble.irisservices.entity.VersionMapping;
import com.nimble.irisservices.entity.ZipCodeDetails;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.EmailContent;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.service.IActivateUserService;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAlertCfgServiceV4;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICommonService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICompanyTypeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IDropdownServiceV4;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IGroupServices;
import com.nimble.irisservices.service.IMailService;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IOAuth2Service;
import com.nimble.irisservices.service.IPetSpeciesServicesV4;
import com.nimble.irisservices.service.IRVCentricDetailsService;
import com.nimble.irisservices.service.IRechargeService;
import com.nimble.irisservices.service.IRegisterDeviceService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IThrottlingService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IZipCodeDetailsService;

@RestController
@RequestMapping("/ext")
public class ExternalControllerV5 {

	private static final Logger log = LogManager.getLogger(ExternalControllerV5.class);
	
	@Autowired
	ICompanyTypeService companyTypeServ;

	@Autowired
	SecretManagerService secretManagerService;

	@Value("${aws_s3_secret_name}")
	private String S3_SECRET_NAME;

	@Autowired
	IThrottlingService throttlingService;

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IUserServiceV4 userServV4;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IGatewayDao gatewayDao;

	@Autowired
	freemarker.template.Configuration templates;

	@Autowired
	@Lazy
	IGroupServices groupservices;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	@Autowired
	@Lazy
	IAlertCfgService alertCfgService;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Autowired
	IRVCentricDetailsService rvServ;

	@Autowired
	@Lazy
	IReportService reportService;

	@Autowired
	@Lazy
	IZipCodeDetailsService iZipCodeDetailsService;

	@Autowired
	IMailService mailService;

	@Autowired
	IRVCentricDetailsService rvcentricServ;

	@Autowired
	@Lazy
	IDropdownServiceV4 dropdownServiceV4;

	@Autowired
	IOAuth2Service oAuth2Service;

	@Autowired
	@Lazy
	ICreditSystemService crService;
	
	@Autowired
	@Lazy
	IPetSpeciesServicesV4 petSpeciesServicesv4;

	@Autowired
	private SMSInterface sms;

	@Value("${show_orderid_popup_while_register}")
	private boolean show_warranty_popup_config;

	@Value("${warranty_msg}")
	private String warranty_msg;

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;

	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;

	@Value("${add_free_vpm}")
	private boolean addFreeVpmFlag;

	@Value("${vpm_freedays}")
	private String freeVPM_days;

	@Value("${google.api.key}")
	private String googleAPIkey;

	@Value("${config.oauth2.clientid.app}")
	private String clientIdApp;

	@Value("${config.oauth2.clientsecret.app}")
	private String clientSecretApp;

	@Value("${config.oauth2.clientid.web}")
	private String clientIdWeb;

	@Value("${config.oauth2.clientsecret.web}")
	private String clientSecretWeb;

	@Value("${enable_call_support}")
	private Boolean enable_call_support;

	@Autowired
	Helper _helper;

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";

	@Value("#{${terms_conditions}}")
	private Map<String,String>  terms_conditions;

	@Value("#{${Privacy_policy}}")
	private Map<String,String> privacy_policy;

	@Value("${nimbleauthkey}")
	private String nimbleAuthKey;

	@Value("${Facebook}")
	private String facebook;

	@Value("${amplitude_andriod}")
	private String amplitude_andriod;

	@Value("${amplitude_ios}")
	private String amplitude_ios;

	private boolean enablegoogle = true;

	private boolean enablefb = true;

	private boolean enableapple = true;

	private boolean enable_tips = true;

	private String plan_version = "V1";

	@Value("${plivono}")
	private String plivoNumber;

	@Value("${valid_minutes_for_OTP}")
	private int validMinutesForOTP;

	@Value("${return.login.username}")
	private String returnLoginUsername;

	@Value("${subscription.buynow.popup}")
	private boolean subscriptionBuyNowPopUp_config;

	@Value("${subscription.buynow.content}")
	private String subscriptionBuyNowContent;

	@Value("${chatbot}")
	private boolean chatbot;

	@Value("${check.recall.device.qrc}")
	private boolean checkRecallQRC;

	@Value("${product_subs_enable}")
	private boolean product_subs_enable;

	@Value("${debug_otp}")
	private int debug_otp;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;

	@Value("${showpopupregisteration}")
	private boolean showPopUpRegisteration;

	private boolean show_orderid = true;

	@Value("${show.subs.page.first.config}")
	private boolean show_subs_page_first_config = true;

	@Value("${redirect.payment.page}")
	private boolean redirectPaymentPage_config = false;

	@Value("${redirect.payment.page.content}")
	private String redirectPaymentPageContent;

	@Value("${orderid.later.popup}")
	private boolean orderid_later_popup_config = false;

	@Value("${orderid.later.popup.content}")
	private String orderid_later_popup_content;

	@Value("${redirectWebSubs}")
	private boolean redirectWebSubs;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IChargebeeService cbService;
	
	@Autowired
	@Lazy
	IMessagingService  messagingService;
	
	@Autowired
	@Lazy
	IAlertCfgServiceV4 alertCfgServiceV4;
	
	@Autowired
	@Lazy
	IActivateUserService activateUserService;
	
	@Autowired
	@Lazy
	ICommonService commonService;
	
	@Autowired
	IUserServiceV4 userServicev4;
	
	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;
	
	@Value("${freeplan}")
	private String freeplan;
	
	@Value("${omitplan}")
	private String omitplan;

	@Value("${vpmplan}")
	private String vpmplan;

	@Value("${addonplan}")
	private String addonplan;

	@Value("${purchased_from}")
	private boolean purchased_from;
	
	@Value("${show_download_popup}")
	private String show_download_popup;
	
	@Value("${later_download}")
	private String later_download;
	
	@Value("${mixpanel_token}")
	private String mixpanel_token;
	
	@Value("${kcalUsername}")
	private String kcalUsername;
	
	@Value("${kcalPassword}")
	private String kcalPassword;

	@Value("${enablePowerBack}")
	private boolean enablePowerBack;

	@Value("${powerBackUUID}")
	private String powerBackUUID;
	
	@Autowired
	@Lazy
	IRechargeService reService;
	
	@Autowired
	IRegisterDeviceService registerDeviceService;
	
	//v5
	@RequestMapping(value = "v5.0/usersignup", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse userSignupV5(@RequestBody @Valid SignUp signUp, 
			BindingResult result,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestHeader HttpHeaders header,
			@RequestParam(value = "time_zone", defaultValue = "", required = false) String timeZone,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		JResponse response = new JResponse();
		log.info("Entered into usersignup :: username : "+ signUp.getEmail());
		log.info("signup request body :: username : "+ signUp.getEmail() +" :: body : "+ signUp.toString());
		log.info("user time zone :" + timeZone);
		Company company = null;
		try {

			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidation(backing);

			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (!validation_authkey.equals(jBacking.getAuthKey())) {
				response.put("Status", -2);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String email = signUp.getEmail().toLowerCase().trim();
			email = email.replaceAll("\\s", "");

			try {
				User user = userService.getUserByUNameOrEmail(email);

				if (user != null) {
					response.put("Status", 0);
					response.put("Msg", "Email id already found. Please use different email id");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			} catch (Exception ex) {
				log.error("Sign up : Email Id not found . Create user account for user : "+ex.getLocalizedMessage());
			}

			// Assign Default things
			int rand = _helper.getRandomNumber(100, 9999999);

			signUp.setUsername(email);
			signUp.setCompanyname(signUp.getFirstname() + "_" + rand);
			signUp.setSupervisor("Nimble");
			signUp.setThrotsettingsid("5");
			signUp.setCmptype_id("3");
			signUp.setAddress("NA");
			signUp.setPhoneno(signUp.getMobileno());

			String mobileNo = signUp.getMobileno();

			if (!mobileNo.contains("-") && mobileNo.length() > 0) {

				mobileNo = new CountryCode().getCountryCode(signUp.getCountry().toUpperCase()) + signUp.getMobileno();
				signUp.setPhoneno(mobileNo);
				signUp.setMobileno(mobileNo);
			}

			JValidateString validString = userServiceV4.checkAlphabetOnly(signUp.getFirstname(), signUp.getLastname());
			if (!validString.isValid()) {
				response.put("Status", 0);
				response.put("Msg", validString.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String password = signUp.getPassword();
			if( !signUp.getPassword_decode_ver().equalsIgnoreCase("V3") ) {
				if (signUp.getPassword_decode_ver().equalsIgnoreCase("V2")) {
					password = _helper.base64Decoder(signUp.getPassword());
					if (password == null) {
						response.put("Status", 0);
						response.put("Msg", "Invalid Session. Please try again later");
						response.put("Return Time", System.currentTimeMillis());
						return response;
					}
				}

				JValidateString validatePassword = userServiceV4.validatePassword(password);
				if (!validatePassword.isValid()) {
					response.put("Status", 0);
					response.put("Msg", validatePassword.getMsg());
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				signUp.setPassword(_helper.bCryptEncoder(password));
			}
			
			long timeMilli = Calendar.getInstance().getTimeInMillis();
			String cmpName = signUp.getCompanyname() + "-" + timeMilli;
			signUp.setCompanyname(cmpName);
			
			try {
				ThrottlingSettings throtsettings = throttlingService.getThrotSettings(signUp.getThrotsettingsid()).get(0);

				CompanyType cmpType = companyTypeServ.getCompanyType(signUp.getCmptype_id(), 1).get(0);
				company = companyService.createCompany(signUp,throtsettings,cmpType);
			} catch (Exception e) {
				log.error("Error in creating the company . Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Not able to process the request, Please try later");
				return response;
			}

			CompanyConfig cfg = new CompanyConfig(company);
			/*
			 * Set temperature unit as celcius
			 */ if (signUp.getCountry().equalsIgnoreCase("AU"))
				cfg.setTemperatureunit("C");
			
			boolean companyConfigCreationStatus = companyService.saveCompanyConfg(cfg);
			log.info("Company Config Creation Status : "+companyConfigCreationStatus);
			
			if( !companyConfigCreationStatus && company.getId()!=0 ) {
				companyService.deleteCompany(company.getId());
			}

			Groups group = new Groups();
			group.setName("Default");
			boolean groupCreationStatus = groupservices.saveORupdateGroups(group, company.getId());
			
			if( !groupCreationStatus & company!=null ) {
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
			
			boolean res = userService.signUp(signUp,company);

			if (res) {
//				User user = userService.getUserByName(email);
				UserV4 userV4 = userServiceV4.verifyAuthV3("username", email);
				UserVerification userVerificationToken = userService.createEmailVerificationTokenV2(userV4.getId());

				if (userVerificationToken.getStatus().equalsIgnoreCase("pending")) {
					mailService.sendVerificationMailV2(userV4, userVerificationToken.getToken());
				}

				// Updating chargebee customerid / creating customer details in chargebee
				// updateChargebeeUser(user);
				String chargebeeId = "NA";
				if (userV4.getChargebeeid().trim().equalsIgnoreCase("NA") || userV4.getChargebeeid() == null)
					chargebeeId = userServicev4.createUserInChargebee(userV4.getFirstname(), userV4.getLastname(), userV4.getEmail(),
							userV4.getMobileno(), userV4.getUsername(), 0, "NA");

//				try {
//					JResponse rechargeResponse = reService.createRechargeSubscription(userV4.getUsername(), chargebeeId, userV4.getId(), "NA");
//					
//					if( (boolean) rechargeResponse.get("is_recharge") ) {
//						boolean is_recharge = (boolean) rechargeResponse.get("is_recharge");
//						String subs_status = (String) rechargeResponse.get("subs_status");
//						ArrayList<String> subDetails = (ArrayList<String>) rechargeResponse.get("sub_details");
//						String cb_status = (String) rechargeResponse.get("cb_status");
//						
//						
//						if( is_recharge ) {
//							String to_address = null;
//							String cc_address = null;
//							String bcc_address = null;
//							
//							Properties prop = new Properties();
//							File file = ResourceUtils.getFile("classpath:iris3.properties");
//							prop.load(new FileInputStream(file));
//
//							to_address = prop.getProperty("to_address");
//							cc_address = prop.getProperty("cc_address");
//							bcc_address = prop.getProperty("bcc_address");
//							
//							String mailSub = "Signup With Recharge Subscription : "+userV4.getUsername();
//							String mailContent = "<p>Hi Team,</p>" + "<p>Find the recharge subscription details</p>";
//							
//							mailContent = mailContent + "<p> Name : "+ userV4.getFirstname() +" "+ userV4.getLastname() +" </p>";
//							mailContent = mailContent + "<p> Email : "+ userV4.getEmail() +" </p>";
//							mailContent = mailContent + "<p> Recharge customer : true </p>";
//							mailContent = mailContent + "<p> Order Id : "+ subDetails.get(6) +"</p>";
//							
//							if( cb_status.contains("Sub_Created") ) {
//								mailSub = "Sucess : " + mailSub ;
//								mailContent = mailContent + "<p> Recharge subscription created in CB </p>";
//								mailContent = mailContent + "<p> Plan : "+ subDetails.get(1) +"</p>";
//							} else {
//								mailSub = "Failed : " + mailSub ;
//								mailContent = mailContent + "<p> <p> Paid Subcription already available, So recharge bundle subscription not created. In order to activate bundle subscription need to activate manually in CB</p>";
//							}
//							
//							mailContent = mailContent + "<br>Thanks,<br> Irisservice ";
//							async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
//						}
//						
//					}	
//				} catch (Exception e) {
//					log.error("Error while processing recharge subscription :: Error : "+e.getLocalizedMessage());
//				}
				
				String cb_planid = "chum";

//				boolean stat = rvcentricServ.saveUserBadgeTxn(userV4.getId(), cb_planid, 0, false, "NA");
//				log.info("in user signup:" + userV4.getId() + " Badge created:" + stat);

				if(signUp.getPassword_decode_ver().equalsIgnoreCase("V3") ){
					password = String.valueOf( userV4.getId() );
					String passwordBcrypt = _helper.bCryptEncoder( String.valueOf( userV4.getId() ) );
					userServicev4.updateUserPasswordV3( userV4.getUsername(), passwordBcrypt, signUp.getPassword_decode_ver() );
					userV4.setPassword_ver("V3");
				}

				// create evalidation
				async.updateEvalidation(userV4.getId(), password);

				response.put("Status", 1);
				response.put("Msg", "success");

				byte[] oAuth2token = oAuth2Service.generateOauth2Token(userV4.getUsername(), password, clientIdApp,
						clientSecretApp);
				if (oAuth2token != null) {
					response.put("token", oAuth2token);
				} else {
					response.put("token", null);
				}
				
				response.put("Status", 1);
				response.put("Msg", "Success");
				userV4.setPassword("NA");
				response.put("User", userV4);
			}
			

		} catch (ConstraintViolationException ce) {
			log.error("signup: ConstraintViolationException:\n" + ce.getStackTrace());
			response.put("Status", 0);
			response.put("Msg", "Username/Company name already exists");
			if( company != null && company.getId()!=0 ) {
				groupservices.deleteGroups( company.getId() );
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
		} catch (DataIntegrityViolationException ce) {
			log.error("signup: DataIntegrityViolationException:\n" + ce.getStackTrace());
			response.put("Status", 0);
			response.put("Msg", "Username/Company name already exists");
			if( company != null && company.getId()!=0 ) {
				groupservices.deleteGroups( company.getId() );
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
		} catch (Exception e) {
			log.error("signup:::" + e.getStackTrace());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Signup");
			if( company != null && company.getId()!=0 ) {
				groupservices.deleteGroups( company.getId() );
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// API created for password version v3. also made the same (v3) changes in v5.0/usersignup
//	@RequestMapping(value = "v6.0/usersignup", method = RequestMethod.POST, headers = "Accept=application/json")
//	public @ResponseBody JResponse userSignupV6(@RequestBody @Valid SignUp signUp,
//												BindingResult result,
//												@RequestParam("os") String os,
//												@RequestParam("app_ver") String app_ver,
//												@RequestHeader HttpHeaders header,
//												@RequestParam(value = "time_zone", defaultValue = "", required = false) String timeZone,
//												@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
//												@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
//		JResponse response = new JResponse();
//		log.info("Entered into usersignup :: username : "+ signUp.getEmail());
//		log.info("signup request body :: username : "+ signUp.getEmail() +" :: body : "+ signUp.toString());
//		log.info("user time zone :" + timeZone);
//		Company company = null;
//		try {
//
//			String backing = header.getFirst("backing");
//			JBacking jBacking = _helper.backingKeyValidation(backing);
//
//			if (jBacking.getStatus() <= 0) {
//				response.put("Status", jBacking.getStatus());
//				response.put("Msg", jBacking.getMsg());
//				response.put("Return Time", System.currentTimeMillis());
//				return response;
//			}
//
//			if (!validation_authkey.equals(jBacking.getAuthKey())) {
//				response.put("Status", -2);
//				response.put("Msg", "Invalid Authkey");
//				response.put("Return Time", System.currentTimeMillis());
//				return response;
//			}
//
//			String email = signUp.getEmail().toLowerCase().trim();
//			email = email.replaceAll("\\s", "");
//
//			try {
//				User user = userService.getUserByUNameOrEmail(email);
//
//				if (user != null) {
//					response.put("Status", 0);
//					response.put("Msg", "Email id already found. Please use different email id");
//					response.put("Return Time", System.currentTimeMillis());
//					return response;
//				}
//			} catch (Exception ex) {
//				log.error("Sign up : Email Id not found . Create user account for user : "+ex.getLocalizedMessage());
//			}
//
//			// Assign Default things
//			int rand = _helper.getRandomNumber(100, 9999999);
//
//			signUp.setUsername(email);
//			signUp.setCompanyname(signUp.getFirstname() + "_" + rand);
//			signUp.setSupervisor("Nimble");
//			signUp.setThrotsettingsid("5");
//			signUp.setCmptype_id("3");
//			signUp.setAddress("NA");
//			signUp.setPhoneno(signUp.getMobileno());
//
//			String mobileNo = signUp.getMobileno();
//
//			if (!mobileNo.contains("-") && mobileNo.length() > 0) {
//
//				mobileNo = new CountryCode().getCountryCode(signUp.getCountry().toUpperCase()) + signUp.getMobileno();
//				signUp.setPhoneno(mobileNo);
//				signUp.setMobileno(mobileNo);
//			}
//
//			JValidateString validString = userServiceV4.checkAlphabetOnly(signUp.getFirstname(), signUp.getLastname());
//			if (!validString.isValid()) {
//				response.put("Status", 0);
//				response.put("Msg", validString.getMsg());
//				response.put("Return Time", System.currentTimeMillis());
//				return response;
//			}
//
//			String password = signUp.getPassword();
//			if( !signUp.getPassword_decode_ver().equalsIgnoreCase("V3") ) {
//				if (signUp.getPassword_decode_ver().equalsIgnoreCase("V2")) {
//					password = _helper.base64Decoder(signUp.getPassword());
//					if (password == null) {
//						response.put("Status", 0);
//						response.put("Msg", "Invalid Session. Please try again later");
//						response.put("Return Time", System.currentTimeMillis());
//						return response;
//					}
//				}
//
//				JValidateString validatePassword = userServiceV4.validatePassword(password);
//				if (!validatePassword.isValid()) {
//					response.put("Status", 0);
//					response.put("Msg", validatePassword.getMsg());
//					response.put("Return Time", System.currentTimeMillis());
//					return response;
//				}
//
//				signUp.setPassword(_helper.bCryptEncoder(password));
//			}
//
//			long timeMilli = Calendar.getInstance().getTimeInMillis();
//			String cmpName = signUp.getCompanyname() + "-" + timeMilli;
//			signUp.setCompanyname(cmpName);
//
//			try {
//				ThrottlingSettings throtsettings = throttlingService.getThrotSettings(signUp.getThrotsettingsid()).get(0);
//
//				CompanyType cmpType = companyTypeServ.getCompanyType(signUp.getCmptype_id(), 1).get(0);
//				company = companyService.createCompany(signUp,throtsettings,cmpType);
//			} catch (Exception e) {
//				log.error("Error in creating the company . Error : " + e.getLocalizedMessage());
//				response.put("Status", 0);
//				response.put("Msg", "Not able to process the request, Please try later");
//				return response;
//			}
//
//			CompanyConfig cfg = new CompanyConfig(company);
//			/*
//			 * Set temperature unit as celcius
//			 */ if (signUp.getCountry().equalsIgnoreCase("AU"))
//				cfg.setTemperatureunit("C");
//
//			boolean companyConfigCreationStatus = companyService.saveCompanyConfg(cfg);
//			log.info("Company Config Creation Status : "+companyConfigCreationStatus);
//
//			if( !companyConfigCreationStatus && company.getId()!=0 ) {
//				companyService.deleteCompany(company.getId());
//			}
//
//			Groups group = new Groups();
//			group.setName("Default");
//			boolean groupCreationStatus = groupservices.saveORupdateGroups(group, company.getId());
//
//			if( !groupCreationStatus & company!=null ) {
//				companyService.deleteCompanyConfigByCMPId(company.getId());
//				companyService.deleteCompany(company.getId());
//			}
//
//			boolean res = userService.signUp(signUp,company);
//
//			if (res) {
//				UserV4 userV4 = userServiceV4.verifyAuthV3("username", email);
//				UserVerification userVerificationToken = userService.createEmailVerificationTokenV2(userV4.getId());
//
//				if (userVerificationToken.getStatus().equalsIgnoreCase("pending")) {
//					mailService.sendVerificationMailV2(userV4, userVerificationToken.getToken());
//				}
//
//				String chargebeeId = "NA";
//				if (userV4.getChargebeeid().trim().equalsIgnoreCase("NA") || userV4.getChargebeeid() == null)
//					chargebeeId = userServicev4.createUserInChargebee(userV4.getFirstname(), userV4.getLastname(), userV4.getEmail(),
//							userV4.getMobileno(), userV4.getUsername(), 0, "NA");
//
//				String cb_planid = "chum";
//
//				if(signUp.getPassword_decode_ver().equalsIgnoreCase("V3") ){
//					password = _helper.bCryptEncoder( String.valueOf( userV4.getId() ) );
//					userServicev4.updateUserPasswordV3( userV4.getUsername(), password, signUp.getPassword_decode_ver() );
//					userV4.setPassword_ver("V3");
//				}
//
//				// create evalidation
//				async.updateEvalidation(userV4.getId(), password);
//
//				response.put("Status", 1);
//				response.put("Msg", "success");
//
//				byte[] oAuth2token = oAuth2Service.generateOauth2Token(userV4.getUsername(), password, clientIdApp,
//						clientSecretApp);
//				if (oAuth2token != null) {
//					response.put("token", oAuth2token);
//				} else {
//					response.put("token", null);
//				}
//
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//				userV4.setPassword("NA");
//				response.put("User", userV4);
//			}
//
//		} catch (ConstraintViolationException ce) {
//			log.error("signup: ConstraintViolationException:\n" + ce.getStackTrace());
//			response.put("Status", 0);
//			response.put("Msg", "Username/Company name already exists");
//			if( company != null && company.getId()!=0 ) {
//				groupservices.deleteGroups( company.getId() );
//				companyService.deleteCompanyConfigByCMPId(company.getId());
//				companyService.deleteCompany(company.getId());
//			}
//		} catch (DataIntegrityViolationException ce) {
//			log.error("signup: DataIntegrityViolationException:\n" + ce.getStackTrace());
//			response.put("Status", 0);
//			response.put("Msg", "Username/Company name already exists");
//			if( company != null && company.getId()!=0 ) {
//				groupservices.deleteGroups( company.getId() );
//				companyService.deleteCompanyConfigByCMPId(company.getId());
//				companyService.deleteCompany(company.getId());
//			}
//		} catch (Exception e) {
//			log.error("signup:::" + e.getStackTrace());
//			response.put("Status", 0);
//			response.put("Msg", "UnExcepted Error in Signup");
//			if( company != null && company.getId()!=0 ) {
//				groupservices.deleteGroups( company.getId() );
//				companyService.deleteCompanyConfigByCMPId(company.getId());
//				companyService.deleteCompany(company.getId());
//			}
//
//		}
//		response.put("Return Time", System.currentTimeMillis());
//		return response;
//	}



	@RequestMapping(value = "v5.0/getlogindata", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getLogindataV5(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		JResponse response = new JResponse();

		String backing = header.getFirst("backing");
		JBacking jBacking = _helper.backingKeyValidation(backing);

		if (jBacking.getStatus() <= 0) {
			response.put("Status", jBacking.getStatus());
			response.put("Msg", jBacking.getMsg());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		String auth = jBacking.getAuthKey();

		try {

			if (!auth.equalsIgnoreCase(validation_authkey)) {
				response.put("Status", -2);
				response.put("Msg", "Authentication Error");
			} else {

				if (!app_ver.isEmpty() && !os.isEmpty()) {
					VersionMapping verObj = crService.getVersionMapping(app_ver, os);

					if (verObj != null) {
						enableapple = verObj.isEnableapple();
						enablegoogle = verObj.isEnablegoogle();
						enablefb = verObj.isEnablefb();
						enable_tips = verObj.isEnable_tips();
						show_orderid = verObj.isShow_orderid();
						plan_version = verObj.getPlan_version();
					}
				}
				
				if(device_country == null || device_country.isEmpty() || device_country.equalsIgnoreCase("US")|| device_country.equalsIgnoreCase("NA") 
						|| device_country.toLowerCase().contains("india") || device_country.equalsIgnoreCase("in") 
					) {
					device_country = "US";
				}

				response.put("supportPhone", supportContactNumber.get(device_country));
				response.put("supportEmail", supportContactEmail.get(device_country));
				response.put("enablegoogle", enablegoogle);
				response.put("enablefb", enablefb);
				response.put("enableapple", enableapple);
				response.put("terms_conditions", terms_conditions.get(device_country));
				response.put("privacy_policy", privacy_policy.get(device_country));
				response.put("facebook", facebook);
				response.put("enable_tips", enable_tips);
				response.put("amplitude_andriod", amplitude_andriod);
				response.put("amplitude_ios", amplitude_ios);
				response.put("nimbleAuthKey", nimbleAuthKey);
				response.put("show_orderid", show_orderid);
				response.put("showPopUpRegisteration", showPopUpRegisteration);
				response.put("plan_version", plan_version);
				response.put("purchased_from", purchased_from);
				response.put("mixpanel_token", mixpanel_token);
				response.put("enable_call_support", enable_call_support);
				response.put("redirectWebSubs", redirectWebSubs);
				response.put("Status", 1);
				response.put("Msg", "Success");
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Return Time", System.currentTimeMillis());
			response.put("Msg", "Excepitoin while getting login data.");
			return response;
		}
	}

	@RequestMapping(value = "v5.0/loginV2", headers = "Accept=application/json", method = RequestMethod.POST)
	@ResponseBody
	public JResponse loginV5(@RequestHeader HttpHeaders header, @RequestParam("mobiletype") String mobiletype,
			@RequestParam("mobileid") String mobileid, @RequestParam("webappid") String webappid,
			@RequestParam("mobileappid") String mobileappid,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "password_decode_ver", defaultValue = "V1", required = false) String password_decode_ver,
			@RequestParam(value = "time_zone", defaultValue = "", required = false) String timeZone,
			Authentication authentication) {

		JResponse response = new JResponse();
		String username = header.getFirst("username").trim();
		String password = header.getFirst("password").trim();

		log.info(username + "username" + password + "password");
		log.info("user time zone : " + timeZone);

		String backing = header.getFirst("backing");
		JBacking jBacking = _helper.backingKeyValidation(backing);

		if (jBacking.getStatus() <= 0) {
			response.put("Status", jBacking.getStatus());
			response.put("Msg", jBacking.getMsg());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		if (!validation_authkey.equals(jBacking.getAuthKey())) {
			response.put("Status", -2);
			response.put("Msg", "Invalid Session. Please try after some time");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		/* check for username */
		if (username.isEmpty() && username.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Please enter Username & Password");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} else if (username.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Please enter Username");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} else if (password.isEmpty()) {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV3("username", username);

				response.put("Status", 0);
				response.put("Msg", "Please enter Password");
				response.put("Return Time", System.currentTimeMillis());
				return response;

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Username/Email");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

		}

		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV3("username", username);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Username/Password");
				response.put("Error", ex.getLocalizedMessage());
				log.error("loginV4 :Exception : " + username);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
//				if (!(user.getUsername().equalsIgnoreCase(username))) {
//					response.put("Status", 0);
//					response.put("Msg", "Invalid username");
//					log.error(" loginV4 : username not matched : " + username);
			//
//					response.put("Return Time", System.currentTimeMillis());
//					return response;
//				}
			
			/* Deleted User */
			if(user != null && user.isDelete_user()) {
				response.put("Status", 0);
				response.put("Msg", "Your account deletion is in process. For more details reach our support team");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String pwd = user.getPassword().toString();

			if (password_decode_ver.equalsIgnoreCase("V2")) {
				password = _helper.base64Decoder(password);
				if (password == null) {
					response.put("Status", 0);
					response.put("Msg", "Invalid Username/Password");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			}

			boolean passwordMatch = _helper.checkUserCredencial(password, user.getPassword(), user.getPassword_ver());

			if (passwordMatch) {
				if (!user.isEnable()) {
					response.put("Status", 0);
					response.put("Msg", "Account Disabled. Please contact support!");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				response.put("Status", 1);
				response.put("Msg", "Success");

				String clientId = clientIdWeb;
				String clientSecret = clientSecretWeb;

				if (webappid.isEmpty() && !mobileappid.isEmpty()) {
					clientId = clientIdApp;
					clientSecret = clientSecretApp;
				}

				byte[] oAuth2token = oAuth2Service.generateOauth2Token(user.getUsername(), password, clientId,
						clientSecret);

				if (oAuth2token != null) {
					response.put("token", oAuth2token);
				} else {
					response.put("token", null);
				}
				user.setPassword("NA");
				response.put("User", user);

			} else {
				if (password.isEmpty()) {
					response.put("Status", 0);
					response.put("Msg", "Please enter Password");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				response.put("Status", 0);
				response.put("Msg", "Invalid Username/Password");
			}
		} catch (Exception e) {
			log.error("loginV4 : Exception : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Username/Password");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "v5.0/getzipcodedetails/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getzipcodedetails(@RequestParam("zipcode") String zipCode,
			@RequestParam("countrycode") String countryCode, @RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		JResponse response = new JResponse();
		try {

			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidation(backing);

			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String autho = jBacking.getAuthKey();

			User user = null;

			if (!validation_authkey.equals(jBacking.getAuthKey()))
				user = userService.verifyAuthKey(autho);

			List<ZipCodeDetails> zipCodeDetails = iZipCodeDetailsService.getZipCodeDetails(zipCode, countryCode);

			if (zipCodeDetails.size() > 0) {
				log.info("Zip Cpode found in database : " + zipCode + countryCode);
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("ZipCodeDetails", zipCodeDetails.get(0));
				response.put("Return Time", System.currentTimeMillis());
				return response;
			} else {

				log.info("Search Zip Cpode in Google : " + zipCode + countryCode);

				ZipCodeDetails newZipCodeDetails = new ZipCodeDetails();

				GeoApiContext context = new GeoApiContext.Builder().apiKey(googleAPIkey).build();

				GeocodingResult[] results = GeocodingApi.newRequest(context)
//							.components(new ComponentFilter("country", countryCode))
						.components(new ComponentFilter("postal_code", zipCode),
								new ComponentFilter("country", countryCode))
						.await();

				if (results.length < 1) {
					log.info("Error in Searching Zip Cpode in Google : " + zipCode + countryCode);
					response.put("Status", 0);
					response.put("Msg", "No Details Found");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				for (GeocodingResult gr : results) {
					for (AddressComponent ac : gr.addressComponents) {
						for (AddressComponentType acType : ac.types) {

							if (acType == AddressComponentType.ADMINISTRATIVE_AREA_LEVEL_1) {

								newZipCodeDetails.setState(ac.shortName);

							} else if (acType == AddressComponentType.LOCALITY) {

								newZipCodeDetails.setCity(ac.shortName);

							}
						}

					}

					if (newZipCodeDetails.getCity().equalsIgnoreCase("NA")) {
						for (AddressComponent ac : gr.addressComponents) {
							for (AddressComponentType acType : ac.types) {
								if (acType == AddressComponentType.ADMINISTRATIVE_AREA_LEVEL_2) {

									newZipCodeDetails.setCity(ac.shortName);

								}
							}

						}
					}

					break;
				}

				newZipCodeDetails.setZipcode(zipCode.trim());
				newZipCodeDetails.setCountry(countryCode.toString().toUpperCase());
				newZipCodeDetails.setCreatedOn(_helper.getCurrentTimeinUTC());
				boolean saveZipDetails = iZipCodeDetailsService.saveZipCode(newZipCodeDetails);

				log.info("Save Zip Code from Google into databases : " + zipCode + countryCode + "Status : ->"
						+ saveZipDetails);

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("ZipCodeDetails", newZipCodeDetails);
				response.put("Return Time", System.currentTimeMillis());
				return response;

			}
		} catch (InvalidAuthoException e) {
			log.error("Error - . invalid authentication key");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			log.error("Error - . Error in Zip Code Details.");
			response.put("Status", 0);
			response.put("Msg", "Error in getting Zip Code..");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

	}

	@PostMapping(value = "v5.0/socialsignup", headers = "Accept=application/json")
	public @ResponseBody JResponse socialSignUpV5(@RequestBody ExternalLogin externalLogin,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestHeader HttpHeaders header,
			@RequestParam(value = "time_zone", defaultValue = "", required = false) String timeZone,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		log.info("Entered socialSignUp : " + externalLogin.getFirstname());
		log.info("user time zone : " + timeZone);
		JResponse response = new JResponse();
		try {

			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidation(backing);

			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (!validation_authkey.equals(jBacking.getAuthKey())) {
				response.put("Status", -2);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			User user = null;
			String userEmail = externalLogin.getEmail().toLowerCase().trim().replaceAll("\\s", "");
			externalLogin.setEmail(userEmail);
			try {
				if (!userEmail.equalsIgnoreCase("NA") && userEmail != null && !(userEmail.isEmpty())
						&& !userEmail.contains("facebook.com") && !userEmail.contains("privaterelay.appleid.com")) {
					try {
						user = userService.getUserByUNameOrEmail(userEmail);
					} catch (Exception e) {
						log.error("Exception occured while getting user by username : " + e.getLocalizedMessage());
					}
					if (user != null) {
						response.put("Status", 1);
						response.put("Msg", "Success");
						response.put("alreadyuser", true);
						
						/* Deleted User */
						if(user != null && user.isDelete_user()) {
							response.put("Status", 0);
							response.put("Msg", "Your account deletion is in process. For more details reach our support team");
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}

						byte[] oAuth2token = oAuth2Service.generateOauth2Token(user.getUsername(), user.getPassword(),
								clientIdApp, clientSecretApp);
						if (oAuth2token != null) {
							response.put("token", oAuth2token);
						} else {
							response.put("token", null);
						}

						if (user.getEmail().equalsIgnoreCase("NA")) {
							response.put("emaileditable", true);
						} else {
							response.put("emaileditable", false);
						}
						if (user.getMobileno() == null || user.getMobileno().isEmpty()) {
							user.setMobileno("");
							user.setCompletesetup(false);
						}
						if (user.getVerificationToken() == null) {
							user.setVerificationToken(new UserVerification());
						}

						String cb_planid = "chum";

//						boolean stat = rvcentricServ.saveUserBadgeTxn(user.getId(), cb_planid, 0, false, "NA");
//						log.info("in socialsignup:" + user.getId() + " Badge created:" + stat);
						user.setPassword("NA");
						response.put("User", user);
						userService.updateLastLoginTypeAndTime(user.getId(),
								Integer.valueOf(externalLogin.getSignuptype()), _helper.getCurrentTimeinUTC());
						response.put("Return Time", System.currentTimeMillis());
						return response;
					}
				}
			} catch (Exception e) {
				log.error("Exception occured while getting user by email / username : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid email!");
			}
			try {
				JResponse jres = userService.loginViaGoogleOrFacebook(externalLogin);
				if (jres != null) {
					if((int) jres.getResponse().get("Status") != 0) {
						User userObj = (User) jres.getResponse().get("User");
						byte[] oAuth2token = oAuth2Service.generateOauth2Token(userObj.getUsername(), userObj.getPassword(),
								clientIdApp, clientSecretApp);
						if (oAuth2token != null) {
							jres.put("token", oAuth2token);
						} else {
							jres.put("token", null);
						}
						userObj.setPassword("NA");
						jres.put("User", userObj);
					}
					jres.put("Return Time", System.currentTimeMillis());
					return jres;
				}
			} catch (Exception e) {
				log.error("Exception occured! " + e.getMessage());
				response.put("Status", 0);
				response.put("Msg", "Unexcepted error!");
			}
		} catch (Exception e) {
			log.error("Exception occured : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Unexcepted error!");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/updateusercompletesetup/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateUserCompleteSetUp(@RequestBody User user1, @RequestHeader HttpHeaders header,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "time_zone", defaultValue = "", required = false) String timeZone,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		log.info("Entered updateUserCompleteSetUp");
		log.info("user time zone : " + timeZone);
		JResponse response = new JResponse();

		try {

			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidation(backing);

			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			String autho = jBacking.getAuthKey();
			User user = null;
			try {
				if (!validation_authkey.equals(jBacking.getAuthKey())) {
					user = userService.verifyAuthKey(autho);
				} else {
					user = userService.getUserById(user1.getId());
				}
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authkey!");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			try {
				// Need to remove in next release
				if ((user1.getEmail().trim().isEmpty()) || (user1.getEmail() == null)) {
					user1.setEmail(user.getEmail());
				}

				User usr = userService.getUserByUNameOrEmail(user1.getEmail());
				if (usr != null && !(user.getId() == usr.getId())) {
					response.put("Status", 0);
					response.put("Msg", "Email already exist. Please enter alternate Email");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			} catch (Exception ex) {
				log.error("user update failed" + ex.getMessage());
			}

			/*if (user1.getZipcode().length() > 8 || user1.getZipcode().length() < 3) {
				log.info("ZIP Code Length : " + user1.getZipcode().length() + ", Zipcode : " + user1.getZipcode());
				SignupType signUp_type = user.getSignupType();
				if (!(signUp_type.getName().equalsIgnoreCase("Apple") && user1.getZipcode().equalsIgnoreCase("NA"))) {
					log.info("Invalid Zipcode : " + user1.getZipcode());
					response.put("Status", 0);
					response.put("Msg", "Invalid zipcode!");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			}*/
			user.setUpdatedOn(_helper.getCurrentTimeinUTC());
			// user.setFirstname(user1.getFirstname());
			// user.setLastname(user1.getLastname());
			String zipCode = user1.getZipcode();
			String country = user1.getCountry();
			user.setCountry(country);
			String city = "NA";
			String state = "NA";
			ZipCodeDetails zipDetails = null;
			try {
				List<ZipCodeDetails> zipCodeDetails = iZipCodeDetailsService.getZipCodeDetails(zipCode, country);
				if (zipCodeDetails.size() > 0) {
					log.info("Zip Cpode found in database : " + zipCode + country);
					zipDetails = zipCodeDetails.get(0);
					city = zipDetails.getCity();
					state = zipDetails.getState();
				} else {
					log.info("Search Zip Cpode in Google : " + zipCode + country);
					ZipCodeDetails newZipCodeDetails = new ZipCodeDetails();
					GeoApiContext context = new GeoApiContext.Builder().apiKey(googleAPIkey).build();
					GeocodingResult[] results = GeocodingApi.newRequest(context)
							// .components(new ComponentFilter("country", countryCode))
							.components(new ComponentFilter("postal_code", zipCode),
									new ComponentFilter("country", country))
							.await();
					if (results.length > 0) {
						for (GeocodingResult gr : results) {
							for (AddressComponent ac : gr.addressComponents) {
								for (AddressComponentType acType : ac.types) {
									if (acType == AddressComponentType.ADMINISTRATIVE_AREA_LEVEL_1) {
										newZipCodeDetails.setState(ac.shortName);
										state = ac.shortName;
									} else if (acType == AddressComponentType.LOCALITY) {
										newZipCodeDetails.setCity(ac.shortName);
										city = ac.shortName;
									}
								}
							}
							if (newZipCodeDetails.getCity().equalsIgnoreCase("NA")) {
								for (AddressComponent ac : gr.addressComponents) {
									for (AddressComponentType acType : ac.types) {
										if (acType == AddressComponentType.ADMINISTRATIVE_AREA_LEVEL_2) {
											newZipCodeDetails.setCity(ac.shortName);
											city = ac.shortName;
										}
									}
								}
							}
							break;
						}
					}
					newZipCodeDetails.setZipcode(zipCode.trim());
					newZipCodeDetails.setCountry(country.toString().toUpperCase());
					newZipCodeDetails.setCreatedOn(_helper.getCurrentTimeinUTC());
					boolean saveZipDetails = iZipCodeDetailsService.saveZipCode(newZipCodeDetails);
					log.info("Save Zip Code from Google into databases : " + zipCode + country + "Status : ->"
							+ saveZipDetails);
				}
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Zipcode error!");
				response.put("Return Time", System.currentTimeMillis());
				log.error("Error - . Error in Zip Code Details - "+e.getLocalizedMessage());
			}
			user.setCity(city);
			user.setState(state);
			if (!city.equalsIgnoreCase("NA") || !state.equalsIgnoreCase("NA")) {
				user.setZipcode(zipCode);
			} else {
				user.setZipcode("NA");
			}

			String mobileNo = "1-1234567890";

			if (!user1.getMobileno().equalsIgnoreCase("NA") && !user1.getMobileno().isEmpty()
					&& user1.getMobileno() != null) {
				mobileNo = user1.getMobileno();
				user.setMobileno(mobileNo);
			}

			String password = mobileNo;
			password = password.replaceAll("\\W", "");
			if (password.length() > 10) {
				password = password.substring(password.length() - 10);
			}
			user.setPassword(_helper.bCryptEncoder(password));
			if (!mobileNo.contains("-") && mobileNo.length() > 0) {
				mobileNo = new CountryCode().getCountryCode(user1.getCountry().toUpperCase()) + user1.getMobileno();
				user.setMobileno(mobileNo);
			}

			boolean sendVerifyEmail = false;
			if ((user.getEmail().equalsIgnoreCase("NA") || user.getEmail() == null)
					|| (!user.getEmail().equalsIgnoreCase(user1.getEmail())
							&& user.getSignupType().getName().equalsIgnoreCase("Apple"))) {
				user.setEmail(user1.getEmail());
				user.setUsername(user1.getEmail());
				user.setAuthKey(_helper.encryptAndSetUser(user1.getEmail().trim()));
				user.setVerified(false);
				sendVerifyEmail = true;
			}

			user.setCompletesetup(true);
			boolean isUserUpdated = userService.updateUser(user);
			if (isUserUpdated) {
				if (sendVerifyEmail) {
					UserVerification userVerificationToken = userService.createEmailVerificationToken(user);
					if (userVerificationToken.getStatus().equalsIgnoreCase("pending")) {
						boolean verificationEmailSend = mailService.sendVerificationMail(user,
								userVerificationToken.getToken());
						log.info("verificationEmailSend : " + verificationEmailSend);
					}
				}
				User updatedUser = userService.getUserById(user.getId());

				byte[] oAuth2token = oAuth2Service.generateOauth2Token(user.getUsername(), password, clientIdApp,
						clientSecretApp);
				if (oAuth2token != null) {
					response.put("token", oAuth2token);
				} else {
					response.put("token", null);
				}

				// update evalidation
				async.updateEvalidation(user.getId(), password);

				updatedUser.setPassword("NA");
				response.put("User", updatedUser);

			} else {
				response.put("Status", 0);
				response.put("Msg", "Could not update user!");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			// update cb customer details
			if (!user.getChargebeeid().trim().equalsIgnoreCase("NA"))
				async.updateCBCustomerDetails(user);

//			UserRvDetails rvObj = userServiceV4.getUserRvDetails(user.getId());
//
//			boolean rvStatus = (rvObj != null) ? true : false;

//			boolean stat1 = rvcentricServ.saveUserBadgeTxn(user.getId(), "NA", 0, rvStatus, user.getChargebeeid());
//			log.info("in user Completesetup:" + user.getId() + " Badge created:" + stat1);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("Return Time", System.currentTimeMillis());
		} catch (Exception e) {
			log.error("userupdate : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in User Updation");
			response.put("Return Time", System.currentTimeMillis());
		}
		return response;
	}

	@RequestMapping(value = "v5.0/findcountry", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse findCountry(HttpServletRequest httpRequest, HttpServletResponse hhtpResponse,
			@RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver,
			@RequestHeader HttpHeaders header) {
		log.info(" Entered into findcountry v4");
		JResponse response = new JResponse();
		try {
			
			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidation(backing);
	
			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			
			response = commonService.findCountry( httpRequest, header );
			return response;
			
		} catch (Exception e) {
			log.error(" Error in findcountryv5 " + e.getLocalizedMessage());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("country_code", "US");
		} finally {
			response.put("Return Time", System.currentTimeMillis());
		}
		return response;
	}
	
	@RequestMapping(value = "v5.0/getUserByUsernameV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserByUsernameV5(@RequestParam("name") String name,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver) {
		JResponse response = new JResponse();
		log.info("Entering getUserByUsernameV4 : " + name);
		name = name.toLowerCase().trim();
		name = name.replaceAll("\\s", "");
		String backing = header.getFirst("backing");
		JBacking jBacking = _helper.backingKeyValidation(backing);

		if (jBacking.getStatus() <= 0) {
			response.put("Status", jBacking.getStatus());
			response.put("Msg", jBacking.getMsg());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		String auth = jBacking.getAuthKey();

		if (!auth.equalsIgnoreCase(validation_authkey)) {
			response.put("Status", 0);
			response.put("Msg", "Authentication Error");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("username", name);

			} catch (InvalidAuthoException e) {
				log.error("Invalid Username :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				response.put("Error", e.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (user == null || !(user.getUsername().equals(name))) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("User", user);

		} catch (Exception e) {
			log.error("Exception : getUserByUsernameV4 :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@PostMapping("v5.0/registerdevice")
	public JResponse registerDevice(@RequestBody JRegisterDevice registerDevice) {
		return registerDeviceService.registerDevice(registerDevice);
	}
	
	@RequestMapping(value = "v5.0/checkqrcexist/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse checkQrcExist(@RequestParam(value = "qrcode") String qrcode,
			@RequestParam(value = "monitortypeid") String monitortypeid,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country,
			@RequestParam(value = "from", defaultValue = "qrcode", required = false) String from) {
		log.info("Entered checkQrcExist");
		JResponse response = new JResponse();
		UserV4 user = null;
		try {

			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidation(backing);

			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			String autho = jBacking.getAuthKey();
			Properties prop = new Properties();
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			prop.load(new FileInputStream(file));
			String errorResponse = "No Error";
			String errorCode = "ER000";
			boolean isExist = false;
			
			if(!from.equalsIgnoreCase("qrcode")) {
				String inventoryqrc = niomDbservice.getInventoryBySerialNo(qrcode);
				if(inventoryqrc != null)
					qrcode = inventoryqrc;
			}
			response.put("qrcode", qrcode != null ? qrcode : "");
			
			if (!validation_authkey.equals(autho)) {
				try {
					user = userServiceV4.verifyAuthV3("authkey", autho);

				} catch (InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for auth : " + ex.getLocalizedMessage());
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				
				ArrayList<JSensorType> stype_list = gatewayServiceV4.getSensorType();			
				response.put("stype_list", stype_list);

				if (!(qrcode.matches("[a-zA-Z0-9]+")) || !(qrcode.length() == 6 || (qrcode.length() == 18 && qrcode.substring(0, 4).equalsIgnoreCase("BSCN"))) || qrcode.startsWith("8")) {
					log.info("Invalid QRCode . " + qrcode);
					response.put("Status", 0);
					response.put("Msg", RegisterUserError.ER048);
					errorCode = "ER048";
					errorResponse = RegisterUserError.ER048;
					String subject = "External User QRC Activation Status";
					String to_address = prop.getProperty("to_address");
					String cc_address = ""; // prop.getProperty("cc_address");
					String bcc_address = prop.getProperty("bcc_address");
					String statusEmailContent = new EmailContent().externalUserQrcActivationEmailContent(user, qrcode,
							errorResponse);
					async.externalQrcActivationStatus(qrcode, errorCode, to_address, cc_address, bcc_address, subject,
							statusEmailContent);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				
				List<Inventory> inventoryList = niomDbservice.getInventory(qrcode);
				if( inventoryList == null || inventoryList.isEmpty() ) {
					log.error("Inventory not found for QRC : "+ qrcode);
					response.put("Status", 0);
					response.put("Msg", "QR code not found");
					response.put("monitor_type_id", 0);
					return response;
				}
				
				AssetModel assetModel = gatewayService.getAssetModelByName( inventoryList.get(0).getDevicemodelnumber() );
				if( assetModel == null ) {
					log.error("Asset Model not found for Meid : "+ inventoryList.get(0).getMeid() );
					response.put("Status", 0);
					response.put("Msg", "QR code not found");
					response.put("monitor_type_id", 0);
					return response;
				}
				
				if( Long.parseLong( monitortypeid ) != 0 && Long.parseLong( monitortypeid ) != assetModel.getMonitor_type().getId() ) {
					log.error("user register a different product :" + monitortypeid);
					response.put("Status", 0);
					response.put("Msg", "Invalid QR Code. Please select valid product.");
					response.put("monitor_type_id", 0);
					return response;
				}
				
				long monitorTypeId = assetModel.getMonitor_type().getId();
				isExist = gatewayService.checkQrcExist(user.getId(), qrcode); // to check already registered with particular user id
				boolean isQRCUser = false;

				if (user.getUsername().equalsIgnoreCase(qrcode)) {
					isQRCUser = true;
				}
				response.put("serial_no", inventoryList.get(0).getSerialnumber());
				if (isExist) {
					response.put("Status", 1);

					if (isQRCUser) {
						response.put("Msg", "Valid QR code");
						response.put("monitor_type_id", monitorTypeId);
					} else {
						if( monitorTypeId == 1 ){
							response.put("Msg", "A Monitor with this QR code already exists in your account");	
						}else if( monitorTypeId == 3 ){
							response.put("Msg", "A Smart AI Bowl with this QR code already exists in your account");
						} else if( monitorTypeId == 4 ){
							response.put("Msg", "A WaggleCam with this QR code already exists in your account");
						} else if( monitorTypeId == 5 ){
							response.put("Msg", "A RV Cam AI with this QR code already exists in your account");
						} else if( monitorTypeId == 6 ){
							response.put("Msg", "A WaggleCam Pro with this QR code already registered. Please contact support!");
						} else if( monitorTypeId == 8 ){
							response.put("Msg", "A RV 4G Camera with this QR code already registered. Please contact support!");
						} else if( monitorTypeId == 9 ){
							response.put("Msg", "A RV Smart Sensor with this QR code already registered. Please contact support!");
						} else if( monitorTypeId == 12 ){
							response.put("Msg", "A RV 4G Mini with this QR code already registered. Please contact support!");
						}  else {
							response.put("Msg", "A Monitor with this QR code already exists in your account");	
						}
						
					}

				} else {
					response.put("Status", 1);
					response.put("Msg", "Qrcode not exist!");
					response.put("monitor_type_id", monitorTypeId);
				}
				response.put("qrcavail", isExist);

			} else {
				log.info("invalid authkey received so do qrcode validation only and send if qrcode is valid");
				
				ArrayList<JSensorType> stype_list = gatewayServiceV4.getSensorType();			
				response.put("stype_list", stype_list);
				
				if (!(qrcode.matches("[a-zA-Z0-9]+")) || !(qrcode.length() == 6 || (qrcode.length() == 18 && qrcode.substring(0, 4).equalsIgnoreCase("BSCN"))) || qrcode.startsWith("8")) {
					log.info("Valid QRCode . " + qrcode);
					response.put("Status", 0);
					response.put("Msg", RegisterUserError.ER048);
					response.put("monitor_type_id", 0);
					return response;
				} else {
					
					List<Inventory> inventoryList = niomDbservice.getInventory(qrcode);
					if( inventoryList == null || inventoryList.isEmpty() ) {
						log.error("Inventory not found for QRC : "+ qrcode);
						response.put("Status", 0);
						response.put("Msg", "QR code not found");
						response.put("monitor_type_id", 0);
						return response;
					}
					
					AssetModel assetModel = gatewayService.getAssetModelByName( inventoryList.get(0).getDevicemodelnumber() );
					if( assetModel == null ) {
						log.error("Asset Model not found for Meid : "+ inventoryList.get(0).getMeid() );
						response.put("Status", 0);
						response.put("Msg", "QR code not found");
						response.put("monitor_type_id", 0);
						return response;
					}
					
					if( Long.parseLong( monitortypeid ) != 0 && Long.parseLong( monitortypeid ) != assetModel.getMonitor_type().getId() ) {
						log.error("user register a different product :" + monitortypeid);
						response.put("Status", 0);
						response.put("Msg", "Invalid QR Code. Please select valid product.");
						response.put("monitor_type_id", 0);
						return response;
					}
					
					long monitorTypeId = assetModel.getMonitor_type().getId();
					
					isExist = gatewayService.checkQrcRegistered( qrcode); // to check already registered 
					response.put("serial_no", inventoryList.get(0).getSerialnumber());
					if(isExist) {
						response.put("Status", 0);
						if( monitorTypeId == 1 ){
							response.put("Msg", "A monitor with this QR code already registered. Please contact support!");	
						}else if( monitorTypeId == 3 ){
							response.put("Msg", "A Smart AI Bowl with this QR code already registered. Please contact support!");
						} else if( monitorTypeId == 4 ){
							response.put("Msg", "A Waggle Cam with this QR code already registered. Please contact support!");
						} else if( monitorTypeId == 5 ){
							response.put("Msg", "A RV Cam AI with this QR code already registered. Please contact support!");
						} else if( monitorTypeId == 8 ){
							response.put("Msg", "A RV 4G Camera with this QR code already registered. Please contact support!");
						} else if( monitorTypeId == 9 ){
							response.put("Msg", "A RV Smart Sensor with this QR code already registered. Please contact support!");
						} else {
							response.put("Msg", "A monitor with this QR code already registered. Please contact support!");	
						}
						response.put("qrcavail", isExist);
						response.put("monitor_type_id", monitorTypeId);
					}else {
						response.put("Status", 1);
						response.put("Msg", "Valid QR code");
						response.put("qrcavail", isExist);
						response.put("monitor_type_id", monitorTypeId);
					}
					
				}
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "invalid authkey");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception checkQrcExist : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/forgetpassword", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse oneTimePassword(@RequestHeader HttpHeaders header,
			@RequestParam(value = "username") String userName,
			@RequestParam(value = "via", defaultValue = "mobileno") String via, @RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver,
			@RequestParam(value = "request_from", defaultValue = "forgotpass") String request_from,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "mobile_number", defaultValue = "0", required = false) String mobileno) {
		log.info(" Entered into forgetpassword v5 :: username :" + userName);
		JResponse response = new JResponse();

		log.info("generateaddonpurchaselink ");
		try {
			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidation(backing);
	
			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
	
			String auth = jBacking.getAuthKey();
	
			try {	
				if (!validation_authkey.equals(auth)) {
					UserV4 user = userServiceV4.verifyAuthV4("authkey", auth);
				}	
			} catch (InvalidAuthoException ex) {
	
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				log.error("Exception while getting user for auth : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;	
			}		
		
			response = commonService.oneTimePassword(userName,via,request_from,mobileno);
		
		} catch (Exception e) {
			log.error(" Error in forgetpassword v4 " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "OTP sent failed");
			response.put("Error", e.getMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "v5.0/validateotp", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse validateOneTimePassword(@RequestHeader HttpHeaders header, @RequestParam long otp,
			@RequestParam(value = "username") String userName, @RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver,
			@RequestParam(value = "request_from", defaultValue = "forgotpass") String request_from,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info("Entered into validateOneTimePassword :: username :" + userName);
		JResponse response = new JResponse();

		try {
			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidation(backing);
	
			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			String auth = jBacking.getAuthKey();
	
			log.info("generateaddonpurchaselink :" + auth);
	
			try {
	
				if (!validation_authkey.equals(auth)) {
					userServiceV4.verifyAuthV4("authkey", auth);
				}
	
			} catch (InvalidAuthoException ex) {
	
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				log.error("Exception while getting user for auth : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
	
			}
			
			response = commonService.validateOneTimePassword(auth,otp,userName,request_from);

		} catch (Exception e) {
			log.error(" Error in validateOneTimePassword " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	@RequestMapping(value = "v5.0/pwdupdatev2", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse passwordUpdateV5(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestBody UpdatePassword udatePassword,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info(" Entered into Password Update v2: "+udatePassword.getUsername()+":"+udatePassword.getPassword());

		JResponse response = new JResponse();
		try {

			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidation(backing);
			log.info("PasswordUpdatev2:backing: "+backing);

			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String autho = jBacking.getAuthKey();
			
			log.info("PasswordUpdatev2:backing: "+backing +": Auth:"+autho);

			UserV4 user = null;

			if (!validation_authkey.equals(autho)) {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} else {
				user = userServiceV4.verifyAuthV4("username", udatePassword.getUsername());
			}

			return commonService.passwordUpdateV4(user, udatePassword);

		} catch (InvalidAuthoException ex) {

			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			log.error("Exception while getting user for auth : " + header.getFirst("auth"));
			return response;
		}
	}
	
	@RequestMapping(value = "v5.0/validateorderid/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse validateOrderId(@RequestParam("orderid") String orderId,
			@RequestParam("orderchannel") String orderChannel,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		log.info("Entered validateOrderId, orderId : " + orderId + " orderChannel : " + orderChannel);
		JResponse response = new JResponse();
		try {

			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidation(backing);

			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String autho = jBacking.getAuthKey();
			int orderIdLength = orderId.length();

			if (!validation_authkey.equals(autho)) {
				response.put("Status", -2);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			
			if (orderChannel.equalsIgnoreCase("rv")) {
				Pattern numbers = Pattern.compile("^[0-9]+$");
				Matcher hasNumbers = numbers.matcher(orderId);
				if (hasNumbers.find() && orderIdLength >= 5 && orderIdLength <= 10) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					log.info(orderChannel + " Order ID validated " + orderId);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "Order ID should be 5 to 10 digits and should contains only numerics.");
					log.info(orderChannel + " Order ID should be 5 to 10 digits and should contains only numerics."
							+ orderId);
				}
			} else if (orderChannel.equalsIgnoreCase("amazon")) {

				Pattern numbersWithHypen = Pattern.compile("^[0-9,-]+$");
				Matcher hasNumbersWithHypen = numbersWithHypen.matcher(orderId);

				Pattern numbers = Pattern.compile("^[0-9]+$");
				Matcher hasNumbers = numbers.matcher(orderId);
				if ((hasNumbers.find() || hasNumbersWithHypen.find()) && orderIdLength >= 7) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					log.info(orderChannel + " Order ID validation success : " + orderId);
				} else {
					response.put("Status", 0);
					response.put("Msg",
							"Order ID should be min 7 characters and should not contains any alphabets or special characters except '-' (hypen)");
					log.info(orderChannel + " Order ID should contains only numerics : " + orderId);
				}
				response.put("Return Time", System.currentTimeMillis());
				return response;
			} else if (orderChannel.equalsIgnoreCase("walmart") || orderChannel.equalsIgnoreCase("others")) {
				Pattern numbers = Pattern.compile("^[0-9]+$");
				Matcher hasNumbers = numbers.matcher(orderId);

				if (hasNumbers.find()) {

					if (orderId.length() >= 10) {
						response.put("Status", 1);
						response.put("Msg", "Success");
						log.info(orderChannel + " Order id validated " + orderId);
					} else {
						response.put("Status", 0);
						response.put("Msg",
								"Order ID should be min 10 characters and should not contains any alphabets or special characters for walmart orders");
						log.info(orderChannel
								+ " Order ID should be min 10 characters and should not contains any alphabets or special characters for walmart orders");
					}

				}else {
					if(device_country == null || device_country.isEmpty() || device_country.equalsIgnoreCase("US")|| device_country.equalsIgnoreCase("NA")
							|| device_country.toLowerCase().contains("india") || device_country.equalsIgnoreCase("in")
							) {
						device_country = "US";
					}
					String msg = "Please contact us at "+supportContactNumber.get(device_country)+"  or email to "+supportContactEmail.get(device_country)+" to register your product";
					response.put("Status", 1);
					response.put("Msg",msg);
					log.info(orderChannel+ " " +msg);
				}
				response.put("Return Time", System.currentTimeMillis());
				return response;
			} else if (orderChannel.equalsIgnoreCase("technorv")) {

				Pattern numbersWithHypen = Pattern.compile("^[a-zA-Z0-9,-]+$");
				Matcher hasNumbersWithHypen = numbersWithHypen.matcher(orderId);

				Pattern numbers = Pattern.compile("^[a-zA-Z0-9]+$");
				Matcher hasNumbers = numbers.matcher(orderId);
				if ((hasNumbers.find() || hasNumbersWithHypen.find()) && orderIdLength >= 5) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					log.info(orderChannel + " Order ID validation success : " + orderId);
				} else {
					response.put("Status", 0);
					response.put("Msg",
							"Order ID should be min 5 characters and should not contains any special characters except '-' (hypen)");
					log.info(orderChannel + " Order ID should contains only numerics : " + orderId);
				}
				response.put("Return Time", System.currentTimeMillis());
				
				return response;
			}  

		} catch (Exception e) {
			log.error("Exception occured while validating order id : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unknown error");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/orderchannel", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getOrderChannelV5(
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestHeader HttpHeaders header,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver) {
		JResponse response = new JResponse();
		log.info("Entering getOrderChannelV4 : ");
		try {

			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidation(backing);

			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (!validation_authkey.equals(jBacking.getAuthKey())) {
				response.put("Status", -2);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			List<JOrderChannel> jOrderChannelsList = new ArrayList<JOrderChannel>();

			List<OrderChannel> orderChannels = dropdownServiceV4.getOrderChannelV4();

			if (orderChannels != null && orderChannels.size() > 0) {

				for (OrderChannel orderChannel : orderChannels) {

					JOrderChannel _orderChannel = new JOrderChannel();

					_orderChannel.setOrderChannel(orderChannel.getOrderchannel());
					_orderChannel.setShortDescription(orderChannel.getShortdescription());

					jOrderChannelsList.add(_orderChannel);
				}
				response.put("Status", 1);
				response.put("Msg", "Success");

				// response.put("orderChannel", jOrderChannelsList);
				response.put("Orderchannel", orderChannels);

				response.put("Return Time", System.currentTimeMillis());
				return response;

			} else {
				response.put("Status", 0);
				response.put("Msg", "No Order channel found.");
				log.error("No Order channel listed.");

				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting orderchannels.");
			response.put("Error", e.getLocalizedMessage());
			log.error("getOrderChannelV4 : Exception : " + e.getLocalizedMessage());

			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

	}

	@RequestMapping(value = "v5.0/countrycode", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCountryCode(@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "req_from", defaultValue = "", required = false) String req_from,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		log.info("Entered countrycode api : ");
		JResponse response = new JResponse();
		String backing = header.getFirst("backing");

		JBacking jBacking = _helper.backingKeyValidation(backing);

		if (jBacking.getStatus() <= 0) {
			response.put("Status", jBacking.getStatus());
			response.put("Msg", jBacking.getMsg());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		String auth = jBacking.getAuthKey();

		if (!auth.equalsIgnoreCase(validation_authkey)) {
			response.put("Status", 0);
			response.put("Msg", "Authentication Error");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		try {
			List<CountryCodeV4> countryCodeList = new ArrayList<CountryCodeV4>();
			countryCodeList = userServiceV4.getCountryCodeList(req_from);
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("CountryCode", countryCodeList);
		} catch (Exception e) {
			log.error("Exception in userV2 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}	

	@RequestMapping(value = "v5.0/forceUpdate", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse  isForceUpdateV5(@RequestParam("app") String app, @RequestParam("version") String version,
			@RequestParam("mobiletype") String mobiletype,
			@RequestParam(value = "userid", defaultValue = "", required = false) String userid,
			@RequestParam("os") String os,
			@RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		String backing = header.getFirst("backing");

		JBacking jBacking = _helper.backingKeyValidation(backing);

		if (jBacking.getStatus() <= 0) {
			response.put("Status", jBacking.getStatus());
			response.put("Msg", jBacking.getMsg());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		String auth = jBacking.getAuthKey();

		if (!auth.equalsIgnoreCase(validation_authkey)) {
			response.put("Status", 0);
			response.put("Msg", "Authentication Error");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		if (app.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Application Type Should Not Be Empty!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} else if (version.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Application verison Should Not Be Empty!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} else if (mobiletype.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Mobile Type Should Not Be Empty!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		// App id
		// 5 RFS_BG - Background
		// 4 RM-Restaurant Monitoring

		int app_i = Integer.parseInt(app);
		int version_i = Integer.parseInt(version.replaceAll("\\.", ""));
		int mobiletype_i = Integer.parseInt(mobiletype);

		String ip = "***************";

		String rv_android_V = "4.0.8";
		String rv_android_force = "0";
		String rv_android_force_V = "4.0.8";

		String rv_ios_V = "6.0.8";
		String rv_ios_force = "0";
		String rv_ios_force_V = "6.0.8";

		String iris3_android_V = "3.0.6";
		String iris3_android_force = "0";
		String iris3_android_force_V = "3.0.6";

		String iris3_ios_V = "5.0.8";
		String iris3_ios_force = "0";
		String iris3_ios_force_V = "5.0.8";

		String iris3_wl_android_V = "3.0.6";
		String iris3_wl_android_force = "0";
		String iris3_wl_android_force_V = "3.0.6";

		String iris3_wl_ios_V = "5.0.8";
		String iris3_wl_ios_force = "0";
		String iris3_wl_ios_force_V = "5.0.8";

		String rm_android_V = "1.0.4";
		String rm_android_force = "1";
		String rm_android_force_V = "1.0.2";

		String rm_ios_V = "1.0.4";
		String rm_ios_force = "1";
		String rm_ios_force_V = "1.0.2";

		String webservice_V = "3.2.0";
		String listener_V = "3.1.19";
		String webapp_V = "3.1.13";
		String database_V = "3.1.19";
		/*
		 * hotline username - mmarimut
		 *
		 * @ nimblewireless . com
		 */
		String rv_hotline_AppId = "cedda3bc-c628-4bac-8f65-9b408d437614_1";
		/*
		 * hotline username - mmarimut
		 *
		 * @ nimblewireless . com
		 */

		String rv_hotline_AppKey = "3ef7a41f-9419-4bbd-ba18-437f8eb83341_1";
		String rm_bg_ios_V = "1.0.2";
		String rm_bg_ios_force = "1.0.2";

		String rm_bg_android_V = "1.0.1";
		String rm_bg_android_force = "1.0.1";

		String rv_petprofile_force = "1";

		String s3bucketname_iris = "iris3.nimblewireless.com";
		String s3_key = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_access_key");
		String s3_secret = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_secret_key");

		Configuration config = new Configuration(ip, rv_android_V, rv_android_force, rv_android_force_V, rv_ios_V,
				rv_ios_force, rv_ios_force_V, iris3_android_V, iris3_android_force, iris3_android_force_V, iris3_ios_V,
				iris3_ios_force, iris3_ios_force_V, iris3_wl_android_V, iris3_wl_android_force,
				iris3_wl_android_force_V, iris3_wl_ios_V, iris3_wl_ios_force, iris3_wl_ios_force_V, rm_android_V,
				rm_android_force, rm_android_force_V, rm_ios_V, rm_ios_force, rm_ios_force_V, listener_V, webservice_V,
				webapp_V, database_V, rv_hotline_AppId, rv_hotline_AppKey, rm_bg_ios_V, rm_bg_ios_force,
				rm_bg_android_V, rm_bg_android_force, rv_petprofile_force, s3bucketname_iris, s3_key, s3_secret);

		/*
		 * Configuration config = new Configuration(ip, rv_android_V,
		 * rv_ios_V,iris3_android_V,webservice_V,listener_V,webapp_V,database_V,
		 * iris3_ios_V,rv_android_force,rv_ios_force,iris3_android_force,
		 * iris3_ios_force,rv_hotline_AppId,rv_hotline_AppKey,rm_android_V,
		 * rm_android_force,rm_ios_V, rm_ios_force);
		 */

		response.put("Status", 1);
		response.put("Msg", "success");

		try {
			Properties prop = new Properties();
			try {
				/* load a properties file */
				// prop.load(new FileInputStream("iris3.properties"));
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));
				ip = prop.getProperty("ip");
				rv_android_V = prop.getProperty("rv_android_V");
				rv_android_force = prop.getProperty("rv_android_force");
				rv_android_force_V = prop.getProperty("rv_android_force_V");

				rv_ios_V = prop.getProperty("rv_ios_V");
				rv_ios_force = prop.getProperty("rv_ios_force");
				rv_ios_force_V = prop.getProperty("rv_ios_force_V");

				iris3_android_V = prop.getProperty("iris3_android_V");
				iris3_android_force = prop.getProperty("iris3_android_force");
				iris3_android_force_V = prop.getProperty("iris3_android_force_V");

				iris3_ios_V = prop.getProperty("iris3_ios_V");
				iris3_ios_force = prop.getProperty("iris3_ios_force");
				iris3_ios_force_V = prop.getProperty("iris3_ios_force_V");

				iris3_wl_android_V = prop.getProperty("iris3_wl_android_V");
				iris3_wl_android_force = prop.getProperty("iris3_wl_android_force");
				iris3_wl_android_force_V = prop.getProperty("iris3_wl_android_force_V");

				iris3_wl_ios_V = prop.getProperty("iris3_wl_ios_V");
				iris3_wl_ios_force = prop.getProperty("iris3_wl_ios_force");
				iris3_wl_ios_force_V = prop.getProperty("iris3_wl_ios_force_V");

				rm_android_V = prop.getProperty("rm_android_V");
				rm_android_force = prop.getProperty("rm_android_force");
				rm_android_force_V = prop.getProperty("rm_android_force_V");

				rm_ios_V = prop.getProperty("rm_ios_V");
				rm_ios_force = prop.getProperty("rm_ios_force");
				rm_ios_force_V = prop.getProperty("rm_ios_force_V");

				webservice_V = prop.getProperty("webservice_V");
				listener_V = prop.getProperty("listener_V");
				webapp_V = prop.getProperty("webapp_V");
				database_V = prop.getProperty("database_V");

				rv_hotline_AppId = prop.getProperty("rv_hotline_AppId");
				rv_hotline_AppKey = prop.getProperty("rv_hotline_AppKey");

				rm_bg_ios_V = prop.getProperty("rm_bg_ios_V");
				rm_bg_ios_force = prop.getProperty("rm_bg_ios_force");

				rm_bg_android_V = prop.getProperty("rm_bg_android_V");
				rm_bg_android_force = prop.getProperty("rm_bg_android_force");

				rv_petprofile_force = prop.getProperty("rv_petprofile_force");

				s3bucketname_iris = prop.getProperty("s3bucketname_iris");
				s3_key = prop.getProperty("s3_key");
				s3_secret = prop.getProperty("s3_secret");

				ForceUpdate forceUpt = userService.getForceUpdate(userid);
				if (forceUpt != null) {
					if (0 < versionCompare(forceUpt.getAndroidVersion(), rv_android_force_V))
						rv_android_force_V = forceUpt.getAndroidVersion();
					if (0 < versionCompare(forceUpt.getIosVersion(), rv_ios_force_V))
						rv_ios_force_V = forceUpt.getIosVersion();
				}

				config = new Configuration(ip, rv_android_V, rv_android_force, rv_android_force_V, rv_ios_V,
						rv_ios_force, rv_ios_force_V, iris3_android_V, iris3_android_force, iris3_android_force_V,
						iris3_ios_V, iris3_ios_force, iris3_ios_force_V, iris3_wl_android_V, iris3_wl_android_force,
						iris3_wl_android_force_V, iris3_wl_ios_V, iris3_wl_ios_force, iris3_wl_ios_force_V,
						rm_android_V, rm_android_force, rm_android_force_V, rm_ios_V, rm_ios_force, rm_ios_force_V,
						listener_V, webservice_V, webapp_V, database_V, rv_hotline_AppId, rv_hotline_AppKey,
						rm_bg_ios_V, rm_bg_ios_force, rm_bg_android_V, rm_bg_android_force, rv_petprofile_force,
						s3bucketname_iris, s3_key, s3_secret);

				/*
				 * config = new Configuration(ip, rv_android_V,
				 * rv_ios_V,iris3_android_V,webservice_V,listener_V,webapp_V, database_V,
				 * iris3_ios_V,rv_android_force,rv_ios_force,iris3_android_force
				 * ,iris3_ios_force,rv_hotline_AppId,rv_hotline_AppKey,
				 * rm_android_V,rm_android_force, rm_ios_V, rm_ios_force);
				 */

				if (app_i == 6) // White label App
				{
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(iris3_wl_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(iris3_wl_ios_force_V, version));
					}
				} else if (app_i == 5) // Background App
				{
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(rm_bg_android_force, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(rm_bg_ios_force, version));
					}
				} else if (app_i == 4) {
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(rm_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(rm_ios_force_V, version));
					}
				} else if (app_i == 3) {
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(rv_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(rv_ios_force_V, version));
					}
				} else if (app_i == 2) {
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(iris3_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(iris3_ios_force_V, version));
					}
				} else {
					response.put("forceUpdate", "Invalid Version");
				}

			} catch (IOException ex) {
				log.error(ex.getLocalizedMessage());
			}
			
			response.put("show_download_popup", Boolean.parseBoolean(show_download_popup));
			response.put("show_download_content", "<center><div><p style=\"color: #000;font-size:18px;font-weight:bolder;\">A new version is available.</p><p style=\"color: #808080;font-size:15px;\">Enhance your pet monitoring experience by updating the app now.</p></div></center>");
			response.put("show_download_content_flutter_light", "<center><div><p style=\"color: #000;font-size:18px;font-weight:bolder;\">A new version available.</p><p style=\"color: #808080;font-size:15px;\">Enhance your pet monitoring experience by updating the app now.</p></div></center>");
			response.put("show_download_content_flutter_dark", "<center><div><p style=\"color: #fff;font-size:18px;font-weight:bolder;\">A new version available.</p><p style=\"color: #fff;font-size:15px;\">Enhance your pet monitoring experience by updating the app now.</p></div></center>");
			
			if(os.equalsIgnoreCase("iOS"))
				response.put("download_link", "https://itunes.apple.com/in/app/rv-petsafety/id1143805780?platform=iphone&preserveScrollPosition=true#platform/iphone");
			else
				response.put("download_link", "https://play.google.com/store/apps/details?id=com.nimble.petsafety");
			
			response.put("later_download", Boolean.parseBoolean(later_download));

			response.put("config", config);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			log.error("GetVersion::::::" + e.getMessage());
			response.put("Status", 1);
			response.put("Msg", "success");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}
	
	public boolean checkForForceupdate(String forceVersion, String appVersion) {
		log.info("checkForForceupdate : forceVersion = " + forceVersion + "appVersion = " + appVersion);
		/*
		 * int a_version = Integer.parseInt(appVersion.replaceAll("\\.","")); int
		 * f_version = Integer.parseInt(forceVersion.replaceAll("\\.",""));
		 *
		 * if(f_version > a_version) { log.info("checkForForceupdate:f_version: "
		 * +f_version+" is greater than app-version:" +a_version+" Force update needed"
		 * ); return true; }
		 */
		int result = versionCompare(forceVersion, appVersion);
		if (result > 0)// forceVersion >appVersion
			return true;

		return false;
	}

	/*
	 * The result is a negative integer if str1 is numerically less than str2. The
	 * result is a positive integer if str1 is numerically greater than str2. The
	 * result is zero if the strings are numerically equal
	 */
	public static int versionCompare(String str1, String str2) {
		String[] vals1 = str1.split("\\.");
		String[] vals2 = str2.split("\\.");
		int i = 0;
		// set index to first non-equal ordinal or length of shortest version
		// string
		while (i < vals1.length && i < vals2.length && vals1[i].equals(vals2[i])) {
			i++;
		}

		// compare first non-equal ordinal number
		if (i < vals1.length && i < vals2.length) {

			int diff = Integer.valueOf(vals1[i]).compareTo(Integer.valueOf(vals2[i]));

			return Integer.signum(diff);
		}

		// the strings are equal or one string is a substring of the other
		// e.g. "1.2.3" = "1.2.3" or "1.2.3" < "1.2.3.4"

		// compare e.g."1.2.3" == "1.2.3.0"
		if (vals1.length - vals2.length == -1) {

			if (vals2[i].equals("0"))
				return 0;
		} else if (vals1.length - vals2.length == 1) { // compare e.g."1.2.3.0"
			// == "1.2.3"

			if (vals1[i].equals("0"))
				return 0;
		}

		return Integer.signum(vals1.length - vals2.length);
	}
	
	@RequestMapping(value = "v5.0/kcalCalculation", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getkcalCalculation(@RequestBody List<JPetprofile> jpetprofiles,@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "req_from", defaultValue = "", required = false) String req_from,@RequestHeader HttpHeaders header) {
		log.info("Entered getkcalCalculation api : ");
		JResponse response = new JResponse();
		
		String basicAuth= header.getFirst("authorization");
		
		String deCodeAuth=new String(Base64.decodeBase64(basicAuth.substring(6)));
		String userName=deCodeAuth.split(":")[0];
		String password=deCodeAuth.split(":")[1];		

		if (userName == null || password == null || !userName.equals(kcalUsername) || !password.equals(kcalPassword) ) {
			response.put("Status", 0);
			response.put("Msg", "Authentication Error");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		try {
			double MER = 0;
			
			double weight = Double.parseDouble( jpetprofiles.get(0).getWeight() );
			double weightPounds = weight * 0.453592;
			double RER = 70 * Math.pow(weightPounds, 0.75);
			double signalment = petSpeciesServicesv4.getSignalment( jpetprofiles.get(0).getSpeciesid() , jpetprofiles.get(0).isIntact());		
			double BCS = petSpeciesServicesv4.getBCS( jpetprofiles.get(0).getStructure() != null && jpetprofiles.get(0).getStructure() != "" ? jpetprofiles.get(0).getStructure() : "ideal");							
			String ActivityLel = petSpeciesServicesv4.getFindActivitylevel( jpetprofiles.get(0).getActivitylevel());
			double activityLevel = petSpeciesServicesv4.getActivityLevel( ActivityLel);
			MER = RER * signalment * activityLevel * BCS;
			
			double kcal = (int)MER; 
			String content_1 = "Your Pet's Daily Calorie Requirements";
			String content_2 = "\"Vet Calculator\" advises calculating a pet's daily energy needs. This calculation incorporates weight and additional elements to establish the recommended daily energy intake for pets. ";
			String content_3 = "Suggested Daily Energy Intake: \n Approx. "+(int)MER+" kcal";
			response.put("kcal", kcal);
			
			response.put("content_1", content_1);
			response.put("content_2", content_2);
			response.put("content_3", content_3);
			
			response.put("Status", 1);
			response.put("Msg", "success");
			
		} catch (Exception e) {
			log.error("Exception in getkcalCalculation : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@GetMapping(value = "v5.0/productcategory")
	@ResponseBody
	public JResponse getProductCategory(
			@RequestHeader HttpHeaders header,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver) {
		JResponse response = new JResponse();
		log.info("Entering getProductCategory :: os : "+os+" :: app_ver : "+ app_ver);
		try {

			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidation(backing);

			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				return response;
			}

			if (!validation_authkey.equals(jBacking.getAuthKey())) {
				response.put("Status", -2);
				response.put("Msg", "Invalid Session, Please try again");
				return response;
			}
			
			ArrayList<JCategory> categorie_list = commonService.getProductCategory();
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("category_list", categorie_list);
			
		} catch (Exception e) {
			log.error("Error in getProductCategory :: Error : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Session, Please try again");
		}
		return response;
	}

	@RequestMapping(value = "v5.0/sendotp", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse sendOtp(@RequestHeader HttpHeaders header,
										   @RequestParam(defaultValue = "NA") String userName,
										   @RequestParam(defaultValue = "NA") String mobileNo,
										   @RequestParam(value = "os") String os,
										   @RequestParam(value = "app_ver") String app_ver) {
		log.info(" Entered into sendOtp :: username :" + userName);
		JResponse response = new JResponse();

		int localOtpTimer=0;
		localOtpTimer=validMinutesForOTP;
		String backing = header.getFirst("backing");
		JBacking jBacking = _helper.backingKeyValidation(backing);

		if (jBacking.getStatus() <= 0) {
			response.put("Status", jBacking.getStatus());
			response.put("Msg", jBacking.getMsg());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		if (!validation_authkey.equals(jBacking.getAuthKey())) {
			response.put("Status", -2);
			response.put("Msg", "Invalid Session, Please try again");
			return response;
		}

		User user = null;
		UserOtpVerification otpVerify = new UserOtpVerification();
		boolean already_user = false;
		int getOtp=0;
		try {


		//	boolean isSecondHit=true;
			if (userName.equalsIgnoreCase("NA")) {

				mobileNo = mobileNo.replaceAll("\\s+", "");
				if (mobileNo.charAt(0) != '-' && mobileNo.charAt(0) != '+')
					mobileNo = "+" + mobileNo;

				if (!mobileNo.contains("-")) {
					mobileNo = insertDashFromEnd(mobileNo);
				}

				user = userServiceV4.verifyUser("mobileno", mobileNo);
				otpVerify.setVerification_type("mobileno");
				otpVerify.setVerification_value(mobileNo);
				getOtp = userServiceV4.checkWithInOtpTimeLimit("user_otp_verification","verification_value",mobileNo);
				//isSecondHit=userServiceV4.getFirstHitTimeDiff("user_otp_verification","verification_value",mobileNo);
			} else {
				user = userServiceV4.verifyUser("username", userName);
				otpVerify.setVerification_type("username");
				otpVerify.setVerification_value(userName);
				getOtp = userServiceV4.checkWithInOtpTimeLimit("user_otp_verification","verification_value",userName);
				//isSecondHit=userServiceV4.getFirstHitTimeDiff("user_otp_verification","verification_value",userName);
			}

			if (user != null) {
				if (user.isDelete_user()) {
					response.put("Status", 0);
					response.put("Msg", "Your account deletion is in process. For more details reach our support team");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				if (!user.isEnable()) {
					response.put("Status", 0);
					response.put("Msg", "Account Disabled. Please contact support!");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				log.info(" user id : " + user.getId());
				already_user = true;
			}

			boolean show_password = true;
			if( !already_user || user.getPassword_ver().equalsIgnoreCase("v3") )
				show_password = false;

			long max = 9876;
			long min = 1234;
			int otp = (int) (Math.random() * (max - min + 1) + min);

			otp=(getOtp==(-1))?otp:getOtp;
			otpVerify.setOtp(otp);

			boolean isOTPSaved ;
			isOTPSaved = getOtp==(-1)?userServiceV4.saveUserOTPVerify(otpVerify):false;
			log.info(" OTP Generated : " + isOTPSaved + " OTP : " + otp);

			String smsMsg = otp + " is your OTP to user verification.";
			String otpInfo = "";


			if(getOtp!=-1)
			{
				int remainingTimer=0;
				if (userName.equalsIgnoreCase("NA")) {

					remainingTimer=userServiceV4.getOtpTimeLimit("user_otp_verification","verification_value",mobileNo);
				}
				else {
					remainingTimer=userServiceV4.getOtpTimeLimit("user_otp_verification","verification_value",userName);

				}
				localOtpTimer=	remainingTimer==-1 ? localOtpTimer : remainingTimer;
			}

			StringBuilder hiddenEmail = new StringBuilder();
			if( !show_password ) {
				if (userName.equalsIgnoreCase("NA")) {
//					log.info("isSecondHit  at Mobile Number : "+isSecondHit);
					String mobNumber=mobileNo;
					Plivo plivo = new Plivo();
					boolean smsSent = plivo.SendMessage(mobileNo, smsMsg, "1", "",
							"irisservice", plivoNumber, enablePowerBack, powerBackUUID);

					mobileNo = _helper.encodeString(mobileNo, 4, '*'); // params ( String, no_of_String_to_visible, encodeChar )
					otpInfo = "<p><center style=\"color: #919191;\"><h3>Verify your sms</h3>" + "Please enter the 4 digit code </br>sent to "
							+ mobileNo + " </center></p>";

					otpInfo = _helper.htmlContent(otpInfo);

					// Saving Hit time
					userServiceV4.saveFirstHitTimeDiff("user_otp_verification","verification_value",mobNumber);


				} else{
//					log.info("isSecondHit  at Email : "+isSecondHit);
					templates.setClassForTemplateLoading(this.getClass(), "/");
					Template oneTimePassword = (Template) templates.getTemplate("OneTimePassword.ftl");
					Map<String, String> OTPEmailParams = new HashMap<>();
					String minutes = localOtpTimer==1?" minute ":" minutes ";
					OTPEmailParams.put("OTP", String.valueOf(otp));
					OTPEmailParams.put("valid_minutes", localOtpTimer +minutes);

					ResponseEntity<String> newEmailContent = ResponseEntity
							.ok(FreeMarkerTemplateUtils.processTemplateIntoString(oneTimePassword, OTPEmailParams));
					String emailContent = newEmailContent.getBody();
					mailService.sendMail(userName, null,
							null, "Your Waggle Verification Code", emailContent);

					for (int i = 0; i < userName.length(); i++)
						hiddenEmail.append((i >= 2 && i < userName.indexOf('@')) ? '*' : userName.charAt(i));

					otpInfo = "<p><center style=\"color: #919191;\"><h3>Verify your email</h3>" + "Please enter the 4 digit code </br>sent to "
							+ hiddenEmail + " </center></p>";
					otpInfo = _helper.htmlContent(otpInfo);
					// Saving Hit time
					userServiceV4.saveFirstHitTimeDiff("user_otp_verification","verification_value",userName);
				}
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("otp_info", otpInfo);
			response.put("valid_otp_time", localOtpTimer * 60);
			response.put("already_user", already_user);
			response.put("show_password", show_password);

		} catch (Exception e) {
			log.error(" Error in sendOtp : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	public String insertDashFromEnd(String phone) {
		String reversed = new StringBuilder(phone).reverse().toString();

		if (reversed.length() > 10) {
			reversed = reversed.substring(0, 10) + "-" + reversed.substring(10);
		}

		return new StringBuilder(reversed).reverse().toString();
	}

	@PostMapping(value = "v5.0/validateotpandlogin", headers = "Accept=application/json")
	public @ResponseBody JResponse validateOtpAndLogin(
			@RequestHeader HttpHeaders header, @RequestParam long otp,
			@RequestParam(defaultValue = "NA") String userName,
			@RequestParam(defaultValue = "NA") String mobileNo,
			@RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver) {

		log.info(" Entered into validateOtpAndLogin ::");
		JResponse response = new JResponse();

		String backing = header.getFirst("backing");
		JBacking jBacking = _helper.backingKeyValidation(backing);

		if (jBacking.getStatus() <= 0) {
			response.put("Status", jBacking.getStatus());
			response.put("Msg", jBacking.getMsg());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		if (!validation_authkey.equals(jBacking.getAuthKey())) {
			response.put("Status", -2);
			response.put("Msg", "Invalid Session, Please try again");
			return response;
		}

		User user = null;
		UserOtpVerification otpVerification = new UserOtpVerification();
		try {
			if (userName.equalsIgnoreCase("NA")) {

				mobileNo = mobileNo.replaceAll("\\s+", "");
				if (mobileNo.charAt(0) != '-' && mobileNo.charAt(0) != '+')
					mobileNo = "+" + mobileNo;

				user = userServiceV4.verifyUser("mobileno", mobileNo);
				otpVerification.setVerification_type("mobileno");
				otpVerification.setVerification_value(mobileNo);
			} else {
				user = userServiceV4.verifyUser("username", userName);
				otpVerification.setVerification_type("username");
				otpVerification.setVerification_value(userName);
			}

			otpVerification.setOtp((int) otp);
			boolean validOTP = false;
			if( debug_otp == otpVerification.getOtp() ) {
				validOTP = true;
			} else {
				validOTP = userServiceV4.validateUserOTP(otpVerification, validMinutesForOTP);
			}
			log.info("is valid OTP : " + validOTP);
			if(validOTP) {
				if(user != null) {
					String clientId = clientIdApp;
					String clientSecret = clientSecretApp;

					String password = user.getPassword();
					if (user.getPassword_ver().equalsIgnoreCase("V2")) {
						password = userServiceV4.getEValidationPassword(user.getId());
					}

					byte[] oAuth2token = oAuth2Service.generateOauth2Token(user.getUsername(),
							password, clientIdApp, clientSecretApp);

					if (oAuth2token != null) {
						response.put("token", oAuth2token);
					} else {
						response.put("token", null);
					}
					user.setPassword("NA");
					response.put("User", user);
				} else {
					response.put("User", null);
				}
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "OTP Not Valid");
			}
		} catch (Exception e) {
			log.error(" Error in validateOtpAndLogin " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
}
