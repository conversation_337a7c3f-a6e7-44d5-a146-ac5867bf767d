package com.nimble.irisservices.appcontroller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JFeatureCredit;
import com.nimble.irisservices.dto.JPauseHistory;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AllProductSubscription;
import com.nimble.irisservices.entity.AllSubscription;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyCreditMonitor;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.VersionMapping;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IThrottlingService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class CreditSystemControllerApp {
	private static final Logger log = LogManager.getLogger(CreditSystemControllerApp.class);

	@Autowired
	@Lazy
	ICompanyService cmpService;

	@Autowired
	@Lazy
	IThrottlingService thrService;

	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	IUserService userService;

	@Autowired
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IChargebeeService chargebeeService;
	
	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	Helper _helper = new Helper();

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Value("${feature_plan_flag}")
	private String feature_plan_flag;

	@Value("${freeplan}")
	private String freeplan;

	@Value("${omitplan}")
	private String omitplan;

	@Value("${vpmplan}")
	private String vpmplan;
	
	@Value("${show_alertlimit}")
	private boolean show_alertlimit = false;
	
	@Autowired
	IGatewayService gatewayService;
	
	//Revamp Flutter App - currently used
	@RequestMapping(value = "v5.0/getalertlimits", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertLimits(@RequestHeader HttpHeaders header,	@RequestParam("os") String os,	@RequestParam("app_ver") String app_ver,
			Authentication authentication,@RequestParam("app_type") String app_type,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "req_ver", defaultValue = "V1", required = false) String reqVer,
			@RequestParam(value = "gateway_id", defaultValue = "0", required = false) String gateway_id) {
		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		log.info("getalertslimit :"+ auth);
				
		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("getalertslimitV2:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid User");
			response.put("Error", e.getMessage());
		}

		try {
			if (user != null) {
				if (user.getPlan_ver().equalsIgnoreCase("V1")) {
					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					boolean enable_appnotify = true;
					String os_type = os;
//					if(app_type.equalsIgnoreCase("flutter"))
//						os_type =  "flutter";
					
					VersionMapping verObj = crService.getVersionMapping(app_ver, os_type);

					if (verObj != null) {
						enable_appnotify = verObj.isEnable_appnotify();
					}
					
					boolean smsEnable=false;
					boolean emailEnable=false;
					boolean appNotifyEnable=false;
					ArrayList<JFeatureCredit> setting_features = new ArrayList<JFeatureCredit>();
					
					if(gateway_id != null && !gateway_id.equalsIgnoreCase("0") && user.getPlan_ver().equalsIgnoreCase("V3")) {
						setting_features  = crService.getSettingGatewayFeatures(Long.valueOf(gateway_id));
					} else {
						setting_features  = crService.getSettingFeatures(user.getId());
					}
					
					for(JFeatureCredit setting_feature: setting_features) {
						if(setting_feature.getFeature_code().equalsIgnoreCase("N_MOBILE") ) {
							smsEnable = true;
							if(setting_feature.getF_cnt()==0) {
								smsEnable=false;
							}
						} else if(setting_feature.getFeature_code().equalsIgnoreCase("N_EMAIL")) {
							emailEnable = true;
							if(setting_feature.getF_cnt()==0) {
								emailEnable=false;
							}

						} else if(setting_feature.getFeature_code().equalsIgnoreCase("PUSH_NOTIFY")) {
							appNotifyEnable = true;
							if(setting_feature.getF_cnt()==0) {
								appNotifyEnable=false;
							}

						}
					}

					if(gateway_id != null && !gateway_id.equalsIgnoreCase("0") && user.getPlan_ver().equalsIgnoreCase("V3")) {
						
						String alertlimit_basedon = _helper.getExternalConfigValue("alertcount", externalConfigService);
					
						long gatewayId = crService.findaqiqndcoenabledevicebyGateway(Long.valueOf(gateway_id));

						response = crService.getalertslimitV5ByGateway(Long.valueOf(gateway_id), alertlimit_basedon, enable_appnotify,reqVer, smsEnable, emailEnable, appNotifyEnable,gatewayId);
						Gateway gatewayObj = gatewayService.getGatewayByid(Long.valueOf(gateway_id));
						long mtype = gatewayObj.getModel().getMonitor_type().getId();
						AllProductSubscription allProdSub = chargebeeService.getProductSubscriptionByGatewayId(Long.valueOf(gateway_id), user.getChargebeeid(),mtype);
						if (allProdSub != null) {
							JPauseHistory pauseHis = null;
							
							if(allProdSub.getSubscriptionId() != null)
								 pauseHis = crService.getPauseHistoryRecord(allProdSub.getSubscriptionId());
							
							if(pauseHis != null && pauseHis.getResumeDate() != null && pauseHis.getStatus() == 1) {
									response.put("next_renewal_date", pauseHis.getResumeDate());
									response.put("next_alert_renewal_content", "Resume Date");
							}
						}

					} else {
		
						String alertlimit_basedon = _helper.getExternalConfigValue("alertcount", externalConfigService);
						
						long gatewayId = crService.findaqiqndcoenabledevice(user.getId());

						response = crService.getalertslimitV5(user.getId(), alertlimit_basedon, enable_appnotify,reqVer, smsEnable, emailEnable, appNotifyEnable,gatewayId);
						
						List<AllSubscription> subscription = chargebeeService.getSubscriptionByChargebeeId(user.getChargebeeid());
						if (subscription != null) {
							JPauseHistory pauseHis = null;
								for (AllSubscription subs : subscription) {
									if(subs.getSubscriptionId() != null)
										pauseHis = crService.getPauseHistoryRecordAllCBS(subs.getSubscriptionId());
									
									if(pauseHis != null && pauseHis.getResumeDate() != null && pauseHis.getStatus() == 1) {
											response.put("next_renewal_date", pauseHis.getResumeDate());
											response.put("next_alert_renewal_content", "Resume Date");
									}
								}
						}
					}
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			log.error("getalertlimits Excep:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());

		}
		response.put("show_alertlimit", show_alertlimit);
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	public void updateCreditMonitor(long cmpid, int cr, int excr) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			Company cmpy = cmpService.getCompany(cmpid);
			ThrottlingSettings throtsettings = thrService.getThrotSettingsById(cmpy.getThrotsettings().getId());
			CompanyCreditMonitor ccm = crService.getCompanyCreditMonitorByCmpy(cmpy.getId());

			if (ccm == null) {
				ccm = new CompanyCreditMonitor();
				ccm.setCmp_id(cmpy);
				ccm.setCredit_assigned((int) throtsettings.getCredits());
				ccm.setEx_credit_assigned((int) throtsettings.getExtra_credits());
				ccm.setCredit_spent(0);
				ccm.setEx_credit_spent(0);
				ccm.setResetmonthly(false);

			}
			// add credit spent with existing cr spent and extra credit spent with existing
			// extra cr spent
			int cr_spent = ccm.getCredit_spent() + cr;
			int ex_cr_spent = ccm.getEx_credit_spent() + excr;

			ccm.setCredit_spent(cr_spent);
			ccm.setEx_credit_spent(ex_cr_spent);

			status = crService.upateCredits(ccm);

			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "invalid plantomonitorid");
			log.error("updateCreditMonitor : " + e.getLocalizedMessage());
		}

	}

}
