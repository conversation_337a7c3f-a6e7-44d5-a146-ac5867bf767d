package com.nimble.irisservices.appcontroller;

import java.io.File;
import java.io.FileInputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;

import com.nimble.irisservices.dto.JTerms;
import com.nimble.irisservices.dto.JVetPlanDetails;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.VersionMapping;
import com.nimble.irisservices.entity.VetChatPlan;
import com.nimble.irisservices.entity.VetChatPlanUser;
import com.nimble.irisservices.entity.VetChatPlanUserId;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Email;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.impl.VetChatServiceImpl;

import freemarker.template.Template;

@RestController
@RequestMapping("/app")
public class VetChatControllerApp {

	private static final Logger log = LogManager.getLogger(VetChatControllerApp.class);

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	Helper _helper;

	@Autowired
	@Lazy
	private VetChatServiceImpl vetChatServiceImpl;
	
	@Autowired
	@Lazy
	ICreditSystemService crService;
	
	@Value("#{${vetchat_terms_and_conditions}}")
	private List<String> vetchat_terms_and_conditions;

	@Value("${vetchat_chosen_main_title}")
	private String vetchat_chosen_main_title;

	@Value("${vetchat_chosen_description}")
	private String vetchat_chosen_description;

	@Value("${vetchat_start_main_title}")
	private String vetchat_start_main_title;

	@Value("${vetchat_start_description}")
	private String vetchat_start_description;
	
	@Value("${vetchat_noneligible_free}")
	private String vetchat_noneligible_free;
	
	@Value("${vetchat_noneligible_paid}")
	private String vetchat_noneligible_paid;
	
	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;
	
	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;
	
	@Autowired
	freemarker.template.Configuration templates;
	
	private String vet_chat_subject = "Welcome to Waggle's VetChat!";
	
	@Autowired
	Email email_helper;

	@GetMapping("v5.0/vetchat")
	public ResponseEntity<Map<String, Object>> getVetChatPlan(Authentication authentication,
			@RequestHeader HttpHeaders header, @RequestParam String os, @RequestParam String app_ver,
			@RequestParam(defaultValue = "V1", required = false) String req_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam String email) {

		String auth = header.getFirst("auth");
		log.info("Entered into shopInfo :: auth : " + auth + " req_ver : " + req_ver);

		Map<String, Object> responseMap = new HashMap<String, Object>();
		responseMap.put("terms_and_conditions", vetchat_terms_and_conditions);
		responseMap.put("vetchat_chosen_main_title", vetchat_chosen_main_title);
		responseMap.put("vetchat_chosen_description", vetchat_chosen_description);
		responseMap.put("vetchat_noneligible_free", vetchat_noneligible_free);
		responseMap.put("vetchat_noneligible_paid", vetchat_noneligible_paid);
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				responseMap.put("Status", 0);
				responseMap.put("Msg", "Invalid Authkey");
				return ResponseEntity.ok(responseMap);
			}

			log.info("username : " + user.getUsername());

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				responseMap.put("Status", errResponse.get("Status"));
				responseMap.put("Msg", errResponse.get("Msg"));
				return ResponseEntity.ok(responseMap);
			}

			boolean isEligible = vetChatServiceImpl.isUserEligible(email);
			responseMap.put("Status", 1);
			responseMap.put("Msg", "Success");
			responseMap.put("eligible", isEligible);

			List<VetChatPlan> vetChatPlanList = vetChatServiceImpl.getVetChatPlans();
			responseMap.put("plan_list", vetChatPlanList != null ? vetChatPlanList : new ArrayList<>());

			VetChatPlanUser vetChatPlanUser = vetChatServiceImpl.getVetChatUserPlan(user.getId());
			if (vetChatPlanUser != null) {
				if (vetChatPlanUser.getVetChatPlanUser().getChat_initiated_at().startsWith("1753")) {
					responseMap.put("plan_id", vetChatPlanUser.getVetChatPlanUser().getPlan_id());
					responseMap.put("expiry_time", vetChatPlanUser.getVetChatPlanUser().getExpiry_time());
					responseMap.put("pet_profile_id", vetChatPlanUser.getVetChatPlanUser().getPet_profile_id());
					responseMap.put("chat_initiated_at", null);
				} else {
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					Date chatDate = sdf.parse(vetChatPlanUser.getVetChatPlanUser().getChat_initiated_at());
					
					Calendar cal = Calendar.getInstance();
					cal.setTime(chatDate);
					cal.add(Calendar.DATE, 1);
					
					if (cal.getTimeInMillis() <= Calendar.getInstance().getTimeInMillis()) {
						if (isEligible) {
							vetChatServiceImpl.removeEligibleUser(email);
							vetChatServiceImpl.removeEligibleUserPlan(user.getId());
						} else {
							vetChatServiceImpl.removeEligibleUserPlan(user.getId());
						}
						responseMap.put("plan_id", 0);
						responseMap.put("expiry_time", "1753-01-01 00:00:00");
						responseMap.put("pet_profile_id", 0);
						responseMap.put("chat_initiated_at", null);
						responseMap.put("eligible", false);
					} else {
						responseMap.put("plan_id", vetChatPlanUser.getVetChatPlanUser().getPlan_id());
						responseMap.put("expiry_time", vetChatPlanUser.getVetChatPlanUser().getExpiry_time());
						responseMap.put("pet_profile_id", vetChatPlanUser.getVetChatPlanUser().getPet_profile_id());
						responseMap.put("chat_initiated_at", vetChatPlanUser.getVetChatPlanUser().getChat_initiated_at());
					}
				}
			} else {
				responseMap.put("plan_id", 0);
				responseMap.put("expiry_time", "1753-01-01 00:00:00");
				responseMap.put("pet_profile_id", 0);
				responseMap.put("chat_initiated_at", null);
			}
			
			if (!app_ver.isEmpty() && !os.isEmpty()) {
				VersionMapping verObj = crService.getVersionMapping(app_ver, os);
				if (verObj != null) {
					responseMap.put("external_redirect", verObj.isVetchat_redirect());
				}
			}

			return ResponseEntity.ok(responseMap);
		} catch (Exception e) {
			log.error("Error in getVetChatPlan : "+e.getLocalizedMessage());
			responseMap.put("Status", 0);
			responseMap.put("Msg", "Invalid request");
			responseMap.put("Error", e.getLocalizedMessage());
		}
		return ResponseEntity.ok(responseMap);
	}

	@PostMapping("v5.0/vetchat")
	public ResponseEntity<Map<String, Object>> saveVetChatPlan(Authentication authentication,
			@RequestHeader HttpHeaders header, @RequestParam String os, @RequestParam String app_ver,
			@RequestParam(defaultValue = "V1", required = false) String req_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam int plan_id, @RequestParam String expiry_time,
			@RequestParam(required = false) long pet_profile_id,
			@RequestParam(required = false) boolean eligible_expired) {

		String auth = header.getFirst("auth");
		log.info("Entered into saveVetChatPlan :: auth : " + auth + " req_ver : " + req_ver);

		Map<String, Object> responseMap = new HashMap<String, Object>();
		responseMap.put("vetchat_start_main_title", vetchat_start_main_title);
		responseMap.put("vetchat_start_description", vetchat_start_description);
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				responseMap.put("Status", 0);
				responseMap.put("Msg", "Invalid Authkey");
				return ResponseEntity.ok(responseMap);
			}
			
			/**Get country from App */
			String support_country = user.getCountry();
			if( support_country == null || support_country.isEmpty() || support_country.equalsIgnoreCase("US")|| support_country.equalsIgnoreCase("NA")
					|| support_country.toLowerCase().contains("india") || support_country.equalsIgnoreCase("in")
				 ) {
				support_country = "US";
			}
			String supportM = supportContactEmail.get(support_country);
			String supportP = supportContactNumber.get(support_country);

			log.info("username : " + user.getUsername());

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				responseMap.put("Status", errResponse.get("Status"));
				responseMap.put("Msg", errResponse.get("Msg"));
				return ResponseEntity.ok(responseMap);
			}

			expiry_time = expiry_time != null ? expiry_time.replace("T", " ") : expiry_time;
			expiry_time =  _helper.getUTCDatev2(expiry_time, "UTC");

			if (eligible_expired) {
				pet_profile_id = 0;
				vetChatServiceImpl.removeEligibleUser(user.getUsername());
			}
			VetChatPlanUserId vetChatPlanUserId = new VetChatPlanUserId(user.getId(), plan_id, expiry_time,
					pet_profile_id);
			VetChatPlanUser vetChatPlanUser = new VetChatPlanUser();
			vetChatPlanUser.setVetChatPlanUser(vetChatPlanUserId);

			boolean status = false;
			try {
				status = vetChatServiceImpl.saveVetChatPlan(vetChatPlanUser);
			} catch (DataIntegrityViolationException e) {
				status = vetChatServiceImpl.updateVetChatPlan(vetChatPlanUser);
			}
			
			if (status) {
				responseMap.put("Status", 1);
				responseMap.put("Msg", "Success");
			} else {
				responseMap.put("Status", 0);
				responseMap.put("Msg", "Failed");
			}
			
			if (!vetChatServiceImpl.isUserEligible(user.getUsername())) {
				VetChatPlan vetChatPlan = vetChatServiceImpl.getPlanById(plan_id);
				if (vetChatPlan != null && vetChatPlan.getTitle().contains("Emergency Fund")) {
					responseMap.replace("vetchat_start_description", vetchat_noneligible_paid);
					responseMap.replace("vetchat_start_main_title", "");
				} else {
					responseMap.replace("vetchat_start_description", vetchat_noneligible_free);
					responseMap.replace("vetchat_start_main_title", "");
				}
			} else {
				Template template = (Template) templates.getTemplate("vetchat.ftl");
				Map<String, String> vetChatEmailParams = new HashMap<>();
				vetChatEmailParams.put("FIRSTNAME", user.getFirstname());
				vetChatEmailParams.put("COPYRIGHT_YEAR", _helper.getCurrentYear());
				vetChatEmailParams.put("SUPPORT_EMAIL", supportM);
				vetChatEmailParams.put("SUPPORT_PHONE_WITH_COUNTRYCODE", supportP);
				String operating_hours = "10 AM - 8 PM EST";
				if(user.getCountry().equalsIgnoreCase("AU"))
				{
					operating_hours = "7 AM - 6 PM AEDT";
				}
				vetChatEmailParams.put("OPERATING_HOURS", operating_hours);
				
				Properties prop = new Properties();
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));

				String bcc_address = prop.getProperty("bcc_address");

				ResponseEntity<String> vetChatEmailContent = ResponseEntity
						.ok(FreeMarkerTemplateUtils.processTemplateIntoString(template, vetChatEmailParams));
				String emailContent = vetChatEmailContent.getBody();
				email_helper.SendEmail_SES(user.getEmail(), "", bcc_address, vet_chat_subject, emailContent);
				log.info("Email sent.");
			}

			return ResponseEntity.ok(responseMap);
		} catch (Exception e) {
			log.error("Error in saveVetChatPlan : "+e.getLocalizedMessage());
			responseMap.put("Status", 0);
			responseMap.put("Msg", "Invalid request");
			responseMap.put("Error", e.getLocalizedMessage());
		}
		return ResponseEntity.ok(responseMap);
	}
	
	@PostMapping("v5.0/updatevetchateligibility")
	public ResponseEntity<Map<String, Object>> updateVetChatEligibility(Authentication authentication,
			@RequestHeader HttpHeaders header, @RequestParam String os, @RequestParam String app_ver,
			@RequestParam(defaultValue = "V1", required = false) String req_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam String email, @RequestParam String chat_initiated_at) {

		String auth = header.getFirst("auth");
		log.info("Entered into updateVetChatEligibility :: auth : " + auth + " req_ver : " + req_ver);

		Map<String, Object> responseMap = new HashMap<String, Object>();
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				responseMap.put("Status", 0);
				responseMap.put("Msg", "Invalid Authkey");
				return ResponseEntity.ok(responseMap);
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				responseMap.put("Status", errResponse.get("Status"));
				responseMap.put("Msg", errResponse.get("Msg"));
				return ResponseEntity.ok(responseMap);
			}
			
			VetChatPlanUser vetChatPlanUser = vetChatServiceImpl.getVetChatUserPlan(user.getId());
			vetChatPlanUser.getVetChatPlanUser().setChat_initiated_at(chat_initiated_at);

			boolean status = false;
			try {
				status = vetChatServiceImpl.saveVetChatPlan(vetChatPlanUser);
			} catch (DataIntegrityViolationException e) {
				status = vetChatServiceImpl.updateVetChatPlan(vetChatPlanUser);
			}
			
			if (status) {
				responseMap.put("Status", 1);
				responseMap.put("Msg", "Success");
			} else {
				responseMap.put("Status", 0);
				responseMap.put("Msg", "Failed");
			}

			return ResponseEntity.ok(responseMap);
		} catch (Exception e) {
			log.error("Error in updateVetChatEligibility : "+e.getLocalizedMessage());
			responseMap.put("Status", 0);
			responseMap.put("Msg", "Invalid request");
			responseMap.put("Error", e.getLocalizedMessage());
		}
		return ResponseEntity.ok(responseMap);
	}

	@PostMapping("v5.0/sendvetchatemail")
	public JResponse sendvetchatemail(Authentication authentication,
															   @RequestHeader HttpHeaders header, @RequestParam String os, @RequestParam String app_ver,
															   @RequestParam(defaultValue = "V1", required = false) String req_ver) {

		String auth = header.getFirst("auth");
		log.info("Entered into sendvetchatemail :: auth : " + auth + " req_ver : " + req_ver);

		JResponse response = new JResponse();
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth sendvetchatemail :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			/**Get country from App */
			String support_country = user.getCountry();
			if (support_country == null || support_country.isEmpty() || support_country.equalsIgnoreCase("US") || support_country.equalsIgnoreCase("NA")
					|| support_country.toLowerCase().contains("india") || support_country.equalsIgnoreCase("in")
			) {
				support_country = "US";
			}
			String supportM = supportContactEmail.get(support_country);
			String supportP = supportContactNumber.get(support_country);

			log.info("username : " + user.getUsername());

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				response.put("Status", errResponse.get("Status"));
				response.put("Msg", errResponse.get("Msg"));
				return response;
			}

			Template template = (Template) templates.getTemplate("vetchatNew.ftl");
			Map<String, String> vetChatEmailParams = new HashMap<>();
			vetChatEmailParams.put("FIRSTNAME", user.getFirstname());
			vetChatEmailParams.put("COPYRIGHT_YEAR", _helper.getCurrentYear());
			vetChatEmailParams.put("SUPPORT_EMAIL", supportM);
			vetChatEmailParams.put("SUPPORT_PHONE_WITH_COUNTRYCODE", supportP);
			String operating_hours = "10 AM - 8 PM EST";
			if (user.getCountry().equalsIgnoreCase("AU")) {
				operating_hours = "7 AM - 6 PM AEDT";
			}
			vetChatEmailParams.put("OPERATING_HOURS", operating_hours);

			Properties prop = new Properties();
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			prop.load(new FileInputStream(file));

			String bcc_address = prop.getProperty("bcc_address");

			ResponseEntity<String> vetChatEmailContent = ResponseEntity
					.ok(FreeMarkerTemplateUtils.processTemplateIntoString(template, vetChatEmailParams));
			String emailContent = vetChatEmailContent.getBody();
			ArrayList<JVetPlanDetails> splan = crService.getVetPlanTerms("Vet-Plan");
			File file1 = null;
			if (splan != null && splan.size() > 0) {
				List<JTerms.Terms> termList = splan.stream()
						.flatMap(details -> details.getTerm_list().stream())
						.collect(Collectors.toList());
				file1 = _helper.writeTextDataTermcondition(termList);
			}
			email_helper.SendEmail_SESWithTxtfile(user.getEmail(), "", bcc_address, vet_chat_subject, emailContent, file1.getAbsolutePath());
			log.info("Email sent.");
			file1.delete();
			vetChatServiceImpl.saveVetChatMail(user.getId());
			response.put("Status", 1);
			response.put("Msg", "Success");
			return response;
		} catch (Exception e) {
			log.error("Error in sendvetchatemail : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid request");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}
}
