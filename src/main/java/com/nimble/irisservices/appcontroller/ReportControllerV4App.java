package com.nimble.irisservices.appcontroller;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JAssetLastReportV4;
import com.nimble.irisservices.dto.JAssetLastReportWatch;
import com.nimble.irisservices.dto.JAssetPetKcalReport;
import com.nimble.irisservices.dto.JGatewayGeneralInfo;
import com.nimble.irisservices.dto.JGatewayGeneralInfoV5;
import com.nimble.irisservices.dto.JGatewayMonthList;
import com.nimble.irisservices.dto.JGatewaySubSetup;
import com.nimble.irisservices.dto.JGatewayTimeList;
import com.nimble.irisservices.dto.JGatewayWeekList;
import com.nimble.irisservices.dto.JMeariNotification;
import com.nimble.irisservices.dto.JPetmonitorHistory;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSensorType;
import com.nimble.irisservices.dto.JSensorTypeCode;
import com.nimble.irisservices.dto.JTimeInterval;
import com.nimble.irisservices.dto.ShipmentDetailV2;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.dto.sensorReportData;
import com.nimble.irisservices.entity.DashboardBanner;
import com.nimble.irisservices.entity.MonitorType;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Email;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IPetSpeciesServicesV4;
import com.nimble.irisservices.service.IReminderService;
import com.nimble.irisservices.service.IReportServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.impl.VetChatServiceImpl;

@Controller
@RequestMapping("/app")
public class ReportControllerV4App {

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	IReportServiceV4 reportServiceV4;

	@Autowired
	IReminderService reminderservice;

	@Value("${notify_freq_msg}")
	private String notify_freq_msg;
	
	@Value("${coupon_img_order}")
	private String coupon_img_order;
	
	@Value("${oldDevicePlanPurchase}")
	private boolean oldDevicePlanPurchase;

	@Value("${oldDevicePlanPurchaseMSG}")
	private String oldDevicePlanPurchaseMSG;

	@Autowired
	@Lazy
	IPetSpeciesServicesV4 ipetSpeciesServicesv4;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	Helper _helper;

	@Value("${show_markettingbanner}")
	private boolean show_markettingbanner;

	@Value("${web_rtc_url}")
	private String web_rtc_url;
	
	@Value("${reportemail}")
	private String reportemail;

	@Autowired
	IChargebeeService chargebeeService;
	
	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;
	
	@Autowired
	VetChatServiceImpl vetChatServiceImpl;
	
	@Autowired
	INiomDataBaseService niomService;

	@Autowired
	private Email email;

	@Value("${show_chat}")
	private boolean show_chat;
	
	@Value("${vetchat_homepage_title}")
	private String vetchat_homepage_title;
	
	@Value("${vetchat_homepage_description}")
	private String vetchat_homepage_description;
	
	@Value("${vetchat_homepage_price}")
	private String vetchat_homepage_price;
	
	@Value("${show_vetchat}")
	private boolean show_vetchat;

	@Value("${appVerFromConfig}")
	private String appVerFromConfig;

	private static final Logger log = LogManager.getLogger(ReportControllerV4App.class);

	@RequestMapping(value = "v5.0/gatewaysummaryTest/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGatewaySummaryTestV5(@RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("offset") String offset,
			@RequestParam("limit") String limit,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
			@RequestParam(value = "backing", defaultValue = "MT", required = false) String backing,
			Authentication authentication, @RequestHeader HttpHeaders header) {

		log.info("called gateway summary services" + "groupid: " + groupid + ", subgroupid: " + subgroupid
				+ ", assetgroupid: " + assetgroupid + " , gatewayid: " + gatewayid);
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering getAssetSummaryTestV4 : " + autho);
		try {
			Map<String, String> map = new HashMap<String, String>();

			try {

//				Helper _helper = new Helper();
				log.info("backing key : " + backing);

//				AES aes = new AES();
//				String auth = null;
//				if (backing != null) {
//					if (!backing.equals("MT")) {
//						String[] credential = _helper.decodeInternalKey(backing);
//						String finalOut = aes.decode(credential[0], credential[1]);
//						
//						if (finalOut == null) {
//							response.put("Status", 0);
//							response.put("Msg", "Authentication Error");
//							return response;
//						}
//						log.info("AES decryption success : "+backing+" : "+finalOut);
//					}
//				} else {
//					response.put("Status", 0);
//					response.put("Msg", "Authentication Error");
//					return response;
//				}
				map = userServiceV4.getUserId_cmpIdByAuth(autho);

				JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String country = map.get("country");
			if (country.equalsIgnoreCase("US") || country.equalsIgnoreCase("NA")
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in") || country.isEmpty()
					|| country == null) {
				country = "US";
			}

			long userId = Long.valueOf(map.get("user_id"));
			long cmp_id = Long.valueOf(map.get("cmp_id"));
			String delete_user = map.get("delete_user");
			int remainderOverdueCount = -1;

			if (delete_user.equalsIgnoreCase("1") || delete_user.equalsIgnoreCase("true")) {
				response.put("delete_user", true);
			} else {
				response.put("delete_user", false);
			}

			List<JAssetLastReportV4> reportsummmary = reportServiceV4.getLastgatewayreportV4(groupid, subgroupid,
					assetgroupid, gatewayid, userId, offset, limit, map.get("tempunit"), country);
			log.info("received gateway summary , report length: " + reportsummmary.size());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("lastgatewayreport", reportsummmary);
			response.put("oldDevicePlanPurchase", oldDevicePlanPurchase);
			response.put("oldDevicePlanPurchaseMSG", oldDevicePlanPurchaseMSG);
			response.put("delete_content",
					"Your account deletion is in process. For more details reach our support team");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in gatewaysummaryTest");
			response.put("Error", e.getLocalizedMessage());
			response.put("oldDevicePlanPurchase", oldDevicePlanPurchase);
			response.put("oldDevicePlanPurchaseMSG", oldDevicePlanPurchaseMSG);

			log.error("Exception : getGatewaySummaryTestV4 : " + e.getLocalizedMessage());

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// ========get sensorReports ================
	@RequestMapping(value = "v5.0/getsensorreports", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getsensorreport(@RequestParam("date") String date, Authentication authentication,
			@RequestParam("gatewayid") long gatewayid, @RequestParam("timezone") String timeZone,
			@RequestHeader HttpHeaders header,
			@RequestParam("os") String os, @RequestParam("app_ver") String app_ver) {

		String auth = header.getFirst("auth");
		log.info("Entered into getsensorreport :: auth : " + auth);
		JResponse response = new JResponse();
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", auth);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid data");
			return response;
		}

		try {
			List<sensorReportData> reportsummmary = reportServiceV4.getSensorRep(gatewayid, date, timeZone);
			
			log.info("received gateway summary , report length: " + reportsummmary.size());
			if (!reportsummmary.isEmpty()) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("reports", reportsummmary);
			} else {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("reports", reportsummmary);
				response.put("msg", "No records found");
			}

		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid data");
			return response;
		}

		return response;

	}

	// ========get sensorGroupReports ================
	@GetMapping(value = "v5.0/exportsensorreports", headers = "Accept=application/json")
	@ResponseBody
	public JResponse exportsensorreports(@RequestParam("fromdate") String fromdate,
			@RequestParam("todate") String todate, Authentication authentication,
			@RequestParam("gatewayid") Long gatewayid, @RequestParam("timezone") String timeZone,
			@RequestHeader HttpHeaders header) {

		String auth = header.getFirst("auth");
		log.info("Entered into getsensorreport :: auth : " + auth);
		JResponse response = new JResponse();
		String mail = "NA";
		String Uname = "NA";
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", auth);
			mail = user.getEmail();
			Uname = user.getFirstname();
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid data");
			return response;
		}
		File file = null;
//		File zipFile = null;
		try {
			Helper _helper = new Helper();

			List<sensorReportData> reportsummmary = reportServiceV4.getSensoGrpRep(gatewayid, fromdate, todate,
					timeZone);
			log.info("received sensor report summary , report length: " + reportsummmary.size());
			if (!reportsummmary.isEmpty()) {
				HashMap<String, String> deviceInfo = reportServiceV4.getNSInfo(gatewayid);
				String devicename = deviceInfo.containsKey("gname") ? deviceInfo.get("gname"): "";
				String sensorname = deviceInfo.containsKey("stype") ? deviceInfo.get("stype"): "";
				
				file = _helper.writeCSVDataLineByLine(reportsummmary, fromdate, todate, devicename);
//				String fileName = devicename + "_" + fromdate + "_" + todate;
//				zipFile = _helper.convertZip(file, fileName);
//
//				if (zipFile == null) {
//					_helper.deleteFile(file);
//					response.put("Status", 0);
//					response.put("Msg", "Failed");
//					return response;
//				}

				String[] toadd = { mail };
				String mailbody = "Dear "+Uname+",\n"
						+ "Your export of "+sensorname+" from "+fromdate+" to "+todate+" is ready to be downloaded.\n"
					
						+ "We're here if you need help - +1(855)983-5566 | <EMAIL>\n"
						+ "Regards,\n"
						+ "Waggle Team\n"
						+ "www.mywaggle.com";

				boolean is_mail_sent = email.SendMail_SEV3("Waggle - "+sensorname+" export", toadd,null, mailbody,
						reportemail, file.getName(), true);

				if (!is_mail_sent) {
					log.info("mail not sent :: email : " + mail);
					response.put("Status", 0);
					response.put("Msg", "Email not sent, please contact support.");
					return response;
				}

				_helper.deleteFile(file);
//				_helper.deleteFile(zipFile);

				response.put("Status", 1);
				response.put("Msg", "Email sent successfully");
			} else {
				response.put("Status", 1);
				response.put("Msg", "No records found");
			}

		} catch (Exception e) {
			log.error("exportsensorreports",e.getMessage());
			response.put("Status", 0);
			_helper.deleteFile(file);
//			_helper.deleteFile(zipFile);
			response.put("Msg", "invalid data");
			return response;
		}

		return response;
	}

	// V4 - External Consultant
	@RequestMapping(value = "v4.0/gatewaysummaryTest/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGatewaySummaryTestV4(@RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("offset") String offset,
			@RequestParam("limit") String limit,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "backing", defaultValue = "MT", required = false) String backing,
			Authentication authentication, @RequestHeader HttpHeaders header) {

		log.info("called gateway summary services" + "groupid: " + groupid + ", subgroupid: " + subgroupid
				+ ", assetgroupid: " + assetgroupid + " , gatewayid: " + gatewayid);
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering getAssetSummaryTestV4 : " + autho);
		try {
			Map<String, String> map = new HashMap<String, String>();

			try {

//				Helper _helper = new Helper();
				log.info("backing key : " + backing);

//				AES aes = new AES();
//				String auth = null;
//				if (backing != null) {
//					if (!backing.equals("MT")) {
//						String[] credential = _helper.decodeInternalKey(backing);
//						String finalOut = aes.decode(credential[0], credential[1]);
//						
//						if (finalOut == null) {
//							response.put("Status", 0);
//							response.put("Msg", "Authentication Error");
//							return response;
//						}
//						log.info("AES decryption success : "+backing+" : "+finalOut);
//					}
//				} else {
//					response.put("Status", 0);
//					response.put("Msg", "Authentication Error");
//					return response;
//				}
				map = userServiceV4.getUserId_cmpIdByAuth(autho);

				JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String country = map.get("country");
			if (country.equalsIgnoreCase("US") || country.equalsIgnoreCase("NA")
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in") || country.isEmpty()
					|| country == null) {
				country = "US";
			}

			long userId = Long.valueOf(map.get("user_id"));
			long cmp_id = Long.valueOf(map.get("cmp_id"));
			String delete_user = map.get("delete_user");
			int remainderOverdueCount = -1;

			if (delete_user.equalsIgnoreCase("1") || delete_user.equalsIgnoreCase("true")) {
				response.put("delete_user", true);
			} else {
				response.put("delete_user", false);
			}

			List<JAssetLastReportV4> reportsummmary = reportServiceV4.getLastgatewayreportV4(groupid, subgroupid,
					assetgroupid, gatewayid, userId, offset, limit, map.get("tempunit"), country);
			log.info("received gateway summary , report length: " + reportsummmary.size());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("lastgatewayreport", reportsummmary);
			response.put("oldDevicePlanPurchase", oldDevicePlanPurchase);
			response.put("oldDevicePlanPurchaseMSG", oldDevicePlanPurchaseMSG);
			response.put("delete_content",
					"Your account deletion is in process. For more details reach our support team");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in gatewaysummaryTest");
			response.put("Error", e.getLocalizedMessage());
			response.put("oldDevicePlanPurchase", oldDevicePlanPurchase);
			response.put("oldDevicePlanPurchaseMSG", oldDevicePlanPurchaseMSG);

			log.error("Exception : getGatewaySummaryTestV4 : " + e.getLocalizedMessage());

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/gatewaysummaryWatch/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGatewaySummaryWatchV5(
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String user_id = header.getFirst("user_id");
		log.info("Entering getGatewaySummaryWatch : " + user_id);
		try {
			UserV4 user = null;

			try {
				user = userServiceV4.verifyAuthV3("id", user_id);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "user id not found");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for user_id : " + user_id);
				return response;
			}

			long userId = Long.valueOf(user_id);

			Map<String, String> map = userServiceV4.getUserId_cmpIdByAuth(user.getAuthKey());
			List<JGatewaySubSetup> gateway_setup = null;
			if (user.getPlan_ver().equalsIgnoreCase("v3")) {
				gateway_setup = reportServiceV4.getProductSubscriptions(user);
			} else {
				gateway_setup = reportServiceV4.getSubscriptionFromDB(user);
			}

			List<JAssetLastReportWatch> reportsummmary = reportServiceV4.getLastgatewayreportWatch(userId,
					map.get("tempunit"), gateway_setup);
			log.info("received gateway summary , report length: " + reportsummmary.size());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("gateway_setup", gateway_setup);
			response.put("lastgatewayreport", reportsummmary);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in gatewaysummaryTest");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception : getGatewaySummaryTestV4 : " + e.getLocalizedMessage());

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// V4 - kalai
	@RequestMapping(value = "v4.0/gatewaysummaryWatch/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGatewaySummaryWatch(@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String user_id = header.getFirst("user_id");
		log.info("Entering getGatewaySummaryWatch : " + user_id);
		try {
			UserV4 user = null;

			try {
				user = userServiceV4.verifyAuthV3("id", user_id);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "user id not found");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for user_id : " + user_id);
				return response;
			}

			long userId = Long.valueOf(user_id);

			Map<String, String> map = userServiceV4.getUserId_cmpIdByAuth(user.getAuthKey());
			List<JGatewaySubSetup> gateway_setup = reportServiceV4.getSubscriptionFromDB(user);
			List<JAssetLastReportWatch> reportsummmary = reportServiceV4.getLastgatewayreportWatch(userId,
					map.get("tempunit"), gateway_setup);
			log.info("received gateway summary , report length: " + reportsummmary.size());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("gateway_setup", gateway_setup);
			response.put("lastgatewayreport", reportsummmary);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in gatewaysummaryTest");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception : getGatewaySummaryTestV4 : " + e.getLocalizedMessage());

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/bowlsummaryReport", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGatewaySummaryReport(@RequestParam long gateway_id,
			@RequestParam(value = "userid", defaultValue = "NA") String userid,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestParam(value = "date", defaultValue = "", required = false) String date,
			@RequestParam(value = "time_zone", defaultValue = "00:00", required = false) String timeZone,
			@RequestParam(value = "is_freePlan", defaultValue = "true", required = false) boolean is_freePlan,
			@RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering getBowlSummaryWatch : " + auth);
		try {

			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuthV2(auth);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			long userId = Long.valueOf(map.get("user_id"));

			timeZone = timeZone.replaceAll("\\s+", "");
			if (timeZone.charAt(0) != '-' && timeZone.charAt(0) != '+')
				timeZone = "+" + timeZone;

			JGatewayGeneralInfo gatewayGen = reportServiceV4.getGatewayGeneralInfo(gateway_id, date, timeZone);

			List<JGatewayWeekList> gatewayGenlist = new ArrayList<JGatewayWeekList>();
			List<JGatewayTimeList> gatewaytime = new ArrayList<JGatewayTimeList>();

			gatewayGenlist = reportServiceV4.getGatewayGenerallist(gateway_id, date, timeZone, is_freePlan);

			if (gatewayGen != null && gatewayGen.getGatewayId() > 0) {
				gatewaytime = reportServiceV4.getGatewayGeneralTimelist(gateway_id, date, timeZone);
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("bowlInfo", gatewayGen);
			response.put("week_report", gatewayGenlist);
			response.put("timebyreport", gatewaytime);
			response.put("Return Time", System.currentTimeMillis());
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in bowlsummaryReport");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			log.error("Exception : getbowlSummaryReportV4 : " + e.getLocalizedMessage());

		}

		return response;
	}

	@RequestMapping(value = "v5.0/getMonitorList", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getMonitorType(@RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestHeader HttpHeaders header) {

		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering getmonitortype : " + auth);
		try {

			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuthV2(auth);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			List<MonitorType> monitorTypelist = reportServiceV4.getAllmonitorType();

			monitorTypelist = monitorTypelist.stream().map(monitorType -> {
				if (monitorType.getId() == 4)
					monitorType.setDisplay_name("Waggle Cam");
				return monitorType;
			}).collect(Collectors.toList());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("monitor_type", monitorTypelist);
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in Monitor Type List");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			log.error("Exception : getmonitortype : " + e.getLocalizedMessage());

		}

		return response;
	}

	@RequestMapping(value = "v5.0/getproductsummary", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getProductSummary(@RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestHeader HttpHeaders header) {

		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering getProductSummary : " + autho);
		try {
			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuth(autho);

				JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String country = map.get("country");
			if (country.equalsIgnoreCase("US") || country.equalsIgnoreCase("NA")
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in") || country.isEmpty()
					|| country == null) {
				country = "US";
			}

			long userId = Long.valueOf(map.get("user_id"));
			// long cmp_id = Long.valueOf(map.get("cmp_id"));
			String delete_user = map.get("delete_user");
			// int remainderOverdueCount = -1;

			if (delete_user.equalsIgnoreCase("1") || delete_user.equalsIgnoreCase("true")) {
				response.put("delete_user", true);
			} else {
				response.put("delete_user", false);
			}

			List<String> productList = gatewayService.getDistinctProductCategory();

			for (String category : productList) {

				Map<String, List<Object>> reportsummmary = reportServiceV4.getLastgatewayreportV5(userId,
						map.get("tempunit"), country, category, os);
				log.info("received gateway summary , report length: " + reportsummmary.size());
				response.put(category, reportsummmary);

			}

			List<MonitorType> monitorTypelist = reportServiceV4.getAllmonitorType();
			for (MonitorType motype : monitorTypelist) {
				response.put(motype.getName() + "_imgurl", motype.getImageurl());
				response.put(motype.getName() + "_name", motype.getDisplay_name());
			}

			if (response.get("SmartBowl_imgurl") == null)
				response.put("SmartBowl_imgurl", "");

			if (response.get("WaggleCam_imgurl") == null)
				response.put("WaggleCam_imgurl", "");

			if (response.get("MiniCam_imgurl") == null)
				response.put("MiniCam_imgurl", "");

			if (response.get("PetMonitor_imgurl") == null)
				response.put("PetMonitor_imgurl", "");

			if (response.get("WaggleCamPro_imgurl") == null)
				response.put("WaggleCamPro_imgurl", "");

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("oldDevicePlanPurchase", oldDevicePlanPurchase);
			response.put("oldDevicePlanPurchaseMSG", oldDevicePlanPurchaseMSG);
			response.put("delete_content",
					"Your account deletion is in process. For more details reach our support team");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in getProductSummary");
			response.put("Error", e.getLocalizedMessage());
			response.put("oldDevicePlanPurchase", oldDevicePlanPurchase);
			response.put("oldDevicePlanPurchaseMSG", oldDevicePlanPurchaseMSG);

			log.error("Exception : getProductSummary : " + e.getLocalizedMessage());

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/bowlsummaryReport", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGatewaySummaryReportV5(@RequestParam long gateway_id,
			@RequestParam(value = "userid", defaultValue = "NA") String userid,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestParam(value = "date", defaultValue = "", required = false) String date,
			@RequestParam(value = "time_zone", defaultValue = "00:00", required = false) String timeZone,
			@RequestParam(value = "is_freePlan", defaultValue = "true", required = false) boolean is_freePlan,
			@RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering getBowlSummaryWatch : " + auth);
		try {

			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuthV2(auth);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			long userId = Long.valueOf(map.get("user_id"));

			timeZone = timeZone.replaceAll("\\s+", "");
			if (timeZone.charAt(0) != '-' && timeZone.charAt(0) != '+')
				timeZone = "+" + timeZone;

			JGatewayGeneralInfo gatewayGen = reportServiceV4.getGatewayGeneralInfo(gateway_id, date, timeZone);

			List<JGatewayWeekList> gatewayGenlist = new ArrayList<JGatewayWeekList>();
			List<JGatewayTimeList> gatewaytime = new ArrayList<JGatewayTimeList>();

			gatewayGenlist = reportServiceV4.getGatewayGenerallist(gateway_id, date, timeZone, is_freePlan);

			if (gatewayGen != null && gatewayGen.getGatewayId() > 0) {
				gatewaytime = reportServiceV4.getGatewayGeneralTimelist(gateway_id, date, timeZone);
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("bowlInfo", gatewayGen);
			response.put("week_report", gatewayGenlist);
			response.put("timebyreport", gatewaytime);
			response.put("Return Time", System.currentTimeMillis());
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in bowlsummaryReport");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			log.error("Exception : getbowlSummaryReportV4 : " + e.getLocalizedMessage());

		}

		return response;
	}

	@RequestMapping(value = "v5.0/bowlsummaryMonthReport", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getbowlsummaryMonthReport(@RequestParam long gateway_id,
			@RequestParam(value = "userid", defaultValue = "NA") String userid,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestParam(value = "date", defaultValue = "", required = false) String date,
			@RequestParam(value = "time_zone", defaultValue = "00:00", required = false) String timeZone,
			@RequestParam(value = "is_freePlan", defaultValue = "true", required = false) boolean is_freePlan,
			@RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering getbowlsummaryMonthReport : " + auth);
		try {

			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuthV2(auth);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			timeZone = timeZone.replaceAll("\\s+", "");
			if (timeZone.charAt(0) != '-' && timeZone.charAt(0) != '+')
				timeZone = "+" + timeZone;

			List<JGatewayMonthList> gatewayGen = new ArrayList<JGatewayMonthList>();

			boolean isFreePlan = ipetSpeciesServicesv4.findDeviceFreeplanornot(Long.valueOf(gateway_id));

			gatewayGen = reportServiceV4.getGatewayMonthlist(gateway_id, date, timeZone, isFreePlan);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("month_report", gatewayGen);
			response.put("Return Time", System.currentTimeMillis());
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in getbowlsummaryMonthReport");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			log.error("Exception : getbowlsummaryMonthReport : " + e.getLocalizedMessage());

		}

		return response;
	}

	@RequestMapping(value = "v5.0/bowlsummaryReportV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse bowlsummaryReportV2(@RequestParam long gateway_id,
			@RequestParam(value = "userid", defaultValue = "NA") String userid,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestParam(value = "date", defaultValue = "", required = false) String date,
			@RequestParam(value = "time_zone", defaultValue = "00:00", required = false) String timeZone,
			@RequestParam(value = "is_freePlan", defaultValue = "true", required = false) boolean is_freePlan,
			@RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering bowlsummaryReportV2 : " + auth);
		try {

			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuthV2(auth);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			long userId = Long.valueOf(map.get("user_id"));

			timeZone = timeZone.replaceAll("\\s+", "");
			if (timeZone.charAt(0) != '-' && timeZone.charAt(0) != '+')
				timeZone = "+" + timeZone;

			JGatewayGeneralInfoV5 gatewayGen = reportServiceV4.getGatewayGeneralInfoV5(gateway_id, date, timeZone);

			List<JGatewayTimeList> gatewaytime = new ArrayList<JGatewayTimeList>();
			List<JGatewayWeekList> gatewayGenlist = new ArrayList<JGatewayWeekList>();

			boolean isFreePlan = ipetSpeciesServicesv4.findDeviceFreeplanornot(Long.valueOf(gateway_id));

			gatewayGenlist = reportServiceV4.getGatewayGenerallist(gateway_id, date, timeZone, isFreePlan);

			if (gatewayGen != null && gatewayGen.getGatewayId() > 0) {
				gatewaytime = reportServiceV4.getGatewayGeneralTimelist(gateway_id, date, timeZone);
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("bowlInfo", gatewayGen);
			response.put("week_report", gatewayGenlist);
			response.put("timebyreport", gatewaytime);
			response.put("Return Time", System.currentTimeMillis());
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in bowlsummaryReportV2");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			log.error("Exception : bowlsummaryReportV2 : " + e.getLocalizedMessage());

		}

		return response;
	}

	@RequestMapping(value = "v5.0/getproductsummaryV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getProductSummaryV2(@RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestHeader HttpHeaders header,
			@RequestParam(value = "req_ver", required = false, defaultValue = "v1") String req_ver) {

		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering getProductSummaryV2 : " + autho);
		try {
			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuth(autho);

				JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String country = map.get("country");
			if (country.equalsIgnoreCase("US") || country.equalsIgnoreCase("NA")
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in") || country.isEmpty()
					|| country == null) {
				country = "US";
			}

			long userId = Long.valueOf(map.get("user_id"));
			// long cmp_id = Long.valueOf(map.get("cmp_id"));
			String delete_user = map.get("delete_user");
			// int remainderOverdueCount = -1;

			if (delete_user.equalsIgnoreCase("1") || delete_user.equalsIgnoreCase("true")) {
				response.put("delete_user", true);
			} else {
				response.put("delete_user", false);
			}

			List<String> productList = gatewayService.getDistinctProductCategory();

			for (String category : productList) {

				Map<String, List<Object>> reportsummmary = reportServiceV4.getLastgatewayreportV6(userId,
						map.get("tempunit"), country, category, os, req_ver);
				log.info("received gateway summary , report length: " + reportsummmary.size());
				response.put(category, reportsummmary);

			}

			List<MonitorType> monitorTypelist = reportServiceV4.getAllmonitorType();
			for (MonitorType motype : monitorTypelist) {
				response.put(motype.getName() + "_imgurl", motype.getImageurl());
				response.put(motype.getName() + "_name", motype.getDisplay_name());
			}

			if (response.get("SmartBowl_imgurl") == null)
				response.put("SmartBowl_imgurl", "");

			if (response.get("WaggleCam_imgurl") == null)
				response.put("WaggleCam_imgurl", "");

			if (response.get("MiniCam_imgurl") == null)
				response.put("MiniCam_imgurl", "");

			if (response.get("PetMonitor_imgurl") == null)
				response.put("PetMonitor_imgurl", "");

			if (response.get("WaggleCamPro_imgurl") == null)
				response.put("WaggleCamPro_imgurl", "");

			boolean checkPaidPlan = chargebeeService.checkCurrentPaidPlan(map.get("chargebee_id"), 1);

			try {
				if (response.get("RvHub") != null
						&& ((Map<String, List<Object>>) response.get("RvHub")).containsKey("PetMonitor")) {
					List<Object> petMonitor = ((Map<String, List<Object>>) response.get("RvHub")).get("PetMonitor");
					if (petMonitor != null && petMonitor.size() == 0) {
						checkPaidPlan = true;
					}
				}
			} catch (Exception e) {
				log.error("Error while filter markerting banner list");
			}

			List<DashboardBanner> bannerList = reportServiceV4.getMarkettingBannerreport(os, app_ver, checkPaidPlan);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("oldDevicePlanPurchase", oldDevicePlanPurchase);
			response.put("banner_details", bannerList);
			response.put("show_banner", show_markettingbanner);
			response.put("oldDevicePlanPurchaseMSG", oldDevicePlanPurchaseMSG);
			response.put("delete_content",
					"Your account deletion is in process. For more details reach our support team");
			response.put("bcs_url",
					"https://www.wagglemerch.com/blogs/news/guide-to-assessing-and-maintaining-your-dog-s-body-condition-with-the-waggle-body-condition-system");
			String content_1 = "User Guideline";
			String content_2 = "Please press the touch sensor once before and after placing food.";
			String content_3 = "Please set the correct feeding time to receive prompt feeder notifications and to establish the feed reporting cycle from the bowl.";
			String content_4 = "After the food is placed in the bowl, the app will display the grams and calories within 5 minutes and will continue updating the values as your pet feeds for the next hour.";
			String content_5 = "During non-feeding times, the values will be updated once every hour.";

			List<JMeariNotification> meariNotify = gatewayService.getMeariNotification();
			response.put("meariNotification", meariNotify);
			response.put("web_rtc_url", web_rtc_url);
			List<String> content = new ArrayList<String>();
			content.add(content_2);
			content.add(content_3);
			content.add(content_4);
			content.add(content_5);

			response.put("content_1", content_1);
			response.put("guidelineContent", content);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in getProductSummaryV2");
			response.put("Error", e.getLocalizedMessage());
			response.put("oldDevicePlanPurchase", oldDevicePlanPurchase);
			response.put("oldDevicePlanPurchaseMSG", oldDevicePlanPurchaseMSG);

			log.error("Exception : getProductSummaryV2 : " + e.getLocalizedMessage());

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v5.0/bowlsummaryWeekReport", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getbowlsummaryWeekReport(@RequestParam long gateway_id,
			@RequestParam(value = "userid", defaultValue = "NA") String userid,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestParam(value = "date", defaultValue = "", required = false) String date,
			@RequestParam(value = "time_zone", defaultValue = "00:00", required = false) String timeZone,
			@RequestParam(value = "is_freePlan", defaultValue = "true", required = false) boolean is_freePlan,
			@RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering bowlsummaryWeekReport : " + auth);
		try {

			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuthV2(auth);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			timeZone = timeZone.replaceAll("\\s+", "");
			if (timeZone.charAt(0) != '-' && timeZone.charAt(0) != '+')
				timeZone = "+" + timeZone;

			List<JGatewayWeekList> gatewayGenlist = new ArrayList<JGatewayWeekList>();

			gatewayGenlist = reportServiceV4.getGatewayGenerallist(gateway_id, date, timeZone, is_freePlan);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("week_report", gatewayGenlist);
			response.put("Return Time", System.currentTimeMillis());
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in bowlsummaryWeekReport");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			log.error("Exception : bowlsummaryWeekReport : " + e.getLocalizedMessage());

		}

		return response;
	}
	

	@RequestMapping(value = "v5.0/getproductsummaryV3", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getProductSummaryV3(@RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestHeader HttpHeaders header,
			@RequestParam(value = "req_ver", required = false, defaultValue = "v1") String req_ver) {

		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering getProductSummaryV3 : " + autho);
		try {
			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuth(autho);

				JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String country = map.get("country");
			if (country.equalsIgnoreCase("US") || country.equalsIgnoreCase("NA")
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in") || country.isEmpty()
					|| country == null) {
				country = "US";
			}

			long userId = Long.valueOf(map.get("user_id"));
			//long cmp_id = Long.valueOf(map.get("cmp_id"));
			String delete_user = map.get("delete_user");
			// int remainderOverdueCount = -1;

			if (delete_user.equalsIgnoreCase("1") || delete_user.equalsIgnoreCase("true")) {
				response.put("delete_user", true);
			} else {
				response.put("delete_user", false);
			}

			Map<String, List<Object>> reportsummmary = reportServiceV4.getLastgatewayreportV7(userId,
						map.get("tempunit"), country, "RvHub", os, req_ver);
			log.info("received gateway summary , report length: " + reportsummmary.size());
			response.put("RvHub", reportsummmary);

			response.put("PetHub", new HashMap<String, List<Object>>());

			List<Object> tileList = new ArrayList<>();
			
			List<MonitorType> monitorTypelist = reportServiceV4.getAllmonitorType();
			for (MonitorType motype : monitorTypelist) {
				response.put(motype.getName() + "_imgurl", motype.getImageurl());	
				response.put(motype.getName() + "_name", motype.getDisplay_name());
				
				Map<String, Object> petHub = (Map<String, Object>) response.get("PetHub");
				Map<String, Object> rvHub = (Map<String, Object>) response.get("RvHub");
				
			    boolean hasListInRvHub = false;
			    boolean hasListInPetHub = false;

			    if (rvHub.get(motype.getName()) instanceof List) {
			        List<Object> rvHubList = (List<Object>) rvHub.get(motype.getName());
			        hasListInRvHub = !rvHubList.isEmpty();
			    }

			    if (petHub.get(motype.getName()) instanceof List) {
			        List<Object> petHubList = (List<Object>) petHub.get(motype.getName());
			        hasListInPetHub = !petHubList.isEmpty();
			    }
			    
			    if (rvHub.get("PetCalories") instanceof List) {
			        List<Object> petHubList = (List<Object>) rvHub.get("PetCalories");
			        if(!petHubList.isEmpty() &&  motype.getId() == 3) {
			        	 Map<String, Long> tileData = new HashMap<>();
					        tileData.put("monitor_type", motype.getId());
					        tileData.put("order", motype.getOrder());
					        tileList.add(tileData);
			        }
			    }


			    if (hasListInRvHub || hasListInPetHub) {
			        Map<String, Long> tileData = new HashMap<>();
			        tileData.put("monitor_type", motype.getId());
			        tileData.put("order", motype.getOrder());
			        tileList.add(tileData);
			    }
			    
			    
				
			}
			Map<String, Long> tileStaticData1 = new HashMap<>();
		    tileStaticData1.put("monitor_type", (long) 100);
		    tileStaticData1.put("order", (long) 100);
	        tileList.add(tileStaticData1);
	        Map<String, Long> tileStaticData2 = new HashMap<>();
	        tileStaticData2.put("monitor_type", (long) 101);
		    tileStaticData2.put("order", (long) 101);
	        tileList.add(tileStaticData2);
			response.put("tile_List", tileList);
			
			if(response.get("SmartBowl_imgurl")==null) 
				response.put("SmartBowl_imgurl", "");
			
			if(response.get("WaggleCam_imgurl")==null) 
				response.put("WaggleCam_imgurl", "");
			
			if(response.get("MiniCam_imgurl")==null) 
				response.put("MiniCam_imgurl", "");
			
			if(response.get("PetMonitor_imgurl")==null) 
				response.put("PetMonitor_imgurl", "");
			
			if(response.get("WaggleCamPro_imgurl")==null) 
				response.put("WaggleCamPro_imgurl", "");
			
			if(response.get("NodeSensor_imgurl")==null) 
				response.put("NodeSensor_imgurl", "");
			
			boolean checkPaidPlan = chargebeeService.checkCurrentPaidPlan( map.get("chargebee_id"), 1 );
			
			try {
				if( response.get("RvHub") != null && ((Map<String, List<Object>>)response.get("RvHub")).containsKey("PetMonitor") ) {
					List<Object> petMonitor = ((Map<String, List<Object>>)response.get("RvHub")).get("PetMonitor");
					if( petMonitor != null && petMonitor.size() == 0 ) {
						checkPaidPlan = true;
					}
				}	
			} catch (Exception e) {
				log.error("Error while filter markerting banner list");
			}

			
			List<DashboardBanner> bannerList = reportServiceV4.getMarkettingBannerreport(os, app_ver, checkPaidPlan);
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("oldDevicePlanPurchase", oldDevicePlanPurchase);
			response.put("banner_details", bannerList);
			response.put("show_banner", show_markettingbanner);
			response.put("oldDevicePlanPurchaseMSG", oldDevicePlanPurchaseMSG);
			response.put("delete_content",
					"Your account deletion is in process. For more details reach our support team");
			response.put("bcs_url","https://www.wagglemerch.com/blogs/news/guide-to-assessing-and-maintaining-your-dog-s-body-condition-with-the-waggle-body-condition-system");
			String content_1 = "User Guideline";
			String content_2 = "Please press the touch sensor once before and after placing food.";
			String content_3 = "Please set the correct feeding time to receive prompt feeder notifications and to establish the feed reporting cycle from the bowl.";
			String content_4 = "After the food is placed in the bowl, the app will display the grams and calories within 5 minutes and will continue updating the values as your pet feeds for the next hour.";
			String content_5 = "During non-feeding times, the values will be updated once every hour.";
			
			List<JMeariNotification>  meariNotify = gatewayService.getMeariNotification();
			response.put("meariNotification", meariNotify);
			response.put("web_rtc_url", web_rtc_url);
			List<String> content = new ArrayList<String>();
			content.add(content_2);
			content.add(content_3);
			content.add(content_4);
			content.add(content_5);
			
			response.put("content_1", content_1);
			response.put("guidelineContent", content);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in getProductSummaryV2");
			response.put("Error", e.getLocalizedMessage());
			response.put("oldDevicePlanPurchase", oldDevicePlanPurchase);
			response.put("oldDevicePlanPurchaseMSG", oldDevicePlanPurchaseMSG);

			log.error("Exception : getProductSummaryV2 : " + e.getLocalizedMessage());

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "v5.0/getproductsummaryV4", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getProductSummaryV4(@RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestHeader HttpHeaders header,
			@RequestParam(value = "req_ver", required = false, defaultValue = "v1") String req_ver) {

		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering getProductSummaryV4 : " + autho);
		try {
			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuth(autho);

				JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String country = map.get("country");
			if (country.equalsIgnoreCase("US") || country.equalsIgnoreCase("NA")
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in") || country.isEmpty()
					|| country == null) {
				country = "US";
			}

			long userId = Long.valueOf(map.get("user_id"));
			//long cmp_id = Long.valueOf(map.get("cmp_id"));
			String delete_user = map.get("delete_user");
			// int remainderOverdueCount = -1;

			if (delete_user.equalsIgnoreCase("1") || delete_user.equalsIgnoreCase("true")) {
				response.put("delete_user", true);
			} else {
				response.put("delete_user", false);
			}

			String tempunit = map.get("tempunit").toUpperCase();
			
			Map<String, Object> reportsummmary = reportServiceV4.getLastgatewayreportV8(userId,
					tempunit, country, "RvHub", os, req_ver);
			log.info("received gateway summary , report length: " + reportsummmary.size());
			boolean hasWcUltra = reportsummmary.containsKey("wcultra");
			if (hasWcUltra) {
				reportsummmary.remove("wcultra");
			}
			
			response.put("RvHub", reportsummmary);
			response.put("tempunit", tempunit);
			response.put("PetHub", new HashMap<String, List<Object>>());
			boolean isvetplanAvail = false;
			
			long vet_planid = 0;
			
			List<Object> tileList = new ArrayList<>();
			List<MonitorType> monitorTypelist = reportServiceV4.getAllmonitorType();
			for (MonitorType motype : monitorTypelist) {
				response.put(motype.getName() + "_imgurl", motype.getImageurl());
				response.put(motype.getName() + "_name", motype.getDisplay_name());

				Map<String, Object> petHub = (Map<String, Object>) response.get("PetHub");
				Map<String, Object> rvHub = (Map<String, Object>) response.get("RvHub");

				boolean hasListInRvHub = false;
				boolean hasListInPetHub = false;

				if (rvHub.get(motype.getName()) instanceof List) {
					List<Object> rvHubList = (List<Object>) rvHub.get(motype.getName());
					hasListInRvHub = !rvHubList.isEmpty();
				}

				if (petHub.get(motype.getName()) instanceof List) {
					List<Object> petHubList = (List<Object>) petHub.get(motype.getName());
					hasListInPetHub = !petHubList.isEmpty();
				}
				
				if (rvHub.get("PetCalories") instanceof Object) {
					JAssetPetKcalReport petCaloriesMap = (JAssetPetKcalReport) rvHub.get("PetCalories");
				    if(petCaloriesMap != null && petCaloriesMap.getContent_1() != null && motype.getId() == 3) {
			        	 Map<String, Object> tileData = new HashMap<>();
			        	 tileData.put("monitor_type", motype.getId());
//							tileData.put("order", motype.getOrder());
							tileData.put("name", motype.getName());
							tileData.put("imageurl", motype.getImageurl());
							tileData.put("category", motype.getCategory());
							tileData.put("deviceMaxCount", motype.getDeviceMaxCount());
							tileData.put("enable", motype.isEnable());
							tileData.put("infourl", motype.getInfourl());
							tileData.put("display_name", motype.getDisplay_name());
							tileData.put("product_link", motype.getProduct_link());
					        tileList.add(tileData);
			        }
				}

				 if (hasListInRvHub || hasListInPetHub) {
					if (motype.getId() == 4 && hasWcUltra) {
						motype.setDisplay_name("WaggleCam Ultra");
						motype.setProduct_link("NA");
					}					
					
					Map<String, Object> tileData = new HashMap<>();
					tileData.put("monitor_type", motype.getId());
//					tileData.put("order", motype.getOrder());
					tileData.put("name", motype.getName());
					tileData.put("imageurl", motype.getImageurl());
					tileData.put("category", motype.getCategory());
					tileData.put("deviceMaxCount", motype.getDeviceMaxCount());
					tileData.put("enable", motype.isEnable());
					tileData.put("infourl", motype.getInfourl());
					tileData.put("display_name", motype.getDisplay_name());
					tileData.put("product_link", motype.getProduct_link());
					tileList.add(tileData);
				}

				
				if (motype.getId() == 11 ) {
					isvetplanAvail = chargebeeService.isVetPlanAvailable((String) map.get("chargebee_id"),
							motype.getId());
					if (isvetplanAvail &&  reportServiceV4.compareVersions(appVerFromConfig,app_ver) >=0 ) {
						Map<String, Object> tileData = new HashMap<>();
						tileData.put("monitor_type", motype.getId());
						tileData.put("name", motype.getName());
						tileData.put("imageurl", motype.getImageurl());
						tileData.put("category", motype.getCategory());
						tileData.put("deviceMaxCount", motype.getDeviceMaxCount());
						tileData.put("enable", motype.isEnable());
						tileData.put("infourl", motype.getInfourl());
						tileData.put("display_name", motype.getDisplay_name());
						tileData.put("product_link", motype.getProduct_link());
						tileList.add(tileData);
					}
				}

			}
			boolean isProductAdded = false;
			if(tileList.size()==1) {
				isProductAdded = !((Map<String, Object>)tileList.get(0)).get("monitor_type").toString().equals("11");
				
			}else if(tileList.size()>1) {
				isProductAdded = true;
			}
			// This is for new user when none of the  device registered 
			boolean nregistered_order = false;
			 if (tileList == null || tileList.isEmpty() || !isProductAdded) {

				// check not delivered orders available
				ShipmentDetailV2 detail = niomService.getShipmentDetailsV2("OS.order_email", map.get("username"));
				Map<String, Object> tileData = new HashMap<>();
		        tileData.put("monitor_type", -1);
		        tileData.put("name", "");
		        tileData.put("imageurl","");
		        tileData.put("category", "");
		        tileData.put("deviceMaxCount", 0);
		        tileData.put("enable", 0);
		        tileData.put("infourl","");
		        tileData.put("display_name", "Home");
		        tileData.put("product_link", "https://mywaggle.com/pages/store");
				tileList.add(tileData);

				if(detail != null ) {
					nregistered_order = true;
					response.put("order_detail", detail);
				}
		/*	if (tileList == null || tileList.size() <= 0 || !isProductAdded) {
				monitorTypelist.stream()
			    .filter(motype -> motype.getId() != 11)
			    .forEach(motype -> {
			        response.put(motype.getName() + "_imgurl", motype.getImageurl());
			        response.put(motype.getName() + "_name", motype.getDisplay_name());

			        if (motype.getId() == 4 && hasWcUltra) {
			            motype.setDisplay_name("WaggleCam Ultra");
			            motype.setProduct_link("NA");
			        }

			        Map<String, Object> tileData = new HashMap<>();
			        tileData.put("monitor_type", motype.getId());
			        tileData.put("name", motype.getName());
			        tileData.put("imageurl", motype.getImageurl());
			        tileData.put("category", motype.getCategory());
			        tileData.put("deviceMaxCount", motype.getDeviceMaxCount());
			        tileData.put("enable", motype.isEnable());
			        tileData.put("infourl", motype.getInfourl());
			        tileData.put("display_name", motype.getDisplay_name());
			        tileData.put("product_link", motype.getProduct_link());
			        
			        tileList.add(tileData);
			    });*/
//				for (MonitorType motype : monitorTypelist) {
//					if(motype.getId() != 11) {
//					response.put(motype.getName() + "_imgurl", motype.getImageurl());
//					response.put(motype.getName() + "_name", motype.getDisplay_name());
//
//					if (motype.getId() == 4 && hasWcUltra) {
//						motype.setDisplay_name("WaggleCam Ultra");
//						motype.setProduct_link("NA");
//					}
//					Map<String, Object> tileData = new HashMap<>();
//					tileData.put("monitor_type", motype.getId());
////						tileData.put("order", motype.getOrder());
//					tileData.put("name", motype.getName());
//					tileData.put("imageurl", motype.getImageurl());
//					tileData.put("category", motype.getCategory());
//					tileData.put("deviceMaxCount", motype.getDeviceMaxCount());
//					tileData.put("enable", motype.isEnable());
//					tileData.put("infourl", motype.getInfourl());
//					tileData.put("display_name", motype.getDisplay_name());
//					tileData.put("product_link", motype.getProduct_link());
//					tileList.add(tileData);
//					}
//				}
			}

			int orderCount = 2;
			for (Object rvObj : tileList) {
				Map<String, Object> rvMapObj = (Map<String, Object>) rvObj;
				if (Long.parseLong(rvMapObj.get("monitor_type").toString()) == 1) {
					rvMapObj.put("order", 1);
				} else {
					rvMapObj.put("order", orderCount++);
				}
			}

			response.put("tile_List", tileList);
			response.put("nregistered_order", nregistered_order);
			
			if (response.get("SmartBowl_imgurl") == null)
				response.put("SmartBowl_imgurl", "");

			if (response.get("WaggleCam_imgurl") == null)
				response.put("WaggleCam_imgurl", "");

			if (response.get("MiniCam_imgurl") == null)
				response.put("MiniCam_imgurl", "");

			if (response.get("PetMonitor_imgurl") == null)
				response.put("PetMonitor_imgurl", "");

			if (response.get("WaggleCamPro_imgurl") == null)
				response.put("WaggleCamPro_imgurl", "");

			if (response.get("NodeSensor_imgurl") == null)
				response.put("NodeSensor_imgurl", "");

			boolean checkPaidPlan = chargebeeService.checkCurrentPaidPlan(map.get("chargebee_id"), 1);
			boolean isSubscribedProduct=reportServiceV4.isSubscribedProduct(map.get("chargebee_id"));
			try {
				if (response.get("RvHub") != null
						&& ((Map<String, List<Object>>) response.get("RvHub")).containsKey("PetMonitor")) {
					List<Object> petMonitor = ((Map<String, List<Object>>) response.get("RvHub")).get("PetMonitor");
					if (petMonitor != null && petMonitor.size() == 0) {
						checkPaidPlan = true;
					}
				}
			} catch (Exception e) {
				log.error("Error while filter markerting banner list");
			}
			List<DashboardBanner> bannerList = new ArrayList<DashboardBanner>();
			if(nregistered_order && !isSubscribedProduct) {
				DashboardBanner banner = new DashboardBanner();
				banner.setEnable(true);
				banner.setBannerImg_url(coupon_img_order);
				bannerList.add(banner);
			}else
				bannerList = reportServiceV4.getMarkettingBannerreport(os, app_ver, checkPaidPlan);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("oldDevicePlanPurchase", oldDevicePlanPurchase);
			response.put("banner_details", bannerList);
			response.put("show_banner", show_markettingbanner);
			response.put("oldDevicePlanPurchaseMSG", oldDevicePlanPurchaseMSG);
			response.put("delete_content",
					"Your account deletion is in process. For more details reach our support team");
			response.put("bcs_url","https://www.wagglemerch.com/blogs/news/guide-to-assessing-and-maintaining-your-dog-s-body-condition-with-the-waggle-body-condition-system");
			String content_1 = "User Guideline";
			String content_2 = "Tap the touch sensor once before and after placing the food.";
			String content_3 = "Set the feeding time correctly to receive timely notifications.";
			String content_4 = "Once the food is placed, the app will show the grams and calories within 5 minutes and update them as your pet eats over the next hour.";
			String content_5 = "During non-feeding periods, the values will refresh every hour.";

			List<JMeariNotification> meariNotify = gatewayService.getMeariNotification();
			response.put("meariNotification", meariNotify);
			response.put("web_rtc_url", web_rtc_url);
			List<String> content = new ArrayList<String>();
			content.add(content_2);
			content.add(content_3);
			content.add(content_4);
			content.add(content_5);

			response.put("vetchat_mail_send", vetChatServiceImpl.getVetChatUserEmail(userId));
			response.put("vetchat_homepage_title", vetchat_homepage_title);
			response.put("vetchat_homepage_price", vetchat_homepage_price);
			response.put("vetchat_homepage_description",vetchat_homepage_description);
			
			if(isvetplanAvail) {
				long vetPlanId = chargebeeService.getCurrentVetPlanid(map.get("chargebee_id"), 11);
				// For vetchat below content need to be overridden 
				if(vetPlanId == 20) {
					response.put("vetchat_homepage_title", "Talk to a Vet");
					response.put("vetchat_homepage_price", "");
					response.put("vetchat_homepage_description","");
				}
			}

			boolean isVetchatUser = userServiceV4.isVetChatUser(userId);

			response.put("content_1", content_1);
			response.put("guidelineContent", content);
			response.put("show_chat", show_chat);
			response.put("show_vetchat", show_vetchat);
			response.put("isvetplanAvail", isvetplanAvail);
			response.put("is_vetchat_user", false);
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in getProductSummaryV4");
			response.put("Error", e.getLocalizedMessage());
			response.put("oldDevicePlanPurchase", oldDevicePlanPurchase);
			response.put("oldDevicePlanPurchaseMSG", oldDevicePlanPurchaseMSG);

			log.error("Exception : getProductSummaryV4 : " + e.getLocalizedMessage());

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}


	@GetMapping(value = "v5.0/sensorDevicelist", headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSensorDevicelist(
			@RequestParam(value = "gatewayid", defaultValue = "0", required = false) String gatewayId,
			@RequestParam(value = "userid", defaultValue = "NA") String userId, @RequestParam("os") String os,
			@RequestParam(value = "type", defaultValue = "iphone", required = false) String type,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestParam(defaultValue = "0", required = false) long monitor_type_id,
			@RequestHeader HttpHeaders header) {
		
		String auth = header.getFirst("auth");
		log.info("Entered :: getSensorDevicelist : " + auth);
		log.info("user id : " + header.getFirst("userid"));
		JResponse response = new JResponse();
		UserV4 user = null;
		try {
			long userID = Long.valueOf(userId);
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);

				if (user.getId() != userID) {
					response.put("Status", 0);
					response.put("Msg", "User id mismatch");
					response.put("user_deleted", user.isDelete_user());
					log.info("User id for authey and user id parameter mismatch! ");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				log.error("Invalid authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			
			ArrayList<JSensorTypeCode> stype_list = gatewayServiceV4.getSensorTypeCode();
			Map<String,List<Object>> lastSenReport = new HashMap<String,List<Object>>();
			List<Object> sensor_list = new ArrayList<Object>();
			
			for (JSensorTypeCode list : stype_list) {
				sensor_list =  gatewayServiceV4.getSensorListByCode(userID, Long.valueOf(gatewayId), list.getSensorcode(),os);
				if(list.getId() == 1)
					lastSenReport.put("doorsensor",sensor_list);
				if(list.getId() == 2)
					lastSenReport.put("waterleaksensor",sensor_list);
				if(list.getId() == 3)
					lastSenReport.put("waterlevelsensor",sensor_list);
				
			}
			
			List<JTimeInterval> alertInterval = getIntervalTimes(true);
			
			ArrayList<JSensorType> stype_list_detail = gatewayServiceV4.getSensorType();			
			response.put("stype_list", stype_list_detail);
			
			response.put("alert_interval", alertInterval);
			
			if (lastSenReport == null) {
				response.put("Status", 0);
				response.put("Msg", "Session timeout, Please try again");
				response.put("sensorlist", lastSenReport);
				log.info("Error while getting SensorDevicelist for the user_id : " + userId);
			} else if (lastSenReport.isEmpty() ) {
				response.put("Status", 0);
				response.put("Msg", "user don't have device!");
				response.put("sensorlist", lastSenReport);			
				log.info("No device found for user id : " + userId);
			} else {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("sensorlist", lastSenReport);
				log.info("SensorDevicelist found for the user id : " + userId);
			}
			log.info("Exit :: sensorList");
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Unable to get sensor device list ");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception occured at getSensorDevicelist  :", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	private List<JTimeInterval> getIntervalTimes(boolean isAlertInterval) {
		List<JTimeInterval> intervalTimes = new ArrayList<>();
		//intervalTimes.add(new JTimeInterval(0, "30 Seconds", 30));
		intervalTimes.add(new JTimeInterval(60, "1 Minute", 60));
		intervalTimes.add(new JTimeInterval(120, "2 Minutes", 120));
		intervalTimes.add(new JTimeInterval(180, "3 Minutes", 180));
		if (isAlertInterval) {
			intervalTimes.add(new JTimeInterval(300, "5 Minutes", 300));
			intervalTimes.add(new JTimeInterval(600, "10 Minutes", 600));
			intervalTimes.add(new JTimeInterval(1800, "30 Minutes", 1800));
			intervalTimes.add(new JTimeInterval(3600, "1 Hour", 3600));
		}
		return intervalTimes;
	}
	
	
	@GetMapping(value = "v5.0/petmonitorhistory", headers = "Accept=application/json")
	@ResponseBody
	public JResponse getpetMonitorHistory(@RequestParam(value = "gatewayid") String gatewayId,
			@RequestParam(value = "app_ver") String app_ver, Authentication authentication,
			@RequestParam(value = "os") String os, @RequestParam(value = "timezone") String timezone,
			@RequestHeader HttpHeaders header) {

		String auth = header.getFirst("auth");
		log.info("Entered :: getpetMonitorHistory : " + auth);
		JResponse response = new JResponse();
		Map<String, String> map = new HashMap<String, String>();
		try {
			try {
				map = userServiceV4.getUserId_cmpIdByAuth(auth);

				JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				log.error("Invalid authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			timezone = timezone.replaceAll("\\s+", "");
			if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
				timezone = "+" + timezone;
			
			List<JPetmonitorHistory> history_detail = gatewayServiceV4.getPetMonitorHistory(Long.valueOf(gatewayId),
					map.get("tempunit"), timezone);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("history", history_detail);
			response.put("Min", 30);
			response.put("Max", 60);
			response.put("tempunit", map.get("tempunit"));
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in getpetMonitorHistory");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : getpetMonitorHistory : " + e.getLocalizedMessage());

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	// ========get exportpetmonitorreports ================
	@GetMapping(value = "v5.0/exportpetmonitorreports", headers = "Accept=application/json")
	@ResponseBody
	public JResponse exportpetmonitorreports(@RequestParam("fromdate") String fromdate,
			@RequestParam("todate") String todate, Authentication authentication,
			@RequestParam("gatewayid") Long gatewayid, @RequestParam("timezone") String timeZone,
			@RequestParam("gateway_name") String gateway_name,
			@RequestHeader HttpHeaders header) {

		String auth = header.getFirst("auth");
		log.info("Entered into exportpetmonitorreports :: auth : " + auth);
		JResponse response = new JResponse();
		Map<String, String> map = new HashMap<String, String>();
		String mail = "NA";
		String Uname = "NA";
		try {

			map = userServiceV4.getUserId_cmpIdByAuth(auth);

			JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			UserV4 user = userServiceV4.verifyAuthV4("authkey", auth);
			mail = user.getEmail();
			Uname = user.getFirstname();

		} catch (Exception e) {
			log.error(e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid data");
			return response;
		}
		timeZone = timeZone.replaceAll("\\s+", "");
		if (timeZone.charAt(0) != '-' && timeZone.charAt(0) != '+')
			timeZone = "+" + timeZone;
		File file = null;
		try {
			Helper _helper = new Helper();

			List<JPetmonitorHistory> reportsummmary = reportServiceV4.getPetmonitorGrpRep(gatewayid, fromdate, todate,
					timeZone,map.get("tempunit"));
			log.info("received exportpetmonitorreports , report length: " + reportsummmary.size());
			if (!reportsummmary.isEmpty()) {

				file = _helper.writeCSVDataPetMonitorHistory(reportsummmary, fromdate, todate, gateway_name,
						map.get("tempunit"));

				String[] toadd = { mail };
				String mailbody = "Dear " + Uname + ",\n\n" + "Your export of Pet Monitor from " + fromdate + " to "
						+ todate + " is ready to be downloaded.\n\n"

						+ "We're here if you need help - +1(855)983-5566 | <EMAIL>\n\n" + "Regards,\n"
						+ "Waggle Team\n" + "www.mywaggle.com";

				boolean is_mail_sent = email.SendMail_SEV3("Waggle - Pet Monitor Reports History", toadd, null, mailbody,
						reportemail, file.getName(), true);

				if (!is_mail_sent) {
					log.info("mail not sent :: email : " + mail);
					response.put("Status", 0);
					response.put("Msg", "Email not sent, please contact support.");
					return response;
				}

				_helper.deleteFile(file);

				response.put("Status", 1);
				response.put("Msg", "Email sent successfully");
			} else {
				response.put("Status", 1);
				response.put("Msg", "No data found");
			}

		} catch (Exception e) {
			log.error("exportpetmonitorreports", e.getMessage());
			response.put("Status", 0);
			_helper.deleteFile(file);
			response.put("Msg", "invalid data");
			return response;
		}

		return response;
	}

}
