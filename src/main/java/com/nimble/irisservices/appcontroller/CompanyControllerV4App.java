package com.nimble.irisservices.appcontroller;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.CompanyConfigResponse;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICompanyServiceV4;
import com.nimble.irisservices.service.IDynamicCmdService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class CompanyControllerV4App {

	@Value("${verificationtime}")
	private long verifytime;

	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	private static final Logger log = LogManager.getLogger(CompanyControllerV4App.class);

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	ICompanyServiceV4 companyServicev4;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;
	
	@Autowired
	IDynamicCmdService dynamicCmdService;
	
	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	Helper _helper;
	
	// saveCompanyConfigV4 - by anand
		@RequestMapping(value = "v5.0/companyconfig/", method = RequestMethod.POST, headers = "Accept=application/json")
		public @ResponseBody JResponse saveCompanyConfigV5(@RequestParam("temperatureunit") String temperatureunit,
				@RequestParam("cmpcfgid") String cmpcfgid, @RequestParam("cmpid") String cmpid,
				@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
				@RequestParam("os") String os,
				@RequestParam("app_ver") String app_ver,
				Authentication authentication, @RequestHeader HttpHeaders header) {
			JResponse response = new JResponse();
			String autho = header.getFirst("auth");
			log.info("Entering saveCompanyConfigV4 : " + autho);
			try {
				UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);
				if (usr == null) {
					response.put("Status", 0);
					response.put("Msg", "Invalid AuthKey");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				if (cmpid.isEmpty())
					cmpid = usr.getCmpId() + "";

				companyServicev4.updateCompanyCfg(cmpid, cmpcfgid, temperatureunit);
				String message = "";
				
				
				List<Gateway> gateway = gatewayService.getGatewayByUser(null, null, null, null,
						usr.getId(), null);
				
				boolean updateTempUnit = false;
				for (Gateway gw : gateway) {
					if (gw.getModel().getModel().toLowerCase().contains("n13")) {
					if (temperatureunit.equalsIgnoreCase("F"))
						message = "LCDMODE=1";
					else
						message = "LCDMODE=2";
				} else {
					if (temperatureunit.equalsIgnoreCase("F"))
						message = "LCDMODE=2";
					else
						message = "LCDMODE=1";
				
					}
					if(gw.getModel().getModel().toLowerCase().contains("n13") || (gw.getModel().getIsgps().equalsIgnoreCase("true")
							|| gw.getModel().getIsgps().equalsIgnoreCase("1")))
						updateTempUnit = dynamicCmdService.saveDynamicCmdV2(gw, message, 1, "notsent", 0L);
					else
						log.info("No OTA sent for non gps model : gateway_id : " + gw.getId());
				}

				response.put("Status", 1);
				response.put("Msg", "Success");
			} catch (Exception e) {
				log.error("Exception : saveCompanyConfigV4 :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "UnExcepted Error in Companyconfig");
				response.put("Error", e.getLocalizedMessage());
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		// getCompanyConfigV4 - By Anand
		@RequestMapping(value = "v5.0/companyconfig/", method = RequestMethod.GET, headers = "Accept=application/json")
		public @ResponseBody JResponse getCompanyConfigV5(
				@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
				@RequestParam("os") String os,
				@RequestParam("app_ver") String app_ver,
				Authentication authentication, @RequestHeader HttpHeaders header) {
			JResponse response = new JResponse();
			String autho = header.getFirst("auth");
			log.info("Entering getCompanyConfig : " + autho);
			try {
				UserV4 user = null;
				try {
					//while checking user verfication change to verifyAuthV3
					user = userServiceV4.verifyAuthV4("authkey", autho);
				} catch (InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for username : " + autho);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				CompanyConfigResponse cmp_cfg = companyServicev4.getCompanyConfigAndCompany(user.getCmpId());

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("companyconfig", cmp_cfg);
				if (cmp_cfg.getCmptype_id() == 3) {
					boolean registerProduct = true;
					if ((user.getUsername().matches("[0-9]+") && user.getUsername().length() == 6)) {
						List<JGateway> gateways = gatewayServiceV4.getGatewayV4(null, null, null, null, user.getId(), null);

						if (gateways.size() > 1 || gateways.size() < 1) {
							registerProduct = true;
						} else if (gateways.size() == 1) {
							JGateway gat = gateways.get(0);
							registerProduct = niomDbservice.isMeidMappedInOrdermap(gat.getMeid());
						}
					}
					String userStatus = "1";
					//currently not used Previously we have restricted user to verify the email as mandatory . Later we have disabled it .
//					String userStatus = "0";
//					try {
//						Calendar curCal = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
//						DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//						sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//						Date date = sdf.parse(user.getCreatedOn());
//						Calendar createdCal = Calendar.getInstance();
//						createdCal.setTime(date);
//						long timeMills = curCal.getTimeInMillis() - createdCal.getTimeInMillis();
//						long verifyMills = verifytime * 60 * 60 * 1000;
//						// user verification time - config parameter from properties file
//						if (user.isVerified() == true) {
//							userStatus = "1";
//						} else if (timeMills < verifyMills && (user.isVerified() == false)) {
//							userStatus = "1";
//						} else if (timeMills > verifyMills && (user.isVerified() == false)) {
//							userStatus = "1";
//						}
//					} catch (Exception e) {
//						log.error("getCompanyConfig : date calc " + e.getLocalizedMessage());
//					}
					response.put("notificationStatus", user.isNotification());
					response.put("isProductRegistered", registerProduct);
					response.put("userverificationStatus", userStatus);
				}
			} catch (Exception e) {
				log.error("Exception : getCompanyConfigV4 :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Error Occur");
				response.put("Error", e.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
}
