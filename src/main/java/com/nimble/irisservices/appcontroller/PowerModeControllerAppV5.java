package com.nimble.irisservices.appcontroller;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.irisservices.dto.JGatewayModeDetails;
import com.nimble.irisservices.dto.JPowerModeConfig;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IPowerModeService;
import com.nimble.irisservices.service.IUserServiceV4;


@RestController
@RequestMapping("/app")
public class PowerModeControllerAppV5 {

	private static final Logger log = LogManager.getLogger(PowerModeControllerAppV5.class);

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	IPowerModeService powerModeService;

	@Autowired
	Helper _helper;
	
	@Autowired
	@Lazy
	private IAsyncService async;
	
	@Value("${power_mode_popup_title}")
	private String power_mode_popup_title;

	@GetMapping("v5.0/getpowermode")
	public JResponse getPowerModeStatus(@RequestParam long gatewayid,
			@RequestParam String os,
			@RequestParam String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,@RequestParam(value = "userid", defaultValue = "NA", required = false) String userid) {
		String auth = header.getFirst("auth");
		log.info("Entered into getPowerModeStatus :: auth : " + auth);
		log.info("user id : " + userid);
		JResponse response = new JResponse();
		try {

			UserV4 user = userServiceV4.verifyAuthV4("authkey", auth);
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			JGatewayModeDetails gatewayModeDetails = powerModeService.getGatewayModeDetails(gatewayid);

			if (gatewayModeDetails == null) {
				log.info("Gateway's power mode details is not available. (null)");
				response.put("Status", 0);
				response.put("Msg", "No power mode is assigned.");
				response.put("current_mode_id", 0);
				response.put("power_mode_configs", "[]");
			}
			List<JPowerModeConfig> powerModeConfig = powerModeService.getPowerModes(gatewayModeDetails.getPowerModes());

			long currentMode = gatewayModeDetails.getCur_mode();
			boolean updated = true;
			if (!gatewayModeDetails.getMode_status().equalsIgnoreCase("sent")) {
				currentMode = gatewayModeDetails.getPrev_mode();
				updated = false;
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("current_mode_id", currentMode);
			response.put("power_mode_configs", powerModeConfig);
			response.put("update_status", updated);
			response.put("power_mode_popup_title", power_mode_popup_title);

		} catch (InvalidAuthoException e) {
			log.error("Exception occured at while getting the user - Invalid auth key " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid session. Please try again later");
		} catch (Exception e) {
			log.error("Error in getPowerModeStatus :: Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid session. Please try again later");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@PostMapping("v5.0/savepowermode")
	public JResponse savePowerMode(
			/* @RequestBody JPowerModeConfig jpowerModeConfig, */@RequestParam long gatewayid, @RequestParam int powermodeid,
			Authentication authentication, @RequestHeader HttpHeaders header,@RequestParam(value = "userid", defaultValue = "NA", required = false) String userid) {
		String autho = header.getFirst("auth");
		log.info("Entered into savePowerMode :: auth : "+autho);
		JResponse response = new JResponse();
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			JGatewayModeDetails gatewayModeDetails = powerModeService.getGatewayModeDetails(gatewayid);
			JPowerModeConfig powerModeConfig = powerModeService.getPowerModes(powermodeid+"").get(0);

			if(gatewayModeDetails.getMode_status().equalsIgnoreCase("notsent")) {
				JPowerModeConfig powerModeConfigPrev = powerModeService.getPowerModes(gatewayModeDetails.getPrev_mode()+"").get(0);
				response.put("Status", 1);
				response.put("Msg", "Power mode set to " + powerModeConfigPrev.getModeName().replace("Power", ""));
//				response.put("Msg", powerModeConfigPrev.getModeName()+" is in progress and will be activated once your waggle update it's data to your phone next time. To enable it now, please reset the waggle now.");
			}else if(gatewayModeDetails.getMode_status().equalsIgnoreCase("sent")) {

				String reportInterval = "reportinterval="+powerModeConfig.getReportInterval();
				String maxSleeptime = "maxsleeptime="+powerModeConfig.getReportInterval();

				String dynamicCmd = reportInterval+","+maxSleeptime;
				async.sendDynamicCommand(gatewayid+"", dynamicCmd, 0L);

				boolean isPowerModeSaved = powerModeService.updatePowerModeStatus(gatewayid, powermodeid, (int) gatewayModeDetails.getCur_mode(), "notsent");
				log.info("is Power Mode Saved to gateway table : "+isPowerModeSaved);
				response.put("Status", 1);
				response.put("Msg", "Power mode set to " + powerModeConfig.getModeName().replace("Power", ""));
//				response.put("Msg", powerModeConfig.getModeName()+" will be activated once your waggle update it's data to your phone next time. To enable it now, please reset the waggle now.");
			}					
		}catch(InvalidAuthoException e) {
			log.error("Exception occured at save Power Mode - Invalid auth key "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid session. Please try again later");
		}catch (Exception e) {
			log.error("Error in savePowerMode :: Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid session. Please try again later");
		}
		return response;
	}
}
