package com.nimble.irisservices.appcontroller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class MessageControllerApp {

	private static final Logger log = LogManager.getLogger(MessageControllerApp.class);

	@Autowired
	@Lazy
	IMessagingService  messagingService;

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";

	@Autowired
	IUserServiceV4 userServiceV4;
	
	@Value("${alert_email}")
	private String alert_email;

	/*================== send EMAIL================ */
	@RequestMapping(value = "v3.0/nimbleemail", method = RequestMethod.POST, 
			headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendEmail(@RequestParam("emailids") String emailids,@RequestParam("subject") String subject,
			@RequestParam("content") String content,
			@RequestParam(value = "isRvEmailId", defaultValue = "false", required = false) String isRvEmailId,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime){
			//@RequestParam("isRvEmailId") String isRvEmailId) {

		JResponse response = new JResponse();
		try{
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				if (!validation_authkey.equals(auth))
					user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			boolean isRVEmailId = false;
			if(isRvEmailId.equalsIgnoreCase("true"))
				isRVEmailId = true;
			messagingService.sendEmail(alert_email,emailids, subject, content,isRVEmailId);
			response.put("Status", 1);
			response.put("Msg", "success");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		catch(Exception e)
		{
			log.info("sendEmail::" +e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}
	
	/*============== send SMS ================ */
	@RequestMapping(value = "v3.0/nimblesms", method = RequestMethod.POST, 
			headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendsms(@RequestParam("phoneno") String phonenumber,@RequestParam("msg") String message,
			@RequestParam("cmpid") String companyid,@RequestParam("cmpname") String companyname,
			@RequestParam("appname") String appname,@RequestParam("type") String type,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info("sendsms called");

		JResponse response = new JResponse();
		try{
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				if (!validation_authkey.equals(auth))
					user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			boolean status = messagingService.savePlivoData(phonenumber, message, companyid, companyname, appname,type);
			if(status){
				response.put("Status", 1);
				response.put("Msg", "success");
			}else{
				response.put("Status", 0);
				response.put("Msg", "sendsms failed");
			}
			
		}
		catch(Exception e)
		{
			//e.printStackTrace();
			log.info("sendsms::" +e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

}
