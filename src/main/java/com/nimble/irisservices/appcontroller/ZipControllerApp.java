package com.nimble.irisservices.appcontroller;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.model.AddressComponent;
import com.google.maps.model.AddressComponentType;
import com.google.maps.model.ComponentFilter;
import com.google.maps.model.GeocodingResult;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.ZipCodeDetails;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IZipCodeDetailsService;

@Controller
@RequestMapping("/app")
public class ZipControllerApp {

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IZipCodeDetailsService iZipCodeDetailsService;

	@Value("${google.api.key}")
	private String googleAPIkey;

	Helper _helper = new Helper();

	private static final Logger log = LogManager.getLogger(ZipControllerApp.class);

	@RequestMapping(value = "v3.0/getzipcodedetails/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse GetTemperature(@RequestParam("zipcode") String zipCode,
			@RequestParam("countrycode") String countryCode, Authentication authentication,
			@RequestHeader HttpHeaders header,@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<ZipCodeDetails> zipCodeDetails = iZipCodeDetailsService.getZipCodeDetails(zipCode, countryCode);

			if (zipCodeDetails.size() > 0) {
				log.info("Zip Cpode found in database : " + zipCode + countryCode);
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("ZipCodeDetails", zipCodeDetails.get(0));
				return response;
			} else {

				log.info("Search Zip Cpode in Google : " + zipCode + countryCode);

				ZipCodeDetails newZipCodeDetails = new ZipCodeDetails();

				GeoApiContext context = new GeoApiContext.Builder().apiKey(googleAPIkey).build();

				GeocodingResult[] results = GeocodingApi.newRequest(context)
//						.components(new ComponentFilter("country", countryCode))
						.components(new ComponentFilter("postal_code", zipCode),
								new ComponentFilter("country", countryCode))
						.await();

				if (results.length < 1) {
					log.info("Error in Searching Zip Cpode in Google : " + zipCode + countryCode);
					response.put("Status", 0);
					response.put("Msg", "No Details Found");
					return response;
				}

				for (GeocodingResult gr : results) {
					for (AddressComponent ac : gr.addressComponents) {
						for (AddressComponentType acType : ac.types) {

							if (acType == AddressComponentType.ADMINISTRATIVE_AREA_LEVEL_1) {

								newZipCodeDetails.setState(ac.shortName);

							} else if (acType == AddressComponentType.LOCALITY) {

								newZipCodeDetails.setCity(ac.shortName);

							}
						}

					}

					if (newZipCodeDetails.getCity().equalsIgnoreCase("NA")) {
						for (AddressComponent ac : gr.addressComponents) {
							for (AddressComponentType acType : ac.types) {
								if (acType == AddressComponentType.ADMINISTRATIVE_AREA_LEVEL_2) {

									newZipCodeDetails.setCity(ac.shortName);

								}
							}

						}
					}

					break;
				}

				newZipCodeDetails.setZipcode(zipCode.trim());
				newZipCodeDetails.setCountry(countryCode.toString().toUpperCase());
				newZipCodeDetails.setCreatedOn(_helper.getCurrentTimeinUTC());
				boolean saveZipDetails = iZipCodeDetailsService.saveZipCode(newZipCodeDetails);

				log.info("Save Zip Code from Google into databases : " + zipCode + countryCode + "Status : ->"
						+ saveZipDetails);

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("ZipCodeDetails", newZipCodeDetails);
				response.put("Return Time", System.currentTimeMillis());
				return response;

			}
		} catch (InvalidAuthoException e) {
			log.error("Error - . invalid authentication key");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			log.error("Error - . Error in Zip Code Details.");
			response.put("Status", 0);
			response.put("Msg", "Error in getting Zip Code..");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

	}
}
