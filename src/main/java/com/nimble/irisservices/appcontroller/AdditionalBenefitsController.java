package com.nimble.irisservices.appcontroller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.irisservices.dto.JCouponData;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.RewardCredit;
import com.nimble.irisservices.entity.eBook;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAvatarService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@RestController
public class AdditionalBenefitsController {

	@Autowired
	IAvatarService iAvatarService;

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	IUserServiceV4 userServiceV4;

	Helper _helper = new Helper();
	private static final Logger log = LogManager.getLogger(AdditionalBenefitsController.class);

	// ========avatar api's ================
	@RequestMapping(value = "v3.0/checkavatarcredits", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse checkCreditForAvatar(
			@RequestParam(value = "emailId", defaultValue = "0", required = true) String emailId) {
		log.info(" Entered checkavatarcredits: ");

		JResponse response = new JResponse();
		try {
			RewardCredit avatarCredit = iAvatarService.checkCredit(emailId);

			if (avatarCredit != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("credits", avatarCredit);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed");
			}
		} catch (Exception e) {
			log.error("Exception :checkavatarcredits : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}
	
	@RequestMapping(value = "app/v5.0/getEbookList", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getEbookListV5(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam("userId") Long userId,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver) {
		log.info(" Entered getEbookList :: ");
		JResponse response = new JResponse();
		List<eBook> ebooks = new ArrayList<eBook>();
		String autho = header.getFirst("auth");
		try {
			UserV4 usr = null;
			usr = userServiceV4.verifyAuthV3("authkey", autho);
			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (userId != usr.getId()) {
				response.put("Status", 0);
				response.put("Msg", "Error");
				return response;
			}

			RewardCredit avatarCredit = iAvatarService.checkCredit(usr.getEmail());
			int credit = 0;
			if (avatarCredit != null) {
				credit = avatarCredit.getEbookCredit();
			}

			ebooks = iAvatarService.getEbooks(userId);
			if (ebooks != null && !ebooks.isEmpty()) {
				response.put("Status", 1);
				response.put("ebooks", ebooks);
				response.put("Msg", "Success");
				response.put("ebookCredit", credit);
			} else {
				response.put("Status", 0);
				response.put("Msg", "e-Book list is empty");
				response.put("ebookCredit", credit);
			}

		} catch (Exception e) {
			log.error("Exception :getEbookList : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "E-Book Error");
			response.put("ebookCredit", 0);
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v3.0/updateavatarcredit", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateAvatarCredit(
			@RequestParam(value = "emailId", defaultValue = "0", required = true) String emailId) {
		log.info(" Entered updateavatarcredit: ");
		JResponse response = new JResponse();
		try {
			Boolean success = iAvatarService.updateCredit(emailId);

			if (success) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed");
			}
		} catch (Exception e) {
			log.error("Exception :updateavatarcredit : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}
	
	@RequestMapping(value = "app/v5.0/BuyEbook", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse BuyEbookV5(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "userId") Long userId, @RequestParam(value = "bookId") Long bookId,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entered into BuyEbook : authkey : " + auth);
		UserV4 usr = null;

		try {
			usr = userServiceV4.verifyAuthV4("authkey", auth);

		} catch (Exception e) {
			log.error("BuyEbook: user by authkey : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		if (usr != null) {

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
		}
		try {

			Boolean success = iAvatarService.buyEbook(userId, bookId);
			if (success) {
				response.put("Status", 1);
				response.put("Msg", "Woo-hoo! You've successfully redeemed your free credits to purchase your e-Book.");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Cant Update e-Book Credit");
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Auth Error");
		}

		return response;
	}
	
	@RequestMapping(value = "app/v5.0/getAdditionalBenefits", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAdditionalBenefitsV5(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "emailId", defaultValue = "0", required = false) String emailId,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver) {

		JResponse response = new JResponse();

		String auth = header.getFirst("auth");
		log.info("Entered into getAdditionalBenefits : authkey : " + auth);
		UserV4 usr = null;
		
		try {
			usr = userServiceV4.verifyAuthV3("authkey", auth);

		} catch (Exception e) {
			log.error("getAdditionalBenefits: user by authkey : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		if (usr != null) {

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
		}
		try {
			RewardCredit rewardCredit = iAvatarService.checkCredit(emailId);

			if (rewardCredit != null) {
				boolean showEbook = true;
				boolean showAvatar = true;
				boolean showMerchCoupon = false;
				boolean showMugCoupon = false;
				boolean showWaggleCamCoupon = false;
				boolean showReferralCoupon = false;

				if (!rewardCredit.getMerchCoupon().equalsIgnoreCase("NA"))
					showMerchCoupon = true;

				if (!rewardCredit.getMugCoupon().equalsIgnoreCase("NA"))
					showMugCoupon = true;
				
				if (!rewardCredit.getwaggleCamCoupon().equalsIgnoreCase("NA"))
					showWaggleCamCoupon = true;
				
				if (!rewardCredit.getReferralCoupon().equalsIgnoreCase("NA"))
					showReferralCoupon = true;
				

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("showEbook", showEbook);
				response.put("showAvatar", showAvatar);
				response.put("showMerchCoupon", showMerchCoupon);
				response.put("showMugCoupon", showMugCoupon);
				response.put("showWaggleCamCoupon", showWaggleCamCoupon);
				response.put("showReferralCoupon", showReferralCoupon);
				response.put("mugUrl", "https://www.wagglemerch.com/products/white-glossy-mug");
				response.put("merchUrl", "https://www.wagglemerch.com/");
				response.put("ebookurl", "https://www.wagglemerch.com/collections/waggfluence-pet-ebooks");
				response.put("waggleCamUrl", "https://mywaggle.com/pages/wagglecam");
				response.put("referralUrl", "");
				response.put("credits", rewardCredit);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed");
			}
		} catch (Exception e) {
			log.error("Exception :getAdditionalBenefits : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
			response.put("Error", e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "app/v5.0/generateCoupons", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse generateCouponsV5(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam("emailId") String emailId, @RequestParam("firstName") String firstName,
			@RequestParam("lastName") String lastName, @RequestParam("dueDate") String dueDate,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam("period") String period) {
		log.info(" Entered generateCoupons ");
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");

		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			String periodid = period;
			if(period.length()>1) {
				HashMap<String, String> periodlist = new HashMap<String, String>();
				periodlist.put( "Monthly","1");
				periodlist.put("Quarterly","2");
				periodlist.put( "Half-Yearly","3");
				periodlist.put( "Half Yearly","3");
				periodlist.put( "Yearly","4");
				periodlist.put( "2-Year","5");
				periodlist.put( "5-Year","6");
				periodlist.put( "2 Year","5");
				periodlist.put( "5 Year","6");
				periodid = periodlist.get(period);
			}
			
			JCouponData couponObj = new JCouponData(user.getId(), emailId, periodid, firstName, lastName, dueDate);

			response = iAvatarService.generateCoupon(couponObj,"plan-purchase");

		} catch (Exception e) {
			log.error("Exception :generateCoupons : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Coupon Not generated ");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v3.0/addAvatarCredit", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse AddCreditToUser(@RequestParam("emailId") String emailId, @RequestParam("period") String period) {
		log.info(" Entered addAvatarCredit ");
		JResponse response = new JResponse();

		try {
			UserV4 user = userServiceV4.verifyAuthV4("email", emailId);
			if (user != null) {
				Boolean success = iAvatarService.addCredit(user.getId(), emailId, period);
				if (success) {
					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					response.put("Status", 0);
					response.put("Msg", "credit update failed");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "invalid user");
			}
		} catch (Exception e) {
			log.error("Exception :addAvatarCredit : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Can't add credit");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	// App apis
	@RequestMapping(value = "app/v4.0/generateCoupons", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse generateCoupons(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam("emailId") String emailId, @RequestParam("firstName") String firstName,
			@RequestParam("lastName") String lastName, @RequestParam("dueDate") String dueDate,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam("period") String period) {
		log.info(" Entered generateCoupons ");
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");

		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			String periodid = period;
			if(period.length()>1) {
				HashMap<String, String> periodlist = new HashMap<String, String>();
				periodlist.put( "Monthly","1");
				periodlist.put("Quarterly","2");
				periodlist.put( "Half-Yearly","3");
				periodlist.put( "Half Yearly","3");
				periodlist.put( "Yearly","4");
				periodlist.put( "2-Year","5");
				periodlist.put( "5-Year","6");
				periodlist.put( "2 Year","5");
				periodlist.put( "5 Year","6");
				periodid = periodlist.get(period);
			}
			
			JCouponData couponObj = new JCouponData(user.getId(), emailId, periodid, firstName, lastName, dueDate);

			response = iAvatarService.generateCoupon(couponObj,"plan-purchase");

		} catch (Exception e) {
			log.error("Exception :generateCoupons : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Coupon Not generated ");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "app/v4.0/getEbookList", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getEbookList(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam("userId") Long userId,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		log.info(" Entered getEbookList :: ");
		JResponse response = new JResponse();
		List<eBook> ebooks = new ArrayList<eBook>();
		String autho = header.getFirst("auth");
		try {
			UserV4 usr = null;
			usr = userServiceV4.verifyAuthV3("authkey", autho);
			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (userId != usr.getId()) {
				response.put("Status", 0);
				response.put("Msg", "Error");
				return response;
			}

			RewardCredit avatarCredit = iAvatarService.checkCredit(usr.getEmail());
			int credit = 0;
			if (avatarCredit != null) {
				credit = avatarCredit.getEbookCredit();
			}

			ebooks = iAvatarService.getEbooks(userId);
			if (ebooks != null && !ebooks.isEmpty()) {
				response.put("Status", 1);
				response.put("ebooks", ebooks);
				response.put("Msg", "Success");
				response.put("ebookCredit", credit);
			} else {
				response.put("Status", 0);
				response.put("Msg", "e-Book list is empty");
				response.put("ebookCredit", credit);
			}

		} catch (Exception e) {
			log.error("Exception :getEbookList : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "E-Book Error");
			response.put("ebookCredit", 0);
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "app/v4.0/BuyEbook", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse BuyEbook(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "userId") Long userId, @RequestParam(value = "bookId") Long bookId,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entered into BuyEbook : authkey : " + auth);
		UserV4 usr = null;

		try {
			usr = userServiceV4.verifyAuthV4("authkey", auth);

		} catch (Exception e) {
			log.error("BuyEbook: user by authkey : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		if (usr != null) {

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
		}
		try {

			Boolean success = iAvatarService.buyEbook(userId, bookId);
			if (success) {
				response.put("Status", 1);
				response.put("Msg", "Woo-hoo! You've successfully redeemed your free credits to purchase your e-Book.");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Cant Update e-Book Credit");
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Auth Error");
		}

		return response;
	}

	@RequestMapping(value = "app/v4.0/getAdditionalBenefits", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAdditionalBenefits(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "emailId", defaultValue = "0", required = false) String emailId,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {

		JResponse response = new JResponse();

		String auth = header.getFirst("auth");
		log.info("Entered into getAdditionalBenefits : authkey : " + auth);
		UserV4 usr = null;
		
		try {
			usr = userServiceV4.verifyAuthV3("authkey", auth);

		} catch (Exception e) {
			log.error("getAdditionalBenefits: user by authkey : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		if (usr != null) {

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
		}
		try {
			RewardCredit rewardCredit = iAvatarService.checkCredit(emailId);

			if (rewardCredit != null) {
				boolean showEbook = true;
				boolean showAvatar = true;
				boolean showMerchCoupon = false;
				boolean showMugCoupon = false;
				boolean showWaggleCamCoupon = false;
				boolean showReferralCoupon = false;

				if (!rewardCredit.getMerchCoupon().equalsIgnoreCase("NA"))
					showMerchCoupon = true;

				if (!rewardCredit.getMugCoupon().equalsIgnoreCase("NA"))
					showMugCoupon = true;
				
				if (!rewardCredit.getwaggleCamCoupon().equalsIgnoreCase("NA"))
					showWaggleCamCoupon = true;
				
				if (!rewardCredit.getReferralCoupon().equalsIgnoreCase("NA"))
					showReferralCoupon = true;
				

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("showEbook", showEbook);
				response.put("showAvatar", showAvatar);
				response.put("showMerchCoupon", showMerchCoupon);
				response.put("showMugCoupon", showMugCoupon);
				response.put("showWaggleCamCoupon", showWaggleCamCoupon);
				response.put("showReferralCoupon", showReferralCoupon);
				response.put("mugUrl", "https://www.wagglemerch.com/products/white-glossy-mug");
				response.put("merchUrl", "https://www.wagglemerch.com/");
				response.put("ebookurl", "https://www.wagglemerch.com/collections/waggfluence-pet-ebooks");
				response.put("waggleCamUrl", "https://mywaggle.com/pages/wagglecam");
				response.put("referralUrl", "");
				response.put("credits", rewardCredit);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed");
			}
		} catch (Exception e) {
			log.error("Exception :getAdditionalBenefits : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
			response.put("Error", e.getLocalizedMessage());
		}

		return response;
	}

}
