package com.nimble.irisservices.appcontroller;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;

import com.nimble.irisservices.entity.*;
import com.nimble.irisservices.service.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.chargebee.Environment;
import com.chargebee.Result;
import com.chargebee.models.HostedPage;
import com.chargebee.models.HostedPage.CheckoutExistingRequest;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSubscriptionPlan;
import com.nimble.irisservices.dto.JUpdateCancelAck;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;

@RestController
@RequestMapping("/app")
public class CancelControllerApp {

	private static final Logger log = LogManager.getLogger(CancelControllerApp.class);

	@Autowired
	IUserServiceV4 userServiceV4;
	
	@Autowired
	Helper _helper;
	
	@Autowired
	ICancelService cancelService;
	
	@Autowired
	ICreditSystemService crService;
	
	@Autowired
	IGatewayService gatewayService;
	
	@Autowired
	private IAsyncService async;

	@Autowired
	@Lazy
	IChargebeeService cbService;
	
	@Value("${price_too_high_success_content_title}")
	private String price_too_high_success_content_title;
	
	@Value("${price_too_high_success_content_body}")
	private String price_too_high_success_content_body;
	
	@Value("${title_content_mini_cam}")
	private String title_content_mini_cam;
	
	@Value("${device_prize_mini_cam}")
	private String device_prize_mini_cam;
	
	@Value("${image_url_mini_cam}")
	private String image_url_mini_cam;
	
	@Value("${offer_prize_mini_cam}")
	private String offer_prize_mini_cam;
	
	@Value("${offer_description_mini_cam}")
	private String offer_description_mini_cam;
	
	@Value("${popup_title_mini_cam}")
	private String popup_title_mini_cam;
	
	@Value("${popup_description_mini_cam}")
	private String popup_description_mini_cam;
	
	@Value("${title_content_wc_pro}")
	private String title_content_wc_pro;
	
	@Value("${device_prize_wc_pro}")
	private String device_prize_wc_pro;
	
	@Value("${image_url_wc_pro}")
	private String image_url_wc_pro;
	
	@Value("${offer_prize_wc_pro}")
	private String offer_prize_wc_pro;
	
	@Value("${offer_description_wc_pro}")
	private String offer_description_wc_pro;
	
	@Value("${popup_title_wc_pro}")
	private String popup_title_wc_pro;
	
	@Value("${popup_description_wc_pro}")
	private String popup_description_wc_pro;
	
	@Value("#{${chargebee.updateaddonid}}")
	private Map<String, String> updateAddonId;
	
	@Value("${redirtPetUrl}")
	private String redirtPetUrl;
	
	@Value("${embedupdate}")
	private boolean embedupdate;
	
	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;
	
	@Value("${apply_coupon_cancel_sub}")
	private boolean apply_coupon_cancel_sub;
	
	@Value("${cancellation_note_content_wc_pro}")
	private String cancellation_note_content_wc_pro;
	
	@Value("${cancellation_note_content_mini_cam}")
	private String cancellation_note_content_mini_cam;
	
	@Value("${nottravel_pm_content}")
	private String nottravel_pm_content;
	
	@Value("${nottravel_pm_title_content}")
	private String nottravel_pm_title_content;
	
	@Value("${nottravel_pm_title_content_note}")
	private String nottravel_pm_title_content_note;
	
	@Value("${renewal_content_note}")
	private String renewal_content_note;

	@Value("${offer_success_content_title}")
	private String offer_success_content_title;

	@Value("${offer_success_content_body}")
	private String offer_success_content_body;

	@Value("${vet_chat.cancel.title}")
	private String vetChatCancelTitle;

	@Value("${vet_chat.cancel.content}")
	private String vetChatCancelContentList;
	
	@GetMapping("v5.0/cancelfeedbacklist")
	public JResponse getCancelFeedBackListV5(
			@RequestParam String os,
			@RequestParam String app_ver,
			@RequestParam long planid,
			@RequestParam long periodid,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "reqver", defaultValue = "V1", required = false) String reqver,
			@RequestParam(required = false, defaultValue = "0") long gateway_id, @RequestParam(required = false, defaultValue = "null") String sub_id
			) {
		log.info("Entered into getCancelFeedBackList");
		JResponse response = new JResponse();
		long monitortype_id = 1;
		try {
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {

				user = userServiceV4.verifyAuthV4("authkey", auth);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
				log.info("user_id : "+ user.getId());
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			List<CancelFeedback> cancelFeedBackList = cancelService.getAllCancelFeedback();

			if( cancelFeedBackList == null || cancelFeedBackList.isEmpty() ) {
				response.put("Status", 0);
				response.put("Msg", "Invalid session, Please try again");
				return response;
			}

			if (reqver.equalsIgnoreCase("V1")) {
				cancelFeedBackList = cancelFeedBackList.stream()
						.filter(feedback -> feedback.getId() != 9 && feedback.getId() != 10)
						.collect(Collectors.toList());
			}

			response.put("cancel_feedback", cancelFeedBackList);

			ArrayList<Map<String,Object>> contentList = new ArrayList<>();

			List<CancelFeedbackContent> cancelFeedbackContent = cancelService.getAllCancelFeedbackContent();
			if( cancelFeedbackContent == null || cancelFeedbackContent.isEmpty() ) {
				response.put("Status", 0);
				response.put("Msg", "Invalid session, Please try again");
				return response;
			}

			List<NotTravelContent> notTravelContent = cancelService.getAllNotTravelContent();
			if(notTravelContent != null && !notTravelContent.isEmpty()) {
				response.put("not_travel_content", notTravelContent);
			}

			List<NotHappyServiceContent> notHappyServiceContent = cancelService.getAllNotHappyServiceContent();
			if(notHappyServiceContent != null && !notHappyServiceContent.isEmpty()) {
				response.put("nothappy_service_content", notHappyServiceContent);
			}

			AllProductSubscription sub = cbService.getProductSubscriptionByGatewayId(gateway_id, user.getChargebeeid(),monitortype_id);
			String type = "downgrade";
			boolean is_flexi = false;
			if(sub != null && sub.getPlanId().contains("flexi")) {
				type = "cancelall";
				is_flexi = true;
			}

			List<CancelFeedbackContentList> cancelFeedimgContentList = cancelService.getAllCancelFeedbackContentList(is_flexi,periodid);
			long countType3 = cancelFeedimgContentList.stream()
					.filter(item -> item.getCancel_feedback_id() == 3)
					.count();
			if ((countType3 == 3 || countType3 == 2)) {
				// Step 2: Remove items with redirectOt == 3
				cancelFeedimgContentList.removeIf(item ->
						item.getCancel_feedback_id() == 3 && item.getBtn_redirect() == 3
				);
			}

			if( cancelFeedimgContentList == null || cancelFeedimgContentList.isEmpty() ) {
				response.put("Status", 0);
				response.put("Msg", "Invalid session, Please try again");
				return response;
			}

			cancelFeedBackList.forEach( feedBack -> {
				Map<String, Object> map = new HashMap<>();

				map = cancelFeedbackContent.stream()
				.filter( content -> content.getCancel_feedback_id() == feedBack.getId() )
				.collect( Collectors.toMap(  CancelFeedbackContent::getContent_key, CancelFeedbackContent::getContent_response) );

				map.put( "id", feedBack.getId() );

				List<CancelFeedbackContentList> newbjList = cancelFeedimgContentList.stream()
						.filter(imgContent -> imgContent.getCancel_feedback_id() == feedBack.getId()) // Match by feedback ID
						.collect(Collectors.toList());

				if (!newbjList.isEmpty()) {
					map.put("cancel_content_list", newbjList);
				}

				contentList.add(map);

			} );

			int available_monitor_size = 0;
			List<JGateway> jGatewayList = gatewayService.getJGatewayByUser(user.getId(), "1");
			if( jGatewayList != null && !jGatewayList.isEmpty() ) {
				available_monitor_size = jGatewayList.size();
			}

			List<Object> pauseList = new ArrayList<>();
	        pauseList.add("Not travelling");
	        pauseList.add("RV under Maintenance");
	        pauseList.add("Others");

	        List<Map<String, Object>> formattedList = new ArrayList<>();

	        int id = 1; // Starting ID value
	        for (Object pause : pauseList) {
	            Map<String, Object> map = new HashMap<>();
	            map.put("id", id);
	            map.put("name", pause.toString());
	            formattedList.add(map);
	            id++;
	        }

			Map<String, Object> vetChatCancelContent = new HashMap<>();
			vetChatCancelContent.put("title", vetChatCancelTitle);
			vetChatCancelContent.put("content", vetChatCancelContentList.split("\\|"));

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("vet_chat_cancel_content", vetChatCancelContent);
			response.put("cancel_feedback_content", contentList);
			response.put("show_downgrade", true);
			response.put("pauseList", formattedList);

			if( periodid >= 4 && available_monitor_size >= 2 ) {
				response.put("title_content", title_content_wc_pro);
				response.put("device_prize", device_prize_wc_pro);
				response.put("image_url", image_url_wc_pro);

				if( apply_coupon_cancel_sub )
					response.put("offer_prize", offer_prize_wc_pro);

				response.put("offer_description", offer_description_wc_pro);
				response.put("popup_title", popup_title_wc_pro);
				response.put("popup_description", popup_description_wc_pro);
				response.put("cancellation_note_content", cancellation_note_content_wc_pro);
			} else if( periodid >= 3 && available_monitor_size >= 1 ) {
				response.put("title_content", title_content_mini_cam);
				response.put("device_prize", device_prize_mini_cam);
				response.put("image_url", image_url_mini_cam);

				if( apply_coupon_cancel_sub )
					response.put("offer_prize", offer_prize_mini_cam);

				response.put("offer_description", offer_description_mini_cam);
				response.put("popup_title", popup_title_mini_cam);
				response.put("popup_description", popup_description_mini_cam);
				response.put("cancellation_note_content", cancellation_note_content_mini_cam);
			}

			response.put("nottravel_pm_content", nottravel_pm_content);
			response.put("nottravel_pm_title_content", nottravel_pm_title_content);
			response.put("nottravel_pm_title_content_note", nottravel_pm_title_content_note);
			response.put("renewal_content_note", renewal_content_note);




			JResponse crRes = crService.getUpgradeSubPlanV6(user, planid, periodid, monitortype_id, "US", false, gateway_id, type,"Data-Plan,Flexi-Plan", sub_id, false, is_flexi);

//			JResponse crRes = crService.upgradePlanList_v5(user, planid, periodid, false, "US", "downgrade");

			if( ((int) crRes.get("Status")) == 1 ) {
				ArrayList<JSubscriptionPlan> planList = (ArrayList<JSubscriptionPlan>) crRes.get("planlist");
				if( planList.isEmpty() )
					response.put("show_downgrade", false);
			}

			SubscriptionPlan planver = crService.getSubsPlanById(planid);
			if (planver != null && !planver.getPlan_ver().equalsIgnoreCase("NA") && planver.getPlan_ver().equalsIgnoreCase("V3") && periodid==1) {
				response.put("show_downgrade", false);
			}

		} catch (Exception e) {
			log.error("Error in getCancelFeedBackList :: Error : "+ e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		return response;
	}
	
	@PostMapping("v5.0/updatecancelack")
	public JResponse updateCancelAckV5(
			@RequestParam String os,
			@RequestParam String app_ver,
			@RequestBody JUpdateCancelAck updateCancelAck,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime
			) {
		log.info("Entered into updateCancelAck");
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");
			
			UserV4 user = null;
			try {
				
				user = userServiceV4.verifyAuthV4("authkey", auth);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
				log.info("user_id : "+ user.getId());
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			
//			if( !updateCancelAck.isIs_cancel() ) {
//				boolean status = cancelService.deleteUserCancelFeedBack(user.getId());
//				log.info("delete user_cancel_feedback status : "+ status);
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//				return response;
//			}
			
			if( updateCancelAck.getCancel_feedback_id() == 0 ) {
				log.info("cancel_feedback_id is 0");
				response.put("Status", 0);
				response.put("Msg", "Invalid session, Please try again");
				return response;
			}
			
			Properties prop = new Properties();
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			prop.load(new FileInputStream(file));

			String to_address = prop.getProperty("to_address");
			String cc_address = prop.getProperty("cc_address");
			String bcc_address = prop.getProperty("bcc_address");
			
			CancelFeedback cancelFeedback = cancelService.getCancelFeedbackById( updateCancelAck.getCancel_feedback_id() );
			if( cancelFeedback == null ) {
				response.put("Status", 0);
				response.put("Msg", "Invalid session, Please try again");
				return response;
			}
 			
			String mailSub = "Reg Cancel Feedback : "+ user.getUsername();
			String mailContent = "<p>Hi Team,</p>" + "<p>Find the user cancel feedback request</p>";
			mailContent += "<p>Email           : "+ user.getEmail() +"</p>";
			mailContent += "<p>Chargebee Id    : "+ user.getChargebeeid() +"</p>";
			mailContent += "<p>Current Plan    : "+ updateCancelAck.getChargebee_plan_id() +"</p>";
			mailContent += "<p>Reason          : "+ cancelFeedback.getFeedback_type() +"</p>";
			mailContent += "<p>Customer Review : "+ updateCancelAck.getCustomer_review() +"</p>";
			if( apply_coupon_cancel_sub )
				mailContent += "<p>Offer Provide   : "+ updateCancelAck.getOffer() +"</p>";
			mailContent += "<p>Offer Device   : "+ updateCancelAck.getOffer_device() +"</p>";
			
			mailContent = mailContent + "<br><br>Thanks,<br> Irisservice ";
			async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
			
			UserCancelFeedBack userCancelFeedBack = null;
			userCancelFeedBack = cancelService.getUserCancelFeedBackByUserId(user.getId());
			if( userCancelFeedBack == null ) {
				userCancelFeedBack = new UserCancelFeedBack();
				userCancelFeedBack.setUser_id( user.getId() );
			} 
			
			userCancelFeedBack.setCancel_feedback_id( updateCancelAck.getCancel_feedback_id() );
			userCancelFeedBack.setUpdated_on( _helper.getCurrentTimeinUTC() );
			userCancelFeedBack.setCustomer_review( updateCancelAck.getCustomer_review() );
			userCancelFeedBack.setShow_cancel_sub( updateCancelAck.isBtn_cancel() );
			
			if( updateCancelAck.isApply_coupon() ) {
				Environment.configure(chargebeeSiteName, chargebeeSiteKey);
				PlanMigration planMigration = crService.getCurrentValidPlan( updateCancelAck.getPlanid(), updateCancelAck.getPeriodid() );
				if( planMigration == null ) {
					response.put("Status", 0);
					response.put("Msg", "Invalid session, Please try again");	
					return response;
				}
				String country = user.getCountry().toUpperCase();

				String[] cbPlanAndTrialPeriod = crService.getChargebeePlanById( planMigration.getMigration_plan_id() , planMigration.getMigration_period_id(), country);
				String cb_plan = cbPlanAndTrialPeriod[0];
				
				String upgradeId = updateAddonId == null ? "upgrade_charges" : updateAddonId.get(country);
			
				CheckoutExistingRequest checkoutExitingRequest = HostedPage.checkoutExisting()
						.subscriptionId( updateCancelAck.getSubs_id() ).subscriptionPlanId( cb_plan ).addonId(0, upgradeId)
						.addonQuantity(0, 1).addonBillingCycles(0, 1).replaceAddonList(true)
						.redirectUrl(redirtPetUrl).embed(embedupdate)
						.forceTermReset(true);
				
				if( apply_coupon_cancel_sub )
					checkoutExitingRequest.subscriptionCoupon( planMigration.getCoupon_code() );

				Result res = checkoutExitingRequest.request();
				
				HostedPage hostedPage = res.hostedPage();
				String checkout_url = hostedPage.url();
				response.put("check_out_url", checkout_url);
				
				CancelCustomerRetain cancelCustomerRetain = crService.getCancelCustomerRetain( user.getId() );
				if( cancelCustomerRetain == null ) {
					cancelCustomerRetain = crService.saveOrUpdateCancelCustomerRetain( new CancelCustomerRetain(0, user.getId(), _helper.getCurrentTimeinUTC()) );	
				} else {
					cancelCustomerRetain.setUpdated_on( _helper.getCurrentTimeinUTC() );
					cancelCustomerRetain = crService.saveOrUpdateCancelCustomerRetain( cancelCustomerRetain );
				}
			}
			
			userCancelFeedBack = cancelService.saveOrUpdateUserCancelFeedBack( userCancelFeedBack );
			if( userCancelFeedBack == null ) {
				log.error("Error while saving userCancelFeedBack");
				response.put("Status", 0);
				response.put("Msg", "Invalid session, Please try again");
				return response;
			}
			
			async.saveOrUpdateUserCancelFeedBackHistory( new UserCancelFeedBackHistory( userCancelFeedBack ) );
			
			response.put("Status", 1);
			response.put("Msg", "Success");

			if( updateCancelAck.getCancel_feedback_id() == 2  && updateCancelAck.getOffer().equalsIgnoreCase("Get Up to $100 Off")){
				response.put("title", offer_success_content_title);
				response.put("content", offer_success_content_body);
			}else if( updateCancelAck.getCancel_feedback_id() == 2 ) {
				response.put("title", price_too_high_success_content_title);
				response.put("content", price_too_high_success_content_body);
			}else if( updateCancelAck.getCancel_feedback_id() == 4 ) {
				response.put("title", offer_success_content_title);
				response.put("content", offer_success_content_body);
			}else if( updateCancelAck.getCancel_feedback_id() == 3 ) {
				response.put("title", offer_success_content_title);
				response.put("content", offer_success_content_body);
			}
			
		} catch (Exception e) {
			log.error("Error in updateCancelAck :: Error : "+ e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		
		return response;
	}
	
}
