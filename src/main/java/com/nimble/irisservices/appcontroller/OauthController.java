package com.nimble.irisservices.appcontroller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.service.impl.OAuth2ServiceImpl;


@Controller
@RequestMapping("/alexa")
@SessionAttributes("authorizationRequest")
public class OauthController {

	@Autowired
	SessionFactory sessionFactory;

	@Autowired
	OAuth2ServiceImpl oauthDAOService;

	private static final Logger log = LogManager.getLogger(OauthController.class);

	@RequestMapping(value="/", method=RequestMethod.GET)
	@ResponseBody
	public String oAuthCheck() {
		log.info("Entered into updateNote");
		return "Welcome to waggle Oauth Server";
	}

	@GetMapping(value = "/login")
	public String loginPage() {
		log.info("Entered into loginPage");
		return "login";
	}

	@RequestMapping(value = "/oauth/confirm_access")
	public String customAuthorize() {
		log.info("Entered into customAuthorize");
		return "authorize";
	}	

	@RequestMapping(value = "/getauthcode")	
	public @ResponseBody JResponse getAuthorized(@RequestParam String username, @RequestParam String password,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		log.info("Entered into getAuthorized");
		JResponse response = new JResponse();
		String authCode = "NA";
		try {			
//			username = AES.decrypt(username);
//			password = AES.decrypt(password);
			OAuth2Authentication oAuth2= oauthDAOService.getOAuth2(username,password);
			
			authCode = oauthDAOService.saveOauth2Code(oAuth2);
			if(authCode != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("authcode", authCode);
			}else {
				response.put("Status", 1);
				response.put("Msg", "Failed");
				response.put("authcode", "NA");
			}
			
		}catch(Exception e) {
			log.error("Exception occured at getAuthorized : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error occured");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

}
