package com.nimble.irisservices.appcontroller;

import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGeneralConfigService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

import freemarker.template.Configuration;

@Controller
@RequestMapping("/app")
public class GeneralControllerApp {

	@Autowired
	@Lazy
	IUserService userService;
	
	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IGeneralConfigService generalConfigService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;

	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;

	@Value("${petguideurl}")
	private String petGuideURL;

	@Value("${mapboxtoken}")
	private String mapBoxKeyToken;

	@Value("${nimbleauthkey}")
	private String nimbleAuthKey;

	@Value("${Facebook}")
	private String facebook;

	@Value("${Twitter}")
	private String twitter;

	@Value("${Instagram}")
	private String instagram;

	@Value("${Pinterest}")
	private String pinterest;

	@Value("${support}")
	private String support;

	@Value("${Support_Appstore}")
	private String Support_Appstore;

	@Value("#{${Marketing_Appstore}}")
	private Map<String,String> Marketing_Appstore;

	@Value("#{${Privacy_policy}}")
	private Map<String,String> privacy_policy;

	@Value("${buynowfurbit}")
	private String buynowFurbit;

//	@Value("${buynowpetsafety}")
//	private String buynowpetsafety;
	
	@Value("${blogUrl}")
	private String blogUrl;
	
//	@Value("${faqUrl}")
//	private String faqUrl;
	
	@Value("${showpopupregisteration}")
	private boolean showPopUpRegisteration;
	
	@Value("${amplitude_andriod}")
	private String amplitude_andriod;

	@Value("${amplitude_ios}")
	private String amplitude_ios;
	
	@Value("${hr_rateus}")
	private boolean hr_rateus;
	
	@Value("${rateuscount}")
	private int rateuscount;
	
	@Value("#{${terms_conditions}}")
	private Map<String,String>  terms_conditions;
	
	@Value("${fb_live}")
	private String fb_live;
	
	@Value("${chatbot}")
	private boolean chatbot;
	
	@Value("${showamazonrateus}")
	private boolean showamazonrateus;
	
	@Value("${redirectamazon}")
	private boolean redirectamazon;
	
	@Value("${amazonredirecturl}")
	private String amazonredirecturl;

	@Autowired
	Configuration templates;
	
	@Autowired
	Helper _helper;
	
	@Value("${service.url}")
	private String serviceBaseURL;
	
	
	private static final Logger log = LogManager.getLogger(GeneralControllerApp.class);
	
	@RequestMapping(value = "v5.0/getchatbot/", method = RequestMethod.GET,produces = MediaType.TEXT_HTML_VALUE, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getChatBotV5(@RequestParam("os") String os, Authentication authentication,@RequestHeader HttpHeaders header,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		try {
			String body = "";
			String auth = header.getFirst("auth");
//			Template t = templates.getTemplate("chatbot.ftl");
//			Map<String, String> map = new HashMap<>();
//			//map.put("FNAME", user.getFirstname() );
//			body = FreeMarkerTemplateUtils.processTemplateIntoString(t, map);
//			return ResponseEntity.badRequest().body(body);

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV3("authkey", auth);
				
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
				
			} catch (Exception e) {
				log.error("getCurrentSubscriptionPlan:userby auth : " + e.getMessage());
			}
			
			if (user != null ) {
				if(chatbot == true) {
					String chatBotURL = serviceBaseURL+"/ext/v4.0/getchatbot/?username="+user.getUsername()+"&email="+user.getEmail()+"&mobileno="+user.getMobileno()+"&firstname="+user.getFirstname();
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("chatbot_url", chatBotURL);
				} else {
					response.put("Status", 0);
					response.put("Msg", "Chatbot not enabled");
				}
			} else {
				log.info("User not exists");
				response.put("Status", 0);
				response.put("Msg", "user not found");
			}

		} catch (Exception e) {
			log.error("getChatBot : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error while getting chatbot url");
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}	
	
}
