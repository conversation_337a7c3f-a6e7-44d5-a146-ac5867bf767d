package com.nimble.irisservices.appcontroller;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.TimeZone;
import java.util.stream.Collectors;

import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.controller.PushNotificatonController;
import com.nimble.irisservices.dto.JBowlPetProfile;
import com.nimble.irisservices.dto.JGatewayInfo;
import com.nimble.irisservices.dto.JPetFood;
import com.nimble.irisservices.dto.JPetFoodPerDay;
import com.nimble.irisservices.dto.JProductSubRes;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.PetBreeds;
import com.nimble.irisservices.entity.PetFeedDetails;
import com.nimble.irisservices.entity.PetFood;
import com.nimble.irisservices.entity.PetFoodPerDay;
import com.nimble.irisservices.entity.PetSpecies;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.job.PetFeedRemainder;
import com.nimble.irisservices.quartz.JobService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IPetSpeciesServices;
import com.nimble.irisservices.service.IPetSpeciesServicesV4;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class PetSpeciesControllerV4App {

	private static final Logger log = LogManager.getLogger(PetSpeciesControllerV4App.class);

	@Autowired
	@Lazy
	IPetSpeciesServices ipetSpeciesServices;

	@Autowired
	@Lazy
	IPetSpeciesServicesV4 ipetSpeciesServicesv4;

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	Helper _helper;
	
	@Autowired
	IAsyncService asyncService;
	
	@Autowired
	@Lazy
	JobService jobService;
	
	@Value("${sheduled_feed_remainder_time}")
	private int sheduled_feed_remainder_time;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;
	
	@Autowired
	PushNotificatonController pushNotificatonController;

    
	@RequestMapping(value = "v5.0/getbreeds", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getBreedsV5(
			@RequestParam(value = "speciename", defaultValue = "", required = false) String speciename,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", required = true) String os,
			@RequestParam(value = "app_ver", required = true) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		log.info("Entering getBreedsV4 : ");
		try {

			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<PetBreeds> petBreeds = new ArrayList<PetBreeds>();

			petBreeds = ipetSpeciesServicesv4.getPetBreedsV4(speciename);

			if (petBreeds != null) {
				if (petBreeds.size() > 0) {
					if (speciename.isEmpty()) {
						response.put("petBreeds", petBreeds);
					} else {
						List<String> breedName = new ArrayList<String>();

						for (PetBreeds breed : petBreeds) {
							breedName.add(breed.getBreedName());
						}

						Collections.sort(breedName);

						response.put("petBreeds", breedName);
						response.put("Msg", "Success");
					}

					response.put("Status", 1);

					response.put("Return Time", System.currentTimeMillis());
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "No pet breeds found");
					log.error("No pet breeds found");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "No pet breeds found");
				log.error("No pet breeds found");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting pet breeds.");
			response.put("Error", e.getLocalizedMessage());
			log.error("Excepitoin : getBreedsV4 : " + e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}
	
	// No changes compared to V3
	@RequestMapping(value = "v4.0/getspecies", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSpeciesV4(@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<PetSpecies> petSpecies = new ArrayList<PetSpecies>();

			petSpecies = ipetSpeciesServices.getPetSpecies();

			if (petSpecies != null) {
				if (petSpecies.size() > 0) {

					List<String> species = new ArrayList<String>();

					for (PetSpecies specie : petSpecies) {
						species.add(specie.getSpeciesName());
					}

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("petSpecies", species);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "No Species found");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "No Species found");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		} catch (Exception e) {
			log.error("Excepitoin while getting pet species:" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting pet species.");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}

	// getBreedsV4 - by anand
	@RequestMapping(value = "v4.0/getbreeds", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getBreedsV4(
			@RequestParam(value = "speciename", defaultValue = "", required = false) String speciename,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		log.info("Entering getBreedsV4 : ");
		try {

			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<PetBreeds> petBreeds = new ArrayList<PetBreeds>();

			petBreeds = ipetSpeciesServicesv4.getPetBreedsV4(speciename);

			if (petBreeds != null) {
				if (petBreeds.size() > 0) {
					if (speciename.isEmpty()) {
						response.put("petBreeds", petBreeds);
					} else {
						List<String> breedName = new ArrayList<String>();

						for (PetBreeds breed : petBreeds) {
							breedName.add(breed.getBreedName());
						}

						Collections.sort(breedName);

						response.put("petBreeds", breedName);
						response.put("Msg", "Success");
					}

					response.put("Status", 1);

					response.put("Return Time", System.currentTimeMillis());
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "No pet breeds found");
					log.error("No pet breeds found");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "No pet breeds found");
				log.error("No pet breeds found");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting pet breeds.");
			response.put("Error", e.getLocalizedMessage());
			log.error("Excepitoin : getBreedsV4 : " + e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}
	
	
	//balaji
	@GetMapping("v4.0/petfood")
	@ResponseBody
	public JResponse getPetFood(@RequestParam String os,
			@RequestParam String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header, @RequestParam(value = "gateway_id", defaultValue = "0", required = false) String gatewayId) {
		log.info("Entered into getPetFood");
		JResponse response = new JResponse();
		try {
			
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			boolean isPaidPlan = false;
			if(!gatewayId.equalsIgnoreCase("0")) {
				isPaidPlan = ipetSpeciesServicesv4.findDevicepaidplanornot(Long.valueOf(gatewayId));
			}
			
			List<PetFood> petFoodList = new ArrayList<>();
			long foodId = 0;
			if(!isPaidPlan && !gatewayId.equalsIgnoreCase("0")) {
				foodId = ipetSpeciesServicesv4.getPetFoodFromFeeddetails(Long.valueOf(gatewayId),user.getId());
			}
			
			if(gatewayId.equalsIgnoreCase("0")) {
				petFoodList = ipetSpeciesServicesv4.getPetFood();
			}else {
				petFoodList = ipetSpeciesServicesv4.getPetFoodByUserToo(user.getId(), isPaidPlan, foodId);
			}
//			if( petFoodList == null ) {
//				response.put("Status", 0);
//				response.put("Msg", "Invalid session. Please try again later");
//				return response;
//			}
			
			
			List<JPetFood> customPetFoodList = petFoodList.stream()
					.map( food -> new JPetFood(food.getId(), food.getName(), food.getCalories()) )
					.collect(Collectors.toList());
			
			List<PetFoodPerDay> petFoodPerDayList = ipetSpeciesServicesv4.getPetFoodPerDay();
			if( petFoodPerDayList == null ) {
				response.put("Status", 0);
				response.put("Msg", "Invalid session. Please try again later");
				return response;
			}
			
			List<JPetFoodPerDay> customPetFoodPerDayList = petFoodPerDayList.stream()
					.map( food -> new JPetFoodPerDay(food.getId(), food.getContent(), food.getMeal_count()) )
					.collect(Collectors.toList());
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("food_list", customPetFoodList);
			response.put("meals_per_day_list", customPetFoodPerDayList);
			
		} catch (Exception e) {
			log.error("Error in getPetFood :: Error : "+ e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid session. Please try again later");
		}
		return response;
	}
	
	@PostMapping("v4.0/petfood")
	@ResponseBody
	public JResponse saveOrUpdateCustomPetFood(@RequestBody JPetFood jPetFood,
			@RequestParam String os,
			@RequestParam String app_ver,
			Authentication authentication,
			@RequestHeader HttpHeaders header) {
		log.info("Entered into saveOrUpdateCustomPetFood");
		JResponse response = new JResponse();
		try {
			
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			PetFood petFood = new PetFood( 0, jPetFood.getFood_name(), jPetFood.getCalories(), true, user.getId(), true,
					_helper.getCurrentTimeinUTC());
			PetFood getPetfeed = ipetSpeciesServicesv4.getPetFoodByName(petFood.getName().trim(),user.getId());
			
			if(getPetfeed != null) {
				response.put("Status", 0);
				response.put("already_available", true);
				response.put("Msg", "Food already exist");
				return response;
			}else {
			
			try {
				petFood = ipetSpeciesServicesv4.saveOrUpdatePetFood(petFood);	
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("already_available", true);
				response.put("Msg", "Food already exist");
				return response;
			}
			}
			
			if( petFood == null ) {
				response.put("Status", 0);
				response.put("Msg", "Invalid session. Please try again later");
				return response;
			}
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("already_available", false);
		} catch (Exception e) {
			log.error("Error in saveOrUpdateCustomPetFood :: Error : "+ e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid session. Please try again later");
		}
		return response;
	}
	
	
	@PostMapping("v4.0/petfeeddetails")
	@ResponseBody
	public JResponse saveOrUpdatePetFeedDetails(@RequestBody PetFeedDetails petFeedDetails,
			@RequestParam String os,
			@RequestParam String app_ver,
			Authentication authentication,
			@RequestHeader HttpHeaders header) {
		log.info("Entered into saveOrUpdatePetFeedDetails");
		JResponse response = new JResponse();
		
		try {
			
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			if( petFeedDetails == null ) {
				response.put("Status", 0);
				response.put("Msg", "Invalid session. Please try again later");
				return response;
			}
			long profId = 0;
			if(petFeedDetails.getReq_calories()==0f) {
				List<JBowlPetProfile> petProfile =ipetSpeciesServices.getJBowlPetProfile(user.getId(), petFeedDetails.getGateway_id());
				
				if(petProfile!= null && !petProfile.isEmpty()) {
					profId = petProfile.get(0).getId();
					double weight = Double.parseDouble(String.valueOf(petProfile.get(0).getWeight()));
					double weightPounds = weight * 0.453592;
					double RER = 70 * Math.pow(weightPounds, 0.75);
					double signalment = ipetSpeciesServicesv4.getSignalment(petProfile.get(0).getSpeciesid(), petProfile.get(0).isIntact());		
					double BCS =ipetSpeciesServicesv4.getBCS(petProfile.get(0).getStructure());							
					double activityLevel = ipetSpeciesServicesv4.getActivityLevel( petProfile.get(0).getActivitylevel());
					double MER = RER * signalment * activityLevel * BCS;
					petFeedDetails.setReq_calories(MER);
				}
			}
			
			PetFood pFood = ipetSpeciesServicesv4.getPetFoodById(petFeedDetails.getPet_food_id());
			
			int meal_cnt = petFeedDetails.getMeal_cnt();
			petFeedDetails.setUpdated_on( _helper.getCurrentTimeinUTC() );
			boolean is_meal_updated = petFeedDetails.isMeal_time_updated();
			petFeedDetails = ipetSpeciesServicesv4.saveOrUpdatePetFeedDetails(petFeedDetails);
			petFeedDetails.setMeal_time_updated(is_meal_updated);
			
			double tot_calories = (float)petFeedDetails.getReq_calories();
			double caloriesPerMeal = tot_calories/meal_cnt;
			double caloriesPerGram= pFood.getCalories()/1000;
			
			double weight_grams =caloriesPerMeal / caloriesPerGram;
			double weight_cup = (weight_grams)/226; 
			double weightGramsperDay = tot_calories / caloriesPerGram; 
			int weightGramsperDay_val = (int)weightGramsperDay;
			//int weight_grams_val = (int)weight_grams;
			DecimalFormat df = new DecimalFormat("0.#");
			String content_1 = "Daily Feeding Amount";
			String content_2 = "The daily calorie requirement is calculated by the \"Vet Calculator\", using weight and health factors to determine your pet's energy needs.";
			String content_3 = "Approx. "+(int)weightGramsperDay+" gm(s)";
			String content_4 = "Note: Use a standard 8 oz measuring cup";
			String content_5 = "You have selected "+meal_cnt+" meals per day. So approx. "+(int)weight_grams+" gm(s) per meal.";
			//String content_5 = "You have selected "+meal_cnt+" meals per day. So approx. "+weight_grams_val+" gm(s) per meal/ "+df.format(weight_cup)+" cups per meal.";
			
			String content_6 = "User Guideline";
			String content_7 = "Please press the touch sensor once before and after placing food.";
			String content_8 = "Please set the correct feeding time to receive prompt feeder notifications and to establish the feed reporting cycle from the bowl.";
			String content_9 = "After the food is placed in the bowl, the app will display the grams and calories within 5 minutes and will continue updating the values as your pet feeds for the next hour.";
			String content_10 = "During non-feeding times, the values will be updated once every hour.";
			
			asyncService.updateReqWeightById( petFeedDetails.getId(), weightGramsperDay_val);
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("content_1", content_1);
			response.put("content_2", content_2);
			response.put("content_3", content_3);
			response.put("content_4", content_4);
			response.put("content_5", content_5);
			response.put("content_6", content_6);
			response.put("content_7", content_7);
			response.put("content_8", content_8);
			response.put("content_9", content_9);
			response.put("content_10", content_10);
			
			if( petFeedDetails.isMeal_time_updated() )
				updatePetFeedRemainder(petFeedDetails, user);
			
		} catch (Exception e) {
			log.error("Error in saveOrUpdatePetFeedDetails :: Error : "+ e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later");
		}
		return response;
	}
	
	public boolean updatePetFeedRemainder(PetFeedDetails petFeedDetails, UserV4 user) {
		log.info("Entered into updatePetFeedRemainder :: set_remainder : "+ petFeedDetails.isRemainder());
		try {
			
			if( petFeedDetails.isRemainder() ) {
				
				String[] dateStrArr = petFeedDetails.getMeal_times().split(",");
				List<Date> dateList = new ArrayList<>();
				
				List<Date> dateListAft = new ArrayList<>();
				
				Date now = new Date();
				Calendar cal = Calendar.getInstance();
				cal.setTimeZone(TimeZone.getTimeZone("UTC"));
				
				Arrays.asList(dateStrArr).forEach( date-> {
					Date convertedDate = _helper.timeZoneConverter( "HH:mm", petFeedDetails.getTimezone(), "+00:00", date); 
					cal.setTime( new Date() );
					cal.set(Calendar.HOUR_OF_DAY, convertedDate.getHours());
					cal.set(Calendar.MINUTE, convertedDate.getMinutes());
					Date conDate = new Date( (cal.getTime().getTime() - (1000*60*sheduled_feed_remainder_time )) );
					
					if( conDate.before( now ) ) {
						conDate = DateUtils.addDays(conDate, 1) ;
					}
					
					dateList.add( conDate );
					
				});
				
				String jobName = petFeedDetails.getUser_id()+"_"+petFeedDetails.getGateway_id();
				String groupName = "PET_FEED_REMAINDER";
				
				HashMap<String, Object> userDetails = new HashMap<>();
				userDetails.put("user_id", user.getId());
				userDetails.put("email", user.getEmail());
				userDetails.put("auth", user.getAuthKey());
				userDetails.put("gateway_id", petFeedDetails.getGateway_id());
			
				boolean jobCreatedStatus = jobService.scheduleRepeatJobWithMultipleTriggers(jobName, PetFeedRemainder.class, dateList, groupName, petFeedDetails.getUser_id()+"", groupName, 0, userDetails);
				
				log.info("pet_feed_remainder job created status : "+ jobCreatedStatus);
				
				
			} else {
				log.info("Not a remainder");
			}
			
		} catch (Exception e) {
			log.info("Error in updatePetFeedRemainder :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}

	
	@GetMapping("v4.0/petfeeddetails")
	@ResponseBody
	public JResponse getPetFeedDetails(@RequestParam long gateway_id,
			@RequestParam String os,
			@RequestParam String app_ver,
			Authentication authentication,
			@RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		log.info("Entered into getPetFeedDetails :: gateway_id : "+ gateway_id);
		try {
			
			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			PetFeedDetails petFeedDetails = ipetSpeciesServicesv4.getPetFeedDetails(gateway_id, user.getId());
			if( petFeedDetails == null ) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("pet_feed_details", null);
				return response;
			}
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("pet_feed_details", petFeedDetails);
			
		} catch (Exception e) {
			log.error("Error in getPetFeedDetails :: Error : "+ e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid session. Please try again later");
		}
		return response;
	}
	
	@PostMapping("v5.0/petfeeddetails")
	@ResponseBody
	public JResponse saveOrUpdatePetFeedDetailsV5(@RequestBody PetFeedDetails petFeedDetails, @RequestParam String os,
			@RequestParam String app_ver, Authentication authentication, @RequestHeader HttpHeaders header) {
		log.info("Entered into saveOrUpdatePetFeedDetailsV5");
		JResponse response = new JResponse();
		try {

			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			if (petFeedDetails == null) {
				response.put("Status", 0);
				response.put("Msg", "Invalid session. Please try again later");
				return response;
			}
			long profId = 0;
			if (petFeedDetails.getReq_calories() == 0f) {
				List<JBowlPetProfile> petProfile = ipetSpeciesServices.getJBowlPetProfileById(user.getId(),
						petFeedDetails.getGateway_id());

				if (petProfile != null && !petProfile.isEmpty()) {
					profId = petProfile.get(0).getId();
					double weight = Double.parseDouble(String.valueOf(petProfile.get(0).getWeight()));
					double weightPounds = weight * 0.453592;
					double RER = 70 * Math.pow(weightPounds, 0.75);
					double signalment = ipetSpeciesServicesv4.getSignalment(petProfile.get(0).getSpeciesid(),
							petProfile.get(0).isIntact());
					double BCS = ipetSpeciesServicesv4.getBCS(petProfile.get(0).getStructure());
					double activityLevel = ipetSpeciesServicesv4.getActivityLevel(petProfile.get(0).getActivitylevel());
					double MER = RER * signalment * activityLevel * BCS;
					petFeedDetails.setReq_calories(MER);
				}
			}

			PetFood pFood = ipetSpeciesServicesv4.getPetFoodById(petFeedDetails.getPet_food_id());

			int meal_cnt = petFeedDetails.getMeal_cnt();
			petFeedDetails.setUpdated_on(_helper.getCurrentTimeinUTC());
			boolean is_meal_updated = petFeedDetails.isMeal_time_updated();
			petFeedDetails = ipetSpeciesServicesv4.saveOrUpdatePetFeedDetails(petFeedDetails);
			petFeedDetails.setMeal_time_updated(is_meal_updated);

			double tot_calories = (float) petFeedDetails.getReq_calories();
			double caloriesPerMeal = tot_calories / meal_cnt;
			double caloriesPerGram = pFood.getCalories() / 1000;

			double weight_grams = caloriesPerMeal / caloriesPerGram;
			double weight_cup = (weight_grams) / 226;
			double weightGramsperDay = tot_calories / caloriesPerGram;
			int weightGramsperDay_val = (int) weightGramsperDay;
			// int weight_grams_val = (int)weight_grams;
			DecimalFormat df = new DecimalFormat("0.#");
			String content_1 = "Daily Feeding Amount";
			String content_2 = "The daily calorie requirement is calculated by the \"Vet Calculator\", using weight and health factors to determine your pet's energy needs.";
			String content_3 = "Approx. " + (int) weightGramsperDay + " gm(s)";
			String content_4 = "Note: Use a standard 8 oz measuring cup";
			String content_5 = "You have selected " + meal_cnt + " meals per day. So approx. " + (int) weight_grams
					+ " gm(s) per meal.";
			// String content_5 = "You have selected "+meal_cnt+" meals per day. So approx.
			// "+weight_grams_val+" gm(s) per meal/ "+df.format(weight_cup)+" cups per
			// meal.";

			String content_6 = "User Guideline";
			String content_7 = "Please press the touch sensor once before and after adding food.";
			String content_8 = "Please set the correct feeding time to receive prompt feeder notifications and to establish the feed reporting cycle from the bowl.";
			String content_9 = "After the food is added to the bowl, the app will display the grams/calories within 5 minutes and will continue updating the values once in every hour.";
			String content_10 = "The app will update each time the button on the bowl is pressed.";

			ipetSpeciesServicesv4.updateReqWeightById(petFeedDetails.getId(), weightGramsperDay_val);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("content_1", content_1);
			response.put("content_2", content_2);
			response.put("content_3", content_3);
			response.put("content_4", content_4);
			response.put("content_5", content_5);
			response.put("content_6", content_6);
			response.put("content_7", content_7);
			response.put("content_8", content_8);
			response.put("content_9", content_9);
			response.put("content_10", content_10);

			if (petFeedDetails.isMeal_time_updated())
				updatePetFeedRemainder(petFeedDetails, user);
			/*
			Unirest.setTimeouts(0, 0);
			HttpResponse<String> response2 = Unirest.get("https://weather.visualcrossing.com/VisualCrossingWebServices/rest/services/timeline/11.004556,76.961632?key=Z9HGXU23FJNY36MJZ4LVGMECG")
			  .asString();

			ObjectMapper mapper = new ObjectMapper();
	        
	        try {
	            JsonNode rootNode = mapper.readTree(response2.getBody());
	           
	            JsonNode currentConditions = rootNode.get("currentConditions");
	            
	            String conditions = currentConditions.get("conditions").asText();
	            double temp = currentConditions.get("temp").asDouble();
	            
	            JSendNotification sendNotification  = new JSendNotification();
	            long[] user_id = { (long) petFeedDetails.getUser_id() };
	            
				
	            if(temp > 80) {
	            	response.put("condition",conditions +" High Temperature");
	            	sendNotification.setPushNotificationId( 81 );
	            }else if(temp < 60) {
	            	response.put("condition",conditions +" Very low Temperature");
	            	sendNotification.setPushNotificationId( 82 );
	            }else {
	            	sendNotification.setPushNotificationId( 82 );
	            }
	            
	            sendNotification.setUserID(user_id);
				
				JResponse res = pushNotificatonController.sendNotifications( auth, sendNotification);
	            
	        }catch(Exception e) {
	        	log.error("Error in saveOrUpdatePetFeedDetailsV5 :: Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Please try again later");
	        }*/

		} catch (Exception e) {
			log.error("Error in saveOrUpdatePetFeedDetailsV5 :: Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later");
		}
		return response;
	}
	
	@GetMapping("v5.0/petfeeddetails")
	@ResponseBody
	public JResponse getPetFeedDetailsV5(@RequestParam long gateway_id, @RequestParam String os,
			@RequestParam String app_ver, Authentication authentication, @RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		log.info("Entered into getPetFeedDetailsV5 :: gateway_id : " + gateway_id);
		try {

			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			PetFeedDetails petFeedDetails = ipetSpeciesServicesv4.getPetFeedDetails(gateway_id, user.getId());
			if (petFeedDetails == null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("pet_feed_details", null);
				return response;
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("pet_feed_details", petFeedDetails);

		} catch (Exception e) {
			log.error("Error in getPetFeedDetailsV5 :: Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid session. Please try again later");
		}
		return response;
	}
	
	@GetMapping("v5.0/petfeeddetailsV1")
	@ResponseBody
	public JResponse getPetFeedDetailsV6(@RequestParam long gateway_id, @RequestParam String os,
			@RequestParam String app_ver, Authentication authentication, @RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		log.info("Entered into getPetFeedDetailsV5 :: gateway_id : " + gateway_id);
		try {

			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			List<JGatewayInfo> gatewayList = gatewayServiceV4.getJGatewayInfo(user.getId(),3);
			
			List<PetFeedDetails> petfeedlist = new ArrayList<PetFeedDetails>();
			for (JGatewayInfo gateway : gatewayList) {

				PetFeedDetails petFeedDetails = ipetSpeciesServicesv4.getPetFeedDetails(gateway.getGateway_id(), user.getId());
				
				if (petFeedDetails != null) {
					petfeedlist.add(petFeedDetails);
				}
			}
			
			
			if (petfeedlist == null || petfeedlist.size() < 0) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("pet_feed_details", null);
				return response;
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("pet_feed_details", petfeedlist);

		} catch (Exception e) {
			log.error("Error in getPetFeedDetailsV5 :: Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid session. Please try again later");
		}
		return response;
	}
	
}
