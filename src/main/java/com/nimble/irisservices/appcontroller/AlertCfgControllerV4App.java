package com.nimble.irisservices.appcontroller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.AlertCfgV4;
import com.nimble.irisservices.dto.CompanyConfigResponse;
import com.nimble.irisservices.dto.JAlertCfgV4;
import com.nimble.irisservices.dto.JGatewayConfig;
import com.nimble.irisservices.dto.JGatewaySensorType;
import com.nimble.irisservices.dto.JGeofence;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AlertCfg;
import com.nimble.irisservices.entity.AppImage;
import com.nimble.irisservices.entity.Asset;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAdvertisementService;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAlertCfgServiceV4;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.ICompanyServiceV4;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class AlertCfgControllerV4App {

	private static final Logger log = LogManager.getLogger(AlertCfgControllerV4App.class);

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	ICompanyServiceV4 companyServicev4;

	@Autowired
	@Lazy
	IAlertCfgServiceV4 alertCfgServiceV4;

	@Autowired
	@Lazy
	IAlertCfgService alertCfgService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Value("#{${notifyfreq_info_url}}")
	private Map<String,String> notifyfreq_info_url;

	@Autowired
	@Lazy
	IAdvertisementService advService;
	
	@Autowired
	IAsyncService async;

	@Value("${gps_info}")
	private String gps_info = "NA";

	@Autowired
	private Helper _helper;
	
	@RequestMapping(value = "v5.0/getgeofence/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGeofenceV5(@RequestParam("assetid") long assetid,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering getAlertCfgV4 : " + autho);
		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key : " + e.getLocalizedMessage());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			if (usr != null) {
				String reqVer = "v5";
				int versionNum = Integer.parseInt(app_ver.replaceAll("\\.", ""));
				if (versionNum <= 781) {
					reqVer = "v4";
				}

				JGeofence geofence = alertCfgServiceV4.getGeofenceDetails(assetid, reqVer);
				if (geofence != null) {

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("geofence", geofence);
				} else {
					response.put("Status", 0);
					response.put("Msg", "unable to get fence details");
				}

			}
		} catch (Exception e) {
			log.error("Exception : getgeofence : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to get geofence Configuration");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "v5.0/alertcfgV5/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertCfgV5(@RequestParam("assetid") String assetid,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		try {
			String autho = header.getFirst("auth");
			log.info("Entering getAlertCfgV4 : " + autho);
			Map<String, String> map = null;
			try {
				map = userServiceV4.getUserId_cmpIdByAuth(autho);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key : " + e.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (!map.isEmpty()) {
				long userId = Long.valueOf(map.get("user_id"));

				UserV4 user = userServiceV4.verifyAuthV3("id", map.get("user_id"));

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}
				
				String country = user.getCountry().toUpperCase();
				if(country.equalsIgnoreCase("US")|| country.equalsIgnoreCase("NA")
						|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")
						|| country.isEmpty() || country == null) {
					country = "US";
				}

				boolean restrict_alert = true;

				if (user.getPlan_ver().equalsIgnoreCase("V1"))
					restrict_alert = false;

				response.put("restrict_alert", restrict_alert);

				String tempunit = map.get("tempunit");

				boolean geofence_enable = false;

				if (map.get("geofence_enable").equalsIgnoreCase("1"))
					geofence_enable = true;

				int temp_range = 1; // for celsius

				if (tempunit.equalsIgnoreCase("F"))
					temp_range = 2;
				long asset_id = 0;

				if (!assetid.isEmpty())
					asset_id = Long.parseLong(assetid);
				List<AlertCfgV4> alertcfgs = alertCfgServiceV4.getAlertCfgV4(userId, tempunit, asset_id,
						geofence_enable);

				int powerloss_min = 30;

				List<JGatewayConfig> gatewayConfigs = gatewayService.getJGatewayConfig(assetid, userId, tempunit);
				response.put("Status", 1);
				response.put("Msg", "Success");
				
				response.put("gps_info", gps_info);
				response.put("temp_range", temp_range);
				response.put("alertcfg", alertcfgs);
				response.put("gatewayConfig", gatewayConfigs);
				response.put("powerloss_min", powerloss_min);
				response.put("powerloss_msg", "Power Loss Alert is currently turned Off. Do you want to turn it On?");
				response.put("powerback_msg", "Power Recovery Alert is currently turned Off. Do you want to turn it On?");
				response.put("aqi_msg","Get alerts when the AQI crosses pet's safe levels.");
				response.put("co2_msg","Get alerts when CO2 levels exceed pet's safe limits.");

				response.put("notifyfreq_info",
						"<p style='text-align:justify; color:white;'>You'll receive alerts when the temperature exceeds "
						+ "set limits, with 60-minute reminders until it returns to range. This interval can be adjusted.<br><br><a href="
								+ notifyfreq_info_url.get(country) + " style='color:#4ACAFF;'>Click Here!</a> For more details</p>");
				response.put("powerloss_info",
						"<p style='text-align:justify; color:white;'>You'll receive alerts when power is lost, "
						+ "with reminders every 60 minutes until restored. This interval can be adjusted.<br><br><a href="
								+ notifyfreq_info_url.get(country) + " style='color:#4ACAFF;'>Click Here!</a> For more details</p>");

				String notifyfreq_img = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/waggle_freqalertcontent_2.png";
				String notifyfreq_img_light = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/waggle_freqalertcontent_light.png";
				String notifyfreq_img_dark = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/waggle_freqalertcontent_dark.png";
				AppImage notifyfreqObj = advService.getAppImages("common", "notify-frequency");
				if (notifyfreqObj != null) {
					notifyfreq_img = notifyfreqObj.getImg_path();
					notifyfreq_img_light = notifyfreqObj.getImg_path_flutter_light();
					notifyfreq_img_dark = notifyfreqObj.getImg_path_flutter_dark();
				}
				response.put("notifyfreq_img", notifyfreq_img);
				response.put("notifyfreq_img_flutter_light", notifyfreq_img_light);
				response.put("notifyfreq_img_flutter_dark", notifyfreq_img_dark);
				response.put("notifyfreq_content", "Waggle alerts you instantly once the temperature exceeds set limits. "
						+ "Once an alert is sent, you also get reminders every 60 minutes by default until it goes back "
						+ "to the set range. This settings allows you to set a different value.");
				response.put("notifyfreq_url", notifyfreq_info_url.get(country));

				String powerloss_img = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/waggle_powerlosscontent_1.png";
				String powerloss_img_light = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/waggle_powerlosscontent_light.png";
				String powerloss_img_dark = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/waggle_powerlosscontent_dark.png";
				AppImage powerlossObj = advService.getAppImages("common", "powerloss-msg");
				if (powerlossObj != null) {
					powerloss_img = powerlossObj.getImg_path();
					powerloss_img_light = powerlossObj.getImg_path_flutter_light();
					powerloss_img_dark = powerlossObj.getImg_path_flutter_dark();
				}
				response.put("powerloss_img", powerloss_img);
				response.put("powerloss_img_flutter_light", powerloss_img_light);
				response.put("powerloss_img_flutter_dark", powerloss_img_dark);
				response.put("powerloss_content", "Waggle alerts you instantly once the RV/Car/Home loses power. "
						+ "Once an alert is sent, you also get reminders every 60 minutes by default until the power is "
						+ "resumed. This settings allows you to set a different value.");
				response.put("powerloss_url", notifyfreq_info_url.get(country));
					
				String celsius_content = "<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><div style=\"padding: 2px;\"><p style=\"color: #000;font-size:14px;font-family: 'Montserrat';\">Temperature calibration ensures precise readings by correcting small variations. Waggle Pet monitor's accuracy is within +/- 1 Deg C. Please use the slider below to calibrate it. For most cases, we recommend you do not change it from the default value of 0.</p></div>";
				String celsius_content_flutter_light = "<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><div style=\"padding: 2px;\"><p style=\"color: #000;font-size:14px;font-family: 'Montserrat';\">Temperature calibration ensures precise readings by correcting small variations. Waggle Pet monitor's accuracy is within +/- 1 Deg C. Please use the slider below to calibrate it. For most cases, we recommend you do not change it from the default value of 0.</p></div>";
				String celsius_content_flutter_dark = "<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><div style=\"padding: 2px;\"><p style=\"color: #fff;font-size:14px;font-family: 'Montserrat';\">Temperature calibration ensures precise readings by correcting small variations. Waggle Pet monitor's accuracy is within +/- 1 Deg C. Please use the slider below to calibrate it. For most cases, we recommend you do not change it from the default value of 0.</p></div>";
				String drag_left_c = "<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><div style=\"padding: 2px;\"><p style=\"color: #4a4a4a;font-size:13px;font-family: 'Montserrat';\"><b>Slide left</b> to adjust negative values</p></div>";
				String drag_left_c_flutter_light = "<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><div style=\"padding: 2px;\"><p style=\"color: #000;font-size:13px;font-family: 'Montserrat';\"><b>Slide left</b> to adjust negative values</p></div>";
				String drag_left_c_flutter_dark = "<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><div style=\"padding: 2px;\"><p style=\"color: #fff;font-size:13px;font-family: 'Montserrat';\"><b>Slide left</b> to adjust negative values</p></div>";
				String drag_right_c = "<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><div style=\"padding: 2px;\"><p style=\"color: #4a4a4a;font-size:13px;font-family: 'Montserrat';\"><b>Slide right</b> to adjust positive values</p></div>";
				String drag_right_c_flutter_light = "<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><div style=\"padding: 2px;\"><p style=\"color: #000;font-size:13px;font-family: 'Montserrat';\"><b>Slide right</b> to adjust positive values</p></div>";
				String drag_right_c_flutter_dark = "<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><div style=\"padding: 2px;\"><p style=\"color: #fff;font-size:13px;font-family: 'Montserrat';\"><b>Slide right</b> to adjust positive values</p></div>";
				HashMap<Object, Object> temp_calib_celsius = new HashMap<Object, Object>();
				temp_calib_celsius.put("min_temp_calib_c", -3);
				temp_calib_celsius.put("max_temp_calib_c", 3);
				temp_calib_celsius.put("slide_range_c", 1);
				temp_calib_celsius.put("drag_left_c", drag_left_c);
				temp_calib_celsius.put("drag_left_c_flutter_light", drag_left_c_flutter_light);
				temp_calib_celsius.put("drag_left_c_flutter_dark", drag_left_c_flutter_dark);
				temp_calib_celsius.put("drag_right_c", drag_right_c);
				temp_calib_celsius.put("drag_right_c_flutter_light", drag_right_c_flutter_light);
				temp_calib_celsius.put("drag_right_c_flutter_dark", drag_right_c_flutter_dark);
				temp_calib_celsius.put("celsius_content", celsius_content);
				temp_calib_celsius.put("celsius_content_flutter_light", celsius_content_flutter_light);
				temp_calib_celsius.put("celsius_content_flutter_dark", celsius_content_flutter_dark);
				
				String fahrenheit_content = "<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><div style=\"padding: 2px;\"><p style=\"color: #000;font-size:14px;font-family: 'Montserrat';\">Temperature calibration ensures precise readings by correcting small variations. Waggle Pet monitor's accuracy is within +/- 2 Deg F. Please use the slider below to calibrate it. For most cases, we recommend you do not change it from the default value of 0.</p></div>";
				String fahrenheit_content_flutter_light = "<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><div style=\"padding: 2px;\"><p style=\"color: #000;font-size:14px;font-family: 'Montserrat';\">Temperature calibration ensures precise readings by correcting small variations. Waggle Pet monitor's accuracy is within +/- 2 Deg F. Please use the slider below to calibrate it. For most cases, we recommend you do not change it from the default value of 0.</p></div>";
				String fahrenheit_content_flutter_dark = "<link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'><div style=\"padding: 2px;\"><p style=\"color: #fff;font-size:14px;font-family: 'Montserrat';\">Temperature calibration ensures precise readings by correcting small variations. Waggle Pet monitor's accuracy is within +/- 2 Deg F. Please use the slider below to calibrate it. For most cases, we recommend you do not change it from the default value of 0.</p></div>";
				HashMap<Object, Object> temp_calib_fahrenheit = new HashMap<Object, Object>();
				temp_calib_fahrenheit.put("min_temp_calib_f", -6);
				temp_calib_fahrenheit.put("max_temp_calib_f", 6);
				temp_calib_fahrenheit.put("slide_range_f", 2);
				temp_calib_fahrenheit.put("drag_left_f", drag_left_c);
				temp_calib_fahrenheit.put("drag_right_f", drag_right_c);
				temp_calib_fahrenheit.put("fahrenheit_content", fahrenheit_content);
				temp_calib_fahrenheit.put("fahrenheit_content_flutter_light", fahrenheit_content_flutter_light);
				temp_calib_fahrenheit.put("fahrenheit_content_flutter_dark", fahrenheit_content_flutter_dark);
				
				response.put("temp_calib_celsius", temp_calib_celsius);
				response.put("temp_calib_fahrenheit", temp_calib_fahrenheit);
				
				HashMap<Object, Object> plpb_frequency = new HashMap<Object, Object>();
				
				plpb_frequency.put("min_plpb_frequency", 30);
				plpb_frequency.put("max_plpb_frequency", 300);
				plpb_frequency.put("slider_increment", 30);
//				plpb_frequency.put("plpb_frequency_info", "<link href='https://fonts.googleapis.com/css?family=Montserra)t' rel='stylesheet'><div style=\"margin-bottom: 0px;text-align:center;\"><p style=\"color: #000;font-size:13px;margin-bottom: 0px;font-family: 'Montserrat';\"><b>This sets the time delay after the power loss for the monitor to send the alert.</b> If your RV power is restored within this duration, the monitor will not send an alert. </p></div>");
				plpb_frequency.put("plpb_frequency_info", "<b>This sets the time delay after the power loss for the monitor to send the alert.</b> If your RV power is restored within this duration, the monitor will not send an alert.");
				plpb_frequency.put("plpb_frequency_info_new", "The 'RV Power Loss Threshold' setting decides how long the monitor waits before sending a power loss alert. If your RV power is restored within this duration, the monitor will not send an alert.");
				plpb_frequency.put("plpb_frequency_title", "RV Power Loss Alert Threshold");
				plpb_frequency.put("plpb_frequency_desc", "NA");
				
				response.put("plpb_frequency", plpb_frequency);

			} else {
				response.put("Status", 0);
				response.put("Msg", "User Details not found");
			}
		} catch (Exception e) {
			log.error("Exception : getAlertCfgV4 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to get Alert Configuration");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// Kalai
	@RequestMapping(value = "v5.0/alertcfg/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateAlertcfgV5(@RequestBody JAlertCfgV4 jalertCfg,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		String autho = header.getFirst("auth");
		log.info("Entering updateAlertcfg : " + autho);
		JResponse response = new JResponse();
		response.put("Msg_content", "");

		try {
			UserV4 user = null;

			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (Exception e) {
				log.error("verifyAuthKey : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Please try after some time!");
				response.put("Error", e.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			String country = user.getCountry().toUpperCase();
			if(country.equalsIgnoreCase("US")|| country.equalsIgnoreCase("NA")
					|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")
					|| country.isEmpty() || country == null) {
				country = "US";
			}

			if (user != null) {
				int result = 0;
				String updatefor = jalertCfg.getUpdatefor();
				log.info("updatefor: " + updatefor);
				boolean checkAsset = false;
				AlertCfg alertcfg = null;
				
				if(!updatefor.equalsIgnoreCase("allalert")) {
					String[] assetCfgLst = jalertCfg.getAlertcfgids().split(",");
	
					alertcfg = alertCfgServiceV4.getAlertCfg(Long.parseLong(assetCfgLst[0]));
					
					Set<Asset> listassets = alertcfg.getAssets();
	
					for (Asset ast : listassets) {
						if (ast.getId() == jalertCfg.getAssetid()) {
							checkAsset = true;
							break;
						}
					}
				}else {
					checkAsset=true;
				}
				
				if (!checkAsset) {
					response.put("Status", 0);
					response.put("Msg", "Please try after some time!");
					response.put("Error", "Asset configuration not matched");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				if(!updatefor.equalsIgnoreCase("allalert") && !updatefor.equalsIgnoreCase("parksafe")) {
					if (!jalertCfg.getAlerttypeids().contains(String.valueOf(alertcfg.getAlerttype().getId()))) {
						response.put("Status", 0);
						response.put("Msg", "Please try after some time!");
						response.put("Error", "Alert type not matched");
						response.put("Return Time", System.currentTimeMillis());
						return response;
					}
				}
				/*
				 * Email = Email updated successfully Mobile Number = Mobile number updated
				 * successfully Minimum temperature = Minimum temperature updated successfully
				 * Maximum temperature = Maximum temperature updated successfully Battery =
				 * Battery updated successfully Alert frequency = Alert frequency updated
				 * successfully Maximum Humidity = Maximum Humidity updated successfully Minimum
				 * Humidity = Minimum Humidity updated successfully Enable all Alert = Alert
				 * updated successfully Email Alert = Alert updated successfully Mobile Number
				 * alert = Alert updated successfully
				 */ String msg = "Success";
				 if (updatefor.equalsIgnoreCase("allalert")) {
						result = alertCfgServiceV4.enableAlldisablealertcfg(jalertCfg.getAssetid(),  jalertCfg.isEnable());
						
						if (jalertCfg.isEnable()) {
							msg = "Alert enabled successfully";
						}else
							msg = "Alert disabled successfully";

				}else if (updatefor.equalsIgnoreCase("enabledisable")) {
					result = alertCfgServiceV4.enabledisablealertcfg(jalertCfg.getAlertcfgids(), jalertCfg.isEnable());

					if (jalertCfg.getAssetid() > 0) {
						JGatewaySensorType gatewayDet = gatewayService
								.getSensorDetailByGatewayId(jalertCfg.getAssetid());
						if (jalertCfg.isEnable()) {
							if (gatewayDet != null && gatewayDet.getMonitorId() == 9) {
								if(jalertCfg.getAlerttypeids().equals("29") || jalertCfg.getAlerttypeids().equals("30") || jalertCfg.getAlerttypeids().equals("31")) {
									msg = gatewayDet.getSensorName() + " alert enabled successfully";
								}else if(jalertCfg.getAlerttypeids().equals("11")) {
									msg = "Network alert enabled successfully";
								}else if(jalertCfg.getAlerttypeids().equals("2")) {
									msg = "Battery alert enabled successfully";
								}else {
									msg = "Alert enabled successfully";
								}
							} else {
								msg = "Alert enabled successfully";
							}

						} else {
							if (gatewayDet != null && gatewayDet.getMonitorId() == 9) {
								if(jalertCfg.getAlerttypeids().equals("29") || jalertCfg.getAlerttypeids().equals("30") || jalertCfg.getAlerttypeids().equals("31")) {
									msg = gatewayDet.getSensorName() + " alert disabled successfully";
								}else if(jalertCfg.getAlerttypeids().equals("11")) {
									msg = "Network alert disabled successfully";
								}else if(jalertCfg.getAlerttypeids().equals("2")) {
									msg = "Battery alert disabled successfully";
								}else {
									msg = "Alert disabled successfully";
								}
							} else {
								msg = "Alert disabled successfully";
							}
						}
					} else {
						if (jalertCfg.isEnable()) {

							msg = "Alert enabled successfully";

						} else {

							msg = "Alert disabled successfully";

						}
					}

				} else if (updatefor.equalsIgnoreCase("updateemailphone")) {
					CompanyConfigResponse cmp_cfg = companyServicev4.getCompanyConfigAndCompany(user.getCmpId());

					long mobile_count = cmp_cfg.getMobileNos();
					long email_count = cmp_cfg.getEmailIds();

					long entered_mobileNos = jalertCfg.getMobilenos().split(",").length;
					long entered_emailIds = jalertCfg.getEmailids().split(",").length;

					if ((mobile_count >= entered_mobileNos && email_count >= entered_emailIds)
							|| cmp_cfg.getThrottsettings_id() == 5) {
						String phnos = jalertCfg.getMobilenos().replaceAll("\\s", "");

						String mailids = jalertCfg.getEmailids().replaceAll("\\s", "");

						result = alertCfgServiceV4.updateEmailPhone(jalertCfg.getAlertcfgids(), phnos, mailids);
						msg = "Phone number/Email updated successfully";

						if (result > 0) {
							log.info("update Phno/mail: " + phnos + " : " + mailids);

							response.put("Status", 1);
							response.put("Msg", msg);
						} else {
							response.put("Status", 0);
							response.put("Msg", "Please try after some time!");
							response.put("Error", "Alert cfgs not updated");
						}
					} else {
						response.put("Status", 0);
						response.put("Msg", "You are allowed to enter " + mobile_count + " mobile numbers and "
								+ email_count + " email address");
					}
				} else if (updatefor.equalsIgnoreCase("updatenotifyfreq")) {
					result = alertCfgServiceV4.updateNotify(jalertCfg.getAlertcfgids(), "1", jalertCfg.getNotifyfreq());
					msg = "Temp Alert frequency updated successfully";
				} else if (updatefor.equalsIgnoreCase("powerlossnotifyfreq")) {
					result = alertCfgServiceV4.updateNotify(jalertCfg.getAlertcfgids(), "3", jalertCfg.getNotifyfreq());
					msg = "Power Loss Alert frequency updated successfully";
				} else if (updatefor.equalsIgnoreCase("minmax")) {
					result = alertCfgServiceV4.updateAlertCfg(Long.parseLong(jalertCfg.getAlertcfgids()),
							Long.parseLong(jalertCfg.getAlerttypeids()), jalertCfg.getMinval(), jalertCfg.getMaxval(),
							jalertCfg.getAssetid(), user.getCmpId(),"updatealertcfg");
					msg = "Alert value updated successfully";
				} else if (updatefor.equalsIgnoreCase("notificationtype")) {
					if(jalertCfg.getNotificationtype().length() == 3)
						jalertCfg.setNotificationtype(jalertCfg.getNotificationtype()+"0");
					result = alertCfgServiceV4.updateNotificationType(jalertCfg.getAlertcfgids(),
							jalertCfg.getNotificationtype());
					msg = "Alert updated successfully";
				} else if (updatefor.equalsIgnoreCase("geofence")) {
					int versionNum = Integer.parseInt(app_ver.replaceAll("\\.", ""));
					if (versionNum > 781) {
						if (jalertCfg.getRadius() == 0) {
							int alertcfgId = Integer.parseInt(jalertCfg.getAlertcfgids());
							float radius = alertCfgServiceV4.getGeofenceDetails(alertcfgId);
							jalertCfg.setRadius(radius);
						}
					}
					response = alertCfgServiceV4.updateGeofenceDetails(jalertCfg,false);
					result = (int)response.get("Status");
					// while updating radius ,based on distance fence state will be update
//					if(!jalertCfg.isRecenter()) {
//						boolean res2 = alertCfgServiceV4.findGeofenceState(Long.parseLong(jalertCfg.getAlertcfgids()), jalertCfg.getAssetid(), jalertCfg.getLat(),
//										jalertCfg.getLon(), jalertCfg.getRadius());
//					}
					msg = "Geofence updated successfully";
				}else if (updatefor.equalsIgnoreCase("parksafe")) {
					jalertCfg.setAlerttypeids("4");
					jalertCfg.setRadius(500); // 500 meter
					response = alertCfgServiceV4.updateGeofenceDetails(jalertCfg,true);
					result = (int)response.get("Status");
					if(jalertCfg.isEnable())
						msg = "Park Safe enabled successfully";
					else
						msg = "Park Safe disabled successfully";
				}  else if (updatefor.equalsIgnoreCase("updatereminder")) {
					String cmdValue = "alertinterval=" + jalertCfg.getNotifyfreq();
					if(cmdValue != null) {
						Gateway gateway = gatewayService.getGatewayByid(jalertCfg.getAssetid());
						async.insertDynamicCmd(gateway, cmdValue, 1, "notsent");

					}
					result = alertCfgServiceV4.updateNotify(jalertCfg.getAlertcfgids(), jalertCfg.getAlerttypeids(), jalertCfg.getNotifyfreq());
					msg = "Reminder Alert frequency updated successfully";
				}

				if (result > 0 && updatefor.equalsIgnoreCase("updatenotifyfreq")) {
					response.put("Status", 1);
					response.put("Msg", msg);
					response.put("Msg_content",
							"<p style='text-align:left; color:white;'>You'll receive alerts when the temperature "
							+ "exceeds set limits, with 60-minute reminders until it returns to range. "
							+ "This interval can be adjusted.<br><br><a href="
							+ notifyfreq_info_url.get(country) + " style='color:#4ACAFF;'>Click Here!</a> For more details</p>");

					String notifyfreq_img = "NA";
					AppImage notifyfreqObj = advService.getAppImages("common", "notify-frequency");
					if (notifyfreqObj != null) {
						notifyfreq_img = notifyfreqObj.getImg_path();
					}
					response.put("notifyfreq_img", notifyfreq_img);
					response.put("notifyfreq_url", notifyfreq_info_url.get(country));
				} else if (result > 0 && updatefor.equalsIgnoreCase("powerlossnotifyfreq")) {
					response.put("Status", 1);
					response.put("Msg", msg);
					response.put("Msg_content",
							"<p style='text-align:justify; color:white;'>You'll receive alerts when power is lost, "
							+ "with reminders every 60 minutes until restored. This interval can be adjusted.<br><br><a href="
							+ notifyfreq_info_url.get(country) + " style='color:#4ACAFF;'>Click Here!</a> For more details</p>");

					String powerloss_img = "NA";
					AppImage powerlossObj = advService.getAppImages("common", "powerloss-msg");
					if (powerlossObj != null) {
						powerloss_img = powerlossObj.getImg_path();
					}
					response.put("powerloss_img", powerloss_img);
					response.put("powerloss_url", notifyfreq_info_url.get(country));
				} else if (result > 0) {
					response.put("Status", 1);
					response.put("Msg", msg);
				} else {
					response.put("Status", 0);
					response.put("Error", "Alert config updation failed");
					response.put("Msg", "Please try after some time!");
				}

				log.info("Alertcfg update: " + msg);
			}
		} catch (Exception e) {
			log.error("Exception : updateAlertcfg : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Alert Config updation failed");
			response.put("Msg_content", "");

			response.put("Error", e.getLocalizedMessage());
		}
		log.info("Exit updateAlertcfg");

		response.put("Return Time", System.currentTimeMillis());
		return response;

	}
}
