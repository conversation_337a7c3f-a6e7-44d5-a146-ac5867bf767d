package com.nimble.irisservices.appcontroller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JReminderDetails;
import com.nimble.irisservices.dto.JReqReminderDetails;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.ReminderRepeat;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.job.ReminderJob;
import com.nimble.irisservices.quartz.JobService;
import com.nimble.irisservices.service.IReminderService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class RemainderControllerApp {

	private static final Logger log = LogManager.getLogger(RemainderControllerApp.class);

	@Autowired
	@Lazy
	JobService jobService;

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	Helper _helper = new Helper();

	@Autowired
	IReminderService reminderservice;

	@Value("${reminderLimit}")
	private int reminderLimit;

	@RequestMapping(value = "v4.0/setreminder/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse setreminder(@RequestBody JReqReminderDetails jreminder,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info("ReminderController :: schedule Reminder.");
		String autho = header.getFirst("auth");
		JResponse response = new JResponse();
		Class<? extends QuartzJobBean> jobClass = ReminderJob.class;
		boolean status = false;

		String reminderName = jreminder.getRemindername();
		String timezone = jreminder.getTimezone();
		Date ScheduleTime = jreminder.getScheduletime();
		long repeatedId = jreminder.getRepeatedid();
		String remainderMsg = jreminder.getRemindermsg();

		if (repeatedId == 0)
			repeatedId = 1;

		timezone = timezone.replaceAll("\\s+", "").trim();
		if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
			timezone = "+" + timezone;
		log.info(timezone);

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String utcDate = "";

		if (reminderName == null || reminderName.trim().equals("") || reminderName.length() > 40) {
			if (reminderName.length() > 40) {
				log.error("jobname");
				response.put("Status", 0);
				response.put("Msg", "Reminder Title too long");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			log.info("jobname");
			response.put("Status", 0);
			response.put("Msg", "Invalid Reminder Title");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		if (remainderMsg == null || remainderMsg.length() >= 255) {
			log.info("remainderMsg");
			response.put("Status", 0);
			response.put("Msg", "Reminder Message too long");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			try {
				utcDate = convertdate(formatter.format(ScheduleTime), timezone);
				ScheduleTime = formatter.parse(utcDate);

				if ((System.currentTimeMillis() + 240000) > ScheduleTime.getTime()) {
					log.error("Its Not Schedule Future dateTime");
					response.put("Status", 0);
					response.put("Msg", "Reminder cannot be set under 5 minutes");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			} catch (ParseException e) {
				log.error("Exception in set reminder : "+e.getLocalizedMessage());
			}

			String displayname = reminderName;

			ArrayList<JReminderDetails> joblist = reminderservice.getReminderDetails(usr.getId(), 0, null, "UTC");

			if (joblist.size() >= reminderLimit) {
				log.error("Schedule Limit Completed");
				response.put("Status", 0);
				response.put("Msg", "You've reached the maximum reminder limit");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			reminderName = reminderName + "_" + System.currentTimeMillis();

			ReminderRepeat repeatinfo = reminderservice.getReminderRepeatTypeList(repeatedId).get(0);

			if (repeatinfo.getId() == 1) {
				// Single Trigger
				status = jobService.scheduleOneTimeJob(reminderName, jobClass, ScheduleTime, usr.getId() + "");

				if (!status) {
					log.error("Schedule not created");
					response.put("Status", 0);
					response.put("Msg", "Reminder not created");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				status = reminderservice.saveReminderDetails(usr.getId(), timezone, reminderName, displayname, utcDate,
						repeatedId, repeatinfo.getRepeatname(), remainderMsg, "started", 1);

			} else {

				status = jobService.scheduleRepeatJob(reminderName, jobClass, ScheduleTime, usr.getId() + "",
						repeatinfo.getRepeat_time(), repeatinfo.getInterval());

				if (!status) {
					log.error("Schedule Not created");
					response.put("Status", 0);
					response.put("Msg", "Reminder not created");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				status = reminderservice.saveReminderDetails(usr.getId(), timezone, reminderName, displayname, utcDate,
						repeatedId, repeatinfo.getRepeatname(), remainderMsg, "started", 1);

			}

			if (status) {
				log.info("Schedule created");
				response.put("Status", 1);
				response.put("Msg", "Reminder added successfully");
			} else {
				log.error("Schedule Not created");
				response.put("Status", 0);
				response.put("Msg", "Reminder not created");
			}

		} catch (Exception e) {
			log.error("Error While create reminder "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "An error occurred. Please try again later");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;

	}

	@RequestMapping(value = "v4.0/updatereminder/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updatereminder(@RequestParam("timechanges") boolean timeChanges,
			@RequestBody JReqReminderDetails jreminder,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info("JobController.update()");
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		Class<? extends QuartzJobBean> jobClass = ReminderJob.class;
		boolean status = true;

		long reminderid = jreminder.getReminderid();
		String reminderName = null;
		String newReminderName = jreminder.getRemindername();
		String timezone = jreminder.getTimezone();
		Date ScheduleTime = jreminder.getScheduletime();
		long repeatedId = jreminder.getRepeatedid();
		String reminderMsg = jreminder.getRemindermsg();

		if (repeatedId == 0)
			repeatedId = 1;

		if (newReminderName == null || newReminderName.trim().equals("") || newReminderName.length() > 40) {
			if (newReminderName.length() > 40) {
				log.error("jobname");
				response.put("Status", 0);
				response.put("Msg", "Reminder Title too long");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			log.error("jobname");
			response.put("Status", 0);
			response.put("Msg", "Invalid Reminder Name");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		if (reminderMsg == null || reminderMsg.length() >= 255) {
			response.put("Status", 0);
			response.put("Msg", "Reminder Message too long");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		timezone = timezone.replaceAll("\\s+", "").trim();
		if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
			timezone = "+" + timezone;

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String utcDate = "";

		try {
			utcDate = convertdate(formatter.format(ScheduleTime), timezone);
			ScheduleTime = formatter.parse(utcDate);

			if (timeChanges && (System.currentTimeMillis() + 240000) > ScheduleTime.getTime()) {
				log.error("Its Not Schedule Future dateTime");
				response.put("Status", 0);
				response.put("Msg", "Reminder cannot be set to the past date/time.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		} catch (ParseException e) {
			log.error("Error While parse Datetime : "+e.getLocalizedMessage());
		}

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr == null) {
				log.error("invalid authentication key");
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			ArrayList<JReminderDetails> joblist = reminderservice.getReminderDetails(usr.getId(), reminderid, null,
					"UTC");

			reminderName = joblist.get(0).getJobName();

			ReminderRepeat repeatinfo = reminderservice.getReminderRepeatTypeList(repeatedId).get(0);

			if (jobService.isJobWithNamePresent(reminderName, usr.getId() + "")) {

				boolean isJobRunning = jobService.isJobRunning(reminderName, usr.getId() + "");

				if (!isJobRunning) {

					if (repeatinfo.getId() == 1) {
						// Single Trigger
						if (timeChanges)
							status = jobService.updateOneTimeJob(reminderName, ScheduleTime, usr.getId() + "");

						if (!status) {
							log.error("reminder not updated");
							response.put("Status", 0);
							response.put("Msg", "Reminder not updated");
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}

						status = reminderservice.updateReminderDetails(reminderid, usr.getId(), timezone, reminderName,
								newReminderName, utcDate, repeatedId, repeatinfo.getRepeatname(), reminderMsg,
								"started", 1);

					} else {

						if (timeChanges)
							status = jobService.updateRepeatJob(reminderName, ScheduleTime, usr.getId() + "",
									repeatinfo.getRepeat_time(), repeatinfo.getInterval());

						if (!status) {
							log.error("reminder not updated");
							response.put("Status", 0);
							response.put("Msg", "Reminder not updated");
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}

						status = reminderservice.updateReminderDetails(reminderid, usr.getId(), timezone, reminderName,
								newReminderName, utcDate, repeatedId, repeatinfo.getRepeatname(), reminderMsg,
								"started", 1);
					}

				}

			} else {
				log.error("JOB_DOESNT_EXIST");
				response.put("Status", 0);
				response.put("Msg", "Reminder doesn't exist.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (status) {
				log.error("reminderName  updated");
				response.put("Status", 1);
				response.put("Msg", "Reminder updated successfully");
			} else {
				log.error("reminderName not updated");
				response.put("Status", 0);
				response.put("Msg", "Reminder not updated");
			}

		} catch (Exception e) {
			log.error("Error While Update reminder : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "An error occurred. Please try again later");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;

	}

	private String convertdate(String date, String timezone) {
		log.info("Date : "+date);
		SimpleDateFormat sdfcurr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		sdfcurr.setTimeZone(TimeZone.getTimeZone("GMT" + timezone));
		SimpleDateFormat dfUtc = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		dfUtc.setTimeZone(TimeZone.getTimeZone("UTC"));
		try {
			return dfUtc.format(sdfcurr.parse(date));
		} catch (ParseException e) {
			log.error("Error While parse to utc date : " + e.getMessage());
			return null; // invalid input
		}
	}

	// get function

	// update function

	@RequestMapping(value = "v4.0/deletereminder/", method = RequestMethod.DELETE, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deletereminder(@RequestParam("reminderid") long reminderid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info("JobController.delete()");
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		Class<? extends QuartzJobBean> jobClass = null;
		boolean status = false;
		String reminderName = null;

		try {

			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr == null) {
				log.error("Invalid authentication key");
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			ArrayList<JReminderDetails> joblist = reminderservice.getReminderDetails(usr.getId(), reminderid, null,
					"UTC");

			reminderName = joblist.get(0).getJobName();

			if (jobService.isJobWithNamePresent(reminderName, usr.getId() + "")) {
				boolean isJobRunning = jobService.isJobRunning(reminderName, usr.getId() + "");

				if (!isJobRunning) {

					status = jobService.deleteJob(reminderName, usr.getId() + "");

					if (!status) {
						log.error("Reminder not deleted");
						response.put("Status", 0);
						response.put("Msg", "Reminder not deleted");
						response.put("Return Time", System.currentTimeMillis());
						return response;
					}

					status = reminderservice.disableReminderDetails(usr.getId() + "", reminderName, "Deleted", -1);
				}

			} else {
				log.error("JOB_DOESNT_EXIST");
				response.put("Status", 0);
				response.put("Msg", "Reminder doesn't exist.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (status) {
				log.info("Reminder deleted");
				response.put("Status", 1);
				response.put("Msg", "Reminder Deleted");
			} else {
				log.error("Reminder not deleted");
				response.put("Status", 0);
				response.put("Msg", "Reminder not deleted");
			}

		} catch (Exception e) {
			log.error("Error While Delete Reminder : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "An error occurred. Please try again later");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;

	}

	@RequestMapping(value = "v4.0/getreminder/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getReminderList(@RequestParam("limitdays") int limitDays,
			@RequestParam("selectedtab") int selectedTab,
			@RequestParam(value = "quartz", defaultValue = "", required = false) String quartz,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info("JobController.get()");
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		Class<? extends QuartzJobBean> jobClass = null;
		boolean status = false;
		int remindercount = Integer.MAX_VALUE;

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr == null) {
				log.error("Invalid authentication key");
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			List<Map<String, Object>> quartzlist = null;
			ArrayList<JReminderDetails> joblist = null;
			Map<String, List<JReminderDetails>> remainderviewList = null;
			ArrayList<ReminderRepeat> repeattypelist = null;

			LinkedList<HashMap<String, Object>> reminder_list = new LinkedList<HashMap<String, Object>>();

			try {

				if (quartz != null && !quartz.isEmpty())
					quartzlist = jobService.getAllJobs(usr.getId() + "", "");

				int viewcomplete = -1;

				if (selectedTab == 2)
					viewcomplete = 0;
				else if (selectedTab == 3)
					viewcomplete = 1;

				if (selectedTab == 0 || selectedTab == 2 || selectedTab == 3)
					remainderviewList = reminderservice.getremainderOverDuelist(usr.getId(), limitDays, viewcomplete);

				if (selectedTab == 0 || selectedTab == 2) {
					HashMap<String, Object> overDue = new HashMap<String, Object>();
					overDue.put("typeid", 2);
					overDue.put("type", "Overdue");
					overDue.put("data_list", remainderviewList.get("OverDue"));
					reminder_list.add(overDue);
				}

				if (selectedTab == 0 || selectedTab == 1) {
					joblist = reminderservice.getReminderDetails(usr.getId(), 0, null, null);
					HashMap<String, Object> upcoming = new HashMap<String, Object>();
					upcoming.put("typeid", 1);
					upcoming.put("type", "Upcoming");
					upcoming.put("data_list", joblist);
					reminder_list.add(upcoming);

					remindercount = joblist.size();

				}

				if (selectedTab == 0 || selectedTab == 3) {
					HashMap<String, Object> completed = new HashMap<String, Object>();
					completed.put("typeid", 3);
					completed.put("type", "Completed");
					completed.put("data_list", remainderviewList.get("ViewCompleteList"));
					reminder_list.add(completed);
				}

				repeattypelist = reminderservice.getReminderRepeatTypeList(0);

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("ReminderList", reminder_list);
				response.put("RepeatTypelist", repeattypelist);
				response.put("AlertMsg", "You've reached the maximum reminder limit");
				response.put("EnableToAdd", remindercount < reminderLimit);

				if (quartz != null && !quartz.isEmpty())
					response.put("quartz", quartzlist);
				response.put("Return Time", System.currentTimeMillis());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			} catch (Exception e) {
				log.error("Error while getreminder : " + e.getMessage());
				response.put("Status", 0);
				response.put("Msg", "An error occurred. Please try again later");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

		} catch (Exception e) {
			log.error("Error while getreminder : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "An error occurred. Please try again later");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

	}

	@RequestMapping(value = "v4.0/updatereminderviews/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateReminderViews(@RequestParam("Reminderviewid") long reminderviewid,
			@RequestParam("isdeleted") boolean isdeleted,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info("updateremainderviews  : reminderviewid:" + reminderviewid);
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		boolean status = false;

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			status = reminderservice.updateReminderViewDetails(reminderviewid, isdeleted);

			if (status) {
				log.info("reminderviews update");
				response.put("Status", 1);
				if (isdeleted)
					response.put("Msg", "Reminder Deleted");
				else
					response.put("Msg", "Reminder Completed");

			} else {
				log.error("reminderviews Not update");
				response.put("Status", 0);
				response.put("Msg", "An error occurred. Please try again later");
			}

		} catch (Exception e) {
			log.error("Error while updating Reminderviews " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "An error occurred. Please try again later");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;

	}

	@RequestMapping(value = "v4.0/skipcurrentreminder/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse skipcurrentreminder(@RequestParam("reminderid") long reminderid, Authentication authentication,
			@RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info("skipcurrentremainder : reminderid: " + reminderid);
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		Class<? extends QuartzJobBean> jobClass = null;
		boolean status = false;
		String reminderName = null;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr == null) {
				log.error("Invalid authentication key");
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			ArrayList<JReminderDetails> joblist = reminderservice.getReminderDetails(usr.getId(), reminderid, null,
					"UTC");
			JReminderDetails rem = joblist.get(0);

			reminderName = rem.getJobName();

			if (jobService.isJobWithNamePresent(reminderName, usr.getId() + "")) {
				boolean isJobRunning = jobService.isJobRunning(reminderName, usr.getId() + "");

				log.info("Reminder Currently running : " + isJobRunning);

				if (!isJobRunning) {

					if (rem.getRepeattype().toString() != null && !rem.getRepeattype().equals("NA")
							&& !rem.getRepeattype().equals("Never") && !rem.getRepeattype().isEmpty()) {
						Date nexttrigger = null;

						List<Map<String, Object>> quartzlist = jobService.getAllJobs(usr.getId() + "", reminderName);

						Date nextdate = (Date) quartzlist.get(0).get("nextFireTime");
						Date firstdate = (Date) quartzlist.get(0).get("scheduleTime");

						log.info("Reminder Curr date : " + nextdate);

						ReminderRepeat repeatt = reminderservice.getReminderRepeatTypeList(rem.getRepeatId()).get(0);

						nexttrigger = calculateNextTrigger(nextdate, repeatt.getRepeat_time(), repeatt.getInterval());
						log.info("Reminder Next trigger date : " + nexttrigger);

						status = jobService.updateRepeatJob(reminderName, nexttrigger, usr.getId() + "",
								repeatt.getRepeat_time(), repeatt.getInterval());
						log.info("Reminder Next trigger date convert to current trigger date Status : " + status);

						status = reminderservice.updateReminderDetails(rem.getId(), usr.getId(), rem.getTimezone(),
								reminderName, rem.getReminderName(), sdf.format(nexttrigger), repeatt.getId(),
								repeatt.getRepeatname(), rem.getReminderMsg(), "started", 1);

						log.info("Reminder updated to ReminderDetails : " + status);

						String viewkey = reminderservice.saveReminderViewDetails(usr.getId(), reminderName,
								sdf.parse(rem.getReminderDate()), 1, 1);
						log.info("save ReminderView Details : " + viewkey);

					} else {

						status = jobService.deleteJob(reminderName, usr.getId() + "");
						log.info("Reminder current trigger Deleted: " + status);

						String viewkey = reminderservice.saveReminderViewDetails(usr.getId(), reminderName,
								sdf.parse(rem.getReminderDate()), 1, 1);
						log.info("save ReminderView Details : " + viewkey);

						status = reminderservice.disableReminderDetails(usr.getId() + "", reminderName, "Completed", 0);
						log.info("Reminder updated to ReminderDetails : " + status);
					}

				}

			} else {
				log.error("JOB_DOESNT_EXIST ");
				response.put("Status", 0);
				response.put("Msg", "Reminder doesn't exist.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (status) {
				log.info("Reminder Skipcurrent job");
				response.put("Status", 1);
				response.put("Msg", "Reminder Completed");
			} else {
				log.error("Reminder not Skipcurrent job");
				response.put("Status", 0);
				response.put("Msg", "An error occurred. Please try again later");
			}

		} catch (Exception e) {
			log.error("Error While Skip current job " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "An error occurred. Please try again later");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;

	}

	private Date calculateNextTrigger(Date reminderDatetime, String repeat_time, int Interval) {
		// TODO Auto-generated method stub
		try {
			Calendar cal = Calendar.getInstance();
			cal.setTime(reminderDatetime);

			if (repeat_time.equalsIgnoreCase("hour"))
				cal.add(Calendar.HOUR, Interval);
			else if (repeat_time.equalsIgnoreCase("day"))
				cal.add(Calendar.DATE, Interval);
			else if (repeat_time.equalsIgnoreCase("week"))
				cal.add(Calendar.DATE, 7 * Interval);
			else if (repeat_time.equalsIgnoreCase("month"))
				cal.add(Calendar.MONTH, Interval);
			else if (repeat_time.equalsIgnoreCase("year"))
				cal.add(Calendar.YEAR, Interval);

			return cal.getTime();

		} catch (Exception e) {
			log.error("Erro While Updating calculate NextTrigger " + e.getMessage());
		}
		return null;
	}

}
