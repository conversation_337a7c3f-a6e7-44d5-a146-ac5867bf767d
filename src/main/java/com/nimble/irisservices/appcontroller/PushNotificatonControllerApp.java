package com.nimble.irisservices.appcontroller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.ocpsoft.prettytime.PrettyTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IPushNotificationService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class PushNotificatonControllerApp {

	@Autowired
	@Lazy
	IPushNotificationService iNotificationService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	Helper _helper = new Helper();

	PrettyTime prettyTime = new PrettyTime();

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	private static final Logger log = LogManager.getLogger(PushNotificatonControllerApp.class);

	@RequestMapping(value = "v5.0/deleteusernotification/{notificationId}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deleteUserNotificaitonsV5(@PathVariable String notificationId, Authentication authentication,
			@RequestHeader HttpHeaders header,@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "req_ver", defaultValue = "v1", required = false) String version) {
		JResponse response = new JResponse();
		try {
			String autho = header.getFirst("auth");
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			boolean status = false;
			if( version.equalsIgnoreCase("v1") ) {
				status = iNotificationService.deleteUserNotification(Long.toString(user.getId()), notificationId);
			} else {
				status = iNotificationService.deletePushNotificationStatus(notificationId);
			}
			if (status) {
				response.put("Status", 1);
				response.put("Msg", "Notification is deleted successfully.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Error in deleting the user notifications.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "Invalid authentication key");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception ex) {
			log.error(ex.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in deleting the user pushnotifications.");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}
	
	@RequestMapping(value = "v3.0/deleteusernotification/{notificationId}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deleteUserNotificaitons(@PathVariable String notificationId, Authentication authentication,
			@RequestHeader HttpHeaders header,@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		try {
			String autho = header.getFirst("auth");
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			boolean status = iNotificationService.deleteUserNotification(Long.toString(user.getId()), notificationId);
			if (status) {
				response.put("Status", 1);
				response.put("Msg", "Notification is deleted successfully.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Error in deleting the user notifications.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "Invalid authentication key");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception ex) {
			log.error(ex.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in deleting the user pushnotifications.");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}

}
