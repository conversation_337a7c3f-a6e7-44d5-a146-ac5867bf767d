package com.nimble.irisservices.appcontroller;

import java.sql.Timestamp;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.json.JSONObject;
import org.ocpsoft.prettytime.PrettyTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dto.BluetoothDeviceList;
import com.nimble.irisservices.dto.JGatewayUserDetails;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.WifiInfo;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IFurBitReportService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IWifiInfoService;
import com.nimble.irisservices.service.IWifiInfoServiceV4;

@Controller
@RequestMapping("/app")
public class WifiInfoControllerApp {

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IWifiInfoService wifiService;
	
	@Autowired
	IWifiInfoServiceV4 wifiInfoServiceV4;

	@Autowired
	IFurBitReportService iFurBitReportService;

	@Value("${lastrpt_max}")
	private String lastrpt_max;

	@Value("${lastwifirpt_max}")
	private String lastWifirptMax;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	Helper _helper = new Helper();

	PrettyTime prettyTime = new PrettyTime();

	private static final Logger log = LogManager.getLogger(WifiInfoControllerApp.class);

	@RequestMapping(value = "v3.0/getwifiinfolist/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getWifiInfoList(
			@RequestParam(value = "gatewayid", defaultValue = "", required = false) String gatewayId,
			@RequestParam("userid") String userId, Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		String autho = header.getFirst("auth");
		log.info("Entered :: getWifiList  :: " + autho);
		JResponse response = new JResponse();
		UserV4 user;
		long gatewayID = 0;
		try {
			if (!gatewayId.isEmpty()) {
				gatewayID = Long.valueOf(gatewayId);
			}
			long userID = Long.valueOf(userId);
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				if (user.getId() != userID) {
					response.put("Status", 0);
					response.put("Msg", "User id mismatch");
					log.info("User if for authey and user id parameter mismatch ");
					return response;
				}

			} catch (InvalidAuthoException e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				log.error("Invalid authkey : " + autho);
				return response;
			}
			List<BluetoothDeviceList> ble_device_list = wifiService.getWifiList(gatewayID, userID);

			if (ble_device_list != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("bledevicelist", ble_device_list);
				log.info("wifi info list found for the gateway id : " + gatewayId + " and user id : " + userId);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Could not get wifi info list");
				log.info("No wifi info list found for the gateway id : " + gatewayId + " and user id : " + userId);
			}
			log.info("Exit :: getWifiList ::");
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception occured ");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception occured at getWifiList  :", e.getLocalizedMessage());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v3.0/savewifiinfo/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveWifiInfo(@RequestBody WifiInfo wifi, Authentication authentication,
			@RequestHeader HttpHeaders header,@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering saveWifiInfo " + autho);
		try {
			String macid = wifi.getMeid().split("_")[1];
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JGatewayUserDetails gateways = gatewayService.getGatewayByMAC(macid, user.getId());
			boolean status = false;

			try {
				if (gateways != null) {
					String CurrentTime = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT,
							IrisservicesConstants.UTCFORMAT);
					Timestamp currTimeStamp = IrisservicesUtil.getDateTime_TS(CurrentTime);
					if (wifi.getGatewayid() != gateways.getGatewayid()) {
						response.put("Status", 0);
						response.put("Msg", "Gateway id or Macid mismatch");
						return response;
					}
					wifi.setUserid(gateways.getUserid());
					wifi.setGatewayid(gateways.getGatewayid());
					wifi.setUpdatedOn(currTimeStamp);

					wifi.setSsidCategory(wifi.getSsidCategory());
					wifi.setLat(wifi.getLat());
					wifi.setLon(wifi.getLon());
					wifi.setBleVersion(wifi.getBleVersion());
					wifi.setSsidName(wifi.getSsidName());
					wifi.setAddress(wifi.getAddress());
					wifi.setTimezone(wifi.getTimezone());

					wifi.setCreatedOn(currTimeStamp);

					status = wifiService.saveOrUpdateWifiInfo(wifi);

					if (status) {
						response.put("Status", 1);
						response.put("Msg", "Success");
					} else {
						response.put("Status", 0);
						response.put("Msg", "Could not save wifi info");
					}

					log.info("Exit :: savewifiinfo::::wificontroller::[{}]");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Macid not found");
				}
				response.put("Return Time", System.currentTimeMillis());
				return response;
			} catch (ConstraintViolationException ex) {
				response.put("Status", 0);
				response.put("Msg", "Wifiinfo already exist");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception : saveWifiInfo : " + ex.getLocalizedMessage());
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "wifiinfo creation failed");
				response.put("Error", e.getLocalizedMessage());
				log.error("Exception : saveWifiInfo : " + e.getLocalizedMessage());
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "wifiinfo creation failed");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : saveWifiInfo : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "v4.0/savewifiinfo", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveWifiInfo(@RequestBody WifiInfo wifi, @RequestParam("os") String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering saveWifiInfo v4: " + auth);
		try {
			
			String macid = wifi.getMeid().split("_")[1];
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			log.info("wifi Ssid -" + wifi.getSsid() + "  : wifi Password -" + wifi.getPassword());

			JGatewayUserDetails gateways = gatewayService.getGatewayByMAC(macid,wifi.getUserid());
			boolean status = false;

			try {
				if (gateways != null) {
					String CurrentTime = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT,
							IrisservicesConstants.UTCFORMAT);
					Timestamp currTimeStamp = IrisservicesUtil.getDateTime_TS(CurrentTime);
					if (wifi.getGatewayid() != gateways.getGatewayid()) {
						response.put("Status", 0);
						response.put("Msg", "Gateway id or meid mismatch");
						response.put("Return Time", System.currentTimeMillis());
						return response;
					}
					wifi.setUserid(gateways.getUserid());
					wifi.setUpdatedOn(currTimeStamp);
					wifi.setCreatedOn(currTimeStamp);

					status = wifiService.saveOrUpdateWifiInfo(wifi);

					if (status) {
						response.put("Status", 1);
						response.put("Msg", "Wifi Details saved successfully");
					} else {
						response.put("Status", 0);
						response.put("Msg", "Could not save wifi info");
					}

					log.info("Exit :: savewifiinfo::::wificontroller::[{}]");
				} else {
					response.put("Status", 0);
					response.put("Msg", "MEID not found");
				}

				response.put("Return Time", System.currentTimeMillis());
				return response;
			} catch (ConstraintViolationException ex) {
				response.put("Status", 0);
				response.put("Msg", "Wifiinfo already exist");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception : saveWifiInfo : " + ex.getLocalizedMessage());
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "wifiinfo creation failed");
				response.put("Error", e.getLocalizedMessage());
				log.error("Exception : saveWifiInfo : " + e.getLocalizedMessage());
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "wifiinfo creation failed");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : saveWifiInfo : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@RequestMapping(value = "v4.0/getwifiinfolist", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getWifiInfoList(
			@RequestParam(value = "gatewayid", defaultValue = "") String gatewayId,
			@RequestParam("os") String os,
			@RequestParam(value = "type") String type,
			@RequestParam(value = "app_ver", defaultValue = "") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {

		String auth = header.getFirst("auth");
		log.info("Entered :: getWifiList v4 : " + auth);
		JResponse response = new JResponse();
		UserV4 user = null;
		long gatewayID = 0;
		try {
			if (!gatewayId.isEmpty()) {
				gatewayID = Long.valueOf(gatewayId);
			}
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				log.error("Invalid authkey : " + auth);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			
			List<BluetoothDeviceList> ble_device_list = wifiInfoServiceV4.getWifiList(gatewayID, user.getId(), os);

			if (ble_device_list != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("bledevicelist", ble_device_list);
				log.info("wifi info list found for the gateway id : " + gatewayId + " and user id : " + user.getId());
			} else {
				response.put("Status", 0);
				response.put("Msg", "Could not get wifi info list");
				log.info("No wifi info list found for the gateway id : " + gatewayId + " and user id : " + user.getId());
			}
			log.info("Exit :: getWifiList ::");
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Unable to get wifi list ");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception occured at getWifiListv4  :", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		log.info("getwifiinfolist Response data : "+new JSONObject(response).toString());
		return response;
	}
	
	@RequestMapping(value = "v5.0/savewifiinfo", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveWifiInfoV5(@RequestBody WifiInfo wifi, @RequestParam("os") String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("Entering saveWifiInfo v4: " + auth);
		try {
			
			String macid = wifi.getMeid().split("_")[1];
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			log.info("wifi Ssid -" + wifi.getSsid() + "  : wifi Password -" + wifi.getPassword());

			JGatewayUserDetails gateways = gatewayService.getGatewayByMAC(macid,wifi.getUserid());
			boolean status = false;

			try {
				if (gateways != null) {
					String CurrentTime = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT,
							IrisservicesConstants.UTCFORMAT);
					Timestamp currTimeStamp = IrisservicesUtil.getDateTime_TS(CurrentTime);
					if (wifi.getGatewayid() != gateways.getGatewayid()) {
						response.put("Status", 0);
						response.put("Msg", "Gateway id or meid mismatch");
						response.put("Return Time", System.currentTimeMillis());
						return response;
					}
					wifi.setUserid(gateways.getUserid());
					wifi.setUpdatedOn(currTimeStamp);
					wifi.setCreatedOn(currTimeStamp);

					status = wifiService.saveOrUpdateWifiInfo(wifi);
					try {
					String timezone = wifi.getTimezone().replaceAll("\\s+", "");
						if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
							timezone = "+" + timezone;
						
						userServiceV4.updateUserTimezone(user.getId(), timezone);
							
							log.info("User timezone updated");
					}catch (Exception e) {
						log.error("Error occured at updateUserTimezone : " + e.getLocalizedMessage());
					}
					
					
					if (status) {
						response.put("Status", 1);
						response.put("Msg", "Wifi Details saved successfully");
					} else {
						response.put("Status", 0);
						response.put("Msg", "Could not save wifi info");
					}

					log.info("Exit :: savewifiinfo::::wificontroller::[{}]");
				} else {
					response.put("Status", 0);
					response.put("Msg", "MEID not found");
				}

				response.put("Return Time", System.currentTimeMillis());
				return response;
			} catch (ConstraintViolationException ex) {
				response.put("Status", 0);
				response.put("Msg", "Wifiinfo already exist");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception : saveWifiInfo : " + ex.getLocalizedMessage());
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "wifiinfo creation failed");
				response.put("Error", e.getLocalizedMessage());
				log.error("Exception : saveWifiInfo : " + e.getLocalizedMessage());
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "wifiinfo creation failed");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : saveWifiInfo : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

}
