package com.nimble.irisservices.appcontroller;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.nimble.irisservices.Util.SecretManagerService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AlertWC;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.PushNotificationStatus;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.pojo.SendNotifications;
import com.nimble.irisservices.service.IAlertServiceV4;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IFileStorageService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IPushNotificationServiceV4;
import com.nimble.irisservices.service.IReminderService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class PushNotificatonControllerV4App {
	private static final Logger log = LogManager.getLogger(PushNotificatonControllerV4App.class);

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IPushNotificationServiceV4 notificationService;

	@Autowired
	IUserService userService;

	@Autowired
	IReminderService reminderservice;

	@Autowired
	IAlertServiceV4 alertService;
	
	@Autowired
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Autowired
	Helper _helper;
	
	@Autowired
	IFileStorageService fileStorageService;

	@Autowired
	SecretManagerService secretManagerService;

	@Value("${aws_s3_secret_name}")
	private String S3_SECRET_NAME;

	@Value("${motion_detection_push_notify_id}")
	private int motion_detection_push_notify_id;
	
	@Value("${motion.S3.bucketname}")
	private String s3_motion_bucketname;

	private String s3_motion_accessKey;

	private String s3_motion_secretKey;
	
	@Value("#{'${video_alert_pushnotification_id}'.split(',')}")
	private List<String> video_alert_pushnotification_id;
	
	@Value("#{'${image_alert_pushnotification_id}'.split(',')}")
	private List<String> image_alert_pushnotification_id;

	
	@GetMapping("v5.0/motiondetectionimage")
	@ResponseBody
	public JResponse getMotionDetectionImage(@RequestParam("app_ver") String app_ver, 
			@RequestParam("os") String os,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam String meid,
			@RequestParam long gateway_id,
			@RequestParam long push_notification_status_id,
			@RequestParam(required = false, defaultValue = "0") long alert_id
			) {
		log.info("Entered into getMotionDetectionImage :: auth : "+ header.getFirst("auth") +" :: meid : "+meid+" :: gateway_id : "+ gateway_id+" :: push_notification_id : "+ push_notification_status_id+" :: alert_id : "+ alert_id);
		JResponse response = new JResponse();
		try {
			String autho = header.getFirst("auth");
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception : getNotificaitonCount : " + ex.getLocalizedMessage());
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			String img_url = "NA";
			String vid_url = "NA";
			
			
			if (alert_id == 0) {

				PushNotificationStatus pushNotificationStatus = notificationService
						.getPushNotificationStatus(push_notification_status_id);
				if (pushNotificationStatus == null) {
					response.put("Status", 0);
					response.put("Msg", "Invalid session, Please try again");
					return response;
				}

				s3_motion_accessKey = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3access_key");
				s3_motion_secretKey = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3secret_key");

				if (video_alert_pushnotification_id.contains(pushNotificationStatus.getPushNotificationId())) {
					vid_url = fileStorageService.createPresignedGetUrl(s3_motion_accessKey, s3_motion_secretKey,
							s3_motion_bucketname, "wc2_" + meid + "_vid/" + pushNotificationStatus.getNotify_vid());
				} else {
					img_url = fileStorageService.createPresignedGetUrl(s3_motion_accessKey, s3_motion_secretKey,
							s3_motion_bucketname, "wc2_" + meid + "/" + pushNotificationStatus.getNotify_img());
				}

			} else {

				if( meid == null || meid.isEmpty() || meid.equalsIgnoreCase("NA") ) {
					Gateway gateway = gatewayService.getGateway(gateway_id);
					if( gateway == null ) {
						response.put("Status", 0);
						response.put("Msg", "Invalid session, Please try again");
						return response;
					}
					meid = gateway.getMeid();
				}
				
				AlertWC alert = alertService.getAlertsWC(alert_id);
				if (alert == null) {
					response.put("Status", 0);
					response.put("Msg", "Invalid session, Please try again");
					return response;
				}

				s3_motion_accessKey = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_access_key");
				s3_motion_secretKey = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_secret_key");

				if (alert.getAlert_type_id() == 27 || alert.getAlert_type_id() == 24
						|| alert.getAlert_type_id() == 23) {
					vid_url = fileStorageService.createPresignedGetUrl(s3_motion_accessKey, s3_motion_secretKey,
							s3_motion_bucketname, "wc2_" + meid + "_vid/" + alert.getFilename());
				} else if (alert.getAlert_type_id() == 7 || alert.getAlert_type_id() == 20
						|| alert.getAlert_type_id() == 21 || alert.getAlert_type_id() == 22) {
					img_url = fileStorageService.createPresignedGetUrl(s3_motion_accessKey, s3_motion_secretKey,
							s3_motion_bucketname, "wc2_" + meid + "/" + alert.getFilename());
				}

			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("image_url", img_url);
			response.put("vid_url", vid_url);
			return response;

		} catch (Exception e) {
			log.error("Error in getMotionDetectionImage :: Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid session, Please try again");
		}
		return response;
	}
	
	
	@RequestMapping(value = "v5.0/getnotificationcounts/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getNotificaitonCountsV5(@RequestParam("app_ver") String app_ver, @RequestParam("os") String os,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		String autho = header.getFirst("auth");
		log.info("Entering  getnotificationcounts : " + autho);

		JResponse response = new JResponse();
		UserV4 user = null;

		int remainderOverdueCount = -1;
		int alert_cnt = 0;
		int notification_cnt = 0;

		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);

		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getNotificaitonCount : " + ex.getLocalizedMessage());
			return response;
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			remainderOverdueCount = reminderservice.getremainderOverDueCount(user.getId());

			alert_cnt = alertService.getUnAckAlertCount(user.getCmpId());

			notification_cnt = notificationService.getNotViewedNotificationCount(user.getId());

			response.put("Status", 1);
			response.put("Msg", "Success");

			response.put("remainder_overdue_count", remainderOverdueCount);
			response.put("alert_cnt", alert_cnt);
			response.put("notification_cnt", notification_cnt);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Error in getting the notification count.");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getnotificationcounts : " + ex.getLocalizedMessage());
			return response;
		}
	}

	
	@RequestMapping(value = "v5.0/usernotifications/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getNotificaitonByUserIDV5(@RequestParam("status") String status,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam int monitor_type_id) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering  getNotificaitonByUserIDV4 : " + autho);
		UserV4 user = null;
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getNotificaitonByUserIDV4 : " + ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		try {
			String userId = String.valueOf(user.getId());
			List<SendNotifications> sendNotificationList = new ArrayList<SendNotifications>();
			//				List<PushNotifications> notifications = new ArrayList<PushNotifications>();
			sendNotificationList = notificationService.userNotificationsV4(userId, status, monitor_type_id, true);

			if (sendNotificationList == null) {
				response.put("Status", 0);
				response.put("Msg", "No notifications found for user.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			ArrayList<Long> notifyIds = new ArrayList<Long>();

			// update pushnotification viewes status as 1
			for (SendNotifications notifyObj : sendNotificationList) {
				notifyIds.add(notifyObj.getNotificationId());

				if( image_alert_pushnotification_id.contains( String.valueOf( notifyObj.getNotificationId() ) ) ) {
					notifyObj.setIs_motion_alert(true);
					//						String img_url = fileStorageService.createPresignedGetUrl( s3_motion_accessKey, s3_motion_secretKey, s3_motion_bucketname, "wc2_"+notifyObj.getMeid()+"/"+notifyObj.getMotion_detection_img());
					notifyObj.setMotion_detection_img( "NA" );
				} else if( video_alert_pushnotification_id.contains(  String.valueOf( notifyObj.getNotificationId() )) ) 
					notifyObj.setIs_video_alert(true);

			}
			notificationService.updateNotificationStatus(user.getId(), notifyIds);

			if (sendNotificationList != null) {

				/* To reverse the collection of latest reported gateway */
				Collections.sort(sendNotificationList, new Comparator<SendNotifications>() {
					@Override
					public int compare(SendNotifications a, SendNotifications b) {
						DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						try {
							return format.parse(b.getSendDate()).compareTo(format.parse(a.getSendDate()));
						} catch (ParseException e) {
							log.error(e.getMessage());
						}
						return 0;
					}
				});
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("userPushNotification", sendNotificationList);
				userService.updateUserNotification(userId, "false");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No notifications found for user.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Error in getting the notifications.");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getNotificaitonByUserIDV4 : " + ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}
		
	// v4.0/usernotifications/ - SIV
	@RequestMapping(value = "v4.0/usernotifications/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getNotificaitonByUserIDV4(@RequestParam("status") String status,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering  getNotificaitonByUserIDV4 : " + autho);
		UserV4 user = null;
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getNotificaitonByUserIDV4 : " + ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		try {
			String userId = String.valueOf(user.getId());
			List<SendNotifications> sendNotificationList = new ArrayList<SendNotifications>();
//			List<PushNotifications> notifications = new ArrayList<PushNotifications>();
			sendNotificationList = notificationService.userNotificationsV4(userId, status, 0, false);
			
			if (sendNotificationList == null) {
				response.put("Status", 0);
				response.put("Msg", "No notifications found for user.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			ArrayList<Long> notifyIds = new ArrayList<Long>();

			// update pushnotification viewes status as 1
			for (SendNotifications notifyObj : sendNotificationList) {
				notifyIds.add(notifyObj.getNotificationId());
			}
			notificationService.updateNotificationStatus(user.getId(), notifyIds);

			if (sendNotificationList != null) {

				/* To reverse the collection of latest reported gateway */
				Collections.sort(sendNotificationList, new Comparator<SendNotifications>() {
					@Override
					public int compare(SendNotifications a, SendNotifications b) {
						DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						try {
							return format.parse(b.getSendDate()).compareTo(format.parse(a.getSendDate()));
						} catch (ParseException e) {
							log.error(e.getMessage());
						}
						return 0;
					}
				});
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("userPushNotification", sendNotificationList);
				userService.updateUserNotification(userId, "false");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No notifications found for user.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Error in getting the notifications.");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getNotificaitonByUserIDV4 : " + ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}

	// Mobile API - kalai
	@RequestMapping(value = "v4.0/getnotificationcounts/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getNotificaitonCounts(@RequestParam("app_ver") String app_ver, @RequestParam("os") String os,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		String autho = header.getFirst("auth");
		log.info("Entering  getnotificationcounts : " + autho);

		JResponse response = new JResponse();
		UserV4 user = null;

		int remainderOverdueCount = -1;
		int alert_cnt = 0;
		int notification_cnt = 0;

		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);

		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getNotificaitonCount : " + ex.getLocalizedMessage());
			return response;
		}

		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
		if (errResponse != null) {
			return errResponse;
		}

		try {
			remainderOverdueCount = reminderservice.getremainderOverDueCount(user.getId());

			alert_cnt = alertService.getUnAckAlertCount(user.getCmpId());

			notification_cnt = notificationService.getNotViewedNotificationCount(user.getId());

			response.put("Status", 1);
			response.put("Msg", "Success");

			response.put("remainder_overdue_count", remainderOverdueCount);
			response.put("alert_cnt", alert_cnt);
			response.put("notification_cnt", notification_cnt);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Error in getting the notification count.");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getnotificationcounts : " + ex.getLocalizedMessage());
			return response;
		}
	}

}
