package com.nimble.irisservices.appcontroller;

import java.io.IOException;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IFileStorageService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IPetSpeciesServices;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@RestController
@RequestMapping("/app")
public class FileControllerApp {

	private static final Logger log = LogManager.getLogger(FileControllerApp.class);
	Helper _helper = new Helper();

	private static String runningenvironment = null;

	@Autowired
	@Lazy
	IFileStorageService fileStorageService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IPetSpeciesServices petService;

	@Value("${profileimages_bucketname}")
	private String bucketName;

	@Value("${profileimages_url}")
	private String profileimages_url;
	
	
	@PostMapping("v5.0/uploadpetprofileimage/")
	public JResponse uploadPetProfileImageV5(@RequestParam("file") MultipartFile mulfile,
			@RequestParam("petid") long petid, @RequestParam("gatewayid") long gatewayid,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) throws IOException {
		String autho = header.getFirst("auth");
		log.info("Entered into uploadPetProfileImageV5 :: auth : "+ autho +" :: petid : "+ petid+" :: gatewayid : "+ gatewayid );
		runningenvironment = _helper.getExternalConfigValue("s3_environment", externalConfigService);
		if (runningenvironment.equals(null))
			runningenvironment = "development";

		JResponse jres = new JResponse();
		if (mulfile.isEmpty()) {
			jres.put("Status", 0);
			jres.put("Msg", "File is empty");
			jres.put("Imageurl", "");
			jres.put("Return Time", System.currentTimeMillis());
			return jres;
		}
		UserV4 user = null;
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

		} catch (InvalidAuthoException e) {
			log.error("Invalid user!");
			jres.put("Status", 0);
			jres.put("Msg", "Invalid user!");
			jres.put("Imageurl", "");
			jres.put("Return Time", System.currentTimeMillis());
			return jres;
		}
		PetProfile petProfile = null;
		try {
			if(petid==0) {
				jres.put("Status", 2);
				jres.put("Msg", "Please save your profile before uploading a profile picture");
				jres.put("Imageurl", "");
				jres.put("Return Time", System.currentTimeMillis());
				return jres;
			}
			
			if (gatewayid == 0) {
				petProfile = petService.getPetprofile("id", String.valueOf(petid)).get(0);
			} else {
				petProfile = gatewayService.getPetProfile(gatewayid);
			}

			if (petProfile != null) {
				petProfile.setUser_id(user.getId());
				if (petProfile.getId() != petid) {
					jres.put("Status", 0);
					jres.put("Msg", "Invalid pet profile!");
					jres.put("Imageurl", "");
					jres.put("Return Time", System.currentTimeMillis());
					return jres;
				}
			} else {
				jres.put("Status", 0);
				jres.put("Msg", "Pet profile not found!");
				jres.put("Imageurl", "");
				jres.put("Return Time", System.currentTimeMillis());
				return jres;
			}
		} catch (Exception e) {
			jres.put("Status", 0);
			jres.put("Msg", "Invalid pet profile!");
			jres.put("Imageurl", "");
			jres.put("Return Time", System.currentTimeMillis());
			log.error("uploadPetProfileImage : " + e.getLocalizedMessage());
			return jres;
		}
		String originalFileName = StringUtils.cleanPath(mulfile.getOriginalFilename());
		String imagePath = runningenvironment + "/" + "petprofile/";
		log.info("imagePath : " + imagePath);
		try {
			boolean isStoredInS3 = fileStorageService.uploadImageToS3Bucket(mulfile, imagePath, bucketName);
			if (isStoredInS3) {
				log.info("Image successfully stored in S3! local file deleted!");
				String resultApi = profileimages_url + bucketName + "/" + imagePath + originalFileName;
				log.info(resultApi);
				try {
					petProfile.setImageurl(resultApi);
					// boolean isPetProfileUpdated = gatewayService.updatePetProfile(petProfile);
					boolean isPetProfileUpdated = gatewayService.updateProfileImgPath(petid, resultApi);
					if (isPetProfileUpdated) {
						jres.put("Status", 1);
						jres.put("Msg", "Success");
						jres.put("Imageurl", resultApi);
					}
				} catch (Exception e) {
					log.error("uploadPetProfileImage : " + e.getLocalizedMessage());
				}
			} else {
				log.info("Save image failed!");
				jres.put("Status", 0);
				jres.put("Msg", "Failed to save image!");
				jres.put("Imageurl", "");
			}
		} catch (Exception e) {
			jres.put("Status", 0);
			jres.put("Msg", "Error occured!");
			jres.put("Imageurl", "");
			log.error("uploadPetProfileImage : " + e.getLocalizedMessage());
		}
		jres.put("Return Time", System.currentTimeMillis());
		return jres;
	}

	@PostMapping("v5.0/uploaduserprofileimage/") // add authkey
	public JResponse uploadUserProfileImageV5(@RequestParam("file") MultipartFile mulfile,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) throws IOException {
		String autho = header.getFirst("auth");
		runningenvironment = _helper.getExternalConfigValue("s3_environment", externalConfigService);
		if (runningenvironment.equals(null))
			runningenvironment = "development";

		JResponse jres = new JResponse();
		if (mulfile.isEmpty()) {
			jres.put("Status", 0);
			jres.put("Msg", "File is empty");
			return jres;
		}
		User user = null;
		try {
			user = userService.verifyAuthKey(autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

		} catch (InvalidAuthoException e) {
			log.error("Invalid user!" +e.getLocalizedMessage());
			jres.put("Status", 0);
			jres.put("Msg", "Invalid user!");
			return jres;
		}
		String originalFileName = StringUtils.cleanPath(mulfile.getOriginalFilename());
		String imagePath = runningenvironment + "/" + "userprofile/";
		log.info("imagePath : " + imagePath);
		try {
			boolean isStoredInS3 = fileStorageService.uploadImageToS3Bucket(mulfile, imagePath, bucketName);
			if (isStoredInS3) {
				log.info("Image successfully stored in S3! local file deleted!");
				String resultApi = profileimages_url + bucketName + "/" + imagePath + originalFileName;
				log.info(resultApi);
				user.setImageUrl(resultApi);
				boolean isSaved = userService.updateUser(user);
				if (isSaved) {
					jres.put("Status", 1);
					jres.put("Msg", "Success");
					jres.put("Imageurl", resultApi);
				}
			} else {
				log.info("Save image failed!");
				jres.put("Status", 0);
				jres.put("Msg", "Failed to save image!");
			}
		} catch (Exception e) {
			log.error("uploadUserProfileImage : " + e.getLocalizedMessage());
			jres.put("Status", 0);
			jres.put("Msg", "Error occured!");
		}
		jres.put("Return Time", System.currentTimeMillis());
		return jres;
	}

}
