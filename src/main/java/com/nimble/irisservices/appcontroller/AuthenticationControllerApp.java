package com.nimble.irisservices.appcontroller;

import com.nimble.irisservices.dto.JBacking;
import com.nimble.irisservices.dto.JOAuth2Token;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JwtTokenData;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.error.OAuth2Error;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.AES;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IOAuth2Service;
import com.nimble.irisservices.service.IUserServiceV4;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.json.JsonParser;
import org.springframework.boot.json.JsonParserFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.jwt.Jwt;
import org.springframework.security.jwt.JwtHelper;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.provider.NoSuchClientException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

@RestController
public class AuthenticationControllerApp {

	private static final Logger log = LogManager.getLogger(AuthenticationControllerApp.class);

	@Value("${config.oauth2.clientid.app}")
	private String clientidApp;

	@Value("${config.oauth2.clientsecret.app}")
	private String clientSecretApp;

	@Value("${config.oauth2.clientid.web}")
	private String clientidWeb;

	@Value("${config.oauth2.clientsecret.web}")
	private String clientSecretWeb;

	@Value("${config.oauth2.forcelogout}")
	private boolean forceLogout = false;

	@Value("${config.oauth2.accesstoken.validation}")
	private int accessTokenValidationTime;

	@Value("#{${Privacy_policy}}")
	private Map<String,String> privacyPolicy;

	@Value("${enable_call_support}")
	private Boolean enable_call_support;

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;

	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;

//	@Autowired
//	private TokenEndpoint tokenEndpoint;

	@Autowired
	IOAuth2Service oAuth2Service;

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	Helper _helper;

	@PostMapping("app/v4.0/checktoken")
	public JResponse checkTokenInfo(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "time_zone", defaultValue = "", required = false) String timeZone,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		log.info("Entered into checkTokenInfo ");
		log.info("user time zone : "+timeZone);
		JResponse response = new JResponse();
		String logoutErrorMsg = OAuth2Error.FORCELOGOUT;
		try {

			if (forceLogout) {
				response.put("Status", -4);
				response.put("Msg", logoutErrorMsg);
				return response;
			}

			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidation(backing);

			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				return response;
			}

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", jBacking.getAuthKey());
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + jBacking.getAuthKey() + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Oops! Your session time out! Log in again");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			byte[] token = oAuth2Service.generateOauth2Token(user.getUsername(), user.getPassword(), clientidApp,
					clientSecretApp);

			if (token == null) {
				log.info("Error while getting token :: username : " + user.getUsername());
				response.put("Status", -1);
				response.put("Msg", "Error while getting token");
				return response;
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("token", token);

		} catch (Exception e) {
			log.error(" Error in checkTokenInfo Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error");
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;

	}

	@PostMapping("v4.0/gettokenbyrefreshtoken")
	public JResponse getOAuth2TokenbyRefreshToken(@RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "time_zone", defaultValue = "", required = false) String timeZone,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		log.info(" Entered into getOAuth2TokenbyRefreshToken ");
		log.info("user time zone : "+timeZone);
		JResponse response = new JResponse();
		String logoutErrorMsg = OAuth2Error.FORCELOGOUT;
		try {

			if (forceLogout) {
				response.put("Status", -4);
				response.put("Msg", logoutErrorMsg);
				return response;
			}

			String backing = header.getFirst("backing");
			String requestFrom = header.getFirst("request_from");
			JBacking jBacking = _helper.backingKeyValidation(backing);

			if (jBacking.getStatus() < 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				return response;
			}

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", jBacking.getAuthKey());
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + jBacking.getAuthKey() + " Error : " + e.getLocalizedMessage());
				response.put("Status", -2);
				response.put("Msg", logoutErrorMsg);
				return response;
			}

			String refreshToken = header.getFirst("refresh_token");
			Jwt jwt = JwtHelper.decode(refreshToken);
			String jwtInfo = jwt.getClaims();
			JsonParser jsonParser = JsonParserFactory.getJsonParser();
			Map<String, Object> claimMap = jsonParser.parseMap(jwtInfo);
			String userName = (String) claimMap.get("user_name");

			if (!userName.equalsIgnoreCase(user.getUsername())) {
				log.info("Authkey not match with JWT credentials :: authkey : " + jBacking.getAuthKey());
				response.put("Status", -2);
				response.put("Msg", logoutErrorMsg);
				return response;
			}

			String clientId = clientidApp;
			String clientSecret = clientSecretApp;

			if (requestFrom != null && requestFrom.equalsIgnoreCase("web")) {
				clientId = clientidWeb;
				clientSecret = clientSecretWeb;
			}
			
			String clientIdInToken = (String) claimMap.get("client_id");
			ResponseEntity<OAuth2AccessToken> token = null;
			try {
				token = oAuth2Service.getOAuth2TokenByRefreshToken(refreshToken, clientIdInToken, clientSecret);
			} catch (NoSuchClientException e) {
				log.info("Invalid Token ClientId : " + clientIdInToken);
				log.info("Recreating OAuth2 token with new client credentials");
				response.put("Status", -4);
				response.put("Msg", logoutErrorMsg);
			} catch (InvalidTokenException e) {
				log.info("Invalid Token");
				log.info("Recreating OAuth2 token");
				byte[] tokenByte = oAuth2Service.generateOauth2Token(user.getUsername(), user.getPassword(), clientId,
						clientSecret);
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("token", tokenByte);
				return response;
			}

			if (token == null) {
				log.info("Error while getting token :: username : " + user.getUsername());
				response.put("Status", -1);
				response.put("Msg", "Error while getting token");
				return response;
			}

			response.put("Status", 1);
			response.put("Msg", "Success");

			JOAuth2Token jOAuth2Token = new JOAuth2Token();
			Date dateUTC = _helper.convertDateTimeToUTC(token.getBody().getExpiration());
			jOAuth2Token.setAccess_token(token.getBody().getValue());
			jOAuth2Token.setRefresh_token(token.getBody().getRefreshToken().getValue());
			jOAuth2Token.setAdditional_information(token.getBody().getAdditionalInformation());
			jOAuth2Token.setExpiration(dateUTC);
			jOAuth2Token.setAccess_token_validation_time(accessTokenValidationTime);

			DateFormat formatterUTC = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String exDate = formatterUTC.format(dateUTC);
			
			jOAuth2Token.setExpire_time(exDate);
			
			byte[] tokenInfoByte = Helper.zipContent(jOAuth2Token);

			response.put("token", tokenInfoByte);
			response.put("Return Time", System.currentTimeMillis());
			return response;

		} catch (Exception e) {
			log.error(" Error in getToken : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error");
			return response;
		}

	}

	@PostMapping("v4.0/gettoken")
	public JResponse getToken(@RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "time_zone", defaultValue = "", required = false) String timeZone,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		log.info(" Entered into gettoken ");
		log.info("user time zone : "+ timeZone);
		JResponse response = new JResponse();
		String logoutErrorMsg = OAuth2Error.FORCELOGOUT;
		try {

			if (forceLogout) {
				response.put("Status", -4);
				response.put("Msg", logoutErrorMsg);
				return response;
			}

			String backing = header.getFirst("backing");
			String username = header.getFirst("username");

			AES aes = new AES();

			username = _helper.backingKeyValidation(username).getAuthKey();

			JBacking jBacking = _helper.backingKeyValidation(backing);

			if (jBacking.getStatus() < 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				return response;
			}

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", jBacking.getAuthKey());
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + jBacking.getAuthKey() + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Oops! Your session time out! Log in again");
				return response;
			}

			if (!user.getUsername().equals(username)) {
				response.put("Status", 0);
				response.put("Msg", "Username not match");
				return response;
			}
			
			byte[] token = oAuth2Service.generateOauth2Token(user.getUsername(), user.getPassword(), clientidApp,
					clientSecretApp);

			if (token == null) {
				log.info("Error while getting token :: username : " + user.getUsername());
				response.put("Status", -1);
				response.put("Msg", "Error while getting token");
				return response;
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("token", token);
			response.put("Return Time", System.currentTimeMillis());
			return response;

		} catch (Exception e) {
			log.error(" Error in getToken : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error");
			return response;
		}

	}

	/**
	 * Validates a JWT token and returns the token data if valid.
	 *
	 * @param header The HTTP headers containing the request information.
	 * @param os     The operating system of the user's device.
	 * @param app_ver The version of the user's application.
	 * @param token   The JWT token to be validated.
	 * @return A JResponse object containing the token data if valid, or an error message if invalid.
	 */
	@GetMapping("v5.0/verify")
	public JResponse validateJWTToken(@RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "token", defaultValue = "", required = false) String token) throws InvalidAuthoException {

		log.info("Entered into validateJWTToken");
		JResponse response = new JResponse();

		JwtTokenData jwtTokenData = oAuth2Service.validateJWTToken(token);
		if (jwtTokenData == null) {
			response.put("Status", 0);
			response.put("Msg", "Invalid JWT token");
			return response;
		}

		String country = userServiceV4.verifyAuthV4("id", jwtTokenData.getUserId() + "").getCountry();

		response.put("Status", 1);
		response.put("Msg", "Success");
		response.put("privacyPolicy", privacyPolicy.get(country));
		response.put("enableCallSupport", enable_call_support);
		response.put("supportContactNumber", supportContactNumber.get(country));
		response.put("supportContactEmail", supportContactEmail.get(country));
		response.put("tokenData", jwtTokenData);
		response.put("Return Time", System.currentTimeMillis());

		return response;
	}
}
