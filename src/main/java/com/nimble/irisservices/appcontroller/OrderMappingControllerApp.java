package com.nimble.irisservices.appcontroller;

import java.io.File;
import java.io.FileInputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import com.nimble.irisservices.dao.IGatewayDao;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.gson.Gson;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JPlan;
import com.nimble.irisservices.dto.JProductSubscription;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AllSubscription;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.ProductSubscription;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICommonService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IRechargeService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class OrderMappingControllerApp {

	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	@Lazy
	IChargebeeService cbService;

	@Autowired
	@Lazy
	IRechargeService reService;

	Helper _helper = new Helper();

	@Value("${redirect.payment.page}")
	private boolean redirectPaymentPage_config = false;

	@Value("${redirect.payment.page.content}")
	private String redirectPaymentPageContent;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Value("${subscription.buynow.popup}")
	private boolean subscriptionBuyNowPopUp_config;

	@Value("${subscription.buynow.content}")
	private String subscriptionBuyNowContent;

	@Value("${show.checkout.page.popup}")
	private boolean show_check_out_page;

	@Value("${redirect.checkout.page.content}")
	private String check_out_page_content;
	
	@Value("${redirect.checkout.page.image}")
	private String check_out_page_image;
	
	@Value("${redirect.checkout.page.image.dark}")
	private String check_out_page_image_dark;

	@Value("${warranty_success}")
	private String warranty_success;

	@Value("${warranty_success_subs}")
	private String warranty_success_subs;

	@Value("${warranty_success_sec_dev}")
	private String warranty_success_sec_dev;
	
	@Value("${warranty_success_v2_image}")
	private String warranty_success_v2_image;

	@Value("${warranty_success_subs_v2_image}")
	private String warranty_success_subs_v2_image;

	@Value("${warranty_success_sec_dev_v2_image}")
	private String warranty_success_sec_dev_v2_image;
	
	@Value("${warranty_success_v2_image_dark}")
	private String warranty_success_v2_image_dark;

	@Value("${warranty_success_subs_v2_image_dark}")
	private String warranty_success_subs_v2_image_dark;

	@Value("${warranty_success_sec_dev_v2_image_dark}")
	private String warranty_success_sec_dev_v2_image_dark;

	
	@Value("${others_warranty_success_v2_image}")
	private String others_warranty_success_v2_image;

	@Value("${others_warranty_success_subs_v2_image}")
	private String others_warranty_success_subs_v2_image;

	@Value("${others_warranty_success_sec_dev_v2_image}")
	private String others_warranty_success_sec_dev_v2_image;
	
	@Value("${others_warranty_success_v2_image_dark}")
	private String others_warranty_success_v2_image_dark;

	@Value("${others_warranty_success_subs_v2_image_dark}")
	private String others_warranty_success_subs_v2_image_dark;

	@Value("${others_warranty_success_sec_dev_v2_image_dark}")
	private String others_warranty_success_sec_dev_v2_image_dark;
	

	@Value("${product_subs_enable}")
	private boolean product_subs_enable;

	@Autowired
	@Lazy
	IChargebeeService chargebeeService;

	@Value("${freeplan}")
	private String freeplan;

	@Value("#{${supportcontactnumber}}")
	private Map<String, String> supportContactNumber;

	@Value("#{${supportemail}}")
	private Map<String, String> supportContactEmail;
	
	@Autowired
	IRechargeService rechargeService;
	
	@Autowired
	ICommonService commonService;

	@Autowired
	@Lazy
	IGatewayDao gatewayDao;
	
	@Value("${bundle_contact_us_content}")
	private String bundle_contact_us_content;

	private static final Logger log = LogManager.getLogger(OrderMappingControllerApp.class);
	
	@RequestMapping(value = "v5.0/checkbundleorder", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse checkBundleOrder(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam String order_id,
			@RequestParam String purchased_from) {
		log.info("Entered into checkBundleOrder :: order_id : "+order_id);
		JResponse response = new JResponse();
		try {
			String auth = header.getFirst("auth");
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV3("authkey", auth);
				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException e) {
				log.error("Invalid Authkey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "User not found. " + RegisterUserError.warrantySupportMsg);
				return response;
			}
			
			List<Orders> orderList = niomDbservice.getOrderById(purchased_from, order_id);
			
			if( orderList == null || orderList.isEmpty() ) {
				log.info("order not found for purchased_from : "+ purchased_from+" :: order_id : "+ order_id);
				response.put("Status", 0);
				response.put("Msg", "Invalid Order Id");
				return response;
			}
			
			boolean is_email_mismatch = false;
			boolean is_recharge = false;
			ArrayList<String> subDetails = new ArrayList<String>();
			subDetails = rechargeService.getReSubscription(user.getEmail(), orderList.get(0).getOrder_id()+"" );
			String email = user.getEmail();
			if(!subDetails.isEmpty()) {
				is_recharge = true;
				is_email_mismatch = subDetails.get(3).equalsIgnoreCase(user.getEmail()) ? false : true;
				email = subDetails.get(3);
			}
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("is_email_mismatch", is_email_mismatch);
			response.put("is_recharge", is_recharge);
			response.put("email", email);
			
		} catch (Exception e) {
			log.error("Error in checkBundleOrder :: Error : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid session, Please try again later");
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v5.0/ordermap/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse orderMapV5(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "orderid", required = true) String orderId,
			@RequestParam(value = "purchasedfrom", required = true) String orderChannel,
			@RequestParam(value = "gatewayid", required = true) long gatewayId,
			@RequestParam(value = "remark", required = false, defaultValue = "NA") String remark,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "show_subs_popup", required = false) boolean show_subs_popup,
			@RequestParam(value = "req_from", defaultValue = "home", required = false) String reqFrom,
			@RequestParam(value = "valid_bundle_subs", required = false) boolean valid_bundle_subs,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country,
			@RequestParam(value = "username", required = false) String username,
			@RequestParam(value = "is_verified_recharge", required = false) boolean is_verified_recharge,
			@RequestParam(value = "skip", defaultValue = "false", required = false) boolean skip,
		  	@RequestParam(value="purchasedMonthYear",defaultValue = "1753-01-01",required = false)String purchasedMonthYear) {

		String auth = "NA";
		log.info("Entered into orderMap :: req_from : "+ reqFrom);
		
		JResponse response = new JResponse();
		if( reqFrom.equalsIgnoreCase("register_product") ) {
			log.info("Entered into orderMap :: username : " + username);  
		} else {
			auth = header.getFirst("auth");
			log.info("Entered into orderMap :: authKey : " + auth);
		}
		
		boolean bundledOrderFailed = false;
		UserV4 user = null;
		String to_address = null;
		String cc_address = null;
		String bcc_address = null;
		String mailSub = "";
		if (device_country.equalsIgnoreCase("US") || device_country.equalsIgnoreCase("NA")
				|| device_country.toLowerCase().contains("india") || device_country.equalsIgnoreCase("in")
				|| device_country.isEmpty() || device_country == null) {
			device_country = "US";
		}
		String supportM = supportContactEmail.get(device_country);
		String supportP = supportContactNumber.get(device_country);

		boolean pFromStatus = false;

//		String contactMsg = "<br>Please contact us at <br><font color='blue'> <a href=\"tel: ************\"><b>************</b></a></font> or <font color='blue'><a href=\"mailto:<EMAIL>\"><b><EMAIL></b></a></font>";
		String mailContent = "<p>Hi Team,</p>" + "<p>Find the user order mapping details</p>";
		String title="";
		String desc1="";
		String desc2="";
		String img_url="";
		try {
			if (skip) {
				Properties prop = new Properties();
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));

				to_address = prop.getProperty("to_address");
				cc_address = prop.getProperty("cc_address");
				bcc_address = prop.getProperty("bcc_address");
				if( reqFrom.equalsIgnoreCase("register_product") )
					user = userServiceV4.verifyAuthV3("username", username);
				else
					user = userServiceV4.verifyAuthV3("authkey", auth);

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("title", title);
				response.put("desc1", desc1);
				response.put("desc2", desc2);
				response.put("img_url", img_url);

				response.put("show_payment_page", false);
				response.put("subscription_buynow_popup", false);
				response.put("show_check_out_popup", false);
				response.put("navigate_to_subscription", false);
				response.put("stay_warranty_page", false);
				response.put("show_bundle_contact_us", false);
				response.put("bundle_contact_us_content", bundle_contact_us_content);
				response.put("subscription_buynow_content", subscriptionBuyNowContent);
				response.put("check_out_popup_content", check_out_page_content);
				response.put("check_out_popup_image", check_out_page_image);
				response.put("check_out_popup_image_dark", check_out_page_image_dark);
				response.put("bundle_check_out_url", "NA");

				response.put("navigate_to_subs_content", warranty_success_subs);
				response.put("navigate_to_subs_content_v2_image", others_warranty_success_subs_v2_image);
				response.put("navigate_to_subs_content_v2_image_dark", others_warranty_success_subs_v2_image_dark);

				response.put("stay_warranty_page_content", warranty_success_sec_dev);
				response.put("stay_warranty_page_content_v2_image", others_warranty_success_sec_dev_v2_image);
				response.put("stay_warranty_page_content_v2_image_dark", others_warranty_success_sec_dev_v2_image_dark);

				mailSub = "Skipped : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Skipped </p>";
				response.put("Return Time", System.currentTimeMillis());

				mailContent = mailContent + "<p>Name : " + user.getFirstname() + " " + user.getLastname() + "</p>"
						+ "<p>Purchased from : " + orderChannel + " </p>" + "<p>Email : " + user.getEmail() + "  </p>"
						+ "<p>Phone : " + user.getMobileno() + "  </p>" + "<p>Order ID : " + orderId + "</p>";
				if(skip){
					Gateway gateway = gatewayService.getGatewayByid(gatewayId);

					if (gateway != null) {
						gatewayService.updateGatewaySkip(true,gateway.getId());
					}

					if(!purchasedMonthYear.equals("1753-01-01") ) {
						purchasedMonthYear=purchasedMonthYear+"-01";
						gatewayService.updatePurchaseMonthYear(user.getId(), gatewayId, purchasedMonthYear);
					}
				}
				mailContent = mailContent + "<br>Thanks,<br> Irisservice ";
				async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);

				return response;
			}

			if(!reqFrom.equalsIgnoreCase("register_product")){
				Gateway gateway = gatewayService.getGatewayByid(gatewayId);

				if (gateway != null) {
					gatewayService.updateGatewaySkip(true,gateway.getId());
				}
			}
		}catch (Exception e){
			log.error("Error in skip warranty :" + e.getLocalizedMessage());
		}
		try {
			Properties prop = new Properties();
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			prop.load(new FileInputStream(file));

			to_address = prop.getProperty("to_address");
			cc_address = prop.getProperty("cc_address");
			bcc_address = prop.getProperty("bcc_address");

			response.put("show_bundle_contact_us", false);
			response.put("bundle_contact_us_content", bundle_contact_us_content);
			
			try {
				if( reqFrom.equalsIgnoreCase("register_product") )
					user = userServiceV4.verifyAuthV3("username", username);
				else
					user = userServiceV4.verifyAuthV3("authkey", auth);
				
				String country = user.getCountry().toUpperCase();
				if (country == null || country.isEmpty() || country.equalsIgnoreCase("US")
						|| country.equalsIgnoreCase("NA") || country.toLowerCase().contains("india")
						|| country.equalsIgnoreCase("in")) {
					country = "US";
				}

				supportM = supportContactEmail.get(country);
				supportP = supportContactNumber.get(country);

				if( !reqFrom.equalsIgnoreCase("register_product") ) {
					JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
					if (errResponse != null) {
						return errResponse;
					}	
				}
				

			} catch (InvalidAuthoException e) {

				log.error("Invalid Authkey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "User not found. " + RegisterUserError.warrantySupportMsg);
				mailSub = "Failed : External Order Mapping Status-Email : ";
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ " <p> Error Msg : User not found </p>";
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			mailContent = mailContent + "<p>Name : " + user.getFirstname() + " " + user.getLastname() + "</p>"
					+ "<p>Purchased from : " + orderChannel + " </p>" + "<p>Email : " + user.getEmail() + "  </p>"
					+ "<p>Phone : " + user.getMobileno() + "  </p>" + "<p>Order ID : " + orderId + "</p>";

			Gson gson = new Gson();
			Orders order = new Orders();
			boolean orderMappedStatus = false;
			String subscriptionStatus = "0";
			String eRes = RegisterUserError.warrantySupportMsg;

			Gateway gateway = gatewayService.getGatewayByid(gatewayId);
			
			
			if (gateway == null) {
				log.error("Gateway not found :: gateway id : " + gatewayId);
				response.put("Status", 0);
				String msg = "Unable to fetch your monitor details. " + eRes;

				response.put("Msg", msg);

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ "<p> Error Msg : Gateway details not found </p>";
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			mailContent = mailContent + "<p> Meid : " + gateway.getMeid() + "</p>";
			Inventory inventory = niomDbservice.getInventoryByMeid(gateway.getMeid());

			if (inventory == null) {
				log.error("Meid not found in inventory :: meid : " + gateway.getMeid());
				response.put("Status", 0);
				 eRes = RegisterUserError.warrantySupportMsg;
				String msg = "Unable to fetch your monitor details. " + eRes;

				response.put("Msg", msg);

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ "<p> Error Msg : Inventory not found </p>";
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			
			boolean oldSubAvail = false;
			List<AllSubscription> subscription = chargebeeService.getSubscriptionByChargebeeId(user.getChargebeeid());
			if (subscription != null) {
				oldSubAvail = true;
			}

			if (orderChannel.equalsIgnoreCase("rv")) {
				pFromStatus = false;
				subscriptionStatus = "1";
			} else if (orderChannel.equalsIgnoreCase("others")) {
				pFromStatus = true;
				boolean isUpdateOrderMappingDetails = userServiceV4.updateOrderMappingDetails(user.getId(),
						inventory.getQrc(), "NA", remark, "NA", "NA", "");

				gatewayService.changeGatewayOrderidStatus(gateway.getId(), pFromStatus);

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p>Remark : " + remark + "  </p>" + "<p>Order Mapping Status : Failed </p>"
						+ " <p> Error Msg : Order purchased from other source : " + remark + "</p>";
				response.put("Return Time", System.currentTimeMillis());

				
				boolean stay_warranty_page = false;

				boolean isPetMonitor = false;
				boolean curPetMonitor = false;
				if (oldSubAvail) {
					List<JGateway> jGatewayList = gatewayService.getJGatewayByUser(user.getId(), "");
					if (jGatewayList != null) {

						for (JGateway gate : jGatewayList) {

							if (gatewayId != gate.getId() && gate.getMonitorTypeId() == 1 && !(gate.isShowOrderId() || gate.isPurchased_from_others()))
								isPetMonitor = true;

							if (gatewayId == gate.getId() && gate.getMonitorTypeId() == 1)
								curPetMonitor = true;

							if (!gate.isShowOrderId() && !gate.isPurchased_from_others() && gate.getMonitorTypeId() == 1)
								stay_warranty_page = true;

							if (!gate.isShowOrderId() && !gate.isPurchased_from_others() && gatewayId != gate.getId() && gate.getMonitorTypeId() > 1)
								stay_warranty_page = true;

						}

					}
				}

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("title", title);
				response.put("desc1", desc1);
				response.put("desc2", desc2);
				response.put("img_url", img_url);
				
				response.put("show_payment_page", false);
				response.put("subscription_buynow_popup", false);
				response.put("show_check_out_popup", false);
				response.put("navigate_to_subscription", false);
				response.put("stay_warranty_page", false);

				response.put("subscription_buynow_content", subscriptionBuyNowContent);
				response.put("check_out_popup_content", check_out_page_content);
				response.put("check_out_popup_image", check_out_page_image);
				response.put("check_out_popup_image_dark", check_out_page_image_dark);
				response.put("bundle_check_out_url", "NA");

				response.put("navigate_to_subs_content", warranty_success_subs);
				response.put("navigate_to_subs_content_v2_image", others_warranty_success_subs_v2_image);
				response.put("navigate_to_subs_content_v2_image_dark", others_warranty_success_subs_v2_image_dark);

				response.put("stay_warranty_page_content", warranty_success_sec_dev);
				response.put("stay_warranty_page_content_v2_image", others_warranty_success_sec_dev_v2_image);
				response.put("stay_warranty_page_content_v2_image_dark", others_warranty_success_sec_dev_v2_image_dark);

				eRes = RegisterUserError.warrantyErrorMsg;
				String msg = "<center><p>" + eRes + ". </p></center>";

				if (!reqFrom.equalsIgnoreCase("warranty")) {
					response.put("show_check_out_popup", false);
					response.put("subscription_buynow_popup", false);
					response.put("show_payment_page", false);
					response.put("navigate_to_subscription", false);
					return response;
				}
				

				if (stay_warranty_page && (isPetMonitor && curPetMonitor) ) {
					response.put("stay_warranty_page", true);
					title = "Hooray!";
					if (orderChannel.equalsIgnoreCase("others"))
						desc1 = "Your warranty is registered successfully.";
					else
						desc1 = "Please contact support to register your warranty.";
					desc2 = "Get one for your other device too!";
					img_url = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/ordermap.png";
					
					response.put("title", title);
					response.put("desc1", desc1);
					response.put("desc2", desc2);
					response.put("img_url", img_url);
					
					String content = warranty_success_sec_dev.replace("</br>Your warranty is registered successfully.",
							msg);
					response.put("stay_warranty_page_content", content);
					return response;
				}

				if (valid_bundle_subs && (curPetMonitor || !oldSubAvail) && gateway.getModel().getMonitor_type().getId() == 1) {
					response.put("navigate_to_subscription", true);
					title = "Hooray!";
					desc2 = "Subscribe & Activate Your alerts now!";
					img_url = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/ordermap_card.png";
					
					response.put("title", title);
					if (orderChannel.equalsIgnoreCase("others"))
						desc1 = "Your warranty is registered successfully.";
					else
						desc1 = "Please contact support to register your warranty.";
					response.put("desc1", desc1);
					response.put("desc2", desc2);
					response.put("img_url", img_url);
					
					String content = warranty_success_subs.replace("</br>Your warranty is registered successfully.",
							msg);
					response.put("navigate_to_subs_content", content);
					return response;
				}

//				response.put("stay_warranty_page", true);
				title = "Hooray!";
				desc1 = "Your warranty is registered successfully.";
				img_url = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/ordermap.png";
				
				response.put("title", title);
				response.put("desc1", desc1);
				response.put("desc2", desc2);
				response.put("img_url", img_url);
				
				String content = warranty_success.replace("</br>Your warranty is registered successfully.", msg);
				response.put("stay_warranty_page_content", content);
				response.put("stay_warranty_page_content_v2_image", others_warranty_success_v2_image);
				response.put("stay_warranty_page_content_v2_image_dark", others_warranty_success_v2_image_dark);
				return response;

			}

			mailContent = mailContent + "<p>QRC : " + inventory.getQrc() + " </p>";
			mailContent = mailContent + "<p>Device Model in Inventory : " + inventory.getDevicemodelnumber() + "</p>";

			JSONObject jorderIdCheckResponse = new JSONObject();
			jorderIdCheckResponse = userService.getNiomGetOrderCount(orderChannel, orderId);

			int orderIdCheckStatus = jorderIdCheckResponse.getInt("Status");

			if (orderIdCheckStatus > 0) {
				order = gson.fromJson(jorderIdCheckResponse.getJSONObject("Order").toString(), Orders.class);
				int totalOrderCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalordered"));
				int totalMappedCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalmapped"));

				if (totalMappedCount >= totalOrderCount) {
					response.put("Status", 0);

					eRes = RegisterUserError.warrantySupportMsg;
					String msg = "Invalid Order ID. " + eRes;

					response.put("Msg", msg);

					mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
					mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
							+ "<p> Error Msg : Order Id is already registered </p>";
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				mailContent = mailContent + "<p>Device Model in Orders : " + order.getDevicemodel() + "</p>";
				mailContent = mailContent + "<p>Order Account Type ID in Orders : "
						+ order.getOrder_acc_type().getAcc_type() + "</p>";

				log.info("Order id found in niom. Order Channel : " + order.getOrder_acc_type().getAcc_type()
						+ "Order ID : " + order.getOrder_id() + "Out Order ID  :" + order.getExternal_order_id());

				if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("amazon")) {
					if (order.getBilling_email().toLowerCase().contains("na")
							|| order.getBilling_phone().toLowerCase().contains("na")) {
						order.setBilling_email(user.getEmail());
						order.setBilling_phone(user.getMobileno());
						order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
						order.setWelcome_status(order.getWelcome_status());
						userService.checkFirstNameAndLastNameInOrder(order, user);
						boolean updateExternalOrderData = niomDbservice.updateExternalOrdersInNiom(order);
					}
				} else if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("walmart")) {
					if (order.getBilling_email().toLowerCase().contains("na")
							|| order.getBilling_phone().toLowerCase().contains("na")) {
						order.setBilling_email(user.getEmail());
						order.setBilling_phone(user.getMobileno());
						order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
						order.setWelcome_status(order.getWelcome_status());
						userService.checkFirstNameAndLastNameInOrder(order, user);
						boolean updateExternalOrderData = niomDbservice.updateExternalOrdersInNiom(order);
					}
				} else if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("technorv")) {
					if (order.getBilling_email().toLowerCase().contains("na")
							|| order.getBilling_phone().toLowerCase().contains("na")) {
						order.setBilling_email(user.getEmail());
						order.setBilling_phone(user.getMobileno());
						order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
						order.setWelcome_status(order.getWelcome_status());
						userService.checkFirstNameAndLastNameInOrder(order, user);
						boolean updateExternalOrderData = niomDbservice.updateExternalOrdersInNiom(order);
					}
				}
				
				ProductSubscription productSubscription = cbService.getProductSubscriptionByOrderId( order.getOrder_id() );
//				ArrayList<String> subDetails = reService.getReSubscription( user.getEmail() , order.getOrder_id()+"" );
//				if( productSubscription != null || !subDetails.isEmpty()) {
				if (productSubscription != null) {
					String planver = cbService.getPlanVersionbyplanname(productSubscription.getPlan_id());
					if (gateway.getModel().getMonitor_type().getId() == 1) {
						if (planver != null && !planver.equalsIgnoreCase("NA") && planver.equalsIgnoreCase("V3")
								&& oldSubAvail) {
							productSubscription.setUser_id(user.getId());
							productSubscription.setActive_subscription(true);
							cbService.saveOrUpdateProductSubscription(productSubscription);
							log.info("bundled order failed due to plan mismatch");
							bundledOrderFailed = true;
							response.put("Status", 0);
							eRes = RegisterUserError.warrantySupportMsg;
							String msg = "Unable to Claim the Warranty. " + eRes;
							response.put("Msg", msg);
							response.put("show_bundle_contact_us", true);
							response.put("bundle_contact_us_content", bundle_contact_us_content);
							return response;
						} else if (planver != null && !planver.equalsIgnoreCase("NA") && !planver.equalsIgnoreCase("V3")
								&& !oldSubAvail) {
							productSubscription.setUser_id(user.getId());
							productSubscription.setActive_subscription(true);
							cbService.saveOrUpdateProductSubscription(productSubscription);
							log.info("bundled order failed due to plan mismatch");
							bundledOrderFailed = true;
							response.put("Status", 0);
							eRes = RegisterUserError.warrantySupportMsg;
							String msg = "Unable to Claim the Warranty. " + eRes;
							response.put("Msg", msg);
							response.put("show_bundle_contact_us", true);
							response.put("bundle_contact_us_content", bundle_contact_us_content);
							return response;
						}
					}
					if (planver != null && !planver.equalsIgnoreCase("NA") && !planver.equalsIgnoreCase("V3")) {
						boolean checkPaidPlan = cbService.checkCurrentPaidPlan(user.getChargebeeid(), 1);
						if (checkPaidPlan) {
							productSubscription.setUser_id(user.getId());
							productSubscription.setActive_subscription(true);
							cbService.saveOrUpdateProductSubscription(productSubscription);
							log.info("bundled order failed due to already have paid plan");
							bundledOrderFailed = true;
							response.put("Status", 0);
							eRes = RegisterUserError.warrantySupportMsg;
							String msg = "Unable to Claim the Warranty. " + eRes;
							response.put("Msg", msg);
							response.put("show_bundle_contact_us", true);
							response.put("bundle_contact_us_content", bundle_contact_us_content);
							return response;
						}
					} else {

						if (planver != null && !planver.equalsIgnoreCase("NA") && planver.equalsIgnoreCase("V3")) {
							cbService.generatePaymentURLForProductsub(productSubscription, user.getChargebeeid(),
									user.getId(), gateway.getId(), gateway.getModel().getMonitor_type().getId());
						}
					}
				}
				

				orderMappedStatus = userService.orderMapping(inventory.getMeid(), _helper.getCurrentTimeinUTC(),
						inventory.getDevicestate().getId(), order.getOrder_id() + "", order.getDevicemodel(),
						subscriptionStatus, "1", "manual", gatewayId);

				if (!orderMappedStatus) {
					response.put("Status", 0);

					eRes = RegisterUserError.warrantySupportMsg;
					String msg = "Unable to Claim the Warranty. " + eRes;

					response.put("Msg", msg);

					String[] deviceModel = order.getDevicemodel().split("\\.");
					boolean deviceModelMisMatch = true;

					for (int index = 0; index < deviceModel.length; index++) {
						if (inventory.getDevicemodelnumber().contains(deviceModel[index])) {
							deviceModelMisMatch = false;
						}
					}

					mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
					mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
							+ "<p> Error Msg : Unable to map order";

					if (deviceModelMisMatch) {
						mailContent = mailContent + ", device model number mismatch </p>";
					} else {
						mailContent = mailContent + "</p>";
					}

					response.put("Return Time", System.currentTimeMillis());
					return response;
				} else {
					boolean isUserId = crService.updateUserIdInProsuctSubs(String.valueOf(order.getOrder_id()),
							user.getId());
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "Sorry, we couldn't find your Order ID. " + RegisterUserError.warrantySupportMsg);

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ "<p> Error Msg : Order Id not found </p>";

				log.info("Order id not found in niom, Error Code : ER039 :" + RegisterUserError.ER039);

				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			niomDbservice.updateOrdermapUserDetails(order.getOrder_id(), user.getUsername(), user.getId() + "", "NA");

			boolean updateExternalOrderDataStatus = userService.updateOrdersDataV2(orderChannel, order, inventory);

			if (orderMappedStatus) {

				boolean isUpdateOrderMappingDetails = userServiceV4.updateOrderMappingDetails(user.getId(),
						inventory.getQrc(), orderId, orderChannel, "6", order.getOrder_sku(), order.getDatetime());

				gatewayService.changeGatewayOrderidStatus(gateway.getId(), pFromStatus);
				int remainingdays = calculateWarrantyDays(order.getDatetime());
				String msg = "You have " + remainingdays + " days of warranty left";

				if (remainingdays <= 0) {
					msg = "Your warranty has expired";
				}

				// Update sales channel in device subscription table
				async.updateSalesChannel(user.getId()+"", orderChannel, gatewayId, gateway.getMeid(), orderId,gateway.getModel().getMonitor_type().getId());
				
				response.put("Status", 1);
				response.put("Msg", msg);
				response.put("show_payment_page", false);
				response.put("subscription_buynow_popup", false);
				response.put("show_check_out_popup", false);
				response.put("subscription_buynow_content", subscriptionBuyNowContent);
				response.put("check_out_popup_content", check_out_page_content);
				response.put("check_out_popup_image", check_out_page_image);
				response.put("check_out_popup_image_dark", check_out_page_image_dark);
				response.put("bundle_check_out_url", "NA");

				response.put("navigate_to_subscription", false);
				response.put("navigate_to_subs_content", warranty_success_subs);
				response.put("navigate_to_subs_content_v2_image", warranty_success_subs_v2_image);
				response.put("navigate_to_subs_content_v2_image_dark", warranty_success_subs_v2_image_dark);

				response.put("stay_warranty_page", false);
				response.put("stay_warranty_page_content", warranty_success_sec_dev);
				response.put("stay_warranty_page_content_v2_image", warranty_success_sec_dev_v2_image);
				response.put("stay_warranty_page_content_v2_image_dark", warranty_success_sec_dev_v2_image_dark);

				String email = user.getUsername();
				if( is_verified_recharge )
					email = "NA";
				
				JResponse rechargeResponse = reService.createRechargeSubscription(email,
						user.getChargebeeid(), user.getId(), String.valueOf(order.getOrder_id()),gateway.getId(),gateway.getModel().getMonitor_type().getId());
				if(reqFrom.equalsIgnoreCase("register_product")) {
					mailSub = "Success : Auto Warranty Claim External Order Mapping Status-Email : " + user.getEmail();
				}else {
					mailSub = "Success : External Order Mapping Status-Email : " + user.getEmail();
				}
				
				mailContent = mailContent + "<p> Order Mapping Status : Success </p> ";
				
				if (rechargeResponse.getResponse().containsKey("mail_content")) {

					if (!((boolean) rechargeResponse.get("is_recharge"))) {
						ArrayList<String> subInfo = reService
								.getRechargeSubscriptinInfo(String.valueOf(order.getOrder_id()));
						if (subInfo.isEmpty()) {
							
							
							try {
								if(((String) rechargeResponse.get("cb_status")).equalsIgnoreCase("Sub_Created") ) {
									response.put("Msg", "Your plan is now activated, and the warranty has been successfully claimed");	
								}	
							} catch (Exception e) {
								log.error("error while proccessing response msg for recharge combo subs");
							}
							
							
							mailContent = mailContent + rechargeResponse.get("mail_content");
						} else {
							mailContent = mailContent + "<p> Recharage customer : true </p>";
							mailContent = mailContent
									+ "<p> This customer already have recharge plan in chargebee </p>";
						}
						
						
						boolean stay_warranty_page = false;

						boolean isPetMonitor = false;
						boolean curPetMonitor = false;
						
						if (oldSubAvail) {
							List<JGateway> jGatewayList = gatewayService.getJGatewayByUser(user.getId(), "");
							if (jGatewayList != null) {

								for (JGateway gate : jGatewayList) {

									if (gatewayId != gate.getId() && gate.getMonitorTypeId() == 1 && !(gate.isShowOrderId() || gate.isPurchased_from_others()))
										isPetMonitor = true;

									if (gatewayId == gate.getId() && gate.getMonitorTypeId() == 1) curPetMonitor = true;

									if (!gate.isShowOrderId() && !gate.isPurchased_from_others() && gate.getMonitorTypeId() != 5 && gate.getMonitorTypeId() != 6)
										stay_warranty_page = true;

								}

							}
						}
						
//						if( !isPetMonitor && !curPetMonitor ) {
//							response.put("title", title);
//							response.put("desc1", desc1);
//							response.put("desc2", desc2);
//							response.put("img_url", img_url);
//							return response;
//						}

						if (stay_warranty_page) {

							response.put("stay_warranty_page", true);

						} else {

							String check_out_page_url = "NA";
							boolean show_check_out_page = this.show_check_out_page;
							ProductSubscription productSubscription = cbService.getProductSubscription(user.getId());

							log.info("valid_bundle_subs : " + valid_bundle_subs + " :: show_check_out_page : "
									+ show_check_out_page + " product_subs_enable : " + product_subs_enable);

							if (valid_bundle_subs && show_check_out_page && productSubscription != null
									&& !productSubscription.isIs_subscription_activated() && product_subs_enable) {

								String planVer = cbService.getPlanVersionbyplanname(productSubscription.getPlan_id());
								
								if(planVer != null && planVer.equalsIgnoreCase("V3")) {
									check_out_page_url = cbService.generatePaymentURLForProductsub(productSubscription,
										user.getChargebeeid(),user.getId(),gateway.getId(),gateway.getModel().getMonitor_type().getId());
								}else {
									check_out_page_url = cbService.checkOutURLForBundleSubs(productSubscription,
											user.getChargebeeid());
								}
										
								if (!check_out_page_url.equalsIgnoreCase("NA")) {
									show_check_out_page = true;
									mailSub = "Success : Bundle Order Mapping Status-Email : " + user.getEmail();
									title = "Woohoo!";
									desc2 = "Your warranty is successfully registered \n"
											+ "					Your subscription is one step away \n"
											+ "					Please enter your patment details for \n"
											+ "					Uninterrupted Pet Prodection \n"
											+ "					You will not be charged anything \n until your next renewal*";
									img_url = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/ordermap_card.png";
									desc1 = "You're almost there!";

									response.put("title", title);
									response.put("desc1", desc1);
									response.put("desc2", desc2);
									response.put("img_url", img_url);
								} else {
									show_check_out_page = false;
								}

								response.put("show_check_out_popup", show_check_out_page);
								response.put("bundle_check_out_url", check_out_page_url);
								JPlan planDesc = chargebeeService.getPlanDesc(productSubscription.getPlan_id());
								String subsPeriod = chargebeeService.getSubscriptionPeriod(planDesc.getPeriodUnit());
								String check_out_page = check_out_page_content;
								check_out_page = check_out_page.replace("$$", subsPeriod);
								log.info("sub period : " + subsPeriod + " :: checkout content : " + check_out_page);
								response.put("check_out_popup_content", check_out_page);

							} else if (valid_bundle_subs && productSubscription == null && (curPetMonitor || !oldSubAvail) && gateway.getModel().getMonitor_type().getId() == 1) {

								response.put("navigate_to_subscription", true);
								

							} else if (!valid_bundle_subs && isPetMonitor) {
								response.put("stay_warranty_page", stay_warranty_page);
								response.put("stay_warranty_page_content", warranty_success);
								response.put("stay_warranty_page_content_v2_image", warranty_success_sec_dev_v2_image);
								response.put("stay_warranty_page_content_v2_image_dark",
										warranty_success_sec_dev_v2_image_dark);
							}

						}

						if (!reqFrom.equalsIgnoreCase("warranty")) {
							response.put("show_check_out_popup", false);
							response.put("subscription_buynow_popup", false);
							response.put("show_payment_page", false);
							response.put("navigate_to_subscription", false);
						}

					
					} else {
						mailContent = mailContent + rechargeResponse.get("mail_content");
					}

				}

			}

		} catch (Exception e) {
			log.error("Error occured in ordermap :: auth : " + auth);
			response.put("Status", 0);
			String eRes = RegisterUserError.warrantySupportMsg;
			response.put("Msg", "Error! " + eRes);

			mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
			mailContent = mailContent + "<p> Order Mapping Status : Failed </p>";
			mailContent = mailContent + "<p> Error Msg : " + e.getLocalizedMessage() + " </p>";
		} finally {
			
			if( bundledOrderFailed ) {
			
				mailSub = "Failed : Bundle Device Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Already have paid plan </p>";
				mailContent = mailContent + "<br>Thanks,<br> Irisservice ";
				async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);	
				
			} else {
				mailContent = mailContent + "<br>Thanks,<br> Irisservice ";
				async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);		
			}
		
		}
		response.put("Return Time", System.currentTimeMillis());
		
		if((boolean)response.get("show_check_out_popup")) {
			title = "Woohoo!";
			desc2 = "Your warranty is successfully registered \n"
					+ "					Your subscription is one step away \n"
					+ "					Please enter your patment details for \n"
					+ "					Uninterrupted Pet Prodection \n"
					+ "					You will not be charged anything \n until your next renewal*";
			img_url = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/ordermap_card.png";
			desc1 = "You're almost there!";
	
		}else if((boolean)response.get("stay_warranty_page")) {
			title = "Woohoo!";
			title = "Hooray!";
			if (!orderChannel.equalsIgnoreCase("others"))
				desc1 = "Your warranty is registered successfully.";
			else
				desc1 = "Please contact support to register your warranty.";
			desc2 = "Get one for your other device too!";
			img_url = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/ordermap.png";
	
		}else if((boolean)response.get("navigate_to_subscription")) {
			title = "Hooray!";
			desc2 = "Subscribe & Activate Your alerts now!";
			img_url = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/ordermap.png";
			
			response.put("title", title);
			if (!orderChannel.equalsIgnoreCase("others"))
				desc1 = "Your warranty is registered successfully.";
			else
				desc1 = "Please contact support to register your warranty.";
	
		}else if((boolean)response.get("subscription_buynow_popup")) {
			title = "Hooray!";
			desc2 = "Subscribe & Activate Your alerts now!";
			img_url = "https://nimbleapi-images.s3.us-west-2.amazonaws.com/mobileappimages/others/ordermap.png";
			
			response.put("title", title);
			if (!orderChannel.equalsIgnoreCase("others"))
				desc1 = "Your warranty is registered successfully.";
			else
				desc1 = "Please contact support to register your warranty.";
	
		}

		response.put("title", title);
		response.put("desc1", desc1);
		response.put("desc2", desc2);
		response.put("img_url", img_url);
		return response;
	}
	// balaji
	@RequestMapping(value = "v4.0/ordermap/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse orderMap(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "orderid", required = true) String orderId,
			@RequestParam(value = "purchasedfrom", required = true) String orderChannel,
			@RequestParam(value = "gatewayid", required = true) long gatewayId,
			@RequestParam(value = "remark", required = false, defaultValue = "NA") String remark,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "show_subs_popup", required = false) boolean show_subs_popup,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "req_ver", defaultValue = "V1", required = false) String reqVer,
			@RequestParam(value = "req_from", defaultValue = "home", required = false) String reqFrom,
			@RequestParam(value = "valid_bundle_subs", required = false) boolean valid_bundle_subs,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country,
			@RequestParam(value = "app_type", defaultValue = "", required = false) String app_type) {
		String auth = header.getFirst("auth");
		log.info("Entered into orderMap : authKey : " + auth);
		JResponse response = new JResponse();
		UserV4 user = null;
		String to_address = null;
		String cc_address = null;
		String bcc_address = null;
		String mailSub = "";
		if (device_country.equalsIgnoreCase("US") || device_country.equalsIgnoreCase("NA")
				|| device_country.toLowerCase().contains("india") || device_country.equalsIgnoreCase("in")
				|| device_country.isEmpty() || device_country == null) {
			device_country = "US";
		}
		String supportM = supportContactEmail.get(device_country);
		String supportP = supportContactNumber.get(device_country);

		boolean pFromStatus = false;

//		String contactMsg = "<br>Please contact us at <br><font color='blue'> <a href=\"tel: ************\"><b>************</b></a></font> or <font color='blue'><a href=\"mailto:<EMAIL>\"><b><EMAIL></b></a></font>";
		String mailContent = "<p>Hi Team,</p>" + "<p>Find the user order mapping details</p>";
		try {
			Properties prop = new Properties();
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			prop.load(new FileInputStream(file));

			to_address = prop.getProperty("to_address");
			cc_address = prop.getProperty("cc_address");
			bcc_address = prop.getProperty("bcc_address");

			try {
				user = userServiceV4.verifyAuthV3("authkey", auth);
				String country = user.getCountry().toUpperCase();
				if (country == null || country.isEmpty() || country.equalsIgnoreCase("US")
						|| country.equalsIgnoreCase("NA") || country.toLowerCase().contains("india")
						|| country.equalsIgnoreCase("in")) {
					country = "US";
				}

				supportM = supportContactEmail.get(country);
				supportP = supportContactNumber.get(country);

				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

			} catch (InvalidAuthoException e) {

				log.error("Invalid Authkey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "<center><p>User not found</p></center>");
				response.put("Msg_V2", "User not found. " + RegisterUserError.warrantySupportMsg);
				mailSub = "Failed : External Order Mapping Status-Email : ";
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ " <p> Error Msg : User not found </p>";
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			mailContent = mailContent + "<p>Name : " + user.getFirstname() + " " + user.getLastname() + "</p>"
					+ "<p>Purchased from : " + orderChannel + " </p>" + "<p>Email : " + user.getEmail() + "  </p>"
					+ "<p>Phone : " + user.getMobileno() + "  </p>" + "<p>Order ID : " + orderId + "</p>";

			Gson gson = new Gson();
			Orders order = new Orders();
			boolean orderMappedStatus = false;
			String subscriptionStatus = "0";

			Gateway gateway = gatewayService.getGatewayByid(gatewayId);

			if (gateway == null) {
				log.error("Gateway not found :: gateway id : " + gatewayId);
				response.put("Status", 0);
				String eRes = RegisterUserError.contactMsg;
				String msg = "<center><p>Unable to fetch your monitor details. "
						+ eRes.replace("#SP#", supportP).replace("#SM#", supportM) + "</p></center>";
				if (app_type.equalsIgnoreCase("flutter")) {
					eRes = RegisterUserError.warrantySupportMsg;
					msg = "Unable to fetch your monitor details. " + eRes;
				}
				String msgV2 = "Unable to fetch your monitor details. ";
				
				response.put("Msg", msg);
				response.put("Msg_V2", msgV2 + RegisterUserError.warrantySupportMsg);

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ "<p> Error Msg : Gateway details not found </p>";
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			mailContent = mailContent + "<p> Meid : " + gateway.getMeid() + "</p>";
			Inventory inventory = niomDbservice.getInventoryByMeid(gateway.getMeid());

			if (inventory == null) {
				log.error("Meid not found in inventory :: meid : " + gateway.getMeid());
				response.put("Status", 0);
				String eRes = RegisterUserError.contactMsg;
				String msg = "<center><p>Unable to fetch your monitor details. "
						+ eRes.replace("#SP#", supportP).replace("#SM#", supportM) + "</p></center>";
				if (app_type.equalsIgnoreCase("flutter")) {
					eRes = RegisterUserError.warrantySupportMsg;
					msg = "Unable to fetch your monitor details. " + eRes;
				}
				
				String msgV2 = "Unable to fetch your monitor details. ";
				
				response.put("Msg", msg);
				response.put("Msg_V2", msgV2 + RegisterUserError.warrantySupportMsg);
				
				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ "<p> Error Msg : Inventory not found </p>";
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (orderChannel.equalsIgnoreCase("rv")) {
				pFromStatus = false;
				subscriptionStatus = "1";
			} else if (orderChannel.equalsIgnoreCase("others")) {
				pFromStatus = true;
				boolean isUpdateOrderMappingDetails = userServiceV4.updateOrderMappingDetails(user.getId(),
						inventory.getQrc(), "NA", remark, "NA", "NA", "");

				gatewayService.changeGatewayOrderidStatus(gateway.getId(), pFromStatus);

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p>Remark : " + remark + "  </p>" + "<p>Order Mapping Status : Failed </p>"
						+ " <p> Error Msg : Order purchased from other source : " + remark + "</p>";
				response.put("Return Time", System.currentTimeMillis());

				if (reqVer.equalsIgnoreCase("V1")) {
					response.put("Status", 0);
					String eRes = RegisterUserError.contactMsg;
					String msg = "<center><p>" + eRes.replace("#SP#", supportP).replace("#SM#", supportM)
							+ ". </p></center>";
					
					if (app_type.equalsIgnoreCase("flutter")) {
						msg = RegisterUserError.warrantyErrorMsg;
						response.put("Msg", msg);
						response.put("Msg_V2", RegisterUserError.warrantySupportMsg);
					} else

						response.put("Msg", "<center><p>" + msg + " to register your warranty.</p></center>");
						response.put("Msg_V2", RegisterUserError.warrantySupportMsg );
				} else {

					List<JGateway> jGatewayList = gatewayService.getJGatewayByUser(user.getId(), "");
					boolean stay_warranty_page = false;

					if (jGatewayList != null) {

						for (JGateway gate : jGatewayList) {
							if (!gate.isShowOrderId() && !gate.isPurchased_from_others()) {
								stay_warranty_page = true;
								break;
							}
						}

					}

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("Msg_V2", "Success");
					response.put("show_payment_page", false);
					response.put("subscription_buynow_popup", false);
					response.put("show_check_out_popup", false);
					response.put("subscription_buynow_content", subscriptionBuyNowContent);
					response.put("check_out_popup_content", check_out_page_content);
					response.put("check_out_popup_image", check_out_page_image);
					response.put("check_out_popup_image_dark", check_out_page_image_dark);
					response.put("bundle_check_out_url", "NA");

					response.put("navigate_to_subscription", false);
					response.put("navigate_to_subs_content", warranty_success_subs);
					response.put("navigate_to_subs_content_v2_image", others_warranty_success_subs_v2_image);
					response.put("navigate_to_subs_content_v2_image_dark", others_warranty_success_subs_v2_image_dark);
					
					response.put("stay_warranty_page", false);
					response.put("stay_warranty_page_content", warranty_success_sec_dev);
					response.put("stay_warranty_page_content_v2_image", others_warranty_success_sec_dev_v2_image);
					response.put("stay_warranty_page_content_v2_image_dark", others_warranty_success_sec_dev_v2_image_dark);

					String eRes = RegisterUserError.warrantyErrorMsg;
					String msg = "<center><p>" + eRes + ". </p></center>";

					if (!reqFrom.equalsIgnoreCase("warranty")) {
						response.put("show_check_out_popup", false);
						response.put("subscription_buynow_popup", false);
						response.put("show_payment_page", false);
						response.put("navigate_to_subscription", false);
						return response;
					}

					if (stay_warranty_page) {
						response.put("stay_warranty_page", true);
						String content = warranty_success_sec_dev
								.replace("</br>Your warranty is registered successfully.", msg);
						response.put("stay_warranty_page_content", content);
						return response;
					}

					if (valid_bundle_subs) {
						response.put("navigate_to_subscription", true);
						String content = warranty_success_subs.replace("</br>Your warranty is registered successfully.",
								msg);
						response.put("navigate_to_subs_content", content);
						return response;
					}

					response.put("stay_warranty_page", true);
					String content = warranty_success.replace("</br>Your warranty is registered successfully.", msg);
					response.put("stay_warranty_page_content", content);
					response.put("stay_warranty_page_content_v2_image", others_warranty_success_v2_image);
					response.put("stay_warranty_page_content_v2_image_dark", others_warranty_success_v2_image_dark);
					return response;

				}

				return response;
			}

			mailContent = mailContent + "<p>QRC : " + inventory.getQrc() + " </p>";
			mailContent = mailContent + "<p>Device Model in Inventory : " + inventory.getDevicemodelnumber() + "</p>";

			JSONObject jorderIdCheckResponse = new JSONObject();
			jorderIdCheckResponse = userService.getNiomGetOrderCount(orderChannel, orderId);

			int orderIdCheckStatus = jorderIdCheckResponse.getInt("Status");

			if (orderIdCheckStatus > 0) {
				order = gson.fromJson(jorderIdCheckResponse.getJSONObject("Order").toString(), Orders.class);
				int totalOrderCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalordered"));
				int totalMappedCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalmapped"));

				if (totalMappedCount >= totalOrderCount) {
					response.put("Status", 0);
					String eRes = RegisterUserError.contactMsg;
					String msg = "<center><p>Invalid Order ID. "
							+ eRes.replace("#SP#", supportP).replace("#SM#", supportM) + ".</p></center>";
					if (app_type.equalsIgnoreCase("flutter")) {
						eRes = RegisterUserError.warrantySupportMsg;
						msg = "Invalid Order ID. " + eRes;
					}
					
					String msgV2 = "Invalid Order ID. ";
					
					response.put("Msg", msg);
					response.put("Msg_V2", msgV2 + RegisterUserError.warrantySupportMsg);
					
					mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
					mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
							+ "<p> Error Msg : Order Id is already registered </p>";
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				mailContent = mailContent + "<p>Device Model in Orders : " + order.getDevicemodel() + "</p>";
				mailContent = mailContent + "<p>Order Account Type ID in Orders : "
						+ order.getOrder_acc_type().getAcc_type() + "</p>";

				log.info("Order id found in niom. Order Channel : " + order.getOrder_acc_type().getAcc_type()
						+ "Order ID : " + order.getOrder_id() + "Out Order ID  :" + order.getExternal_order_id());

				if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("amazon")) {
					if (order.getBilling_email().toLowerCase().contains("na")
							|| order.getBilling_phone().toLowerCase().contains("na")) {
						order.setBilling_email(user.getEmail());
						order.setBilling_phone(user.getMobileno());
						order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
						order.setWelcome_status(order.getWelcome_status());
						userService.checkFirstNameAndLastNameInOrder(order, user);
						boolean updateExternalOrderData = niomDbservice.updateExternalOrdersInNiom(order);
					}
				} else if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("walmart")) {
					if (order.getBilling_email().toLowerCase().contains("na")
							|| order.getBilling_phone().toLowerCase().contains("na")) {
						order.setBilling_email(user.getEmail());
						order.setBilling_phone(user.getMobileno());
						order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
						order.setWelcome_status(order.getWelcome_status());
						userService.checkFirstNameAndLastNameInOrder(order, user);
						boolean updateExternalOrderData = niomDbservice.updateExternalOrdersInNiom(order);
					}
				} else if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("technorv")) {
					if (order.getBilling_email().toLowerCase().contains("na")
							|| order.getBilling_phone().toLowerCase().contains("na")) {
						order.setBilling_email(user.getEmail());
						order.setBilling_phone(user.getMobileno());
						order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
						order.setWelcome_status(order.getWelcome_status());
						userService.checkFirstNameAndLastNameInOrder(order, user);
						boolean updateExternalOrderData = niomDbservice.updateExternalOrdersInNiom(order);
					}
				}

				orderMappedStatus = userService.orderMapping(inventory.getMeid(), _helper.getCurrentTimeinUTC(),
						inventory.getDevicestate().getId(), order.getOrder_id() + "", order.getDevicemodel(),
						subscriptionStatus, "1", "manual", gatewayId);

				if (!orderMappedStatus) {
					response.put("Status", 0);
					String eRes = RegisterUserError.contactMsg;
					String msg = "<center><p>Unable to Claim the Warranty. "
							+ eRes.replace("#SP#", supportP).replace("#SM#", supportM) + ". </p></center>";
					if (app_type.equalsIgnoreCase("flutter")) {
						eRes = RegisterUserError.warrantySupportMsg;
						msg = "Unable to Claim the Warranty. " + eRes;
					}
					String msgV2 = "Unable to Claim the Warranty. ";
					
					response.put("Msg", msg);
					response.put("Msg_V2", msgV2 + RegisterUserError.warrantySupportMsg);

					String[] deviceModel = order.getDevicemodel().split("\\.");
					boolean deviceModelMisMatch = true;

					for (int index = 0; index < deviceModel.length; index++) {
						if (inventory.getDevicemodelnumber().contains(deviceModel[index])) {
							deviceModelMisMatch = false;
						}
					}

					mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
					mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
							+ "<p> Error Msg : Unable to map order";

					if (deviceModelMisMatch) {
						mailContent = mailContent + ", device model number mismatch </p>";
					} else {
						mailContent = mailContent + "</p>";
					}

					response.put("Return Time", System.currentTimeMillis());
					return response;
				} else {
					boolean isUserId = crService.updateUserIdInProsuctSubs(String.valueOf(order.getOrder_id()),
							user.getId());
				}

			} else {
				String eRes = RegisterUserError.contactMsg;
				String errorMsg = eRes.replace("#SP#", supportP).replace("#SM#", supportM);
				response.put("Status", 0);
				if (app_type.equalsIgnoreCase("flutter")) {
					eRes = RegisterUserError.warrantySupportMsg;
					response.put("Msg", "Sorry, we couldn't find your Order ID. " + eRes);
					response.put("Msg_V2", "Sorry, we couldn't find your Order ID. "+ RegisterUserError.warrantySupportMsg);
				} else
					response.put("Msg", "<center><p>Unable to find your Order ID " + errorMsg + ".</p></center>");

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ "<p> Error Msg : Order Id not found </p>";

				log.info("Order id not found in niom, Error Code : ER039 :" + RegisterUserError.ER039);

				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			niomDbservice.updateOrdermapUserDetails(order.getOrder_id(), user.getUsername(), user.getId() + "", "NA");

			boolean updateExternalOrderDataStatus = userService.updateOrdersDataV2(orderChannel, order, inventory);

			if (orderMappedStatus) {

				boolean isUpdateOrderMappingDetails = userServiceV4.updateOrderMappingDetails(user.getId(),
						inventory.getQrc(), orderId, orderChannel, "6", order.getOrder_sku(), order.getDatetime());

				gatewayService.changeGatewayOrderidStatus(gateway.getId(), pFromStatus);
				int remainingdays = calculateWarrantyDays(order.getDatetime());
				String msg = "You have " + remainingdays + " days of warranty left";

				if (remainingdays <= 0) {
					msg = "Your warranty has expired";
				}

				// Update oder details in device subscription table
				async.updateSalesChannel(user.getId()+"", orderChannel, gatewayId, gateway.getMeid(), orderId,gateway.getModel().getMonitor_type().getId());
				
//				String bundleOrderId = _helper.getExternalConfigValue("budled_orderids", externalConfigService);
//				List<String> bundledOrderIdList = new ArrayList<String>();
//				bundledOrderIdList = Arrays.asList( bundleOrderId.split(",") );

				boolean redirectPaymentPage = redirectPaymentPage_config;
				boolean subscriptionBuyNowPopUp = subscriptionBuyNowPopUp_config;

//				if( bundledOrderIdList.contains( orderId ) ) {
//					
//					subscriptionBuyNowPopUp = false;
//					
//					//redirectPaymentPage = true; ** no need it will take config value **
//					
//				} else {
//					//subscriptionBuyNowPopUp =  true; ** no need it will take config value **
//					
//					if( show_subs_popup ) {
//			,			subscriptionBuyNowPopUp = false;
//					}
//					
//					redirectPaymentPage = false;
//				}

				response.put("Status", 1);
				response.put("Msg", msg);
				response.put("Msg_V2", msg);
				response.put("show_payment_page", false);
				response.put("subscription_buynow_popup", false);
				response.put("show_check_out_popup", false);
				response.put("subscription_buynow_content", subscriptionBuyNowContent);
				response.put("check_out_popup_content", check_out_page_content);
				response.put("check_out_popup_image", check_out_page_image);
				response.put("check_out_popup_image_dark", check_out_page_image_dark);
				response.put("bundle_check_out_url", "NA");

				response.put("navigate_to_subscription", false);
				response.put("navigate_to_subs_content", warranty_success_subs);
				response.put("navigate_to_subs_content_v2_image", warranty_success_subs_v2_image);
				response.put("navigate_to_subs_content_v2_image_dark", warranty_success_subs_v2_image_dark);

				response.put("stay_warranty_page", false);
				response.put("stay_warranty_page_content", warranty_success_sec_dev);
				response.put("stay_warranty_page_content_v2_image", warranty_success_sec_dev_v2_image);
				response.put("stay_warranty_page_content_v2_image_dark", warranty_success_sec_dev_v2_image_dark);
				

				JResponse rechargeResponse = reService.createRechargeSubscription(user.getUsername(),
						user.getChargebeeid(), user.getId(), String.valueOf(order.getOrder_id()),gateway.getId(),gateway.getModel().getMonitor_type().getId());

				if (rechargeResponse.getResponse().containsKey("mail_content")) {

					if (!((boolean) rechargeResponse.get("is_recharge"))) {
						ArrayList<String> subInfo = reService
								.getRechargeSubscriptinInfo(String.valueOf(order.getOrder_id()));
						if (subInfo.isEmpty()) {
							mailContent = mailContent + rechargeResponse.get("mail_content");
						} else {
							mailContent = mailContent + "<p> Recharage customer : true </p>";
							mailContent = mailContent
									+ "<p> This customer already have recharge plan in chargebee </p>";
						}
					} else {
						mailContent = mailContent + rechargeResponse.get("mail_content");
					}

				}

//				// Activate CB subscription
//				String email = user.getUsername();
//				ArrayList<String> subDetails =reService.getReSubscription(email,String.valueOf(order.getOrder_id()));
//				String cbPlan = "NA";
//				String recharge_cusid = "NA";
//				String email_match ="";
//				String reSubId = "NA";
//				boolean is_email_match = false;
//				String nextrenewal_at = null;
//				String subs_status = "";
//				String price ="0.0";
//				
//				if(!subDetails.isEmpty()) {
//					recharge_cusid = subDetails.get(0);
//					cbPlan = subDetails.get(1);
//					reSubId = subDetails.get(2);
//					is_email_match= subDetails.get(3).equalsIgnoreCase(email) ? true : false;
//					nextrenewal_at= subDetails.get(4);
//					price = subDetails.get(5);
//					mailContent = mailContent + "<p> Recharage customer : true </p>";
//					if(!is_email_match) {
//						mailContent = mailContent + "<p>Subscription Status :  Recharage Purchase email and signup email are different.</p>";
//					}
//				}else {
//					mailContent = mailContent + "<p> Recharage customer : false </p>";
//				}
//				
//				if(!cbPlan.equalsIgnoreCase("NA") ) {
//					if(!is_email_match) {
//						log.info("Recharge Subscription not created in CB for orderid:"+order.getOrder_id());
//					} else {
//						String status = cbService.createCBSubsForRecharge("NA", user.getChargebeeid(), cbPlan,
//								reSubId,nextrenewal_at,price);
//						
//						if (!status.equalsIgnoreCase("Failed")) {
//							String cb_status = "NA";
//							if (status.equalsIgnoreCase("Success")) {
//								mailContent = mailContent + "<p> Recharge subscription Created in CB</p>";
//								cb_status = "Sub_Created";	
//							}
//							else {
//								mailContent = mailContent + "<p> Paid Subcription already available, So recharge bundle subscription"
//										+ " not created. In order to activate bundle subscription need to activate manually in CB</p>";
//								cb_status = "Sub_Available";
//							}
//							
//													
//							boolean stat1 = crService.updateSubStatus(String.valueOf(order.getOrder_id()),
//									String.valueOf(user.getId()));
//							log.info("update product_subscription:" + stat1);
//
//							stat1 = crService.updateReSubStatus(String.valueOf(order.getOrder_id()),cb_status);
//							log.info("update updateReSubStatus:" + stat1);
//							
//							String qry = "update user set recharge_custid='" + recharge_cusid + "' where id="
//									+ user.getId() + " ;";
//							int stat2 = userServiceV4.executeQuery(qry);
//
//							log.info("update user:" + user.getId() + " recustid:" + recharge_cusid + " :" + stat2);
//						}
//					}
//					
//					redirectPaymentPage = false;
//					
//					response.put("show_payment_page", redirectPaymentPage);
//					response.put("stay_warranty_page_content", warranty_success);
//					response.put("stay_warranty_page", true);
//				}else {

				if (!((boolean) rechargeResponse.get("is_recharge"))) {

					if (reqVer.equals("V1")) {
						JProductSubscription jProductSubscription = crService
								.getCBPlan(String.valueOf(order.getOrder_id()));

						if (jProductSubscription != null && !jProductSubscription.getPlan_id().equalsIgnoreCase("NA")
								&& !jProductSubscription.getPlan_id().isEmpty() && product_subs_enable) {

							// 1753-11-11 11:11:11
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
							Date orderedDate = sdf.parse(jProductSubscription.getOrder_date());

							Calendar cal = Calendar.getInstance();
							cal.setTime(orderedDate);
							cal.add(Calendar.DATE, jProductSubscription.getSubscription_period_days() + 21); // Adding
																												// plan
																												// expired
																												// date
							String output = sdf.format(cal.getTime());
							Date subsEndDate = cal.getTime();

							cal.setTime(new Date());
							Date curDate = cal.getTime();
							// System.out.println( sdf.format( cal.getTime() ) );
							boolean stat = false;
							if (subsEndDate.after(curDate)) {
								cal.setTime(orderedDate);
								cal.add(Calendar.DATE, 21); // Adding 21 days into ordered date
								Date purchasedDate = cal.getTime();
								// System.out.println( sdf.format( cal.getTime() ) );

								if (purchasedDate.after(curDate)) {
									stat = cbService.createPaidSubscription(user.getChargebeeid(), jProductSubscription,
											new Date());
								} else {
									stat = cbService.createPaidSubscription(user.getChargebeeid(), jProductSubscription,
											purchasedDate);
								}

							} else {
								redirectPaymentPage = false;
							}
							if (stat) {
								subscriptionBuyNowPopUp = false;
								stat = crService.updateSubStatus(String.valueOf(order.getOrder_id()),
										String.valueOf(user.getId()));
							}
						} else {
							if (show_subs_popup) {
								subscriptionBuyNowPopUp = false;
							}
							redirectPaymentPage = false;
						}

						if (redirectPaymentPage && !user.getChargebeeid().equalsIgnoreCase("NA")) {

							boolean isPaymentSrcAvail = chargebeeService.checkCardDetails(user.getChargebeeid());
							log.info("Card details status : " + isPaymentSrcAvail);
							if (!isPaymentSrcAvail) {
								String paymentURL = chargebeeService.generatePaymentURL(user.getChargebeeid());

								if (paymentURL != null) {
									response.put("show_payment_page", redirectPaymentPage);
									response.put("payment_page_content", redirectPaymentPageContent);
									response.put("payment_page_url", paymentURL);

									msg = "Hurray! Your subscription is activated successfully. " + msg;
									response.put("Msg", msg);
								}
							}

						}

						if (subscriptionBuyNowPopUp) {
							response.put("subscription_buynow_popup", subscriptionBuyNowPopUp);
							response.put("subscription_buynow_content", subscriptionBuyNowContent);
						}

					} else {

						List<JGateway> jGatewayList = gatewayService.getJGatewayByUser(user.getId(), "");
						boolean stay_warranty_page = false;

						if (jGatewayList != null) {

							for (JGateway gate : jGatewayList) {
								if (!gate.isShowOrderId() && !gate.isPurchased_from_others()) {
									stay_warranty_page = true;
									break;
								}
							}

						}

						if (stay_warranty_page) {

							response.put("stay_warranty_page", true);

						} else {

							String check_out_page_url = "NA";
							boolean show_check_out_page = this.show_check_out_page;
							ProductSubscription productSubscription = cbService.getProductSubscription(user.getId());
							
							log.info("valid_bundle_subs : "+valid_bundle_subs +" :: show_check_out_page : "+ show_check_out_page +" product_subs_enable : "+product_subs_enable);
							
							if (valid_bundle_subs && show_check_out_page && productSubscription != null
									&& !productSubscription.isIs_subscription_activated() && product_subs_enable) {

								check_out_page_url = cbService.checkOutURLForBundleSubs(productSubscription,
										user.getChargebeeid());
								if (!check_out_page_url.equalsIgnoreCase("NA")) {
									show_check_out_page = true;
									
								} else {
									show_check_out_page = false;
								}

								response.put("show_check_out_popup", show_check_out_page);
								response.put("bundle_check_out_url", check_out_page_url);
								JPlan planDesc = chargebeeService.getPlanDesc(productSubscription.getPlan_id());
								String subsPeriod = chargebeeService.getSubscriptionPeriod(planDesc.getPeriodUnit());
								String check_out_page = check_out_page_content;
								check_out_page = check_out_page.replace("$$", subsPeriod);
								log.info("sub period : " + subsPeriod + " :: checkout content : " + check_out_page);
								response.put("check_out_popup_content", check_out_page);

							} else if (valid_bundle_subs && productSubscription == null) {

								response.put("navigate_to_subscription", true);

							} else if (!valid_bundle_subs) {
								response.put("stay_warranty_page", true);
								response.put("stay_warranty_page_content", warranty_success);
								response.put("stay_warranty_page_content_v2_image", warranty_success_sec_dev_v2_image);
								response.put("stay_warranty_page_content_v2_image_dark", warranty_success_sec_dev_v2_image_dark);
							}

						}

						if (!reqFrom.equalsIgnoreCase("warranty")) {
							response.put("show_check_out_popup", false);
							response.put("subscription_buynow_popup", false);
							response.put("show_payment_page", false);
							response.put("navigate_to_subscription", false);
						}

					}
				}

				mailSub = "Success : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Success </p>";

			}

		} catch (Exception e) {
			log.error("Error occured in ordermap :: auth : " + auth);
			response.put("Status", 0);
			String eRes = RegisterUserError.contactMsg;
			String errorMsg = eRes.replace("#SP#", supportP).replace("#SM#", supportM);
			if (app_type.equalsIgnoreCase("flutter")) {
				eRes = RegisterUserError.warrantySupportMsg;
				response.put("Msg", "Error! " + eRes);
				response.put("Msg_V2", "Error! " + eRes);
			} else
				response.put("Msg", "<center><p>Error! " + errorMsg + ".</p></center>");

			mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
			mailContent = mailContent + "<p> Order Mapping Status : Failed </p>";
			mailContent = mailContent + "<p> Error Msg : " + e.getLocalizedMessage() + " </p>";
		} finally {
			mailContent = mailContent + "<br>Thanks,<br> Irisservice ";
			async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	private int calculateWarrantyDays(String orderDate) {
		String currentDate = IrisservicesUtil.getCurrentTimeUTC();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			Date orderdate = sdf.parse(orderDate);
			Date currentdate = sdf.parse(currentDate);

			long difference = currentdate.getTime() - orderdate.getTime();
			int daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
			daysBetween = 365 - daysBetween;
			return daysBetween;
		} catch (Exception e) {
			return 0;
		}
	}

}
