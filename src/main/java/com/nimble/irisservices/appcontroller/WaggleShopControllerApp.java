package com.nimble.irisservices.appcontroller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.irisservices.dto.JProductList;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JShopProducts;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.ShopBanner;
import com.nimble.irisservices.entity.ShopFeature;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.pojo.SortByOrderNoProductList;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IWaggleShopService;

@RestController
@RequestMapping("/app")
public class WaggleShopControllerApp {

	private static final Logger log = LogManager.getLogger(WaggleShopControllerApp.class);
	
	@Autowired
	IUserServiceV4 userServiceV4;
	
	@Autowired
	Helper _helper;
	
	@Autowired
	IWaggleShopService waggleShopService;
	
	@GetMapping("/v5.0/shop")
	public JResponse shopInfoV5(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam String os, @RequestParam String app_ver,
			@RequestParam(defaultValue = "V1", required = false) String req_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		
		String auth = header.getFirst("auth");
		log.info("Entered into shopInfo :: auth : "+auth+" req_ver : "+ req_ver);
		JResponse response = new JResponse();
		try {
			
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			
			log.info("username : "+user.getUsername());
			
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			List<ShopFeature> shopFeatureList = waggleShopService.getShopFeature();
			List<ShopBanner> shopBannerList = waggleShopService.getShopBanner();
			
			if( shopFeatureList == null || shopFeatureList.isEmpty() || shopBannerList == null || shopBannerList.isEmpty()) {
				response.put("Status", "0");
				response.put("Msg", "Session expired, please try again later");	
				return response;
			} 
			
			response.put("Status", "1");
			response.put("Msg", "Success");
			response.put("banner_list", shopBannerList);
			
			HashMap<String, ArrayList<JProductList> > productCatogaryMap = new HashMap<>();
			
			
			if( req_ver.equalsIgnoreCase("V2") ) {
				
				shopFeatureList.forEach( shop -> {
					
					if( shop.getVersion().equalsIgnoreCase("V2") ) {
						if(productCatogaryMap.containsKey( shop.getCategory() ) ){
							ArrayList<JProductList> product = productCatogaryMap.get( shop.getCategory() );
							product.add( new JProductList( shop.getTitle(), shop.getImg_url(), shop.getAction_url(), shop.getOrder_no(), shop.getBtn_name() ) );
						} else {
							ArrayList<JProductList> product = new ArrayList<>();
							product.add( new JProductList( shop.getTitle(), shop.getImg_url(), shop.getAction_url(), shop.getOrder_no(), shop.getBtn_name() ) );
							productCatogaryMap.put( shop.getCategory(), product);
						}
					}
					
				});
				
				ArrayList<JShopProducts> shopProductList = new ArrayList<>();
				productCatogaryMap.forEach( (catogory, productList) -> {
					JShopProducts products = new JShopProducts();
					products.setCategory_name(catogory);
					products.setProduct_list(productList);
					products.setOrder_no( productList.get(0).getOrder_no() );
					shopProductList.add(products);
				});
				
				Collections.sort(shopProductList, new SortByOrderNoProductList());
				
				response.put("products", shopProductList);
			} else {
				
				shopFeatureList = shopFeatureList
									.stream()
									.filter( shop ->  shop.getVersion().equalsIgnoreCase("V1")  )
									.collect(Collectors.toList());
				
				response.put("feature_list", shopFeatureList);
				
			}
			
		} catch (Exception e) {
			log.error("Error in shopInfo :: Error : "+e.getLocalizedMessage());
			response.put("Status", "0");
			response.put("Msg", "Session expired, please try again later");
		}
		
		return response;
	}
	
	@GetMapping("/v4.0/shop")
	public JResponse shopInfo(Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam String os, @RequestParam String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		
		String auth = header.getFirst("auth");
		log.info("Entered into shopInfo :: auth : "+auth);
		JResponse response = new JResponse();
		try {
			
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			
			log.info("username : "+user.getUsername());
			
			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			List<ShopFeature> shopFeatureList = waggleShopService.getShopFeature();
			List<ShopBanner> shopBannerList = waggleShopService.getShopBanner();
			
			if( shopFeatureList == null || shopFeatureList.isEmpty() || shopBannerList == null || shopBannerList.isEmpty()) {
				response.put("Status", "0");
				response.put("Msg", "Session expired, please try again later");	
				return response;
			} 
			
			response.put("Status", "1");
			response.put("Msg", "Success");
			response.put("feature_list", shopFeatureList);
			response.put("banner_list", shopBannerList);
			
		} catch (Exception e) {
			log.error("Error in shopInfo :: Error : "+e.getLocalizedMessage());
			response.put("Status", "0");
			response.put("Msg", "Session expired, please try again later");
		}
		
		return response;
	}
	
}
