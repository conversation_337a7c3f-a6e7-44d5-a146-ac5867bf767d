package com.nimble.irisservices.appcontroller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dto.AmazonReviewStatus;
import com.nimble.irisservices.dto.AmazonUserReview;
import com.nimble.irisservices.dto.GeneralReview;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JUserFeedback;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.OrderMappingDetails;
import com.nimble.irisservices.entity.UserFeedbackTransaction;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IReportServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
@RequestMapping("/app")
public class FeedbackControllerApp {

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	ICreditSystemService crService;

	@Value("${amazonrateuscount}")
	private int amazonrateuscount;

	@Value("${amazonredirecturl}")
	private String amazonredirecturl;

	@Value("${walmartredirecturl}")
	private String walmartredirecturl;

	@Value("${showamazonrateus}")
	private boolean showamazonrateus;

	@Value("${showfeedback}")
	private boolean showfeedback;

	@Value("${popup_orderid_feedback}")
	private boolean popup_orderid_feedback = false;

	@Value("${reportcount_rating}")
	private int reportCount = 200;

	@Autowired
	@Lazy
	IReportServiceV4 rptServicev4;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	Helper _helper;
	
	@Value("${amazonredirectsburl}")
	private String amazonredirectsburl;
	
	@Value("${amazonredirectwcurl}")
	private String amazonredirectwcurl;
	
	@Value("${amazonredirectmcurl}")
	private String amazonredirectmcurl;
	
	@Value("${amazonredirectwcpurl}")
	private String amazonredirectwcpurl;
	
	@Value("${amazonredirectnsurl}")
	private String amazonredirectnsurl;

	private static final Logger log = LogManager.getLogger(FeedbackControllerApp.class);

	public FeedbackControllerApp() {
		// TODO Auto-generated constructor stub
	}
	
	@RequestMapping(value = "v5.0/updateuserfeedback/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateUserFeedbackV5(@RequestParam("option") String option,
			@RequestParam("formid") long formid,
			@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "reviewtype", defaultValue = "NA", required = false) String reviewtype,
			@RequestParam(value = "comments", defaultValue = "NA", required = false) String comments,
			@RequestParam(value = "category", defaultValue = "NA", required = false) String category,
			@RequestParam(value = "skip", defaultValue = "false") boolean skip,
			@RequestParam(value = "monitorType", defaultValue = "1", required = false) String monitorType) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering updateuserfeedback : " + autho);

		// option 1-never; 2-later; 3-now

		UserV4 usr = null;
		try {
			usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr != null) {

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				UserFeedbackTransaction uftObj = userServiceV4.getUserFeedbackTransactionWithMonitor(usr.getId(), formid, Long.valueOf(monitorType) );
				option = option.trim();
				boolean show = false;
				boolean close = false;

				if (option.equalsIgnoreCase("1")) {
					show = false;
					close = true;
				} else if (option.equalsIgnoreCase("2")) {
					show = false;
					close = false;
				} else if (option.equalsIgnoreCase("3")) {
					show = true;
					close = false;
				}

				String cutUtc = IrisservicesUtil.getCurrentTimeUTC();

				if (uftObj != null) {
					int retry = uftObj.getRetry_count() - 1;

					if (retry < 0)
						retry = 0;

					uftObj.setShow(show);
					uftObj.setClose_form(close);
					uftObj.setRetry_count(retry);
					uftObj.setUpdatedon(cutUtc);
					uftObj.setReviewtype(category);
					uftObj.setComments(comments);
					uftObj.setSkip(skip);
					uftObj.setMonitor_type(Long.valueOf(monitorType));
				} else {
					uftObj = new UserFeedbackTransaction(usr.getId(), formid, cutUtc, cutUtc, show, close,category,  comments, skip, Long.valueOf(monitorType));
					uftObj.setRetry_count(2);
				}
				boolean stat = userServiceV4.saveOrUpdateUserFeedback(uftObj);

				if (stat) {
					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Failed");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (InvalidAuthoException e) {
			log.error("Invalid Authkey :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}
	
	@RequestMapping(value = "v5.0/getratingandfeedback/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getRatingAndFeedbackV5(@RequestParam("gatewayid") long gatewayid,
			@RequestParam("requestfrom") String requestfrom, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "monitorType", defaultValue = "1", required = false) long monitorType) {
		String auth = header.getFirst("auth");
		log.info("Entered into get ratingandfeedback : autho : " + auth);
		JResponse response = new JResponse();

		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			
			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			Gateway gateway = null;
			if (gatewayid != 0) {
				gateway = gatewayService.getGatewayByid(gatewayid);
				if (gateway == null) {
					response.put("Status", 0);
					response.put("Msg", "Gateway not found");
					return response;
				}
			}
			if (usr != null) {
				boolean showlink = false;
				String showtype = "";
				String msg = "";
				String lastRptDate = userServiceV4.getLastFeedbackDate(usr.getId(),monitorType);
				String currenttime = IrisservicesUtil.getCurrentTimeUTC();

				final SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
				long diffInHrs = 0;
				Date curr = formatter.parse(currenttime);
				Date pre = null;

				if (lastRptDate != null) {
					pre = formatter.parse(lastRptDate);

					diffInHrs = ((curr.getTime() - pre.getTime()) / 3600000);
				} else {
					diffInHrs = 25; // for very first transaction date will be null. so initializing to 25 hrs
				}

				if (requestfrom.equalsIgnoreCase("settingspage-rating")) {
					boolean show_dialog = true;

					String redirecturl = "";
					String redirect_type = "internal";
					msg = "Rate us";

					ArrayList<OrderMappingDetails> orderList = userServiceV4.getOrderMappingListByUser(usr.getId());

					for (OrderMappingDetails orderMap : orderList) {
						if (orderMap.getOrderchannel().equalsIgnoreCase("amazon")) {
							redirecturl = amazonredirecturl;
							redirect_type = "external";
							msg = "Would you like to rate us on Amazon!";
							break;
						} else if (orderMap.getOrderchannel().equalsIgnoreCase("walmart")) {
							redirecturl = walmartredirecturl;
							redirect_type = "external";
							msg = "Would you like to rate us on Walmart!";
						}
					}

					AmazonReviewStatus reviewStatus = new AmazonReviewStatus(redirecturl, show_dialog, 0,
							redirect_type);
					reviewStatus.setAmazon_msg(msg);

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("showlink", true);
					response.put("showtype", "rating");
					response.put("reviewstatus", reviewStatus);

				} else if (showfeedback) {
					// check this feed back sent to user in last x days
					if (monitorType == 1 &&( diffInHrs >= 24 || (requestfrom.equalsIgnoreCase("vpm")))) {
						JUserFeedback jUserFeedback = userServiceV4.getFeedbackLink(usr.getId(), requestfrom);

						if (jUserFeedback != null) {
							showlink = true;
							showtype = "feedback";
							msg = "Success";

							if (requestfrom.equalsIgnoreCase("vpm"))
								jUserFeedback.setTitle("Do you like our WaggleVet?");

							response.put("feedbackstatus", jUserFeedback);
						} else if (popup_orderid_feedback && gateway != null && (!gateway.isShowOrderId() && !gateway.isPurchased_from_others())) {
							Date currTime = formatter.parse(currenttime);
							long gatewayTimeDiff = ((currTime.getTime() - gateway.getInstalled_date().getTime())
									/ 3600000);

							JUserFeedback jUserFeedbackOrderId = userServiceV4.getFeedbackLink(usr.getId(), "orderid");
							if (jUserFeedbackOrderId != null && gatewayTimeDiff >= 5) {
								showlink = true;
								showtype = "orderid";
								msg = "Success";
								response.put("feedbackstatus", jUserFeedbackOrderId);
							} else if (showamazonrateus && monitorType != 3 && monitorType !=4 ) {
								JResponse ratingResponse = showAmazonRateUs(usr, gatewayid, monitorType);
								AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse()
										.get("reviewstatus");
								msg = (String) ratingResponse.getResponse().get("msg");
								if (reviewsStatus != null) {
									response.put("reviewstatus", reviewsStatus);
									showlink = true;
									showtype = "rating";
									msg = "Success";
								} else {
									showlink = false;
									showtype = "rating";
								}
							}

						} else if (showamazonrateus && monitorType != 3 && monitorType !=4) {
							JResponse ratingResponse = showAmazonRateUs(usr, gatewayid, monitorType);
							AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse()
									.get("reviewstatus");
							msg = (String) ratingResponse.getResponse().get("msg");
							if (reviewsStatus != null) {
								response.put("reviewstatus", reviewsStatus);
								showlink = true;
								showtype = "rating";
								msg = "Success";
							} else {
								showlink = false;
								showtype = "rating";
							}
						} else {
							showlink = false;
							showtype = "feedback";
							msg = "Feedback/Rating not applicable";
						}
					} // end of >24 hrs
					else if (showamazonrateus && monitorType != 3 && monitorType !=4) {
						JResponse ratingResponse = showAmazonRateUs(usr, gatewayid, monitorType);
						AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse()
								.get("reviewstatus");
						msg = (String) ratingResponse.getResponse().get("msg");
						if (reviewsStatus != null) {
							response.put("reviewstatus", reviewsStatus);
							showlink = true;
							showtype = "rating";
							msg = "Success";
						} else {
							showlink = false;
							showtype = "rating";
						}
					} else {
						showlink = false;
						showtype = "feedback";
						msg = "Feedback/Rating submitted. will be shown after 24 hrs from last transaction";
					}
					response.put("Status", 1);
					response.put("Msg", msg);
					response.put("showlink", showlink);
					response.put("showtype", showtype);

				} else if (diffInHrs >= 24 && popup_orderid_feedback && gateway != null && (!gateway.isShowOrderId() && !gateway.isPurchased_from_others())) {
					Date currTime = formatter.parse(currenttime);
					long gatewayTimeDiff = ((currTime.getTime() - gateway.getInstalled_date().getTime()) / 3600000);
					JUserFeedback jUserFeedback = userServiceV4.getFeedbackLink(usr.getId(), "orderid");
					if (jUserFeedback != null && gatewayTimeDiff >= 5) {
						showlink = true;
						showtype = "orderid";
						msg = "Success";
						response.put("feedbackstatus", jUserFeedback);
					} else if (showamazonrateus  && monitorType != 3 && monitorType !=4) {
						JResponse ratingResponse = showAmazonRateUs(usr, gatewayid, monitorType);
						AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse()
								.get("reviewstatus");
						msg = (String) ratingResponse.getResponse().get("msg");
						if (reviewsStatus != null) {
							response.put("reviewstatus", reviewsStatus);
							showlink = true;
							showtype = "rating";
							msg = "Success";
						} else {
							showlink = false;
							showtype = "rating";
						}
					} else {
						showlink = false;
						showtype = "orderid";
						msg = "orderid not applicable";
					}
				} else if (showamazonrateus  && monitorType != 3 && monitorType !=4) {
					// check this feed back sent to user in last x days
					if (diffInHrs >= 24 || (requestfrom.equalsIgnoreCase("vpm"))) {
						JResponse ratingResponse = showAmazonRateUs(usr, gatewayid, monitorType);
						AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse()
								.get("reviewstatus");
						msg = (String) ratingResponse.getResponse().get("msg");
						if (reviewsStatus != null) {
							response.put("reviewstatus", reviewsStatus);
							showlink = true;
							showtype = "rating";
							msg = "Success";
						} else {
							showlink = false;
							showtype = "rating";
						}
					} // end of >24 hrs
					else {
						showlink = false;
						showtype = "feedback";
						msg = "Feedback/Rating submitted. will be shown after 24 hrs from last transaction";
					}

					response.put("showlink", showlink);
					response.put("showtype", showtype);
					response.put("Status", 1);
					response.put("Msg", msg);
				} else {
					response.put("Status", 1);
					response.put("Msg", "Feedback /Order ID/ Amazon Rating not enabled");
				}
			}
			GeneralReview reviewData = null;
			if (response.get("reviewstatus") == null) {
				reviewData = userServiceV4.getGeneralReviewData(usr.getId(), monitorType);
				if (reviewData != null) {
					response.put("show_reviewpopup", true);
					response.put("showtype", "generalReview");
				} else {
					response.put("show_reviewpopup", false);
				}
			}
			response.put("generalReview", reviewData);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting feedback/rating");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception : getratingandfeedback : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}


	// getRatingAndFeedback - kalai
	@RequestMapping(value = "v4.0/getratingandfeedback/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getRatingAndFeedback(@RequestParam("gatewayid") long gatewayid,
			@RequestParam("requestfrom") String requestfrom, @RequestParam("os") String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "monitorType", defaultValue = "1", required = false) long monitorType) {
		String auth = header.getFirst("auth");
		log.info("Entered into get ratingandfeedback : autho : " + auth);
		JResponse response = new JResponse();

		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			
			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			Gateway gateway = null;
			if (gatewayid != 0) {
				gateway = gatewayService.getGatewayByid(gatewayid);
				if (gateway == null) {
					response.put("Status", 0);
					response.put("Msg", "Gateway not found");
					return response;
				}
			}
			if (usr != null) {
				boolean showlink = false;
				String showtype = "";
				String msg = "";
				String lastRptDate = userServiceV4.getLastFeedbackDate(usr.getId(),monitorType);
				String currenttime = IrisservicesUtil.getCurrentTimeUTC();

				final SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
				long diffInHrs = 0;
				Date curr = formatter.parse(currenttime);
				Date pre = null;

				if (lastRptDate != null) {
					pre = formatter.parse(lastRptDate);

					diffInHrs = ((curr.getTime() - pre.getTime()) / 3600000);
				} else {
					diffInHrs = 25; // for very first transaction date will be null. so initializing to 25 hrs
				}

				if (requestfrom.equalsIgnoreCase("settingspage-rating")) {
					boolean show_dialog = true;

					String redirecturl = "";
					String redirect_type = "internal";
					msg = "Rate us";

					ArrayList<OrderMappingDetails> orderList = userServiceV4.getOrderMappingListByUser(usr.getId());

					for (OrderMappingDetails orderMap : orderList) {
						if (orderMap.getOrderchannel().equalsIgnoreCase("amazon")) {
							redirecturl = amazonredirecturl;
							redirect_type = "external";
							msg = "Would you like to rate us on Amazon!";
							break;
						} else if (orderMap.getOrderchannel().equalsIgnoreCase("walmart")) {
							redirecturl = walmartredirecturl;
							redirect_type = "external";
							msg = "Would you like to rate us on Walmart!";
						}
					}

					AmazonReviewStatus reviewStatus = new AmazonReviewStatus(redirecturl, show_dialog, 0,
							redirect_type);
					reviewStatus.setAmazon_msg(msg);

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("showlink", true);
					response.put("showtype", "rating");
					response.put("reviewstatus", reviewStatus);

				} else if (showfeedback) {
					// check this feed back sent to user in last x days
					if (diffInHrs >= 24 || (requestfrom.equalsIgnoreCase("vpm"))) {
						JUserFeedback jUserFeedback = userServiceV4.getFeedbackLink(usr.getId(), requestfrom);

						if (jUserFeedback != null) {
							showlink = true;
							showtype = "feedback";
							msg = "Success";

							if (requestfrom.equalsIgnoreCase("vpm"))
								jUserFeedback.setTitle("Do you like our WaggleVet?");

							response.put("feedbackstatus", jUserFeedback);
						} else if (popup_orderid_feedback && gateway != null && (!gateway.isShowOrderId() && !gateway.isPurchased_from_others())) {
							Date currTime = formatter.parse(currenttime);
							long gatewayTimeDiff = ((currTime.getTime() - gateway.getInstalled_date().getTime())
									/ 3600000);

							JUserFeedback jUserFeedbackOrderId = userServiceV4.getFeedbackLink(usr.getId(), "orderid");
							if (jUserFeedbackOrderId != null && gatewayTimeDiff >= 5) {
								showlink = true;
								showtype = "orderid";
								msg = "Success";
								response.put("feedbackstatus", jUserFeedbackOrderId);
							} else if (showamazonrateus) {
								JResponse ratingResponse = showAmazonRateUs(usr, gatewayid, monitorType);
								AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse()
										.get("reviewstatus");
								msg = (String) ratingResponse.getResponse().get("msg");
								if (reviewsStatus != null) {
									response.put("reviewstatus", reviewsStatus);
									showlink = true;
									showtype = "rating";
									msg = "Success";
								} else {
									showlink = false;
									showtype = "rating";
								}
							}

						} else if (showamazonrateus) {
							JResponse ratingResponse = showAmazonRateUs(usr, gatewayid, monitorType);
							AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse()
									.get("reviewstatus");
							msg = (String) ratingResponse.getResponse().get("msg");
							if (reviewsStatus != null) {
								response.put("reviewstatus", reviewsStatus);
								showlink = true;
								showtype = "rating";
								msg = "Success";
							} else {
								showlink = false;
								showtype = "rating";
							}
						} else {
							showlink = false;
							showtype = "feedback";
							msg = "Feedback/Rating not applicable";
						}
					} // end of >24 hrs
					else if (showamazonrateus) {
						JResponse ratingResponse = showAmazonRateUs(usr, gatewayid, monitorType);
						AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse()
								.get("reviewstatus");
						msg = (String) ratingResponse.getResponse().get("msg");
						if (reviewsStatus != null) {
							response.put("reviewstatus", reviewsStatus);
							showlink = true;
							showtype = "rating";
							msg = "Success";
						} else {
							showlink = false;
							showtype = "rating";
						}
					} else {
						showlink = false;
						showtype = "feedback";
						msg = "Feedback/Rating submitted. will be shown after 24 hrs from last transaction";
					}
					response.put("Status", 1);
					response.put("Msg", msg);
					response.put("showlink", showlink);
					response.put("showtype", showtype);

				} else if (diffInHrs >= 24 && popup_orderid_feedback && gateway != null && (!gateway.isShowOrderId() && !gateway.isPurchased_from_others())) {
					Date currTime = formatter.parse(currenttime);
					long gatewayTimeDiff = ((currTime.getTime() - gateway.getInstalled_date().getTime()) / 3600000);
					JUserFeedback jUserFeedback = userServiceV4.getFeedbackLink(usr.getId(), "orderid");
					if (jUserFeedback != null && gatewayTimeDiff >= 5) {
						showlink = true;
						showtype = "orderid";
						msg = "Success";
						response.put("feedbackstatus", jUserFeedback);
					} else if (showamazonrateus) {
						JResponse ratingResponse = showAmazonRateUs(usr, gatewayid, monitorType);
						AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse()
								.get("reviewstatus");
						msg = (String) ratingResponse.getResponse().get("msg");
						if (reviewsStatus != null) {
							response.put("reviewstatus", reviewsStatus);
							showlink = true;
							showtype = "rating";
							msg = "Success";
						} else {
							showlink = false;
							showtype = "rating";
						}
					} else {
						showlink = false;
						showtype = "orderid";
						msg = "orderid not applicable";
					}
				} else if (showamazonrateus) {
					// check this feed back sent to user in last x days
					if (diffInHrs >= 24 || (requestfrom.equalsIgnoreCase("vpm"))) {
						JResponse ratingResponse = showAmazonRateUs(usr, gatewayid, monitorType);
						AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse()
								.get("reviewstatus");
						msg = (String) ratingResponse.getResponse().get("msg");
						if (reviewsStatus != null) {
							response.put("reviewstatus", reviewsStatus);
							showlink = true;
							showtype = "rating";
							msg = "Success";
						} else {
							showlink = false;
							showtype = "rating";
						}
					} // end of >24 hrs
					else {
						showlink = false;
						showtype = "feedback";
						msg = "Feedback/Rating submitted. will be shown after 24 hrs from last transaction";
					}

					response.put("showlink", showlink);
					response.put("showtype", showtype);
					response.put("Status", 1);
					response.put("Msg", msg);
				} else {
					response.put("Status", 1);
					response.put("Msg", "Feedback /Order ID/ Amazon Rating not enabled");
				}
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting feedback/rating");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception : getratingandfeedback : " + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	private JResponse showAmazonRateUs(UserV4 usr, long gatewayid, long monitorType) {
		JResponse response = new JResponse();
		AmazonReviewStatus reviewStatus = null;
		try {
			AmazonUserReview amazonReviewObj = userServiceV4.getAmazonUserReview(usr.getId(), monitorType);
			int viewCount = 0;
			int rptCount = 0;
			String lastRptDate = userServiceV4.getLastFeedbackDate(usr.getId(),monitorType);
			String currenttime = IrisservicesUtil.getCurrentTimeUTC();
			final SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
			long diffInHrs = 0;
			Date curr = formatter.parse(currenttime);
			Date pre = null;
			Date rewDate = null;
			boolean showlink = false;
			String msg = "";
			String showtype = "rating";
			long diffInHrsReview = 0;

			if (lastRptDate != null) {
				pre = formatter.parse(lastRptDate);
				diffInHrs = ((curr.getTime() - pre.getTime()) / 3600000);
			} else {
				diffInHrs = 25; // for very first transaction date will be null. so initializing to 25 hrs
			}
			
			if (amazonReviewObj != null && !amazonReviewObj.getUpdatedon().equals("NA")) {
				rewDate = formatter.parse(amazonReviewObj.getUpdatedon());
				diffInHrsReview = ((curr.getTime() - rewDate.getTime()) / 3600000);
			} else {
				diffInHrsReview = 25; // for very first transaction date will be null. so initializing to 25 hrs
			}

			viewCount = userServiceV4.getViewCount(usr.getId());
			
			if (gatewayid > 0 && monitorType == 1) {
				rptCount = rptServicev4.getGatewayReportCount(gatewayid);
				lastRptDate = rptServicev4.getLastGatewayReporttime(gatewayid);
				
				long diffInMins = 0;
				try {
					pre = formatter.parse(lastRptDate);
					diffInMins = ((curr.getTime() - pre.getTime()) / 60000);
					log.info(" diffInMins : " + diffInMins);
				} catch (Exception e) {
					log.error("Error in parsing lastreportdate : "+e.getLocalizedMessage());
				}

				boolean subsStatus = userServiceV4.checkActiveSubsForAmazonReview( usr.getId() );
				
				int amazon_redirect_cnt = 3;
				if ( subsStatus && (rptCount >= reportCount) && (diffInMins <= 60) && ( diffInHrs > 48 ) && ( amazonReviewObj == null || amazonReviewObj.getSkip_count() < 2 ) ) {
					boolean show_dialog = true;

					String redirecturl = "";
					String redirect_type = "internal";

					ArrayList<OrderMappingDetails> orderList = userServiceV4.getOrderMappingListByUser(usr.getId());
					for (OrderMappingDetails orderMap : orderList) {
						if (orderMap.getOrderchannel().equalsIgnoreCase("amazon")) {
							redirecturl = amazonredirecturl;
							redirect_type = "external";
							msg = "Would you like to rate us on Amazon!";
							break;
						} else if (orderMap.getOrderchannel().equalsIgnoreCase("walmart")) {
							redirecturl = walmartredirecturl;
							redirect_type = "external";
							msg = "Would you like to rate us on Walmart!";
						}
					}

					reviewStatus = new AmazonReviewStatus(redirecturl, show_dialog, amazon_redirect_cnt, redirect_type);

					msg = "Success";
					showlink = true;
					response.put("reviewstatus", reviewStatus);
					response.put("msg", msg);
					return response;
				} else {
					msg = "Rating not applicable. Criteria not matched";
					response.put("reviewstatus", reviewStatus);
					response.put("msg", msg);
					return response;
				}
			}else {
				boolean subsMeariStatus = userServiceV4.checkActiveSubsForMeariDevice( usr.getId(),monitorType );
				
				int amazon_redirect_cnt = 3;
				if ( subsMeariStatus && ( amazonReviewObj == null || (amazonReviewObj.getSkip_count() < 2  && diffInHrsReview > 24) ) ) {
					boolean show_dialog = true;

					String redirecturl = "";
					String redirect_type = "internal";

					ArrayList<OrderMappingDetails> orderList = userServiceV4.getOrderMappingListByUser(usr.getId());
					for (OrderMappingDetails orderMap : orderList) {
						if (orderMap.getOrderchannel().equalsIgnoreCase("amazon")) {
							if(monitorType == 3) {
								redirecturl = amazonredirectsburl;
								showlink = false;
							}else if(monitorType == 4) {
								redirecturl = amazonredirectwcurl;
								showlink = false;
							}else if(monitorType == 5) {
								redirecturl = amazonredirectmcurl;
								showlink = true;
							}else if(monitorType == 6) {
								redirecturl = amazonredirectwcpurl;
								showlink = true;
							}else if(monitorType == 9) {
								redirecturl = amazonredirectnsurl;
								showlink = false;
							}
							
							redirect_type = "external";
							msg = "Would you like to rate us on Amazon!";
							break;
						} else if (orderMap.getOrderchannel().equalsIgnoreCase("walmart")) {
							redirecturl = walmartredirecturl;
							redirect_type = "external";
							showlink = true;
							msg = "Would you like to rate us on Walmart!";
						} else {
							showlink = false;
						}
					}

					reviewStatus = new AmazonReviewStatus(redirecturl, show_dialog, amazon_redirect_cnt, redirect_type);

					msg = "Success";
					if(showlink)
						response.put("reviewstatus", reviewStatus);
					response.put("msg", msg);
					return response;
				} else {
					msg = "Rating not applicable. Criteria not matched";
					response.put("reviewstatus", reviewStatus);
					response.put("msg", msg);
					return response;
				}
			}
		} catch (Exception e) {
			log.error("Error in showAmazonRateUs : " + e.getLocalizedMessage());
			response.put("reviewstatus", reviewStatus);
			response.put("msg", "Error");
			return response;
		}

	}

	// updatefeedbacklink - kalai
	@RequestMapping(value = "v4.0/updateuserfeedback/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateUserFeedback(@RequestParam("option") String option,
			@RequestParam("formid") long formid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering updateuserfeedback : " + autho);

		// option 1-never; 2-later; 3-now

		UserV4 usr = null;
		try {
			usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr != null) {

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				UserFeedbackTransaction uftObj = userServiceV4.getUserFeedbackTransaction(usr.getId(), formid);
				option = option.trim();
				boolean show = false;
				boolean close = false;

				if (option.equalsIgnoreCase("1")) {
					show = false;
					close = true;
				} else if (option.equalsIgnoreCase("2")) {
					show = false;
					close = false;
				} else if (option.equalsIgnoreCase("3")) {
					show = true;
					close = false;
				}

				String cutUtc = IrisservicesUtil.getCurrentTimeUTC();

				if (uftObj != null) {
					int retry = uftObj.getRetry_count() - 1;

					if (retry < 0)
						retry = 0;

					uftObj.setShow(show);
					uftObj.setClose_form(close);
					uftObj.setRetry_count(retry);
					uftObj.setUpdatedon(cutUtc);
				} else {
					uftObj = new UserFeedbackTransaction(usr.getId(), formid, cutUtc, cutUtc, show, close, "NA", "NA",false,1l );
					uftObj.setRetry_count(2);
				}
				boolean stat = userServiceV4.saveOrUpdateUserFeedback(uftObj);

				if (stat) {
					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Failed");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (InvalidAuthoException e) {
			log.error("Invalid Authkey :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}
	
	@RequestMapping(value = "v4.0/updateuserreview/", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateUserReview(@RequestParam("option") String option,
			@RequestParam("formid") long formid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering updateuserfeedback : " + autho);

		// option 1-never; 2-later; 3-now

		UserV4 usr = null;
		try {
			usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr != null) {

				JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				UserFeedbackTransaction uftObj = userServiceV4.getUserFeedbackTransaction(usr.getId(), formid);
				option = option.trim();
				boolean show = false;
				boolean close = false;

				if (option.equalsIgnoreCase("1")) {
					show = false;
					close = true;
				} else if (option.equalsIgnoreCase("2")) {
					show = false;
					close = false;
				} else if (option.equalsIgnoreCase("3")) {
					show = true;
					close = false;
				}

				String cutUtc = IrisservicesUtil.getCurrentTimeUTC();

				if (uftObj != null) {
					int retry = uftObj.getRetry_count() - 1;

					if (retry < 0)
						retry = 0;

					uftObj.setShow(show);
					uftObj.setClose_form(close);
					uftObj.setRetry_count(retry);
					uftObj.setUpdatedon(cutUtc);
				} else {
					uftObj = new UserFeedbackTransaction(usr.getId(), formid, cutUtc, cutUtc, show, close, "", "NA",false,1l);
					uftObj.setRetry_count(2);
				}
				boolean stat = userServiceV4.saveOrUpdateUserFeedback(uftObj);

				if (stat) {
					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Failed");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (InvalidAuthoException e) {
			log.error("Invalid Authkey :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}

}
