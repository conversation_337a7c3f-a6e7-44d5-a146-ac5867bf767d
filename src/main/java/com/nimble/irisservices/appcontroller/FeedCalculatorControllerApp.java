package com.nimble.irisservices.appcontroller;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dto.JBMIinfo;
import com.nimble.irisservices.dto.JBowlPetProfile;
import com.nimble.irisservices.dto.JCaloriesinfo;
import com.nimble.irisservices.dto.JGatewayForPet;
import com.nimble.irisservices.dto.JInjuryinfo;
import com.nimble.irisservices.dto.JPetprofileV2;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.PetBreeds;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.PetSpecies;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IPetBreedServices;
import com.nimble.irisservices.service.IPetSpeciesServices;
import com.nimble.irisservices.service.IPetSpeciesServicesV4;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@RestController
@RequestMapping("/app")
public class FeedCalculatorControllerApp {
	private static final Logger log = LogManager.getLogger(FeedCalculatorControllerApp.class);
	@Autowired
	IUserService userService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Value("${maxpetprofile}")
	private int maxprofilecnt;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;

	@Autowired
	@Lazy
	IPetSpeciesServices petSpeciesServices;
	
	@Autowired
	@Lazy
	IPetSpeciesServicesV4 petSpeciesServicesv4;

	@Autowired
	@Lazy
	IPetBreedServices breedServices;

	@Value("${bmi_uw}")
	private String bmi_uw;

	@Value("${bmi_cw}")
	private String bmi_cw;

	@Value("${bmi_ow}")
	private String bmi_ow;

	@Value("${injury_lr}")
	private String injury_lr;

	@Value("${injury_mr}")
	private String injury_mr;

	@Value("${injury_hr}")
	private String injury_hr;

	@Value("${calories_msg}")
	private String calories_insight;

	@Value("${note_msg}")
	private String note;

	@Value("${disclaimer_msg}")
	private String disclaimer_msg;

	@Autowired
	Helper _helper;

	@Autowired
	IAsyncService asyncService;
	
	@RequestMapping(value = "v5.0/listpetprofile/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPetProfileV5(@RequestParam("os") String os,
			@RequestParam( "app_ver") String app_ver,
			@RequestParam( value = "monitortype", required = false) String monitortype,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering listPetProfile : " + autho);

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);
			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JResponse errResponse = _helper.validateUser(usr.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			
			long monitorType = monitortype != null && monitortype != "" ? Long.valueOf(monitortype) : 0;

			List<JPetprofileV2> profilelist = petSpeciesServices.getJPetprofilesByUserAndMonitor(usr.getId(), "1,2",monitorType);
			int temp_maxprofilecnt = maxprofilecnt;
			int remaincnt = temp_maxprofilecnt;
			boolean woglistflag = false;
			boolean glistflag = false;
			boolean profileflag = false;

			List<JPetprofileV2> withoutgatewaylist = new ArrayList<JPetprofileV2>();
			List<JGatewayForPet> gatewaylist = gatewayService.getNotMappedGateway(usr.getId());

			if (!profilelist.isEmpty()) {
				if (profilelist.size() > temp_maxprofilecnt)
					temp_maxprofilecnt = profilelist.size();
				if (gatewaylist.size() > temp_maxprofilecnt)
					temp_maxprofilecnt = gatewaylist.size();

				remaincnt = temp_maxprofilecnt - profilelist.size();

				for (JPetprofileV2 jp : profilelist) {
					if (jp.getGatewayId() == 0)
						withoutgatewaylist.add(jp);
				}
			}
			if (!withoutgatewaylist.isEmpty())
				woglistflag = true;

			if (!gatewaylist.isEmpty()) {
				glistflag = true;
			}

			if (!profilelist.isEmpty()) {
				profileflag = true;
			}

			response.put("Msg", "Success");
			response.put("Status", 1);
			response.put("maxprofilecnt", temp_maxprofilecnt);
			response.put("remainprofilecnt", remaincnt);
			response.put("wog_plistflag", woglistflag);
			response.put("wopp_glistflag", glistflag);
			response.put("profileflag", profileflag);
			response.put("wopp_glist", gatewaylist);
			response.put("gnotmapped_plist", withoutgatewaylist);
			response.put("profilelist", profilelist);
			response.put("petname_msg", " This pet profile already selected. Please select some other profile");
			response.put("ppselect_msg", "Please select anyone from the list or create new pet profile");

		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Unknown error, Please try after sometime");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : listPetProfile : " + ex.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	public JResponse generateReport(JPetprofileV2 jpetprofile) {
		JResponse response = new JResponse();
		HashMap<String, String> formulaList = petSpeciesServices.getFormulas();
		try {

			// BMI = (weight/(height*height))* 703;
			String formula = formulaList.get("bmi");

			ExpressionParser parser = new SpelExpressionParser();
			Expression exp = parser.parseExpression(formula);
			EvaluationContext context = new StandardEvaluationContext(jpetprofile);
			float bmivalue = exp.getValue(context, Float.class);

			PetBreeds breed = breedServices.getPetBreedsByBName(jpetprofile.getBreed());

			long breedid = 0; // breedid = 0 has default bmi look data
			if (breed != null)
				breedid = breed.getId();
			long classificationid = petSpeciesServices.getClassification(Integer.parseInt(jpetprofile.getAgeYr()),
					jpetprofile.getWeight());

			JBMIinfo bmiinfo = petSpeciesServices.getBMIinfo(breedid, jpetprofile.getSex(), bmivalue);
			String displaymsg = "";
			if (bmiinfo != null) {
				displaymsg = bmiinfo.getBmidesc();
				if (classificationid == 1 && bmivalue > bmiinfo.getCw_max()) {
					bmiinfo.setBmivalue(bmiinfo.getCw_max());
					bmiinfo.setInsight(bmi_cw);
					displaymsg = " Ideal Weight";
				} else {
					if (bmiinfo.getOw_max() < bmivalue) {
						int temp_owmax = (int) (Math.ceil((float) bmivalue / 50f) * 50);
						bmiinfo.setOw_max(temp_owmax);
					}

					if (bmivalue <= bmiinfo.getUw_max())
						bmiinfo.setInsight(bmi_uw);
					else if (bmivalue <= bmiinfo.getCw_max())
						bmiinfo.setInsight(bmi_cw);
					else
						bmiinfo.setInsight(bmi_ow);
				}

				bmiinfo.setBmidesc(displaymsg);
			}

			// injury = "weight/height";
			formula = formulaList.get("injury");
			exp = parser.parseExpression(formula);
			context = new StandardEvaluationContext(jpetprofile);
			float injuryvalue = exp.getValue(context, Float.class);

			JInjuryinfo injuryinfo = petSpeciesServices.getInjuryinfo(injuryvalue);

			if (injuryinfo != null) {
				if (classificationid == 1 && injuryvalue >= injuryinfo.getMr_max()) {
					injuryinfo.setInjuryindex(injuryinfo.getMr_max());
					injuryinfo.setInjurydesc("Moderate Risk");
					injuryinfo.setInsight(injury_mr);
				} else {
					if (injuryvalue <= injuryinfo.getLr_max())
						injuryinfo.setInsight(injury_lr);
					else if (injuryvalue <= injuryinfo.getMr_max())
						injuryinfo.setInsight(injury_mr);
					else
						injuryinfo.setInsight(injury_hr);
				}
			}
			// RER = 70*((weight/2.2)^0.75)
			formula = formulaList.get("rer");
			exp = parser.parseExpression(formula);
			context = new StandardEvaluationContext(jpetprofile);
			float rer = exp.getValue(context, Float.class);

			JCaloriesinfo caloriesinfo = petSpeciesServices.getFactor(jpetprofile);
			float factor = caloriesinfo.getFactor();
			long id = caloriesinfo.getId();
			// MER = "rer*factor";
			formula = formulaList.get("mer");
			short rerValue = (short) Math.round(rer);
			caloriesinfo.setRer(rerValue);
			exp = parser.parseExpression(formula);
			context = new StandardEvaluationContext(caloriesinfo);

			float mer = exp.getValue(context, Float.class);

			short merValue = (short) Math.round(mer);

			short range1 = (short) Math.round(rerValue * 1.1);
			short range2 = (short) Math.round(rerValue * 2);
			short range3 = (short) Math.round(rerValue * 3);
			short range4 = (short) Math.round(rerValue * 4);
			short range5 = (short) Math.round(rerValue * 5);
			short index = 1;

			if (factor >= 1.2 && factor <= 2) {
				range2 = (short) Math.round(rer * factor);
				index = 2;
				merValue = range2;
			} else if (factor >= 2.1 && factor <= 3) {
				range3 = (short) Math.round(rer * factor);
				index = 3;
				merValue = range3;
			} else if (factor >= 3.1 && factor <= 4) {
				range4 = (short) Math.round(rer * factor);
				index = 4;
				merValue = range4;
			} else if (factor >= 4.1 && factor <= 5) {
				range5 = (short) Math.round(rer * factor);
				index = 5;
				merValue = range5;
			}
			String calories_msg = merValue + " Kcal Per Day";

			caloriesinfo = new JCaloriesinfo(range1, range2, range3, range4, range5, index, calories_msg);
			caloriesinfo.setInsight(calories_insight);
			caloriesinfo.setRer(rerValue);
			caloriesinfo.setFactor(factor);
			caloriesinfo.setId(id);

			String curUTC = IrisservicesUtil.getUtcDateTime();
			String qry = "INSERT INTO `healthreport_history` (`user_id`,`petprofile_id`, `bmi_id`, `bmiindex`, `bmi_desc`, `injury_id`, `injuryindex`, "
					+ "`injury_desc`, `calories_id`,  `calories_desc`, `merindex`, `range1`, `range2`, `range3`, `range4`, `range5`,`create_date`) VALUES"
					+ "(" + jpetprofile.getUser_id() + "," + jpetprofile.getId() + "," + bmiinfo.getId() + ","
					+ bmiinfo.getBmivalue() + ",'" + bmiinfo.getBmidesc() + "'," + injuryinfo.getId() + ","
					+ injuryinfo.getInjuryindex() + ",'" + injuryinfo.getInjurydesc() + "'," + caloriesinfo.getId()
					+ ",'" + caloriesinfo.getCalories_desc() + "'," + caloriesinfo.getMer_index() + ","
					+ caloriesinfo.getRange1() + "," + caloriesinfo.getRange2() + "," + caloriesinfo.getRange3() + ","
					+ caloriesinfo.getRange4() + "," + caloriesinfo.getRange5() + ",'" + curUTC + "');";
			boolean rptStatus = petSpeciesServices.saveHealthReport(qry);

			NumberFormat formatter = new DecimalFormat("00");
			jpetprofile.setAgeYr(formatter.format(Integer.parseInt(jpetprofile.getAgeYr())));
			jpetprofile.setAgeMonth(formatter.format(Integer.parseInt(jpetprofile.getAgeMonth())));
			String banner_msg = jpetprofile.getName() + "'s health report";
			response.put("Msg", "Success");
			response.put("Status", 1);
			response.put("jpetprofile", jpetprofile);
			response.put("bmiinfo", bmiinfo);
			response.put("injuryinfo", injuryinfo);
			response.put("caloriesinfo", caloriesinfo);
			response.put("banner_msg", banner_msg);
			response.put("disclaimer_msg", disclaimer_msg);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Unknown error, Please try after sometime");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception generateReport: " + e.getLocalizedMessage());
		}
				response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	
	@RequestMapping(value = "v4.0/createbowlpetprofile/", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createBowlPetProfile(@RequestBody List<JBowlPetProfile> jBowlPetProfileList, @RequestParam("os") String os,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		log.info("Entering createPetProfile : " + autho);
		try {
			if (!jBowlPetProfileList.isEmpty()) {

				Map<String, String> map = new HashMap<String, String>();

				try {
					map = userServiceV4.getUserId_cmpIdByAuthV2(autho);
				} catch (InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "Invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for authkey : " + autho);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				JResponse errResponse = _helper.validateUser(map.get("username"), authentication.getName());
				if (errResponse != null) {
					return errResponse;
				}

				long userId = Long.valueOf(map.get("user_id"));

				int status = 0;
				double MER = 0;
				for (JBowlPetProfile jBowlPetProfile : jBowlPetProfileList) {
					if (!jBowlPetProfile.getBreed().isEmpty() && !jBowlPetProfile.getName().isEmpty()) {

						jBowlPetProfile.setUser_id(userId);

						PetSpecies species = petSpeciesServices.getPetSpeciesByName(jBowlPetProfile.getSpecieName());
						long speciesid = 1;

						if (species != null)
							speciesid = species.getId();

						jBowlPetProfile.setSpeciesid(speciesid);

						PetProfile pp = petSpeciesServices.createBowlPetProfile(jBowlPetProfile);

						if (pp != null)
							status = 1;

						if (jBowlPetProfile.getGatewayId() != 0) {
							log.info("Enter Into Gateway");
							boolean issuccess = gatewayService.updateGatewayName(jBowlPetProfile.getName(),
									String.valueOf(jBowlPetProfile.getGatewayId()));
							log.info("GatewayName updated with respect to petname");
						}
						double weight = Double.parseDouble(pp.getWeight());
						double weightPounds = weight * 0.453592;
						double RER = 70 * Math.pow(weightPounds, 0.75);
						double signalment = petSpeciesServicesv4.getSignalment(speciesid, jBowlPetProfile.isIntact());		
						double BCS = petSpeciesServicesv4.getBCS(jBowlPetProfile.getStructure());							
						double activityLevel = 1;
						MER = RER * signalment * activityLevel * BCS;
						
						asyncService.updateReqCalories(jBowlPetProfile.getGatewayId(), MER);

					}
				}

				if (status == 1) {
					response.put("Status", 1);
					response.put("Msg", "success");
					response.put("Msg", "Pet Profile saved successfully");
					double kcal = (int)MER; 
					String content_1 = "Your Pet's Daily Calorie Requirements";
					String content_2 = "Our Vet Calculator make a calculation for your pet's suggested daily energy requirements. This calculation considers factors such as weight and other elements to determine the recommended daily energy intake for pets. "
							+ "This calculation incorporates your pet's food calories along with your Pet's Daily Requirements.";
					String content_3 = "Suggested Daily Energy Intake: \n Approx. "+(int)MER+" kcal";
					response.put("kcal", kcal);
					response.put("content_1", content_1);
					response.put("content_2", content_2);
					response.put("content_3", content_3);
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error, Not saved");
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "Empty pet profile received");
			}

		} catch (DataIntegrityViolationException e) {
			log.error("createPetProfile :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unknown error, Please try after sometime");
			response.put("Error", e.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", 0);
			log.error("createPetProfile :" + e.getLocalizedMessage());
			response.put("Msg", "Unknown error, Please try after sometime");
			response.put("Error", e.getLocalizedMessage());

		}
		log.info("Exit createPetProfile");
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	@GetMapping("v4.0/listbowlpetprofile")
	public JResponse listbowlpetprofile(@RequestParam long gateway_id, @RequestParam String os,
			@RequestParam String app_ver, Authentication authentication, @RequestHeader HttpHeaders header) {
		log.info("Entered into listbowlpetprofile :: gateway_id : "+gateway_id);
		JResponse response = new JResponse();
		try {

			String auth = header.getFirst("auth");

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.info("Invalid auth :: auth : " + auth + " Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}
			 
			List<JBowlPetProfile> petProfile = petSpeciesServices.getJBowlPetProfile(user.getId(), gateway_id);
			if( petProfile == null ) {
				response.put("Status", 1);
				response.put("Msg", "Success"); 
				response.put("pet_profile_list", new ArrayList<>());
				return response;
			}
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("pet_profile_list", petProfile);
			
		} catch (Exception e) {
			log.error("Error in listbowlpetprofile :: Error : "+ e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid session. Please try again later");
		}
		return response;
	}

}
