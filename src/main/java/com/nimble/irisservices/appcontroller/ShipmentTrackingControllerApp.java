package com.nimble.irisservices.appcontroller;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.OrderTrackingData;
import com.nimble.irisservices.dto.ShipmentDetailData;
import com.nimble.irisservices.service.INiomDataBaseService;

@RestController
public class ShipmentTrackingControllerApp {
	
	@Autowired
	INiomDataBaseService iShipmentService;
	
	private static final Logger log = LogManager.getLogger(ShipmentTrackingControllerApp.class);
	
	@RequestMapping(value = "/app/v5.0/getshipmentdetails", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getShipmentDetailsV5(
			Authentication authentication,@RequestHeader HttpHeaders header,
			@RequestParam(value = "orderid", defaultValue = "NA", required = true) String order_ID,
			@RequestParam(value = "emailid", defaultValue = "NA", required = true) String email_ID) {
			log.info(" Entered getshipmentdetails: ");

			JResponse response = new JResponse();
			String autho = header.getFirst("auth");
			List<ShipmentDetailData> Details = null;
			
			try {
				order_ID = order_ID.replace("-", "");
				
				if(!email_ID.isEmpty() && !email_ID.equalsIgnoreCase("NA")) {
					Details = iShipmentService.getShipmentDetails("OS.order_email", email_ID);
				}else {
					Details = iShipmentService.getShipmentDetails("O.order_id", order_ID);
				}
				if (Details != null && !Details.isEmpty()) {
					List<OrderTrackingData> TDetails = null;
					for( ShipmentDetailData shipmentDetailData : Details) {
						TDetails = iShipmentService.getTrackingDetails(String.valueOf(shipmentDetailData.getId()));
						shipmentDetailData.setTracking_status((ArrayList<OrderTrackingData>) TDetails);
					}
						response.put("Status", 1);
						response.put("Msg", "Success");
						response.put("shipment_details", Details);
						return response;
				
				} else {
					if(!order_ID.isEmpty() && !order_ID.equalsIgnoreCase("NA")) {
						Details = iShipmentService.getShipmentDetails("order_id", order_ID);
						if (Details != null && !Details.isEmpty()) {
							List<OrderTrackingData> TDetails = null;
							for( ShipmentDetailData shipmentDetailData : Details) {
								TDetails = iShipmentService.getTrackingDetails(String.valueOf(shipmentDetailData.getId()));
								shipmentDetailData.setTracking_status((ArrayList<OrderTrackingData>) TDetails);
							}
							response.put("Status", 1);
							response.put("Msg", "Success");
							response.put("shipment_details", Details);
							return response;
						}
					}
				}
			} catch (Exception e) {
				log.error("Exception :getShipmentDetails : ", e.getLocalizedMessage());
				response.put("Error", e.getLocalizedMessage());
			}
			response.put("Status", 0);
			response.put("Msg", "No records found! Please enter a valid Order ID.");
			response.put("shipment_details", Details);
			return response;
		}
	
	@RequestMapping(value = "/app/v4.0/getshipmentdetails", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getShipmentDetails(
			Authentication authentication,@RequestHeader HttpHeaders header,
			@RequestParam(value = "orderid", defaultValue = "NA", required = true) String order_ID,
			@RequestParam(value = "emailid", defaultValue = "NA", required = true) String email_ID) {
			log.info(" Entered getshipmentdetails: ");

			JResponse response = new JResponse();
			String autho = header.getFirst("auth");
			List<ShipmentDetailData> Details = null;
			
			try {
				order_ID = order_ID.replace("-", "");
				
				if(!email_ID.isEmpty() && !email_ID.equalsIgnoreCase("NA")) {
					Details = iShipmentService.getShipmentDetails("order_email", email_ID);
				}else {
					Details = iShipmentService.getShipmentDetails("order_id", order_ID);
				}
				if (Details != null || !Details.isEmpty()) {
					List<OrderTrackingData> TDetails = null;
					for( ShipmentDetailData shipmentDetailData : Details) {
						TDetails = iShipmentService.getTrackingDetails(String.valueOf(shipmentDetailData.getId()));
						shipmentDetailData.setTracking_status((ArrayList<OrderTrackingData>) TDetails);
					}
						response.put("Status", 1);
						response.put("Msg", "Success");
						response.put("shipment_details", Details);
						return response;
				
				} else {
					if(!order_ID.isEmpty() && !order_ID.equalsIgnoreCase("NA")) {
						Details = iShipmentService.getShipmentDetails("order_id", order_ID);
						if (Details != null || !Details.isEmpty()) {
							List<OrderTrackingData> TDetails = null;
							for( ShipmentDetailData shipmentDetailData : Details) {
								TDetails = iShipmentService.getTrackingDetails(String.valueOf(shipmentDetailData.getId()));
								shipmentDetailData.setTracking_status((ArrayList<OrderTrackingData>) TDetails);
							}
							response.put("Status", 1);
							response.put("Msg", "Success");
							response.put("shipment_details", Details);
							return response;
						}
					}
				}
			} catch (Exception e) {
				log.error("Exception :getShipmentDetails : ", e.getLocalizedMessage());
				response.put("Error", e.getLocalizedMessage());
			}
			response.put("Status", 0);
			response.put("Msg", "No records found! Please enter a valid Order ID.");
			response.put("shipment_details", Details);
			return response;
		}
	
	}
	
