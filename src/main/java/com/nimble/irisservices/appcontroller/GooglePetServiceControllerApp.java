package com.nimble.irisservices.appcontroller;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.gson.Gson;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.Apidetails;
import com.nimble.irisservices.entity.CompanyAccountSettings;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.googlenearbysearch.dto.GoogleNearBySearchResponse;
import com.nimble.irisservices.googlenearbysearch.dto.Results;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAccountService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.IUserService;

@Controller
@RequestMapping("/app")
public class GooglePetServiceControllerApp {

	private static final Logger log = LogManager.getLogger(GooglePetServiceControllerApp.class);

	Helper _helper = new Helper();

	@Autowired
	@Lazy
	IAccountService accService;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IUserService userService;

	@RequestMapping(value = "v3.0/getnearbyservices/", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse enabledisablealertcfg(@RequestParam("servicetype") String servicetype,
			@RequestParam("apitype") String apitype, @RequestParam("lat") String latitude,
			@RequestParam("lon") String longitude, Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime)
			throws FileNotFoundException, IOException {

		JResponse response = new JResponse();

		String lat1 = latitude;

		String lon1 = longitude;
		String autho = header.getFirst("auth");
		try {

			User user = userService.verifyAuthKey(autho);

			JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
			if (errResponse != null) {
				return errResponse;
			}

			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());

			if (!cmp_cfg.isPetservice_enable()) {

				response.put("Status", 0);
				response.put("Msg", "Pet Service not enable for the user");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			ArrayList<Results> results = new ArrayList<Results>();

			Apidetails apiDetail = new Apidetails();

			apiDetail.setServiceType(servicetype.toString().trim());
			apiDetail.setApi(apitype.toLowerCase().trim());

			List<Apidetails> apiDetailList = accService.getApi(apiDetail);

			if (apiDetailList.size() > 0) {

				String url = apiDetailList.get(0).getServiceType() + "_" + apiDetailList.get(0).getApi();

				String googleUrl = null;

				String photoUrl = null;
				// String googleUrl =
				// "https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=LATLON&radius=15000&type=petservicekeyword=Dog&keyword=Grooming&key=AIzaSyCtcw9q_Bo1XlHGecRcaJtRy0UUFpLiBCs";

				try {
					Properties prop = new Properties();
					File file = ResourceUtils.getFile("classpath:iris3.properties");
					prop.load(new FileInputStream(file));
//					prop.load(new FileInputStream("classpath:iris3.properties"));
					googleUrl = prop.getProperty(url);

					photoUrl = prop.getProperty("googlePhotoApiURL");

				} catch (IOException ex) {
					log.error("Exception while reading" + url + " from iris3.propertiesfile");
					response.put("Status", 0);
					response.put("Msg", "Exception while reading Google API url from properties file");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				googleUrl = googleUrl.replaceAll("LATLON", lat1 + "," + lon1);

				boolean isNextPageAvailbale = true;

				String nextPageToken = "";
				int requestCount = 0;

				while (isNextPageAvailbale) {

					String apiResponse = null;

					GoogleNearBySearchResponse apiGoogleResponse = new GoogleNearBySearchResponse();

					String _url = null;
					if (nextPageToken.equals("")) {
						_url = googleUrl.trim();
					} else {
						_url = googleUrl.trim() + "&pagetoken=" + nextPageToken;
//						_url = googleUrl.trim() + "&pagetoken=" + nextPageToken + "&requestCount=" + requestCount;
//						Thread.sleep(5000);			
					}

					apiResponse = _helper.getURL(_url);

					Gson gson = new Gson();

					apiGoogleResponse = gson.fromJson(apiResponse, GoogleNearBySearchResponse.class);

					if (apiGoogleResponse.getStatus().equalsIgnoreCase("ok")) {

						CompanyAccountSettings cmpAccSettings = new CompanyAccountSettings();

						cmpAccSettings.setCompany(user.giveCompany());
						cmpAccSettings.setApidetails(apiDetailList.get(0));

						List<CompanyAccountSettings> cmpAccSetting = accService.getCmpAccount(cmpAccSettings);

						if (cmpAccSetting.size() > 0) {

							accService.update(cmpAccSetting.get(0));

						} else {
							CompanyAccountSettings newCmpAcc = new CompanyAccountSettings();
							Long transaction = new Long(*********);
							Long totalCount = new Long(1);
							newCmpAcc.setApidetails(apiDetailList.get(0));
							newCmpAcc.setCompany(user.giveCompany());
							newCmpAcc.setTotalTransaction(transaction);
							newCmpAcc.setRemainingTransaction(transaction);
							newCmpAcc.setTotalCount(totalCount);
							boolean status = accService.saveOrUpdateCmpAccount(newCmpAcc);
						}
					}

					if (!apiGoogleResponse.getStatus().equalsIgnoreCase("ok")) {
						if (results.size() > 0) {
							response.put("Status", 1);
							response.put("Msg", "Success");
							response.put("nearByService", results);

						} else {
							response.put("Status", 0);
							response.put("Msg", "No services available for your location");
							response.put("nearByService", results);
							break;

						}
					}
					if (apiGoogleResponse.getNext_page_token() != null) {
						isNextPageAvailbale = true;
						nextPageToken = apiGoogleResponse.getNext_page_token();
					} else {
						isNextPageAvailbale = false;
					}
					for (Results result : apiGoogleResponse.getResults()) {

						String photoReference = "";
						String _photoUrl = "";
						if (result.getPhotos().size() > 0) {
							photoReference = result.getPhotos().get(0).getPhoto_reference();
							_photoUrl = photoUrl.replaceAll("PHOTO_REFERENCE", photoReference);
						}

						double lat2 = result.getGeometry().getLocation().getLat();
						double lon2 = result.getGeometry().getLocation().getLng();

						double distance = (double) distance(Double.valueOf(lat1), Double.valueOf(lon1), lat2, lon2);
						result.setDistance(distance);
						result.setImageUrl(_photoUrl);
						results.add(result);
					}
					requestCount++;
				}

				if (results.size() > 0) {
					Collections.sort(results, new Comparator<Results>() {
						@Override
						public int compare(Results a, Results b) {

							return Double.compare(a.getDistance(), b.getDistance());

//							return a.getDistance().compareTo(b.getDistance());

						}
					});
				}

				if (results.size() > 0) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("nearByService", results);
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid Service Type or API Type");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

		} catch (InvalidAuthoException ex) {
			log.error("in valid auth:"+ex.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid authentication key");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception e) {
			log.error("Excepitoin while creating/updating Company details:"+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while creating/updating Company details");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	public double distance(double lat1, double lon1, double lat2, double lon2) {
		double theta = lon1 - lon2;
		double dist = Math.sin(deg2rad(lat1)) * Math.sin(deg2rad(lat2))
				+ Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * Math.cos(deg2rad(theta));
		dist = Math.acos(dist);
		dist = rad2deg(dist);
		dist = dist * 60 * 1.1515;
		DecimalFormat df = new DecimalFormat("#.#");

		return (Double.parseDouble(df.format(dist)));
	}

	public double deg2rad(double deg) {
		return (deg * Math.PI / 180.0);
	}

	public double rad2deg(double rad) {
		return (rad * 180.0 / Math.PI);
	}
}
