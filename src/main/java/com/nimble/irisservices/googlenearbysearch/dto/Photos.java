package com.nimble.irisservices.googlenearbysearch.dto;

import java.util.ArrayList;

public class Photos {

	  private float height;
	  ArrayList<Object> html_attributions = new ArrayList<Object>();
	  private String photo_reference;
	  private float width;


	 // Getter Methods 

	  public float getHeight() {
	    return height;
	  }

	  public String getPhoto_reference() {
	    return photo_reference;
	  }

	  public float getWidth() {
	    return width;
	  }

	 // Setter Methods 

	  public void setHeight( float height ) {
	    this.height = height;
	  }

	  public void setPhoto_reference( String photo_reference ) {
	    this.photo_reference = photo_reference;
	  }

	  public void setWidth( float width ) {
	    this.width = width;
	  }

}
