package com.nimble.irisservices.googlenearbysearch.dto;

import java.util.ArrayList;

public class GoogleNearBySearchResponse {

	private String next_page_token;

	private String status;

	private ArrayList <Results> results = new ArrayList <Results> ();
	
	
	public GoogleNearBySearchResponse(String next_page_token, String status, ArrayList<Results> results) {
		super();
		this.next_page_token = next_page_token;
		this.status = status;
		this.results = results;
	}

	public GoogleNearBySearchResponse() {
		super();
		// TODO Auto-generated constructor stub
	}

	public String getNext_page_token() {
		return next_page_token;
	}

	public String getStatus() {
		return status;
	}

	// Setter Methods

	public void setNext_page_token(String next_page_token) {
		this.next_page_token = next_page_token;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public ArrayList<Results> getResults() {
		return results;
	}

	public void setResults(ArrayList<Results> results) {
		this.results = results;
	}

}
