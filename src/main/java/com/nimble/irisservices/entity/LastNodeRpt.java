package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="lastnodereport", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class LastNodeRpt  implements Serializable {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	@Column(name="version")
	private String version;
	@Column(name="datetime")
	private Timestamp datetime;
	@Column(name="date")
	private Date date;
	@Column(name="time")
	private Time time;
	@Column(name="timezone")
	private String timezone;
	
	@Column(name="battery")
	private int battery;
	@Column(name="rawlqa")
	private int rawlqa;
	@Column(name="lqa")
	private String lqa;
	@Column(name="motion")
	private String motion;
	@Column(name="externalsensor")
	private float extsensor;
	@Column(name="humidity")
	private float humidity;
	@Column(name="temperature")
	private float temperature;
	@Column(name="tempseverity")
	private int tempseverity;
	@Column(name="light")
	private float light;
	@Column(name="pressure")
	private float pressure;
	@Column(name="eventid")
	private String eventid;
	@Column(name="errorcode")
	private int errorcode;
	
	@Column(name="heat_index")
	private float heat_index;
	
    @ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="node_id")
	//@JsonBackReference
    private Node node;	
   
	
    @ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="cmp_id")
	//@JsonBackReference
    private Company company;


	public LastNodeRpt() {
		super();
	}


	public LastNodeRpt(String version, Timestamp datetime, Date date,
			Time time, String timezone, int battery, int rawlqa, String lqa,
			String motion, float extsensor, float humidity, float temperature,
			int tempseverity, float light, float pressure, String eventid,
			int errorcode, Node node, Company company) {
		super();
		this.version = version;
		this.datetime = datetime;
		this.date = date;
		this.time = time;
		this.timezone = timezone;
		this.battery = battery;
		this.rawlqa = rawlqa;
		this.lqa = lqa;
		this.motion = motion;
		this.extsensor = extsensor;
		this.humidity = humidity;
		this.temperature = temperature;
		this.tempseverity = tempseverity;
		this.light = light;
		this.pressure = pressure;
		this.eventid = eventid;
		this.errorcode = errorcode;
		this.node = node;
		this.company = company;
	}


	public long getId() {
		return id;
	}


	public String getVersion() {
		return version;
	}


	public Timestamp getDatetime() {
		return datetime;
	}

	

	public Date getDate() {
		return date;
	}


	public Time getTime() {
		return time;
	}


	public String getTimezone() {
		return timezone;
	}


	public int getBattery() {
		return battery;
	}

	public int getRawlqa() {
		return rawlqa;
	}


	public String getLqa() {
		return lqa;
	}


	public String getMotion() {
		return motion;
	}


	public float getExtsensor() {
		return extsensor;
	}


	public float getHumidity() {
		return humidity;
	}


	public float getTemperature() {
		return temperature;
	}
	

	public int getTempseverity() {
		return tempseverity;
	}


	public float getLight() {
		return light;
	}


	public float getPressure() {
		return pressure;
	}


	public Node getNode() {
		return node;
	}


	public Company getCompany() {
		return company;
	}


	public String getEventid() {
		return eventid;
	}


	public int getErrorcode() {
		return errorcode;
	}


	public float getHeat_index() {
		return heat_index;
	}


	public void setHeat_index(float heat_index) {
		this.heat_index = heat_index;
	}

    

}
