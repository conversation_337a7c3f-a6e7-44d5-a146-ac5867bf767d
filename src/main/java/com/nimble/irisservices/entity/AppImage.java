package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="app_image", uniqueConstraints =@UniqueConstraint (columnNames={"img_name","type" } ))
public class AppImage implements Serializable {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@Column(name="img_name")
	private String img_name;
	
	@Column(name="type")
	private String type;
	
	@Column(name="img_path")
	private String img_path;
	
	@Column(name="img_path_flutter_light")
	private String img_path_flutter_light;
	
	@Column(name="img_path_flutter_dark")
	private String img_path_flutter_dark;
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getImg_name() {
		return img_name;
	}

	public void setImg_name(String img_name) {
		this.img_name = img_name;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getImg_path() {
		return img_path;
	}

	public void setImg_path(String img_path) {
		this.img_path = img_path;
	}

	public String getImg_path_flutter_light() {
		return img_path_flutter_light;
	}

	public void setImg_path_flutter_light(String img_path_flutter_light) {
		this.img_path_flutter_light = img_path_flutter_light;
	}

	public String getImg_path_flutter_dark() {
		return img_path_flutter_dark;
	}

	public void setImg_path_flutter_dark(String img_path_flutter_dark) {
		this.img_path_flutter_dark = img_path_flutter_dark;
	}
	
}
