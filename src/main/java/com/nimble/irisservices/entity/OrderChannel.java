package com.nimble.irisservices.entity;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.GenericGenerator;


@Entity 
@Table(name="order_channel", uniqueConstraints =@UniqueConstraint (columnNames={"id","orderchannel" } ))
public class OrderChannel {

	
	public OrderChannel() {
		super();
		// TODO Auto-generated constructor stub
	}

	public OrderChannel(long id, String orderchannel, String shortdescription,String imgpath) {
		super();
		this.id = id;
		this.orderchannel = orderchannel;
		this.shortdescription = shortdescription;
		this.imgpath = imgpath;
	}

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@Column(name="orderchannel")
	private String orderchannel;
	
	/*@Column(name="redirect_url")
	private String redirect_url;*/
	
	@Column(name="shortdescription")
	private String shortdescription;
	
	@Column(name="imgpath")
	private String imgpath;
	
	@Column(name="enable")
	private boolean enable;

	@JsonIgnore
	private boolean sub_key;

	public long getId() {
		return id;
	}

	public OrderChannel(String orderchannel, String shortdescription,String imgpath) {
		super();
		this.orderchannel = orderchannel;
		this.shortdescription = shortdescription;
		this.imgpath = imgpath;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getOrderchannel() {
		return orderchannel;
	}

	public void setOrderchannel(String orderchannel) {
		this.orderchannel = orderchannel;
	}

	public String getShortdescription() {
		return shortdescription;
	}

	public void setShortdescription(String shortdescription) {
		this.shortdescription = shortdescription;
	}

	public String getImgpath() {
		return imgpath;
	}

	public void setImgpath(String imgpath) {
		this.imgpath = imgpath;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public boolean isSub_key() {
		return sub_key;
	}

	public void setSub_key(boolean sub_key) {
		this.sub_key = sub_key;
	}
}
