package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "banner_details")
public class DashboardBanner {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	private String bannerImg_url = "NA";
	
	private boolean enable = false;
	
	private String bannerImg_link = "NA";
	
	private String updated_on;
	
	private String title = "NA";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getBannerImg_url() {
		return bannerImg_url;
	}

	public void setBannerImg_url(String bannerImg_url) {
		this.bannerImg_url = bannerImg_url;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getBannerImg_link() {
		return bannerImg_link;
	}

	public void setBannerImg_link(String bannerImg_link) {
		this.bannerImg_link = bannerImg_link;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}
	
	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public DashboardBanner() {
		super();
	}
	
	public DashboardBanner(long id, String bannerImg_url, boolean enable, String bannerImg_link, String updated_on) {
		super();
		this.id = id;
		this.bannerImg_url = bannerImg_url;
		this.enable = enable;
		this.bannerImg_link = bannerImg_link;
		this.updated_on = updated_on;
	}
	
	
	
}
