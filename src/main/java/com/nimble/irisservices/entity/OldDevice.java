package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "old_device")
public class OldDevice {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	private String meid = "NA";

	private String qrc = "NA";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getQrc() {
		return qrc;
	}

	public void setQrc(String qrc) {
		this.qrc = qrc;
	}

	public OldDevice() {
		super();
	}

	public OldDevice(long id, String meid, String qrc) {
		super();
		this.id = id;
		this.meid = meid;
		this.qrc = qrc;
	}

}
