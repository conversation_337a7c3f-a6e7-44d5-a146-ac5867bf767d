package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name="feedback_form")

public class FeedbackForm implements Serializable {
	@Id
	@Column(name = "id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@Column(name = "link")
	private String link;

	@Column(name = "createdon")
	private String createdon;

	@Column(name = "expiredon")
	private String expiredon;

	@Column(name = "enable")
	private boolean enable = true;

	@Column(name = "category")
	private String category;
	
	@Column(name="next_show_interval")
	private int next_show_interval =10; // no of days to display feedback form next time   
	
	@Column(name = "param1")
	private String param1;
	
	@Column(name = "param2")
	private String param2;
	
	@Column(name="show_count")
	private int show_count =3; //3 times will be shown to user if ,user hits later
	
	@Column(name="show_alluser")
	private boolean show_alluser = true;	
	
	public FeedbackForm() {
		super();
	}	

	public FeedbackForm(long id, String link, String createdon, String expiredon, boolean enable, String category,
			int next_show_interval, String param1, String param2, int show_count, boolean show_alluser) {
		super();
		this.id = id;
		this.link = link;
		this.createdon = createdon;
		this.expiredon = expiredon;
		this.enable = enable;
		this.category = category;
		this.next_show_interval = next_show_interval;
		this.param1 = param1;
		this.param2 = param2;
		this.show_count = show_count;
		this.show_alluser = show_alluser;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getLink() {
		return link;
	}

	public void setLink(String link) {
		this.link = link;
	}

	public String getCreatedon() {
		return createdon;
	}

	public void setCreatedon(String createdon) {
		this.createdon = createdon;
	}

	public String getExpiredon() {
		return expiredon;
	}

	public void setExpiredon(String expiredon) {
		this.expiredon = expiredon;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public int getNext_show_interval() {
		return next_show_interval;
	}

	public void setNext_show_interval(int next_show_interval) {
		this.next_show_interval = next_show_interval;
	}

	public String getParam1() {
		return param1;
	}

	public void setParam1(String param1) {
		this.param1 = param1;
	}

	public String getParam2() {
		return param2;
	}

	public void setParam2(String param2) {
		this.param2 = param2;
	}

	public int getShow_count() {
		return show_count;
	}

	public void setShow_count(int show_count) {
		this.show_count = show_count;
	}

	public boolean getShow_alluser() {
		return show_alluser;
	}

	public void setShow_alluser(boolean show_alluser) {
		this.show_alluser = show_alluser;
	}	
}
