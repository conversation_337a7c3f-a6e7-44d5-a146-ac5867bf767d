package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

import com.nimble.irisservices.constant.IrisservicesConstants;

@Entity
@Table(name = "upgrade_device_history")
public class UpgradeDeviceHistory {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	private long user_id = 0;
	
	private String old_meid = "NA";
	
	private String new_meid = "NA";
	
	private String updated_on = IrisservicesConstants.DEFAULT_DATE;

	private String old_qrc = "NA";

	private String new_qrc = "NA";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getOld_meid() {
		return old_meid;
	}

	public void setOld_meid(String old_meid) {
		this.old_meid = old_meid;
	}

	public String getNew_meid() {
		return new_meid;
	}

	public void setNew_meid(String new_meid) {
		this.new_meid = new_meid;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public void setOld_qrc(String old_qrc) { this.old_qrc = old_qrc;}

	public String getOld_qrc() { return old_qrc;}

	public void setNew_qrc(String new_qrc) {this.new_qrc = new_qrc;}

	public String getNew_qrc() { return new_qrc;}

	public UpgradeDeviceHistory() {
		super();
	}
	
	public UpgradeDeviceHistory(long id, long user_id, String old_meid, String new_meid, String updated_on, String old_qrc, String new_qrc) {
		super();
		this.id = id;
		this.user_id = user_id;
		this.old_meid = old_meid;
		this.new_meid = new_meid;
		this.updated_on = updated_on;
		this.old_qrc = old_qrc;
		this.new_qrc = new_qrc;
	}
	
}
