package com.nimble.irisservices.entity;


import java.io.Serializable;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.GenericGenerator;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

@Entity
@Table(name = "user")
public class User implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;
	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	@Column(name = "username")
	private String username;
	@Column(name = "password")
	private String password;
	@Column(name = "authkey")
	private String authKey;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "role_id")
	private Role role;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "cmp_id")
	private Company company;

	@JsonIgnore
	@ManyToMany(cascade = { CascadeType.ALL }, fetch = FetchType.EAGER)
	@JoinTable(name = "usergateway", joinColumns = { @JoinColumn(name = "userId") }, inverseJoinColumns = {
			@JoinColumn(name = "gatewayId") })

	private Set<Gateway> gateways = new HashSet<Gateway>();

	@ManyToMany(fetch = FetchType.LAZY, mappedBy = "users")
	private Set<PushNotifications> pushNotifications = new HashSet<PushNotifications>();

	@Column(name = "email")
	private String email;

	@Column(name = "mobileno")
	private String mobileno;

	@Column(name = "webappid")
	private int webappid;

	@Column(name = "mobileappid")
	private int mobileappid;

	@Column(name = "enable")
	private boolean enable;

	@Column(name = "resetpassword")
	private int resetpassword;

	@Column(name = "notification")
	private boolean notification;

	@Column(name = "alternate_email")
	private String alternateemail = "NA";

	@Column(name = "alternate_phone")
	private String alternatephone = "NA";

	@Column(name = "firstname")
	private String firstname = "NA";

	@Column(name = "lastname")
	private String lastname = "NA";

	@Column(name = "zipcode")
	private String zipcode = "NA";

	@Column(name = "city")
	private String city = "NA";

	@Column(name = "state")
	private String state = "NA";

	@Column(name = "country")
	private String country = "NA";

	@Column(name = "signuptoken")
	private String signuptoken = "NA";

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "signuptype_id")
	private SignupType signupType;

	@Column(name = "createdon")
	private String createdOn = "1753-01-01 00:00:00";

	@Column(name = "updatedon")
	private String updatedOn = "1753-01-01 00:00:00";

	@Column(name = "isverified")
	private boolean isVerified = false;

	@Column(name = "imageurl")
	private String imageUrl = "NA";

	@OneToOne(mappedBy = "user", cascade = CascadeType.ALL)
	private UserVerification verificationToken;

	@Column(name = "chargebeeid")
	private String chargebeeid = "NA";

	@Column(name = "in_app")
	private boolean in_app = true;

	@Column(name = "lastlogintype")
	private int lastlogintype;

	@Column(name = "completesetup")
	private boolean completesetup = true;

	@Column(name = "lastlogintime")
	private String lastlogintime = "1753-01-01 00:00:00";

	@Column(name = "inapp_purchase")
	private int inapp_purchase = 1; // 0- general, 1-cb, 2-ios

	@Column(name = "plan_ver")
	private String plan_ver = "V2";

	@Column(name = "password_ver")
	private String password_ver = "V2";
	
	@Column(name = "delete_user")
	private boolean delete_user = false;
	
	@Column(name = "delete_time")
	private String delete_time = "1753-01-01 00:00:00";
	
	@Column (name = "recharge_custid")
	private String recharge_custid = "NA";

	public Set<Gateway> getGateways() {
		return gateways;
	}

	public void setGateways(Set<Gateway> gateways) {
		this.gateways = gateways;
	}

	@Transient
	@JsonProperty
	private boolean updateRvDetails = false;

	@Transient
	@JsonProperty
	private UserRvDetails userRvDetails;

	@Transient
	@JsonProperty
	private String plan_id = "NA";

	@Transient
	@JsonProperty
	private int device_cnt=0;
	
	
	@Transient
	char hexDigit[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };

	public User() {
		super();
		// TODO Auto-generated constructor stub
	}

	public User(String username, String password, Role role, Company company, String email, String mobileno) {
		super();
		this.username = username;
		this.password = password;
		this.role = role;
		this.company = company;
		this.email = email;
		this.mobileno = mobileno;
		encryptAndSetUser(username);
	}

	public User(long id, String username, String password, Role role, Company company, String email, String mobileno,
			boolean enable, boolean notification) {
		super();
		this.id = id;
		this.username = username;
		this.password = password;
		this.role = role;
		this.company = company;
		this.email = email;
		this.mobileno = mobileno;
		this.enable = enable;
		this.notification = notification;

		encryptAndSetUser(username);
	}

	public User(long id, String username, String password, Role role, Company company, String email, String mobileno,
			boolean enable, boolean notification, String alternateemail, String alternatephone) {
		super();
		this.id = id;
		this.username = username;
		this.password = password;
		this.role = role;
		this.company = company;
		this.email = email;
		this.mobileno = mobileno;
		this.enable = enable;
		this.notification = notification;
		this.alternateemail = alternateemail;
		this.alternatephone = alternatephone;
		encryptAndSetUser(username);
	}

	public String getPassword() {
		return password;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getId() {
		return id;
	}

	public String getUsername() {
		return username;
	}

	public String getAuthKey() {
		return authKey;
	}

	public Role getRole() {
		return role;
	}

	public Company giveCompany() {
		return company;
	}

	public void encryptAndSetUser(String user) {
		if (user != null) {
			MessageDigest msgDigest = null;
			try {
				msgDigest = MessageDigest.getInstance("SHA1");
				msgDigest.update(user.getBytes());
			} catch (NoSuchAlgorithmException e) {
			}

			byte rawByte[] = msgDigest.digest();
			this.authKey = bytesToHex(rawByte).toLowerCase();

		}
	}

	public String getAlternateemail() {
		return alternateemail;
	}

	public String getAlternatephone() {
		return alternatephone;
	}

	public void setAlternateemail(String alternateemail) {
		this.alternateemail = alternateemail;
	}

	public void setAlternatephone(String alternatephone) {
		this.alternatephone = alternatephone;
	}

	public String bytesToHex(byte[] b) {

		StringBuffer buf = new StringBuffer();
		for (int j = 0; j < b.length; j++) {
			buf.append(hexDigit[(b[j] >> 4) & 0x0f]);
			buf.append(hexDigit[b[j] & 0x0f]);
		}
		return buf.toString();
	}

	public String getEmail() {
		return email;
	}

	public String getMobileno() {
		return mobileno;
	}

	public int getWebappid() {
		return webappid;
	}

	public void setWebappid(int webappid) {
		this.webappid = webappid;
	}

	public int getMobileappid() {
		return mobileappid;
	}

	public void setMobileappid(int mobileappid) {
		this.mobileappid = mobileappid;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public int getResetpassword() {
		return resetpassword;
	}

	public void setResetpassword(int resetpassword) {
		this.resetpassword = resetpassword;
	}

//	public Company getCompany() {
//		return company;
//	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public void setMobileno(String mobileno) {
		this.mobileno = mobileno;
	}

	public boolean isNotification() {
		return notification;
	}

	public void setNotification(boolean notification) {
		this.notification = notification;
	}

	public void setAuthKey(String authKey) {
		this.authKey = authKey;
	}

	/**
	 * @return the firstname
	 */
	public String getFirstname() {
		return firstname;
	}

	/**
	 * @param firstname the firstname to set
	 */
	public void setFirstname(String firstname) {
		this.firstname = firstname;
	}

	/**
	 * @return the lastname
	 */
	public String getLastname() {
		return lastname;
	}

	/**
	 * @param lastname the lastname to set
	 */
	public void setLastname(String lastname) {
		this.lastname = lastname;
	}

	/**
	 * @return the zipcode
	 */
	public String getZipcode() {
		return zipcode;
	}

	/**
	 * @param zipcode the zipcode to set
	 */
	public void setZipcode(String zipcode) {
		this.zipcode = zipcode;
	}

	/**
	 * @return the city
	 */
	public String getCity() {
		return city;
	}

	/**
	 * @param city the city to set
	 */
	public void setCity(String city) {
		this.city = city;
	}

	/**
	 * @return the state
	 */
	public String getState() {
		return state;
	}

	/**
	 * @param state the state to set
	 */
	public void setState(String state) {
		this.state = state;
	}

	/**
	 * @return the signupType
	 */
	public SignupType getSignupType() {
		return signupType;
	}

	/**
	 * @param signupType the signupType to set
	 */
	public void setSignupType(SignupType signupType) {
		this.signupType = signupType;
	}

	/**
	 * @return the signuptoken
	 */
	public String getSignuptoken() {
		return signuptoken;
	}

	/**
	 * @param signuptoken the signuptoken to set
	 */
	public void setSignuptoken(String signuptoken) {
		this.signuptoken = signuptoken;
	}

	public String getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}

	public String getUpdatedOn() {
		return updatedOn;
	}

	public void setUpdatedOn(String updatedOn) {
		this.updatedOn = updatedOn;
	}

	public boolean isVerified() {
		return isVerified;
	}

	public void setVerified(boolean isVerified) {
		this.isVerified = isVerified;
	}

	public UserVerification getVerificationToken() {
		return verificationToken;
	}

	public void setVerificationToken(UserVerification verificationToken) {
		this.verificationToken = verificationToken;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getChargebeeid() {
		return chargebeeid;
	}

	public void setChargebeeid(String chargebeeid) {
		this.chargebeeid = chargebeeid;
	}

	public boolean isIn_app() {
		return in_app;
	}

	public void setIn_app(boolean in_app) {
		this.in_app = in_app;
	}

	public int getLastlogintype() {
		return lastlogintype;
	}

	public void setLastlogintype(int lastlogintype) {
		this.lastlogintype = lastlogintype;
	}

	public boolean isCompletesetup() {
		return completesetup;
	}

	public void setCompletesetup(boolean completesetup) {
		this.completesetup = completesetup;
	}

	public String getLastlogintime() {
		return lastlogintime;
	}

	public void setLastlogintime(String lastlogintime) {
		this.lastlogintime = lastlogintime;
	}

	public String getImageUrl() {
		return imageUrl;
	}

	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}

	public int getInapp_purchase() {
		return inapp_purchase;
	}

	public void setInapp_purchase(int inapp_purchase) {
		this.inapp_purchase = inapp_purchase;
	}

	public boolean isUpdateRvDetails() {
		return updateRvDetails;
	}

	public void setUpdateRvDetails(boolean updateRvDetails) {
		this.updateRvDetails = updateRvDetails;
	}

	public UserRvDetails getUserRvDetails() {
		return userRvDetails;
	}

	public void setUserRvDetails(UserRvDetails userRvDetails) {
		this.userRvDetails = userRvDetails;
	}

	public String getPlan_id() {
		return plan_id;
	}

	public void setPlan_id(String plan_id) {
		this.plan_id = plan_id;
	}

	public int getDevice_cnt() {
		return device_cnt;
	}

	public void setDevice_cnt(int device_cnt) {
		this.device_cnt = device_cnt;
	}

	public String getPlan_ver() {
		return plan_ver;
	}

	public void setPlan_ver(String plan_ver) {
		this.plan_ver = plan_ver;
	}

	public String getPassword_ver() {
		return password_ver;
	}

	public void setPassword_ver(String password_ver) {
		this.password_ver = password_ver;
	}

	public boolean isDelete_user() {
		return delete_user;
	}

	public void setDelete_user(boolean delete_user) {
		this.delete_user = delete_user;
	}

	public String getDelete_time() {
		return delete_time;
	}

	public void setDelete_time(String delete_time) {
		this.delete_time = delete_time;
	}

	public String getRecharge_custid() {
		return recharge_custid;
	}

	public void setRecharge_custid(String recharge_custid) {
		this.recharge_custid = recharge_custid;
	}
	
}
