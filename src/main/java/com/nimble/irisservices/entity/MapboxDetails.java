package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "mapbox_details")
public class MapboxDetails {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name = "from_lat")
	private double from_lat;
	
	@Column(name = "from_lon")
	private double from_lon;
	
	@Column(name = "to_lat")
	private double to_lat;
	
	@Column(name = "to_lon")
	private double to_lon;	
	
	@Column(name="image_path")
	private String image_path = "NA";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public double getFrom_lat() {
		return from_lat;
	}

	public void setFrom_lat(double from_lat) {
		this.from_lat = from_lat;
	}

	public double getFrom_lon() {
		return from_lon;
	}

	public void setFrom_lon(double from_lon) {
		this.from_lon = from_lon;
	}

	public double getTo_lat() {
		return to_lat;
	}

	public void setTo_lat(double to_lat) {
		this.to_lat = to_lat;
	}

	public double getTo_lon() {
		return to_lon;
	}

	public void setTo_lon(double to_lon) {
		this.to_lon = to_lon;
	}

	public String getImage_path() {
		return image_path;
	}

	public void setImage_path(String image_path) {
		this.image_path = image_path;
	}

	public MapboxDetails() {
		
	}
	
	public MapboxDetails(long id, double from_lat, double from_lon, double to_lat, double to_lon, String image_path) {
		super();
		this.id = id;
		this.from_lat = from_lat;
		this.from_lon = from_lon;
		this.to_lat = to_lat;
		this.to_lon = to_lon;
		this.image_path = image_path;
	}
	
	
	

}
