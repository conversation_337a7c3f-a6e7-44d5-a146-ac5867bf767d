package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="gatewayreport", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class GatewayRpt  implements Serializable{
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	@Column(name="version")
	private String version;
	@Column(name="datetime")
	private Timestamp datetime;
	@Column(name="date")
	private Date date;
	@Column(name="time")
	private Time time;
	@Column(name="timezone")
	private String timezone;
	
	@Column(name="lat")
	private double lat;
	@Column(name="latdir")
	private String latdir;
	@Column(name="lon")
	private double lon;
	@Column(name="londir")
	private String londir;
	@Column(name="speed")
	private float speed;
	@Column(name="distance")
	private float distance;
	@Column(name="accuracy")
	private float accuracy;
	@Column(name="heading")
	private String heading;
	@Column(name="idletimer")
	private int idletimer;
	@Column(name="gpsdatetime")
	private Timestamp gpsdatetime;
	@Column(name="gpsstatus")
	private String gpsstatus;
	@Column(name="gpsinfo")
	private String gpsinfo;
	@Column(name="gpsmode")
	private int gpsmode;
	@Column(name="mems")
	private String mems;
	@Column(name="eventid")
	private String eventid;
	@Column(name="nmeventid")
	private String nmeventid;
	@Column(name="iostatus")
	private int iostatus;
	@Column(name="distfromlastloc")
	private float distfromlastloc;
	@Column(name="fencebreach")
	private String fencebreach;
	@Column(name="address")
	private String address;
	
	@Column(name="battery")
	private int battery;
	@Column(name="rawrssi")
	private int rawrssi;
	@Column(name="rssi")
	private String rssi;
	@Column(name="motion")
	private String motion;
	@Column(name="externalsensor")
	private float extsensor;
	@Column(name="humidity")
	private float humidity;
	@Column(name="temperature")
	private float temperature;
	@Column(name="tempseverity")
	private int tempseverity;
	@Column(name="light")
	private float light;
	@Column(name="pressure")
	private float pressure;
	
	@Column(name="cellIdlat")
	private double cellidlat;
	@Column(name="cellIdlon")
	private double cellidlon;
	@Column(name="cellIdacc")
	private float cellidacc;
	@Column(name="sfwpkt")
	private int sfwpkt;
	
	@Column(name="heat_index")
	private float heat_index;
	
    @ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="gateway_id")
	//@JsonBackReference
    private Gateway gateway;	
    
    @Transient
	private String nmeventName;
   
	
    @ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="cmp_id")
	//@JsonBackReference
    private Company company;
    
    @Column(name="createdon")
	private Timestamp createdon;
    
    @Column(name="qrc")
	private String qrc="00000";
    
	@Column(name="packetid")
	private int packetid=0;
	
    @Column(name="fota_ver")
    private String fota_ver = "NA";

    @Column(name="hwid_pin")
    private String hwid_pin = "NA"; 
    
	@Column(name="prev_wakeup_time")
	private long prev_wakeup_time=0;
    
    @Column(name="aqi")
	private int aqi=0;
    
    @Column(name="voc")
	private int voc=0;
    
    @Column(name="co2")
	private int co2=0;
    
    @Column(name="tx_mode")
	private int tx_mode=0;
 
    @Column(name="cel_info")
    private String cel_info="";

	public GatewayRpt(String version, Timestamp datetime, Date date, Time time,	String timezone, double lat, 
			String latdir, double lon,String londir, float speed, float distance, float accuracy,String heading, 
			int idletimer, Timestamp gpsdatetime,String gpsstatus, String gpsinfo, String mems, String eventid,
			String nmeventid, int iostatus, float distfromlastloc,	String fencebreach, String address,int battery, 
			int rawrssi, String rssi, String motion, float extsensor, float humidity,float temperature,
			int tempseverity, float light, float pressure,double cellidlat, double cellidlon, float cellidacc,
			int gpsmode,int sfwpkt, Gateway gateway,Company company,float heat_index,Timestamp createdon,
			String qrc,int packetid) {
		super();
		this.version = version;
		this.datetime = datetime;
		this.date = date;
		this.time = time;
		this.timezone = timezone;
		this.lat = lat;
		this.latdir = latdir;
		this.lon = lon;
		this.londir = londir;
		this.speed = speed;
		this.distance = distance;
		this.accuracy = accuracy;
		this.heading = heading;
		this.idletimer = idletimer;
		this.gpsdatetime = gpsdatetime;
		this.gpsstatus = gpsstatus;
		this.gpsinfo = gpsinfo;
		this.mems = mems;
		this.eventid = eventid;
		this.nmeventid = nmeventid;
		this.iostatus = iostatus;
		this.distfromlastloc = distfromlastloc;
		this.fencebreach = fencebreach;
		this.address = address;
		this.battery = battery;
		this.rawrssi = rawrssi;
		this.rssi = rssi;
		this.motion = motion;
		this.extsensor = extsensor;
		this.humidity = humidity;
		this.temperature = temperature;
		this.tempseverity = tempseverity;
		this.light = light;
		this.pressure = pressure;
		this.cellidlat = cellidlat;
		this.cellidlon = cellidlon;
		this.cellidacc = cellidacc;
		this.sfwpkt  = sfwpkt;
		this.gpsmode = gpsmode;
		this.gateway = gateway;
		this.company = company;
		this.heat_index=heat_index;
		this.createdon = createdon;
		this.qrc = qrc;
		this.packetid = packetid;
	}

	public long getId() {
		return id;
	}


	public String getVersion() {
		return version;
	}


	public Timestamp getDatetime() {
		return datetime;
	}

	

	public Date getDate() {
		return date;
	}


	public Time getTime() {
		return time;
	}


	public String getMems() {
		return mems;
	}


	public String getTimezone() {
		return timezone;
	}


	public double getLat() {
		return lat;
	}


	public String getLatdir() {
		return latdir;
	}


	public double getLon() {
		return lon;
	}


	public String getLondir() {
		return londir;
	}


	public float getSpeed() {
		return speed;
	}


	public float getDistance() {
		return distance;
	}


	public float getAccuracy() {
		return accuracy;
	}


	public String getHeading() {
		return heading;
	}


	public int getIdletimer() {
		return idletimer;
	}


	public Timestamp getGpsdatetime() {
		return gpsdatetime;
	}


	public String getGpsstatus() {
		return gpsstatus;
	}


	public String getGpsinfo() {
		return gpsinfo;
	}


	public String getEventid() {
		return eventid;
	}


	public String getNmeventid() {
		return nmeventid;
	}


	public int getIostatus() {
		return iostatus;
	}


	public float getDistfromlastloc() {
		return distfromlastloc;
	}


	public String getFencebreach() {
		return fencebreach;
	}


	public String getAddress() {
		return address;
	}


	public int getBattery() {
		return battery;
	}


	public int getRawrssi() {
		return rawrssi;
	}

	public String getRssi() {
		return rssi;
	}

	public String getMotion() {
		return motion;
	}


	public float getExtsensor() {
		return extsensor;
	}


	public float getHumidity() {
		return humidity;
	}


	public float getTemperature() {
		return temperature;
	}

	public int getTempseverity() {
		return tempseverity;
	}


	public float getLight() {
		return light;
	}


	public float getPressure() {
		return pressure;
	}


	public Gateway getGateway() {
		return gateway;
	}


	public Company getCompany() {
		return company;
	}

	public double getCellidlat() {
		return cellidlat;
	}


	public double getCellidlon() {
		return cellidlon;
	}


	public float getCellidacc() {
		return cellidacc;
	}


	public int getGpsmode() {
		return gpsmode;
	}

	public int getSfwpkt() {
		return sfwpkt;
	}

	public float getHeat_index() {
		return heat_index;
	}

	public void setHeat_index(float heat_index) {
		this.heat_index = heat_index;
	}

	public Timestamp getCreatedon() {
		return createdon;
	}

	public void setCreatedon(Timestamp createdon) {
		this.createdon = createdon;
	}

	public String getQrc() {
		return qrc;
	}

	public void setQrc(String qrc) {
		this.qrc = qrc;
	}

	public int getPacketid() {
		return packetid;
	}

	public void setPacketid(int packetid) {
		this.packetid = packetid;
	}

	public String getFota_ver() {
		return fota_ver;
	}

	public void setFota_ver(String fota_ver) {
		this.fota_ver = fota_ver;
	}

	public String getHwid_pin() {
		return hwid_pin;
	}

	public void setHwid_pin(String hwid_pin) {
		this.hwid_pin = hwid_pin;
	}
	
	public String getNmeventName() {
		return nmeventName;
	}

	public void setNmeventName(String nmeventName) {
		this.nmeventName = nmeventName;
	}

	public long getPrev_wakeup_time() {
		return prev_wakeup_time;
	}

	public void setPrev_wakeup_time(long prev_wakeup_time) {
		this.prev_wakeup_time = prev_wakeup_time;
	}

	public int getAqi() {
		return aqi;
	}

	public void setAqi(int aqi) {
		this.aqi = aqi;
	}

	public int getVoc() {
		return voc;
	}

	public void setVoc(int voc) {
		this.voc = voc;
	}

	public int getCo2() {
		return co2;
	}

	public void setCo2(int co2) {
		this.co2 = co2;
	}
		   
	public int getTx_mode() {
		return tx_mode;
	}

	public void setTx_mode(int tx_mode) {
		this.tx_mode = tx_mode;
	}

	public String getCel_info() {
		return cel_info;
	}

	public void setCel_info(String cel_info) {
		this.cel_info = cel_info;
	}

	public GatewayRpt() {
		super();
	}
}
