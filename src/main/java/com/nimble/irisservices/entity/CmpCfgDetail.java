package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="companyinfo", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class CmpCfgDetail  implements Serializable {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	@Column(name="noofverticalgroup")
	private String noofverticalgroup;
	@Column(name="vgroupname")
	private String vgroupname;
	@Column(name="vsubgroupname")
	private String vsubgroupname;
	@Column(name="temperatureunit")
	private String temperatureunit;
	@Column(name="humidityunit")
	private String humidityunit;
	@Column(name="pressureunit")
	private String pressureunit;
	@Column(name="speedunit")
	private String speedunit;
	@Column(name="lightunit")
	private String lightunit;
	@Column(name="moistureunit")
	private String moistureunit;
	@Column(name="gatewayname")
	private String gatewayname;
	@Column(name="nodename")
	private String nodename;
	@Column(name="panic/tamper")
	private String panic_tamper;
	@Column(name="memstype")
	private String memstype;
	@Column(name="incursionenable")
	private String incursionenable;
	@Column(name="realtimemonitor")
	private boolean realtimemonitor;
	
	 @OneToOne(cascade = CascadeType.ALL)
	 @JoinColumn(name="cmp_id")
	 private Company company;

	public long getId() {
		return id;
	}

	public String getNoofverticalgroup() {
		return noofverticalgroup;
	}

	public String getVgroupname() {
		return vgroupname;
	}

	public String getVsubgroupname() {
		return vsubgroupname;
	}

	public String getTemperatureunit() {
		return temperatureunit;
	}

	public String getHumidityunit() {
		return humidityunit;
	}

	public String getPressureunit() {
		return pressureunit;
	}

	public String getSpeedunit() {
		return speedunit;
	}

	public String getLightunit() {
		return lightunit;
	}

	public String getMoistureunit() {
		return moistureunit;
	}

	public String getGatewayname() {
		return gatewayname;
	}

	public String getNodename() {
		return nodename;
	}

	public String getPanic_tamper() {
		return panic_tamper;
	}

	public String getMemstype() {
		return memstype;
	}

	public String getIncursionenable() {
		return incursionenable;
	}

	public boolean isRealtimemonitor() {
		return realtimemonitor;
	}

	public Company getCompany() {
		return company;
	}
	 
	 

}
