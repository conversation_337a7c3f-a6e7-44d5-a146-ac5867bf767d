package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.validator.constraints.NotEmpty;

@Entity
@Table(name="inventory", uniqueConstraints =@UniqueConstraint (columnNames={"id","qrc"}))
public class Inventory implements Serializable{

	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Id
	@NotEmpty(message ="qrc should not be null")
	@Column(name = "qrc")
	private String qrc;
	
	@NotEmpty(message ="meid should not be null")
	@Column(name = "meid")
	private String meid;
	
	@NotEmpty(message ="mac id should not be null")
	@Column(name = "mac_id")
	private String mac_id;
	
	@Column(name= "mdn")
	private String mdn ;
	
	@Column(name = "sensoravailable")
	private String sensoravailable="NA";
	
	@Column(name = "probetype")
	private String probetype="NA";
	
	@NotEmpty(message ="serialnumber should not be null")
	@Column(name = "serialnumber")
	private String serialnumber;
	
	@NotEmpty(message ="devicemodelnumber should not be null")
	@Column(name = "devicemodelnumber")
	private String devicemodelnumber;
	
	@Column(name = "datetime")
	private String datetime = "1111-11-11 11:11:11";
	
	@Column(name = "sim_no")
	private String sim_no = "0";
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="devicestate_id")
    private Devicestate devicestate;
	@Transient
	private long devicestateid = 7;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="location_id")
//	@JsonIgnore
    private Devicelocation device_location;
	@Transient
	private long locationid = 1;
	
	@ManyToOne(cascade = CascadeType.ALL/*, fetch = FetchType.LAZY*/)
	@JoinColumn(name="order_id")
//	@JsonIgnore
    private Orders order;
	@Transient
	private String orderid = null;
	
	@Column(name = "device_status")
	private String device_status = "available";
	
	@Column(name = "action")
	private String action = "active";
	
	@Column(name = "sim_vendor")
	private String sim_vendor="NA";
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="plan_id")
//	@JsonIgnore
    private Plans plan;
	@Transient
	private long planid;
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getMac_id() {
		return mac_id;
	}

	public void setMac_id(String mac_id) {
		this.mac_id = mac_id;
	}

	public String getMdn() {
		return mdn;
	}

	public void setMdn(String mdn) {
		this.mdn = mdn;
	}

	public String getSensoravailable() {
		return sensoravailable;
	}

	public void setSensoravailable(String sensoravailable) {
		this.sensoravailable = sensoravailable;
	}

	public String getProbetype() {
		return probetype;
	}

	public void setProbetype(String probetype) {
		this.probetype = probetype;
	}

	public String getQrc() {
		return qrc;
	}

	public void setQrc(String qrc) {
		this.qrc = qrc;
	}

	public String getSerialnumber() {
		return serialnumber;
	}

	public void setSerialnumber(String serialnumber) {
		this.serialnumber = serialnumber;
	}

	public String getDevicemodelnumber() {
		return devicemodelnumber;
	}

	public void setDevicemodelnumber(String devicemodelnumber) {
		this.devicemodelnumber = devicemodelnumber;
	}
	
	public String getDatetime() {
		//1111-11-11 11:11:11
		return datetime.substring(0, 16);
	}

	public void setDatetime(String datetime) {
		this.datetime = datetime;
	}

	public Devicestate getDevicestate() {
		return devicestate;
	}

	public void setDevicestate(Devicestate devicestate) {
		this.devicestate = devicestate;
	}
	
	public Devicelocation getDevice_location() {
		return device_location;
	}

	public void setDevice_location(Devicelocation device_location) {
		this.device_location = device_location;
	}

	/*public Orders getOrder() {
		return order;
	}

	public void setOrder(Orders order) {
		this.order = order;
	}*/

	public long givedevicestateid() {
		return devicestateid;
	}
	
	public void setdevicestateid(long devicestateid) {
		this.devicestateid = devicestateid;
	}
	
	public long givelocationid() {
		return locationid;
	}
	
	public void setlocationid(long locationid) {
		this.locationid = locationid;
	}
	
	public String getOrderid() {
		if(order != null)
			return Long.toString(order.getOrder_id());
		else
			return orderid;
	}
	
	public String giveOrderid() {
		return orderid;
	}
	
	public void setOrderid(String orderid) {
		this.orderid = orderid;
	}
	
	public String getSim_no() {
		return sim_no;
	}

	public void setSim_no(String sim_no) {
		this.sim_no = sim_no;
	}

	public String getDevice_status() {
		return device_status;
	}

	public void setDevice_status(String device_status) {
		this.device_status = device_status;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public Plans getPlan() {
		return plan;
	}

	public void setPlan(Plans plan) {
		this.plan = plan;
	}

	public long giveplanid() {
		return planid;
	}

	public void setplanid(long planid) {
		this.planid = planid;
	}

	public String getSim_vendor() {
		return sim_vendor;
	}

	public void setSim_vendor(String sim_vendor) {
		this.sim_vendor = sim_vendor;
	}
}
