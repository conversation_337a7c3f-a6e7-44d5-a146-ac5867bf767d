package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "reminder_views_datatils")
public class ReminderViews implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "userId")
	private long userId;

	@Column(name = "remindername")
	private String remindername;

	@Column(name = "reminder_id")
	private long remainderId;;

	@Column(name = "viewcomplete")
	private int viewcomplete = 0;

	@Column(name = "on_time")
	private int on_time = 0;

	@Column(name = "trigger_date")
	private String triggerDate;

	@Column(name = "createdon")
	private String createdon; // date

	@Column(name = "updatedon")
	private String updatedon; // date

	public int getOn_time() {
		return on_time;
	}

	public void setOn_time(int on_time) {
		this.on_time = on_time;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getRemaindername() {
		return remindername;
	}

	public void setRemaindername(String remaindername) {
		this.remindername = remaindername;
	}

	public int getViewcomplete() {
		return viewcomplete;
	}

	public void setViewcomplete(int viewcomplete) {
		this.viewcomplete = viewcomplete;
	}

	public String getTriggerDate() {
		return triggerDate;
	}

	public void setTriggerDate(String triggerDate) {
		this.triggerDate = triggerDate;
	}

	public String getCreatedon() {
		return createdon;
	}

	public void setCreatedon(String createdon) {
		this.createdon = createdon;
	}

	public String getUpdatedon() {
		return updatedon;
	}

	public void setUpdatedon(String updatedon) {
		this.updatedon = updatedon;
	}

	public long getRemainderId() {
		return remainderId;
	}

	public void setRemainderId(long remainderId) {
		this.remainderId = remainderId;
	}

}
