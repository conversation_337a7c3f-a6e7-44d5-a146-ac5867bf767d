package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "rv_checklist_type")
public class RVChecklistType implements Serializable {
	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	
	@Column(name = "checklist_type")
	private String checklist_type;	

	@Column(name = "orderno")
	private int orderno;
	
	@Column(name = "enable")
	private boolean enable;

	@Column(name = "createdon")
	private String createdon = "1753-01-01 00:00:00";

	@Column(name = "color_code")
	private String color_code;
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getChecklist_type() {
		return checklist_type;
	}

	public void setChecklist_type(String checklist_type) {
		this.checklist_type = checklist_type;
	}

	public int getOrderno() {
		return orderno;
	}

	public void setOrderno(int orderno) {
		this.orderno = orderno;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getCreatedon() {
		return createdon;
	}

	public void setCreatedon(String createdon) {
		this.createdon = createdon;
	}

	public String getColor_code() {
		return color_code;
	}

	public void setColor_code(String color_code) {
		this.color_code = color_code;
	}	
}
