package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="lastfotareport", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class LastFotaRpt {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	@Column(name="meid")
	private String meid;
	@Column(name="curr_version")
	private String curr_version;
	@Column(name="upg_version")
	private String upg_version;
	@Column(name="response_type")
	private String response_type;
	@Column(name="fota_status")
	private String fota_status;
	@Column(name="reason")
	private String reason;
	@Column(name="updated_date")
	private String updated_date;
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getMeid() {
		return meid;
	}
	public void setMeid(String meid) {
		this.meid = meid;
	}
	public String getCurr_version() {
		return curr_version;
	}
	public void setCurr_version(String curr_version) {
		this.curr_version = curr_version;
	}
	public String getUpg_version() {
		return upg_version;
	}
	public void setUpg_version(String upg_version) {
		this.upg_version = upg_version;
	}
	public String getResponse_type() {
		return response_type;
	}
	public void setResponse_type(String response_type) {
		this.response_type = response_type;
	}
	public String getFota_status() {
		return fota_status;
	}
	public void setFota_status(String fota_status) {
		this.fota_status = fota_status;
	}
	public String getReason() {
		return reason;
	}
	public void setReason(String reason) {
		this.reason = reason;
	}
	public String getUpdated_date() {
		return updated_date;
	}
	public void setUpdated_date(String updated_date) {
		this.updated_date = updated_date;
	}
}
