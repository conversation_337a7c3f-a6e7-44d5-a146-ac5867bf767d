package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="ask_feature")
public class AskFeature {

	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	private long user_id = 0;
	
	private String feature_request = "NA";
	
	private String updated_on = "NA";
	
	@Transient
	private String username = "NA";
	
	@Transient
	private String email = "NA";

	@Transient
	private String register_dt = "NA";
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getFeature_request() {
		return feature_request;
	}

	public void setFeature_request(String feature_request) {
		this.feature_request = feature_request;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public AskFeature() {
		super();
	}
	
	public AskFeature(long id, long user_id, String feature_request, String updated_on) {
		super();
		this.id = id;
		this.user_id = user_id;
		this.feature_request = feature_request;
		this.updated_on = updated_on;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getRegister_dt() {
		return register_dt;
	}

	public void setRegister_dt(String register_dt) {
		this.register_dt = register_dt;
	}

}
