package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

import com.nimble.irisservices.constant.IrisservicesConstants;

@Entity 
@Table(name="alert_cfg_wc")
public class AlertCfgWC {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	private long alert_type_id = 0;
	
	private long gateway_id = 0;
	
	private boolean enable = false;
	
	private boolean sms = true;
	
	private boolean email = true;
	
	private boolean push_notification = true;
	
	private boolean in_app = false;
	
	private String updated_on = IrisservicesConstants.DEFAULT_DATE;
	
	private long notify_freq = 3600;
	
	private String email_ids = "NA";
	
	private String mobile_nos = "NA";
	
	private float min = 0;
	
	private float max = 0;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getAlert_type_id() {
		return alert_type_id;
	}

	public void setAlert_type_id(long alert_type_id) {
		this.alert_type_id = alert_type_id;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public boolean isSms() {
		return sms;
	}

	public void setSms(boolean sms) {
		this.sms = sms;
	}

	public boolean isEmail() {
		return email;
	}

	public void setEmail(boolean email) {
		this.email = email;
	}

	public boolean isPush_notification() {
		return push_notification;
	}

	public void setPush_notification(boolean push_notification) {
		this.push_notification = push_notification;
	}

	public boolean isIn_app() {
		return in_app;
	}

	public void setIn_app(boolean in_app) {
		this.in_app = in_app;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public long getNotify_freq() {
		return notify_freq;
	}

	public void setNotify_freq(long notify_freq) {
		this.notify_freq = notify_freq;
	}

	public String getEmail_ids() {
		return email_ids;
	}

	public void setEmail_ids(String email_ids) {
		this.email_ids = email_ids;
	}

	public String getMobile_nos() {
		return mobile_nos;
	}

	public void setMobile_nos(String mobile_nos) {
		this.mobile_nos = mobile_nos;
	}

	public float getMin() {
		return min;
	}

	public void setMin(float min) {
		this.min = min;
	}

	public float getMax() {
		return max;
	}

	public void setMax(float max) {
		this.max = max;
	}

	public AlertCfgWC() {
		super();
	}

	public AlertCfgWC(long id, long alert_type_id, long gateway_id, boolean enable, boolean sms, boolean email,
			boolean push_notification, boolean in_app, String updated_on, long notify_freq) {
		super();
		this.id = id;
		this.alert_type_id = alert_type_id;
		this.gateway_id = gateway_id;
		this.enable = enable;
		this.sms = sms;
		this.email = email;
		this.push_notification = push_notification;
		this.in_app = in_app;
		this.updated_on = updated_on;
		this.notify_freq = notify_freq;
	}
	
	
	public AlertCfgWC( AlertCfgWC alertCfgWC ) {
		this.id = alertCfgWC.getId();
		this.alert_type_id = alertCfgWC.getAlert_type_id();
		this.gateway_id = alertCfgWC.getGateway_id();
		this.enable = alertCfgWC.isEnable();
		this.sms = alertCfgWC.isSms();
		this.email = alertCfgWC.isEmail();
		this.push_notification = alertCfgWC.isPush_notification();
		this.in_app = alertCfgWC.isIn_app();
		this.updated_on = alertCfgWC.getUpdated_on();
		this.notify_freq = alertCfgWC.getNotify_freq();
		this.email_ids = alertCfgWC.getEmail_ids();
		this.mobile_nos = alertCfgWC.getMobile_nos();
		this.min = alertCfgWC.getMin();
		this.max = alertCfgWC.getMax();
	}

	
	
}
