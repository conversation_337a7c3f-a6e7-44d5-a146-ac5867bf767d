package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

import org.hibernate.annotations.GenericGenerator;

@Entity(name="simreactivationhistory")
public class SimReactivationHistory {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@Column(name="meid")
	private String meid;
	
	@Column(name="status")
	private String status;
	
	@Column(name="retrycount")
	private int retrycount = 0;
	
	@Column(name="createdon")
	private String createdOn;
	
	@Column(name="updatedon")
	private String updatedOn;
	
	@Column(name="request_id")
	private String requestId;
	
	@Column(name="action")
	private String action;
	
	@Column(name="last_Scheduled_retryon")
	private String lastRetry;
	

	public String getLastRetry() {
		return lastRetry;
	}

	public void setLastRetry(String lastRetry) {
		this.lastRetry = lastRetry;
	}

	public SimReactivationHistory() {
		super();
		// TODO Auto-generated constructor stub
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public int getRetrycount() {
		return retrycount;
	}

	public void setRetrycount(int retrycount) {
		this.retrycount = retrycount;
	}

	public String getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}

	public String getUpdatedOn() {
		return updatedOn;
	}

	public void setUpdatedOn(String updatedOn) {
		this.updatedOn = updatedOn;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}
	
	
}
