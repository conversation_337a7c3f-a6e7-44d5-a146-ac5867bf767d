package com.nimble.irisservices.entity;

import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="pet_profile")
public class PetProfile {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="gateway_id")	
    private Gateway gateway;
	
	@Column(name="name")
	private String name;
	
	@Column(name="birth_date")
	private Date birth_date;
	
	@Column(name="sex")
	private String sex;
	
	@Column(name="breed")
	private String breed;
	
	@Column(name="height")
	private String height="0";
	
	@Column(name="weight")
	private String weight="0";
	
	@Column(name="remarks")
	private String remarks="";
	
	@Column(name="imageurl")
	private String imageurl="https://nimbleapi-images.s3-us-west-2.amazonaws.com/mobileappimages/defaultpetProfile.png";
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "speciesid")
	private PetSpecies petSpecies;
	
	@Column(name="intact")
	private boolean intact = true;
	
	@Column(name="working")
	private boolean working = true;
	
	@Column(name="activitylevel")
	private String activitylevel = "Moderate";
	
	@Column(name="user_id")
	private long user_id;
	
	@Column(name="enable")
	private boolean enable = true;
	
	public PetProfile(){
		super();
	}
	
	public PetProfile(long id,Gateway gateway,String name,Date birth_date,
			String sex,String breed,String height,String weight,String remarks,
			String imageurl,long user_id){
		this.id = id;
		this.gateway = gateway;
		this.name=name;
		this.birth_date=birth_date;
		this.sex = sex;
		this.breed  =breed;
		this.height = height;
		this.weight = weight;
		this.remarks = remarks;
		this.imageurl = imageurl;
		this.user_id = user_id;
	}
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Gateway getGateway() {
		return gateway;
	}

	public void setGateway(Gateway gateway) {
		this.gateway = gateway;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getBreed() {
		return breed;
	}

	public void setBreed(String breed) {
		this.breed = breed;
	}

	public String getHeight() {
		return height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getImageurl() {
		return imageurl;
	}

	public void setImageurl(String imageurl) {
		this.imageurl = imageurl;
	}

	public Date getBirth_date() {
		return birth_date;
	}

	public void setBirth_date(Date birth_date) {
		this.birth_date = birth_date;
	}

	public PetSpecies getPetSpecies() {
		return petSpecies;
	}

	public void setPetSpecies(PetSpecies petSpecies) {
		this.petSpecies = petSpecies;
	}

	public boolean isIntact() {
		return intact;
	}

	public void setIntact(boolean intact) {
		this.intact = intact;
	}

	public boolean isWorking() {
		return working;
	}

	public void setWorking(boolean working) {
		this.working = working;
	}

	public String getActivitylevel() {
		return activitylevel;
	}

	public void setActivitylevel(String activitylevel) {
		this.activitylevel = activitylevel;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	} 	
}
