package com.nimble.irisservices.entity;

import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="calibration", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class Calibration {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="user_id")
    private User user;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="monitor_id")
    private F5Monitor f5monitor;
	
	@Column(name="temperature")
	private String temperature;
	
	@Column(name="date_time")
	private Timestamp date_time;
	
	@Column(name="status")
	private String status;
	
	public Calibration() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	public Calibration(long id,User user,F5Monitor f5monitor,String temp,Timestamp datetime,String status){
		this.setUser(user);
		this.setF5monitor(f5monitor);
		this.setDatetime(datetime);
		this.setStatus(status);
		this.setTemperature(temp);
		this.id=id;
		
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public F5Monitor getF5monitor() {
		return f5monitor;
	}

	public void setF5monitor(F5Monitor f5monitor) {
		this.f5monitor = f5monitor;
	}

	public String getTemperature() {
		return temperature;
	}

	public void setTemperature(String temperature) {
		this.temperature = temperature;
	}

	public Timestamp getDatetime() {
		return date_time;
	}

	public void setDatetime(Timestamp datetime) {
		this.date_time = datetime;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}
	
}
