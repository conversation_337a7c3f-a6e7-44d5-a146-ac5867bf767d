package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "cancelsub_history")
public class CancelsubHistory implements Serializable  {

	private static final long serialVersionUID = 1L;
	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	
	@Column(name = "orderid")
	private String orderid;
	
	@Column(name = "email")
	private String email;
	
	@Column(name = "meid")
	private String meid;
	
	@Column(name = "chargebee_id")
	private String chargebee_id;
	
	@Column(name = "subsciption_id")
	private String subsciption_id;
	
	@Column(name = "plan_name")
	private String plan_name;
	
	@Column(name = "status")
	private String status;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getOrderid() {
		return orderid;
	}

	public void setOrderid(String orderid) {
		this.orderid = orderid;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getSubsciption_id() {
		return subsciption_id;
	}

	public void setSubsciption_id(String subsciption_id) {
		this.subsciption_id = subsciption_id;
	}

	public String getPlan_name() {
		return plan_name;
	}

	public void setPlan_name(String plan_name) {
		this.plan_name = plan_name;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getChargebee_id() {
		return chargebee_id;
	}

	public void setChargebee_id(String chargebee_id) {
		this.chargebee_id = chargebee_id;
	}
	
}
