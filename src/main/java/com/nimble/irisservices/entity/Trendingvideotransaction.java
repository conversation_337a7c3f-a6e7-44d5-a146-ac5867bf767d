package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

import com.fasterxml.jackson.annotation.JsonIgnore;

@Entity
@Table(name = "trending_video_transaction" )
public class Trendingvideotransaction implements Serializable {
	private static final long serialVersionUID = 0l;

	@Id  
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	@Column(name="id")
	private long id;

	@JsonIgnore
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="user_id")
	private User user_id;

	@JsonIgnore
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="video_id")
	private Trendingvideo video_id;

	@Column(name = "Enabled")
	private boolean video_Status;

	@Column(name="likes")
	private boolean like = false;

	@Column(name="dislike")
	private boolean dislike = false;

	@Column(name="viewcount")
	private int viewCount = 0;

	@Column(name="comments")
	private String comments="NA";
	
	@Column(name = "lastseen")
	private Timestamp lastSeen = Timestamp.valueOf("1753-01-01 00:00:00.0");
	
	public Timestamp getLastSeen() {
		return lastSeen;
	}

	public void setLastSeen(Timestamp lastSeen) {
		this.lastSeen = lastSeen;
	}

	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public User getUserId() {
		return user_id;
	}

	public void setUserId(User userId) {
		this.user_id = userId;
	}

	public Trendingvideo getVideoid() {
		return video_id;
	}

	public void setVideoid(Trendingvideo videoid) {
		this.video_id = videoid;
	}

	public boolean isEnable() {
		return video_Status;
	}

	public void setEnable(boolean enable) {
		this.video_Status = enable;
	}

	public boolean getLike() {
		return like;
	}

	public void setLike(boolean like) {
		this.like = like;
	}

	public boolean getDislike() {
		return dislike;
	}

	public void setDislike(boolean dislike) {
		this.dislike = dislike;
	}

	public int getViewCount() {
		return viewCount;
	}

	public void setViewCount(int viewCount) {
		this.viewCount = viewCount;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

}
