package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="ccp_template_config", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class CCPTemplateConfig implements Serializable{

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="user_id")
    private User user;
	
	@Column(name="name")
	private String name;
	
	@Column(name="template_enable")
	private boolean template_enable;
	
	@Column(name="freq_enable")
	private boolean freq_enable;
	
	@Column(name="start_time1")
	private String start_time1 	= null;
	
	@Column(name="end_time1")
	private String end_time1 	= null;
	
	@Column(name="start_time2")
	private String start_time2 	= null;
	
	@Column(name="end_time2")
	private String end_time2 	= null;
	
	@Column(name="start_time3")
	private String start_time3 	= null;
	
	@Column(name="end_time3")
	private String end_time3 	= null;
	
	@Column(name="start_time4")
	private String start_time4	= null;
	
	@Column(name="end_time4")
	private String end_time4	= null;
	
	public CCPTemplateConfig() {
		super();
		// TODO Auto-generated constructor stub
	}

	public CCPTemplateConfig(long id,String name,boolean btemplate_enable,boolean bfreq_enable,
						String startTime1,String endTime1,
						String startTime2,String endTime2,
						String startTime3,String endTime3,
						String startTime4,String endTime4,
						User user) {
		
		
		super();
		this.setId(id);
		this.setName(name);
		this.setTemplate_enable(btemplate_enable);
		this.setFreq_enable(bfreq_enable);
		this.start_time1 = startTime1;
		this.end_time1 = endTime1;
		
		this.start_time2 = startTime2;
		this.end_time2 = endTime2;
		
		this.start_time3 = startTime3;
		this.end_time3 = endTime3;
		
		this.start_time4 = startTime4;
		this.end_time4 = endTime4;
		this.user = user;
		
		
		/*for(int i=0;i<timeValues.length;i++){
			switch(i){
			case 0:
				this.setStart_time1(timeValues[0][0]);
				this.setEnd_time1(timeValues[0][1]);				
				break;
			case 1:
				this.setStart_time2(timeValues[1][0]);
				this.setEnd_time2(timeValues[1][1]);
				break;
			case 2:
				this.setStart_time3(timeValues[2][0]);
				this.setEnd_time3(timeValues[2][1]);
				break;
			case 3:
				this.setStart_time4(timeValues[3][0]);
				this.setEnd_time4(timeValues[3][1]);
				break;
			//only 4 sets of times are taken for now
			}
		}*/
				
	}
	
	public void setStartTimeEndTime(String[][] values){
		
		/*if(values.length == 4) {// statically 4 values will be initialized in JTemplate "startEndTime" string array
			this.start_time1 = values[0][0];
			this.end_time1   = values[0][1];
			
			this.start_time2 = values[1][0];
			this.end_time2   = values[1][1];
			
			this.start_time3 = values[2][0];
			this.end_time3   = values[2][1];
			
			this.start_time4 = values[3][0];
			this.end_time4   = values[3][1];
		}*/
		
	
		
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public boolean isTemplate_enable() {
		return template_enable;
	}

	public void setTemplate_enable(boolean template_enable) {
		this.template_enable = template_enable;
	}

	public boolean isFreq_enable() {
		return freq_enable;
	}

	public void setFreq_enable(boolean freq_enable) {
		this.freq_enable = freq_enable;
	}

	public String getStart_time1() {
		return start_time1;
	}

	public void setStart_time1(String start_time1) {
		this.start_time1 = start_time1;
	}

	public String getEnd_time1() {
		return end_time1;
	}

	public void setEnd_time1(String end_time1) {
		this.end_time1 = end_time1;
	}

	public String getStart_time2() {
		return start_time2;
	}

	public void setStart_time2(String start_time2) {
		this.start_time2 = start_time2;
	}

	public String getEnd_time2() {
		return end_time2;
	}

	public void setEnd_time2(String end_time2) {
		this.end_time2 = end_time2;
	}

	public String getStart_time3() {
		return start_time3;
	}

	public void setStart_time3(String start_time3) {
		this.start_time3 = start_time3;
	}

	public String getEnd_time3() {
		return end_time3;
	}

	public void setEnd_time3(String end_time3) {
		this.end_time3 = end_time3;
	}

	public String getStart_time4() {
		return start_time4;
	}

	public void setStart_time4(String start_time4) {
		this.start_time4 = start_time4;
	}

	public String getEnd_time4() {
		return end_time4;
	}

	public void setEnd_time4(String end_time4) {
		this.end_time4 = end_time4;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}
}
