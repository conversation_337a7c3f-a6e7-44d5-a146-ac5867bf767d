package com.nimble.irisservices.entity;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="rv_user", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class RVUser {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="userId")
    private User userId;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="rvBlogId")
    private RVPetSafety rvBlogId;
	
	@Column(name="visitedCount")
	private long visitedCount ;
	
	public RVUser() {
		super();
		// TODO Auto-generated constructor stub
	}

	public RVUser(long id, User userId,RVPetSafety rvBlogId,long visitedCount) {
		super();		
		this.userId 	 = userId;
		this.id			 =id;
		this.rvBlogId	 =rvBlogId;
		this.visitedCount=visitedCount;
	}
	
	public RVUser(User userId,RVPetSafety rvBlogId,long visitedCount) {
		super();		
		this.userId 	 = userId;		
		this.rvBlogId	 =rvBlogId;
		this.visitedCount=visitedCount;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public User getUserId() {
		return userId;
	}

	public void setUserId(User userId) {
		this.userId = userId;
	}

	public RVPetSafety getRvBlogId() {
		return rvBlogId;
	}

	public void setRvBlogId(RVPetSafety rvBlogId) {
		this.rvBlogId = rvBlogId;
	}

	public long getVisitedCount() {
		return visitedCount;
	}

	public void setVisitedCount(long visitedCount) {
		this.visitedCount = visitedCount;
	}
	
	
}
