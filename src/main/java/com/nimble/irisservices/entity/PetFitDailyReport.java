package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="petfitdailyreportdata", uniqueConstraints = @UniqueConstraint(columnNames = { "id" }))
public class PetFitDailyReport  implements Serializable{
	
	public PetFitDailyReport() {
		super();
		// TODO Auto-generated constructor stub
	}

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
    @ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="gateway_id")
	//@JsonBackReference
    private Gateway gateway;
	
	@Column(name = "utcdate")
	private String utcDate;
	
	@Column(name = "totalstepcount")
	private int totalStepCount;
	
	@Column(name = "totalidlesecs")
	private int totalIdleSecs;
	
	@Column(name = "totalwalksecs")
	private int totalwalksecs;
	
	@Column(name = "totalrunsecs")
	private int totalRunSecs;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Gateway getGateway() {
		return gateway;
	}

	public void setGateway(Gateway gateway) {
		this.gateway = gateway;
	}

	public String getUtcDate() {
		return utcDate;
	}

	public void setUtcDate(String utcDate) {
		this.utcDate = utcDate;
	}

	public int getTotalStepCount() {
		return totalStepCount;
	}

	public void setTotalStepCount(int totalStepCount) {
		this.totalStepCount = totalStepCount;
	}

	public int getTotalIdleSecs() {
		return totalIdleSecs;
	}

	public void setTotalIdleSecs(int totalIdleSecs) {
		this.totalIdleSecs = totalIdleSecs;
	}

	public int getTotalwalksecs() {
		return totalwalksecs;
	}

	public void setTotalwalksecs(int totalwalksecs) {
		this.totalwalksecs = totalwalksecs;
	}

	public int getTotalRunSecs() {
		return totalRunSecs;
	}

	public void setTotalRunSecs(int totalRunSecs) {
		this.totalRunSecs = totalRunSecs;
	}

	public PetFitDailyReport(long id, Gateway gateway, String utcDate, int totalStepCount, int totalIdleSecs,
			int totalwalksecs, int totalRunSecs) {
		super();
		this.id = id;
		this.gateway = gateway;
		this.utcDate = utcDate;
		this.totalStepCount = totalStepCount;
		this.totalIdleSecs = totalIdleSecs;
		this.totalwalksecs = totalwalksecs;
		this.totalRunSecs = totalRunSecs;
	}
	

	
	
	
	
}
