package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;
@Entity
@Table(name="resettype", uniqueConstraints =@UniqueConstraint (columnNames={"reset_name" }) )

public class ResetType {
	private static final long serialVersionUID = 0l;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@Column(name="reset_name")
    private String reset_name;
	
	public ResetType() {
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getReset_name() {
		return reset_name;
	}

	public void setReset_name(String reset_name) {
		this.reset_name = reset_name;
	}	
}
