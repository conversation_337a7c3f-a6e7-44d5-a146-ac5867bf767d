package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "trending_video", uniqueConstraints =@UniqueConstraint (columnNames={"url"} ))
public class Trendingvideo implements Serializable {

	private static final long serialVersionUID = 1L;
	
	@Id  
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	@Column(name="id")
	private long id;

	@Column(name = "url")
	private String url  ;
	
	@Column(name = "title")
	private String title  ;

	@Column(name = "createdon")
	private Timestamp createdon ;
	
	@Column(name = "video_Status")
	private boolean video_Status;
	
	

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}	

	public Timestamp getCreatedon() {
		return createdon;
	}

	public void setCreatedon(Timestamp createdon) {
		this.createdon = createdon;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public boolean isVideo_Status() {
		return video_Status;
	}

	public void setVideo_Status(boolean video_Status) {
		this.video_Status = video_Status;
	}
	

	
	
}
