package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name="user_feature", uniqueConstraints =@UniqueConstraint (columnNames={"user_id","feature_id" }) )

public class UsertoFeature  implements Serializable{
	private static final long serialVersionUID = 0l;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id = 0 ;
	
    private long user_id;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch=FetchType.EAGER)
	@JoinColumn(name="feature_id")
	private Feature feature_id;
	
	@Column(name="txn_limit")
	private int txn_limit=0;
	
	@Column(name="extra_txn_limit")
	private int extra_txn_limit=0;
	
	@Column(name="remaining_limit")
	private int remaining_limit = 0;

	@Column(name="feature_code")
	private String feature_code = "NA";

	@Column(name="last_reset_on")
	private String last_reset_on = "1753-01-01 00:00:00";

	@ManyToOne(cascade = CascadeType.ALL, fetch=FetchType.EAGER)
	@JoinColumn(name="resettype_id")
	private ResetType resettype_id;

	@Column(name = "enable")
	private boolean enable =true;
	
//	@Column(name = "monitortype_id")
//	private String monitortype_id = "NA";
//	
//	@Column(name = "device_config")
//	private String device_config = "NA";

	@Transient
	private long featureid;
	
	@Transient
	private long resettype;
	
	@Column(name="addon_limit")
	private int addon_limit = 0;
	
	@Column(name = "unlimited_cr")
	private boolean unlimited_cr = false;
	
	public UsertoFeature()
	{
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public Feature getFeature_id() {
		return feature_id;
	}

	public void setFeature_id(Feature feature_id) {
		this.feature_id = feature_id;
	}
	
	public int getTxn_limit() {
		return txn_limit;
	}

	public void setTxn_limit(int txn_limit) {
		this.txn_limit = txn_limit;
	}

	public int getExtra_txn_limit() {
		return extra_txn_limit;
	}

	public void setExtra_txn_limit(int extra_txn_limit) {
		this.extra_txn_limit = extra_txn_limit;
	}

	public ResetType getResettype_id() {
		return resettype_id;
	}

	public void setResettype_id(ResetType resettype_id) {
		this.resettype_id = resettype_id;
	}
	
	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}
	
	public long getFeatureid() {
		return featureid;
	}

	public void setFeatureid(long featureid) {
		this.featureid = featureid;
	}
//	public String getMonitortype_id() {
//		return monitortype_id;
//	}
//
//	public void setMonitortype_id(String monitortype_id) {
//		this.monitortype_id = monitortype_id;
//	}
//
//	public String getDevice_config() {
//		return device_config;
//	}
//
//	public void setDevice_config(String device_config) {
//		this.device_config = device_config;
//	}

	public long getResettype() {
		return resettype;
	}

	public void setResettype(long resettype) {
		this.resettype = resettype;
	}

	public int getRemaining_limit() {
		return remaining_limit;
	}

	public void setRemaining_limit(int remaining_limit) {
		this.remaining_limit = remaining_limit;
	}

	public String getFeature_code() {
		return feature_code;
	}

	public void setFeature_code(String feature_code) {
		this.feature_code = feature_code;
	}

	public String getLast_reset_on() {
		return last_reset_on;
	}

	public void setLast_reset_on(String last_reset_on) {
		this.last_reset_on = last_reset_on;
	}

	public int getAddon_limit() {
		return addon_limit;
	}

	public void setAddon_limit(int addon_limit) {
		this.addon_limit = addon_limit;
	}

	public boolean isUnlimited_cr() {
		return unlimited_cr;
	}

	public void setUnlimited_cr(boolean unlimited_cr) {
		this.unlimited_cr = unlimited_cr;
	}	
	
}
