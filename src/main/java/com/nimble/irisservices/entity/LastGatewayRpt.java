package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;
//import org.junit.Ignore;

@Entity 
@Table(name="lastgatewayreport", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class LastGatewayRpt  implements Serializable{
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	@Column(name="version")
	private String version;
	@Column(name="datetime")
	private Timestamp datetime;
	@Column(name="date")
	private Date date;
	@Column(name="time")
	private Time time;
	@Column(name="timezone")
	private String timezone;
	
	@Column(name="lat")
	private double lat;
	@Column(name="latdir")
	private String latdir;
	@Column(name="lon")
	private double lon;
	@Column(name="londir")
	private String londir;
	@Column(name="speed")
	private float speed;
	@Column(name="distance")
	private float distance;
	@Column(name="accuracy")
	private float accuracy;
	@Column(name="heading")
	private String heading;
	@Column(name="idletimer")
	private int idletimer;
	@Column(name="gpsdatetime")
	private Timestamp gpsdatetime;
	@Column(name="gpsstatus")
	private String gpsstatus;
	@Column(name="gpsinfo")
	private String gpsinfo;
	@Column(name="gpsmode")
	private int gpsmode;
	@Column(name="mems")
	private String mems;
	@Column(name="eventid")
	private String eventid;
	@Column(name="nmeventid")
	private String nmeventid;
	@Column(name="iostatus")
	private int iostatus;
	@Column(name="distfromlastloc")
	private float distfromlastloc;
	@Column(name="fencebreach")
	private String fencebreach;
	@Column(name="address")
	private String address;
	
	@Column(name="battery")
	private int battery;
	@Column(name="rawrssi")
	private int rawrssi;
	@Column(name="rssi")
	private String rssi;
	@Column(name="motion")
	private String motion;
	@Column(name="externalsensor")
	private float extsensor;
	@Column(name="humidity")
	private float humidity;
	@Column(name="temperature")
	private float temperature;
	@Column(name="tempseverity")
	private int tempseverity;
	@Column(name="light")
	private float light;
	@Column(name="pressure")
	private float pressure;
	
	@Column(name="lastvalidlat")
	private double lastvalidlat;
	@Column(name="lastvalidlatdir")
	private String lastvalidlatdir;
	@Column(name="lastvalidlon")
	private double lastvalidlon;
	@Column(name="lastvalidlondir")
	private String lastvalidlondir;
	@Column(name="lastvaliddatetime")
	private Timestamp lastvaliddatetime;
	
	@Column(name="cellIdlat")
	private double cellidlat;
	@Column(name="cellIdlon")
	private double cellidlon;
	@Column(name="cellIdacc")
	private float cellidacc;
	@Column(name="lastvalidaddress")
	private String lastvalidaddress;
	@Column(name="lastvalidtemp")
	private float lastvalidtemp;
	
	@Transient
	private String allEventIds;
	
	@Transient
	private String powerCmds;
	

	
	
	@Column(name="heat_index")
	private float heat_index;
	
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name="gateway_id")
	//@JsonBackReference
    private Gateway gateway;	
   
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name="cmp_id")
	//@JsonBackReference
    private Company company;

    @Column(name="updatedon")
	private Timestamp updatedon;
    
    @Column(name="power_cmd")
	private String power_cmd;

	@Column(name="fota_ver")
    private String fota_ver = "NA";

	@Column(name="prev_wakeup_time")
	private long prev_wakeup_time=0;
	 
    @Column(name="aqi")
	private int aqi=0;
    
    @Column(name="voc")
	private int voc=0;
    
    @Column(name="co2")
	private int co2=0;
    
	public LastGatewayRpt(String version, Timestamp datetime, Date date,Time time, String timezone, double lat, 
			String latdir, double lon,String londir, float speed, float distance, float accuracy,String heading, 
			int idletimer, Timestamp gpsdatetime,String gpsstatus, String gpsinfo, String mems, String eventid,
			String nmeventid, int iostatus, float distfromlastloc,String fencebreach, String address, int battery,
			int rawrssi,String rssi, String motion, float extsensor, float humidity,float temperature, int tempseverity, 
			float light, float pressure, double lastvalidlat, String lastvalidlatdir, double lastvalidlon, 
			String lastvalidlondir, Timestamp lastvaliddatetime, double cellidlat, double cellidlon, float cellidacc, 
			int gpsmode, String lastvalidaddress,float lastvalidtemp, Gateway gateway,Company company,float heat_index,
			Timestamp updatedon) {
		super();
		this.version = version;
		this.datetime = datetime;
		this.date = date;
		this.time = time;
		this.timezone = timezone;
		this.lat = lat;
		this.latdir = latdir;
		this.lon = lon;
		this.londir = londir;
		this.speed = speed;
		this.distance = distance;
		this.accuracy = accuracy;
		this.heading = heading;
		this.idletimer = idletimer;
		this.gpsdatetime = gpsdatetime;
		this.gpsstatus = gpsstatus;
		this.gpsinfo = gpsinfo;
		this.mems = mems;
		this.eventid = eventid;
		this.nmeventid = nmeventid;
		this.iostatus = iostatus;
		this.distfromlastloc = distfromlastloc;
		this.fencebreach = fencebreach;
		this.address = address;
		this.battery = battery;
		this.rawrssi = rawrssi;
		this.rssi = rssi;
		this.motion = motion;
		this.extsensor = extsensor;
		this.humidity = humidity;
		this.temperature = temperature;
		this.tempseverity = tempseverity;
		this.light = light;
		this.pressure = pressure;
		this.lastvalidlat = lastvalidlat;
		this.lastvalidlatdir = lastvalidlatdir;
		this.lastvalidlon = lastvalidlon;
		this.lastvalidlondir = lastvalidlondir;
		this.lastvaliddatetime = lastvaliddatetime;
		this.cellidlat = cellidlat;
		this.cellidlon = cellidlon;
		this.cellidacc = cellidacc;
		this.gpsmode = gpsmode;
		this.lastvalidaddress = lastvalidaddress;
		this.lastvalidtemp = lastvalidtemp;
		this.gateway = gateway;
		this.company = company;
		this.heat_index=heat_index;
		this.updatedon = updatedon;
	}


	public LastGatewayRpt() {
		super();
		// TODO Auto-generated constructor stub
	}


	public long getId() {
		return id;
	}


	public String getVersion() {
		return version;
	}


	public Timestamp getDatetime() {
		return datetime;
	}
	
	

	public Date getDate() {
		return date;
	}


	public Time getTime() {
		return time;
	}


	public String getMems() {
		return mems;
	}


	public String getTimezone() {
		return timezone;
	}


	public double getLat() {
		return lat;
	}


	public String getLatdir() {
		return latdir;
	}


	public double getLon() {
		return lon;
	}


	public String getLondir() {
		return londir;
	}


	public float getSpeed() {
		return speed;
	}


	public float getDistance() {
		return distance;
	}


	public float getAccuracy() {
		return accuracy;
	}


	public String getHeading() {
		return heading;
	}


	public int getIdletimer() {
		return idletimer;
	}


	public Timestamp getGpsdatetime() {
		return gpsdatetime;
	}


	public String getGpsstatus() {
		return gpsstatus;
	}


	public String getGpsinfo() {
		return gpsinfo;
	}


	public String getEventid() {
		return eventid;
	}


	public String getNmeventid() {
		return nmeventid;
	}


	public int getIostatus() {
		return iostatus;
	}


	public float getDistfromlastloc() {
		return distfromlastloc;
	}


	public String getFencebreach() {
		return fencebreach;
	}


	public String getAddress() {
		return address;
	}


	public int getBattery() {
		return battery;
	}


	public int getRawrssi() {
		return rawrssi;
	}

	
	public String getRssi() {
		return rssi;
	}


	public String getMotion() {
		return motion;
	}


	public float getExtsensor() {
		return extsensor;
	}


	public float getHumidity() {
		return humidity;
	}


	public float getTemperature() {
		return temperature;
	}
	public int getTempseverity() {
		return tempseverity;
	}


	public float getLight() {
		return light;
	}


	public float getPressure() {
		return pressure;
	}
	
	

	public double getLastvalidlat() {
		return lastvalidlat;
	}


	public String getLastvalidlatdir() {
		return lastvalidlatdir;
	}


	public double getLastvalidlon() {
		return lastvalidlon;
	}


	public String getLastvalidlondir() {
		return lastvalidlondir;
	}


	public Timestamp getLastvaliddatetime() {
		return lastvaliddatetime;
	}


	public Gateway getGateway() {
		return gateway;
	}


	public Company getCompany() {
		return company;
	}


	public double getCellidlat() {
		return cellidlat;
	}


	public double getCellidlon() {
		return cellidlon;
	}


	public float getCellidacc() {
		return cellidacc;
	}


	public int getGpsmode() {
		return gpsmode;
	}


	public String getLastvalidaddress() {
		return lastvalidaddress;
	}

	public float getLastvalidtemp() {
		return lastvalidtemp;
	}


	public float getHeat_index() {
		return heat_index;
	}


	public void setHeat_index(float heat_index) {
		this.heat_index = heat_index;
	}


	public Timestamp getUpdatedon() {
		return updatedon;
	}


	public void setUpdatedon(Timestamp updatedon) {
		this.updatedon = updatedon;
	}


	public String getFota_ver() {
		return fota_ver;
	}


	public void setFota_ver(String fota_ver) {
		this.fota_ver = fota_ver;
	}
	
	public String getPower_cmd() {
		return power_cmd;
	}

	public void setPower_cmd(String power_cmd) {
		this.power_cmd = power_cmd;
	}


	public String getAllEventIds() {
		return allEventIds;
	}


	public void setAllEventIds(String allEventIds) {
		this.allEventIds = allEventIds;
	}


	public String getPowerCmds() {
		return powerCmds;
	}

	public void setPowerCmds(String powerCmds) {
		this.powerCmds = powerCmds;
	}
	

	public int getAqi() {
		return aqi;
	}

	public void setAqi(int aqi) {
		this.aqi = aqi;
	}

	public int getVoc() {
		return voc;
	}

	public void setVoc(int voc) {
		this.voc = voc;
	}

	public int getCo2() {
		return co2;
	}

	public void setCo2(int co2) {
		this.co2 = co2;
	}	

	public long getPrev_wakeup_time() {
		return prev_wakeup_time;
	}

	public void setPrev_wakeup_time(long prev_wakeup_time) {
		this.prev_wakeup_time = prev_wakeup_time;
	}
	
}
