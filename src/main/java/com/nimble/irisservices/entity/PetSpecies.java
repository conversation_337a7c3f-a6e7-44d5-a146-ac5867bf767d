package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="petspecies")
public class PetSpecies  implements Serializable{
	

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name="speciesname")
	private String speciesName;
	
	
	public long getId() {
		return id;
	}
	public String getSpeciesName() {
		return speciesName;
	}
	public void setSpeciesName(String speciesName) {
		this.speciesName = speciesName;
	}
	public void setId(long id) {
		this.id = id;
	}
	public PetSpecies(long id, String speciesName) {
		super();
		this.id = id;
		this.speciesName = speciesName;
	}
	public PetSpecies() {
		super();
		// TODO Auto-generated constructor stub
	}
	public PetSpecies(String speciesName) {
		super();
		this.speciesName = speciesName;
	}

	
	
}
