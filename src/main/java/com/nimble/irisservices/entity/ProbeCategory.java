package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "probecategory")
public class ProbeCategory {

	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Id
	@Column(name = "type")
	private String type;

	@Column(name = "name")
	private String name;

	@Column(name = "adcposition")
	private String adcposition;

	@Column(name = "min")
	private float min;

	@Column(name = "max")
	private float max;

	@Column(name = "model_id")
	private long model_id;

	@Column(name = "revtemp_offset")
	private float revtemp_offset;

	@Column(name = "cmp_id")
	private long cmp_id;

	@Column(name = "temp_offset")
	private float temp_offset;

	@Column(name = "batt_temp_offset")
	private float batt_temp_offset;

	@Column(name = "adclookup_enable")
	private boolean adclookup_enable;

	@Column(name = "enable_chargingformula")
	private boolean enable_chargingformula;

	public ProbeCategory() {
		super();
		// TODO Auto-generated constructor stub
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAdcposition() {
		return adcposition;
	}

	public void setAdcposition(String adcposition) {
		this.adcposition = adcposition;
	}

	public float getMin() {
		return min;
	}

	public void setMin(float min) {
		this.min = min;
	}

	public float getMax() {
		return max;
	}

	public void setMax(float max) {
		this.max = max;
	}

	public long getModel_id() {
		return model_id;
	}

	public void setModel_id(long model_id) {
		this.model_id = model_id;
	}

	public float getRevtemp_offset() {
		return revtemp_offset;
	}

	public void setRevtemp_offset(float revtemp_offset) {
		this.revtemp_offset = revtemp_offset;
	}

	public long getCmp_id() {
		return cmp_id;
	}

	public void setCmp_id(long cmp_id) {
		this.cmp_id = cmp_id;
	}

	public float getTemp_offset() {
		return temp_offset;
	}

	public void setTemp_offset(float temp_offset) {
		this.temp_offset = temp_offset;
	}

	public float getBatt_temp_offset() {
		return batt_temp_offset;
	}

	public void setBatt_temp_offset(float batt_temp_offset) {
		this.batt_temp_offset = batt_temp_offset;
	}

	public boolean isAdclookup_enable() {
		return adclookup_enable;
	}

	public void setAdclookup_enable(boolean adclookup_enable) {
		this.adclookup_enable = adclookup_enable;
	}

	public boolean isEnable_chargingformula() {
		return enable_chargingformula;
	}

	public void setEnable_chargingformula(boolean enable_chargingformula) {
		this.enable_chargingformula = enable_chargingformula;
	}

}
