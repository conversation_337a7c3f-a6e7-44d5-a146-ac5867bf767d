package com.nimble.irisservices.entity;


import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

/**
 * Livetracking generated by hbm2java
 */
@Entity
@Table(name="livetracking" , uniqueConstraints = @UniqueConstraint(columnNames={"id","gateway_id"}) 
)
public class Livetracking  implements java.io.Serializable {


	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
     private Long id;
     
	@OneToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="gateway_id")
	private Gateway gateway;
	
	@Column(name="enabledtime")
     private String enabledtime;
	
	@Column(name="disabledtime")
     private String disabledtime;
     
     @Column(name="minutes")
     private Long minutes;

    public Livetracking() {
    }

	
    public Livetracking(Gateway gateway) {
        this.gateway = gateway;
    }
    public Livetracking(Gateway gateway, String enabledtime, String disabledtime, Long minutes) {
       this.gateway = gateway;
       this.enabledtime = enabledtime;
       this.disabledtime = disabledtime;
       this.minutes = minutes;
    }
    
    public Livetracking(Gateway gateway, String enabledtime, Long minutes) {
        this.gateway = gateway;
        this.enabledtime = enabledtime;
        this.minutes = minutes;
     }
    

    public Long getId() {
        return this.id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }

    public Gateway getGateway() {
        return this.gateway;
    }
    
    public void setGateway(Gateway gateway) {
        this.gateway = gateway;
    }


    public String getEnabledtime() {
        return this.enabledtime;
    }
    
    public void setEnabledtime(String enabledtime) {
        this.enabledtime = enabledtime;
    }


    public String getDisabledtime() {
        return this.disabledtime;
    }
    
    public void setDisabledtime(String disabledtime) {
        this.disabledtime = disabledtime;
    }

    
    
    public Long getMinutes() {
        return this.minutes;
    }
    
    public void setMinutes(Long minutes) {
        this.minutes = minutes;
    }




}