
package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "reminder_repeattype")
public class ReminderRepeat implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "repeatname")
	private String repeatname;

	@Column(name = "repeat_time")
	private String repeat_time;;

	@Column(name = "Interval")
	private int Interval = 1;

	@Column(name = "enable")
	private boolean enable = true;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getRepeatname() {
		return repeatname;
	}

	public void setRepeatname(String repeatname) {
		this.repeatname = repeatname;
	}

	public String getRepeat_time() {
		return repeat_time;
	}

	public void setRepeat_time(String repeat_time) {
		this.repeat_time = repeat_time;
	}

	public int getInterval() {
		return Interval;
	}

	public void setInterval(int interval) {
		Interval = interval;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

}
