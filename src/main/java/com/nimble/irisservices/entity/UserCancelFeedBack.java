package com.nimble.irisservices.entity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "user_cancel_feedback")
public class UserCancelFeedBack {

	@Id
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	
	private long cancel_feedback_id = 0;
	
	private long user_id = 0;
	
	private String updated_on = "1753-01-01 11:11:11";
	
	private String customer_review = "NA";
	
	private boolean show_cancel_sub = false;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getCancel_feedback_id() {
		return cancel_feedback_id;
	}

	public void setCancel_feedback_id(long cancel_feedback_id) {
		this.cancel_feedback_id = cancel_feedback_id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public String getCustomer_review() {
		return customer_review;
	}

	public void setCustomer_review(String customer_review) {
		this.customer_review = customer_review;
	}

	public boolean isShow_cancel_sub() {
		return show_cancel_sub;
	}

	public void setShow_cancel_sub(boolean show_cancel_sub) {
		this.show_cancel_sub = show_cancel_sub;
	}

	public UserCancelFeedBack() {
		super();	
	}
	
	public UserCancelFeedBack(long id, long cancel_feedback_id, long user_id, String updated_on) {
		super();
		this.id = id;
		this.cancel_feedback_id = cancel_feedback_id;
		this.user_id = user_id;
		this.updated_on = updated_on;
	}
	
	
	
}
