package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity
@Table(name = "gateway", uniqueConstraints = @UniqueConstraint(columnNames = { "id" }))
public class Gateway implements Serializable {

	@Id
	@Column(name = "id")
	/*
	 * @GenericGenerator(name="gen",strategy="identity") We should not have auto
	 * generation because node creation is based on asset id .
	 * 
	 * Asset id and node id should be shame
	 * 
	 * @GeneratedValue(generator="gen")
	 */
	private long id;
	@Column(name = "name")
	private String name;
	@Column(name = "meid")
	private String meid;
	@Column(name = "mdn")
	private String mdn;
	@Column(name = "carrier")
	private String carrier;
	@Column(name = "isenable")
	private boolean enable;
	@Column(name = "isalive")
	private boolean alive;
	@Column(name = "location")
	private String location;
	@Column(name = "description")
	private String description;
	@Column(name = "sensorenable")
	private String sensorenable;
	@Column(name = "extsensortype")
	private String extsensortype;
	@Column(name = "owner")
	private String owner;
	@Column(name = "minval")
	private float minTemp;
	@Column(name = "maxval")
	private float maxTemp;
	@Column(name = "lastrptdatetime")
	private Timestamp lastrptdatetime;
	@Column(name = "stopreport")
	private boolean stopreport;
	@Column(name = "starttime")
	private String starttime;
	@Column(name = "stoptime")
	private String stoptime;

	@Column(name = "installed_date")
	private Timestamp installed_date;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "asset_id")
	// @JsonBackReference
	private Asset asset;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "assetgroup_id")
	// @JsonBackReference
	private AssetGroup assetgroup;

	/*
	 * @ManyToOne(cascade = CascadeType.ALL)
	 * 
	 * @JoinColumn(name="subgroup_id") //@JsonBackReference private SubGroup
	 * subgroup;
	 */

	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "model_id")
	// @JsonBackReference
	private AssetModel model;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "passwordtype")
	// @JsonBackReference
	private DataP passwordtype;

	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	// @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "cmp_id")
	// @JsonBackReference
	private Company company;

	@ManyToMany(mappedBy = "gateways", fetch = FetchType.EAGER)
	private Set<User> users = new HashSet<User>();

	@Column(name = "default_goal")
	private int default_goal;

	@Column(name = "calories_goal")
	private int calories_goal;

	@Column(name = "macid")
	private String macid;

	@Column(name = "show_temp_video")
	boolean show_temp_video = false;
	/*
	 * @ManyToMany(mappedBy = "gateways") //@JsonIgnore private Set<User> users =
	 * new HashSet<User>();
	 */

	@OneToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "assetinfo_id")
	// @JsonBackReference
	private AssetInformation assetinformation;

	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "groups_id")
	// @JsonBackReference
	private Groups groups;

	@Column(name = "qrcode")
	private String qrcode;

	@Column(name = "onoffstatus")
	private boolean onOffStatus;

	@Column(name = "gatewayconfig")
	private String gatewayConfig;

	@Column(name = "onsleeptime")
	private String onSleepTime;

	@Column(name = "offsleeptime")
	private String offSleepTime;

	@Column(name = "show_order_id")
	private boolean showOrderId = true;

	@Column(name = "temp_calib")
	private float temp_calib = 0;

	@Column(name = "default_temp_calib")
	private float default_temp_calib = 0;

	@Column(name = "default_battery_offset")
	private float default_battery_offset = 0;

	@Column(name = "default_charging_offset")
	private float default_charging_offset = 0;

	@Column(name = "default_fullcharge_offset")
	private float default_fullcharge_offset = 0;

	@Column(name = "pl_threshold")
	private int pl_threshold = 0;

	@Column(name = "purchased_from_others")
	private boolean purchased_from_others = true;

	@Column(name = "temp_range_offset")
	private int temp_range_offset = 3;

	@Column(name = "sensor_type_id")
	private int sensor_type_id = 0;

	@Column(name = "order_channel")
	private long order_channel = 0;

	@Column(name = "sim_vendor")
	private String sim_vendor = "NA";

	@Column(name = "dnr_interval")
	private int dnrInterval = 60;

	@Column(name = "iswithoutsub")
	private boolean iswithoutsub = false;

	@Column(name = "isnewverdevice")
	private boolean isnewverdevice = false;

	@Column(name = "is_bt_update")
	private boolean bleUpdateStatus = false;

	@Column(name = "update_passcode")
	private String updatePasscode = "0000";

	@Column(name = "warranty_skipped")
	private boolean warranty_skipped = false;

	@Column(name = "meari_update_popup")
	private boolean meari_update_popup = false;

	public Gateway() {
		super();
		// TODO Auto-generated constructor stub
	}

	public Gateway(long id, String name, String meid, String mdn, String carrier, boolean isenable, boolean isalive,
			String location, String description, String sensorenable, String owner, Asset asset, AssetGroup assetgroup,
			Groups groups, AssetModel model, Company company, Timestamp lastrptdatetime, boolean stopreport,
			String starttime, String stoptime, String qrcode, String gatewayConfig, String onSleepTime,
			String offSleepTime, int default_goal, int calories_goal, String macid, int sensor_type_id, int dnrInterval,boolean iswithoutsub,boolean isnewverdevice) {
		super();
		this.id = id;
		this.name = name;
		this.meid = meid;
		this.mdn = mdn;
		this.carrier = carrier;
		this.enable = isenable;
		this.alive = isalive;
		this.location = location;
		this.description = description;
		this.sensorenable = sensorenable;
		this.owner = owner;
		this.asset = asset;
		this.assetgroup = assetgroup;
		this.groups = groups;
		this.model = model;
		this.company = company;
		this.lastrptdatetime = lastrptdatetime;
		this.stopreport = stopreport;
		this.starttime = starttime;
		this.stoptime = stoptime;
		this.qrcode = qrcode;
		this.gatewayConfig = gatewayConfig;
		this.onSleepTime = onSleepTime;
		this.offSleepTime = offSleepTime;
		this.default_goal = default_goal;
		this.calories_goal = calories_goal;
		this.macid = macid;
		this.sensor_type_id = sensor_type_id;
		this.dnrInterval = dnrInterval;
		this.iswithoutsub = iswithoutsub;
		this.isnewverdevice = isnewverdevice;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public String getMeid() {
		return meid;
	}

	public String getMdn() {
		return mdn;
	}

	public String getCarrier() {
		return carrier;
	}

	public boolean isEnable() {
		return enable;
	}

	public boolean isAlive() {
		return alive;
	}

	public String getLocation() {
		return location;
	}

	public String getDescription() {
		return description;
	}

	public String getSensorenable() {
		return sensorenable;
	}

	public String getExtsensortype() {
		return extsensortype;
	}

	public Asset giveAsset() {
		return asset;
	}

	public AssetGroup getAssetgroup() {
		return assetgroup;
	}

	/*
	 * public SubGroup getSubgroup() { return subgroup; }
	 */

	public AssetModel getModel() {
		return model;
	}

	public Company giveCompany() {
		return company;
	}

	public String getOwner() {
		return owner;
	}

	public Set<User> getUsers() {
		return users;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public void setUsers(Set<User> users) {
		this.users = users;
	}

	public AssetInformation getAssetinformation() {
		return assetinformation;
	}

	public void setAssetinformation(AssetInformation assetinformation) {
		this.assetinformation = assetinformation;
	}

	/*
	 * public void setSubgroup(SubGroup subgroup) { this.subgroup = subgroup; }
	 */
	public Groups getGroups() {
		return groups;
	}

	public float getMinTemp() {
		return minTemp;
	}

	public void setMinTemp(float minTemp) {
		this.minTemp = minTemp;
	}

	public float getMaxTemp() {
		return maxTemp;
	}

	public void setMaxTemp(float maxTemp) {
		this.maxTemp = maxTemp;
	}

	public Timestamp getLastrptdatetime() {
		return lastrptdatetime;
	}

	public void setLastrptdatetime(Timestamp lastrptdatetime) {
		this.lastrptdatetime = lastrptdatetime;
	}

	public DataP giveDatap() {
		return passwordtype;
	}

	public void saveDatap(DataP datap) {
		this.passwordtype = datap;
	}

	public void setAlive(boolean alive) {
		this.alive = alive;
	}

	public boolean isStopreport() {
		return stopreport;
	}

	public void setStopreport(boolean stopreport) {
		this.stopreport = stopreport;
	}

	public String getStarttime() {
		return starttime;
	}

	public void setStarttime(String starttime) {
		this.starttime = starttime;
	}

	public String getStoptime() {
		return stoptime;
	}

	public void setStoptime(String stoptime) {
		this.stoptime = stoptime;
	}

	public DataP getPasswordtype() {
		return passwordtype;
	}

	public void setPasswordtype(DataP passwordtype) {
		this.passwordtype = passwordtype;
	}

	public Timestamp getInstalled_date() {
		return installed_date;
	}

	public void setInstalled_date(Timestamp installed_date) {
		this.installed_date = installed_date;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getQrcode() {
		return qrcode;
	}

	public void setQrcode(String qrcode) {
		this.qrcode = qrcode;
	}

	public boolean isOnOffStatus() {
		return onOffStatus;
	}

	public void setOnOffStatus(boolean onOffStatus) {
		this.onOffStatus = onOffStatus;
	}

	public String getGatewayConfig() {
		return gatewayConfig;
	}

	public void setGatewayConfig(String gatewayConfig) {
		this.gatewayConfig = gatewayConfig;
	}

	public String getOnSleepTime() {
		return onSleepTime;
	}

	public void setOnSleepTime(String onSleepTime) {
		this.onSleepTime = onSleepTime;
	}

	public String getOffSleepTime() {
		return offSleepTime;
	}

	public void setOffSleepTime(String offSleepTime) {
		this.offSleepTime = offSleepTime;
	}

	public int getDefault_goal() {
		return default_goal;
	}

	public void setDefault_goal(int default_goal) {
		this.default_goal = default_goal;
	}

	public int getCalories_goal() {
		return calories_goal;
	}

	public void setCalories_goal(int calories_goal) {
		this.calories_goal = calories_goal;
	}

	public String getMacid() {
		return macid;
	}

	public boolean isShowOrderId() {
		return showOrderId;
	}

	public void setShowOrderId(boolean showOrderId) {
		this.showOrderId = showOrderId;
	}

	public void setMacid(String macid) {
		this.macid = macid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public float getTemp_calib() {
		return temp_calib;
	}

	public void setTemp_calib(float temp_calib) {
		this.temp_calib = temp_calib;
	}

	public float getDefault_temp_calib() {
		return default_temp_calib;
	}

	public void setDefault_temp_calib(float default_temp_calib) {
		this.default_temp_calib = default_temp_calib;
	}

	public float getDefault_battery_offset() {
		return default_battery_offset;
	}

	public void setDefault_battery_offset(float default_battery_offset) {
		this.default_battery_offset = default_battery_offset;
	}

	public float getDefault_charging_offset() {
		return default_charging_offset;
	}

	public void setDefault_charging_offset(float default_charging_offset) {
		this.default_charging_offset = default_charging_offset;
	}

	public float getDefault_fullcharge_offset() {
		return default_fullcharge_offset;
	}

	public void setDefault_fullcharge_offset(float default_fullcharge_offset) {
		this.default_fullcharge_offset = default_fullcharge_offset;
	}

	public int getPl_threshold() {
		return pl_threshold;
	}

	public void setPl_threshold(int pl_threshold) {
		this.pl_threshold = pl_threshold;
	}

	public boolean isPurchased_from_others() {
		return purchased_from_others;
	}

	public void setPurchased_from_others(boolean purchased_from_others) {
		this.purchased_from_others = purchased_from_others;
	}

	public boolean isShow_temp_video() {
		return show_temp_video;
	}

	public void setShow_temp_video(boolean show_temp_video) {
		this.show_temp_video = show_temp_video;
	}

	public int getTemp_range_offset() {
		return temp_range_offset;
	}

	public void setTemp_range_offset(int temp_range_offset) {
		this.temp_range_offset = temp_range_offset;
	}

	public int getSensor_type_id() {
		return sensor_type_id;
	}

	public void setSensor_type_id(int sensor_type_id) {
		this.sensor_type_id = sensor_type_id;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public long getOrder_channel() {
		return order_channel;
	}

	public void setOrder_channel(long order_channel) {
		this.order_channel = order_channel;
	}

	public String getSim_vendor() {
		return sim_vendor;
	}

	public void setSim_vendor(String sim_vendor) {
		this.sim_vendor = sim_vendor;
	}

	public int getDnrInterval() {
		return dnrInterval;
	}

	public void setDnrInterval(int dnrInterval) {
		this.dnrInterval = dnrInterval;
	}

	public boolean isIswithoutsub() {
		return iswithoutsub;
	}

	public void setIswithoutsub(boolean iswithoutsub) {
		this.iswithoutsub = iswithoutsub;
	}

	public boolean isIsnewverdevice() {
		return isnewverdevice;
	}

	public void setIsnewverdevice(boolean isnewverdevice) {
		this.isnewverdevice = isnewverdevice;
	}

	public boolean isBleUpdateStatus() {
		return bleUpdateStatus;
	}

	public void setBleUpdateStatus(boolean bleUpdateStatus) {
		this.bleUpdateStatus = bleUpdateStatus;
	}

	public String getUpdatePasscode() {
		return updatePasscode;
	}

	public void setUpdatePasscode(String updatePasscode) {
		this.updatePasscode = updatePasscode;
	}

	public boolean isWarranty_skipped() {
		return warranty_skipped;
	}

	public void setWarranty_skipped(boolean warranty_skipped) {
		this.warranty_skipped = warranty_skipped;
	}

	public boolean isMeari_update_popup() {
		return meari_update_popup;
	}

	public void setMeari_update_popup(boolean meari_update_popup) {
		this.meari_update_popup = meari_update_popup;
	}
}
