package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="pet_food")
public class PetFood {

	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	private String name = "NA";
	
	private double calories = 0;
	
	private boolean custom = false;
	
	private long user_id = 0;
	
	private boolean enable = true;
	
	private String updated_on = "1753-01-01 11:11:11";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public double getCalories() {
		return calories;
	}

	public void setCalories(double calories) {
		this.calories = calories;
	}

	public boolean isCustom() {
		return custom;
	}

	public void setCustom(boolean custom) {
		this.custom = custom;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public PetFood() {
		super();
	}
	
	public PetFood(long id, String name, double calories, boolean custom, long user_id, boolean enable,
			String updated_on) {
		super();
		this.id = id;
		this.name = name;
		this.calories = calories;
		this.custom = custom;
		this.user_id = user_id;
		this.enable = enable;
		this.updated_on = updated_on;
	}
	
	
}
