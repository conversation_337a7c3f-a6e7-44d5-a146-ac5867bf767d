package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

import com.nimble.irisservices.constant.IrisservicesConstants;

@Entity 
@Table(name="cancel_customer_retain")
public class CancelCustomerRetain {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id = 0;
	
	private long user_id = 0;
	
	private String updated_on = IrisservicesConstants.DEFAULT_DATE;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public CancelCustomerRetain() {
		super();	
	}
	
	public CancelCustomerRetain(long id, long user_id, String updated_on) {
		super();
		this.id = id;
		this.user_id = user_id;
		this.updated_on = updated_on;
	}
	
	
	
}
