package com.nimble.irisservices.entity;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;
@Entity
@Table(name="plan_to_upgrade", uniqueConstraints =@UniqueConstraint (columnNames={"plan_to_period_id","upgradeplan_id" }) )

public class PlanToUpgrade {
private static final long serialVersionUID = 0l;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="plan_to_period_id")
    private PlanToPeriod plan_to_period_id;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="upgradeplan_id")
    private PlanToPeriod upgradeplan_id;
	
	@Column(name="price")
	private Float price =0.0f;
	
	@Column(name = "description")
	private String description;
		
	public PlanToUpgrade() {
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public PlanToPeriod getPlan_to_period_id() {
		return plan_to_period_id;
	}

	public void setPlan_to_period_id(PlanToPeriod plan_to_period_id) {
		this.plan_to_period_id = plan_to_period_id;
	}

	public PlanToPeriod getUpgradeplan_id() {
		return upgradeplan_id;
	}

	public void setUpgradeplan_id(PlanToPeriod upgradeplan_id) {
		this.upgradeplan_id = upgradeplan_id;
	}

	public float getPrice() {
		return price;
	}

	public void setPrice(float price) {
		this.price = price;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}
	

}
