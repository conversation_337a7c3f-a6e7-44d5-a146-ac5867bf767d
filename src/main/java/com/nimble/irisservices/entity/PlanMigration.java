package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "plan_migration")
public class PlanMigration {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	private long old_plan_id = 0;
	
	private long old_period_id = 0;
	
	private long migration_plan_id = 0;
	
	private long migration_period_id = 0;
	
	private String coupon_code = "NA"; 

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getOld_plan_id() {
		return old_plan_id;
	}

	public void setOld_plan_id(long old_plan_id) {
		this.old_plan_id = old_plan_id;
	}

	public long getOld_period_id() {
		return old_period_id;
	}

	public void setOld_period_id(long old_period_id) {
		this.old_period_id = old_period_id;
	}

	public long getMigration_plan_id() {
		return migration_plan_id;
	}

	public void setMigration_plan_id(long migration_plan_id) {
		this.migration_plan_id = migration_plan_id;
	}

	public long getMigration_period_id() {
		return migration_period_id;
	}

	public void setMigration_period_id(long migration_period_id) {
		this.migration_period_id = migration_period_id;
	}

	public String getCoupon_code() {
		return coupon_code;
	}

	public void setCoupon_code(String coupon_code) {
		this.coupon_code = coupon_code;
	}

	public PlanMigration() {
		super();
	}

	public PlanMigration(long id, long old_plan_id, long old_period_id, long migration_plan_id,
			long migration_period_id, String coupon_code) {
		super();
		this.id = id;
		this.old_plan_id = old_plan_id;
		this.old_period_id = old_period_id;
		this.migration_plan_id = migration_plan_id;
		this.migration_period_id = migration_period_id;
		this.coupon_code = coupon_code;
	}

	
	
}
