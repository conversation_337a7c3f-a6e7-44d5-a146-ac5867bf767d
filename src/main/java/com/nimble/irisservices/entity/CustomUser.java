package com.nimble.irisservices.entity;

import org.springframework.security.core.userdetails.User;

public class CustomUser extends User {

	private String id;

	public CustomUser(UserEntity userEntity) {
		super(userEntity.getUserName(), userEntity.getPassword(), userEntity.getGrantedAuthoritiesList());
		this.id = userEntity.getId();
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

}
