package com.nimble.irisservices.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "cancel_feedback_content_list")
public class CancelFeedbackContentList {

    @Id
    @GenericGenerator(name = "gen", strategy = "identity")
    @GeneratedValue(generator = "gen")
    private long id;

    private long cancel_feedback_id = 0;

    private String title = "NA";

    private String desc = "NA";

    private String button_name = "NA";

    private String img_url = "NA";

    private boolean enable = true;

    private long btn_redirect = 0;

    private boolean is_flexi = false;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getCancel_feedback_id() {
        return cancel_feedback_id;
    }

    public void setCancel_feedback_id(long cancel_feedback_id) {
        this.cancel_feedback_id = cancel_feedback_id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getButton_name() {
        return button_name;
    }

    public void setButton_name(String button_name) {
        this.button_name = button_name;
    }

    public String getImg_url() {
        return img_url;
    }

    public void setImg_url(String img_url) {
        this.img_url = img_url;
    }

    public CancelFeedbackContentList() {
        super();
    }

    public CancelFeedbackContentList(long id, long cancel_feedback_id, String title, String desc,
                                 String button_name,String img_url,boolean enable,long btn_redirect,boolean is_flexi) {
        super();
        this.id = id;
        this.cancel_feedback_id = cancel_feedback_id;
        this.title = title;
        this.desc = desc;
        this.button_name = button_name;
        this.img_url = img_url;
        this.enable = enable;
        this.btn_redirect = btn_redirect;
        this.is_flexi = is_flexi;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public long getBtn_redirect() {
        return btn_redirect;
    }

    public void setBtn_redirect(long btn_redirect) {
        this.btn_redirect = btn_redirect;
    }

    public boolean isIs_flexi() {
        return is_flexi;
    }

    public void setIs_flexi(boolean is_flexi) {
        this.is_flexi = is_flexi;
    }
}
