package com.nimble.irisservices.entity;

import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "vetchat_plan_user")
public class VetChatPlanUser {

	@EmbeddedId
	private VetChatPlanUserId vetChatPlanUser;

	public VetChatPlanUserId getVetChatPlanUser() {
		return vetChatPlanUser;
	}

	public void setVetChatPlanUser(VetChatPlanUserId vetChatPlanUser) {
		this.vetChatPlanUser = vetChatPlanUser;
	}
}
