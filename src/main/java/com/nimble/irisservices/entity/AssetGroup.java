package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="assetgroup", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class AssetGroup  implements Serializable {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	@Column(name="assetgroupname")
	private String name;
	
    @ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="cmp_id")
	//@JsonBackReference
    private Company company;

	public long getId() {
		return id;
	}

	public String getName() {
		return name;
	}

	public Company giveCompany() {
		return company;
	}
	
	public void setId(long id) {
		this.id = id;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

    

}
