package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name="user_feedback_transaction", uniqueConstraints = @UniqueConstraint(columnNames = { "userid","formid" }))

public class UserFeedbackTransaction implements Serializable {
	@Id
	@Column(name = "id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@Column(name = "userid")
	private long userid;

	@Column(name = "formid")
	private long formid;

	@Column(name = "createdon")
	private String createdon;

	@Column(name = "updatedon")
	private String updatedon;

	@Column(name = "retry_count")
	private int retry_count=3;

	@Column(name="show_form")
	private boolean show_form = true;
	
	@Column(name="close_form")
	private boolean close_form = true;
	
	@Column(name="comments")
	private String comments;
	
	@Column(name="reviewtype")
	private String reviewtype;
	
	@Column(name="skip")
	private boolean skip = true;
	
	@Column(name = "monitor_type")
	private long monitor_type;


	public UserFeedbackTransaction()
	{
		
	}
	
	public UserFeedbackTransaction(long userid,long formid,String createdon,String updatedon,boolean show,boolean close,String reviewtype, String comments, boolean skip, long monitor_type)
	{
		this.userid = userid;
		this.formid = formid;
		this.createdon = createdon;
		this.updatedon = updatedon;
		this.show_form = show;
		this.close_form = close;
		this.comments = comments;
		this.reviewtype = reviewtype;
		this.skip = skip;
		this.monitor_type = monitor_type;
		
	}
	
	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}
	
	public String getReviewtype() {
		return reviewtype;
	}

	public void setReviewtype(String reviewtype) {
		this.reviewtype = reviewtype;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUserid() {
		return userid;
	}

	public void setUserid(long userid) {
		this.userid = userid;
	}

	public long getFormid() {
		return formid;
	}

	public void setFormid(long formid) {
		this.formid = formid;
	}

	public String getCreatedon() {
		return createdon;
	}

	public void setCreatedon(String createdon) {
		this.createdon = createdon;
	}

	public String getUpdatedon() {
		return updatedon;
	}

	public void setUpdatedon(String updatedon) {
		this.updatedon = updatedon;
	}

	public int getRetry_count() {
		return retry_count;
	}

	public void setRetry_count(int retry_count) {
		this.retry_count = retry_count;
	}


	public boolean isShow() {
		return show_form;
	}

	public void setShow(boolean show) {
		this.show_form = show;
	}

	public boolean isClose_form() {
		return close_form;
	}

	public void setClose_form(boolean close_form) {
		this.close_form = close_form;
	}

	public boolean isSkip() {
		return skip;
	}

	public void setSkip(boolean skip) {
		this.skip = skip;
	}

	public boolean isShow_form() {
		return show_form;
	}

	public void setShow_form(boolean show_form) {
		this.show_form = show_form;
	}

	public long getMonitor_type() {
		return monitor_type;
	}

	public void setMonitor_type(long monitor_type) {
		this.monitor_type = monitor_type;
	}	
}
