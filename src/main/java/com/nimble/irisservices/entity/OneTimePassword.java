package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "one_time_password")
public class OneTimePassword {

	@Id
	@Column(name = "userid")
	private long userId = 0;

	@Column(name = "otp")
	private int otp = 0;

	@Column(name = "created_on")
	private String createdOn = "1753-01-01 00:00:00";

	@Column(name = "updated_on")
	private String updatedOn = "1753-01-01 00:00:00";

	@Column(name = "status")
	private boolean status = false;

	@Column(name = "request_from")
	private String request_from = "forget_password";

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public long getOtp() {
		return otp;
	}

	public void setOtp(int otp) {
		this.otp = otp;
	}

	public String getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}

	public String getUpdatedOn() {
		return updatedOn;
	}

	public void setUpdatedOn(String updatedOn) {
		this.updatedOn = updatedOn;
	}

	public boolean isStatus() {
		return status;
	}

	public void setStatus(boolean status) {
		this.status = status;
	}

	public OneTimePassword() {
		super();
	}

	public String getRequest_from() {
		return request_from;
	}

	public void setRequest_from(String request_from) {
		this.request_from = request_from;
	}

}
