package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

import org.hibernate.annotations.GenericGenerator;

/*@Entity 
@Table(name="vgroup", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))*/
public class Group  implements Serializable{
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name="groupname")
	private String name;
	
	 @ManyToOne(cascade = CascadeType.ALL)
	 @JoinColumn(name="cmp_id")
	 private Company company;


	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Company giveCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	} 

}
