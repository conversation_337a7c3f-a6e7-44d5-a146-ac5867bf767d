package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="messagetype", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class MessageType {
	
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name="name")
	private String name;
	@Column(name="format")
	private String format;
	@Column(name="unit")
	private String unit;
	@Column(name="question")
	private String question;
	

	public long getId() {
		return id;
	}

	public String getName() {
		return name;
	}

	public String getFormat() {
		return format;
	}

	public String getUnit() {
		return unit;
	}

	public String getQuestion() {
		return question;
	}
	
	
	
	
}
