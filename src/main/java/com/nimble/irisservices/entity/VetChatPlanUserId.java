package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.util.Objects;

import javax.persistence.Embeddable;

@Embeddable
public class VetChatPlanUserId implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private long user_id;
	private int plan_id;
	private String expiry_time = "1753-01-01 00:00:00";
	private long pet_profile_id;
	private String chat_initiated_at = "1753-01-01 00:00:00";

	public VetChatPlanUserId() {
		super();
	}

	public VetChatPlanUserId(long user_id, int plan_id, String expiry_time, long pet_profile_id) {
		this.user_id = user_id;
		this.plan_id = plan_id;
		this.expiry_time = expiry_time;
		this.pet_profile_id = pet_profile_id;
	}

	// Override equals and hashCode
	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		VetChatPlanUserId that = (VetChatPlanUserId) o;
		return Objects.equals(user_id, that.user_id) && Objects.equals(plan_id, that.plan_id);
	}

	// Getters and setters

	@Override
	public int hashCode() {
		return Objects.hash(user_id, plan_id);
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public int getPlan_id() {
		return plan_id;
	}

	public void setPlan_id(int plan_id) {
		this.plan_id = plan_id;
	}

	public String getExpiry_time() {
		return expiry_time;
	}

	public void setExpiry_time(String expiry_time) {
		this.expiry_time = expiry_time;
	}

	public long getPet_profile_id() {
		return pet_profile_id;
	}

	public void setPet_profile_id(long pet_profile_id) {
		this.pet_profile_id = pet_profile_id;
	}

	public String getChat_initiated_at() {
		return chat_initiated_at;
	}

	public void setChat_initiated_at(String chat_initiated_at) {
		this.chat_initiated_at = chat_initiated_at;
	}
}
