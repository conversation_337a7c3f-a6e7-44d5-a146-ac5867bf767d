package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "apidetails",uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class Apidetails implements Serializable {

	@Id 
    @GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;

	@Column(name = "service_type")
	
	private String serviceType;

	@Column(name = "api_type")
	
	private String apiType;

	@Column(name = "description")
	private String description;

	public Apidetails() {
		super();
		// TODO Auto-generated constructor stub
	}

	public Apidetails(long id, String serviceType, String apiType) {
		this.id = id;
		this.serviceType = serviceType;
		this.apiType = apiType;
	}

	public Apidetails(long id, String serviceType, String apiType, String description) {
		this.id = id;
		this.serviceType = serviceType;
		this.apiType = apiType;
		this.description = description;
	}
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getServiceType() {
		return serviceType == null ? null : serviceType.replaceAll("\\s","").toLowerCase();
	}

	public void setServiceType(String serviceType) {
		this.serviceType = serviceType == null ? null : serviceType.replaceAll("\\s","").toLowerCase();
		
	}

	public String getApi() {
		return apiType == null ? null : apiType.replaceAll("\\s","").toLowerCase();
	}

	public void setApi(String apiType) {
		this.apiType = apiType == null ? null : apiType.replaceAll("\\s","").toLowerCase();
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
