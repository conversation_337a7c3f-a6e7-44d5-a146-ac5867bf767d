package com.nimble.irisservices.entity;

import org.hibernate.annotations.GeneratorType;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Entity
@Table(name = "order_sku_details")
public class OrderSkuDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    private String sku;
    private String sales_channel;
    private String product_type;
    private boolean is_bundle;
    private boolean is_combo;

    public OrderSkuDetails(){

    }

    public OrderSkuDetails(String sku, String sales_channel, String product_type, boolean is_bundle, boolean is_combo) {
        this.sku = sku;
        this.sales_channel = sales_channel;
        this.product_type = product_type;
        this.is_bundle = is_bundle;
        this.is_combo = is_combo;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSales_channel() {
        return sales_channel;
    }

    public void setSales_channel(String sales_channel) {
        this.sales_channel = sales_channel;
    }

    public String getProduct_type() {
        return product_type;
    }

    public void setProduct_type(String product_type) {
        this.product_type = product_type;
    }

    public boolean isIs_bundle() {
        return is_bundle;
    }

    public void setIs_bundle(boolean is_bundle) {
        this.is_bundle = is_bundle;
    }

    public boolean isIs_combo() {
        return is_combo;
    }

    public void setIs_combo(boolean is_combo) {
        this.is_combo = is_combo;
    }
}
