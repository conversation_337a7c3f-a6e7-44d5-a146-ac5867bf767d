package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

import com.fasterxml.jackson.annotation.JsonIgnore;

@Entity 
@Table(name="asset", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class Asset  implements Serializable{
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	@Column(name="assettype")
	private String assettype;
	@Column(name="assetaddr")
	private String assetaddress;
	
	@JsonIgnore
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name="model_id")
    private AssetModel model;
		
	public Asset() {
		super();
	}
	public Asset(String assettype, String assetaddress,AssetModel model) {
		super();
		this.assettype = assettype;
		this.assetaddress = assetaddress;
		this.model = model;
	}
	public long getId() {
		return id;
	}
	
	public void setId(long id) {
		this.id = id;
	}
	
	public String getAssettype() {
		return assettype;
	}
	
	public void setAssettype(String assettype) {
		this.assettype = assettype;
	}
	
	public String getAssetaddress() {
		return assetaddress;
	}
	
	public void setAssetaddress(String assetaddress) {
		this.assetaddress = assetaddress;
	}
	
	public void setModel(AssetModel model) {
		this.model = model;
	}
	public AssetModel getModel() {
		return model;
	}
	
}
