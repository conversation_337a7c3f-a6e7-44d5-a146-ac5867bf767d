package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="user_retained")
public class UserRetained {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;      
	
	private long user_id = 0;
	
	private String coupon_id = "NA";
	
	private String next_renewal_date = "1753-01-01 11:11:11";
	
	private long plan_to_period_id = 0;
	
	private boolean activated = false;
	
	private String updated_on = "1753-01-01 11:11:11";
	
	private int applied_count = 0;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getCoupon_id() {
		return coupon_id;
	}

	public void setCoupon_id(String coupon_id) {
		this.coupon_id = coupon_id;
	}

	public String getNext_renewal_date() {
		return next_renewal_date;
	}

	public void setNext_renewal_date(String next_renewal_date) {
		this.next_renewal_date = next_renewal_date;
	}

	public long getPlan_to_period_id() {
		return plan_to_period_id;
	}

	public void setPlan_to_period_id(long plan_to_period_id) {
		this.plan_to_period_id = plan_to_period_id;
	}

	public boolean isActivated() {
		return activated;
	}

	public void setActivated(boolean activated) {
		this.activated = activated;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public int getApplied_count() {
		return applied_count;
	}

	public void setApplied_count(int applied_count) {
		this.applied_count = applied_count;
	}

	public UserRetained() {
		super();
	}

	public UserRetained( long user_id, String coupon_id, String next_renewal_date, long plan_to_period_id,
			boolean activated, String updated_on, int applied_count) {
		super();
		this.user_id = user_id;
		this.coupon_id = coupon_id;
		this.next_renewal_date = next_renewal_date;
		this.plan_to_period_id = plan_to_period_id;
		this.activated = activated;
		this.updated_on = updated_on;
		this.applied_count = applied_count;
	}
	
	 
	
	
}
