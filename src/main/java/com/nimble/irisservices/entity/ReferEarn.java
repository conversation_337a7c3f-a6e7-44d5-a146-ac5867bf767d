package com.nimble.irisservices.entity;
// Generated Jan 10, 2019 6:07:46 PM by Hibernate Tools 4.3.1

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

/**
 * JAdclookup generated by hbm2java
 */
@Entity
@Table(name = "refer_earn")
public class ReferEarn implements java.io.Serializable {
	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private Long id;

	@Column(name = "offerone")
	private String offerOne;

	@Column(name = "messagecontent")
	private String messageContent;

	@Column(name = "referallink")
	private String referalLink;

	@Column(name = "offertwo")
	private String offerTwo;

	@Column(name = "createdon")
	private String createdOn;

	public ReferEarn() {
		super();
		// TODO Auto-generated constructor stub
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMessageContent() {
		return messageContent;
	}

	public void setMessageContent(String messageContent) {
		this.messageContent = messageContent;
	}

	public String getReferalLink() {
		return referalLink;
	}

	public void setReferalLink(String referalLink) {
		this.referalLink = referalLink;
	}

	public String getCreatedOn() {
		return createdOn = createdOn == null ? null : createdOn.substring(0, 19);
	}

	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}

	public String getOfferOne() {
		return offerOne;
	}

	public void setOfferOne(String offerOne) {
		this.offerOne = offerOne;
	}

	public String getOfferTwo() {
		return offerTwo;
	}

	public void setOfferTwo(String offerTwo) {
		this.offerTwo = offerTwo;
	}

	public ReferEarn(String offerOne, String messageContent, String referalLink, String offerTwo, String createdOn) {
		super();
		this.offerOne = offerOne;
		this.messageContent = messageContent;
		this.referalLink = referalLink;
		this.offerTwo = offerTwo;
		this.createdOn = createdOn;
	}

	public ReferEarn(Long id, String offerOne, String messageContent, String referalLink, String offerTwo,
			String createdOn) {
		super();
		this.id = id;
		this.offerOne = offerOne;
		this.messageContent = messageContent;
		this.referalLink = referalLink;
		this.offerTwo = offerTwo;
		this.createdOn = createdOn;
	}



}
