package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

import com.nimble.irisservices.constant.IrisservicesConstants;



@Entity
@Table(name = "gateway_pending_events")
public class GatewayPendingEvent {

	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id = 0;
	
	private long gateway_id = 0;
	
	private boolean scheduled_toss = false;
	
	private boolean custom_toss_audio = false;
	
	private String updated_on = IrisservicesConstants.DEFAULT_DATE;
	
	private boolean motion_alert = false;
	
	private boolean sound_alert = false;
	
	private boolean temp_alert = false;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public boolean isScheduled_toss() {
		return scheduled_toss;
	}

	public void setScheduled_toss(boolean scheduled_toss) {
		this.scheduled_toss = scheduled_toss;
	}

	public boolean isCustom_toss_audio() {
		return custom_toss_audio;
	}

	public void setCustom_toss_audio(boolean custom_toss_audio) {
		this.custom_toss_audio = custom_toss_audio;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public boolean isMotion_alert() {
		return motion_alert;
	}

	public void setMotion_alert(boolean motion_alert) {
		this.motion_alert = motion_alert;
	}

	public boolean isSound_alert() {
		return sound_alert;
	}

	public void setSound_alert(boolean sound_alert) {
		this.sound_alert = sound_alert;
	}

	public boolean isTemp_alert() {
		return temp_alert;
	}

	public void setTemp_alert(boolean temp_alert) {
		this.temp_alert = temp_alert;
	}

	public GatewayPendingEvent() {
		super();
	}
	
	public GatewayPendingEvent(long id, long gateway_id, boolean scheduled_toss, boolean custom_toss_audio,
			String updated_on) {
		super();
		this.id = id;
		this.gateway_id = gateway_id;
		this.scheduled_toss = scheduled_toss;
		this.custom_toss_audio = custom_toss_audio;
		this.updated_on = updated_on;
	}
	
	
}
