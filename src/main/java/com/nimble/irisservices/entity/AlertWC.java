package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.GenericGenerator;

import com.nimble.irisservices.constant.IrisservicesConstants;

@Entity 
@Table(name="alert_wc")
public class AlertWC {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id = 0;

	private long gateway_id = 0;
	
	private long alert_cfg_id = 0;
	
	private long alert_type_id = 0;
	
	private String message = "NA";
	
	private boolean sms = false;
	
	private boolean email = false;
	
	private boolean push_notification = false;
	
	private String created_on = IrisservicesConstants.DEFAULT_DATE;
	
	private boolean sent = false;
	
	private String short_disc = "NA";
	
	private String alert_value = "NA";
	
	private String filename = "NA";
	
	@Transient
	private String gateway_name = "NA";
	
	@Transient
	private String pretty_time = "NA";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public long getAlert_cfg_id() {
		return alert_cfg_id;
	}

	public void setAlert_cfg_id(long alert_cfg_id) {
		this.alert_cfg_id = alert_cfg_id;
	}

	public long getAlert_type_id() {
		return alert_type_id;
	}

	public void setAlert_type_id(long alert_type_id) {
		this.alert_type_id = alert_type_id;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public boolean isSms() {
		return sms;
	}

	public void setSms(boolean sms) {
		this.sms = sms;
	}

	public boolean isEmail() {
		return email;
	}

	public void setEmail(boolean email) {
		this.email = email;
	}

	public boolean isPush_notification() {
		return push_notification;
	}

	public void setPush_notification(boolean push_notification) {
		this.push_notification = push_notification;
	}

	public String getCreated_on() {
		return created_on;
	}

	public void setCreated_on(String created_on) {
		this.created_on = created_on;
	}

	public boolean isSent() {
		return sent;
	}

	public void setSent(boolean sent) {
		this.sent = sent;
	}

	public String getShort_disc() {
		return short_disc;
	}

	public void setShort_disc(String short_disc) {
		this.short_disc = short_disc;
	}

	public String getAlert_value() {
		return alert_value;
	}
	
	public void setAlert_value(String alert_value) {
		this.alert_value = alert_value;
	}

	public String getFilename() {
		return filename;
	}

	public void setFilename(String filename) {
		this.filename = filename;
	}

	public String getGateway_name() {
		return gateway_name;
	}

	public void setGateway_name(String gateway_name) {
		this.gateway_name = gateway_name;
	}

	public String getPretty_time() {
		return pretty_time;
	}

	public void setPretty_time(String pretty_time) {
		this.pretty_time = pretty_time;
	}

	public AlertWC() {
		super();
	}
	
	public AlertWC(long id, long gateway_id, long alert_cfg_id, long alert_type_id, String message, boolean sms,
			boolean email, boolean push_notification, String created_on) {
		super();
		this.id = id;
		this.gateway_id = gateway_id;
		this.alert_cfg_id = alert_cfg_id;
		this.alert_type_id = alert_type_id;
		this.message = message;
		this.sms = sms;
		this.email = email;
		this.push_notification = push_notification;
		this.created_on = created_on;
	}
	
	
	
}
