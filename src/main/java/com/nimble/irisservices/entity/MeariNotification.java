package com.nimble.irisservices.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import java.io.Serializable;

@Entity
@Table(name = "meari_notification_history", uniqueConstraints = @UniqueConstraint(columnNames = {"user_id", "create_date"}))
public class MeariNotification implements Serializable {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = "gen", strategy = "identity")
    @GeneratedValue(generator = "gen")
    private long id;

    @Column(name = "user_id")
    private long userID = 0;

    @Column(name = "msg_id")
    private long msgID = 0;

    @Column(name = "msg_type_id")
    private int msgTypeID = 0;

    @Column(name = "device_id")
    private long deviceID = 0;

    @Column(name = "device_name")
    private String deviceName = "NA";

    @Column(name = "image_alert_type")
    private int imageAlertType = 0;

    @Column(name = "event_type")
    private int eventType = 0;

    @Column(name = "create_date")
    private String createDate = "1753-01-01 00:00:00";

    @Column(name = "decibel")
    private String decibel = "NA";

    @Column(name = "device_unique_id")
    private String deviceUniqueId = "NA";

    @Column(name = "gateway_id")
    private String gateWayId = "";

    public MeariNotification() {
    }

    public MeariNotification(int id, long userID, long msgID, int msgTypeID, long deviceID, String deviceName,
                             int imageAlertType, int eventType, String createDate, String decibel,
                             String deviceUniqueId, String gateWayId) {
        this.id = id;
        this.userID = userID;
        this.msgID = msgID;
        this.msgTypeID = msgTypeID;
        this.deviceID = deviceID;
        this.deviceName = deviceName;
        this.imageAlertType = imageAlertType;
        this.eventType = eventType;
        this.createDate = createDate;
        this.decibel = decibel;
        this.deviceUniqueId = deviceUniqueId;
        this.gateWayId = gateWayId;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getUserID() {
        return userID;
    }

    public void setUserID(long userID) {
        this.userID = userID;
    }

    public long getMsgID() {
        return msgID;
    }

    public void setMsgID(long msgID) {
        this.msgID = msgID;
    }

    public int getMsgTypeID() {
        return msgTypeID;
    }

    public void setMsgTypeID(int msgTypeID) {
        this.msgTypeID = msgTypeID;
    }

    public long getDeviceID() {
        return deviceID;
    }

    public void setDeviceID(long deviceID) {
        this.deviceID = deviceID;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public int getImageAlertType() {
        return imageAlertType;
    }

    public void setImageAlertType(int imageAlertType) {
        this.imageAlertType = imageAlertType;
    }

    public int getEventType() {
        return eventType;
    }

    public void setEventType(int eventType) {
        this.eventType = eventType;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getDecibel() {
        return decibel;
    }

    public void setDecibel(String decibel) {
        this.decibel = decibel;
    }

    public String getDeviceUniqueId() {
        return deviceUniqueId;
    }

    public void setDeviceUniqueId(String deviceUniqueId) {
        this.deviceUniqueId = deviceUniqueId;
    }

    public String getGateWayId() {
        return gateWayId;
    }

    public void setGateWayId(String gateWayId) {
        this.gateWayId = gateWayId;
    }
}
