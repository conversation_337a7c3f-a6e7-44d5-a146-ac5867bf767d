package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "user_metadata")
public class UserMetaData implements Serializable {

	@Id
	@Column(name = "user_id")
	private long user_id;

	@Column(name = "vpm_id")
	private String vpm_id = "NA";

	@Column(name = "show_marketing_notif")
	private boolean show_marketing_notif = true;

	@Column(name = "app_ver")
	private String app_ver = "NA";
	
	@Column(name="app_type")
	private String app_type = "NA";

	@Column(name = "created_on")
	private String created_on = "1753-01-01 11:11:11";

	@Column(name = "updated_on")
	private String updated_on = "1753-01-01 11:11:11";

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getVpm_id() {
		return vpm_id;
	}

	public void setVpm_id(String vpm_id) {
		this.vpm_id = vpm_id;
	}

	public boolean isShow_marketing_notif() {
		return show_marketing_notif;
	}

	public void setShow_marketing_notif(boolean show_marketing_notif) {
		this.show_marketing_notif = show_marketing_notif;
	}

	public String getApp_ver() {
		return app_ver;
	}

	public void setApp_ver(String app_ver) {
		this.app_ver = app_ver;
	}
	
	public String getApp_type() {
		return app_type;
	}

	public void setApp_type(String app_type) {
		this.app_type = app_type;
	}

	public String getCreated_on() {
		return created_on;
	}

	public void setCreated_on(String created_on) {
		this.created_on = created_on;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

}
