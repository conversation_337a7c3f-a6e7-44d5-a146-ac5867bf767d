package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "email_verify_otp")
public class EmailOtp {

	@Id
	@Column(name = "email")
	private String email;

	@Column(name = "request_from")
	private String request_from = "NA";

	@Column(name = "otp")
	private int otp = 0;

	@Column(name = "created_on")
	private String created_on = "1753-01-01 00:00:00";

	@Column(name = "updated_on")
	private String updatedOn = "1753-01-01 00:00:00";

	@Column(name = "status")
	private boolean status = false;

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getRequest_from() {
		return request_from;
	}

	public void setRequest_from(String request_from) {
		this.request_from = request_from;
	}

	public int getOtp() {
		return otp;
	}

	public void setOtp(int otp) {
		this.otp = otp;
	}

	public String getCreated_on() {
		return created_on;
	}

	public void setCreated_on(String created_on) {
		this.created_on = created_on;
	}

	public String getUpdatedOn() {
		return updatedOn;
	}

	public void setUpdatedOn(String updatedOn) {
		this.updatedOn = updatedOn;
	}

	public boolean isStatus() {
		return status;
	}

	public void setStatus(boolean status) {
		this.status = status;
	}

}
