package com.nimble.irisservices.entity;

import javax.persistence.*;
import java.util.Calendar;
import java.util.TimeZone;

@Entity
@Table(name = "user_otp_verification")
public class UserOtpVerification {

    @Id
    private String verification_value;

    private String verification_type;

    private int otp;

    private boolean status;

    @Temporal(TemporalType.TIMESTAMP)
    private Calendar updated_on = Calendar.getInstance(TimeZone.getTimeZone("UTC"));

    public String getVerification_value() {
        return verification_value;
    }

    public void setVerification_value(String verification_value) {
        this.verification_value = verification_value;
    }

    public String getVerification_type() {
        return verification_type;
    }

    public void setVerification_type(String verification_type) {
        this.verification_type = verification_type;
    }

    public int getOtp() {
        return otp;
    }

    public void setOtp(int otp) {
        this.otp = otp;
    }

    public Calendar getUpdated_on() {
        return updated_on;
    }

    public void setUpdated_on(Calendar updated_on) {
        this.updated_on = updated_on;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }
}
