package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name="device_replaced_history", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class DeviceReplacedHistory implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen",strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	
	@Column(name = "user_id")
	private long userId = 0;
	
	@Column(name = "old_meid")
	private String oldMeid = "NA";
	
	@Column(name = "new_meid")
	private String newMeid = "NA";
	
	@Column(name = "registered_date")
	private String registeredDate = "1753-01-01 00:00:00";
	
	@Column(name = "is_return_received")
	private boolean isReturnReceived = false;
	
	@Column(name = "tracking_no")
	private String trackingNo = "NA" ;
	
	@Column(name = "device_received_date")
	private String deviceReceivedDate = "1753-01-01 00:00:00";
	
	@Column(name = "action")
	private String action = "NA";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getOldMeid() {
		return oldMeid;
	}

	public void setOldMeid(String oldMeid) {
		this.oldMeid = oldMeid;
	}

	public String getNewMeid() {
		return newMeid;
	}

	public void setNewMeid(String newMeid) {
		this.newMeid = newMeid;
	}

	public String getRegisteredDate() {
		return registeredDate;
	}

	public void setRegisteredDate(String registeredDate) {
		this.registeredDate = registeredDate;
	}

	public boolean isReturnReceived() {
		return isReturnReceived;
	}

	public void setReturnReceived(boolean isReturnReceived) {
		this.isReturnReceived = isReturnReceived;
	}

	public String getTrackingNo() {
		return trackingNo;
	}

	public void setTrackingNo(String trackingNo) {
		this.trackingNo = trackingNo;
	}

	public String getDeviceReceivedDate() {
		return deviceReceivedDate;
	}

	public void setDeviceReceivedDate(String deviceReceivedDate) {
		this.deviceReceivedDate = deviceReceivedDate;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}


	
	
}

