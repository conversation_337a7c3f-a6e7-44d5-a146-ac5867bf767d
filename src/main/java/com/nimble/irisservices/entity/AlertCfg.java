package com.nimble.irisservices.entity;

import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="alertcfg")
public class AlertCfg {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	@Column(name="name")
	private String name;
	@Column(name="minval")
	private float minval;
	@Column(name="maxval")
	private float maxval;
	@Column(name="rule")
	private String rule;
	@Column(name="lat")
	private double lat;
	@Column(name="lon")
	private double lon;
	@Column(name="radius")
	private float radius;
	@Column(name="fencetype")
	private float fencetype;
	@Column(name="severity")
	private int severity;
	@Column(name="enable")
	private boolean enable;
	@Column(name="levelpattern")
	private String levelpattern;
	@Column(name="notifyfreq")
	private int notifyfreq;
	@Column(name="min_notifyfreq")
	private int min_notifyfreq=1800;
	@Column(name="mobileNos")
	private String mobilenos;
	@Column(name="country")
	private String country;
	@Column(name="emailids")
	private String emailids;
	@Column(name="alertmsg")
	private String alertmsg;
	@Column(name="intermittentfreq")
	private int intermittentfreq = 1;
	@Column(name="notificationtype")
	private String notificationtype;
	@Column(name="alertstarttime")
	private String alertstarttime;
	@Column(name="alertendtime")
	private String alertendtime;
	@Column(name="voicealertStarttime")
	private String voicealertstarttime;
	@Column(name="voicealertstop")
	private String voicealertstoptime                               ;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="alerttype_id")
	//@JsonBackReference
    private AlertType alerttype;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name="cmp_id")
	//@JsonBackReference
    private Company company;
	
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "alertcfg_to_asset", 
             joinColumns = { @JoinColumn(name = "alertcfg_id") }, 
             inverseJoinColumns = { @JoinColumn(name = "asset_id") })
    private Set<Asset> assets = new HashSet<Asset>();
	
	@Transient
	private long alerttypeid;
	
	@Column(name="enable_delay_freq")
	private boolean enableDelayFreq = false;
	
	@Column(name="delay_freq_secs")
	private int delayFreqSecs = 0;
	
	@Column(name="updated_on")
	private String updated_on;
	
	public AlertCfg() {
		super();
	}

	public AlertCfg(long id, String name, float minval, float maxval,
			String rule, double lat, double lon, float radius, float fencetype,
			int severity, boolean enable, String levelpattern, int notifyfreq,
			String mobilenos, String country, String emailids, String alertmsg,
			int intermittentfreq,String notificationtype,String alertstarttime,
			String alertendtime,String voicealertstarttime, String voicealertstoptime,
			AlertType alerttype, Company company, int min_notifyfreq,boolean enableDelayFreq,int delayFreqSecs) {
		super();
		this.id = id;
		this.name = name;
		this.minval = minval;
		this.maxval = maxval;
		this.rule = rule;
		this.lat = lat;
		this.lon = lon;
		this.radius = radius;
		this.fencetype = fencetype;
		this.severity = severity;
		this.enable = enable;
		this.levelpattern = levelpattern;
		this.notifyfreq = notifyfreq;
		this.mobilenos = mobilenos;
		this.country = country;
		this.emailids = emailids;
		this.alertmsg = alertmsg;
		this.intermittentfreq= intermittentfreq;
		this.notificationtype = notificationtype;
		this.alertstarttime = alertstarttime;
		this.alertendtime = alertendtime;
		this.voicealertstarttime = voicealertstarttime;
		this.voicealertstoptime = voicealertstoptime;
		this.alerttype = alerttype;
		this.company = company;
		this.min_notifyfreq = min_notifyfreq;
		this.enableDelayFreq = enableDelayFreq;
		this.delayFreqSecs = delayFreqSecs;
	}

	public long giveAlerttypeid() {
		return alerttypeid;
	}

	public void setAlerttypeid(long alerttypeid) {
		this.alerttypeid = alerttypeid;
	}
	@Transient
	private Long[] assetids;
	
	
	public Long[] giveAssetids() {
		return assetids;
	}

	public void setAssetids(Long[] assetids) {
		this.assetids = assetids;
	}
	
	

	public long getId() {
		return id;
	}

	public String getName() {
		return name;
	}

	public float getMinval() {
		return minval;
	}

	public float getMaxval() {
		return maxval;
	}

	public double getLat() {
		return lat;
	}

	public double getLon() {
		return lon;
	}

	public float getRadius() {
		return radius;
	}
	
	public float getFencetype() {
		return fencetype;
	}

	public int getSeverity() {
		return severity;
	}

	public boolean isEnable() {
		return enable;
	}

	public String getLevelpattern() {
		return levelpattern;
	}

	public int getNotifyfreq() {
		return notifyfreq;
	}
	
	

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getEmailids() {
		return emailids;
	}

	public void setEmailids(String emailids) {
		this.emailids = emailids;
	}

	public int getIntermittentfreq() {
		return intermittentfreq;
	}

	public String getNotificationtype() {
		return notificationtype;
	}
	

	public String getMobilenos() {
		return mobilenos;
	}

	public void setMobilenos(String mobilenos) {
		this.mobilenos = mobilenos;
	}

	public String getAlertmsg() {
		return alertmsg;
	}

	public void setAlertmsg(String alertmsg) {
		this.alertmsg = alertmsg;
	}

	public String getAlertstarttime() {
		return alertstarttime;
	}

	public void setAlertstarttime(String alertstarttime) {
		this.alertstarttime = alertstarttime;
	}

	public String getAlertendtime() {
		return alertendtime;
	}

	public void setAlertendtime(String alertendtime) {
		this.alertendtime = alertendtime;
	}

	public String getVoicealertstarttime() {
		return voicealertstarttime;
	}

	public void setVoicealertstarttime(String voicealertstarttime) {
		this.voicealertstarttime = voicealertstarttime;
	}

	public String getVoicealertstoptime() {
		return voicealertstoptime;
	}

	public void setVoicealertstoptime(String voicealertstoptime) {
		this.voicealertstoptime = voicealertstoptime;
	}

	public String getRule() {
		return rule;
	}

	public AlertType getAlerttype() {
		return alerttype;
	}
	
	
	public Company giveCompany() {
		return company;
	}

	public void setId(long id) {
		this.id = id;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setMinval(float minval) {
		this.minval = minval;
	}

	public void setMaxval(float maxval) {
		this.maxval = maxval;
	}

	public void setRule(String rule) {
		this.rule = rule;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public void setLon(double lon) {
		this.lon = lon;
	}

	public void setRadius(float radius) {
		this.radius = radius;
	}
	
	public void setFencetype(float fencetype) {
		this.fencetype = fencetype;
	}

	public void setSeverity(int severity) {
		this.severity = severity;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public void setLevelpattern(String levelpattern) {
		this.levelpattern = levelpattern;
	}

	public void setNotifyfreq(int notifyfreq) {
		this.notifyfreq = notifyfreq;
	}


	public void setIntermittentfreq(int intermittentfreq) {
		this.intermittentfreq = intermittentfreq;
	}
	
	public void setNotificationtype(String notificationtype) {
		this.notificationtype = notificationtype;
	}


	public void setAlerttype(AlertType alerttype) {
		this.alerttype = alerttype;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public void setAssets(Set<Asset> assets) {
		this.assets = assets;
	}

	public Set<Asset> getAssets() {
		return assets;
	}

	public int getMin_notifyfreq() {
		return min_notifyfreq;
	}

	public void setMin_notifyfreq(int min_notifyfreq) {
		this.min_notifyfreq = min_notifyfreq;
	}

	public boolean isEnableDelayFreq() {
		return enableDelayFreq;
	}

	public void setEnableDelayFreq(boolean enableDelayFreq) {
		this.enableDelayFreq = enableDelayFreq;
	}

	public int getDelayFreqSecs() {
		return delayFreqSecs;
	}

	public void setDelayFreqSecs(int delayFreqSecs) {
		this.delayFreqSecs = delayFreqSecs;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}
	
}
