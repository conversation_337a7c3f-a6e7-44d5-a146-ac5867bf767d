package com.nimble.irisservices.entity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "cancel_feedback")
public class CancelFeedback {

	
	@Id
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	
	private String feedback_type = "NA";
	
	private boolean enable = false;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getFeedback_type() {
		return feedback_type;
	}

	public void setFeedback_type(String feedback_type) {
		this.feedback_type = feedback_type;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public CancelFeedback() {
		super();
	}
	
	public CancelFeedback(long id, String feedback_type, boolean enable) {
		super();
		this.id = id;
		this.feedback_type = feedback_type;
		this.enable = enable;
	}
	
	
	
}
