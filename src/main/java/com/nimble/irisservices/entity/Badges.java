package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "badges")
public class Badges implements Serializable {
	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	
	@Column(name = "badge_name")
	private String badge_name;
	
	@Column(name = "level")
	private String level;
	
	@Column(name = "img_path")
	private String img_path;
	
	@Column(name = "desc1")
	private String desc1;
	
	@Column(name = "desc2")
	private String desc2;

	@Column(name = "desc3")
	private String desc3;

	@Column(name = "enable")
	private boolean enable;

	@Column(name = "createdon")
	private String createdon = "1753-01-01 00:00:00";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getBadge_name() {
		return badge_name;
	}

	public void setBadge_name(String badge_name) {
		this.badge_name = badge_name;
	}

	public String getLevel() {
		return level;
	}

	public void setLevel(String level) {
		this.level = level;
	}

	public String getImg_path() {
		return img_path;
	}

	public void setImg_path(String img_path) {
		this.img_path = img_path;
	}

	public String getDesc1() {
		return desc1;
	}

	public void setDesc1(String desc1) {
		this.desc1 = desc1;
	}

	public String getDesc2() {
		return desc2;
	}

	public void setDesc2(String desc2) {
		this.desc2 = desc2;
	}

	public String getDesc3() {
		return desc3;
	}

	public void setDesc3(String desc3) {
		this.desc3 = desc3;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getCreatedon() {
		return createdon;
	}

	public void setCreatedon(String createdon) {
		this.createdon = createdon;
	};

}
