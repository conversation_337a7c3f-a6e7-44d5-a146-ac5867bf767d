package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;


@Entity(name="all_chargebee_subscription")
@Table(name="all_chargebee_subscription",uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class AllSubscription implements Serializable{
	
	@Id  
    @Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	
	@Column(name="subscription_id")
	private String subscriptionId;
	
	@Column(name="plan_id")
	private String planId;
	
	@Column(name="subscription_status")
	private String subscriptionStatus;
	
	@Column(name="subscription_created_at")
	private String subscriptionCreatedAt;
	
	@Column(name="subscription_started_at")
	private String subscriptionStartedAt;
	
	@Column(name="subscription_activated_at")
	private String subscriptionActivatedAt;
	
	@Column(name="subscription_cancelled_at")
	private String subscriptionCancelledAt;
	
	@Column(name="plan_amount")
	private double planAmount;
	
	@Column(name="chargebee_id")
	private String customerId;
	
	@Column(name="billing_email")
	private String billingEmail;
	
	@Column(name="addons")
	private String addons;
	
	@Column(name="trial_start")
	private String trialStart;
	
	@Column(name="trial_end")
	private String trialEnd;
	
	@Column(name="subscription_next_billing_at")
	private String nextBillingAt;
	
	@Column(name="updated_indb")
	private String updatedIndb;
	
	@Column(name="is_deleted")
	private int isDeleted;
	
	@Column(name="metadata")
	private String metaData;
	
	@Column(name="enable")
	private int enable;
	
	@Column(name="updated_date")
	private String updatedDate;
	
	@Column(name="plan_period")
	private String planPeriod;
	
	@Column(name="quantity")
	private int quantity;


	public Integer getId() {
		return id;
	}


	public void setId(Integer id) {
		this.id = id;
	}


	public String getSubscriptionId() {
		return subscriptionId;
	}


	public void setSubscriptionId(String subscriptionId) {
		this.subscriptionId = subscriptionId;
	}


	public String getPlanId() {
		return planId;
	}


	public void setPlanId(String planId) {
		this.planId = planId;
	}


	public String getSubscriptionStatus() {
		return subscriptionStatus;
	}


	public void setSubscriptionStatus(String subscriptionStatus) {
		this.subscriptionStatus = subscriptionStatus;
	}


	public String getSubscriptionCreatedAt() {
		return subscriptionCreatedAt;
	}


	public void setSubscriptionCreatedAt(String subscriptionCreatedAt) {
		this.subscriptionCreatedAt = subscriptionCreatedAt;
	}


	public String getSubscriptionStartedAt() {
		return subscriptionStartedAt;
	}


	public void setSubscriptionStartedAt(String subscriptionStartedAt) {
		this.subscriptionStartedAt = subscriptionStartedAt;
	}


	public String getSubscriptionActivatedAt() {
		return subscriptionActivatedAt;
	}


	public void setSubscriptionActivatedAt(String subscriptionActivatedAt) {
		this.subscriptionActivatedAt = subscriptionActivatedAt;
	}


	public String getSubscriptionCancelledAt() {
		return subscriptionCancelledAt;
	}


	public void setSubscriptionCancelledAt(String subscriptionCancelledAt) {
		this.subscriptionCancelledAt = subscriptionCancelledAt;
	}

	public String getCustomerId() {
		return customerId;
	}


	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}


	public String getBillingEmail() {
		return billingEmail;
	}


	public void setBillingEmail(String customerEmail) {
		this.billingEmail = customerEmail;
	}


	public String getAddons() {
		return addons;
	}


	public void setAddons(String addons) {
		this.addons = addons;
	}


	public String getTrialStart() {
		return trialStart;
	}


	public void setTrialStart(String trialStart) {
		this.trialStart = trialStart;
	}


	public String getTrialEnd() {
		return trialEnd;
	}


	public void setTrialEnd(String trialEnd) {
		this.trialEnd = trialEnd;
	}


	public String getNextBillingAt() {
		return nextBillingAt;
	}


	public void setNextBillingAt(String nextBillingAt) {
		this.nextBillingAt = nextBillingAt;
	}


	public String getUpdatedIndb() {
		return updatedIndb;
	}


	public void setUpdatedIndb(String updatedIndb) {
		this.updatedIndb = updatedIndb;
	}


	public int getIsDeleted() {
		return isDeleted;
	}


	public void setIsDeleted(int isDeleted) {
		this.isDeleted = isDeleted;
	}


	public String getMetaData() {
		return metaData;
	}


	public void setMetaData(String metaData) {
		this.metaData = metaData;
	}


	public int getEnable() {
		return enable;
	}


	public void setEnable(int enable) {
		this.enable = enable;
	}


	public String getUpdatedDate() {
		return updatedDate;
	}


	public void setUpdatedDate(String updatedDate) {
		this.updatedDate = updatedDate;
	}


	public String getPlanPeriod() {
		return planPeriod;
	}


	public void setPlanPeriod(String planPeriod) {
		this.planPeriod = planPeriod;
	}

	public AllSubscription() {
		super();
	}
	
	public AllSubscription(String subscriptionId, String planId, String subscriptionStatus,
			String subscriptionCreatedAt, String subscriptionStartedAt, String subscriptionActivatedAt,
			String subscriptionCancelledAt, double planAmount, String customerId, String billingEmail, String addons,
			String trialStart, String trialEnd, String nextBillingAt, String updatedIndb, int isDeleted,
			String metaData, int enable, String updatedDate, String planPeriod) {
		super();
		this.subscriptionId = subscriptionId;
		this.planId = planId;
		this.subscriptionStatus = subscriptionStatus;
		this.subscriptionCreatedAt = subscriptionCreatedAt;
		this.subscriptionStartedAt = subscriptionStartedAt;
		this.subscriptionActivatedAt = subscriptionActivatedAt;
		this.subscriptionCancelledAt = subscriptionCancelledAt;
		this.planAmount = planAmount;
		this.customerId = customerId;
		this.billingEmail = billingEmail;
		this.addons = addons;
		this.trialStart = trialStart;
		this.trialEnd = trialEnd;
		this.nextBillingAt = nextBillingAt;
		this.updatedIndb = updatedIndb;
		this.isDeleted = isDeleted;
		this.metaData = metaData;
		this.enable = enable;
		this.updatedDate = updatedDate;
		this.planPeriod = planPeriod;
	}


	public double getPlanAmount() {
		return planAmount;
	}


	public void setPlanAmount(double planAmount) {
		this.planAmount = planAmount;
	}


	public int getQuantity() {
		return quantity;
	}


	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}	
}
