package com.nimble.irisservices.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;


@Entity
@Table(name = "user_location")
public class UserLocation {

	@Id
	private long id;
	
	private long user_id;
	
	private double latitude;
	
	private double longitude;
	
	private String updated_on = "1753-01-01 00:00:00";
	
	private int weather_id = 0;
	
	private String weather_condition = "NA";
	
	private String weather_description = "NA";
	
	private String area = "NA";
	
	private long gateway_id;

	public UserLocation() {
		super();
	}

	public UserLocation(long user_id, double latitude, double longitude, String updated_on, int weather_id,
			String weather_condition, String weather_description, String area) {
		super();
		this.user_id = user_id;
		this.latitude = latitude;
		this.longitude = longitude;
		this.updated_on = updated_on;
		this.weather_id = weather_id;
		this.weather_condition = weather_condition;
		this.weather_description = weather_description;
		this.area = area;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public double getLatitude() {
		return latitude;
	}

	public void setLatitude(double latitude) {
		this.latitude = latitude;
	}

	public double getLongitude() {
		return longitude;
	}

	public void setLongitude(double longitude) {
		this.longitude = longitude;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public int getWeather_id() {
		return weather_id;
	}

	public void setWeather_id(int weather_id) {
		this.weather_id = weather_id;
	}

	public String getWeather_condition() {
		return weather_condition;
	}

	public void setWeather_condition(String weather_condition) {
		this.weather_condition = weather_condition;
	}

	public String getWeather_description() {
		return weather_description;
	}

	public void setWeather_description(String weather_description) {
		this.weather_description = weather_description;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}
}
