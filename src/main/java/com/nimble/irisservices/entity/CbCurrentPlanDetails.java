package com.nimble.irisservices.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "cb_current_plan")
public class CbCurrentPlanDetails {
	
	private int plan_to_period_id = 0;
	
	private int plan_id = 0;
	
	private int period_id = 0;
	
	private long user_id = 0;
	
	@Id
	private String chargebee_id = "NA";
	
	private String current_plan = "NA";
	
	private String current_plan_response = "NA";

	private String updated_on = "1753-11-11 11:11:11";
	
	public int getPlan_to_period_id() {
		return plan_to_period_id;
	}

	public void setPlan_to_period_id(int plan_to_period_id) {
		this.plan_to_period_id = plan_to_period_id;
	}

	public int getPlan_id() {
		return plan_id;
	}

	public void setPlan_id(int plan_id) {
		this.plan_id = plan_id;
	}

	public int getPeriod_id() {
		return period_id;
	}

	public void setPeriod_id(int period_id) {
		this.period_id = period_id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getChargebee_id() {
		return chargebee_id;
	}

	public void setChargebee_id(String chargebee_id) {
		this.chargebee_id = chargebee_id;
	}

	public String getCurrent_plan() {
		return current_plan;
	}

	public void setCurrent_plan(String current_plan) {
		this.current_plan = current_plan;
	}

	public String getCurrent_plan_response() {
		return current_plan_response;
	}

	public void setCurrent_plan_response(String current_plan_response) {
		this.current_plan_response = current_plan_response;
	}
	
	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public CbCurrentPlanDetails() {
		super();
	}
	
	public CbCurrentPlanDetails(int plan_to_period_id, int plan_id, int period_id, long user_id,
			String chargebee_id, String current_plan, String current_plan_response, String updated_on) {
		super();
		this.plan_to_period_id = plan_to_period_id;
		this.plan_id = plan_id;
		this.period_id = period_id;
		this.user_id = user_id;
		this.chargebee_id = chargebee_id;
		this.current_plan = current_plan;
		this.current_plan_response = current_plan_response;
		this.updated_on = updated_on;
	}
	
}
