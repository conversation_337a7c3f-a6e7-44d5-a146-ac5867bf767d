package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "rv_checklist")
public class RVChecklist implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "listname")
	private String listname;
	
	@Column(name = "category")
	private String category;
	
	@Column(name = "listtype")
	private long listtype;
	
	@Column(name = "description")
	private String description = "NA";

	@Column(name = "enable")
	private boolean enable = true;

	@Column(name = "orderno")
	private int orderno = 1;
	
	@Column(name = "user_id")
	private long user_id = 0;

	@Column(name = "createdon")
	private String createdon = "1753-01-01 00:00:00";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getListname() {
		return listname;
	}

	public void setListname(String listname) {
		this.listname = listname;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public long getListtype() {
		return listtype;
	}

	public void setListtype(long listtype) {
		this.listtype = listtype;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public int getOrderno() {
		return orderno;
	}

	public void setOrderno(int orderno) {
		this.orderno = orderno;
	}

	public String getCreatedon() {
		return createdon;
	}

	public void setCreatedon(String createdon) {
		this.createdon = createdon;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}
	
}
