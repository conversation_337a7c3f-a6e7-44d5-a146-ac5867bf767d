package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "plan", uniqueConstraints = @UniqueConstraint(columnNames = { "plan_name" }))

public class SubscriptionPlan implements Serializable {
	private static final long serialVersionUID = 0l;

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "description")
	private String description;

	@Column(name = "display_msg")
	private String display_msg;

	@Column(name = "plan_name")
	private String plan_name;

	@Column(name = "enable")
	private boolean enable;

	@Column(name = "custom")
	private boolean custom;

	@Column(name = "alert_setting")
	private boolean alert_setting;

	@Column(name = "orderno")
	private int orderno;

	@Column(name = "pricecompare_period")
	private int pricecompare_period = 1;

	@Column(name = "plan_type")
	private String plan_type;

	@Column(name = "plan_ver")
	private String plan_ver = "V1";

	@Column(name = "feature_list")
	private String feature_list;

	@Column(name = "device_cnt")
	private int device_cnt = 1;

	@Column(name = "cur_feature")
	private String cur_feature = "";

	@Column(name = "cur_feature_flutter")
	private String cur_feature_flutter = "";

	@Column(name = "cur_feature_flutter_dark")
	private String cur_feature_flutter_dark = "";
	
	@Column(name = "monitor_type")
	private long monitor_type;
	
	@Column(name = "is_freeplan")
	private boolean is_freeplan;
	
	@Column(name = "cur_feature_ui_new")
	private String cur_feature_ui_new;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getDisplay_msg() {
		return display_msg;
	}

	public void setDisplay_msg(String display_msg) {
		this.display_msg = display_msg;
	}

	public String getPlan_name() {
		return plan_name;
	}

	public void setPlan_name(String plan_name) {
		this.plan_name = plan_name;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public boolean isCustom() {
		return custom;
	}

	public void setCustom(boolean custom) {
		this.custom = custom;
	}

	public int getOrderno() {
		return orderno;
	}

	public void setOrderno(int orderno) {
		this.orderno = orderno;
	}

	public boolean isAlert_setting() {
		return alert_setting;
	}

	public void setAlert_setting(boolean alert_setting) {
		this.alert_setting = alert_setting;
	}

	public int getPricecompare_period() {
		return pricecompare_period;
	}

	public void setPricecompare_period(int pricecompare_period) {
		this.pricecompare_period = pricecompare_period;
	}

	public String getPlan_type() {
		return plan_type;
	}

	public void setPlan_type(String plan_type) {
		this.plan_type = plan_type;
	}

	public String getPlan_ver() {
		return plan_ver;
	}

	public void setPlan_ver(String plan_ver) {
		this.plan_ver = plan_ver;
	}

	public String getFeature_list() {
		return feature_list;
	}

	public void setFeature_list(String feature_list) {
		this.feature_list = feature_list;
	}

	public int getDevice_cnt() {
		return device_cnt;
	}

	public void setDevice_cnt(int device_cnt) {
		this.device_cnt = device_cnt;
	}

	public String getCur_feature() {
		return cur_feature;
	}

	public void setCur_feature(String cur_feature) {
		this.cur_feature = cur_feature;
	}

	public String getCur_feature_flutter() {
		return cur_feature_flutter;
	}

	public void setCur_feature_flutter(String cur_feature_flutter) {
		this.cur_feature_flutter = cur_feature_flutter;
	}

	public String getCur_feature_flutter_dark() {
		return cur_feature_flutter_dark;
	}

	public void setCur_feature_flutter_dark(String cur_feature_flutter_dark) {
		this.cur_feature_flutter_dark = cur_feature_flutter_dark;
	}

	public long getMonitor_type() {
		return monitor_type;
	}

	public void setMonitor_type(long monitor_type) {
		this.monitor_type = monitor_type;
	}

	public boolean isIs_freeplan() {
		return is_freeplan;
	}

	public void setIs_freeplan(boolean is_freeplan) {
		this.is_freeplan = is_freeplan;
	}

	public String getCur_feature_ui_new() {
		return cur_feature_ui_new;
	}

	public void setCur_feature_ui_new(String cur_feature_ui_new) {
		this.cur_feature_ui_new = cur_feature_ui_new;
	}

}
