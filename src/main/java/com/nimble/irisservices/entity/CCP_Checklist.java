package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;


@Entity 
@Table(name="checklist", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class CCP_Checklist implements Serializable{
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	/*@Column(name="type_id")
	private long type_id;*/
	@Column(name="description")
	private String description;
	@Column(name="corrective_action")
	private String corrective_action;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="type_id")
	//@JsonBackReference
    private CCPType type;
	
	@Column(name="min")
	private Long min;
	@Column(name="max")
	private Long max;
	@Column(name="category")
	private String category;
	
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getDescription() {
		return description;
	}

	public CCPType getType() {
		return type;
	}

	public void setType(CCPType type) {
		this.type = type;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getCorrective_action() {
		return corrective_action;
	}

	public void setCorrective_action(String corrective_action) {
		this.corrective_action = corrective_action;
	}

	public Long getMin() {
		return min;
	}

	public void setMin(long min) {
		this.min = min;
	}

	public Long getMax() {
		return max;
	}

	public void setMax(long max) {
		this.max = max;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	
	
}
