package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name="plan_to_monitortype", uniqueConstraints =@UniqueConstraint (columnNames={"monitortype_id","plan_id" }) )

public class PlanToMonitorType implements Serializable{
	private static final long serialVersionUID = 0l;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="plan_id")
    private SubscriptionPlan plan_id;

	@Column(name="monitortype_id")
    private String monitortype_id;
	
	@Column(name="device_config")
    private String device_config;
	
	@Column(name="no_cnt ")
	private short no_cnt ;
	
	@Column(name = "custom")
	private boolean custom;
	
	public PlanToMonitorType()
	{
		super();
	}

	public SubscriptionPlan getPlan_id() {
		return plan_id;
	}

	public void setPlan_id(SubscriptionPlan plan_id) {
		this.plan_id = plan_id;
	}

	public String getMonitortype_id() {
		return monitortype_id;
	}

	public void setMonitortype_id(String monitortype_id) {
		this.monitortype_id = monitortype_id;
	}

	public String getDevice_config() {
		return device_config;
	}

	public void setDevice_config(String device_config) {
		this.device_config = device_config;
	}

	public short getNo_cnt() {
		return no_cnt;
	}

	public void setNo_cnt(short no_cnt) {
		this.no_cnt = no_cnt;
	}

	public boolean isCustom() {
		return custom;
	}

	public void setCustom(boolean custom) {
		this.custom = custom;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}	
}
