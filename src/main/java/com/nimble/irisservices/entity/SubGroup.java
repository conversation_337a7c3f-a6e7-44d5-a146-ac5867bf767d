package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;

import org.hibernate.annotations.GenericGenerator;

/*@Entity 
@Table(name="vsubgroup", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))*/
public class SubGroup  implements Serializable{
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name="subgroupname")
	private String name;
	
    @ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="group_id")
	//@JsonBackReference
    private Group group;
	
    @ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="cmp_id")
	//@JsonBackReference
    private Company company;
    
    @Transient
    private String groupid;
 
	public String giveGroupid() {
		return groupid;
	}

	public void setGroupid(String groupid) {
		this.groupid = groupid;
	}

	public long getId() {
		return id;
	}

	public String getName() {
		return name;
	}

	public Group getGroup() {
		return group;
	}

	public Company giveCompany() {
		return company;
	}
	


	public void setId(long id) {
		this.id = id;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setGroup(Group group) {
		this.group = group;
	}

	public void setCompany(Company company) {
		this.company = company;
	}
    
}
