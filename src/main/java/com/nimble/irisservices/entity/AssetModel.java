package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "assetmodel", uniqueConstraints = @UniqueConstraint(columnNames = { "id" }))
public class AssetModel implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	@Column(name = "model")
	private String model;

	@Column(name = "assettype")
	private String assettype;
	@Column(name = "sensoravailable")
	private String sensoravailable;
	@Column(name = "extsensortype")
	private String extensortype;
	@Column(name = "description")
	private String description;
	@Column(name = "inventorymodelname")
	private String inventorymodelname;

	@Column(name = "isgps")
	private String isgps;

	@Column(name = "isadvmode")
	private boolean isadvmode;

	@Column(name = "ispowermode")
	private boolean ispowermode;

	@Column(name = "is_ble")
	private boolean is_ble;

	@Column(name = "is_upgrade")
	private boolean is_upgrade;

	@Column(name = "ishumidity")
	private boolean ishumidity;

	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "monitor_type_id")
	private MonitorType monitor_type;

	@Column(name = "enable_delay_freq")
	private boolean enableDelayFreq = false;

	@Column(name = "delay_freq_secs")
	private int delayFreqSecs = 0;

	@Column(name = "sku_number")
	private String skuNumber = "NA";

	@Column(name = "is_temp_calib")
	private boolean is_temp_calib = false;

	@Column(name = "temp_calib")
	private float temp_calib = 0;

	@Column(name = "battery_offset")
	private float battery_offset = 0;

	@Column(name = "charging_offset")
	private float charging_offset = 0;

	@Column(name = "fullcharge_offset")
	private float fullcharge_offset = 0;
	
	@Column(name = "is_pl_threshold")
	private boolean is_pl_threshold = false;

	@Column(name = "default_pl_threshold")
	private int default_pl_threshold = 0;
	
	@Column(name = "country")
	private String country = "US";
	
	@Column(name = "live_tracking")
	private boolean live_tracking = false;
	
	@Column(name = "is_aqi")
	private boolean is_aqi = false;
	
	@Column(name = "model_alias_name")
	private String model_alias_name = "";
	
	@Column(name = "ble_version")
	private String ble_version = "";

	@Column(name = "is_wifidelete")
	private boolean is_wifidelete = false;
	
	@Column(name = "debug_cmd")
	private String debug_cmd = "";
	
	@Column(name = "is_wifi_connection")
	private boolean is_wifi_connection = false;
	
	@Column(name = "is_manual_connection")
	private boolean is_manual_connection = false;
	
	@Column(name = "is_static_wifi")
	private String is_static_wifi = "";
	
	@Column(name = "min_rotation")
	private int min_rotation = 0;
	
	@Column(name = "max_rotation")
	private int max_rotation = 0;
	
	@Column(name = "motion_detection")
	private boolean motion_detection = false;
	
	@Column(name = "stored_video")
	private boolean stored_video = false;
	
	@Column(name = "debug")
	private boolean debug = false;
	
	@Column(name = "barking_alert")
	private boolean sound_alert = false;
	
	@Column(name = "temp_alert")
	private boolean temp_alert = false;

	@Column(name = "dnr_interval")
	private int dnrInterval = 60;
	
	public long getId() {
		return id;
	}

	public String getModel() {
		return model;
	}

	public String getAssettype() {
		return assettype;
	}

	public String getSensoravailable() {
		return sensoravailable;
	}

	public String getExtensortype() {
		return extensortype;
	}

	public String getDescription() {
		return description;
	}

	public String getInventorymodelname() {
		return inventorymodelname;
	}

	public void setInventorymodelname(String inventorymodelname) {
		this.inventorymodelname = inventorymodelname;
	}

	public String getIsgps() {
		return isgps;
	}

	public void setIsgps(String isgps) {
		this.isgps = isgps;
	}

	public MonitorType getMonitor_type() {
		return monitor_type;
	}

	public void setMonitor_type(MonitorType monitor_type) {
		this.monitor_type = monitor_type;
	}

	public boolean isAdvmode() {
		return isadvmode;
	}

	public void setAdvmode(boolean isadvmode) {
		this.isadvmode = isadvmode;
	}

	public boolean isPowermode() {
		return ispowermode;
	}

	public void setPowermode(boolean ispowermode) {
		this.ispowermode = ispowermode;
	}

	public boolean isBle() {
		return is_ble;
	}

	public void setBle(boolean is_ble) {
		this.is_ble = is_ble;
	}

	public boolean isIs_upgrade() {
		return is_upgrade;
	}

	public void setIs_upgrade(boolean is_upgrade) {
		this.is_upgrade = is_upgrade;
	}

	public boolean isIshumidity() {
		return ishumidity;
	}

	public void setIshumidity(boolean ishumidity) {
		this.ishumidity = ishumidity;
	}

	public boolean isEnableDelayFreq() {
		return enableDelayFreq;
	}

	public void setEnableDelayFreq(boolean enableDelayFreq) {
		this.enableDelayFreq = enableDelayFreq;
	}

	public int getDelayFreqSecs() {
		return delayFreqSecs;
	}

	public void setDelayFreqSecs(int delayFreqSecs) {
		this.delayFreqSecs = delayFreqSecs;
	}

	public void setId(long id) {
		this.id = id;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public void setAssettype(String assettype) {
		this.assettype = assettype;
	}

	public void setSensoravailable(String sensoravailable) {
		this.sensoravailable = sensoravailable;
	}

	public void setExtensortype(String extensortype) {
		this.extensortype = extensortype;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getSkuNumber() {
		return skuNumber;
	}

	public void setSkuNumber(String skuNumber) {
		this.skuNumber = skuNumber;
	}

	public boolean isIs_temp_calib() {
		return is_temp_calib;
	}

	public void setIs_temp_calib(boolean is_temp_calib) {
		this.is_temp_calib = is_temp_calib;
	}

	public float getTemp_calib() {
		return temp_calib;
	}

	public void setTemp_calib(float temp_calib) {
		this.temp_calib = temp_calib;
	}

	public float getBattery_offset() {
		return battery_offset;
	}

	public void setBattery_offset(float battery_offset) {
		this.battery_offset = battery_offset;
	}

	public float getCharging_offset() {
		return charging_offset;
	}

	public void setCharging_offset(float charging_offset) {
		this.charging_offset = charging_offset;
	}

	public float getFullcharge_offset() {
		return fullcharge_offset;
	}

	public void setFullcharge_offset(float fullcharge_offset) {
		this.fullcharge_offset = fullcharge_offset;
	}

	public boolean isIs_pl_threshold() {
		return is_pl_threshold;
	}

	public void setIs_pl_threshold(boolean is_pl_threshold) {
		this.is_pl_threshold = is_pl_threshold;
	}

	public int getDefault_pl_threshold() {
		return default_pl_threshold;
	}

	public void setDefault_pl_threshold(int default_pl_threshold) {
		this.default_pl_threshold = default_pl_threshold;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public boolean isIsadvmode() {
		return isadvmode;
	}

	public void setIsadvmode(boolean isadvmode) {
		this.isadvmode = isadvmode;
	}

	public boolean isIspowermode() {
		return ispowermode;
	}

	public void setIspowermode(boolean ispowermode) {
		this.ispowermode = ispowermode;
	}

	public boolean isIs_ble() {
		return is_ble;
	}

	public void setIs_ble(boolean is_ble) {
		this.is_ble = is_ble;
	}

	public boolean isLive_tracking() {
		return live_tracking;
	}

	public void setLive_tracking(boolean live_tracking) {
		this.live_tracking = live_tracking;
	}

	public boolean isIs_aqi() {
		return is_aqi;
	}

	public void setIs_aqi(boolean is_aqi) {
		this.is_aqi = is_aqi;
	}

	public String getModel_alias_name() {
		return model_alias_name;
	}

	public void setModel_alias_name(String model_alias_name) {
		this.model_alias_name = model_alias_name;
	}

	public String getBle_version() {
		return ble_version;
	}

	public void setBle_version(String ble_version) {
		this.ble_version = ble_version;
	}

	public boolean isIs_wifidelete() {
		return is_wifidelete;
	}

	public void setIs_wifidelete(boolean is_wifidelete) {
		this.is_wifidelete = is_wifidelete;
	}

	public String getDebug_cmd() {
		return debug_cmd;
	}

	public void setDebug_cmd(String debug_cmd) {
		this.debug_cmd = debug_cmd;
	}

	public boolean isIs_wifi_connection() {
		return is_wifi_connection;
	}

	public void setIs_wifi_connection(boolean is_wifi_connection) {
		this.is_wifi_connection = is_wifi_connection;
	}

	public boolean isIs_manual_connection() {
		return is_manual_connection;
	}

	public void setIs_manual_connection(boolean is_manual_connection) {
		this.is_manual_connection = is_manual_connection;
	}

	public String getIs_static_wifi() {
		return is_static_wifi;
	}

	public void setIs_static_wifi(String is_static_wifi) {
		this.is_static_wifi = is_static_wifi;
	}

	public int getMin_rotation() {
		return min_rotation;
	}

	public void setMin_rotation(int min_rotation) {
		this.min_rotation = min_rotation;
	}

	public int getMax_rotation() {
		return max_rotation;
	}

	public void setMax_rotation(int max_rotation) {
		this.max_rotation = max_rotation;
	}

	public boolean isMotion_detection() {
		return motion_detection;
	}

	public void setMotion_detection(boolean motion_detection) {
		this.motion_detection = motion_detection;
	}

	public boolean isStored_video() {
		return stored_video;
	}

	public void setStored_video(boolean stored_video) {
		this.stored_video = stored_video;
	}

	public boolean isDebug() {
		return debug;
	}

	public void setDebug(boolean debug) {
		this.debug = debug;
	}

	public boolean isSound_alert() {
		return sound_alert;
	}

	public void setSound_alert(boolean sound_alert) {
		this.sound_alert = sound_alert;
	}

	public boolean isTemp_alert() {
		return temp_alert;
	}

	public void setTemp_alert(boolean temp_alert) {
		this.temp_alert = temp_alert;
	}

	public int getDnrInterval() {
		return dnrInterval;
	}

	public void setDnrInterval(int dnrInterval) {
		this.dnrInterval = dnrInterval;
	}
}
