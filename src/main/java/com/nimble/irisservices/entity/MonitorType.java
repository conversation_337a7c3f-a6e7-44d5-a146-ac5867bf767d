package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "monitortype", uniqueConstraints = @UniqueConstraint(columnNames = { "name" }))
public class MonitorType implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "name")
	private String name;

	@Column(name = "description")
	private String description;

	@Column(name = "imageurl")
	private String imageurl;
	
	@Column(name = "category")
	private String category;
	
	@Column(name = "devicemaxcount")
	private long deviceMaxCount;

	@Column(name = "enable")
	private boolean enable;
	
	@Column(name = "infourl")
	private String infourl;
	
	@Column(name = "display_name")
	private String display_name;
	
	@Column(name = "order")
	private long order;
	
	private String product_link;
	
	public MonitorType() {
		super();
		// TODO Auto-generated constructor stub
	}

	public MonitorType(long id,String name, String description, String imageurl, String category, long deviceMaxCount,
			boolean enable,String infourl) {
		super();
		this.id = id;
		this.name = name;
		this.description = description;
		this.imageurl = imageurl;
		this.category = category;
		this.deviceMaxCount = deviceMaxCount;
		this.enable = enable;
		this.infourl = infourl;
	}

	public long getId() {
		return id;
	}

	public void setId(long l) {
		this.id = l;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getImageurl() {
		return imageurl;
	}

	public void setImageurl(String imageurl) {
		this.imageurl = imageurl;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public long getDeviceMaxCount() {
		return deviceMaxCount;
	}

	public void setDeviceMaxCount(long deviceMaxCount) {
		this.deviceMaxCount = deviceMaxCount;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getInfourl() {
		return infourl;
	}

	public void setInfourl(String infourl) {
		this.infourl = infourl;
	}

	public String getDisplay_name() {
		return display_name;
	}

	public void setDisplay_name(String display_name) {
		this.display_name = display_name;
	}

	public long getOrder() {
		return order;
	}

	public void setOrder(long order) {
		this.order = order;
	}

	public String getProduct_link() {
		return product_link;
	}

	public void setProduct_link(String product_link) {
		this.product_link = product_link;
	}
}
