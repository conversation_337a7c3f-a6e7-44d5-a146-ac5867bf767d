package com.nimble.irisservices.entity;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name="furbitdailyreport")
public class FurBitDailyReport implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@Id
	@Column(name="id",unique = true, nullable = false)
	@GeneratedValue(strategy=IDENTITY)
	private long id;
	@Column(name="date")
	private Date date;
	@Column(name="time")
	private Time time;
	@Column(name="datetime")
	private Timestamp datetime;
	@Column(name="totalstepcount")
	private long totalstepcount;
	@Column(name="totalidlesecs")
	private long totalidlesecs;
	@Column(name="totalwalksecs")
	private long totalwalksecs;
	@Column(name="totalrunsecs")
	private long totalrunsecs;
	@Column(name="runstepcount")
	private long runstepcount;
	@Column(name="walkstepcount")
	private long walkstepcount;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name="gateway_id")
	private Gateway gateway;

	/**
	 * @return the id
	 */
	public long getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(long id) {
		this.id = id;
	}
	/**
	 * @return the date
	 */
	public Date getDate() {
		return date;
	}

	/**
	 * @param date the date to set
	 */
	public void setDate(Date date) {
		this.date = date;
	}

	/**
	 * @return the time
	 */
	public Time getTime() {
		return time;
	}

	/**
	 * @param time the time to set
	 */
	public void setTime(Time time) {
		this.time = time;
	}

	/**
	 * @return the datetime
	 */
	public Timestamp getDatetime() {
		return datetime;
	}

	/**
	 * @param datetime the datetime to set
	 */
	public void setDatetime(Timestamp datetime) {
		this.datetime = datetime;
	}

	/**
	 * @return the totalstepcount
	 */
	public long getTotalstepcount() {
		return totalstepcount;
	}

	/**
	 * @param totalstepcount the totalstepcount to set
	 */
	public void setTotalstepcount(long totalstepcount) {
		this.totalstepcount = totalstepcount;
	}

	/**
	 * @return the totalidlesecs
	 */
	public long getTotalidlesecs() {
		return totalidlesecs;
	}

	/**
	 * @param totalidlesecs the totalidlesecs to set
	 */
	public void setTotalidlesecs(long totalidlesecs) {
		this.totalidlesecs = totalidlesecs;
	}

	/**
	 * @return the totalwalksecs
	 */
	public long getTotalwalksecs() {
		return totalwalksecs;
	}

	/**
	 * @param totalwalksecs the totalwalksecs to set
	 */
	public void setTotalwalksecs(long totalwalksecs) {
		this.totalwalksecs = totalwalksecs;
	}

	/**
	 * @return the totalrunsecs
	 */
	public long getTotalrunsecs() {
		return totalrunsecs;
	}

	/**
	 * @param totalrunsecs the totalrunsecs to set
	 */
	public void setTotalrunsecs(long totalrunsecs) {
		this.totalrunsecs = totalrunsecs;
	}

	/**
	 * @return the gateway
	 */
	public Gateway getGateway() {
		return gateway;
	}

	/**
	 * @param gateway the gateway to set
	 */
	public void setGateway(Gateway gateway) {
		this.gateway = gateway;
	}

	/**
	 * @return the runstepcount
	 */
	public long getRunstepcount() {
		return runstepcount;
	}

	/**
	 * @param runstepcount the runstepcount to set
	 */
	public void setRunstepcount(long runstepcount) {
		this.runstepcount = runstepcount;
	}

	/**
	 * @return the walkstepcount
	 */
	public long getWalkstepcount() {
		return walkstepcount;
	}

	/**
	 * @param walkstepcount the walkstepcount to set
	 */
	public void setWalkstepcount(long walkstepcount) {
		this.walkstepcount = walkstepcount;
	}
}
