package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;



@Entity 
@Table(name="zipcode_details")
public class ZipCodeDetails  implements Serializable{

	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	
	@Column(name="zipcode")
	private String zipcode = "NA";
	
	@Column(name="city")
	private String city = "NA";
	
	@Column(name="state")
	private String state = "NA";
	
	@Column(name="country")
	private String country = "NA";
	
	
	@Column(name = "createdon")
	private String createdOn="1753-01-01 00:00:00";


	/**
	 * @return the id
	 */
	public long getId() {
		return id;
	}


	/**
	 * @param id the id to set
	 */
	public void setId(long id) {
		this.id = id;
	}


	/**
	 * @return the zipcode
	 */
	public String getZipcode() {
		return zipcode;
	}


	/**
	 * @param zipcode the zipcode to set
	 */
	public void setZipcode(String zipcode) {
		this.zipcode = zipcode;
	}


	/**
	 * @return the city
	 */
	public String getCity() {
		return city;
	}


	/**
	 * @param city the city to set
	 */
	public void setCity(String city) {
		this.city = city;
	}


	/**
	 * @return the state
	 */
	public String getState() {
		return state;
	}


	/**
	 * @param state the state to set
	 */
	public void setState(String state) {
		this.state = state;
	}


	/**
	 * @return the country
	 */
	public String getCountry() {
		return country;
	}


	/**
	 * @param country the country to set
	 */
	public void setCountry(String country) {
		this.country = country;
	}


	/**
	 * @return the createdOn
	 */
	public String getCreatedOn() {
		return createdOn;
	}


	/**
	 * @param createdOn the createdOn to set
	 */
	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}


	public ZipCodeDetails(long id, String zipcode, String city, String state, String country, String createdOn) {
		super();
		this.id = id;
		this.zipcode = zipcode;
		this.city = city;
		this.state = state;
		this.country = country;
		this.createdOn = createdOn;
	}


	public ZipCodeDetails(String zipcode, String city, String state, String country, String createdOn) {
		super();
		this.zipcode = zipcode;
		this.city = city;
		this.state = state;
		this.country = country;
		this.createdOn = createdOn;
	}


	public ZipCodeDetails() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	
	
	
	
}
