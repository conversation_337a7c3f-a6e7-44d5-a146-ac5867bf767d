package com.nimble.irisservices.entity;

import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "referral_credits", uniqueConstraints = @UniqueConstraint(columnNames = { "id"}))
public class ReferralCredits {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name="title")
	private String title; 
	
	@Column(name="advocate_msg")
	private String advocate_msg; 
	
	@Column(name="advocate_desc")
	private String advocate_desc; 
	
	@Column(name="referral_msg")
	private String referral_msg;
	
	@Column(name="referral_desc")
	private String referral_desc; 	
	
	@Column(name="referral_successfull_days")
	private int referral_successfull_days; 
	
	@Column(name="advocate_successfull_days")
	private int advocate_successfull_days;
	
	@Column(name="createdon")
	private Timestamp createdon;
	
	@Column(name="enable")
	private boolean enable;
	
	@Column(name="expiry_date")
	private Timestamp expiry_date;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="referral_credit_type")
	private CreditType referral_credit_type;
	
	@Column(name="referral_credit_value")
	private int referral_credit_value;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="advocate_credit_type")
	private CreditType advocate_credit_type;
	
	@Column(name="advocate_credit_value")
	private int advocate_credit_value;

	public ReferralCredits() {
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}
	
	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getAdvocate_msg() {
		return advocate_msg;
	}

	public void setAdvocate_msg(String advocate_msg) {
		this.advocate_msg = advocate_msg;
	}

	public String getReferral_msg() {
		return referral_msg;
	}

	public void setReferral_msg(String referral_msg) {
		this.referral_msg = referral_msg;
	}

	public int getReferral_successfull_days() {
		return referral_successfull_days;
	}

	public void setReferral_successfull_days(int referral_successfull_days) {
		this.referral_successfull_days = referral_successfull_days;
	}

	public int getAdvocate_successfull_days() {
		return advocate_successfull_days;
	}

	public void setAdvocate_successfull_days(int advocate_successfull_days) {
		this.advocate_successfull_days = advocate_successfull_days;
	}

	public Timestamp getCreatedon() {
		return createdon;
	}

	public void setCreatedon(Timestamp createdon) {
		this.createdon = createdon;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public Timestamp getExpiry_date() {
		return expiry_date;
	}

	public void setExpiry_date(Timestamp expiry_date) {
		this.expiry_date = expiry_date;
	}

	public CreditType getReferral_credit_type() {
		return referral_credit_type;
	}

	public void setReferral_credit_type(CreditType referral_credit_type) {
		this.referral_credit_type = referral_credit_type;
	}

	public int getReferral_credit_value() {
		return referral_credit_value;
	}

	public void setReferral_credit_value(int referral_credit_value) {
		this.referral_credit_value = referral_credit_value;
	}

	public CreditType getAdvocate_credit_type() {
		return advocate_credit_type;
	}

	public void setAdvocate_credit_type(CreditType advocate_credit_type) {
		this.advocate_credit_type = advocate_credit_type;
	}

	public int getAdvocate_credit_value() {
		return advocate_credit_value;
	}

	public void setAdvocate_credit_value(int advocate_credit_value) {
		this.advocate_credit_value = advocate_credit_value;
	}

	public String getAdvocate_desc() {
		return advocate_desc;
	}

	public void setAdvocate_desc(String advocate_desc) {
		this.advocate_desc = advocate_desc;
	}

	public String getReferral_desc() {
		return referral_desc;
	}

	public void setReferral_desc(String referral_desc) {
		this.referral_desc = referral_desc;
	}
	
	
}
