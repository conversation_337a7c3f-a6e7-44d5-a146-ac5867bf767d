package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;


@Entity
@Table(name="plan_feature", uniqueConstraints =@UniqueConstraint (columnNames={"plan_id","feature_id" }) )

public class PlanToFeature  implements Serializable{
	private static final long serialVersionUID = 0l;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch=FetchType.EAGER)
	@JoinColumn(name="plan_id")
    private SubscriptionPlan plan_id;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch=FetchType.EAGER)
	@JoinColumn(name="feature_id")
	private Feature feature_id;
	
	@Column(name="txn_limit")
	private int txn_limit;
	
	@Column(name="extra_txn_limit")
	private int extra_txn_limit;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch=FetchType.EAGER)
	@JoinColumn(name="resettype_id")
	private ResetType resettype_id;
	
	@Column(name = "enable")
	private boolean enable = true;
	
//	@Column(name = "monitortype_id")
//	private String monitortype_id = "NA";
//	
//	@Column(name = "device_config")
//	private String device_config = "NA";
	
//	@Column(name = "plan_period_id")
//	private int plan_period_id;
	
	@Transient
	private long featureid;
	
	@Transient
	private long planid;
	
	@Transient
	private long resettype;
	
	@Column(name = "unlimited_cr")
	private boolean unlimited_cr = false;
	
	public PlanToFeature()
	{
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}
	
	public SubscriptionPlan getPlan_id() {
		return plan_id;
	}

	public void setPlan_id(SubscriptionPlan plan_id) {
		this.plan_id = plan_id;
	}

	public Feature getFeature_id() {
		return feature_id;
	}

	public void setFeature_id(Feature feature_id) {
		this.feature_id = feature_id;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public int getTxn_limit() {
		return txn_limit;
	}

	public void setTxn_limit(int txn_limit) {
		this.txn_limit = txn_limit;
	}

	public int getExtra_txn_limit() {
		return extra_txn_limit;
	}

	public void setExtra_txn_limit(int extra_txn_limit) {
		this.extra_txn_limit = extra_txn_limit;
	}	

	public ResetType getResettype_id() {
		return resettype_id;
	}

	public void setResettype_id(ResetType resettype_id) {
		this.resettype_id = resettype_id;
	}

	public long getFeatureid() {
		return featureid;
	}

	public void setFeatureid(long featureid) {
		this.featureid = featureid;
	}

	public long getPlanid() {
		return planid;
	}

	public void setPlanid(long planid) {
		this.planid = planid;
	}

//	public String getMonitortype_id() {
//		return monitortype_id;
//	}
//
//	public void setMonitortype_id(String monitortype_id) {
//		this.monitortype_id = monitortype_id;
//	}
//
//	public String getDevice_config() {
//		return device_config;
//	}
//
//	public void setDevice_config(String device_config) {
//		this.device_config = device_config;
//	}

	public long getResettype() {
		return resettype;
	}

	public void setResettype(long resettype) {
		this.resettype = resettype;
	}

//	public int getPlan_period_id() {
//		return plan_period_id;
//	}
//
//	public void setPlan_period_id(int plan_period_id) {
//		this.plan_period_id = plan_period_id;
//	}

	public boolean isUnlimited_cr() {
		return unlimited_cr;
	}

	public void setUnlimited_cr(boolean unlimited_cr) {
		this.unlimited_cr = unlimited_cr;
	}	
	
}
