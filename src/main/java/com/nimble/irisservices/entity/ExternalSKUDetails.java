package com.nimble.irisservices.entity;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "external_skudetail", uniqueConstraints = @UniqueConstraint(columnNames = "sku"))
public class ExternalSKUDetails implements java.io.Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private Long id;

	@Column(name = "sku")
	private String sku  ;
	
	@Column(name = "startdate")
	private Timestamp startdate;

	@Column(name = "enddate")
	private Timestamp enddate;

	@Column(name = "enable")
	private boolean enable;
	
	@Column(name="credit_type")
	private long credit_type ;
	
	@Column(name="credit_amt")
	private double credit_amt ;

	
	public ExternalSKUDetails() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getSku() {
		return sku;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public Timestamp getStartdate() {
		return startdate;
	}

	public void setStartdate(Timestamp startdate) {
		this.startdate = startdate;
	}

	public Timestamp getEnddate() {
		return enddate;
	}

	public void setEnddate(Timestamp enddate) {
		this.enddate = enddate;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public long getCredit_type() {
		return credit_type;
	}

	public void setCredit_type(long credit_type) {
		this.credit_type = credit_type;
	}

	public double getCredit_amt() {
		return credit_amt;
	}

	public void setCredit_amt(double credit_amt) {
		this.credit_amt = credit_amt;
	}	

}
