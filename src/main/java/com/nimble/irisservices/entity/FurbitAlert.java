package com.nimble.irisservices.entity;

import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;


@Entity 
@Table(name="furbitalert")
public class FurbitAlert {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@Column(name="startdatetime")
	private Timestamp startDateTime;
	@Column(name="enddatetime")
	private Timestamp endDateTime;
	@Column(name="timezone")
	private String timezone;
	@Column(name="alertvalue")
	private float alertValue;
	@Column(name="enable")
	private boolean enable;
	@Column(name="count")
	private int count;
	@Column(name="ack")
	private boolean ack;
	@Column(name="lat")
	private double lat;
	@Column(name="latDir")
	private String latdir;
	@Column(name="lon")
	private double lon;
	@Column(name="lonDir")
	private String londir;	
	@Column(name="address")
	private String address;
	@Column(name="gpsstatus")
	private String gpsstatus;
	@Column(name="battery")
	private int battery;
	@Column(name="rawrssi")
	private int rawrssi;
	@Column(name="rssi")
	private String rssi;
	@Column(name="motion")
	private String motion;
	@Column(name="externalsensor")
	private float extsensor;
	@Column(name="humidity")
	private float humidity;
	@Column(name="temperature")
	private float temperature;
	@Column(name="light")
	private float light;
	@Column(name="pressure")
	private float pressure;	
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="alerttype_id")
    private AlertType alerttype;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="alertcfg_id")
    private FurbitAlertCfg alertcfg;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="asset_id")
    private Asset asset;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="gateway_id")
    private Gateway gateway;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="cmp_id")
    private Company company;
	
	public long getId() {
		return id;
	}
	public Timestamp getStartDateTime() {
		return startDateTime;
	}
	public Timestamp getEndDateTime() {
		return endDateTime;
	}
	public String getTimezone() {
		return timezone;
	}
	public float getAlertValue() {
		return alertValue;
	}
	public boolean isEnable() {
		return enable;
	}
	public int getCount() {
		return count;
	}
	public AlertType getAlerttype() {
		return alerttype;
	}
	public FurbitAlertCfg getAlertcfg() {
		return alertcfg;
	}
	public Asset getAsset() {
		return asset;
	}
	public Gateway getGateway() {
		return gateway;
	}
	public Company getCompany() {
		return company;
	}
	public boolean getAck() {
		return ack;
	}
	
	public double getLat() {
		return lat;
	}
	public String getLatdir() {
		return latdir;
	}
	public double getLon() {
		return lon;
	}
	public String getLondir() {
		return londir;
	}
	public String getGpsstatus() {
		return gpsstatus;
	}
	public String getAddress() {
		return address;
	}
	public int getBattery() {
		return battery;
	}
	public int getRawrssi() {
		return rawrssi;
	}
	public String getRssi() {
		return rssi;
	}
	public String getMotion() {
		return motion;
	}
	public float getExtsensor() {
		return extsensor;
	}
	public float getHumidity() {
		return humidity;
	}
	public float getTemperature() {
		return temperature;
	}
	public float getLight() {
		return light;
	}
	public float getPressure() {
		return pressure;
	}	
}
