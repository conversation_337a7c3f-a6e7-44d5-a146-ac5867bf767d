package com.nimble.irisservices.entity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

@Entity(name="freshchat")
public class FreshChat {

	@Id  
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private long id;
	
	String freshchat_id = "NA";

	long user_id;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getFreshchat_id() {
		return freshchat_id;
	}

	public void setFreshchat_id(String freshchat_id) {
		this.freshchat_id = freshchat_id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	
	public FreshChat() {
		super();
	}
	
	public FreshChat(long id, String freshchat_id, long user_id) {
		super();
		this.id = id;
		this.freshchat_id = freshchat_id;
		this.user_id = user_id;
	}
	
	
	
	
}
