package com.nimble.irisservices.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity 
@Table(name="user_timezone")
public class UserTimeZone {
	
	@Id
	private long user_id = 0;

	private String timezone = "+00:00";

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}

	public UserTimeZone() {
		super();
	}
	
	public UserTimeZone(long user_id, String timezone) {
		super();
		this.user_id = user_id;
		this.timezone = timezone;
	}
	
	
	
}
