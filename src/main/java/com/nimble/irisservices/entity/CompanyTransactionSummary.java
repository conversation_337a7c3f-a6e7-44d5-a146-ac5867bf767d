package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.sql.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="cmp_txn_smry", uniqueConstraints =@UniqueConstraint (columnNames={"date","cmp_id","gateway_id","feature_id"} ))
public class CompanyTransactionSummary implements Serializable {

	private  static final long serialVersionUID = 0l;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="cmp_id")
    private Company cmp_id;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="gateway_id")
    private Gateway gateway_id;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="feature_id")
    private Feature feature_id;
			
	@Column(name="date")
	private Date date;
	
	@Column(name="no_txn")
	private int no_txn;
	
	public CompanyTransactionSummary()
	{
		super();
	}
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}
	
	public Company getCmp_id() {
		return cmp_id;
	}

	public void setCmp_id(Company cmp_id) {
		this.cmp_id = cmp_id;
	}

	public Gateway getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(Gateway gateway_id) {
		this.gateway_id = gateway_id;
	}

	public Feature getFeature_id() {
		return feature_id;
	}

	public void setFeature_id(Feature feature_id) {
		this.feature_id = feature_id;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public int getNo_txn() {
		return no_txn;
	}

	public void setNo_txn(int no_txn) {
		this.no_txn = no_txn;
	}

}
