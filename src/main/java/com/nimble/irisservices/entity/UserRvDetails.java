package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "user_rvdetails")
public class UserRvDetails implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "user_id")
	private long user_id;

	@Column(name = "own_rv")
	private long own_rv;

	@Column(name = "rvtype")
	private long rvtype ;

	@Column(name = "with_pet")
	private long withPet ;

	@Column(name = "how_often")
	private long how_often ;

	@Column(name = "enable")
	private boolean enable = true;

	@Column(name = "createdon")
	private String createdon; // date

	@Column(name = "updatedon")
	private String updatedon; // date
	
	@Column(name="others_type")
	private String others_type;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}	

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public long getOwn_rv() {
		return own_rv;
	}

	public void setOwn_rv(long own_rv) {
		this.own_rv = own_rv;
	}

	public long getRvtype() {
		return rvtype;
	}

	public void setRvtype(long rvtype) {
		this.rvtype = rvtype;
	}

	public long getWithPet() {
		return withPet;
	}

	public void setWithPet(long withPet) {
		this.withPet = withPet;
	}

	public long getHow_often() {
		return how_often;
	}

	public void setHow_often(long how_often) {
		this.how_often = how_often;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}	

	public String getUpdatedon() {
		return updatedon;
	}

	public void setUpdatedon(String updatedon) {
		this.updatedon = updatedon;
	}
	
	public String getCreatedon() {
		return createdon;
	}

	public void setCreatedon(String createdon) {
		this.createdon = createdon;
	}

	public String getOthers_type() {
		return others_type;
	}

	public void setOthers_type(String others_type) {
		this.others_type = others_type;
	}

}
