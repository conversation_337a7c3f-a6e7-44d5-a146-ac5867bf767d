package com.nimble.irisservices.entity;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

/**
 * Gatewayconfig generated by hbm2java
 */
@Entity
@Table(name = "gatewayconfig", uniqueConstraints = @UniqueConstraint(columnNames={"id","gateway_id"}))
public class Gatewayconfig implements java.io.Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "increment")
	@GeneratedValue(generator = "gen")
	private Long id;

	@OneToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "gateway_id")
	private Gateway gateway;

	@Column(name = "livetrack_enable")
	private Boolean livetrackEnable;
	
	//Live Track OTA call is 2 bit configuration to represent how to send data call to device when enabling and disabling the live tracking
	//"01"  -- First bit while enabling the live track . Second bit while disabling the live tracking
//	@Column(name = "livetrackotacall")
//	private String livetrackotacall;

	public Gatewayconfig() {
	}

	public Gatewayconfig(Gateway gateway) {
		this.gateway = gateway;
	}

	public Gatewayconfig(Gateway gateway, Boolean livetrackEnable) {
		this.gateway = gateway;
		this.livetrackEnable = livetrackEnable;
//		this.livetrackotacall = livetrackotacall;
	}

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Gateway getGateway() {
		return this.gateway;
	}

	public void setGateway(Gateway gateway) {
		this.gateway = gateway;
	}

	public Boolean getLivetrackEnable() {
		return this.livetrackEnable;
	}

	public void setLivetrackEnable(Boolean livetrackEnable) {
		this.livetrackEnable = livetrackEnable;
	}

//	public String getLivetrackotacall() {
//		return livetrackotacall;
//	}
//
//	public void setLivetrackotacall(String livetrackotacall) {
//		this.livetrackotacall = livetrackotacall;
//	}
	
	
	

}
