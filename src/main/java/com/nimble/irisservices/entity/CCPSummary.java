package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="ccp_summary",uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class CCPSummary implements Serializable{ 

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="template_id")
    private CCPTemplateConfig ccpTemplateConfig;
	
	@Column(name="start_time")
	private String start_time;
	
	@Column(name="end_time")
	private String end_time;
	
	@Column(name="start_date")
	private Date start_date;
	
	@Column(name="end_date")
	private Date end_date;
	
	@Column(name="actual_start_date_time")
	private Timestamp actual_start_date_time;
	
	@Column(name="actual_end_date_time")
	private Timestamp actual_end_date_time;
	
	@Column(name="status")
	private int status;
	//1- Scheduled
	//2- Started
	//3- Completed
	//4-Not completed
	
	@Column(name="no_of_violations")
	private Integer no_of_violations;		
	
	@Column(name="no_of_questions_chckd")
	private Integer no_of_questions_chckd;
	
	@Column(name="email_sent")
	private int email_sent = 0;
	
	public CCPSummary() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	public CCPSummary(long id,CCPTemplateConfig ccpTemplateConfig,String start_time,String end_time,
			int status,Integer no_of_violations,Integer no_of_questions_checked,
			Date startDate,Date endDate,Timestamp actualStartDateTime,Timestamp actualEndDateTime){
		super();
		this.setId(id);
		this.setCcpTemplateConfig(ccpTemplateConfig);
		this.setStart_time(start_time);
		this.setEnd_time(end_time);
		this.setStatus(status);
		this.setNo_of_violations(no_of_violations);			
		this.setNo_of_questions_chckd(no_of_questions_checked);
		start_date = startDate;
		end_date   = endDate;
		actual_start_date_time = actualStartDateTime;
		actual_end_date_time   = actualEndDateTime;
		
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public CCPTemplateConfig getCcpTemplateConfig() {
		return ccpTemplateConfig;
	}

	public void setCcpTemplateConfig(CCPTemplateConfig ccpTemplateConfig) {
		this.ccpTemplateConfig = ccpTemplateConfig;
	}

	public String getStart_time() {
		return start_time;
	}

	public void setStart_time(String start_time) {
		this.start_time = start_time;
	}

	public String getEnd_time() {
		return end_time;
	}

	public void setEnd_time(String end_time) {
		this.end_time = end_time;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public Integer getNo_of_violations() {
		return no_of_violations;
	}

	public void setNo_of_violations(Integer no_of_violations) {
		this.no_of_violations = no_of_violations;
	}


	public Integer getNo_of_questions_chckd() {
		return no_of_questions_chckd;
	}

	public void setNo_of_questions_chckd(Integer no_of_questions_chckd) {
		this.no_of_questions_chckd = no_of_questions_chckd;
	}

	public Date getStart_date() {
		return start_date;
	}

	public void setStart_date(Date start_date) {
		this.start_date = start_date;
	}

	public Date getEnd_date() {
		return end_date;
	}

	public void setEnd_date(Date end_date) {
		this.end_date = end_date;
	}

	public Timestamp getActual_start_date_time() {
		return actual_start_date_time;
	}

	public void setActual_start_date_time(Timestamp actual_start_date_time) {
		this.actual_start_date_time = actual_start_date_time;
	}

	public Timestamp getActual_end_date_time() {
		return actual_end_date_time;
	}

	public void setActual_end_date_time(Timestamp actual_end_date_time) {
		this.actual_end_date_time = actual_end_date_time;
	}

	public int getEmail_sent() {
		return email_sent;
	}

	public void setEmail_sent(int email_sent) {
		this.email_sent = email_sent;
	}



	
	
}
