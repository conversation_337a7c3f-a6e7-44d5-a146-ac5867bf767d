package com.nimble.irisservices.entity;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "ordermappingdetails")
public class OrderMappingDetails implements java.io.Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private Long id;

	@Column(name = "name")
	private String name;

	@Column(name = "email")
	private String email;

	@Column(name = "address")
	private String address;

	@Column(name = "devicestate_id")
	private String devicestateId;

	@Column(name = "qrccode")
	private String qrccode;

	@Column(name = "companytype")
	private String companytype;
	
	@Column(name = "status")
	private String status;

	@Column(name = "orderid")
	private String orderid;
	
	@Column(name = "orderchannel")
	private String orderchannel;
	
	//@OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	//@JoinColumn(name="user_id")
    private long user_id;
	
	@Column(name = "createdon")
	private Timestamp createdon ;
	
	@Column(name = "orderdate")
	private Timestamp orderdate ;
	
	@Column(name="externalsku")
	private String externalsku;
	
	@Column(name = "offer_applied")
	private boolean offer_applied;

	
	public OrderMappingDetails() {
	}

	public OrderMappingDetails(String name, String email,  String address,String devicestateId, String qrccode, String companytype,String status,
			String orderid, String orderchannel, long user_id, Timestamp createdon, Timestamp orderdate, String externalsku ) {
		this.name = name;
		this.email = email;
		this.devicestateId = devicestateId;
		this.qrccode = qrccode;
		this.companytype = companytype;
		this.status=status;
		this.orderid = orderid;
		this.orderchannel = orderchannel;
		this.user_id = user_id;
		this.createdon = createdon;
		this.orderdate = orderdate;
		this.externalsku = externalsku;
	}

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getAddress() {
		return this.address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getDevicestateId() {
		return this.devicestateId;
	}

	public void setDevicestateId(String devicestateId) {
		this.devicestateId = devicestateId;
	}

	public String getQrccode() {
		return this.qrccode;
	}

	public void setQrccode(String qrccode) {
		this.qrccode = qrccode;
	}

	public String getCompanytype() {
		return this.companytype;
	}

	public void setCompanytype(String companytype) {
		this.companytype = companytype;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getOrderid() {
		return orderid;
	}

	public void setOrderid(String orderid) {
		this.orderid = orderid;
	}

	public String getOrderchannel() {
		return orderchannel;
	}

	public void setOrderchannel(String orderchannel) {
		this.orderchannel = orderchannel;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public Timestamp getCreatedon() {
		return createdon;
	}

	public void setCreatedon(Timestamp createdon) {
		this.createdon = createdon;
	}

	public String getExternalsku() {
		return externalsku;
	}

	public void setExternalsku(String externalsku) {
		this.externalsku = externalsku;
	}

	public boolean isOffer_applied() {
		return offer_applied;
	}

	public void setOffer_applied(boolean offer_applied) {
		this.offer_applied = offer_applied;
	}

	public Timestamp getOrderdate() {
		return orderdate;
	}

	public void setOrderdate(Timestamp orderdate) {
		this.orderdate = orderdate;
	}	
}
