package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "night_vision_mode")
public class NightVisionMode {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	private String title = "NA";
	
	private String discription = "NA";
	
	private long monitor_type = 5;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDiscription() {
		return discription;
	}

	public void setDiscription(String discription) {
		this.discription = discription;
	}

	public NightVisionMode() {
		super();
	}
	
	public NightVisionMode(long id, String title, String discription, long monitor_type) {
		super();
		this.id = id;
		this.title = title;
		this.discription = discription;
		this.monitor_type = monitor_type;
	}

	public long getMonitor_type() {
		return monitor_type;
	}

	public void setMonitor_type(long monitor_type) {
		this.monitor_type = monitor_type;
	}

	
}
