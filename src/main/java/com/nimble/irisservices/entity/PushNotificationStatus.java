package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "pushnotificationstatus")
public class PushNotificationStatus implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;

	@Column(name = "pushnotificationid")
	private String pushNotificationId;

	@Column(name = "userid")
	private String userId;

	@Column(name = "senddate")
	private String sendDate;

	@Column(name = "status")
	private String status;

	@Column(name="viewed")
	private boolean viewed=false;
	
	@Column(name="short_description")
	private String short_description="NA";
	
	@Column(name = "gateway_id")
	private long gateway_id=0;
	
	@Column(name="notify_img")
	private String notify_img="NA";
	
	@Column(name="notify_vid")
	private String notify_vid="NA";

	
	public PushNotificationStatus() {
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getPushNotificationId() {
		return pushNotificationId;
	}

	public void setPushNotificationId(String pushNotificationId) {
		this.pushNotificationId = pushNotificationId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getSendDate() {
		return sendDate;
	}

	public void setSendDate(String sendDate) {
		this.sendDate = sendDate;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public boolean isViewed() {
		return viewed;
	}

	public void setViewed(boolean viewed) {
		this.viewed = viewed;
	}

	public String getShort_description() {
		return short_description;
	}

	public void setShort_description(String short_description) {
		this.short_description = short_description;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public String getNotify_img() {
		return notify_img;
	}

	public void setNotify_img(String notify_img) {
		this.notify_img = notify_img;
	}

	public String getNotify_vid() {
		return notify_vid;
	}

	public void setNotify_vid(String notify_vid) {
		this.notify_vid = notify_vid;
	}

	public PushNotificationStatus(long id, String pushNotificationId, String userId, String sendDate, String status) {
		super();
		this.id = id;
		this.pushNotificationId = pushNotificationId;
		this.userId = userId;
		this.sendDate = sendDate;
		this.status = status;
	}

	public PushNotificationStatus(String pushNotificationId, String userId, String sendDate, String status,
			String short_description,long gateway_id) {
		super();
		this.pushNotificationId = pushNotificationId;
		this.userId = userId;
		this.sendDate = sendDate;
		this.status = status;
		this.short_description = short_description;
		this.gateway_id = gateway_id;
	}

	
	

}
