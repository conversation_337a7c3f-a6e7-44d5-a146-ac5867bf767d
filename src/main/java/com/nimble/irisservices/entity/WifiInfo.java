package com.nimble.irisservices.entity;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "wifi_info")
public class WifiInfo {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "gatewayid")
	private long gatewayid;

	@Column(name = "userid")
	private long userid;

	@Column(name = "ssid")
	private String ssid;

	@Column(name = "password")
	private String password;

	@Column(name = "status")
	private boolean status;

	@Column(name = "createdon")
	private Timestamp createdOn;

	@Column(name = "updatedon")
	private Timestamp updatedOn;

	// temp variable - not stored in DB
	@Transient
	private String meid;

	@Column(name = "ssid_category")
	private String ssidCategory = "SSID_1";

	@Column(name = "lat")
	private double lat = 270.00000000;

	@Column(name = "lon")
	private double lon = 270.00000000;

	@Column(name = "ble_version")
	private String bleVersion = "v3";

	@Column(name = "ssid_name")
	private String ssidName = "Home";

	@Column(name = "address")
	private String address = "";

	@Column(name = "timezone")
	private String timezone = "+0:00";

	@Column(name = "ip_type")
	private String ip_type = "dynamic";

	@Column(name = "staticip")
	private String staticip = "0.0.0.0";

	@Column(name = "subnet")
	private String subnet = "0.0.0.0";

	@Column(name = "default_gateway")
	private String default_gateway = "0.0.0.0";

	@Column(name = "dns")
	private String dns = "0.0.0.0";

	@Column(name = "rssi")
	private int rssi = 0;

	@Column(name = "nearby")
	private boolean nearby = true;

	@Column(name = "dev_resp_code")
	private int dev_resp_code = 0;

	@Column(name = "manual_wifi")
	private boolean manual_wifi = false;

	@Column(name = "user_wifi_status")
	private boolean user_wifi_status = true;

	@Column(name = "security")
	private String security = "NA";

	@Column(name = "wifi_channel")
	private int wifi_channel = 0;
	
	private String bandwidth = "NA";

	public WifiInfo(long id, long gatewayid, long userid, String ssid, String password, boolean status,
			Timestamp createdOn, Timestamp updatedOn, String meid, String ssidCategory, double lat, double lon,
			String bleVersion, String ssidName, String address, String timezone, String ip_type, String staticip,
			String subnet, String default_gateway, String dns, int rssi, boolean nearby, int dev_resp_code,
			boolean manual_wifi) {
		super();
		this.id = id;
		this.gatewayid = gatewayid;
		this.userid = userid;
		this.ssid = ssid;
		this.password = password;
		this.status = status;
		this.createdOn = createdOn;
		this.updatedOn = updatedOn;
		this.meid = meid;
		this.ssidCategory = ssidCategory;
		this.lat = lat;
		this.lon = lon;
		this.bleVersion = bleVersion;
		this.ssidName = ssidName;
		this.address = address;
		this.timezone = timezone;
		this.ip_type = ip_type;
		this.staticip = staticip;
		this.subnet = subnet;
		this.default_gateway = default_gateway;
		this.dns = dns;
		this.rssi = rssi;
		this.nearby = nearby;
		this.dev_resp_code = dev_resp_code;
		this.manual_wifi = manual_wifi;
	}

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getSsidName() {
		return ssidName;
	}

	public void setSsidName(String ssidName) {
		this.ssidName = ssidName;
	}

	public String getSsidCategory() {
		return ssidCategory;
	}

	public void setSsidCategory(String ssidCategory) {
		this.ssidCategory = ssidCategory;
	}

	public String getBleVersion() {
		return bleVersion;
	}

	public void setBleVersion(String bleVersion) {
		this.bleVersion = bleVersion;
	}

	public double getLat() {
		return lat;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public double getLon() {
		return lon;
	}

	public void setLon(double lon) {
		this.lon = lon;
	}

	public WifiInfo() {
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getGatewayid() {
		return gatewayid;
	}

	public void setGatewayid(long gatewayid) {
		this.gatewayid = gatewayid;
	}

	public long getUserid() {
		return userid;
	}

	public void setUserid(long userid) {
		this.userid = userid;
	}

	public String getSsid() {
		return ssid;
	}

	public void setSsid(String ssid) {
		this.ssid = ssid;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public boolean isStatus() {
		return status;
	}

	public void setStatus(boolean status) {
		this.status = status;
	}

	public Timestamp getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(Timestamp createdOn) {
		this.createdOn = createdOn;
	}

	public Timestamp getUpdatedOn() {
		return updatedOn;
	}

	public void setUpdatedOn(Timestamp updatedOn) {
		this.updatedOn = updatedOn;
	}

	public String getIp_type() {
		return ip_type;
	}

	public void setIp_type(String ip_type) {
		this.ip_type = ip_type;
	}

	public String getStaticip() {
		return staticip;
	}

	public void setStaticip(String staticip) {
		this.staticip = staticip;
	}

	public String getSubnet() {
		return subnet;
	}

	public void setSubnet(String subnet) {
		this.subnet = subnet;
	}

	public String getDefault_gateway() {
		return default_gateway;
	}

	public void setDefault_gateway(String default_gateway) {
		this.default_gateway = default_gateway;
	}

	public String getDns() {
		return dns;
	}

	public void setDns(String dns) {
		this.dns = dns;
	}

	public int getRssi() {
		return rssi;
	}

	public void setRssi(int rssi) {
		this.rssi = rssi;
	}

	public boolean isNearby() {
		return nearby;
	}

	public void setNearby(boolean nearby) {
		this.nearby = nearby;
	}

	public int getDev_resp_code() {
		return dev_resp_code;
	}

	public void setDev_resp_code(int dev_resp_code) {
		this.dev_resp_code = dev_resp_code;
	}

	public boolean isManual_wifi() {
		return manual_wifi;
	}

	public void setManual_wifi(boolean manual_wifi) {
		this.manual_wifi = manual_wifi;
	}

	public boolean isUser_wifi_status() {
		return user_wifi_status;
	}

	public void setUser_wifi_status(boolean user_wifi_status) {
		this.user_wifi_status = user_wifi_status;
	}

	public String getSecurity() {
		return security;
	}

	public void setSecurity(String security) {
		this.security = security;
	}

	public int getWifi_channel() {
		return wifi_channel;
	}

	public void setWifi_channel(int wifi_channel) {
		this.wifi_channel = wifi_channel;
	}

	public String getBandwidth() {
		return bandwidth;
	}

	public void setBandwidth(String bandwidth) {
		this.bandwidth = bandwidth;
	}

	
	
}
