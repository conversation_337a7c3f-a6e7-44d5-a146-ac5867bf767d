package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="reverselookup", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class ReverseLookup implements Serializable {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	@Column(name="min")
	private float min;
	@Column(name="max")
	private float max;
	@Column(name="formula")
	private String formula;
	
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="model_id")
	//@JsonBackReference
	private AssetModel assetmodel;

	public ReverseLookup() {
		super();
	}
	
	public ReverseLookup(long id, float min, float max, String formula,
			AssetModel assetmodel) {
		super();
		this.id = id;
		this.min = min;
		this.max = max;
		this.formula = formula;
		this.assetmodel = assetmodel;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public float getMin() {
		return min;
	}

	public void setMin(float min) {
		this.min = min;
	}

	public float getMax() {
		return max;
	}

	public void setMax(float max) {
		this.max = max;
	}

	public String getFormula() {
		return formula;
	}

	public void setFormula(String formula) {
		this.formula = formula;
	}

	public AssetModel getAssetModel() {
		return assetmodel;
	}

	public void setAssetModel(AssetModel assetmodel) {
		this.assetmodel = assetmodel;
	}

}
