package com.nimble.irisservices.entity;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;


@Entity 
@Table(name="rv_petsafety", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class RVPetSafety {

	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@Column(name="blog_url")
	private String blog_url;
	
	/*@Column(name="redirect_url")
	private String redirect_url;*/
	
	@Column(name="image_url")
	private String image_url;
	
	@Column(name="title")
	private String title;
	
	public RVPetSafety(){
		super();
	}
	
	public RVPetSafety(long id,String blog_url,String image_url,String title){
		this.id=id;
		this.blog_url=blog_url;
		this.image_url=image_url;
		this.title=title;
		//this.setRedirect_url(redirect_url);
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getBlog_url() {
		return blog_url;
	}

	public void setBlog_url(String blog_url) {
		this.blog_url = blog_url;
	}

	public String getImage_url() {
		return image_url;
	}

	public void setImage_url(String image_url) {
		this.image_url = image_url;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	/*public String getRedirect_url() {
		return redirect_url;
	}

	public void setRedirect_url(String redirect_url) {
		this.redirect_url = redirect_url;
	}*/
	
	
}
