package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="registeruseremailstatus", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class RegisterUserEmailStatus  implements Serializable{
	


	public RegisterUserEmailStatus() {
		super();
		// TODO Auto-generated constructor stub
	}

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	
	@Column(name="qrcode")
	private String qrCode;
	@Column(name="errorcode")
	private String errorCode;
	
	@Column(name="count")
	private long count;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getQrCode() {
		return qrCode;
	}

	public void setQrCode(String qrCode) {
		this.qrCode = qrCode;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public long getCount() {
		return count;
	}

	public void setCount(long count) {
		this.count = count;
	}

	public RegisterUserEmailStatus(long id, String qrCode, String errorCode, long count) {
		super();
		this.id = id;
		this.qrCode = qrCode;
		this.errorCode = errorCode;
		this.count = count;
	}
	
	public RegisterUserEmailStatus(String qrCode, String errorCode, long count) {
		super();
		this.qrCode = qrCode;
		this.errorCode = errorCode;
		this.count = count;
	}
}
