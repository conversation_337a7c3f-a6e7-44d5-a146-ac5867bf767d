package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="recharge_cb_webhook_status")
public class ReCBWebhookStatus implements Serializable {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@Column(name="cb_id")
	private String cb_id;
	
	@Column(name="cb_subid")
	private String cb_subid;
	
	@Column(name="re_subid")
	private String re_subid;
	
	@Column(name="event_status")
	private String event_status;
	
	@Column(name="created_at")
	private String created_at;
	
	public ReCBWebhookStatus() {
		
	}
	
	public ReCBWebhookStatus(String cb_id, String cb_subid, String re_subid, String event_status) {
		super();
		this.cb_id = cb_id;
		this.cb_subid = cb_subid;
		this.re_subid = re_subid;
		this.event_status = event_status;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getCb_id() {
		return cb_id;
	}

	public void setCb_id(String cb_id) {
		this.cb_id = cb_id;
	}

	public String getCb_subid() {
		return cb_subid;
	}

	public void setCb_subid(String cb_subid) {
		this.cb_subid = cb_subid;
	}

	public String getRe_subid() {
		return re_subid;
	}

	public void setRe_subid(String re_subid) {
		this.re_subid = re_subid;
	}

	public String getEvent_status() {
		return event_status;
	}

	public void setEvent_status(String event_status) {
		this.event_status = event_status;
	}

	public String getCreated_at() {
		return created_at;
	}

	public void setCreated_at(String created_at) {
		this.created_at = created_at;
	}	
}