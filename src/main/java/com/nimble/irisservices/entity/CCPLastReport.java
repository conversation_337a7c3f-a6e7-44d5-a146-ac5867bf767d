package com.nimble.irisservices.entity;

import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="ccp_lastreport", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class CCPLastReport {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="template_id")
    private CCPTemplateConfig ccpTemplateConfig;
	
	@Column(name="datetime")
	private Timestamp datetime;
	
	@Column(name="value")
	private String value;
	
	@Column(name="temp_value")
	private String temp_value;
	
	@Column(name="description")
	private String description;
	
	@Column(name="corrective_action")
	private String corrective_action;

	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="user_id")
    private User user;

	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="type_id")
	//@JsonBackReference
    private CCPType type;
	
	@Column(name="min")
	private Long min ;
	
	@Column(name="max")
	private Long max ;
	
	@Column(name="ccp_id")
	private String ccp_id;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="monitor_id")
    private F5Monitor monitor;
	
	@Column(name="category")
	private String category;
	
	@Column(name="comments")
	private String comments;
	
	@Column(name="image_url")
	private String image_url;
	
	public CCPLastReport() {
		super();
		// TODO Auto-generated constructor stub
	}

	public CCPLastReport(long id,Timestamp datetime,String description, String corrective_action,  String value,  String temp_value, 
							User user, CCPType type,Long min,Long max,String ccp_id, F5Monitor monitor,
							CCPTemplateConfig ccpTemplateConfig,String category,String comments,String image_url) {
		super();
		this.id = id;
		this.datetime = datetime;
		this.value = value;
		this.description = description;
		this.corrective_action = corrective_action;
		this.user = user;
		this.type = type;
		this.min = min;
		this.max = max;
		this.temp_value = temp_value;
		this.ccp_id = ccp_id;
		this.monitor = monitor;
		this.ccpTemplateConfig = ccpTemplateConfig;
		this.setCategory(category);
		this.setComments(comments);
		this.setImage_url(image_url);
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Timestamp getDatetime() {
		return datetime;
	}

	public void setDatetime(Timestamp datetime) {
		this.datetime = datetime;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getTemp_value() {
		return temp_value;
	}

	public void setTemp_value(String temp_value) {
		this.temp_value = temp_value;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getCorrective_action() {
		return corrective_action;
	}

	public void setCorrective_action(String corrective_action) {
		this.corrective_action = corrective_action;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public CCPType getType() {
		return type;
	}

	public void setType(CCPType type) {
		this.type = type;
	}

	public Long getMin() {
		return min;
	}

	public void setMin(Long min) {
		this.min = min;
	}

	public Long getMax() {
		return max;
	}

	public void setMax(Long max) {
		this.max = max;
	}

	public String getCcp_id() {
		return ccp_id;
	}

	public void setCcp_id(String ccp_id) {
		this.ccp_id = ccp_id;
	}

	public F5Monitor getMonitor() {
		return monitor;
	}

	public void setMonitor(F5Monitor monitor) {
		this.monitor = monitor;
	}

	public CCPTemplateConfig getCcpTemplateConfig() {
		return ccpTemplateConfig;
	}

	public void setCcpTemplateConfig(CCPTemplateConfig ccpTemplateConfig) {
		this.ccpTemplateConfig = ccpTemplateConfig;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getImage_url() {
		return image_url;
	}

	public void setImage_url(String image_url) {
		this.image_url = image_url;
	}
	
}
