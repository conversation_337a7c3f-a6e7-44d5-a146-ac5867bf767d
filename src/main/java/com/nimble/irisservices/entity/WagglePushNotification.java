package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "waggle_pushnotification")
public class WagglePushNotification implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "userId")
	private long userId;
	@Column(name = "reminderId")
	private long reminderId;

	@Column(name = "usertokenId")
	private long usertokenId;

	@Column(name = "notification_datetime")
	private String notification_datetime;

	@Column(name = "notificationType")
	private String notificationType;

	@Column(name = "os")
	private String os;

	@Column(name = "status")
	private String status;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public long getReminderId() {
		return reminderId;
	}

	public void setReminderId(long reminderId) {
		this.reminderId = reminderId;
	}

	public long getUsertokenId() {
		return usertokenId;
	}

	public void setUsertokenId(long usertokenId) {
		this.usertokenId = usertokenId;
	}

	public String getNotification_datetime() {
		return notification_datetime;
	}

	public void setNotification_datetime(String notification_datetime) {
		this.notification_datetime = notification_datetime;
	}

	public String getNotificationType() {
		return notificationType;
	}

	public void setNotificationType(String notificationType) {
		this.notificationType = notificationType;
	}

	public String getOs() {
		return os;
	}

	public void setOs(String os) {
		this.os = os;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}
