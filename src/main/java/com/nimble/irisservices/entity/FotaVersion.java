package com.nimble.irisservices.entity;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "fota_version_mapping")
public class FotaVersion {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	public long id = 0;

	@Column(name = "fota_version")
	public String fota_version = "NA";

	@Column(name = "filename")
	public String filename = "NA";

	@Column(name = "filesize")
	public long filesize = 0;

	@Column(name = "curr_version")
	public String curr_version = "NA";

	@Column(name = "enable")
	public int enable = -1;

	@Column(name = "createdon")
	public String createdon = "NA";

	@Transient
	public long curr_modelid = 0;

	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "curr_modelid")
	public AssetModel assetmodel;

	@Column(name = "is_bt_fota")
	private boolean showBtUpdate = false;

	public FotaVersion() {
		super();
	}

	public FotaVersion(long id, String fota_version, String filename, long filesize, String curr_version, int enable,
			String createdon, long curr_modelid, AssetModel assetmodel) {
		super();
		this.id = id;
		this.fota_version = fota_version;
		this.filename = filename;
		this.filesize = filesize;
		this.curr_version = curr_version;
		this.enable = enable;
		this.createdon = createdon;
		this.curr_modelid = curr_modelid;
		this.assetmodel = assetmodel;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getFota_version() {
		return fota_version;
	}

	public void setFota_version(String fota_version) {
		this.fota_version = fota_version;
	}

	public String getFilename() {
		return filename;
	}

	public void setFilename(String filename) {
		this.filename = filename;
	}

	public long getFilesize() {
		return filesize;
	}

	public void setFilesize(long filesize) {
		this.filesize = filesize;
	}

	public String getCurr_version() {
		return curr_version;
	}

	public void setCurr_version(String curr_version) {
		this.curr_version = curr_version;
	}

	public int getEnable() {
		return enable;
	}

	public void setEnable(int enable) {
		this.enable = enable;
	}

	public String getCreatedon() {
		return createdon;
	}

	public void setCreatedon(String createdon) {
		this.createdon = createdon;
	}

	public long getCurr_modelid() {
		return curr_modelid;
	}

	public void setCurr_modelid(long curr_modelid) {
		this.curr_modelid = curr_modelid;
	}

	public AssetModel getAssetmodel() {
		return assetmodel;
	}

	public void setAssetmodel(AssetModel assetmodel) {
		this.assetmodel = assetmodel;
	}

	public boolean isShowBtUpdate() {
		return showBtUpdate;
	}

	public void setShowBtUpdate(boolean showBtUpdate) {
		this.showBtUpdate = showBtUpdate;
	}
}