package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;


@Entity 
@Table(name="assetinformation", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class AssetInformation  implements Serializable {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	
	@Column(name ="field_1")
	private String field_1;

	@Column(name ="field_2")
	private String field_2;

	@Column(name ="field_3")
	private String field_3;

	@Column(name ="field_4")
	private String field_4;
	
	@Column(name ="field_5")
	private String field_5;
	
	@Column(name ="field_6")
	private String field_6;
	
	@Column(name ="field_7")
	private String field_7;
	
	@Column(name ="field_8")
	private String field_8;
	
	@Column(name ="field_9")
	private String field_9;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getField_1() {
		return field_1;
	}

	public void setField_1(String field_1) {
		this.field_1 = field_1;
	}

	public String getField_2() {
		return field_2;
	}

	public void setField_2(String field_2) {
		this.field_2 = field_2;
	}

	public String getField_3() {
		return field_3;
	}

	public void setField_3(String field_3) {
		this.field_3 = field_3;
	}

	public String getField_4() {
		return field_4;
	}

	public void setField_4(String field_4) {
		this.field_4 = field_4;
	}

	public String getField_5() {
		return field_5;
	}

	public void setField_5(String field_5) {
		this.field_5 = field_5;
	}

	public String getField_6() {
		return field_6;
	}

	public void setField_6(String field_6) {
		this.field_6 = field_6;
	}

	public String getField_7() {
		return field_7;
	}

	public void setField_7(String field_7) {
		this.field_7 = field_7;
	}

	public String getField_8() {
		return field_8;
	}

	public void setField_8(String field_8) {
		this.field_8 = field_8;
	}

	public String getField_9() {
		return field_9;
	}

	public void setField_9(String field_9) {
		this.field_9 = field_9;
	}
	
	

}
