package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;


@Entity
@Table(name = "referral_detail", uniqueConstraints = @UniqueConstraint(columnNames = { "referral_user_id","referral_subid" }))
public class ReferralDetail implements Serializable {


	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@Column(name="createdon")
	private Timestamp createdon;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="referral_user_id")
    private User referral_user_id;
		
	@Column(name="referral_subid")
	private String referral_subid;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="advocate_user_id")
    private User advocate_user_id;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="credit_id")
    private ReferralCredits credit_id;

	@Column(name="subs_amt")
	private double subs_amt;
	
	@Column(name="referral_credit_is_applied")
	private boolean referral_credit_is_applied;

	@Column(name="advocate_credit_is_applied")
	private boolean advocate_credit_is_applied;


	public ReferralDetail() {
		super();
	}
	
	public ReferralDetail( User referral_user_id,Timestamp createdon, String referral_subid, User advocate_user_id,
			double subs_amt,boolean referral_credit_is_applied,boolean advocate_credit_is_applied) {
		this.createdon = createdon;
		this.referral_user_id = referral_user_id;
		this.referral_subid = referral_subid;
		this.advocate_user_id = advocate_user_id;
		this.subs_amt = subs_amt;
		this.referral_credit_is_applied = referral_credit_is_applied;
		this.advocate_credit_is_applied = advocate_credit_is_applied;		
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Timestamp getCreatedon() {
		return createdon;
	}

	public void setCreatedon(Timestamp createdon) {
		this.createdon = createdon;
	}

	public User getReferral_user_id() {
		return referral_user_id;
	}

	public void setReferral_user_id(User referral_user_id) {
		this.referral_user_id = referral_user_id;
	}

	public String getReferral_subid() {
		return referral_subid;
	}

	public void setReferral_subid(String referral_subid) {
		this.referral_subid = referral_subid;
	}

	public User getAdvocate_user_id() {
		return advocate_user_id;
	}

	public void setAdvocate_user_id(User advocate_user_id) {
		this.advocate_user_id = advocate_user_id;
	}

	public ReferralCredits getCredit_id() {
		return credit_id;
	}

	public void setCredit_id(ReferralCredits credit_id) {
		this.credit_id = credit_id;
	}

	public double getSubs_amt() {
		return subs_amt;
	}

	public void setSubs_amt(double subs_amt) {
		this.subs_amt = subs_amt;
	}

	public boolean isReferral_credit_is_applied() {
		return referral_credit_is_applied;
	}

	public void setReferral_credit_is_applied(boolean referral_credit_is_applied) {
		this.referral_credit_is_applied = referral_credit_is_applied;
	}

	public boolean isAdvocate_credit_is_applied() {
		return advocate_credit_is_applied;
	}

	public void setAdvocate_credit_is_applied(boolean advocate_credit_is_applied) {
		this.advocate_credit_is_applied = advocate_credit_is_applied;
	}
	
	
}
