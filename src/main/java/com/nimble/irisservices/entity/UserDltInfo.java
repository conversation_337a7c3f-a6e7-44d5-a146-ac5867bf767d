package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name="user_dlt_info")
public class UserDltInfo {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id = 0;
	
	@Column(name = "user_id")
	private long user_id = 0;

	@Column(name = "meid")
	private String meid = "NA";
	
	@Column(name = "cmp_id")
	private long cmp_id = 0;
	
	@Column(name = "gateway_id")
	private long gateway_id = 0;
	
	@Column(name = "qrcode")
	private String qrcode = "NA";
	
	@Column(name = "username")
	private String username = "NA";
	
	@Column(name = "billing_email")
	private String billing_email = "NA";
	
	@Column(name = "order_id")
	private long order_id = 0;
	
	@Column(name = "order_channel")
	private String order_channel = "NA";
	
	@Column(name = "order_date")
	private String order_date = "1753-01-01 00:00:00";
	
	@Column(name = "updated_date")
	private String updated_date = "1753-01-01 00:00:00";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public long getCmp_id() {
		return cmp_id;
	}

	public void setCmp_id(long cmp_id) {
		this.cmp_id = cmp_id;
	}

	public String getQrcode() {
		return qrcode;
	}

	public void setQrcode(String qrcode) {
		this.qrcode = qrcode;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getBilling_email() {
		return billing_email;
	}

	public void setBilling_email(String billing_email) {
		this.billing_email = billing_email;
	}

	public long getOrder_id() {
		return order_id;
	}

	public void setOrder_id(long order_id) {
		this.order_id = order_id;
	}

	public String getOrder_date() {
		return order_date;
	}

	public void setOrder_date(String order_date) {
		this.order_date = order_date;
	}

	public String getUpdated_date() {
		return updated_date;
	}

	public void setUpdated_date(String updated_date) {
		this.updated_date = updated_date;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public String getOrder_channel() {
		return order_channel;
	}

	public void setOrder_channel(String order_channel) {
		this.order_channel = order_channel;
	}
}
