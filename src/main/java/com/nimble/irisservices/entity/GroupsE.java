package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="groupse", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class GroupsE implements Serializable{

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@Column(name="groupEname")
	private String groupEname;

	@JsonIgnore
	@ManyToOne(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
	@JoinColumn(name="cmp_id")
	private Company company;
	
	@ManyToOne(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
	@JoinColumn(name = "groupsf_id")
	private GroupsF groupsf_id;

	
	public GroupsE(String groupEname, Company company, GroupsF groupsf_id) {
		super();
		this.groupEname = groupEname;
		this.company = company;
		this.groupsf_id = groupsf_id;
	}

	public GroupsE() {
		super();
		// TODO Auto-generated constructor stub
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getGroupEname() {
		return groupEname;
	}

	public void setGroupEname(String groupEname) {
		this.groupEname = groupEname;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public GroupsF getGroupsf_id() {
		return groupsf_id;
	}

	public void setGroupsf_id(GroupsF groupsf_id) {
		this.groupsf_id = groupsf_id;
	}
}
