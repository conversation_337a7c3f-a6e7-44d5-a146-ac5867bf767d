package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "version_mapping")
public class VersionMapping implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "app_version")
	private String app_version;

	@Column(name = "os_type")
	private String os_type;

	@Column(name = "inapp_redirect")
	private int inapp_redirect;

	@Column(name = "cb_checkout")
	private boolean cb_checkout;

	@Column(name = "hide_subscription")
	private boolean hide_subscription = false;

	@Column(name = "enablegoogle")
	private boolean enablegoogle = true;

	@Column(name = "enablefb")
	private boolean enablefb = true;

	@Column(name = "enableapple")
	private boolean enableapple = true;

	@Column(name = "show_user_story")
	private boolean show_user_story = true;

	@Column(name = "enable_powerloss")
	private boolean enable_powerloss = true;

	@Column(name = "enable_tips")
	private boolean enable_tips = true;

	@Column(name = "show_orderid")
	private boolean show_orderid = true;

	@Column(name = "show_vpmdialog")
	private boolean show_vpmdialog = true;

	@Column(name = "plan_version")
	private String plan_version = "V1";

	@Column(name = "enable_appnotify")
	private boolean enable_appnotify = true;

	@Column(name = "enable_getsocial")
	private boolean enable_getsocial = true;

	@Column(name = "enable_delete")
	private boolean enable_delete = true;

	@Column(name = "show_cancel_sub")
	private boolean show_cancel_sub = false;
	
	@Column(name = "vetchat_redirect")
	private boolean vetchat_redirect = false;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getApp_version() {
		return app_version;
	}

	public void setApp_version(String app_version) {
		this.app_version = app_version;
	}

	public String getOs_type() {
		return os_type;
	}

	public void setOs_type(String os_type) {
		this.os_type = os_type;
	}

	public int getInapp_redirect() {
		return inapp_redirect;
	}

	public void setInapp_redirect(int inapp_redirect) {
		this.inapp_redirect = inapp_redirect;
	}

	public boolean isCb_checkout() {
		return cb_checkout;
	}

	public void setCb_checkout(boolean cb_checkout) {
		this.cb_checkout = cb_checkout;
	}

	public boolean isHide_subscription() {
		return hide_subscription;
	}

	public void setHide_subscription(boolean hide_subscription) {
		this.hide_subscription = hide_subscription;
	}

	public boolean isEnablegoogle() {
		return enablegoogle;
	}

	public void setEnablegoogle(boolean enablegoogle) {
		this.enablegoogle = enablegoogle;
	}

	public boolean isEnablefb() {
		return enablefb;
	}

	public void setEnablefb(boolean enablefb) {
		this.enablefb = enablefb;
	}

	public boolean isEnableapple() {
		return enableapple;
	}

	public void setEnableapple(boolean enableapple) {
		this.enableapple = enableapple;
	}

	public boolean isShow_user_story() {
		return show_user_story;
	}

	public void setShow_user_story(boolean show_user_story) {
		this.show_user_story = show_user_story;
	}

	public boolean isEnable_powerloss() {
		return enable_powerloss;
	}

	public void setEnable_powerloss(boolean enable_powerloss) {
		this.enable_powerloss = enable_powerloss;
	}

	public boolean isEnable_tips() {
		return enable_tips;
	}

	public void setEnable_tips(boolean enable_tips) {
		this.enable_tips = enable_tips;
	}

	public boolean isShow_orderid() {
		return show_orderid;
	}

	public void setShow_orderid(boolean show_orderid) {
		this.show_orderid = show_orderid;
	}

	public boolean isShow_vpmdialog() {
		return show_vpmdialog;
	}

	public void setShow_vpmdialog(boolean show_vpmdialog) {
		this.show_vpmdialog = show_vpmdialog;
	}

	public String getPlan_version() {
		return plan_version;
	}

	public void setPlan_version(String plan_version) {
		this.plan_version = plan_version;
	}

	public boolean isEnable_appnotify() {
		return enable_appnotify;
	}

	public void setEnable_appnotify(boolean enable_appnotify) {
		this.enable_appnotify = enable_appnotify;
	}

	public boolean isEnable_getsocial() {
		return enable_getsocial;
	}

	public void setEnable_getsocial(boolean enable_getsocial) {
		this.enable_getsocial = enable_getsocial;
	}

	public boolean isEnable_delete() {
		return enable_delete;
	}

	public void setEnable_delete(boolean enable_delete) {
		this.enable_delete = enable_delete;
	}

	public boolean isShow_cancel_sub() {
		return show_cancel_sub;
	}

	public void setShow_cancel_sub(boolean show_cancel_sub) {
		this.show_cancel_sub = show_cancel_sub;
	}

	public boolean isVetchat_redirect() {
		return vetchat_redirect;
	}

	public void setVetchat_redirect(boolean vetchat_redirect) {
		this.vetchat_redirect = vetchat_redirect;
	}

}
