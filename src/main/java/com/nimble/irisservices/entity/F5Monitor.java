package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.hibernate.annotations.GenericGenerator;


@Entity 
@Table(name="f5_monitor", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class F5Monitor implements Serializable{

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@JsonIgnore
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="user_id")
    private User user;
	
	@Column(name="name")
	private String name;
	
	@Column(name="meid")
	private String meid;
	

	public F5Monitor() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	public F5Monitor(long id, String name, String meid, User user) {
		this.id 	= id;
		this.name 	= name;
		this.meid 	= meid;
		this.user 	= user;
	}

	public F5Monitor(String name, String meid, User user) {
		this.name 	= name;
		this.meid 	= meid;
		this.user 	= user;
	}

	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMeid() {
		return meid;
	}


	public void setMeid(String meid) {
		this.meid = meid;
	}
	
}
