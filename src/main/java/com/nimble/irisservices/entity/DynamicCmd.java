package com.nimble.irisservices.entity;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="dynamiccmd")
public class DynamicCmd {
	
	@Id
	@Column(name="seqno")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	@Column(name="cmdname")
	private String cmdname = "Iris";
	@Column(name="ackvalue")
	private String ackvalue = "NULL";
	@Column(name="cmdvalue")
	private String cmdvalue;
	@Column(name="status")
	private String status = "notsent";
	@Column(name="datetime")
	private String datetime;
	@Column(name="retrycount")
	private int retryCount = 3;
	@Column(name="transporttype")
	private int transportType;
	@Column(name="ackdatetime")
	private String ackdatetime;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="gateway_id")
	//@JsonBackReference
    private Gateway gateway;
	
	
	public DynamicCmd() {
		super();
		// TODO Auto-generated constructor stub
	}

	public DynamicCmd(Gateway gateway, String cmdvalue, String datetime, int transportType,String status) {
		super();
		this.gateway = gateway;
		this.cmdvalue = cmdvalue;
		this.datetime = datetime;
		this.transportType = transportType;
		this.status = status;
	}

	public long getId() {
		return id;
	}
	
	public void setId(long id) {
		this.id = id;
	}

	public void setCmdname(String cmdname) {
		this.cmdname = cmdname;
	}

	public void setAckvalue(String ackvalue) {
		this.ackvalue = ackvalue;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public void setDatetime(String datetime) {
		this.datetime = datetime;
	}

	public void setRetryCount(int retryCount) {
		this.retryCount = retryCount;
	}

	public void setTransportType(int transportType) {
		this.transportType = transportType;
	}

	public void setGateway(Gateway gateway) {
		this.gateway = gateway;
	}

	public String getCmdname() {
		return cmdname;
	}

	public String getAckvalue() {
		return ackvalue;
	}

	public String getCmdvalue() {
		return cmdvalue;
	}
	

	public void setCmdvalue(String cmdvalue) {
		this.cmdvalue = cmdvalue;
	}

	public String getStatus() {
		return status;
	}

	public String getDatetime() {
		return datetime;
	}

	public int getRetryCount() {
		return retryCount;
	}

	public int getTransportType() {
		return transportType;
	}

	public Gateway giveGateway() {
		return gateway;
	}

	public String getAckdatetime() {
		return ackdatetime;
	}

	public void setAckdatetime(String ackdatetime) {
		this.ackdatetime = ackdatetime;
	}
		
}
