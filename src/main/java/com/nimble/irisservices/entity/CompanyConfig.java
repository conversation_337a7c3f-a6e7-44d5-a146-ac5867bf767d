package com.nimble.irisservices.entity;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="companyconfig")
public class CompanyConfig {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	@Column(name="vgroupname")
	private String vgroupname = "Group" ;
	/*@Column(name="vsubgroupname")
	private String vsubgroupname = "Subgroup";*/
	@Column(name="gatewayname")
	private String gatewayname= "Gateway";
	@Column(name="nodename")
	private String nodename= "node";
	@Column(name="incursionenable")
	private String incursionenable = "0";
	@Column(name="realtimemonitor")
	private boolean realtimemonitor = true;
	@Column(name="mapview")
	private boolean mapView = true;
	@Column(name="wirelesssensorview")
	private boolean wirelessSensorView = false;
	@Column(name="dashboardsensorview")
	private boolean dashboardSensorView = false;
	@Column(name="temperatureunit")
	private String temperatureunit = "F";
	@Column(name="humidityunit")
	private String humidityunit = "%";
	@Column(name="pressureunit")
	private String pressureunit = "Pa";
	@Column(name="speedunit")
	private String speedunit = "MPH";
	@Column(name="lightunit")
	private String lightunit = "lx";
	@Column(name="moistureunit")
	private String moistureunit = "%";
	@Column(name="memstype")
	private String memstype = "motion alert";
	@Column(name="panic_or_tamper")
	private String panic_tamper = "Panic";
	@Column(name="reportenable")
	private String reportenable = "11100000";
	@Column(name="alertenable")
	private String alertenable = "11110000001001001";
	@Column(name="sensorenable")
	private String sensorenable = "01100001";
	@Column(name="cellIdEnable")
	private boolean cellIdEnable = true;
	@Column(name="cellIdUrlKey")
	private String cellIdUrlKey;
	@Column(name = "httpredirenable")
	private boolean httpredirenable =false;
	@Column(name="httpredirURL")
	private String httpredirURL;
	@Column(name ="tcpredirenable")
	private boolean tcpredirenable = false;
	@Column(name ="tcpredirIP")
	private String tcpredirIP;
	@Column(name ="tcpredirPORT")
	private int tcpredirPORT;
	@Column(name="notreportinginterval")
	private int notreportinginterval = 60;
	@Column(name="appnotifyenable")
	private boolean appnotifyenable = false;
	@Column(name="optgatewayname")
	private String optgatewayname= null;
	@Column(name="whitelabelenable")
	private boolean whitelabelenable = false;
	@Column(name = "wl_logourl")
	private String  wl_logourl;
	@Column(name = "httpredirenable_SAND")
	private boolean httpredirenable_SAND =false;	
	@Column(name="httpredirURL_SAND")
	private String httpredirURL_SAND;
	@Column(name="interfreqenable")
	private boolean interfreqenable = false;
	@Column(name="heatindex_enable")
	private boolean heatindex_enable = true;
	@Column(name="petservice_enable")
	private boolean petservice_enable = false;
	@Column(name="pethealth_enable")
	private boolean pethealth_enable = false;
	@Column(name="livetrack_enable")
	private boolean livetrack_enable = false;
	@Column(name="is_vpm")
	private boolean is_vpm = false;
	@Column(name="show_referralmsg")
	private boolean show_referralmsg = false;
	
	@Column(name="geofence_enable")
	private boolean geofence_enable = true;
	
	@Column(name="custom_plan_enable")
	private boolean custom_plan_enable = false;
	
	@OneToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="cmp_id")
	private Company company;

	public CompanyConfig() {
		super();
	}

	public CompanyConfig(Company company) {
		super();
		this.company = company;
	}

	public long getId() {
		return id;
	}

	public String getVgroupname() {
		return vgroupname;
	}

	/*public String getVsubgroupname() {
		return vsubgroupname;
	}*/

	public String getGatewayname() {
		return gatewayname;
	}

	public String getNodename() {
		return nodename;
	}

	public String getIncursionenable() {
		return incursionenable;
	}

	public boolean isRealtimemonitor() {
		return realtimemonitor;
	}

	public String getTemperatureunit() {
		return temperatureunit;
	}

	public String getHumidityunit() {
		return humidityunit;
	}

	public String getPressureunit() {
		return pressureunit;
	}

	public String getSpeedunit() {
		return speedunit;
	}

	public String getLightunit() {
		return lightunit;
	}

	public String getMoistureunit() {
		return moistureunit;
	}

	public String getMemstype() {
		return memstype;
	}

	public String getPanic_tamper() {
		return panic_tamper;
	}

	public Company giveCompany() {
		return company;
	}

	public boolean isMapView() {
		return mapView;
	}

	public boolean isWirelessSensorView() {
		return wirelessSensorView;
	}

	public boolean isDashboardSensorView() {
		return dashboardSensorView;
	}
	
	public void setId(long id) {
		this.id = id;
	}

	public void setVgroupname(String vgroupname) {
		this.vgroupname = vgroupname;
	}

	/*public void setVsubgroupname(String vsubgroupname) {
		this.vsubgroupname = vsubgroupname;
	}
*/
	public void setGatewayname(String gatewayname) {
		this.gatewayname = gatewayname;
	}

	public void setNodename(String nodename) {
		this.nodename = nodename;
	}

	public void setIncursionenable(String incursionenable) {
		this.incursionenable = incursionenable;
	}

	public void setRealtimemonitor(boolean realtimemonitor) {
		this.realtimemonitor = realtimemonitor;
	}

	public void setMapView(boolean mapView) {
		this.mapView = mapView;
	}

	public void setWirelessSensorView(boolean wirelessSensorView) {
		this.wirelessSensorView = wirelessSensorView;
	}

	public void setDashboardSensorView(boolean dashboardSensorView) {
		this.dashboardSensorView = dashboardSensorView;
	}

	public void setTemperatureunit(String temperatureunit) {
		this.temperatureunit = temperatureunit;
	}

	public void setHumidityunit(String humidityunit) {
		this.humidityunit = humidityunit;
	}

	public void setPressureunit(String pressureunit) {
		this.pressureunit = pressureunit;
	}

	public void setSpeedunit(String speedunit) {
		this.speedunit = speedunit;
	}

	public void setLightunit(String lightunit) {
		this.lightunit = lightunit;
	}

	public void setMoistureunit(String moistureunit) {
		this.moistureunit = moistureunit;
	}

	public void setMemstype(String memstype) {
		this.memstype = memstype;
	}

	public void setPanic_tamper(String panic_tamper) {
		this.panic_tamper = panic_tamper;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public String getReportenable() {
		return reportenable;
	}

	public void setReportenable(String reportenable) {
		this.reportenable = reportenable;
	}

	public String getAlertenable() {
		return alertenable;
	}

	public void setAlertenable(String alertenable) {
		this.alertenable = alertenable;
	}

	public String getSensorenable() {
		return sensorenable;
	}

	public void setSensorenable(String sensorenable) {
		this.sensorenable = sensorenable;
	}	

	public boolean isHttpredirenable() {
		return httpredirenable;
		
	}

	public void setHttpredirenable(boolean httpredirenable) {
		this.httpredirenable = httpredirenable;
	}

	public String getHttpredirURL() {
		return httpredirURL;
	}

	public void setHttpredirURL(String httpredirURL) {
		this.httpredirURL = httpredirURL;
	}

	public boolean isTcpredirenable() {
		return tcpredirenable;
	}

	public void setTcpredirenable(boolean tcpredirenable) {
		this.tcpredirenable = tcpredirenable;
	}

	public String getTcpredirIP() {
		return tcpredirIP;
	}

	public void setTcpredirIP(String tcpredirIP) {
		this.tcpredirIP = tcpredirIP;
	}

	public int getTcpredirPORT() {
		return tcpredirPORT;
	}

	public void setTcpredirPORT(int tcpredirPORT) {
		this.tcpredirPORT = tcpredirPORT;
	}

	public String getCellIdUrlKey() {
		return cellIdUrlKey;
	}

	public void setCellIdUrlKey(String cellIdUrlKey) {
		this.cellIdUrlKey = cellIdUrlKey;
	}

	public int getNotreportinginterval() {
		return notreportinginterval;
	}

	public void setNotreportinginterval(int notreportinginterval) {
		this.notreportinginterval = notreportinginterval;
	}	
	
	public boolean isAppnotifyenable() {
		return appnotifyenable;
	}
	
	public void setAppnotifyenable(boolean appnotifyenable) {
		this.appnotifyenable = appnotifyenable;
	}

	public String getOptgatewayname() {
		return optgatewayname;
	}

	public void setOptgatewayname(String optgatewayname) {
		this.optgatewayname = optgatewayname;
	}

	public boolean isWhitelabelenable() {
		return whitelabelenable;
	}

	public void setWhitelabelenable(boolean whitelabelenable) {
		this.whitelabelenable = whitelabelenable;
	}

	public String getWl_logourl() {
		return wl_logourl;
	}

	public void setWl_logourl(String wl_logourl) {
		this.wl_logourl = wl_logourl;
	}

	public boolean isCellIdEnable() {
		return cellIdEnable;
	}

	public void setCellIdEnable(boolean cellIdEnable) {
		this.cellIdEnable = cellIdEnable;
	}

	public boolean isHttpredirenable_SAND() {
		return httpredirenable_SAND;
	}

	public void setHttpredirenable_SAND(boolean httpredirenable_SAND) {
		this.httpredirenable_SAND = httpredirenable_SAND;
	}

	public String getHttpredirURL_SAND() {
		return httpredirURL_SAND;
	}

	public void setHttpredirURL_SAND(String httpredirURL_SAND) {
		this.httpredirURL_SAND = httpredirURL_SAND;
	}

	public boolean isInterfreqenable() {
		return interfreqenable;
	}

	public void setInterfreqenable(boolean interfreqenable) {
		this.interfreqenable = interfreqenable;
	}

	public boolean isHeatindex_enable() {
		return heatindex_enable;
	}

	public void setHeatindex_enable(boolean heatindex_enable) {
		this.heatindex_enable = heatindex_enable;
	}

	public boolean isPetservice_enable() {
		return petservice_enable;
	}

	public void setPetservice_enable(boolean petservice_enable) {
		this.petservice_enable = petservice_enable;
	}

	public boolean isPethealth_enable() {
		return pethealth_enable;
	}

	public void setPethealth_enable(boolean pethealth_enable) {
		this.pethealth_enable = pethealth_enable;
	}

	public boolean isLivetrack_enable() {
		return livetrack_enable;
	}

	public void setLivetrack_enable(boolean livetrack_enable) {
		this.livetrack_enable = livetrack_enable;
	}

	public boolean isIs_vpm() {
		return is_vpm;
	}

	public void setIs_vpm(boolean is_vpm) {
		this.is_vpm = is_vpm;
	}

	public boolean isShow_referralmsg() {
		return show_referralmsg;
	}

	public void setShow_referralmsg(boolean show_referralmsg) {
		this.show_referralmsg = show_referralmsg;
	}

	public boolean isGeofence_enable() {
		return geofence_enable;
	}

	public void setGeofence_enable(boolean geofence_enable) {
		this.geofence_enable = geofence_enable;
	}

	public boolean isCustom_plan_enable() {
		return custom_plan_enable;
	}

	public void setCustom_plan_enable(boolean custom_plan_enable) {
		this.custom_plan_enable = custom_plan_enable;
	}

}
