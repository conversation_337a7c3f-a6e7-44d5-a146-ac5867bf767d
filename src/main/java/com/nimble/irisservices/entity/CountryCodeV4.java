package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "country_code")
public class CountryCodeV4 implements Serializable {
	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "code")
	private String code;

	@Column(name = "country")
	private String country;

	@Column(name = "short_name")
	private String short_name;
	
	@Column(name = "page_select")
	private int page_select;
	
	@Column(name = "phone_num_min")
	private int ph_min;
	
	@Column(name = "phone_num_max")
	private int ph_max;
	
	@Column(name = "min_zip_code")
	private int min_zip_code;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getShort_name() {
		return short_name;
	}

	public void setShort_name(String short_name) {
		this.short_name = short_name;
	}

	public int getPage_select() {
		return page_select;
	}

	public void setPage_select(int page_select) {
		this.page_select = page_select;
	}

	public int getPh_min() {
		return ph_min;
	}

	public void setPh_min(int ph_min) {
		this.ph_min = ph_min;
	}

	public int getPh_max() {
		return ph_max;
	}

	public void setPh_max(int ph_max) {
		this.ph_max = ph_max;
	}

	public int getMin_zip_code() {
		return min_zip_code;
	}

	public void setMin_zip_code(int min_zip_code) {
		this.min_zip_code = min_zip_code;
	}

}
