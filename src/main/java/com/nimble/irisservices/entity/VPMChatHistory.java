package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "vpm_chat_history")
public class VPMChatHistory implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private int id;
	
	@Column(name = "vpm_user_id")
	private String vpmUserId = "NA" ;
	
	@Column(name = "channel_id")
	private String channelSid = "NA" ;
	
	@Column(name = "vet_id")
	private String vetId = "NA";
	
	@Column(name = "status")
	private boolean chatStatus = false;
	
	@Column(name = "created_on")
	private String createdOn = "1753-01-01 00:00:00";
	
	@Column(name = "updated_on")
	private String updatedOn = "1753-01-01 00:00:00";

	public String getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}

	public String getUpdatedOn() {
		return updatedOn;
	}

	public void setUpdatedOn(String updatedOn) {
		this.updatedOn = updatedOn;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getVpmUserId() {
		return vpmUserId;
	}

	public void setVpmUserId(String vpmUserId) {
		this.vpmUserId = vpmUserId;
	}

	public String getChannelSid() {
		return channelSid;
	}

	public void setChannelSid(String channelSid) {
		this.channelSid = channelSid;
	}

	public String getVetId() {
		return vetId;
	}

	public void setVetId(String vetId) {
		this.vetId = vetId;
	}

	public boolean isChatStatus() {
		return chatStatus;
	}

	public void setChatStatus(boolean chatStatus) {
		this.chatStatus = chatStatus;
	}

	public VPMChatHistory() {
		super();
	}

	public VPMChatHistory(int id, String vpmUserId, String channelSid, String vetId, boolean chatStatus,
			String createdOn, String updatedOn) {
		super();
		this.id = id;
		this.vpmUserId = vpmUserId;
		this.channelSid = channelSid;
		this.vetId = vetId;
		this.chatStatus = chatStatus;
		this.createdOn = createdOn;
		this.updatedOn = updatedOn;
	}
	
	
	
	
		
}

