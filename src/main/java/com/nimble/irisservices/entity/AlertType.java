package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.GenericGenerator;


@Entity 
@Table(name="alerttype")
public class AlertType {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name="name")
	private String name;
	
	@Column(name="minrange")
	private float minRange;
	
	@Column(name="maxrange")
	private float maxRange;
	
	@Column(name="adcposition")
	private String adcPosition;
	
	@Column(name="spelvariable")
	private String spelVarialbe;
	
	@Transient
	private int alertMinRange;
	
	@Transient
	private int alertMaxRange;
	
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public float getMinRange() {
		return minRange;
	}
	public void setMinRange(float minRange) {
		this.minRange = minRange;
	}
	public float getMaxRange() {
		return maxRange;
	}
	public void setMaxRange(float maxRange) {
		this.maxRange = maxRange;
	}
	public String getAdcPosition() {
		return adcPosition;
	}
	public void setAdcPosition(String adcPosition) {
		this.adcPosition = adcPosition;
	}
	public String getSpelVarialbe() {
		return spelVarialbe;
	}
	public void setSpelVarialbe(String spelVarialbe) {
		this.spelVarialbe = spelVarialbe;
	}
	public int getAlertMinRange() {
		return alertMinRange;
	}
	public void setAlertMinRange(int alertMinRange) {
		this.alertMinRange = alertMinRange;
	}
	public int getAlertMaxRange() {
		return alertMaxRange;
	}
	public void setAlertMaxRange(int alertMaxRange) {
		this.alertMaxRange = alertMaxRange;
	}
}
