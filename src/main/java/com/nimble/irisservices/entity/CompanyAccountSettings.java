package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="company_account_settings",uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class CompanyAccountSettings implements Serializable {
	
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	 private long id;
	
	@ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="api_id")
     private Apidetails apidetails;
	
	@ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="company_id")
     private Company company;
	
	@Column(name="total_transaction")
     private Long totalTransaction;
	
	@Column(name="remaining_transaction")
     private Long remainingTransaction;
	 
	 @Column(name="total_count")
     private Long totalCount;
	 
	 
	 public CompanyAccountSettings() {
		 super();
	    }
     
	public CompanyAccountSettings(long id, Apidetails apidetails, Company company, Long totalTransaction,
			Long remainingTransaction, Long totalCount) {
		super();
		this.id = id;
		this.apidetails = apidetails;
		this.company = company;
		this.totalTransaction = totalTransaction;
		this.remainingTransaction = remainingTransaction;
		this.totalCount = totalCount;
	}
	

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Apidetails getApidetails() {
		return apidetails;
	}

	public void setApidetails(Apidetails apidetails) {
		this.apidetails = apidetails;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public Long getTotalTransaction() {
		return totalTransaction;
	}

	public void setTotalTransaction(Long totalTransaction) {
		this.totalTransaction = totalTransaction;
	}

	public Long getRemainingTransaction() {
		return remainingTransaction;
	}

	public void setRemainingTransaction(Long remainingTransaction) {
		this.remainingTransaction = remainingTransaction;
	}

	public Long getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Long totalCount) {
		this.totalCount = totalCount;
	}
	
    

}
