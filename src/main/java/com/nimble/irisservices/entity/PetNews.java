package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "petnews")
public class PetNews implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "android_shortnews")
	private String android_shortnews;

	@Column(name = "iphone_shortnews")
	private String iphone_shortnews;

	@Column(name = "ipad_shortnews")
	private String ipad_shortnews;

	@Column(name = "android_longnews")
	private String android_longnews;

	@Column(name = "iphone_longnews")
	private String iphone_longnews;

	@Column(name = "ipad_longnews")
	private String ipad_longnews;

	@Column(name = "android_imgurl")
	private String android_imgurl;

	@Column(name = "iphone_imgurl")
	private String iphone_imgurl;

	@Column(name = "ipad_imgurl")
	private String ipad_imgurl;

	@Column(name = "enable")
	private boolean enable = true;

	@Column(name = "start_date")
	private String start_date = "1753-01-01 00:00:00";

	@Column(name = "expired_on")
	private String expired_on = "1753-01-01 00:00:00";

	@Column(name = "created_on")
	private String created_on = "1753-01-01 00:00:00";

	@Column(name = "updated_on")
	private String updated_on = "1753-01-01 00:00:00";

	@Column(name = "dmf_snews")
	private String dmf_snews;

	@Column(name = "f_snews")
	private String f_snews;

	@Column(name = "dmf_lnews")
	private String dmf_lnews;

	@Column(name = "f_lnews")
	private String f_lnews;
	
	@Column(name = "show_CTA")
	private boolean show_CTA = false;
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getAndroid_shortnews() {
		return android_shortnews;
	}

	public void setAndroid_shortnews(String android_shortnews) {
		this.android_shortnews = android_shortnews;
	}

	public String getIphone_shortnews() {
		return iphone_shortnews;
	}

	public void setIphone_shortnews(String iphone_shortnews) {
		this.iphone_shortnews = iphone_shortnews;
	}

	public String getIpad_shortnews() {
		return ipad_shortnews;
	}

	public void setIpad_shortnews(String ipad_shortnews) {
		this.ipad_shortnews = ipad_shortnews;
	}

	public String getAndroid_longnews() {
		return android_longnews;
	}

	public void setAndroid_longnews(String android_longnews) {
		this.android_longnews = android_longnews;
	}

	public String getIphone_longnews() {
		return iphone_longnews;
	}

	public void setIphone_longnews(String iphone_longnews) {
		this.iphone_longnews = iphone_longnews;
	}

	public String getIpad_longnews() {
		return ipad_longnews;
	}

	public void setIpad_longnews(String ipad_longnews) {
		this.ipad_longnews = ipad_longnews;
	}

	public String getAndroid_imgurl() {
		return android_imgurl;
	}

	public void setAndroid_imgurl(String android_imgurl) {
		this.android_imgurl = android_imgurl;
	}

	public String getIphone_imgurl() {
		return iphone_imgurl;
	}

	public void setIphone_imgurl(String iphone_imgurl) {
		this.iphone_imgurl = iphone_imgurl;
	}

	public String getIpad_imgurl() {
		return ipad_imgurl;
	}

	public void setIpad_imgurl(String ipad_imgurl) {
		this.ipad_imgurl = ipad_imgurl;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getStart_date() {
		return start_date;
	}

	public void setStart_date(String start_date) {
		this.start_date = start_date;
	}

	public String getExpired_on() {
		return expired_on;
	}

	public void setExpired_on(String expired_on) {
		this.expired_on = expired_on;
	}

	public String getCreated_on() {
		return created_on;
	}

	public void setCreated_on(String created_on) {
		this.created_on = created_on;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public String getDmf_snews() {
		return dmf_snews;
	}

	public void setDmf_snews(String dmf_snews) {
		this.dmf_snews = dmf_snews;
	}

	public String getF_snews() {
		return f_snews;
	}

	public void setF_snews(String f_snews) {
		this.f_snews = f_snews;
	}

	public String getDmf_lnews() {
		return dmf_lnews;
	}

	public void setDmf_lnews(String dmf_lnews) {
		this.dmf_lnews = dmf_lnews;
	}

	public String getF_lnews() {
		return f_lnews;
	}

	public void setF_lnews(String f_lnews) {
		this.f_lnews = f_lnews;
	}

	public boolean isShow_CTA() {
		return show_CTA;
	}

	public void setShow_CTA(boolean show_CTA) {
		this.show_CTA = show_CTA;
	}

}
