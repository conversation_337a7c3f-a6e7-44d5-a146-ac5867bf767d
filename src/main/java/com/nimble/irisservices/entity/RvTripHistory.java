package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "user_rvtrip_history")
public class RvTripHistory implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "from_addr")
	private String from_addr;

	@Column(name = "from_lat")
	private double from_lat;

	@Column(name = "from_lon")
	private double from_lon;

	@Column(name = "to_addr")
	private String to_addr;

	@Column(name = "to_lat")
	private double to_lat;

	@Column(name = "to_lon")
	private double to_lon;

	@Column(name = "no_members")
	private int no_members;

	@Column(name = "pet_count")
	private int pet_count;

	@Column(name = "vehicle_type")
	private long vehicle_type = 0;

	@Column(name = "start_time")
	private String start_time = "1753-01-01 00:00:00";

	@Column(name = "end_time")
	private String end_time = "1753-01-01 00:00:00";

	@Column(name = "image_path")
	private String image_path = "NA";

	@Column(name = "timezone")
	private String timezone;

	@Column(name = "trip_started")
	private boolean trip_started;

	@Column(name = "createdon")
	private String createdon;

	@Column(name = "user_id")
	private long user_id;

	@Column(name = "short_from_addr")
	private String shortFromAdd;

	@Column(name = "short_to_addr")
	private String shortToAdd;

	@Column(name = "others_type")
	private String others_type = "NA";

	public RvTripHistory() {
		super();
	}

	public RvTripHistory(long id, String from_addr, double from_lat, double from_lon, String to_addr, double to_lat,
			double to_lon, int no_members, int pet_count, long vehicle_type, String start_time, String end_time,
			String image_path, String timezone, boolean trip_started, String createdon, long user_id,
			String shortFromAdd, String shortToAdd, String others_type) {
		super();
		this.id = id;
		this.from_addr = from_addr;
		this.from_lat = from_lat;
		this.from_lon = from_lon;
		this.to_addr = to_addr;
		this.to_lat = to_lat;
		this.to_lon = to_lon;
		this.no_members = no_members;
		this.pet_count = pet_count;
		this.vehicle_type = vehicle_type;
		this.start_time = start_time;
		this.end_time = end_time;
		this.image_path = image_path;
		this.timezone = timezone;
		this.trip_started = trip_started;
		this.createdon = createdon;
		this.user_id = user_id;
		this.shortFromAdd = shortFromAdd;
		this.shortToAdd = shortToAdd;
		this.others_type = others_type;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getFrom_addr() {
		return from_addr;
	}

	public void setFrom_addr(String from_addr) {
		this.from_addr = from_addr;
	}

	public double getFrom_lat() {
		return from_lat;
	}

	public void setFrom_lat(double from_lat) {
		this.from_lat = from_lat;
	}

	public double getFrom_lon() {
		return from_lon;
	}

	public void setFrom_lon(double from_lon) {
		this.from_lon = from_lon;
	}

	public String getTo_addr() {
		return to_addr;
	}

	public void setTo_addr(String to_addr) {
		this.to_addr = to_addr;
	}

	public double getTo_lat() {
		return to_lat;
	}

	public void setTo_lat(double to_lat) {
		this.to_lat = to_lat;
	}

	public double getTo_lon() {
		return to_lon;
	}

	public void setTo_lon(double to_lon) {
		this.to_lon = to_lon;
	}

	public int getNo_members() {
		return no_members;
	}

	public void setNo_members(int no_members) {
		this.no_members = no_members;
	}

	public int getPet_count() {
		return pet_count;
	}

	public void setPet_count(int pet_count) {
		this.pet_count = pet_count;
	}

	public long getVehicle_type() {
		return vehicle_type;
	}

	public void setVehicle_type(long vehicle_type) {
		this.vehicle_type = vehicle_type;
	}

	public String getStart_time() {
		return start_time;
	}

	public void setStart_time(String start_time) {
		this.start_time = start_time;
	}

	public String getEnd_time() {
		return end_time;
	}

	public void setEnd_time(String end_time) {
		this.end_time = end_time;
	}

	public String getImage_path() {
		return image_path;
	}

	public void setImage_path(String image_path) {
		this.image_path = image_path;
	}

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}

	public boolean isTrip_started() {
		return trip_started;
	}

	public void setTrip_started(boolean trip_started) {
		this.trip_started = trip_started;
	}

	public String getCreatedon() {
		return createdon;
	}

	public void setCreatedon(String createdon) {
		this.createdon = createdon;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getShortFromAdd() {
		return shortFromAdd;
	}

	public void setShortFromAdd(String shortFromAdd) {
		this.shortFromAdd = shortFromAdd;
	}

	public String getShortToAdd() {
		return shortToAdd;
	}

	public void setShortToAdd(String shortToAdd) {
		this.shortToAdd = shortToAdd;
	}

	public String getOthers_type() {
		return others_type;
	}

	public void setOthers_type(String others_type) {
		this.others_type = others_type;
	}

}
