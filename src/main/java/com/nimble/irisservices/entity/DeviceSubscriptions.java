package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;


@Entity
@Table(name = "devicesubscriptions", uniqueConstraints = @UniqueConstraint(columnNames = { "id" }))
public class DeviceSubscriptions implements Serializable {

	@Id
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	@Column(name = "id")
	private long id;

	@Column(name = "niom_sub_id")
	private long niomSubId;

	@Column(name = "woocom_sub_id")
	private long woocomSubId;

	@Column(name = "order_id")
	private long orderId;

	@Column(name = "meid")
	private String meid;

	@Column(name = "status")
	private String status;

	@Column(name = "billing_firstname")
	private String billingFirstname;

	@Column(name = "billing_lastname")
	private String billingLastname;

	@Column(name = "billing_email")
	private String billingEmail;
	
	@Column(name = "billing_phone")
	private String billingPhone;

	@Column(name = "plan_name")
	private String planName;

	@Column(name = "sku")
	private String sku;

	@Column(name = "quantity")
	private Long quantity;

	 @Column(name="price", precision=12, scale=0)
     private float price;

	@Column(name = "billing_period")
	private String billingPeriod;

	@Column(name = "billing_start_date")
	private String billingStartDate;

	@Column(name = "trial_end_date")
	private String trialEndDate;

	@Column(name = "next_payment_date", length = 19)
	private String nextPaymentDate;

	@Column(name = "last_payment_date")
	private String lastPaymentDate;

	@Column(name = "subscription_created")
	private String subscriptionCreated;

	@Column(name = "end_date")
	private String endDate;
	
	

	@Column(name = "account_type")
	private String accountType;
	
	@Column(name = "external_order_id")
	private String externalOrderId;
	
	@Column(name = "hassleenddate")
	private String hassleEndDate;
	
	@Column(name = "orderchannel")
	private String orderChannel;



	public long getNiomSubId() {
		return niomSubId;
	}

	public void setNiomSubId(long niomSubId) {
		this.niomSubId = niomSubId;
	}

	public long getWoocomSubId() {
		return woocomSubId;
	}

	public void setWoocomSubId(long woocomSubId) {
		this.woocomSubId = woocomSubId;
	}

	public long getOrderId() {
		return orderId;
	}

	public void setOrderId(long orderId) {
		this.orderId = orderId;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getBillingFirstname() {
		return billingFirstname;
	}

	public void setBillingFirstname(String billingFirstname) {
		this.billingFirstname = billingFirstname;
	}

	public String getBillingLastname() {
		return billingLastname;
	}

	public void setBillingLastname(String billingLastname) {
		this.billingLastname = billingLastname;
	}

	public String getBillingEmail() {
		return billingEmail;
	}

	public void setBillingEmail(String billingEmail) {
		this.billingEmail = billingEmail;
	}

	public String getPlanName() {
		return planName;
	}

	public void setPlanName(String planName) {
		this.planName = planName;
	}

	public String getSku() {
		return sku;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public Long getQuantity() {
		return quantity;
	}

	public void setQuantity(Long quantity) {
		this.quantity = quantity;
	}

	public String getBillingPeriod() {
		return billingPeriod;
	}

	public void setBillingPeriod(String billingPeriod) {
		this.billingPeriod = billingPeriod;
	}

	public String getBillingStartDate() {
		return billingStartDate;
	}

	public void setBillingStartDate(String billingStartDate) {
		this.billingStartDate = billingStartDate;
	}

	public String getTrialEndDate() {
		return trialEndDate;
	}

	public void setTrialEndDate(String trialEndDate) {
		this.trialEndDate = trialEndDate;
	}

	public String getNextPaymentDate() {
		return nextPaymentDate;
	}

	public void setNextPaymentDate(String nextPaymentDate) {
		this.nextPaymentDate = nextPaymentDate;
	}

	public String getLastPaymentDate() {
		return lastPaymentDate;
	}

	public void setLastPaymentDate(String lastPaymentDate) {
		this.lastPaymentDate = lastPaymentDate;
	}

	public String getSubscriptionCreated() {
		return subscriptionCreated;
	}

	public void setSubscriptionCreated(String subscriptionCreated) {
		this.subscriptionCreated = subscriptionCreated;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getAccountType() {
		return accountType;
	}

	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	public String getExternalOrderId() {
		return externalOrderId;
	}

	public void setExternalOrderId(String externalOrderId) {
		this.externalOrderId = externalOrderId;
	}

	public String getHassleEndDate() {
		return hassleEndDate;
	}

	public void setHassleEndDate(String hassleEndDate) {
		this.hassleEndDate = hassleEndDate;
	}

	public String getOrderChannel() {
		return orderChannel;
	}

	public void setOrderChannel(String orderChannel) {
		this.orderChannel = orderChannel;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}
	
	public DeviceSubscriptions() {
		super();
		// TODO Auto-generated constructor stub
	}

	public DeviceSubscriptions(long niomSubId, long woocomSubId, long orderId, String meid, String status,
			String billingFirstname, String billingLastname, String billingEmail, String billingPhone,String planName, String sku,
			Long quantity,Float price, String billingPeriod, String billingStartDate, String trialEndDate, String nextPaymentDate,
			String lastPaymentDate, String subscriptionCreated, String endDate, String accountType,
			String externalOrderId, String hassleEndDate, String orderChannel) {
		super();
		this.niomSubId = niomSubId;
		this.woocomSubId = woocomSubId;
		this.orderId = orderId;
		this.meid = meid;
		this.status = status;
		this.billingFirstname = billingFirstname;
		this.billingLastname = billingLastname;
		this.billingEmail = billingEmail;
		this.billingPhone=billingPhone;
		this.planName = planName;
		this.sku = sku;
		this.quantity = quantity;
		this.price=price;
		this.billingPeriod = billingPeriod;
		this.billingStartDate = billingStartDate;
		this.trialEndDate = trialEndDate;
		this.nextPaymentDate = nextPaymentDate;
		this.lastPaymentDate = lastPaymentDate;
		this.subscriptionCreated = subscriptionCreated;
		this.endDate = endDate;
		this.accountType = accountType;
		this.externalOrderId = externalOrderId;
		this.hassleEndDate = hassleEndDate;
		this.orderChannel = orderChannel;
		
	}


	
	public DeviceSubscriptions(long id, long niomSubId, long woocomSubId, long orderId, String meid, String status,
			String billingFirstname, String billingLastname, String billingEmail, String billingPhone,String planName, String sku,
			Long quantity,Float price, String billingPeriod, String billingStartDate, String trialEndDate, String nextPaymentDate,
			String lastPaymentDate, String subscriptionCreated, String endDate, String accountType,
			String externalOrderId, String hassleEndDate, String orderChannel) {
		super();
		this.id = id;
		this.niomSubId = niomSubId;
		this.woocomSubId = woocomSubId;
		this.orderId = orderId;
		this.meid = meid;
		this.status = status;
		this.billingFirstname = billingFirstname;
		this.billingLastname = billingLastname;
		this.billingEmail = billingEmail;
		this.billingPhone=billingPhone;
		this.planName = planName;
		this.sku = sku;
		this.quantity = quantity;
		this.billingPeriod = billingPeriod;
		this.billingStartDate = billingStartDate;
		this.trialEndDate = trialEndDate;
		this.nextPaymentDate = nextPaymentDate;
		this.lastPaymentDate = lastPaymentDate;
		this.subscriptionCreated = subscriptionCreated;
		this.endDate = endDate;
		this.accountType = accountType;
		this.externalOrderId = externalOrderId;
		this.hassleEndDate = hassleEndDate;
		this.orderChannel = orderChannel;
		this.price=price;
		
	}

	public float getPrice() {
		return price;
	}

	public void setPrice(float price) {
		this.price = price;
	}

	public String getBillingPhone() {
		return billingPhone;
	}

	public void setBillingPhone(String billingPhone) {
		this.billingPhone = billingPhone;
	}

}

