package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="petfitreportdata")
public class PetFitReportData  implements Serializable{
	

	public PetFitReportData() {
		super();
		// TODO Auto-generated constructor stub
	}

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name="datetime")
	private String datetime;
	
	@Column(name="timezone")
	private String timezone;
	
    @ManyToOne(cascade = CascadeType.ALL,fetch = FetchType.EAGER)
	@JoinColumn(name="gateway_id")
	//@JsonBackReference
    private Gateway gateway;
   
	
    @ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="cmp_id")
	//@JsonBackReference
    private Company company;
	
	
	
	@Column(name="date")
	private String date;
	
	@Column(name="time")
	private String time;
	
	@Column(name="sfwpkt")
	private int storedPacket;
	
	@Column(name = "createdon")
	private String createdOn;
	
	@Column(name="utctime")
	private String utcTime;
	
	public PetFitReportData(long id, String datetime, String timezone, Gateway gateway, Company company, String date,
			String time, int storedPacket, String createdOn, String utcTime, String activityType, int stepCount,
			int transmitMode, String packetversion) {
		super();
		this.id = id;
		this.datetime = datetime;
		this.timezone = timezone;
		this.gateway = gateway;
		this.company = company;
		this.date = date;
		this.time = time;
		this.storedPacket = storedPacket;
		this.createdOn = createdOn;
		this.utcTime = utcTime;
		this.activityType = activityType;
		this.stepCount = stepCount;
		this.transmitMode = transmitMode;
		this.packetversion = packetversion;
	}

	@Column(name="activitytype")
	private String activityType;
	
	@Column(name="stepcount")
	private int stepCount;
	
	@Column(name="transmitmode")
	private int transmitMode;
	
	
	
	@Column(name="packetversion")
	private String packetversion;



	public long getId() {
		return id;
	}



	public void setId(long id) {
		this.id = id;
	}



	public String getDatetime() {
		return datetime;
	}



	public void setDatetime(String datetime) {
		this.datetime = datetime;
	}



	public String getTimezone() {
		return timezone;
	}



	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}



	public Gateway getGateway() {
		return gateway;
	}



	public void setGateway(Gateway gateway) {
		this.gateway = gateway;
	}



	public Company getCompany() {
		return company;
	}



	public void setCompany(Company company) {
		this.company = company;
	}



	public String getDate() {
		return date;
	}



	public void setDate(String date) {
		this.date = date;
	}



	public String getTime() {
		return time;
	}



	public void setTime(String time) {
		this.time = time;
	}



	public int getStoredPacket() {
		return storedPacket;
	}



	public void setStoredPacket(int storedPacket) {
		this.storedPacket = storedPacket;
	}



	public String getCreatedOn() {
		return createdOn;
	}



	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}



	public String getUtcTime() {
		return utcTime;
	}



	public void setUtcTime(String utcTime) {
		this.utcTime = utcTime;
	}



	public String getActivityType() {
		return activityType;
	}



	public void setActivityType(String activityType) {
		this.activityType = activityType;
	}



	public int getStepCount() {
		return stepCount;
	}



	public void setStepCount(int stepCount) {
		this.stepCount = stepCount;
	}



	public int getTransmitMode() {
		return transmitMode;
	}



	public void setTransmitMode(int transmitMode) {
		this.transmitMode = transmitMode;
	}



	public String getPacketversion() {
		return packetversion;
	}



	public void setPacketversion(String packetversion) {
		this.packetversion = packetversion;
	}


	
	


	
	
	
	
}
