package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

/**
 * Offlineuserdetails generated by hbm2java
 */
@Entity
@Table(name = "offlineuserdetails", uniqueConstraints = @UniqueConstraint(columnNames = "qrccode"))
public class Offlineuserdetails implements java.io.Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private Long id;

	@Column(name = "name")
	private String name;

	@Column(name = "email")
	private String email;

	@Column(name = "address")
	private String address;

	@Column(name = "devicestate_id")
	private String devicestateId;

	@Column(name = "qrccode")
	private String qrccode;

	@Column(name = "companytype")
	private String companytype;
	
	@Column(name = "status")
	private String status;

	public Offlineuserdetails() {
	}

	public Offlineuserdetails(String name, String email, String devicestateId, String qrccode, String companytype,String status) {
		this.name = name;
		this.email = email;
		this.devicestateId = devicestateId;
		this.qrccode = qrccode;
		this.companytype = companytype;
		this.status=status;
	}

	public Offlineuserdetails(String name, String email, String address, String devicestateId, String qrccode,
			String companytype,String status) {
		this.name = name;
		this.email = email;
		this.address = address;
		this.devicestateId = devicestateId;
		this.qrccode = qrccode;
		this.companytype = companytype;
		this.status=status;
	}

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getAddress() {
		return this.address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getDevicestateId() {
		return this.devicestateId;
	}

	public void setDevicestateId(String devicestateId) {
		this.devicestateId = devicestateId;
	}

	public String getQrccode() {
		return this.qrccode;
	}

	public void setQrccode(String qrccode) {
		this.qrccode = qrccode;
	}

	public String getCompanytype() {
		return this.companytype;
	}

	public void setCompanytype(String companytype) {
		this.companytype = companytype;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}
