package com.nimble.irisservices.entity;

public class eBook {
	int id,enabled, pages = 1;
	String book_name, ebook_path, image_path, language = "English", description, shortdescription, rating;
	

	public eBook() {

	}

	public eBook(int id, String book_name, String ebook_path, String image_path, int enabled, int pages,
			String language, String description, String shortdescription, String rating) {
		super();
		this.id = id;
		this.book_name = book_name;
		this.ebook_path = ebook_path;
		this.image_path = image_path;
		this.enabled = enabled;
		this.pages = pages;
		this.language = language;
		this.description = description;
		this.shortdescription = shortdescription;
		this.rating = rating;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getBook_name() {
		return book_name;
	}

	public void setBook_name(String book_name) {
		this.book_name = book_name;
	}

	public String getEbook_path() {
		return ebook_path;
	}

	public void setEbook_path(String ebook_path) {
		this.ebook_path = ebook_path;
	}

	public String getImage_path() {
		return image_path;
	}

	public void setImage_path(String image_path) {
		this.image_path = image_path;
	}

	public int getEnabled() {
		return enabled;
	}

	public void setEnabled(int enabled) {
		this.enabled = enabled;
	}

	public int getPages() {
		return pages;
	}

	public void setPages(int pages) {
		this.pages = pages;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getShortdescription() {
		return shortdescription;
	}

	public void setShortdescription(String shortdescription) {
		this.shortdescription = shortdescription;
	}

	public String getRating() {
		return rating;
	}

	public void setRating(String rating) {
		this.rating = rating;
	}

}
