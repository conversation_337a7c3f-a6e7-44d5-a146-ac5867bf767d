package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity(name="flexi_plan_history")
@Table(name="flexi_plan_history",uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class FlexiPlanHistory {

	@Id  
    @Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	
	@Column(name="gateway_id")
	private long gateway_id;
	
	@Column(name="subscription_id")
	private String subscription_id;
	
	@Column(name="plan_started_at")
	private String plan_started_at;
	
	@Column(name="current_cycle_started_at")
	private String current_cycle_started_at;
	
	@Column(name="current_cycle_end_at")
	private String current_cycle_end_at;
	
	@Column(name="current_cycle")
	private int current_cycle;
	
	@Column(name="is_paused")
	private int is_paused;
	
	@Column(name="updated_on")
	private String updated_on;
	
	@Column(name="paused_count")
	private int paused_count;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getSubscription_id() {
		return subscription_id;
	}

	public void setSubscription_id(String subscription_id) {
		this.subscription_id = subscription_id;
	}

	public String getPlan_started_at() {
		return plan_started_at;
	}

	public void setPlan_started_at(String plan_started_at) {
		this.plan_started_at = plan_started_at;
	}

	public String getCurrent_cycle_started_at() {
		return current_cycle_started_at;
	}

	public void setCurrent_cycle_started_at(String current_cycle_started_at) {
		this.current_cycle_started_at = current_cycle_started_at;
	}

	public String getCurrent_cycle_end_at() {
		return current_cycle_end_at;
	}

	public void setCurrent_cycle_end_at(String current_cycle_end_at) {
		this.current_cycle_end_at = current_cycle_end_at;
	}

	public int getCurrent_cycle() {
		return current_cycle;
	}

	public void setCurrent_cycle(int current_cycle) {
		this.current_cycle = current_cycle;
	}

	public int getIs_paused() {
		return is_paused;
	}

	public void setIs_paused(int is_paused) {
		this.is_paused = is_paused;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public int getPaused_count() {
		return paused_count;
	}

	public void setPaused_count(int paused_count) {
		this.paused_count = paused_count;
	}
	
	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}
	
}
