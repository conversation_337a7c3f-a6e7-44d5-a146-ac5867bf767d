package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="cmp_credit_monitor", uniqueConstraints =@UniqueConstraint (columnNames={"cmp_id"} ))
public class CompanyCreditMonitor implements Serializable{

	private  static final long serialVersionUID = 0l;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="cmp_id")
    private Company cmp_id;
	
	@Column(name="credit_spent")
	private int credit_spent;
	
	@Column(name="ex_credit_spent")
	private int ex_credit_spent;
	
	@Column(name="credit_assigned")
	private int credit_assigned;	
		
	@Column(name="ex_credit_assigned")
	private int ex_credit_assigned;
	
	@Column(name="resetmonthly")
	private boolean resetmonthly;
	
	public CompanyCreditMonitor()
	{
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Company getCmp_id() {
		return cmp_id;
	}

	public void setCmp_id(Company cmp_id) {
		this.cmp_id = cmp_id;
	}

	public int getCredit_spent() {
		return credit_spent;
	}

	public void setCredit_spent(int credit_spent) {
		this.credit_spent = credit_spent;
	}

	public int getEx_credit_spent() {
		return ex_credit_spent;
	}

	public void setEx_credit_spent(int ex_credit_spent) {
		this.ex_credit_spent = ex_credit_spent;
	}

	public int getCredit_assigned() {
		return credit_assigned;
	}

	public void setCredit_assigned(int credit_assigned) {
		this.credit_assigned = credit_assigned;
	}

	public int getEx_credit_assigned() {
		return ex_credit_assigned;
	}

	public void setEx_credit_assigned(int ex_credit_assigned) {
		this.ex_credit_assigned = ex_credit_assigned;
	}

	public boolean getResetmonthly() {
		return resetmonthly;
	}

	public void setResetmonthly(boolean resetmonthly) {
		this.resetmonthly = resetmonthly;
	}

}
