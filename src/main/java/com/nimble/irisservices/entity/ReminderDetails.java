package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "reminder_details")
public class ReminderDetails implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "userId")
	private long userId;

	@Column(name = "time_zone")
	private String timeZone;

	@Column(name = "display_name")
	private String displayName;

	@Column(name = "reminder_name")
	private String reminder_name;

	@Column(name = "reminder_datetime")
	private String reminderDatetime;

	@Column(name = "repeated")
	private boolean repeated = false;

	//
	@Column(name = "repeat_type")
	private String repeatType;

	@Column(name = "reminder_message")
	private String reminderMsg;

	@Column(name = "jobstate")
	private String jobState;

	@Column(name = "enable")
	private int enable = 1;

	@Column(name = "createdon")
	private String createdon; // date

	@Column(name = "updatedon")
	private String updatedon; // date

	//
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getTimeZone() {
		return timeZone;
	}

	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getRemainder_name() {
		return reminder_name;
	}

	public void setRemainder_name(String remainder_name) {
		this.reminder_name = remainder_name;
	}

	public String getRemainderDatetime() {
		return reminderDatetime;
	}

	public void setRemainderDatetime(String remainderDatetime) {
		this.reminderDatetime = remainderDatetime;
	}

	public boolean isRepeated() {
		return repeated;
	}

	public void setRepeated(boolean repeated) {
		this.repeated = repeated;
	}

	public String getRepeatType() {
		return repeatType;
	}

	public void setRepeatType(String repeatType) {
		this.repeatType = repeatType;
	}

	public String getRemainderMsg() {
		return reminderMsg;
	}

	public void setRemainderMsg(String remainderMsg) {
		this.reminderMsg = remainderMsg;
	}

	public String getJobState() {
		return jobState;
	}

	public void setJobState(String jobState) {
		this.jobState = jobState;
	}

	public String getCreatedon() {
		return createdon;
	}

	public void setCreatedon(String createdon) {
		this.createdon = createdon;
	}

	public String getUpdatedon() {
		return updatedon;
	}

	public void setUpdatedon(String updatedon) {
		this.updatedon = updatedon;
	}

	public int getEnable() {
		return enable;
	}

	public void setEnable(int enable) {
		this.enable = enable;
	}
}