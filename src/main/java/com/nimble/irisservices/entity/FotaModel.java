package com.nimble.irisservices.entity;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "fota_model_mapping")
public class FotaModel {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	public long id = 0;

	@Column(name = "fota_ver")
	public String fota_ver = "NA";

	@Transient
	public long model_id = 0;

	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "model_id")
	public AssetModel assetmodel;

	public FotaModel() {
		super();
	}

	public FotaModel(long id, String fota_ver, long model_id, AssetModel assetmodel) {
		super();
		this.id = id;
		this.fota_ver = fota_ver;
		this.model_id = model_id;
		this.assetmodel = assetmodel;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getFota_ver() {
		return fota_ver;
	}

	public void setFota_ver(String fota_ver) {
		this.fota_ver = fota_ver;
	}

	public long getModel_id() {
		return model_id;
	}

	public void setModel_id(long model_id) {
		this.model_id = model_id;
	}

	public AssetModel getAssetmodel() {
		return assetmodel;
	}

	public void setAssetmodel(AssetModel assetmodel) {
		this.assetmodel = assetmodel;
	}
}
