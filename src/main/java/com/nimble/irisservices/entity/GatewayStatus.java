package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

import com.nimble.irisservices.constant.IrisservicesConstants;


@Entity 
@Table(name="gateway_status")
public class GatewayStatus {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	private String meid = "NA";
	
	private long gateway_id = 0;
	
	private boolean streaming = false;
	
	private boolean audio_call = false;
	
	private boolean night_vision = false;
	
	private boolean auto_night_vision = false;
	
	private String quality = "sd";
	
	private int speaker_volume = 55;
	
	private boolean mic_status = false;
	
	private boolean is_online = false;
	
	private String stream_url = "NA";
	
	private int rssi = 0;
	
	private int device_angle = 0;
	
	private String created_on = IrisservicesConstants.DEFAULT_DATE;
	
	private String updated_on = IrisservicesConstants.DEFAULT_DATE;
	
	private String last_online_updated_on = IrisservicesConstants.DEFAULT_DATE;
	
	private boolean motion_detection = false;
	
	private boolean livetracking_status = false;
	
	private boolean livetracking_person = false;
	
	private boolean livetracking_boundingbox = false;
	
	private boolean livetracking_rotation = false;
	
	private boolean barking_alert = false;
	
	private boolean motion_detection_person = false;
	
	private boolean notification = false;
	
	private boolean screen_flip = false;
	
	private int night_vision_mode = 1;
	
	private boolean noise_detection = false;
	
	private String detection_sensitivity = "Medium";
	
	private String serial_number = "NA";
	
	private boolean continuous_playback = true;
	
	private String noise_sensitivity = "Medium";
	
	private int recording_time = 120;
	
	private int alarm_interval = 120;

	private boolean event_playback;
	
	private boolean pet_detection;
	
	private boolean throwing_beep;
	
	private boolean temp_unit_celsius = false;
	
	private boolean vehicle_detection = false;
	
	private String device_unique_id = "NA";
	
	private int treat_count = 1;
	
	private boolean aitreat_onoff = false;
	
	private int aitreat_interval = 60;
	
	private int aitreat_maxcount = 10;
	
	private boolean schedule_enable = true;
	
	private boolean auto_tracking = false;
	
	private int operation_mode = 0;
	
	private int light_time_id = 30;
	
	private boolean is_activated = false;
	
	private String videoencoding_format = "H265";
	
	private int flicker_level = 0;
	
	private boolean smart_light_detection = false;
	
	private String last_device_reset = IrisservicesConstants.DEFAULT_DATE;

	private boolean is_microphone_enabled  = true;

	private boolean is_recordvideo_enabled = true;

	private boolean is_speaker_enabled = true;

	private boolean human_detection_day = false;

	private boolean human_detection_night = false;

	private boolean is_auto_sensitive_enabled = false;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public boolean isStreaming() {
		return streaming;
	}

	public void setStreaming(boolean streaming) {
		this.streaming = streaming;
	}

	public boolean isAudio_call() {
		return audio_call;
	}

	public void setAudio_call(boolean audio_call) {
		this.audio_call = audio_call;
	}

	public boolean isNight_vision() {
		return night_vision;
	}

	public void setNight_vision(boolean night_vision) {
		this.night_vision = night_vision;
	}

	public String getQuality() {
		return quality;
	}

	public void setQuality(String quality) {
		this.quality = quality;
	}

	public int getDevice_angle() {
		return device_angle;
	}

	public void setDevice_angle(int device_angle) {
		this.device_angle = device_angle;
	}

	public String getCreated_on() {
		return created_on;
	}

	public void setCreated_on(String created_on) {
		this.created_on = created_on;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public String getStream_url() {
		return stream_url;
	}

	public void setStream_url(String stream_url) {
		this.stream_url = stream_url;
	}

	public boolean isAuto_night_vision() {
		return auto_night_vision;
	}

	public void setAuto_night_vision(boolean auto_night_vision) {
		this.auto_night_vision = auto_night_vision;
	}

	public boolean isIs_online() {
		return is_online;
	}

	public void setIs_online(boolean is_online) {
		this.is_online = is_online;
	}

	public String getLast_online_updated_on() {
		return last_online_updated_on;
	}

	public void setLast_online_updated_on(String last_online_updated_on) {
		this.last_online_updated_on = last_online_updated_on;
	}

	public int getRssi() {
		return rssi;
	}

	public void setRssi(int rssi) {
		this.rssi = rssi;
	}

	public int getSpeaker_volume() {
		return speaker_volume;
	}

	public void setSpeaker_volume(int speaker_volume) {
		this.speaker_volume = speaker_volume;
	}

	public boolean isMic_status() {
		return mic_status;
	}

	public void setMic_status(boolean mic_status) {
		this.mic_status = mic_status;
	}

	public boolean isMotion_detection() {
		return motion_detection;
	}

	public void setMotion_detection(boolean motion_detection) {
		this.motion_detection = motion_detection;
	}

	public boolean isLivetracking_status() {
		return livetracking_status;
	}

	public void setLivetracking_status(boolean livetracking_status) {
		this.livetracking_status = livetracking_status;
	}

	public boolean isLivetracking_person() {
		return livetracking_person;
	}

	public void setLivetracking_person(boolean livetracking_person) {
		this.livetracking_person = livetracking_person;
	}

	public boolean isLivetracking_boundingbox() {
		return livetracking_boundingbox;
	}

	public void setLivetracking_boundingbox(boolean livetracking_boundingbox) {
		this.livetracking_boundingbox = livetracking_boundingbox;
	}

	public boolean isLivetracking_rotation() {
		return livetracking_rotation;
	}

	public void setLivetracking_rotation(boolean livetracking_rotation) {
		this.livetracking_rotation = livetracking_rotation;
	}

	public boolean isBarking_alert() {
		return barking_alert;
	}

	public void setBarking_alert(boolean barking_alert) {
		this.barking_alert = barking_alert;
	}

	public boolean isMotion_detection_person() {
		return motion_detection_person;
	}

	public void setMotion_detection_person(boolean motion_detection_persion) {
		this.motion_detection_person = motion_detection_persion;
	}

	public boolean isNotification() {
		return notification;
	}

	public void setNotification(boolean notification) {
		this.notification = notification;
	}

	public boolean isScreen_flip() {
		return screen_flip;
	}

	public void setScreen_flip(boolean screen_flip) {
		this.screen_flip = screen_flip;
	}

	public int getNight_vision_mode() {
		return night_vision_mode;
	}

	public void setNight_vision_mode(int night_vision_mode) {
		this.night_vision_mode = night_vision_mode;
	}

	public boolean isNoise_detection() {
		return noise_detection;
	}

	public void setNoise_detection(boolean noise_detection) {
		this.noise_detection = noise_detection;
	}

	public String getDetection_sensitivity() {
		return detection_sensitivity;
	}

	public void setDetection_sensitivity(String detection_sensitivity) {
		this.detection_sensitivity = detection_sensitivity;
	}

	public String getSerial_number() {
		return serial_number;
	}

	public void setSerial_number(String serial_number) {
		this.serial_number = serial_number;
	}

	public boolean isContinuous_playback() {
		return continuous_playback;
	}

	public void setContinuous_playback(boolean continuous_playback) {
		this.continuous_playback = continuous_playback;
	}

	public String getNoise_sensitivity() {
		return noise_sensitivity;
	}

	public void setNoise_sensitivity(String noise_sensitivity) {
		this.noise_sensitivity = noise_sensitivity;
	}

	public int getRecording_time() {
		return recording_time;
	}

	public void setRecording_time(int recording_time) {
		this.recording_time = recording_time;
	}

	public int getAlarm_interval() {
		return alarm_interval;
	}

	public void setAlarm_interval(int alarm_interval) {
		this.alarm_interval = alarm_interval;
	}

	public boolean isEvent_playback() {
		return event_playback;
	}

	public void setEvent_playback(boolean event_playback) {
		this.event_playback = event_playback;
	}

	public boolean isPet_detection() {
		return pet_detection;
	}

	public void setPet_detection(boolean pet_detection) {
		this.pet_detection = pet_detection;
	}

	public boolean isThrowing_beep() {
		return throwing_beep;
	}

	public void setThrowing_beep(boolean throwing_beep) {
		this.throwing_beep = throwing_beep;
	}

	public boolean isTemp_unit_celsius() {
		return temp_unit_celsius;
	}

	public void setTemp_unit_celsius(boolean temp_unit_celsius) {
		this.temp_unit_celsius = temp_unit_celsius;
	}

	public boolean isVehicle_detection() {
		return vehicle_detection;
	}

	public void setVehicle_detection(boolean vehicle_detection) {
		this.vehicle_detection = vehicle_detection;
	}

	public String getDevice_unique_id() {
		return device_unique_id;
	}

	public void setDevice_unique_id(String device_unique_id) {
		this.device_unique_id = device_unique_id;
	}

	public GatewayStatus() {
		super();
	}
	
	public GatewayStatus(long id, String meid, long gateway_id, boolean streaming, boolean audio_call,
			boolean night_vision, String quality, int device_angle, String created_on, String updated_on) {
		super();
		this.id = id;
		this.meid = meid;
		this.gateway_id = gateway_id;
		this.streaming = streaming;
		this.audio_call = audio_call;
		this.night_vision = night_vision;
		this.quality = quality;
		this.device_angle = device_angle;
		this.created_on = created_on;
		this.updated_on = updated_on;
	}

	public int getTreat_count() {
		return treat_count;
	}

	public void setTreat_count(int treat_count) {
		this.treat_count = treat_count;
	}

	public boolean isAitreat_onoff() {
		return aitreat_onoff;
	}

	public void setAitreat_onoff(boolean aitreat_onoff) {
		this.aitreat_onoff = aitreat_onoff;
	}

	public int getAitreat_interval() {
		return aitreat_interval;
	}

	public void setAitreat_interval(int aitreat_interval) {
		this.aitreat_interval = aitreat_interval;
	}

	public int getAitreat_maxcount() {
		return aitreat_maxcount;
	}

	public void setAitreat_maxcount(int aitreat_maxcount) {
		this.aitreat_maxcount = aitreat_maxcount;
	}

	public boolean isSchedule_enable() {
		return schedule_enable;
	}
	
	public void setSchedule_enable(boolean schedule_enable) {
		this.schedule_enable = schedule_enable;
	}

	public boolean isAuto_tracking() {
		return auto_tracking;
	}

	public void setAuto_tracking(boolean auto_tracking) {
		this.auto_tracking = auto_tracking;
	}

	public int getOperation_mode() {
		return operation_mode;
	}

	public void setOperation_mode(int operation_mode) {
		this.operation_mode = operation_mode;
	}

	public int getLight_time_id() {
		return light_time_id;
	}

	public void setLight_time_id(int light_time_id) {
		this.light_time_id = light_time_id;
	}

	public boolean isIs_activated() {
		return is_activated;
	}

	public void setIs_activated(boolean is_activated) {
		this.is_activated = is_activated;
	}

	public String getVideoencoding_format() {
		return videoencoding_format;
	}

	public void setVideoencoding_format(String videoencoding_format) {
		this.videoencoding_format = videoencoding_format;
	}

	public int getFlicker_level() {
		return flicker_level;
	}

	public void setFlicker_level(int flicker_level) {
		this.flicker_level = flicker_level;
	}

	public boolean getSmart_light_detection() {
		return smart_light_detection;
	}

	public void setSmart_light_detection(boolean smart_light_detection) {
		this.smart_light_detection = smart_light_detection;
	}

	public String getLast_device_reset() {
		return last_device_reset;
	}

	public void setLast_device_reset(String last_device_reset) {
		this.last_device_reset = last_device_reset;
	}

	public boolean isSmart_light_detection() {
		return smart_light_detection;
	}

	public boolean getIs_microphone_enabled() {
		return is_microphone_enabled;
	}

	public void setIs_microphone_enabled(boolean is_microphone_enabled) {
		this.is_microphone_enabled = is_microphone_enabled;
	}

	public boolean getIs_recordvideo_enabled() {
		return is_recordvideo_enabled;
	}

	public void setIs_recordvideo_enabled(boolean is_recordvideo_enabled) {
		this.is_recordvideo_enabled = is_recordvideo_enabled;
	}

	public boolean getIs_speaker_enabled() {
		return is_speaker_enabled;
	}

	public void setIs_speaker_enabled(boolean is_speaker_enabled) {
		this.is_speaker_enabled = is_speaker_enabled;
	}

	public boolean getHuman_detection_day() {
		return human_detection_day;
	}

	public void setHuman_detection_day(boolean human_detection_day) {
		this.human_detection_day = human_detection_day;
	}

	public boolean getHuman_detection_night() {
		return human_detection_night;
	}

	public void setHuman_detection_night(boolean human_detection_night) {
		this.human_detection_night = human_detection_night;
	}

	public boolean getAuto_sensitive_enabled() {
		return is_auto_sensitive_enabled;
	}

	public void setAuto_sensitive_enabled(boolean auto_sensitive_enabled) {
		this.is_auto_sensitive_enabled = auto_sensitive_enabled;
	}
}
