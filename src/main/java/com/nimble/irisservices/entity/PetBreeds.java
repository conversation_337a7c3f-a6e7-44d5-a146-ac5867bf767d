package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "petbreeds")
public class PetBreeds implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "speciesid")
	private PetSpecies petSpecies;

	@Column(name = "breedname")
	private String breedName;

	@Transient
	private long speciesId;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public PetSpecies getPetSpecies() {
		return petSpecies;
	}

	public void setPetSpecies(PetSpecies petSpecies) {
		this.petSpecies = petSpecies;
	}

	public String getBreedName() {
		return breedName;
	}

	public void setBreedName(String breedName) {
		this.breedName = breedName;
	}

	public PetBreeds() {
		super();
		// TODO Auto-generated constructor stub
	}

	public PetBreeds(long id, PetSpecies petSpecies, String breedName) {
		super();
		this.id = id;
		this.petSpecies = petSpecies;
		this.breedName = breedName;
	}

	public PetBreeds(String breedName) {
		super();
		this.breedName = breedName;
	}

	public PetBreeds(PetSpecies petSpecies, String breedName) {
		super();
		this.petSpecies = petSpecies;
		this.breedName = breedName;
	}
	
	public PetBreeds(String breedName, int speciesId) {
		super();
		this.breedName = breedName;
		this.speciesId = speciesId;
	}

	public long getSpeciesId() {
		return speciesId;
	}

	public void setSpeciesId(int speciesId) {
		this.speciesId = speciesId;
	}
	
	
	
	
	
	

}
