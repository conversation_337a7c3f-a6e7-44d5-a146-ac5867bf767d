package com.nimble.irisservices.entity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "remove_gateway_type")
public class RemoveGatewayType {
	
	
	@Id
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	
	private String remove_reason = "NA";
	
	private boolean enable = false;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getRemove_reason() {
		return remove_reason;
	}

	public void setRemove_reason(String remove_reason) {
		this.remove_reason = remove_reason;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public RemoveGatewayType() {
		super();
	}
	
	public RemoveGatewayType(long id, String remove_reason, boolean enable) {
		super();
		this.id = id;
		this.remove_reason = remove_reason;
		this.enable = enable;
	}
	
	

}
