package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "adventurereport")
public class AdventureReport implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name="startdatetime")
	private Timestamp startdatetime;
	
	@Column(name="enddatetime")
	private Timestamp enddatetime;
	
	@Column(name="lat")
	private double lat;
	
	@Column(name="lon")
	private double lon;

	@Column(name="gateway_id")
	private long gatewayId;

	public AdventureReport ()
	{
		
	}
	
	public AdventureReport (Timestamp startdatetime,Timestamp enddatetime,double lat,double lon,long gatewayId){
		this.startdatetime = startdatetime;
		this.enddatetime = enddatetime;
		this.lat = lat;
		this.lon = lon;
		this.gatewayId = gatewayId;		
	}
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Timestamp getStartdatetime() {
		return startdatetime;
	}

	public void setStartdatetime(Timestamp startdatetime) {
		this.startdatetime = startdatetime;
	}

	public Timestamp getEnddatetime() {
		return enddatetime;
	}

	public void setEnddatetime(Timestamp enddatetime) {
		this.enddatetime = enddatetime;
	}

	public double getLat() {
		return lat;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public double getLon() {
		return lon;
	}

	public void setLon(double lon) {
		this.lon = lon;
	}

	public long getGatewayId() {
		return gatewayId;
	}

	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}
	
	
}
