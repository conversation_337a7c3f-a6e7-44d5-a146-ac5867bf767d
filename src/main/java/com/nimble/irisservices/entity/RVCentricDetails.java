package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "rv_centric_details")
public class RVCentricDetails implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "os")
	private String os;
	
	@Column(name = "category")
	private String category;
	
	@Column(name = "title")
	private String title;
	
	@Column(name = "android_mediapath")
	private String android_mediapath;
	
	@Column(name = "ios_mediapath")
	private String ios_mediapath;
	
	@Column(name = "description")
	private String description;
	
	@Column(name = "url")
	private String url;

	@Column(name = "orderno")
	private int orderno;

	@Column(name = "enable")
	private boolean enable;

	@Column(name = "expiredon")
	private String expiredon = "1753-01-01 00:00:00";;
	
	@Column(name = "updatedon")
	private String updatedon = "1753-01-01 00:00:00";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getOs() {
		return os;
	}

	public void setOs(String os) {
		this.os = os;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getAndroid_mediapath() {
		return android_mediapath;
	}

	public void setAndroid_mediapath(String android_mediapath) {
		this.android_mediapath = android_mediapath;
	}

	public String getIos_mediapath() {
		return ios_mediapath;
	}

	public void setIos_mediapath(String ios_mediapath) {
		this.ios_mediapath = ios_mediapath;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public int getOrderno() {
		return orderno;
	}

	public void setOrderno(int orderno) {
		this.orderno = orderno;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getExpiredon() {
		return expiredon;
	}

	public void setExpiredon(String expiredon) {
		this.expiredon = expiredon;
	}

	public String getUpdatedon() {
		return updatedon;
	}

	public void setUpdatedon(String updatedon) {
		this.updatedon = updatedon;
	};
	
}
