package com.nimble.irisservices.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;


@Entity
@Table(name = "manageplan_list")
public class ManageList {

    @Id
    @GenericGenerator(name = "gen", strategy = "identity")
    @GeneratedValue(generator = "gen")
    private long id;
    String name="NA";
    boolean enable=true;
    boolean is_expand=true;
    boolean is_flexi=false;
    @Transient
    boolean is_listAvailable = false;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isIs_expand() {
        return is_expand;
    }

    public void setIs_expand(boolean is_expand) {
        this.is_expand = is_expand;
    }

    public boolean isIs_listAvailable() {
        return is_listAvailable;
    }

    public void setIs_listAvailable(boolean is_listAvailable) {
        this.is_listAvailable = is_listAvailable;
    }

    public boolean isIs_flexi() {
        return is_flexi;
    }

    public void setIs_flexi(boolean is_flexi) {
        this.is_flexi = is_flexi;
    }
}
