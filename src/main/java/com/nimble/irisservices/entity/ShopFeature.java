package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "shop_feature")
public class ShopFeature {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	private String title;
	
	private String btn_name;
	
	private String action_url;
	
	private boolean external_redirect;
	
	private String img_url;
	
	private boolean enable = false;
	
	private String version = "V1";
	
	private String category = "Dog";
	
	private int order_no = 1;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getBtn_name() {
		return btn_name;
	}

	public void setBtn_name(String btn_name) {
		this.btn_name = btn_name;
	}

	public String getAction_url() {
		return action_url;
	}

	public void setAction_url(String action_url) {
		this.action_url = action_url;
	}

	public boolean isExternal_redirect() {
		return external_redirect;
	}

	public void setExternal_redirect(boolean external_redirect) {
		this.external_redirect = external_redirect;
	}

	public String getImg_url() {
		return img_url;
	}

	public void setImg_url(String img_url) {
		this.img_url = img_url;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public int getOrder_no() {
		return order_no;
	}

	public void setOrder_no(int order_no) {
		this.order_no = order_no;
	}

	public ShopFeature() {
		super();
	}
	
	public ShopFeature(long id, String title, String btn_name, String action_url, boolean external_redirect) {
		super();
		this.id = id;
		this.title = title;
		this.btn_name = btn_name;
		this.action_url = action_url;
		this.external_redirect = external_redirect;
	}
	
	
	
}
