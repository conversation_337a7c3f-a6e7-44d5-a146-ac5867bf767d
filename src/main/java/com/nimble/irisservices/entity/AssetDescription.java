package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;



@Entity 
@Table(name="assetdescription", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class AssetDescription implements Serializable{

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	
	@Column(name ="field_des_1")
	private String field_des_1;

	@Column(name ="field_des_2")
	private String field_des_2;

	@Column(name ="field_des_3")
	private String field_des_3;

	@Column(name ="field_des_4")
	private String field_des_4;
	
	@Column(name ="field_des_5")
	private String field_des_5;
	
	@Column(name ="field_des_6")
	private String field_des_6;
	
	@Column(name ="field_des_7")
	private String field_des_7;
	
	@Column(name ="field_des_8")
	private String field_des_8;
	
	@Column(name ="field_des_9")
	private String field_des_9;

	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name="cmp_id")
	//@JsonBackReference
    private Company company;

	public long getId() {
		return id;
	}

	public String getField_des_1() {
		return field_des_1;
	}

	public String getField_des_2() {
		return field_des_2;
	}

	public String getField_des_3() {
		return field_des_3;
	}

	public String getField_des_4() {
		return field_des_4;
	}

	public String getField_des_5() {
		return field_des_5;
	}

	public String getField_des_6() {
		return field_des_6;
	}

	public String getField_des_7() {
		return field_des_7;
	}
	
	public String getField_des_8() {
		return field_des_8;
	}
	
	public String getField_des_9() {
		return field_des_9;
	}

	public void setField_des_9(String field_des_9) {
		this.field_des_9 = field_des_9;
	}

	public Company getCompany() {
		return company;
	}
	
	
}
