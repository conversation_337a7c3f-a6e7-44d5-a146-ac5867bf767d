package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name="device_replaced", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class DeviceReplaced implements Serializable{

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen",strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	
	@Column(name = "user_id")
	private long userId = 0;
	
	@Column(name = "meid")
	private String meid = "NA";
			
	@Column(name = "action")
	private int isReplaced = 0;
	
	/*
	 *  0 default
	 *  1 Replace 
	 *  2 Recall
	 *  3 Replacement Success
	 *  4 Recall Success
	 */

	@Column(name = "inserted_date")
	private String insertedDate = "1753-01-01 00:00:00";

	@Column(name = "updated_date")
	private String updatedDate = "1753-01-01 00:00:00";

	@Column(name = "monitorType")
	private long monitorType = 1;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public int getIsReplaced() {
		return isReplaced;
	}

	public void setIsReplaced(int isReplaced) {
		this.isReplaced = isReplaced;
	}

	public String getInsertedDate() {
		return insertedDate;
	}

	public void setInsertedDate(String insertedDate) {
		this.insertedDate = insertedDate;
	}

	public String getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(String updatedDate) {
		this.updatedDate = updatedDate;
	}

	public long getMonitorType() {
		return monitorType;
	}

	public void setMonitorType(long monitorType) {
		this.monitorType = monitorType;
	}
}
