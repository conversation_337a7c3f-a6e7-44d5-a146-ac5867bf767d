package com.nimble.irisservices.entity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "gateway_to_alexa")
public class GatewayToAlexa {

	@Id
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id = 0;
	
	private long user_id = 0;
	
	private long gateway_id = 0;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public GatewayToAlexa() {
		super();	
	}
	
	public GatewayToAlexa(long id, long user_id, long gateway_id) {
		super();
		this.id = id;
		this.user_id = user_id;
		this.gateway_id = gateway_id;
	}
	
	
	
	
}
