package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "product_subscription")
public class ProductSubscription {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	long id;
	
	long order_id = 0;
	
	String sku = "NA";
	
	String plan_id = "NA";
	
	boolean enable = false;
	
	String createdOn = "1753-11-11 11:11:11";
	
	String updatedon = "1753-11-11 11:11:11";
	
	String status = "NA";
	
	boolean is_subscription_activated = false;
	
	long user_id = 0;
	
	String order_date = "1753-11-11 11:11:11";
	
	long subscription_period_days = 0;
	
	String order_channel = "NA";
	
	boolean order_mapping_status = false;
	
	boolean active_subscription = false;
	
//	String subscription_from = "NA";
//	
//	String meta_data = "NA";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getOrder_id() {
		return order_id;
	}

	public void setOrder_id(long order_id) {
		this.order_id = order_id;
	}

	public String getSku() {
		return sku;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public String getPlan_id() {
		return plan_id;
	}

	public void setPlan_id(String plan_id) {
		this.plan_id = plan_id;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}

	public String getUpdatedon() {
		return updatedon;
	}

	public void setUpdatedon(String updatedon) {
		this.updatedon = updatedon;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public boolean isIs_subscription_activated() {
		return is_subscription_activated;
	}

	public void setIs_subscription_activated(boolean is_subscription_activated) {
		this.is_subscription_activated = is_subscription_activated;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getOrder_date() {
		return order_date;
	}

	public void setOrder_date(String order_date) {
		this.order_date = order_date;
	}

	public long getSubscription_period_days() {
		return subscription_period_days;
	}

	public void setSubscription_period_days(long subscription_period_days) {
		this.subscription_period_days = subscription_period_days;
	}

	public String getOrder_channel() {
		return order_channel;
	}

	public void setOrder_channel(String order_channel) {
		this.order_channel = order_channel;
	}

	public boolean isOrder_mapping_status() {
		return order_mapping_status;
	}

	public void setOrder_mapping_status(boolean order_mapping_status) {
		this.order_mapping_status = order_mapping_status;
	}

//	public String getSubscription_from() {
//		return subscription_from;
//	}
//
//	public void setSubscription_from(String subscription_from) {
//		this.subscription_from = subscription_from;
//	}
//
//	public String getMeta_data() {
//		return meta_data;
//	}
//
//	public void setMeta_data(String meta_data) {
//		this.meta_data = meta_data;
//	}

	public boolean isActive_subscription() {
		return active_subscription;
	}

	public void setActive_subscription(boolean active_subscription) {
		this.active_subscription = active_subscription;
	}

	public ProductSubscription() {
		super();
	}
	
	public ProductSubscription(long id, long order_id, String sku, String plan_id, boolean enable, String createdOn,
			String updatedon, String status, boolean is_subscription_activated, long user_id, String order_date,
			long subscription_period_days, String order_channel, boolean order_mapping_status
//			, String subscription_from,
//			String meta_data
			) {
		super();
		this.id = id;
		this.order_id = order_id;
		this.sku = sku;
		this.plan_id = plan_id;
		this.enable = enable;
		this.createdOn = createdOn;
		this.updatedon = updatedon;
		this.status = status;
		this.is_subscription_activated = is_subscription_activated;
		this.user_id = user_id;
		this.order_date = order_date;
		this.subscription_period_days = subscription_period_days;
		this.order_channel = order_channel;
		this.order_mapping_status = order_mapping_status;
//		this.subscription_from = subscription_from;
//		this.meta_data = meta_data;
	}

	
	
	
}
