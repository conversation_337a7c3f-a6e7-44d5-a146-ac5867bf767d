package com.nimble.irisservices.entity;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "batterylookup", uniqueConstraints = @UniqueConstraint(columnNames = { "min", "max", "batteryvalue",
		"probecategory", "ischargingformula" }))
public class BatteryLookup {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "min")
	private int min;

	@Column(name = "max")
	private int max;

	@Column(name = "batteryvalue")
	private float batteryvalue;

	@Column(name = "ischargingformula")
	private boolean ischargingformula;

	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "probecategory")
	private ProbeCategory probecategory;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public int getMin() {
		return min;
	}

	public void setMin(int min) {
		this.min = min;
	}

	public int getMax() {
		return max;
	}

	public void setMax(int max) {
		this.max = max;
	}

	public float getBatteryvalue() {
		return batteryvalue;
	}

	public void setBatteryvalue(float batteryvalue) {
		this.batteryvalue = batteryvalue;
	}

	public boolean isIschargingformula() {
		return ischargingformula;
	}

	public void setIschargingformula(boolean ischargingformula) {
		this.ischargingformula = ischargingformula;
	}

	public ProbeCategory getProbecategory() {
		return probecategory;
	}

	public void setProbecategory(ProbeCategory probecategory) {
		this.probecategory = probecategory;
	}

}
