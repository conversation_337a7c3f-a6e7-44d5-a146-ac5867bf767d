package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;



@Entity 
@Table(name="credit_points", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class CreditPoints implements Serializable{
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	@Column(name="name")
	private String name;
	@Column(name="credits")
	private long credits;
	
	
	public CreditPoints() {
		super();
		// TODO Auto-generated constructor stub
	}

	public CreditPoints(long id, String name, long credits) {
		super();
		this.id = id;
		this.name = name;
		this.credits = credits;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public long getCredits() {
		return credits;
	}

	public void setCredits(long credits) {
		this.credits = credits;
	}

	
}
