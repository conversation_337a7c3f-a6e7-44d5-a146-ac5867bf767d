package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="plivo_data", uniqueConstraints =@UniqueConstraint (columnNames={"id"} ))
public class PlivoData {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@Column(name="phoneno")
	private String phoneno;

	@Column(name="message")
	private String message;

	@Column(name="companyid")
	private String companyid;

	@Column(name="companyname")
	private String companyname;

	@Column(name="appname")
	private String appname;

	@Column(name="istdatetime")
	private String istdatetime;

	@Column(name="utcdatetime")
	private String utcdatetime;

	@Column(name="type")
	private String type;
	
	@Column(name="transport_type")
	private int transport_type;
	
	public PlivoData() {
		super();
		// TODO Auto-generated constructor stub
	}

	public PlivoData(String phoneno, String message, String companyid,String companyname, 
			String appname, String istdatetime, String utcdatetime,String type,int transport_type) {
		super();
		this.phoneno = phoneno;
		this.message = message;
		this.companyid = companyid;
		this.companyname = companyname;
		this.appname = appname;
		this.istdatetime = istdatetime;
		this.utcdatetime = utcdatetime;
		this.type		 = type;
		this.transport_type=transport_type;
		

	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getPhoneno() {
		return phoneno;
	}

	public void setPhoneno(String phoneno) {
		this.phoneno = phoneno;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getCompanyid() {
		return companyid;
	}

	public void setCompanyid(String companyid) {
		this.companyid = companyid;
	}

	public String getCompanyname() {
		return companyname;
	}

	public void setCompanyname(String companyname) {
		this.companyname = companyname;
	}

	public String getAppname() {
		return appname;
	}

	public void setAppname(String appname) {
		this.appname = appname;
	}

	public String getIstdatetime() {
		return istdatetime;
	}

	public void setIstdatetime(String istdatetime) {
		this.istdatetime = istdatetime;
	}

	public String getUtcdatetime() {
		return utcdatetime;
	}

	public void setUtcdatetime(String utcdatetime) {
		this.utcdatetime = utcdatetime;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}




}
