package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="furbitgatewayreport", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class FurbitGatewayReport  implements Serializable{
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name="version")
	private String version;
	
	@Column(name="datetime")
	private Timestamp datetime;
	
	@Column(name="date")
	private Date date;
	
	@Column(name="time")
	private Time time;
	
	@Column(name="timezone")
	private String timezone;
	
	@Column(name="battery")
	private int battery;
	
	@Column(name="eventid1")
	private String eventid1;
	
	@Column(name="eventid2")
	private String eventid2;
	
	@Column(name="iostatus")
	private int iostatus;

	@Column(name="gpsmode")
	private int gpsmode;

	@Column(name="txnmode")
	private int txnmode;
	
	@Column(name="lat")
	private double lat;
	
	@Column(name="latdir")
	private String latdir;
	
	@Column(name="lon")
	private double lon;
	
	@Column(name="londir")
	private String londir;

	@Column(name="gpsstatus")
	private String gpsstatus;
	
	@Column(name="gpsinfo")
	private String gpsinfo;
	
	@Column(name="rawrssi")
	private int rawrssi;
	
	@Column(name="rssi")
	private String rssi;

	@Column(name="lastpkt")
	private int lastpkt;
		
    @ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="gateway_id")
    private Gateway gateway;	
   
	public FurbitGatewayReport() {
		super();
	}

	public FurbitGatewayReport(String version, Timestamp datetime, Date date, Time time,	String timezone, double lat, 
			String latdir, double lon,String londir,String gpsstatus, String gpsinfo, String eventid1,
			String eventid2, int iostatus, int battery, int rawrssi, String rssi, int gpsmode,int txnmode,int lastpkt, Gateway gateway) {
		super();
		this.version = version;
		this.datetime = datetime;
		this.date = date;
		this.time = time;
		this.timezone = timezone;
		this.lat = lat;
		this.latdir = latdir;
		this.lon = lon;
		this.londir = londir;
		this.gpsstatus = gpsstatus;
		this.gpsinfo = gpsinfo;
		this.iostatus = iostatus;
		this.battery = battery;
		this.eventid1 = eventid1;
		this.eventid2 = eventid2;
		this.rawrssi = rawrssi;
		this.rssi = rssi;
		this.lastpkt  = lastpkt;
		this.gpsmode = gpsmode;
		this.txnmode = txnmode;
		this.gateway = gateway;
	}

	public String getEventid1() {
		return eventid1;
	}

	public String getEventid2() {
		return eventid2;
	}

	public long getId() {
		return id;
	}

	public String getVersion() {
		return version;
	}

	public Timestamp getDatetime() {
		return datetime;
	}

	public Date getDate() {
		return date;
	}

	public Time getTime() {
		return time;
	}

	public String getTimezone() {
		return timezone;
	}

	public double getLat() {
		return lat;
	}

	public String getLatdir() {
		return latdir;
	}

	public double getLon() {
		return lon;
	}

	public String getLondir() {
		return londir;
	}

	public String getGpsstatus() {
		return gpsstatus;
	}

	public String getGpsinfo() {
		return gpsinfo;
	}

	public int getIostatus() {
		return iostatus;
	}

	public int getBattery() {
		return battery;
	}

	public int getRawrssi() {
		return rawrssi;
	}

	public String getRssi() {
		return rssi;
	}

	public Gateway getGateway() {
		return gateway;
	}

	public int getGpsmode() {
		return gpsmode;
	}

	public int getLastpkt() {
		return lastpkt;
	}
	
	public int getTxnmode() {
		return txnmode;
	}
   
}
