package com.nimble.irisservices.entity;

import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="furbitlastgatewayreport", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class FurbitLastGatewayReport {
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name="version")
	private String version;
	
	@Column(name="datetime")
	private Timestamp datetime;
	
	@Column(name="date")
	private Date date;
	
	@Column(name="time")
	private Time time;
	
	@Column(name="timezone")
	private String timezone;
	
	@Column(name="battery")
	private int battery;
	
	@Column(name="eventid1")
	private String eventid1;
	
	@Column(name="eventid2")
	private String eventid2;
	
	@Column(name="iostatus")
	private int iostatus;

	@Column(name="gpsmode")
	private int gpsmode;

	@Column(name="txnmode")
	private int txnMode;
	
	@Column(name="lat")
	private double lat;
	
	@Column(name="latdir")
	private String latdir;
	
	@Column(name="lon")
	private double lon;
	
	@Column(name="londir")
	private String londir;

	@Column(name="gpsstatus")
	private String gpsstatus;
	
	@Column(name="gpsinfo")
	private String gpsinfo;
	
	@Column(name="rawrssi")
	private int rawrssi;
	
	@Column(name="rssi")
	private String rssi;

	@Column(name="lastpkt")
	private int lastpkt;
		
    @ManyToOne(cascade = CascadeType.ALL,fetch = FetchType.EAGER)
	@JoinColumn(name="gateway_id")
    private Gateway gateway;	

	@Column(name="wifirssi")
	private int wifirssi;
	
	@Column(name="wifissid")
	private int wifissid;

	@Column(name="prptinterval")
	private int prptinterval;
	
    @Column(name="zonename")
	private String zonename;
	
	@Column(name="zonelevel")
	private int zonelevel;
	
	@Column(name="zonedesc")
	private String zonedesc;
	
	@Column(name="lastgpsdatetime")
	private Timestamp lastgpsdatetime;
	
	@Column(name="lastvalidaddress")
	private String lastvalidaddress;
	
	@Column(name="lastvalidlat")
	private double lastvalidlat;
	
	@Column(name="lastvalidlon")
	private double lastvalidlon;
	
	public FurbitLastGatewayReport() {
		super();
	}

	public FurbitLastGatewayReport(String version, Timestamp datetime, Date date, Time time,	String timezone, double lat, 
			String latdir, double lon,String londir,String gpsstatus, String gpsinfo, String eventid1,
			String eventid2, int iostatus, int battery, int rawrssi, String rssi, int gpsmode,int txnMode,int lastpkt, 
			Gateway gateway,String zonename, int zonelevel, String zonedesc,double lastvalidlat,double lastvalidlon,
			String lastvalidaddress,Timestamp lastgpsdatetime) {
		super();
		this.version = version;
		this.datetime = datetime;
		this.date = date;
		this.time = time;
		this.timezone = timezone;
		this.lat = lat;
		this.latdir = latdir;
		this.lon = lon;
		this.londir = londir;
		this.gpsstatus = gpsstatus;
		this.gpsinfo = gpsinfo;
		this.iostatus = iostatus;
		this.battery = battery;
		this.eventid1 = eventid1;
		this.eventid2 = eventid2;
		this.rawrssi = rawrssi;
		this.rssi = rssi;
		this.lastpkt  = lastpkt;
		this.gpsmode = gpsmode;
		this.gateway = gateway;
		this.lastvalidaddress = lastvalidaddress;
		this.lastgpsdatetime = lastgpsdatetime;
		this.lastvalidlat = lastvalidlat;
		this.lastvalidlon = lastvalidlon;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public Timestamp getDatetime() {
		return datetime;
	}

	public void setDatetime(Timestamp datetime) {
		this.datetime = datetime;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Time getTime() {
		return time;
	}

	public void setTime(Time time) {
		this.time = time;
	}

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}

	public int getBattery() {
		return battery;
	}

	public void setBattery(int battery) {
		this.battery = battery;
	}

	public String getEventid1() {
		return eventid1;
	}

	public void setEventid1(String eventid1) {
		this.eventid1 = eventid1;
	}

	public String getEventid2() {
		return eventid2;
	}

	public void setEventid2(String eventid2) {
		this.eventid2 = eventid2;
	}

	public int getIostatus() {
		return iostatus;
	}

	public void setIostatus(int iostatus) {
		this.iostatus = iostatus;
	}

	public int getGpsmode() {
		return gpsmode;
	}

	public void setGpsmode(int gpsmode) {
		this.gpsmode = gpsmode;
	}

	public int getTxnMode() {
		return txnMode;
	}

	public void setTxnMode(int txnMode) {
		this.txnMode = txnMode;
	}

	public double getLat() {
		return lat;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public String getLatdir() {
		return latdir;
	}

	public void setLatdir(String latdir) {
		this.latdir = latdir;
	}

	public double getLon() {
		return lon;
	}

	public void setLon(double lon) {
		this.lon = lon;
	}

	public String getLondir() {
		return londir;
	}

	public void setLondir(String londir) {
		this.londir = londir;
	}

	public String getGpsstatus() {
		return gpsstatus;
	}

	public void setGpsstatus(String gpsstatus) {
		this.gpsstatus = gpsstatus;
	}

	public String getGpsinfo() {
		return gpsinfo;
	}

	public void setGpsinfo(String gpsinfo) {
		this.gpsinfo = gpsinfo;
	}

	public int getRawrssi() {
		return rawrssi;
	}

	public void setRawrssi(int rawrssi) {
		this.rawrssi = rawrssi;
	}

	public String getRssi() {
		return rssi;
	}

	public void setRssi(String rssi) {
		this.rssi = rssi;
	}

	public int getLastpkt() {
		return lastpkt;
	}

	public void setLastpkt(int lastpkt) {
		this.lastpkt = lastpkt;
	}

	public Gateway getGateway() {
		return gateway;
	}

	public void setGateway(Gateway gateway) {
		this.gateway = gateway;
	}

	public int getWifirssi() {
		return wifirssi;
	}

	public void setWifirssi(int wifirssi) {
		this.wifirssi = wifirssi;
	}

	public int getWifissid() {
		return wifissid;
	}

	public void setWifissid(int wifissid) {
		this.wifissid = wifissid;
	}

	public int getPrptinterval() {
		return prptinterval;
	}

	public void setPrptinterval(int prptinterval) {
		this.prptinterval = prptinterval;
	}

	public String getZonename() {
		return zonename;
	}

	public void setZonename(String zonename) {
		this.zonename = zonename;
	}

	public int getZonelevel() {
		return zonelevel;
	}

	public void setZonelevel(int zonelevel) {
		this.zonelevel = zonelevel;
	}

	public String getZonedesc() {
		return zonedesc;
	}

	public void setZonedesc(String zonedesc) {
		this.zonedesc = zonedesc;
	}

	public Timestamp getLastgpsdatetime() {
		return lastgpsdatetime;
	}

	public void setLastgpsdatetime(Timestamp lastgpsdatetime) {
		this.lastgpsdatetime = lastgpsdatetime;
	}

	public String getLastvalidaddress() {
		return lastvalidaddress;
	}

	public void setLastvalidaddress(String lastvalidaddress) {
		this.lastvalidaddress = lastvalidaddress;
	}

	public double getLastvalidlat() {
		return lastvalidlat;
	}

	public void setLastvalidlat(double lastvalidlat) {
		this.lastvalidlat = lastvalidlat;
	}

	public double getLastvalidlon() {
		return lastvalidlon;
	}

	public void setLastvalidlon(double lastvalidlon) {
		this.lastvalidlon = lastvalidlon;
	}	
}
