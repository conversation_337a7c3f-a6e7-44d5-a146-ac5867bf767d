package com.nimble.irisservices.entity;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name="furbitreportv1")
public class FurBitReportv1 implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@Id
	@Column(name="id",unique = true, nullable = false)
	@GeneratedValue(strategy=IDENTITY)
	private long id;
	
	@Column(name="datetime")
	private Timestamp datetime;
	
	@Column(name="timezone")
	private String timezone;
	
	@Column(name="date")
	private Date date;
	
	@Column(name="time")
	private Time time;
	
	@Column(name="sfwpkt")
	private int sfwpkt;
	
	@Column(name="createdon")
	private Timestamp createdon;
	
	@Column(name="activitytype")
	private String activitytype;
	
	@Column(name="prevdatetime")
	private Timestamp prevdatetime;
	
	@Column(name="prevactivitytype")
	private String prevActivitytype;
	
	@Column(name="packetversion")
	private String packetversion;
	
	@Column(name="stepcount")
	private long stepcount;
	
	@Column(name="transmitmode")
	private long transmitmode;

	@ManyToOne(cascade = CascadeType.MERGE, fetch = FetchType.LAZY)
	@JoinColumn(name="gateway_id")
	private Gateway gateway;
	
	@ManyToOne(cascade = CascadeType.MERGE, fetch = FetchType.LAZY)
	@JoinColumn(name="cmp_id")
	private Company company;

	/**
	 * @return the id
	 */
	public long getId() {
		return id;
	}
	/**
	 * @param id the id to set
	 */
	public void setId(long id) {
		this.id = id;
	}
	/**
	 * @return the datetime
	 */
	public Timestamp getDatetime() {
		return datetime;
	}
	/**
	 * @param datetime the datetime to set
	 */
	public void setDatetime(Timestamp datetime) {
		this.datetime = datetime;
	}
	/**
	 * @return the timezone
	 */
	public String getTimezone() {
		return timezone;
	}
	/**
	 * @param timezone the timezone to set
	 */
	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}
	/**
	 * @return the date
	 */
	public Date getDate() {
		return date;
	}
	/**
	 * @param date the date to set
	 */
	public void setDate(Date date) {
		this.date = date;
	}
	/**
	 * @return the time
	 */
	public Time getTime() {
		return time;
	}
	/**
	 * @param time the time to set
	 */
	public void setTime(Time time) {
		this.time = time;
	}
	/**
	 * @return the sfwpkt
	 */
	public int getSfwpkt() {
		return sfwpkt;
	}
	/**
	 * @param sfwpkt the sfwpkt to set
	 */
	public void setSfwpkt(int sfwpkt) {
		this.sfwpkt = sfwpkt;
	}
	/**
	 * @return the createdon
	 */
	public Timestamp getCreatedon() {
		return createdon;
	}
	/**
	 * @param createdon the createdon to set
	 */
	public void setCreatedon(Timestamp createdon) {
		this.createdon = createdon;
	}
	
	/**
	 * @return the activitytype
	 */
	public String getActivitytype() {
		return activitytype;
	}
	/**
	 * @param activitytype the activitytype to set
	 */
	public void setActivitytype(String activitytype) {
		this.activitytype = activitytype;
	}
	/**
	 * @return the packetversion
	 */
	public String getPacketversion() {
		return packetversion;
	}
	/**
	 * @param packetversion the packetversion to set
	 */
	public void setPacketversion(String packetversion) {
		this.packetversion = packetversion;
	}
	/**
	 * @return the stepcount
	 */
	public long getStepcount() {
		return stepcount;
	}
	/**
	 * @param stepcount the stepcount to set
	 */
	public void setStepcount(long stepcount) {
		this.stepcount = stepcount;
	}
	/**
	 * @return the transmitmode
	 */
	public long getTransmitmode() {
		return transmitmode;
	}
	/**
	 * @param transmitmode the transmitmode to set
	 */
	public void setTransmitmode(long transmitmode) {
		this.transmitmode = transmitmode;
	}
	/**
	 * @return the company
	 */
	public Company giveCompany() {
		return company;
	}
	/**
	 * @param company the company to set
	 */
	public void setCompany(Company company) {
		this.company = company;
	}
	/**
	 * @return the gateway
	 */
	public Gateway getGateway() {
		return gateway;
	}
	/**
	 * @param gateway the gateway to set
	 */
	public void setGateway(Gateway gateway) {
		this.gateway = gateway;
	}
	/**
	 * @return the prevdatetime
	 */
	public Timestamp getPrevdatetime() {
		return prevdatetime;
	}
	/**
	 * @param prevdatetime the prevdatetime to set
	 */
	public void setPrevdatetime(Timestamp prevdatetime) {
		this.prevdatetime = prevdatetime;
	}
	/**
	 * @return the prevActivitytype
	 */
	public String getPrevActivitytype() {
		return prevActivitytype;
	}
	/**
	 * @param prevActivitytype the prevActivitytype to set
	 */
	public void setPrevActivitytype(String prevActivitytype) {
		this.prevActivitytype = prevActivitytype;
	}
}
