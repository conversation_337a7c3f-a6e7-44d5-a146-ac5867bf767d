package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "evalidation")
public class EValidation implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id = 0;

	@Column(name = "entity_id")
	private String entity_id = "NA";

	@Column(name = "entity_password")
	private String entity_password = "NA";

	@Column(name = "updated_date")
	private String updated_date = "1753-01-01 00:00:00";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getEntity_id() {
		return entity_id;
	}

	public void setEntity_id(String entity_id) {
		this.entity_id = entity_id;
	}

	public String getEntity_password() {
		return entity_password;
	}

	public void setEntity_password(String entity_password) {
		this.entity_password = entity_password;
	}

	public String getUpdated_date() {
		return updated_date;
	}

	public void setUpdated_date(String updated_date) {
		this.updated_date = updated_date;
	}

}
