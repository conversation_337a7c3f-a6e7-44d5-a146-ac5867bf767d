package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "adventuremodestatus")
public class AdventureModeStatus implements Serializable {

	  
	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name="startdatetime")
	private Timestamp startdatetime;
	
	@Column(name="enddatetime")
	private Timestamp enddatetime;
	
	@Column(name="status")
	private String status;
	
	@Column(name="prev_mode")
	private String prev_mode;
	
	@Column(name="current_mode")
	private String current_mode;

	@Column(name="auto_off")
	private boolean auto_off;
	
	@Column(name="gateway_id")
	private long gatewayId;

	public AdventureModeStatus ()
	{
		
	}
		
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Timestamp getStartdatetime() {
		return startdatetime;
	}

	public void setStartdatetime(Timestamp startdatetime) {
		this.startdatetime = startdatetime;
	}

	public Timestamp getEnddatetime() {
		return enddatetime;
	}

	public void setEnddatetime(Timestamp enddatetime) {
		this.enddatetime = enddatetime;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getPrev_mode() {
		return prev_mode;
	}

	public void setPrev_mode(String prev_mode) {
		this.prev_mode = prev_mode;
	}

	public String getCurrent_mode() {
		return current_mode;
	}

	public void setCurrent_mode(String current_mode) {
		this.current_mode = current_mode;
	}

	public boolean isAuto_off() {
		return auto_off;
	}

	public void setAuto_off(boolean auto_off) {
		this.auto_off = auto_off;
	}

	public long getGatewayId() {
		return gatewayId;
	}

	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}
	
	
}
