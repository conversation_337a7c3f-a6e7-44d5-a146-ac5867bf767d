package com.nimble.irisservices.entity;


import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "vetchat_noneligible_users")
public class VetChatNonEligibleUsers {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private String email;
    private LocalDateTime dateTime;
    private String interested_plan;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public LocalDateTime getDateTime() {
        return dateTime;
    }

    public void setDateTime(LocalDateTime dateTime) {
        this.dateTime = dateTime;
    }

	public String getInterested_plan() {
		return interested_plan;
	}

	public void setInterested_plan(String interested_plan) {
		this.interested_plan = interested_plan;
	}
}
