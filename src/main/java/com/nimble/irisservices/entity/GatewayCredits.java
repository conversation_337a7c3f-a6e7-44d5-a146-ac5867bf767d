package com.nimble.irisservices.entity;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;


@Entity 
@Table(name="gatewaycredits", uniqueConstraints =@UniqueConstraint (columnNames={"gateway_id" }) )
public class GatewayCredits{
	
	@Id
	@Column(name="gateway_id")
    private long gateway_id;
	
	@Column(name="cmp_id")
    private long cmp_id;
	
	@Column(name="creditsSpent")
	private long creditsSpent;
	
		
	@Column(name="extraCreditsSpent")
	private long extraCreditsSpent;
	
	@Column(name="creditsAssigned")
	private long creditsAssigned;
	
		
	@Column(name="extraCreditsAssigned")
	private long extraCreditsAssigned;
	
	@Column(name="billDate")
	private Timestamp billDate;
	
	@Column(name="resetMonthly")
	private int resetMonthly;
	
	public GatewayCredits() {
		super();
		// TODO Auto-generated constructor stub
	}

	public GatewayCredits(long gateway_id, long cmp_id, long creditsSpent, long creditsAssigned,
			Timestamp billDate, int resetMonthly,long extraCreditsSpent,long extraCreditsAssigned) {
		super();
		this.gateway_id 		= gateway_id;
		this.cmp_id		    	= cmp_id;
		this.creditsSpent 		= creditsSpent;
		this.creditsAssigned 	= creditsAssigned;
		this.billDate 			= billDate;
		this.resetMonthly 		= resetMonthly;
		this.extraCreditsSpent  = extraCreditsSpent;
		this.extraCreditsAssigned =extraCreditsAssigned;
	}

	
	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public long getCmp_id() {
		return cmp_id;
	}

	public void setCmp_id(long cmp_id) {
		this.cmp_id = cmp_id;
	}
	
	public long getCreditsSpent() {
		return creditsSpent;
	}

	public void setCreditsSpent(long creditsSpent) {
		this.creditsSpent = creditsSpent;
	}

	

	public Timestamp getBillDate() {
		return billDate;
	}

	public void setBillDate(Timestamp billDate) {
		this.billDate = billDate;
	}

	public int getResetMonthly() {
		return resetMonthly;
	}

	public void setResetMonthly(int resetMonthly) {
		this.resetMonthly = resetMonthly;
	}

	public long getExtraCreditsSpent() {
		return extraCreditsSpent;
	}

	public void setExtraCreditsSpent(long extraCreditsSpent) {
		this.extraCreditsSpent = extraCreditsSpent;
	}

	public long getCreditsAssigned() {
		return creditsAssigned;
	}

	public void setCreditsAssigned(long creditsAssigned) {
		this.creditsAssigned = creditsAssigned;
	}

	public long getExtraCreditsAssigned() {
		return extraCreditsAssigned;
	}

	public void setExtraCreditsAssigned(long extraCreditsAssigned) {
		this.extraCreditsAssigned = extraCreditsAssigned;
	}

	
}
