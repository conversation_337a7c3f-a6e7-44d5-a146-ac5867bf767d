package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;


@Entity 
@Table(name="alertnotifyhistory", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class AlertNotifyHistory implements Serializable{

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name="startDateTime")
	private Timestamp startDateTime;
	@Column(name="endDateTime")
	private Timestamp endDateTime;
	@Column(name="timeZone")
	private String timezone;
	@Column(name="notifyFreq")
	private int notifyfreq;
	@Column(name="firstNotifiedTime")
	private Timestamp firstNotifiedTime;
	@Column(name="lastNotifiedTime")
	private Timestamp lastNotifiedTime;
	@Column(name="notifiedCount")
	private int notifiedCount;
	@Column(name="acknowledge")
	private boolean acknowledge;
	@Column(name="smsAlertMsg")
	private String smsAlertMsg;
	
	@OneToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="alertId")
	//@JsonBackReference
    private Alert alert;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Timestamp getStartDateTime() {
		return startDateTime;
	}

	public void setStartDateTime(Timestamp startDateTime) {
		this.startDateTime = startDateTime;
	}

	public Timestamp getEndDateTime() {
		return endDateTime;
	}

	public void setEndDateTime(Timestamp endDateTime) {
		this.endDateTime = endDateTime;
	}

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}

	public int getNotifyfreq() {
		return notifyfreq;
	}

	public void setNotifyfreq(int notifyfreq) {
		this.notifyfreq = notifyfreq;
	}

	public Timestamp getFirstNotifiedTime() {
		return firstNotifiedTime;
	}

	public void setFirstNotifiedTime(Timestamp firstNotifiedTime) {
		this.firstNotifiedTime = firstNotifiedTime;
	}

	public Timestamp getLastNotifiedTime() {
		return lastNotifiedTime;
	}

	public void setLastNotifiedTime(Timestamp lastNotifiedTime) {
		this.lastNotifiedTime = lastNotifiedTime;
	}

	public int getNotifiedCount() {
		return notifiedCount;
	}

	public void setNotifiedCount(int notifiedCount) {
		this.notifiedCount = notifiedCount;
	}

	public boolean isAcknowledge() {
		return acknowledge;
	}

	public void setAcknowledge(boolean acknowledge) {
		this.acknowledge = acknowledge;
	}

	public String getSmsAlertMsg() {
		return smsAlertMsg;
	}

	public void setSmsAlertMsg(String smsAlertMsg) {
		this.smsAlertMsg = smsAlertMsg;
	}

	public Alert getAlert() {
		return alert;
	}

	public void setAlert(Alert alert) {
		this.alert = alert;
	}	
}
