package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="pet_feed_details")
public class PetFeedDetails {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	private long gateway_id = 0;
	
	private long user_id = 0;
	
	private long pet_food_id = 0;
	
	private long meals_per_day_id = 0;
	
	private String meal_times = "NA";
	
	private String updated_on = "1753-01-01 11:11:11";
	
	private String timezone = "+00:00";
	
	private double req_calories = 0;
	
	private double req_grams = 0;
	
	private boolean remainder = true;
	
	@Transient
	private boolean meal_time_updated = false;
	
	@Transient
	private int meal_cnt = 1;
	
	@Transient
	private String pet_food_name = "";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public long getPet_food_id() {
		return pet_food_id;
	}

	public void setPet_food_id(long pet_food_id) {
		this.pet_food_id = pet_food_id;
	}

	public long getMeals_per_day_id() {
		return meals_per_day_id;
	}

	public void setMeals_per_day_id(long meals_per_day_id) {
		this.meals_per_day_id = meals_per_day_id;
	}

	public String getMeal_times() {
		return meal_times;
	}

	public void setMeal_times(String meal_times) {
		this.meal_times = meal_times;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}

	public boolean isRemainder() {
		return remainder;
	}

	public void setRemainder(boolean remainder) {
		this.remainder = remainder;
	}

	public boolean isMeal_time_updated() {
		return meal_time_updated;
	}

	public void setMeal_time_updated(boolean meal_time_updated) {
		this.meal_time_updated = meal_time_updated;
	}

	public PetFeedDetails() {
		super();
	}

	public double getReq_calories() {
		return req_calories;
	}

	public void setReq_calories(double req_calories) {
		this.req_calories = req_calories;
	}

	public int getMeal_cnt() {
		return meal_cnt;
	}

	public void setMeal_cnt(int meal_cnt) {
		this.meal_cnt = meal_cnt;
	}

	public PetFeedDetails(long id, long gateway_id, long user_id, long pet_food_id, long meals_per_day_id,
			String meal_times, String updated_on, String timezone, boolean remainder) {
		super();
		this.id = id;
		this.gateway_id = gateway_id;
		this.user_id = user_id;
		this.pet_food_id = pet_food_id;
		this.meals_per_day_id = meals_per_day_id;
		this.meal_times = meal_times;
		this.updated_on = updated_on;
		this.timezone = timezone;
		this.remainder = remainder;
	}

	public double getReq_grams() {
		return req_grams;
	}

	public void setReq_grams(double req_grams) {
		this.req_grams = req_grams;
	}

	public String getPet_food_name() {
		return pet_food_name;
	}

	public void setPet_food_name(String pet_food_name) {
		this.pet_food_name = pet_food_name;
	}	

}
