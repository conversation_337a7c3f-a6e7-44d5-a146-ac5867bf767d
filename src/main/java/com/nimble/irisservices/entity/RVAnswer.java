package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "rv_answer")
public class RVAnswer implements Serializable {
	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	
	@Column(name = "ques_id")
	private long ques_id;
	
	@Column(name = "ans_value")
	private String ans_value;	

	@Column(name = "orderno")
	private int orderno;
	
	@Column(name = "enable")
	private boolean enable;

	@Column(name = "createdon")
	private String createdon = "1753-01-01 00:00:00";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getQues_id() {
		return ques_id;
	}

	public void setQues_id(long ques_id) {
		this.ques_id = ques_id;
	}

	public String getAns_value() {
		return ans_value;
	}

	public void setAns_value(String ans_value) {
		this.ans_value = ans_value;
	}

	public int getOrderno() {
		return orderno;
	}

	public void setOrderno(int orderno) {
		this.orderno = orderno;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getCreatedon() {
		return createdon;
	}

	public void setCreatedon(String createdon) {
		this.createdon = createdon;
	}
}
