package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="device_license", uniqueConstraints =@UniqueConstraint (columnNames={"id","device_name" } ))
public class DeviceLicense implements Serializable{
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@JsonIgnore
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="user_id")
    private User user;
	
	@Column(name="device_name")
	private String device_name;
	
	@Column(name="device_type")
	private String device_type;
	
	@Column(name="valid_from")
	private Date valid_from;
	
	@Column(name="valid_to")
	private Date valid_to;
	
	@Column(name="enable")
	private boolean enable;
	
	@Column(name="reset_validity")
	private boolean reset_validity;	
	
	
	public DeviceLicense() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	public DeviceLicense(long id, String device_name, String device_type, User user) {
		this.setId(id);
		this.setDevice_name(device_name);
		this.setDevice_type(device_type);
		this.setUser(user);
	}
	
	public DeviceLicense(long id, String device_name, String device_type, User user,
			Date valid_from,Date valid_to, boolean enable, boolean reset_validity) {
		this.setId(id);
		this.setDevice_name(device_name);
		this.setDevice_type(device_type);
		this.setUser(user);
		this.valid_from = valid_from;
		this.valid_to = valid_to;
		this.enable = enable;
		this.reset_validity = reset_validity;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public String getDevice_name() {
		return device_name;
	}

	public void setDevice_name(String device_name) {
		this.device_name = device_name;
	}

	public String getDevice_type() {
		return device_type;
	}

	public void setDevice_type(String device_type) {
		this.device_type = device_type;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public Date getValid_from() {
		return valid_from;
	}

	public void setValid_from(Date valid_from) {
		this.valid_from = valid_from;
	}

	public Date getValid_to() {
		return valid_to;
	}

	public void setValid_to(Date valid_to) {
		this.valid_to = valid_to;
	}

	public boolean isReset_validity() {
		return reset_validity;
	}

	public void setReset_validity(boolean reset_validity) {
		this.reset_validity = reset_validity;
	}
	

}
