package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;


@Entity 
@Table(name="userdeviceinfo", uniqueConstraints =@UniqueConstraint (columnNames={"id" ,"deviceid"} ))
public class UserDeviceInfo implements Serializable{
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name="os")
	private String os;

	@Column(name="version")
	private String version;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="userId")
    private User userId;

	
	@Column(name="devicemodel")
	private String devicemodel;

	@Column(name="deviceid")
	private String deviceid;
	
	@Column(name="ipaddress")
	private String ipaddress;
	
	@Column(name = "createdon")
	private String createdOn;
	
	@Column(name = "appversion")
	private String appversion;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getOs() {
		return os;
	}

	public void setOs(String os) {
		this.os = os;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public User getUserId() {
		return userId;
	}

	public void setUserId(User userId) {
		this.userId = userId;
	}

	public String getDevicemodel() {
		return devicemodel;
	}

	public void setDevicemodel(String devicemodel) {
		this.devicemodel = devicemodel;
	}

	public String getDeviceid() {
		return deviceid;
	}

	public void setDeviceid(String deviceid) {
		this.deviceid = deviceid;
	}

	public UserDeviceInfo() {
		super();
		// TODO Auto-generated constructor stub
	}




	public UserDeviceInfo(String os, String version, User userId, String devicemodel, String deviceid, String ipaddress,
			String createdOn,String appversion) {
		super();
		this.os = os;
		this.version = version;
		this.userId = userId;
		this.devicemodel = devicemodel;
		this.deviceid = deviceid;
		this.ipaddress = ipaddress;
		this.createdOn = createdOn;
		this.appversion=appversion;
	}

	public UserDeviceInfo(long id, String os, String version, User userId, String devicemodel, String deviceid,
			String ipaddress, String createdOn,String appversion) {
		super();
		this.id = id;
		this.os = os;
		this.version = version;
		this.userId = userId;
		this.devicemodel = devicemodel;
		this.deviceid = deviceid;
		this.ipaddress = ipaddress;
		this.createdOn = createdOn;
		this.appversion=appversion;
	}

	public String getIpaddress() {
		return ipaddress;
	}

	public void setIpaddress(String ipaddress) {
		this.ipaddress = ipaddress;
	}

	public String getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}

	public String getAppversion() {
		return appversion;
	}

	public void setAppversion(String appversion) {
		this.appversion = appversion;
	}

	

	
}
