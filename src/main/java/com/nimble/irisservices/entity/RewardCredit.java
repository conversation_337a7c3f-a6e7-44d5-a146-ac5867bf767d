package com.nimble.irisservices.entity;

public class RewardCredit {

	int ebookCredit = 0;
    int credit = 0;
    int style_count= 0;
    String merchCoupon= "NA";
    String mugCoupon= "NA";
    String waggleCamCoupon= "NA";
    String referralCoupon= "NA";
    String content1= "NA";
    String content2= "NA";
    String content3= "NA";
    String content4= "NA";

    public RewardCredit(int credit, int style_count, String merchCoupon, String waggleCamCoupon, String referralCoupon, int ebookCredit,
    		String mugCoupon,String content1,String content2, String content3, String content4) {
		super();
		this.credit = credit;
		this.style_count = style_count;
		this.merchCoupon = merchCoupon;
		this.ebookCredit = ebookCredit;
		this.mugCoupon = mugCoupon;
		this.waggleCamCoupon = waggleCamCoupon;
		this.referralCoupon = referralCoupon;
		this.content1 = content1;
		this.content2 = content2;
		this.content3 = content3;
		this.content4 = content4;
	}
	
    public RewardCredit() {
    	
    }
    
	public int getStyle_count() {
		return style_count;
	}

	public void setStyle_count(int style_count) {
		this.style_count = style_count;
	}

	public String getMerchCoupon() {
		return merchCoupon;
	}

	public void setMerchCoupon(String merchCoupon) {
		this.merchCoupon = merchCoupon;
	}

	public int getEbookCredit() {
		return ebookCredit;
	}

	public void setEbookCredit(int ebookCredit) {
		this.ebookCredit = ebookCredit;
	}

	public int getCredit() {
		return credit;
	}

	public void setCredit(int credit) {
		this.credit = credit;
	}

	public String getMugCoupon() {
		return mugCoupon;
	}

	public void setMugCoupon(String mugCoupon) {
		this.mugCoupon = mugCoupon;
	}
	
	public String getwaggleCamCoupon() {
		return waggleCamCoupon;
	}

	public void setwaggleCamCoupon(String waggleCamCoupon) {
		this.waggleCamCoupon = waggleCamCoupon;
	}
	
	public String getReferralCoupon() {
		return referralCoupon;
	}

	public void setReferralCoupon(String referralCoupon) {
		this.referralCoupon = referralCoupon;
	}

	public String getContent1() {
		return content1;
	}

	public void setContent1(String content1) {
		this.content1 = content1;
	}

	public String getContent2() {
		return content2;
	}

	public void setContent2(String content2) {
		this.content2 = content2;
	}
	
	public String getContent3() {
		return content3;
	}

	public void setContent3(String content3) {
		this.content3 = content3;
	}
	
	public String getContent4() {
		return content4;
	}

	public void setContent4(String content4) {
		this.content4 = content4;
	}
    
}
