package com.nimble.irisservices.entity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "cancel_feedback_content")
public class CancelFeedbackContent {

	
	@Id
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	
	
	private long cancel_feedback_id = 0;
	
	private String content_key = "NA";
	
	private String content_response = "NA";
	
	private boolean enable = false;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getCancel_feedback_id() {
		return cancel_feedback_id;
	}

	public void setCancel_feedback_id(long cancel_feedback_id) {
		this.cancel_feedback_id = cancel_feedback_id;
	}

	public String getContent_key() {
		return content_key;
	}

	public void setContent_key(String content_key) {
		this.content_key = content_key;
	}

	public String getContent_response() {
		return content_response;
	}

	public void setContent_response(String content_response) {
		this.content_response = content_response;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public CancelFeedbackContent() {
		super();
	}
	
	public CancelFeedbackContent(long id, long cancel_feedback_id, String content_key, String content_response,
			boolean enable) {
		super();
		this.id = id;
		this.cancel_feedback_id = cancel_feedback_id;
		this.content_key = content_key;
		this.content_response = content_response;
		this.enable = enable;
	}
	
	
}
