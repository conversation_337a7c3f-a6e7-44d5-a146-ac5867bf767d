package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "temp_calib_status")
public class TempCalibStatus implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "gateway_id")
	private long gateway_id;

	@Column(name = "current_calib")
	private float current_calib = 0;

	@Column(name = "new_calib")
	private float new_calib = 0;

	@Column(name = "status")
	private String status = "NA";

	@Column(name = "updated_on")
	private String updated_on = "1753-01-01 00:00:00";

	@Column(name = "battery_offset_status")
	private String battery_offset_status = "NA";

	@Column(name = "charging_offset_status")
	private String charging_offset_status = "NA";

	@Column(name = "fullcharge_offset_status")
	private String fullcharge_offset_status = "NA";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public float getCurrent_calib() {
		return current_calib;
	}

	public void setCurrent_calib(float current_calib) {
		this.current_calib = current_calib;
	}

	public float getNew_calib() {
		return new_calib;
	}

	public void setNew_calib(float new_calib) {
		this.new_calib = new_calib;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public String getBattery_offset_status() {
		return battery_offset_status;
	}

	public void setBattery_offset_status(String battery_offset_status) {
		this.battery_offset_status = battery_offset_status;
	}

	public String getCharging_offset_status() {
		return charging_offset_status;
	}

	public void setCharging_offset_status(String charging_offset_status) {
		this.charging_offset_status = charging_offset_status;
	}

	public String getFullcharge_offset_status() {
		return fullcharge_offset_status;
	}

	public void setFullcharge_offset_status(String fullcharge_offset_status) {
		this.fullcharge_offset_status = fullcharge_offset_status;
	}

}
