package com.nimble.irisservices.entity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "remove_gateway_request_history")
public class RemoveGatewayRequestHistory {
	
	@Id
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	
	private long user_id = 0;
	
	private long gateway_id = 0;
	
	private long remove_gateway_type_id = 0;
	
	private String reason = "NA";
	
	private String updated_on = "1753-01-01 11:11:11";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public long getRemove_gateway_type_id() {
		return remove_gateway_type_id;
	}

	public void setRemove_gateway_type_id(long remove_gateway_type_id) {
		this.remove_gateway_type_id = remove_gateway_type_id;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

	public RemoveGatewayRequestHistory() {
		super();
	}
	
	public RemoveGatewayRequestHistory( RemoveGatewayRequest removeGatewayRequest ) {
		super();
		this.user_id = removeGatewayRequest.getUser_id();
		this.gateway_id = removeGatewayRequest.getGateway_id();
		this.remove_gateway_type_id = removeGatewayRequest.getRemove_gateway_type_id();
		this.reason = removeGatewayRequest.getReason();
	}

	public RemoveGatewayRequestHistory(long id, long user_id, long gateway_id, long remove_gateway_type_id, String reason,
			String updated_on) {
		super();
		this.id = id;
		this.user_id = user_id;
		this.gateway_id = gateway_id;
		this.remove_gateway_type_id = remove_gateway_type_id;
		this.reason = reason;
		this.updated_on = updated_on;
	}
	


}
