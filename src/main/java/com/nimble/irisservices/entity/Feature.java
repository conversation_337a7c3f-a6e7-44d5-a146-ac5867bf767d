package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "feature", uniqueConstraints = @UniqueConstraint(columnNames = { "feature_name" }))

public class Feature implements Serializable {
	private static final long serialVersionUID = 0l;

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "feature_name")
	private String feature_name;

	@Column(name = "description")
	private String description = "NA";

	@Column(name = "enable")
	private boolean enable;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "type_id")
	private FeatureType type_id;

	@Transient
	private long featuretype;

	@Column(name = "feature_code")
	private String feature_code;

	@Column(name = "alerttype_id")
	private long alerttype_id;

	@Column(name = "monitor_type_id")
	private long monitor_type_id;

	public long getMonitor_type_id() {
		return monitor_type_id;
	}

	public void setMonitor_type_id(long monitor_type_id) {
		this.monitor_type_id = monitor_type_id;
	}

	public Feature() {
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getFeature_name() {
		return feature_name;
	}

	public void setFeature_name(String feature_name) {
		this.feature_name = feature_name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public FeatureType getType_id() {
		return type_id;
	}

	public void setType_id(FeatureType type_id) {
		this.type_id = type_id;
	}

	public long getFeaturetype() {
		return featuretype;
	}

	public void setFeaturetype(long featuretype) {
		this.featuretype = featuretype;
	}

	public String getFeature_code() {
		return feature_code;
	}

	public void setFeature_code(String feature_code) {
		this.feature_code = feature_code;
	}

	public long getAlerttype_id() {
		return alerttype_id;
	}

	public void setAlerttype_id(long alerttype_id) {
		this.alerttype_id = alerttype_id;
	}

}
