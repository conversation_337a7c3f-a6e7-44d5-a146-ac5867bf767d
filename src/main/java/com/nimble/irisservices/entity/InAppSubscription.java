package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "inapp_subscription", uniqueConstraints = @UniqueConstraint(columnNames = { "user_id" }))
public class InAppSubscription implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "user_id")
	private long user_id;

	@Column(name = "product_id")
	private String product_id;

	@Column(name = "quantity")
	private int quantity;

	@Column(name = "created_date")
	private String created_date; // date

	@Column(name = "original_transaction_id")
	private String original_transaction_id;

	@Column(name = "renewal_transaction_id")
	private String renewal_transaction_id;

	@Column(name = "auto_renew")
	private boolean auto_renew = true;

	@Column(name = "renew_status")
	private String renew_status;

	@Column(name = "start_date")
	private String start_date; // date

	@Column(name = "expires_date")
	private String expires_date; // date

	@Column(name = "enable")
	private boolean enable = true;

	@Column(name = "sub_type")
	private String sub_type;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getProduct_id() {
		return product_id;
	}

	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}

	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	public String getCreated_date() {
		return created_date;
	}

	public void setCreated_date(String created_date) {
		this.created_date = created_date;
	}

	public String getOriginal_transaction_id() {
		return original_transaction_id;
	}

	public void setOriginal_transaction_id(String original_transaction_id) {
		this.original_transaction_id = original_transaction_id;
	}

	public String getRenewal_transaction_id() {
		return renewal_transaction_id;
	}

	public void setRenewal_transaction_id(String renewal_transaction_id) {
		this.renewal_transaction_id = renewal_transaction_id;
	}

	public boolean isAuto_renew() {
		return auto_renew;
	}

	public void setAuto_renew(boolean auto_renew) {
		this.auto_renew = auto_renew;
	}

	public String getRenew_status() {
		return renew_status;
	}

	public void setRenew_status(String renew_status) {
		this.renew_status = renew_status;
	}

	public String getStart_date() {
		return start_date;
	}

	public void setStart_date(String start_date) {
		this.start_date = start_date;
	}

	public String getExpires_date() {
		return expires_date;
	}

	public void setExpires_date(String expires_date) {
		this.expires_date = expires_date;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getSub_type() {
		return sub_type;
	}

	public void setSub_type(String sub_type) {
		this.sub_type = sub_type;
	}

	public String toString() {
		return "'" + user_id + "','" + product_id + "','" + quantity + "','" + created_date + "','"
				+ original_transaction_id + "','" + renewal_transaction_id + "'," + auto_renew + ",'" + renew_status
				+ "','" + start_date + "','" + expires_date + "'," + enable + ",'" + sub_type + "'";
	}

	public String toHeaderString() {
		return " user_id, product_id, quantity, created_date, original_transaction_id, renewal_transaction_id, auto_renew, renew_status, start_date, expires_date, enable, sub_type";
	}

	public String getUpdateQuery() throws Exception {

		try {
			String query = "update inapp_subscription SET `renewal_transaction_id`='" + renewal_transaction_id + "',"
					+ "`product_id`	='" + product_id + "', `quantity`	='" + quantity + "', "
					+ "`created_date`	='" + created_date + "',`original_transaction_id`	='"
					+ original_transaction_id + "', `auto_renew`	=" + auto_renew + ", `renew_status`	='"
					+ renew_status + "',`start_date`	='" + start_date + "'," + "`expires_date`	='"
					+ expires_date + "', `enable`	=" + enable + ", " + "`sub_type`	='" + sub_type
					+ "' ";
			return query;
		} catch (Exception e) {
			throw e;
		}

	}

}
