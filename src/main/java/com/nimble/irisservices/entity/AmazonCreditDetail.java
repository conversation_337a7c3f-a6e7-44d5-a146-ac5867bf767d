/*
 * package com.nimble.irisservices.entity;
 * 
 * import java.sql.Timestamp;
 * 
 * import javax.persistence.CascadeType; import javax.persistence.Column; import
 * javax.persistence.Entity; import javax.persistence.GeneratedValue; import
 * javax.persistence.Id; import javax.persistence.JoinColumn; import
 * javax.persistence.ManyToOne; import javax.persistence.Table;
 * 
 * import org.hibernate.annotations.GenericGenerator;
 * 
 * @Entity
 * 
 * @Table(name = "amazon_credit_detail") public class AmazonCreditDetail
 * implements java.io.Serializable {
 * 
 * CREATE TABLE `amazon_credit_detail` ( `id` BIGINT(20) NOT NULL
 * AUTO_INCREMENT, `credit_amt` DOUBLE DEFAULT '0', `desc` VARCHAR(255) NOT
 * NULL, `user_id` BIGINT(20) DEFAULT NULL, `createdon` DATETIME NOT NULL
 * DEFAULT '1753-01-01 00:00:00', PRIMARY KEY (`id`), KEY
 * `FK_amazoncreditdetail` (`user_id`), CONSTRAINT `FK_amazoncreditdetail`
 * FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) );
 * 
 * 
 * @Id
 * 
 * @Column(name = "id")
 * 
 * @GenericGenerator(name = "gen", strategy = "identity")
 * 
 * @GeneratedValue(generator = "gen") private Long id;
 * 
 * @Column(name = "credit_amt") private double credit_amt;
 * 
 * @Column(name = "desc") private String desc;
 * 
 * @ManyToOne(cascade = CascadeType.ALL)
 * 
 * @JoinColumn(name="user_id") private User user_id;
 * 
 * @Column(name = "createdon") private Timestamp createdon ;
 * 
 * public AmazonCreditDetail() { }
 * 
 * public AmazonCreditDetail(double credit_amt, String desc, User user_id,
 * Timestamp createdon ) { this.credit_amt = credit_amt; this.desc = desc;
 * this.user_id = user_id; this.createdon = createdon; }
 * 
 * public Long getId() { return id; }
 * 
 * public void setId(Long id) { this.id = id; }
 * 
 * public double getCredit_amt() { return credit_amt; }
 * 
 * public void setCredit_amt(double credit_amt) { this.credit_amt = credit_amt;
 * }
 * 
 * public String getDesc() { return desc; }
 * 
 * public void setDesc(String desc) { this.desc = desc; }
 * 
 * public User getUser_id() { return user_id; }
 * 
 * public void setUser_id(User user_id) { this.user_id = user_id; }
 * 
 * public Timestamp getCreatedon() { return createdon; }
 * 
 * public void setCreatedon(Timestamp createdon) { this.createdon = createdon; }
 * }
 */