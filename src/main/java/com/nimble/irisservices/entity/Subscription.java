package com.nimble.irisservices.entity;


import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

/**
 * Subscription generated by hbm2java
 */
@Entity(name="subscription")
@Table(name="subscription",uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class Subscription  implements Serializable {

     @Id  
     @GenericGenerator(name="gen",strategy="identity")
 	 @GeneratedValue(generator="gen")
     @Column(name="id")
	 private Integer id;
	 
     
	 @Column(name="woocom_sub_id")
     private long woocomSubId;
	 
     
	 @Column(name="order_id")
     private long orderId;
	 
     
	 @Column(name="cus_id")
     private long cusId;
	 
	 @Column(name="meid")
     private String meid;
	 
	 
	 @Column(name="sub_number")
     private long subNumber;
	 
	 @Column(name="status")
     private String status;
	 
	 @Column(name="billing_firstname")
     private String billingFirstname;
	 
	 @Column(name="billing_lastname")
     private String billingLastname;
	 
	 @Column(name="billing_email")
     private String billingEmail;
	 
	 @Column(name="billing_mobileno")
     private String billingMobileno;
	 
	 @Column(name="lineItems")
     private String lineItems;
	 
	 @Column(name="totalLineitems")
	 private int totalLineitems;
	 
	 @Column(name="plan_name")
     private String planName;
	 
	 @Column(name="sku")
     private String sku;
	 
	 @Column(name="quantity")
     private Long quantity;
	 
	 @Column(name="price", precision=12, scale=0)
     private float price;
	 
	 @Column(name="billing_period")
     private String billingPeriod;
	 
	 @Column(name="billing_start_date")
     private String billingStartDate;
	 
	 @Column(name="trial_end_date")
     private String trialEndDate;
	 
	 
	 @Column(name="next_payment_date", length=19)
     private String nextPaymentDate;
	 
	 @Column(name="payment_type")
     private String paymentType;
	 
	 @Column(name="last_payment_date")
     private String lastPaymentDate;
	 
	 @Column(name="address")
     private String address;
	 
	 @Column(name="subscription_created")
     private String subscriptionCreated;
	 
	 @Column(name="end_date")
     private String endDate;
	 
	 @Column(name="account_type")
     private String accountType;
	 
	 @Column(name="parent_id")
     private Integer parentId;
	 
	 @Column(name="parent_email")
     private String  parent_email;
	 
	 @Transient
     private String autoRenewalStatus;
	 
	 
    public Subscription() {
    	super();
    }

	
    public Subscription(long woocomSubId, long orderId, long cusId, long subNumber) {
        this.woocomSubId = woocomSubId;
        this.orderId = orderId;
        this.cusId = cusId;
        this.subNumber = subNumber;
    }
    
//    public Subscription(long subId, long orderId, long cusId, String meid, long subNumber, String status, String billingFirstname, String billingLastname, String billingEmail, String billingMobileno, String lineItems, String planName, String sku, Long quantity, Float price, String billingPeriod, String billingStartDate, String trialEndDate, String nextPaymentDate, String paymentType, String lastPaymentDate, String address, String subscriptionCreated, String endDate, String accountType, Integer parentId) {
//       this.subId = subId;
//       this.orderId = orderId;
//       this.cusId = cusId;
//       this.meid = meid;
//       this.subNumber = subNumber;
//       this.status = status;
//       this.billingFirstname = billingFirstname;
//       this.billingLastname = billingLastname;
//       this.billingEmail = billingEmail;
//       this.billingMobileno = billingMobileno;
//       this.lineItems = lineItems;
//       this.planName = planName;
//       this.sku = sku;
//       this.quantity = quantity;
//       this.price = price;
//       this.billingPeriod = billingPeriod;
//       this.billingStartDate = billingStartDate.substring(0, 19);
//       this.trialEndDate = trialEndDate.substring(0, 19);
//       this.nextPaymentDate = nextPaymentDate.substring(0, 19);
//       this.paymentType = paymentType;
//       this.lastPaymentDate = lastPaymentDate.substring(0, 19);
//       this.address = address;
//       this.subscriptionCreated = subscriptionCreated.substring(0, 19);
//       this.endDate = endDate.substring(0, 19);
//       this.accountType = accountType;
//       this.parentId = parentId;
//       
//    }
    
    
   
    
     
    public Integer getId() {
        return this.id;
    }
    
    public Subscription(Integer id, long woocomSubId, long orderId, long cusId, String meid, long subNumber, String status,
		String billingFirstname, String billingLastname, String billingEmail, String billingMobileno, String lineItems,
		int totalLineitems, String planName, String sku, Long quantity, Float price, String billingPeriod,
		String billingStartDate, String trialEndDate, String nextPaymentDate, String paymentType,
		String lastPaymentDate, String address, String subscriptionCreated, String endDate, String accountType,
		Integer parentId, String parent_email) {
	super();
	this.id = id;
	this.woocomSubId = woocomSubId;
	this.orderId = orderId;
	this.cusId = cusId;
	this.meid = meid;
	this.subNumber = subNumber;
	this.status = status;
	this.billingFirstname = billingFirstname;
	this.billingLastname = billingLastname;
	this.billingEmail = billingEmail;
	this.billingMobileno = billingMobileno;
	this.lineItems = lineItems;
	this.totalLineitems = totalLineitems;
	this.planName = planName;
	this.sku = sku;
	this.quantity = quantity;
	this.price = price;
	this.billingPeriod = billingPeriod;
	this.billingStartDate = billingStartDate;
	this.trialEndDate = trialEndDate;
	this.nextPaymentDate = nextPaymentDate;
	this.paymentType = paymentType;
	this.lastPaymentDate = lastPaymentDate;
	this.address = address;
	this.subscriptionCreated = subscriptionCreated;
	this.endDate = endDate;
	this.accountType = accountType;
	this.parentId = parentId;
	this.parent_email = parent_email;
}


	public void setId(Integer id) {
        this.id = id;
    }

    
    
    public long getOrderId() {
        return this.orderId;
    }
    
    public void setOrderId(long orderId) {
        this.orderId = orderId;
    }

    
    
    public long getCusId() {
        return this.cusId;
    }
    
    public void setCusId(long cusId) {
        this.cusId = cusId;
    }

    
    
    public String getMeid() {
        return this.meid;
    }
    
    public void setMeid(String meid) {
        this.meid = meid;
    }

    
    
    public long getSubNumber() {
        return this.subNumber;
    }
    
    public void setSubNumber(long subNumber) {
        this.subNumber = subNumber;
    }

    
    
    public String getStatus() {
        return this.status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }

    
    
    public String getBillingFirstname() {
        return this.billingFirstname;
    }
    
    public void setBillingFirstname(String billingFirstname) {
        this.billingFirstname = billingFirstname;
    }

    
    
    public String getBillingLastname() {
        return this.billingLastname;
    }
    
    public void setBillingLastname(String billingLastname) {
        this.billingLastname = billingLastname;
    }

    
    
    public String getBillingEmail() {
        return this.billingEmail;
    }
    
    public void setBillingEmail(String billingEmail) {
        this.billingEmail = billingEmail;
    }

    
    
    public String getBillingMobileno() {
        return this.billingMobileno;
    }
    
    public void setBillingMobileno(String billingMobileno) {
        this.billingMobileno = billingMobileno;
    }

    
    
    public String getLineItems() {
        return this.lineItems;
    }
    
    public void setLineItems(String lineItems) {
        this.lineItems = lineItems;
    }

    
    
    public String getPlanName() {
        return this.planName;
    }
    
    public void setPlanName(String planName) {
        this.planName = planName;
    }

    
    
    public String getSku() {
        return this.sku;
    }
    
    public void setSku(String sku) {
        this.sku = sku;
    }

    
    
    public Long getQuantity() {
        return this.quantity;
    }
    
    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    
    
    public Float getPrice() {
        return this.price;
    }
    
    public void setPrice(Float price) {
        this.price = price;
    }

    
    
    public String getBillingPeriod() {
        return this.billingPeriod;
    }
    
    public void setBillingPeriod(String billingPeriod) {
        this.billingPeriod = billingPeriod;
    }

    public String getBillingStartDate() {
        return this.billingStartDate == null ? null : billingStartDate.substring(0, 19);
    }
    
    public void setBillingStartDate(String billingStartDate) {
        this.billingStartDate = billingStartDate== null ? null : billingStartDate.substring(0, 19);
    }

    public String getTrialEndDate() {
        return this.trialEndDate== null ? null : trialEndDate.substring(0, 19);
    }
    
    public void setTrialEndDate(String trialEndDate) {
        this.trialEndDate = trialEndDate== null ? null : trialEndDate.substring(0, 19);
    }

    public String getNextPaymentDate() {
        return this.nextPaymentDate == null ? null : nextPaymentDate.substring(0, 19);
    }
    
    public void setNextPaymentDate(String nextPaymentDate) {
        this.nextPaymentDate= nextPaymentDate== null ? null : nextPaymentDate.substring(0, 19);
    }
   
    public String getPaymentType() {
        return this.paymentType;
    }
    
    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getLastPaymentDate() {
        return this.lastPaymentDate == null ? null : lastPaymentDate.substring(0, 19);
    }
    
    public void setLastPaymentDate(String lastPaymentDate) {
        this.lastPaymentDate = lastPaymentDate == null ? null : lastPaymentDate.substring(0, 19);
    }

    
    
    public String getAddress() {
        return this.address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }

    public String getSubscriptionCreated() {
        return this.subscriptionCreated == null ? null : subscriptionCreated.substring(0, 19);
    }
    
    public void setSubscriptionCreated(String subscriptionCreated) {
        this.subscriptionCreated = subscriptionCreated== null ? null : subscriptionCreated.substring(0, 19);
    }

    public String getEndDate() {
        return this.endDate == null ? null : endDate.substring(0, 19);
    }
    
    public void setEndDate(String endDate) {
        this.endDate = endDate== null ? null : endDate.substring(0, 19);
    }

    
    
    public String getAccountType() {
        return this.accountType;
    }
    
    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    
    
    public Integer getParentId() {
        return this.parentId;
    }
    
    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }


	public String getParent_email() {
		return parent_email;
	}


	public void setParent_email(String parent_email) {
		this.parent_email = parent_email;
	}


	public int getTotalLineitems() {
		return totalLineitems;
	}


	public void setTotalLineitems(int totalLineitems) {
		this.totalLineitems = totalLineitems;
	}


	public long getWoocomSubId() {
		return woocomSubId;
	}


	public void setWoocomSubId(long woocomSubId) {
		this.woocomSubId = woocomSubId;
	}


	public String getAutoRenewalStatus() {
		return autoRenewalStatus;
	}


	public void setAutoRenewalStatus(String autoRenewalStatus) {
		this.autoRenewalStatus = autoRenewalStatus;
	}




}


