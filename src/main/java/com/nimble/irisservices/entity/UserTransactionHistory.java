package com.nimble.irisservices.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name="user_txn_history", uniqueConstraints =@UniqueConstraint (columnNames={"user_id","feature_id","txn_date" }) )

public class UserTransactionHistory  implements Serializable{
	private static final long serialVersionUID = 0l;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
    private long user_id;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch=FetchType.EAGER)
	@JoinColumn(name="feature_id")
	private Feature feature_id;
	
	@Column(name="no_txn")
	private int no_txn;

	@Column(name="txn_date")
	private Date txn_date;

	public UserTransactionHistory()
	{
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public Feature getFeature_id() {
		return feature_id;
	}

	public void setFeature_id(Feature feature_id) {
		this.feature_id = feature_id;
	}

	public int getNo_txn() {
		return no_txn;
	}

	public void setNo_txn(int no_txn) {
		this.no_txn = no_txn;
	}

	public Date getTxn_date() {
		return txn_date;
	}

	public void setTxn_date(Date txn_date) {
		this.txn_date = txn_date;
	}
}
