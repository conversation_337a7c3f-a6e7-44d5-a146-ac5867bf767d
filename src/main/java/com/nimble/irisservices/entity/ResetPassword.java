package com.nimble.irisservices.entity;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import java.util.UUID;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.nimble.irisservices.helper.AES;

@Entity
@Table(name = "resetpassword")
public class ResetPassword {
	
	public static final String STATUS_PENDING = "PENDING";
	public static final String STATUS_VERIFIED = "VERIFIED";
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "token")
	private String token;

	@Column(name = "status")
	private String status;

	@Column(name = "expireddatetime")
	private String expiredDateTime;

	@Column(name = "issueddatetime")
	private String issuedDateTime;

	@Column(name = "confirmeddatetime")
	private String confirmedDateTime = "1753-01-01 00:00:00";

	@Column(name = "resentlinkcount")
	private int resentLinkCount;
	
	@Column(name = "userid")
	private long userId;
	
	@Transient
	char hexDigit[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };


	public ResetPassword() {
		super();
		// TODO Auto-generated constructor stub
	}

	public ResetPassword(String expireTime) {
//		this.token = encrypt_UUID(UUID.randomUUID().toString());
		String uuid = UUID.randomUUID().toString();
		this.token = AES.encrypt(uuid.substring(0,10));
		this.issuedDateTime = getCurrentTimeinUTC();
		this.expiredDateTime = getPlusDays(issuedDateTime, expireTime);
		this.resentLinkCount = 1;
		this.status = STATUS_PENDING;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getExpiredDateTime() {
		return expiredDateTime;
	}

	public void setExpiredDateTime(String expiredDateTime) {
		this.expiredDateTime = expiredDateTime;
	}

	public String getIssuedDateTime() {
		return issuedDateTime;
	}

	public void setIssuedDateTime(String issuedDateTime) {
		this.issuedDateTime = issuedDateTime;
	}

	public String getConfirmedDateTime() {
		return confirmedDateTime;
	}

	public void setConfirmedDateTime(String confirmedDateTime) {
		this.confirmedDateTime = confirmedDateTime;
	}
	
	public int getResentLinkCount() {
		return resentLinkCount;
	}

	public void setResentLinkCount(int resentLinkCount) {
		this.resentLinkCount = resentLinkCount;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public static String getStatusPending() {
		return STATUS_PENDING;
	}

	public static String getStatusVerified() {
		return STATUS_VERIFIED;
	}
	
	
	public String getCurrentTimeinUTC() {

		Calendar calendar = Calendar.getInstance();
		Date d = calendar.getTime();

		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		df.setTimeZone(TimeZone.getTimeZone("UTC"));
		return df.format(d);
	}
	
	public String getPlusDays(String dateTime, String addDays) {

		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		Calendar cal = Calendar.getInstance();

		try {
			Date formatedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateTime);
			cal.setTime(formatedDate);
		} catch (ParseException e) {
			return getCurrentTimeinUTC();
		}

		cal.add(Calendar.HOUR, Integer.parseInt(addDays));

		return df.format(cal.getTime());
	}
	
	public String encrypt_UUID(String uuid) {
		
		if (uuid != null) {
			MessageDigest msgDigest = null;
			try {
				msgDigest = MessageDigest.getInstance("SHA1");
				msgDigest.update(uuid.getBytes());
			} catch (NoSuchAlgorithmException e) {
			}

			byte rawByte[] = msgDigest.digest();
			return bytesToHex(rawByte).toLowerCase();
		}
		return uuid;
	}
	
	public String bytesToHex(byte[] b) {

		StringBuffer buf = new StringBuffer();
		for (int j = 0; j < b.length; j++) {
			buf.append(hexDigit[(b[j] >> 4) & 0x0f]);
			buf.append(hexDigit[b[j] & 0x0f]);
		}
		return buf.toString();
	}
	
	public String generateNewToken() {
		String uuid = UUID.randomUUID().toString();
		return AES.encrypt(uuid.substring(0,10));
	}
}
