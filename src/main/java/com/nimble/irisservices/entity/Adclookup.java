package com.nimble.irisservices.entity;
// Generated Jan 10, 2019 6:07:46 PM by Hibernate Tools 4.3.1


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

/**
 * JAdclookup generated by hbm2java
 */
@Entity
@Table(name="adclookup") 
public class Adclookup  implements java.io.Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
     private Long id;
	
    @ManyToOne(fetch=FetchType.EAGER)
    @JoinColumn(name="probecategory")
     private ProbeCategory probecategory;
    
    @Column(name="adccount")
     private Integer adccount;
    
    @Column(name="output")
     private Float output;
    
    @Column(name="batterymode")
     private Float batterymode;
    
    @Column(name="chargingmode")
     private Float chargingmode;

    public Adclookup() {
    }

    public Adclookup(ProbeCategory probecategory, Integer adccount, Float output, Float batterymode, Float chargingmode) {
       this.probecategory = probecategory;
       this.adccount = adccount;
       this.output = output;
       this.batterymode = batterymode;
       this.chargingmode = chargingmode;
    }

    public Long getId() {
        return this.id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }

    public ProbeCategory getProbecategory() {
        return this.probecategory;
    }
    
    public void setProbecategory(ProbeCategory probecategory) {
        this.probecategory = probecategory;
    }

    

    public Integer getAdccount() {
        return this.adccount;
    }
    
    public void setAdccount(Integer adccount) {
        this.adccount = adccount;
    }

    public Float getOutput() {
        return this.output;
    }
    
    public void setOutput(Float output) {
        this.output = output;
    }

    
   
    public Float getBatterymode() {
        return this.batterymode;
    }
    
    public void setBatterymode(Float batterymode) {
        this.batterymode = batterymode;
    }
   
    public Float getChargingmode() {
        return this.chargingmode;
    }
    
    public void setChargingmode(Float chargingmode) {
        this.chargingmode = chargingmode;
    }

}


