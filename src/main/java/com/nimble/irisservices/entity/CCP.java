package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity 
@Table(name="ccp", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class CCP implements Serializable {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="user_id")
    private User user;
	
	@Column(name="description")
	private String description;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="type_id")
	//@JsonBackReference
    private CCPType type;
	
	@Column(name="min")
	private Long min;
	@Column(name="max")
	private Long max;
	@Column(name="corrective_action")
	private String corrective_action;
	
	@ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name="template_id")
    private CCPTemplateConfig ccpTemplateConfig;
	
	@Column(name="category")
	private String category;
	
	@Column(name="comments")
	private String comments;
	
	
	public CCP() {
		super();
		// TODO Auto-generated constructor stub
	}

	public CCP(long id,User user, String description, CCPType type, Long min, Long max, String corrective_action,
				String category,String comments,CCPTemplateConfig ccpTemplateConfig) {
		super();
		this.user = user;
		this.description = description;
		this.type = type;
		this.min = min;
		this.max = max;
		this.corrective_action = corrective_action;
		this.id = id;
		this.category = category;
		this.comments = comments;
		this.ccpTemplateConfig = ccpTemplateConfig;
	}
	
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public User getUser() {
		return user;
	}
	public void setUser(User user) {
		this.user = user;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public CCPType getType() {
		return type;
	}
	public void setType(CCPType type) {
		this.type = type;
	}
	public Long getMin() {
		return min;
	}
	public void setMin(Long min) {
		this.min = min;
	}
	public Long getMax() {
		return max;
	}
	public void setMax(Long max) {
		this.max = max;
	}
	public String getCorrective_action() {
		return corrective_action;
	}
	public void setCorrective_action(String corrective_action) {
		this.corrective_action = corrective_action;
	}

	public CCPTemplateConfig getCcpTemplateConfig() {
		return ccpTemplateConfig;
	}

	public void setCcpTemplateConfig(CCPTemplateConfig ccpTemplateConfig) {
		this.ccpTemplateConfig = ccpTemplateConfig;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	
	
}
