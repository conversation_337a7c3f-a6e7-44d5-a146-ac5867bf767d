package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "shop_banner")
public class ShopBanner {

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id;
	
	private String banner_url = "NA";
	
	private boolean action = false;
	
	private String action_url = "NA";
	
	private boolean external_redirect = true;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getBanner_url() {
		return banner_url;
	}

	public void setBanner_url(String banner_url) {
		this.banner_url = banner_url;
	}

	public boolean isAction() {
		return action;
	}

	public void setAction(boolean action) {
		this.action = action;
	}

	public String getAction_url() {
		return action_url;
	}

	public void setAction_url(String action_url) {
		this.action_url = action_url;
	}

	public boolean isExternal_redirect() {
		return external_redirect;
	}

	public void setExternal_redirect(boolean external_redirect) {
		this.external_redirect = external_redirect;
	}

	public ShopBanner() {
		super();
	}
	
	public ShopBanner(long id, String banner_url, boolean action, String action_url, boolean external_redirect) {
		super();
		this.id = id;
		this.banner_url = banner_url;
		this.action = action;
		this.action_url = action_url;
		this.external_redirect = external_redirect;
	}
	
	
}
