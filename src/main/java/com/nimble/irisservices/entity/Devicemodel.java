package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name="devicemodel", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class Devicemodel implements Serializable{

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@Column(name = "devicemodelnumber")
	private String devicemodelnumber;
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getDevicemodelnumber() {
		return devicemodelnumber;
	}

	public void setDevicemodelnumber(String devicemodelnumber) {
		this.devicemodelnumber = devicemodelnumber;
	}

}
