package com.nimble.irisservices.entity;

import static javax.persistence.GenerationType.IDENTITY;

import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name="furbitreport")
public class FurBitReport {
	private static final long serialVersionUID = 1L;
	
	@Id
	@Column(name="id",unique = true, nullable = false)
	@GeneratedValue(strategy=IDENTITY)
	private long id;
	
	@ManyToOne(cascade = CascadeType.MERGE, fetch = FetchType.LAZY)
	@JoinColumn(name="gateway_id")
	private Gateway gateway;
	
	@Column(name="packetversion")
	private String packetversion;
	
	@Column(name="date")
	private Date date;
	
	@Column(name="startdatetime")
	private Timestamp startdatetime;
	
	@Column(name="enddatetime")
	private Timestamp enddatetime;
	
	@Column(name="starttime")
	private Time starttime;
	
	@Column(name="endtime")
	private Time endtime;	
	
	@Column(name="idletime")
	private int idletime;
	
	@Column(name="walktime")
	private int walktime;
	
	@Column(name="runtime")
	private int runtime;
	
	@Column(name="stepcount")
	private int stepcount;
	
	@Column(name="transmitmode")
	private short transmitmode;
	
	@Column(name="sfwpkt")
	private short sfwpkt;
	
	@Column(name="reset")
	private short reset;
	
	@Column(name="createdon")
	private Timestamp createdon;

	/**
	 * @return the id
	 */
	public long getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(long id) {
		this.id = id;
	}

	/**
	 * @return the gateway
	 */
	public Gateway getGateway() {
		return gateway;
	}

	/**
	 * @param gateway the gateway to set
	 */
	public void setGateway(Gateway gateway) {
		this.gateway = gateway;
	}

	/**
	 * @return the packetversion
	 */
	public String getPacketversion() {
		return packetversion;
	}

	/**
	 * @param packetversion the packetversion to set
	 */
	public void setPacketversion(String packetversion) {
		this.packetversion = packetversion;
	}

	/**
	 * @return the date
	 */
	public Date getDate() {
		return date;
	}

	/**
	 * @param date the date to set
	 */
	public void setDate(Date date) {
		this.date = date;
	}

	/**
	 * @return the startdatetime
	 */
	public Timestamp getStartdatetime() {
		return startdatetime;
	}

	/**
	 * @param startdatetime the startdatetime to set
	 */
	public void setStartdatetime(Timestamp startdatetime) {
		this.startdatetime = startdatetime;
	}

	/**
	 * @return the enddatetime
	 */
	public Timestamp getEnddatetime() {
		return enddatetime;
	}

	/**
	 * @param enddatetime the enddatetime to set
	 */
	public void setEnddatetime(Timestamp enddatetime) {
		this.enddatetime = enddatetime;
	}

	/**
	 * @return the starttime
	 */
	public Time getStarttime() {
		return starttime;
	}

	/**
	 * @param starttime the starttime to set
	 */
	public void setStarttime(Time starttime) {
		this.starttime = starttime;
	}

	/**
	 * @return the endtime
	 */
	public Time getEndtime() {
		return endtime;
	}

	/**
	 * @param endtime the endtime to set
	 */
	public void setEndtime(Time endtime) {
		this.endtime = endtime;
	}

	/**
	 * @return the idletime
	 */
	public int getIdletime() {
		return idletime;
	}

	/**
	 * @param idletime the idletime to set
	 */
	public void setIdletime(int idletime) {
		this.idletime = idletime;
	}

	/**
	 * @return the walktime
	 */
	public int getWalktime() {
		return walktime;
	}

	/**
	 * @param walktime the walktime to set
	 */
	public void setWalktime(int walktime) {
		this.walktime = walktime;
	}

	/**
	 * @return the runtime
	 */
	public int getRuntime() {
		return runtime;
	}

	/**
	 * @param runtime the runtime to set
	 */
	public void setRuntime(int runtime) {
		this.runtime = runtime;
	}

	/**
	 * @return the stepcount
	 */
	public int getStepcount() {
		return stepcount;
	}

	/**
	 * @param stepcount the stepcount to set
	 */
	public void setStepcount(int stepcount) {
		this.stepcount = stepcount;
	}

	/**
	 * @return the transmitmode
	 */
	public short getTransmitmode() {
		return transmitmode;
	}

	/**
	 * @param transmitmode the transmitmode to set
	 */
	public void setTransmitmode(short transmitmode) {
		this.transmitmode = transmitmode;
	}

	/**
	 * @return the sfwpkt
	 */
	public short getSfwpkt() {
		return sfwpkt;
	}

	/**
	 * @param sfwpkt the sfwpkt to set
	 */
	public void setSfwpkt(short sfwpkt) {
		this.sfwpkt = sfwpkt;
	}

	/**
	 * @return the reset
	 */
	public short getReset() {
		return reset;
	}

	/**
	 * @param reset the reset to set
	 */
	public void setReset(short reset) {
		this.reset = reset;
	}

	/**
	 * @return the createdon
	 */
	public Timestamp getCreatedon() {
		return createdon;
	}

	/**
	 * @param createdon the createdon to set
	 */
	public void setCreatedon(Timestamp createdon) {
		this.createdon = createdon;
	}

}
