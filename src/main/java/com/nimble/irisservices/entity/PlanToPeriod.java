package com.nimble.irisservices.entity;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "plan_to_period", uniqueConstraints = @UniqueConstraint(columnNames = { "plan_id", "sub_period_id" }))
public class PlanToPeriod {
	private static final long serialVersionUID = 0l;

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "plan_id")
	private SubscriptionPlan plan_id;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "sub_period_id")
	private SubscriptionPeriod sub_period_id;

	@Column(name = "chargebee_planid ")
	private String chargebee_planid;

	@Column(name = "custom")
	private boolean custom;

	@Column(name = "enable")
	private boolean enable;

	@Column(name = "offer_desc ")
	private String offer_desc = "NA";

	@Column(name = "offer_id")
	private int offer_id = 0;

	@Column(name = "offer_label")
	private String offer_label = "NA";

	@Column(name = "offer_content")
	private String offer_content = "NA";

	@Column(name = "cb_addon_id")
	private String cb_addon_id = "NA";

	@Column(name = "cb_coupon_id")
	private String cb_coupon_id = "NA";

	@Column(name = "plan_price")
	private String plan_price = "NA";

	@Column(name = "free_trial_days")
	private int free_trial_days = 0;

	@Column(name = "strike_price")
	private int strike_price = 0;

	@Column(name = "content_1")
	private String content_1 = "NA";

	@Column(name = "content_2")
	private String content_2 = "NA";

	@Column(name = "content_3")
	private String content_3 = "NA";

	@Column(name = "content_4")
	private String content_4 = "NA";

	@Column(name = "is_best_deal")
	private boolean is_best_deal = false;

	@Column(name = "img_url")
	private String img_url = "NA";

	@Column(name = "country_code")
	private String country_code = "US";

	@Column(name = "is_recharge_plan")
	private boolean is_recharge_plan = false;

	@Column(name = "content_5")
	private String content_5 = "NA";

	@Column(name = "compare_image")
	private String compare_image = "NA";

	@Column(name = "product_image")
	private String product_image = "NA";

	@Column(name = "free_minicam_avil")
	private boolean free_minicam_avil = false;

	public PlanToPeriod() {
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public SubscriptionPlan getPlan_id() {
		return plan_id;
	}

	public void setPlan_id(SubscriptionPlan plan_id) {
		this.plan_id = plan_id;
	}

	public SubscriptionPeriod getSub_period_id() {
		return sub_period_id;
	}

	public void setSub_period_id(SubscriptionPeriod sub_period_id) {
		this.sub_period_id = sub_period_id;
	}

	public String getChargebee_planid() {
		return chargebee_planid;
	}

	public void setChargebee_planid(String chargebee_planid) {
		this.chargebee_planid = chargebee_planid;
	}

	public boolean isCustom() {
		return custom;
	}

	public void setCustom(boolean custom) {
		this.custom = custom;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getOffer_desc() {
		return offer_desc;
	}

	public void setOffer_desc(String offer_desc) {
		this.offer_desc = offer_desc;
	}

	public int getOffer_id() {
		return offer_id;
	}

	public void setOffer_id(int offer_id) {
		this.offer_id = offer_id;
	}

	public String getOffer_label() {
		return offer_label;
	}

	public void setOffer_label(String offer_label) {
		this.offer_label = offer_label;
	}

	public String getOffer_content() {
		return offer_content;
	}

	public void setOffer_content(String offer_content) {
		this.offer_content = offer_content;
	}

	public String getCb_addon_id() {
		return cb_addon_id;
	}

	public void setCb_addon_id(String cb_addon_id) {
		this.cb_addon_id = cb_addon_id;
	}

	public String getCb_coupon_id() {
		return cb_coupon_id;
	}

	public void setCb_coupon_id(String cb_coupon_id) {
		this.cb_coupon_id = cb_coupon_id;
	}

	public String getPlan_price() {
		return plan_price;
	}

	public void setPlan_price(String plan_price) {
		this.plan_price = plan_price;
	}

	public int getFree_trial_days() {
		return free_trial_days;
	}

	public void setFree_trial_days(int free_trial_days) {
		this.free_trial_days = free_trial_days;
	}

	public int getStrike_price() {
		return strike_price;
	}

	public void setStrike_price(int strike_price) {
		this.strike_price = strike_price;
	}

	public String getContent_1() {
		return content_1;
	}

	public void setContent_1(String content_1) {
		this.content_1 = content_1;
	}

	public String getContent_2() {
		return content_2;
	}

	public void setContent_2(String content_2) {
		this.content_2 = content_2;
	}

	public String getContent_3() {
		return content_3;
	}

	public void setContent_3(String content_3) {
		this.content_3 = content_3;
	}

	public String getContent_4() {
		return content_4;
	}

	public void setContent_4(String content_4) {
		this.content_4 = content_4;
	}

	public boolean isIs_best_deal() {
		return is_best_deal;
	}

	public void setIs_best_deal(boolean is_best_deal) {
		this.is_best_deal = is_best_deal;
	}

	public String getImg_url() {
		return img_url;
	}

	public void setImg_url(String img_url) {
		this.img_url = img_url;
	}

	public String getCountry_code() {
		return country_code;
	}

	public void setCountry_code(String country_code) {
		this.country_code = country_code;
	}

	public boolean isIs_recharge_plan() {
		return is_recharge_plan;
	}

	public void setIs_recharge_plan(boolean is_recharge_plan) {
		this.is_recharge_plan = is_recharge_plan;
	}

	public String getContent_5() {
		return content_5;
	}

	public void setContent_5(String content_5) {
		this.content_5 = content_5;
	}

	public String getCompare_image() {
		return compare_image;
	}

	public void setCompare_image(String compare_image) {
		this.compare_image = compare_image;
	}

	public String getProduct_image() {
		return product_image;
	}

	public void setProduct_image(String product_image) {
		this.product_image = product_image;
	}

	public boolean isFree_minicam_avil() {
		return free_minicam_avil;
	}

	public void setFree_minicam_avil(boolean free_minicam_avil) {
		this.free_minicam_avil = free_minicam_avil;
	}

}
