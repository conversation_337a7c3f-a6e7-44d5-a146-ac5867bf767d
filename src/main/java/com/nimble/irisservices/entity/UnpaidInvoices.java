package com.nimble.irisservices.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

@Entity(name="unpaid_invoices")
@Table(name="unpaid_invoices")
public class UnpaidInvoices {

	@Id
    @Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private int id;
	
	@Column(name="invoice_id")
	private String invoice_id;
	
	@Column(name="due_date")
	private String due_date = "1753-01-01 00:00:00";
	
	@Column(name="unpaid_date")
	private String unpaid_date = "1753-01-01 00:00:00";
	
	@Column(name="paid_date")
	private String paid_date = "1753-01-01 00:00:00";
	
	@Column(name="planid")
	private String planid = "NA";
	
	@Column(name="subscription_id")
	private String sub_id;
	
	@Column(name="chargebee_id")
	private String chargebeeId;
	
	@Column(name="invoicestatus")
	private String invoicestatus = "NA";
	
	@Column(name="status")
	private String status = "NA";
	
	@Column(name="paidstatus")
	private int paidstatus = 0;
	
	@Column(name="billing_email")
	private String billing_Email = "NA";
	
	@Column(name="updated_date")
	private String updated_date = "1753-01-01 00:00:00";
	
	@Transient
	private String name;
	
	@Transient
	private String mobileNo;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getInvoice_id() {
		return invoice_id;
	}

	public void setInvoice_id(String invoice_id) {
		this.invoice_id = invoice_id;
	}

	public String getDue_date() {
		return due_date;
	}

	public void setDue_date(String due_date) {
		this.due_date = due_date;
	}

	public String getPaid_date() {
		return paid_date;
	}

	public void setPaid_date(String paid_date) {
		this.paid_date = paid_date;
	}

	public String getPlanid() {
		return planid;
	}

	public void setPlanid(String planid) {
		this.planid = planid;
	}

	public String getSub_id() {
		return sub_id;
	}

	public void setSub_id(String sub_id) {
		this.sub_id = sub_id;
	}

	public String getChargebeeId() {
		return chargebeeId;
	}

	public void setChargebeeId(String chargebeeId) {
		this.chargebeeId = chargebeeId;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getBilling_Email() {
		return billing_Email;
	}

	public void setBilling_Email(String billing_Email) {
		this.billing_Email = billing_Email;
	}

	public String getUpdated_date() {
		return updated_date;
	}

	public void setUpdated_date(String updated_date) {
		this.updated_date = updated_date;
	}

	public String getInvoicestatus() {
		return invoicestatus;
	}

	public void setInvoicestatus(String invoicestatus) {
		this.invoicestatus = invoicestatus;
	}

	public String getUnpaid_date() {
		return unpaid_date;
	}

	public void setUnpaid_date(String unpaid_date) {
		this.unpaid_date = unpaid_date;
	}

	public int getPaidstatus() {
		return paidstatus;
	}

	public void setPaidstatus(int paidstatus) {
		this.paidstatus = paidstatus;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMobileNo() {
		return mobileNo;
	}

	public void setMobileNo(String mobileNo) {
		this.mobileNo = mobileNo;
	}
	
}