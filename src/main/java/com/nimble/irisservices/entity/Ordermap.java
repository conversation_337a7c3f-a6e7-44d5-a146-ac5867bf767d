package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.annotations.GenericGenerator;


@Entity
@Table(name="ordermap", uniqueConstraints =@UniqueConstraint (columnNames={"id","meid" } ))
public class Ordermap implements Serializable{

	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Min(1)
	@Column(name = "order_id")
	private int order_id;
	@Column(name = "order_date")
	private String order_date = "1111-11-11 11:11:11";
	@Column(name = "shipped_date")
	private String shipped_date = "1111-11-11 11:11:11";
	@Column(name = "customer_name")
	private String customer_name = "NA";
	/*@Column(name = "billing_first_name")
	private String billing_first_name = "NA";*/
	@Column(name = "billing_email")
	private String billing_email = "NA";
	@Column(name = "billing_phone")
	private String billing_phone = "0";	
	@Id
	@NotNull
	@Column(name = "meid")
	private String meid = "0";
	@Column(name = "mdn")
	private String mdn = "0";
	@Column(name = "user_id")
	private Long user_id ;
	@Column(name = "user_name")
	private String user_name = "NA";
	@Column(name = "password")
	private String password = "NA";
	@Column(name = "tracking_number")
	private String tracking_number = "NA";
	@Column(name = "device_model")
	private String device_model = "NA";
	@Column(name = "quantity")
	private String quantity = "0";
	@Column(name = "customer_type")
	private String customer_type = "NA";
	@Column(name = "device_status")
	private String device_status = "NA";
	@Column(name = "suspended_date")
	private String suspended_date = "1111-11-11 11:11:11";
	@Column(name = "activated_date")
	private String activated_date = "1111-11-11 11:11:11";
	@Column(name = "provision_status")
	private int provision_status = 0; // 0- provision yet to start or provision failed, 1- provision successful with 4 alerts
	/*@Column(name = "postal_service")
	private int postal_service = 1;*/
	@Transient
	private String address;
	
	@Transient
	private long locationids;
	
	@Transient
	private String resellerUser;
	
	@Transient
	private String nextPaymentDate;
	
	@Transient
	private String orderSku;
	
	@Transient
	private String billingPeriod;
	
	public Ordermap() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	public Ordermap(int order_id, String order_date,String shipped_date,String customer_name, String billing_email, String billing_phone,
			String meid, String user_name,String password, String tracking_number, String device_model,
			String quantity, String customer_type,String device_status, String suspended_date,String activated_date,int provision_status,
			Long user_id,String mdn) 
	{
		super();
		this.order_id = order_id;
		this.order_date= order_date;
		this.shipped_date = shipped_date;
		this.customer_name = customer_name;
		this.billing_email = billing_email;
		this.billing_phone = billing_phone;
		this.meid = meid;
		this.user_name = user_name;
		this.password = password;
		this.tracking_number = tracking_number;
		this.device_model = device_model;
		this.quantity = quantity;
		this.customer_type = customer_type;
		this.device_status = device_status;
		this.suspended_date  =suspended_date;
		this.activated_date = activated_date;
		this.provision_status = provision_status;
		//this.billing_first_name =billing_first_name;
		this.user_id = user_id;
		this.mdn=mdn;
	}
	
	public Ordermap(int order_id, String order_date,String customer_name, String billing_email, String billing_phone,
			String meid, String user_name, String tracking_number, String device_model,
			String quantity, String customer_type,String device_status, String suspended_date,String activated_date) 
	{
		this.order_id=order_id;
		this.order_date=order_date;
		this.customer_name=customer_name;
		this.billing_email=billing_email;
		this.billing_phone=billing_phone;
		this.meid=meid;
		this.user_name=user_name;
		this.tracking_number=tracking_number;
		this.device_model=device_model;
		this.quantity=quantity;
		this.customer_type=customer_type;
		this.device_status=device_status;
		this.suspended_date=suspended_date;
		this.activated_date=activated_date;
		
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}
	
	public int getOrder_id() {
		return order_id;
	}

	public void setOrder_id(int order_id) {
		this.order_id = order_id;
	}

	public String getOrder_date() {
		return order_date;
	}

	public void setOrder_date(String order_date) {
		this.order_date = order_date;
	}
	
	public String getShipped_date() {
		return shipped_date;
	}

	public void setShipped_date(String shipped_date) {
		this.shipped_date = shipped_date;
	}

	public String getCustomer_name() {
		return customer_name;
	}

	public void setCustomer_name(String customer_name) {
		this.customer_name = customer_name;
	}

	public String getBilling_email() {
		return billing_email;
	}

	public void setBilling_email(String billing_email) {
		this.billing_email = billing_email;
	}

	public String getBilling_phone() {
		return billing_phone;
	}

	public void setBilling_phone(String billing_phone) {
		this.billing_phone = billing_phone;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getUser_name() {
		return user_name;
	}

	public void setUser_name(String user_name) {
		this.user_name = user_name;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getTracking_number() {
		return tracking_number;
	}

	public void setTracking_number(String tracking_number) {
		this.tracking_number = tracking_number;
	}

	public String getDevice_model() {
		return device_model;
	}

	public void setDevice_model(String device_model) {
		this.device_model = device_model;
	}

	public String getQuantity() {
		return quantity;
	}

	public void setQuantity(String quantity) {
		this.quantity = quantity;
	}

	public String getCustomer_type() {
		return customer_type;
	}

	public void setCustomer_type(String customer_type) {
		this.customer_type = customer_type;
	}

	public String getDevice_status() {
		return device_status;
	}

	public void setDevice_status(String device_status) {
		this.device_status = device_status;
	}

	public String getSuspended_date() {
		return suspended_date;
	}

	public void setSuspended_date(String suspended_date) {
		this.suspended_date = suspended_date;
	}

	public String getActivated_date() {
		return activated_date;
	}

	public void setActivated_date(String activated_date) {
		this.activated_date = activated_date;
	}

	public int getProvision_status() {
		return provision_status;
	}

	public void setProvision_status(int provision_status) {
		this.provision_status = provision_status;
	}

	public Long getUser_id() {
		return user_id;
	}

	public void setUser_id(Long user_id) {
		this.user_id = user_id;
	}

/*	public String getBilling_first_name() {
		return billing_first_name;
	}

	public void setBilling_first_name(String billing_first_name) {
		this.billing_first_name = billing_first_name;
	}*/

	public String getMdn() {
		return mdn;
	}

	public void setMdn(String mdn) {
		this.mdn = mdn;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public long getLocationids() {
		return locationids;
	}

	public void setLocationids(long locationids) {
		this.locationids = locationids;
	}

	public String getResellerUser() {
		return resellerUser;
	}

	public void setResellerUser(String resellerUser) {
		this.resellerUser = resellerUser;
	}

	public String getNextPaymentDate() {
		return nextPaymentDate;
	}

	public void setNextPaymentDate(String nextPaymentDate) {
		this.nextPaymentDate = nextPaymentDate;
	}

	public String getOrderSku() {
		return orderSku;
	}

	public void setOrderSku(String orderSku) {
		this.orderSku = orderSku;
	}

	public String getBillingPeriod() {
		return billingPeriod;
	}

	public void setBillingPeriod(String billingPeriod) {
		this.billingPeriod = billingPeriod;
	}



/*	public int getPostal_service() {
		return postal_service;
	}

	public void setPostal_service(int postal_service) {
		this.postal_service = postal_service;
	}*/

	
}