package com.nimble.irisservices.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;


@Entity
@Table(name = "combo_content")
public class ComboContent {

    @Id
    @GenericGenerator(name = "gen", strategy = "identity")
    @GeneratedValue(generator = "gen")
    private long id;
    String title="NA";
    String content="NA";
    String imageUrl="NA";
    String full_imgurl="NA";




    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getFull_imgurl() {
        return full_imgurl;
    }

    public void setFull_imgurl(String full_imgurl) {
        this.full_imgurl = full_imgurl;
    }
}