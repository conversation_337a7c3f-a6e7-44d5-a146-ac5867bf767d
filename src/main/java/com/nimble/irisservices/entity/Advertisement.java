package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "advertisement", uniqueConstraints = @UniqueConstraint(columnNames = { "url" }))
public class Advertisement implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "imgpath")
	private String imgpath;
	
	@Column(name = "url")
	private String url;
	
	@Column(name = "title")
	private String title;

	@Column(name = "enable")
	private boolean enable;

	@Column(name = "createdon")
	private String createdon = "1753-01-01 00:00:00";
	
	@Column(name = "updatedon")
	private String updatedon = "1753-01-01 00:00:00";
	
	@Column(name = "expiredon")
	private String expiredon = "1753-01-01 00:00:00";
	
	@Column(name = "ver")
	private String ver;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getImgpath() {
		return imgpath;
	}

	public void setImgpath(String imgpath) {
		this.imgpath = imgpath;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getCreatedon() {
		return createdon;
	}

	public void setCreatedon(String createdon) {
		this.createdon = createdon;
	}

	public String getUpdatedon() {
		return updatedon;
	}

	public void setUpdatedon(String updatedon) {
		this.updatedon = updatedon;
	}

	public String getExpiredon() {
		return expiredon;
	}

	public void setExpiredon(String expiredon) {
		this.expiredon = expiredon;
	}

	public String getVer() {
		return ver;
	}

	public void setVer(String ver) {
		this.ver = ver;
	}	
}
