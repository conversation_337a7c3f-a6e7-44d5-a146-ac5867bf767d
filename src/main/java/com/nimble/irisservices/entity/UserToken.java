package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;


@Entity 
@Table(name="usertoken", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class UserToken implements Serializable{
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name="token")
	private String token;

	@Column(name="enable")
	private boolean enable;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="userId")
    private User userId;

	@Column(name="ostype")
	private String ostype="";
	
	@Column(name="updatedon")
	private String updatedon = "1753-01-01 00:00:00";
	
	@Column(name="ver")
	private String ver="v1";
	
	public UserToken() {
		super();
		// TODO Auto-generated constructor stub
	}

	public UserToken(String token, boolean enable, User userId,String ostype,String updatedon,String ver) {
		super();
		this.token = token;
		this.enable = enable;
		this.userId = userId;
		this.ostype= ostype;
		this.updatedon = updatedon;
		this.ver = ver;
	}

	public UserToken(long id, String token, boolean enable, User userId,String ostype,String updatedon) {
		super();
		this.id = id;
		this.token = token;
		this.enable = enable;
		this.userId = userId;
		this.ostype= ostype;
		this.updatedon = updatedon;
	}

	public User getUserId() {
		return userId;
	}

	public void setUserId(User userId) {
		this.userId = userId;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getOstype() {
		return ostype;
	}

	public void setOstype(String ostype) {
		this.ostype = ostype;
	}

	public String getCreatedon() {
		return updatedon;
	}

	public void setCreatedon(String createdon) {
		this.updatedon = createdon;
	}

	public String getUpdatedon() {
		return updatedon;
	}

	public void setUpdatedon(String updatedon) {
		this.updatedon = updatedon;
	}

	public String getVer() {
		return ver;
	}

	public void setVer(String ver) {
		this.ver = ver;
	}
	
}
