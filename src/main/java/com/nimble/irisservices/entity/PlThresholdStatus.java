package com.nimble.irisservices.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "pl_threshold_status")
public class PlThresholdStatus implements Serializable {

	@Id
	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;

	@Column(name = "gateway_id")
	private long gateway_id;

	@Column(name = "pl_threshold")
	private int pl_threshold = 0;

	@Column(name = "status")
	private String status = "NA";

	@Column(name = "updated_on")
	private String updated_on = "1753-01-01 00:00:00";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public int getPl_threshold() {
		return pl_threshold;
	}

	public void setPl_threshold(int pl_threshold) {
		this.pl_threshold = pl_threshold;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

}
