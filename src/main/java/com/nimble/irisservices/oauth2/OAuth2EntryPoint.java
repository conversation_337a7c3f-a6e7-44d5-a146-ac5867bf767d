package com.nimble.irisservices.oauth2;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import com.google.gson.Gson;
import com.nimble.irisservices.dto.JResponse;

@Component
public class OAuth2EntryPoint implements AuthenticationEntryPoint {
	
	@Value("${service.url}")
	private String errorpage;
	
	private static final Logger log = LogManager.getLogger(OAuth2EntryPoint.class);

	@Override
	public void commence(HttpServletRequest request, HttpServletResponse response,
			AuthenticationException authException) throws IOException, ServletException {
		
		String msg = authException.getMessage();
		String requestURI = request.getRequestURI();
		String requestMethod = request.getMethod();
		
		Gson gson = new Gson();
		JResponse jResponse = new JResponse();
		
		jResponse.put("Status", -1);
		jResponse.put("Msg", "Unauthorized");	

		try {
			
			response.getWriter().append(gson.toJson(jResponse));
			
			String url = errorpage+"/error";
			
			if( !requestMethod.equalsIgnoreCase("OPTIONS") ) { // Ignore preflight request
					
				if( msg.equalsIgnoreCase("Full authentication is required to access this resource") ) {
					response.setHeader("Location",url);
					response.sendRedirect(url);
				}
				
				if( msg.contains("token expired") && ( requestURI.contains("/irisservices/web/") ) )
					response.setStatus(401);

				if( requestURI.contains("/v5.0/devicelist") )
					response.setStatus(401);
					
				String auth = request.getHeader("auth");
				log.info("Entered into oauth entrypoint :: auth : "+ auth);
				log.info("Error Msg : "+ msg);
				log.info("Request Method : "+ requestMethod);
				log.info("Request URL : "+ requestURI);
			}
			
		} catch (IOException e) {
			log.error("Error in oauth entry point :: Error : "+ e.getLocalizedMessage());
		}
		
	}

}
