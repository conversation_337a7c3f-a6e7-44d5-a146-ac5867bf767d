package com.nimble.irisservices.oauth2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;

import com.nimble.irisservices.service.impl.OAuth2CustomUserDetailsServiceImpl;

@EnableWebSecurity
@EnableAuthorizationServer
@EnableResourceServer
public class MySecurityConfig extends WebSecurityConfigurerAdapter {

	@Autowired
	OAuth2CustomUserDetailsServiceImpl customUserDetailsService;

	@Override
	protected void configure(AuthenticationManagerBuilder auth) throws Exception {
		auth.userDetailsService(customUserDetailsService).passwordEncoder(passwordEncoder());
	}
	
	@Override
	@Bean
	public AuthenticationManager authenticationManagerBean() throws Exception {
		return super.authenticationManagerBean();
	}


	public PasswordEncoder passwordEncoder() {
		return new BCryptPasswordEncoder();
//		return NoOpPasswordEncoder.getInstance();
	}

	@Override
	public void configure(WebSecurity web) throws Exception {

//		RequestMatcher webRequests = new AntPathRequestMatcher("/web/**");
//		RequestMatcher notWebRequests = new NegatedRequestMatcher(webRequests);

		web.ignoring().antMatchers("/v1.0/**").antMatchers("/v2.0/**").antMatchers("/v3.0/**").antMatchers("/v4.0/**").antMatchers("/v5.0/**")
				.antMatchers("/ext/**").antMatchers("/alexa/**").antMatchers("/").antMatchers("/checkirisservice").antMatchers("/error")
				.antMatchers("/alexawaggle").antMatchers("/checkverizonserviceV2").antMatchers("/checkverizonservice")
				.antMatchers("/web/v3.0/otp/**")
				.antMatchers("/app/v4.0/gatewaysummaryWatch/**")
				.antMatchers("/app/v4.0/listpetprofileWatch/**")
				.antMatchers("/app/v1.0/deleteuserpl")
				.antMatchers("/app/v5.0/gatewaysummaryWatch/**")
				.antMatchers("/app/v5.0/listpetprofileWatch/**")
				.antMatchers("/web/wc/v5.0/**")
				.antMatchers("/web/v5.0/returncancelsubplan/*")
				.antMatchers("/v5.0/sendotp/**")
				//swagger
				.antMatchers("/v2/api-docs")
				.antMatchers("/v3/api-docs")
				.antMatchers("/swagger-ui/**")
				.antMatchers("/swagger-resources/**");
	}
	
	@Override
	protected void configure(HttpSecurity http) throws Exception {
		http.csrf().disable().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.NEVER).and()
				.authorizeRequests().antMatchers("/web/**").hasAnyRole("delete", "write", "read").antMatchers("/app/**")
				.hasAnyRole("delete", "write", "read").anyRequest().permitAll();
	}

	
}
