package com.nimble.irisservices.oauth2;

import java.util.LinkedHashMap;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;

import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IUserServiceV4;


public class CustomTokenEnhancer extends JwtAccessTokenConverter {

	@Autowired
	IUserServiceV4 userServiceV4;
    
	@Autowired
	Helper _helper;
	
	private static final Logger log = LogManager.getLogger(CustomTokenEnhancer.class);

	@Override
	public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
		log.info("Entered to enhance : accessToken : "+accessToken);
		try {
		Map<String, Object> info = new LinkedHashMap<>(accessToken.getAdditionalInformation());	
		
		DefaultOAuth2AccessToken customAccessToken = new DefaultOAuth2AccessToken(accessToken);
		
		String createdOn = _helper.getCurrentTimeinUTC();
		
		info.put("created_on", createdOn);
		
		customAccessToken.setAdditionalInformation(info);
		
		return super.enhance(customAccessToken, authentication);
		}catch(Exception e) {
			log.error("Exception occured at enhance for OAuth2AccessToken : "+e.getLocalizedMessage());
		}
		return accessToken;
	}
	
    

}
