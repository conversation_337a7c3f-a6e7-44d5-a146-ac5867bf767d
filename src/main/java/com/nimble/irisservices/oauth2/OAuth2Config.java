package com.nimble.irisservices.oauth2;



import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.approval.DefaultUserApprovalHandler;
import org.springframework.security.oauth2.provider.approval.UserApprovalHandler;
import org.springframework.security.oauth2.provider.code.JdbcAuthorizationCodeServices;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;

import com.nimble.irisservices.service.impl.OAuth2CustomUserDetailsServiceImpl;

@Configuration
public class OAuth2Config extends AuthorizationServerConfigurerAdapter {

	private String privateKey = "-----BEGIN RSA PRIVATE KEY-----MIIEpAIBAAKCAQEAu7yaHBZ7lGnsBbJd1uFq6n9wDfwoZYfGYjnaaDbibJNVKhnxo5oNdd9G6ufxJ5yGPq52mSSLDWpoNU6wHIDEXC4d7oNba23R/J+FMd/LZ91WdkRr3hRXVrf92pSZSwURDUxFTWpS7J4vuv1mz5xLHyjERb1K8/0rDNMN2j1dY55faHC68tlguNwKy0u/9Oh9IZKAfbY9XbuYtGyXhzJMVGgEGYMV72L5+DA5702IPORy3dvCCyVJhvJxUg5yfXuUzm0eOWVabuGaLqcjCESKdy/KEjQ0c2TPmSQqW7ZmeTaXOI/uy5TtlUyhoj+yvbSDDXriETPGRuRzAXcBdS4QrwIDAQABAoIBAQC0YiFJP6j6D1RCnQFhSDIsosoOBezisTTCiIf8fk/MtzurOkZdRdYNHb3MU5P41YJ/k65MszoQZ9886SB6IpeJYp5L4FbkwB51tuiJ5VhRsfAzrsZeGflZT2xQq1snI/Uga9pi6fhHdi6yYmx4qtnbVJZR0/HCwcytg9TZjwC+h3TCd24GpTB+u0N3cSPVyqH3cDkYBBlScq0xSTylj4WLNcUqKeF9mewvRUUYpwoRqpi3GJaYk59/IinEbIpkOqcFC4qHJsyMTlON43eF7GVcuLj7c7S0yarMG1J4qpFl0G8X/gtp0jXQ77sGv7hfq4u69BjRnDMjm/VEPuOSjQIRAoGBAO3LvxV86OMLS4PCPD8d1wiNSGbticPUiOb1PlWaTQxnfBBzBHFxnHsAgY8J6tmlKmAwqt/yBW4YuBhkwz6xn81KQXOM5RHeB7dRae//kik42X+GCFlrvD54NRmJDYdxEKPZ/gsAKY+NUa8uDSqUarIF9WxCDiWdN1qqy9PsCZ9TAoGBAMobz5tFvi5BkDnQVECq6FjFfQ3Dq3LqSeB5mFdQfVt2Mp2Ru3y1igcU0T8hRt0Ig++ofoBoyBN9YIjKd9I2/IZ+vKQ/ZVjfMDheD2ikp7Llq3R6R9diohJxVM4FEypnP1byPvlI9hsHO+vAiwgcZKSE4ByuDvKU+om0zIgIz4m1AoGAVXxRjWACONzuoMdK8UXA8AoaZSYvyH63WJABgLSfzj3FlARFLKMzJeTgrpNaIjQGL95PD4LqANJSHDLdHcStNhfjv65/CxVOoNBbzTOLT9ZoGjgM+FiKq8p7DFsAyhgTa1QKx9mfF0QugVOE2DHSyXzq5ag5fbwvEiEkdWL9cpECgYEAuV9UxafsTWTZMg3AmSeowHMyxgMoJ9h4NimQ7tTmhZMdzaQzAKlJlkEjwvF0m5sJ2DEFBiYVDLs8ZXq+99JzP7DSuRtki2T6R1kObP5Iyi8ZWVYmuoi++od0Lxo8f/H/CHJcDC09fYDfFtadLq1MKhGttOB42UqHmPJ5fnQGVtkCgYAlpEwfHDfg/EJSTp0fh78fL6ibL0dQFyjUA/xDta7faluDzMg1nO6yG5iXMMLqBX3BirXhGj1V2sNpEco94fw/hIDy8S9MiuPBIzgFhRoX88btKYz8a44wy69jBibW7jCO1FHPhYybCbODLvsSG67JdqPAajZ8T7L+c4QT07npOg==-----END RSA PRIVATE KEY-----"; 

	private String publicKey = "-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu7yaHBZ7lGnsBbJd1uFq6n9wDfwoZYfGYjnaaDbibJNVKhnxo5oNdd9G6ufxJ5yGPq52mSSLDWpoNU6wHIDEXC4d7oNba23R/J+FMd/LZ91WdkRr3hRXVrf92pSZSwURDUxFTWpS7J4vuv1mz5xLHyjERb1K8/0rDNMN2j1dY55faHC68tlguNwKy0u/9Oh9IZKAfbY9XbuYtGyXhzJMVGgEGYMV72L5+DA5702IPORy3dvCCyVJhvJxUg5yfXuUzm0eOWVabuGaLqcjCESKdy/KEjQ0c2TPmSQqW7ZmeTaXOI/uy5TtlUyhoj+yvbSDDXriETPGRuRzAXcBdS4QrwIDAQAB-----END PUBLIC KEY-----";

	@Autowired
	@Qualifier("passwordEncoder")
	PasswordEncoder passwordEncoder;

	@Autowired
	DataSource dataSource;

	@Autowired
	@Qualifier("authenticationManagerBean")
	private AuthenticationManager authenticationManager;
	
	@Autowired
	OAuth2CustomUserDetailsServiceImpl customUserDetailsService;

	@Bean
	public JwtAccessTokenConverter tokenEnhancer() {
		JwtAccessTokenConverter converter = new CustomTokenEnhancer();
		converter.setSigningKey(privateKey);
		converter.setVerifierKey(publicKey);
		return converter;
	}

	@Bean
	public JwtTokenStore tokenStore() {
		return new JwtTokenStore(tokenEnhancer());
	}

	@Override
	public void configure(AuthorizationServerEndpointsConfigurer endpoints) throws Exception {
		
		endpoints.pathMapping("/oauth/token", "/web/oauth/token")
				.authenticationManager(authenticationManager).tokenStore(tokenStore())
				.accessTokenConverter(tokenEnhancer()).authorizationCodeServices(authorizationCodeServices())
				.userApprovalHandler(userApprovalHandler())
				.userDetailsService( customUserDetailsService );
	}
	
	public JdbcAuthorizationCodeServices authorizationCodeServices() {
		 return new JdbcAuthorizationCodeServices(dataSource);
	}
	
	public UserApprovalHandler userApprovalHandler() {
		return new DefaultUserApprovalHandler();
	}

	@Override
	public void configure(AuthorizationServerSecurityConfigurer security) throws Exception {
		security.tokenKeyAccess("permitAll()").checkTokenAccess("isAuthenticated()");
	}

	@Override
	public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
//		clients.jdbc(dataSource);
		clients.jdbc(dataSource).passwordEncoder(passwordEncoder());
	}
	
	@Bean
	public PasswordEncoder passwordEncoder() {
		return new BCryptPasswordEncoder();
	}

}
