package com.nimble.irisservices.oauth2;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.header.HeaderWriter;

@Configuration	
@EnableResourceServer
public class ResourceServerConfig extends ResourceServerConfigurerAdapter{

	@Autowired
	public AuthenticationEntryPoint oAuthEntryPoint;
	
	@Override
	public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
		super.configure(resources);
		resources.authenticationEntryPoint(oAuthEntryPoint);
	}

	@Override
	public void configure(HttpSecurity http) throws Exception {
		http.exceptionHandling().authenticationEntryPoint(oAuthEntryPoint).and().headers().addHeaderWriter( addHeaders() );
		super.configure(http);
	}

	private HeaderWriter addHeaders() {
		HeaderWriter headers = new HeaderWriter() {
			
			@Override
			public void writeHeaders(HttpServletRequest request, HttpServletResponse response) {
				response.setHeader("Access-Control-Allow-Origin", "*");
				response.setHeader("Access-Control-Allow-Methods", "POST, GET, PUT, OPTIONS, DELETE");
				response.setHeader("Access-Control-Max-Age", "1800");
				// Kindly add CorsFilter.java too !!!
				response.setHeader("Access-Control-Allow-Headers",
						"Origin,x-requested-with, content-type,Accept,authorization,username,password,backing,refresh_token,request_from,auth,user_id");
				response.setHeader("Access-Control-Allow-Credentials", "true");
				
			}
		};
		return headers;
	}

	
	
}
