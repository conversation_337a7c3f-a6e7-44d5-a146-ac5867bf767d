package com.nimble.irisservices.quartz;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.quartz.ObjectAlreadyExistsException;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleTrigger;
import org.quartz.Trigger;
import org.quartz.Trigger.TriggerState;
import org.quartz.TriggerKey;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.job.SIMRetryJob;

@Service
public class JobServiceImpl implements JobService {

	private static final Logger log = LogManager.getLogger(JobServiceImpl.class);
	
	@Autowired
	@Lazy
	SchedulerFactoryBean schedulerFactoryBean;

	@Autowired
	private ApplicationContext context;

	@Autowired
	@Lazy
	JobService jobService;
	
	/**
	 * Schedule a job by jobName at given date.
	 */
	@Override
	public boolean scheduleOneTimeJob(String jobName, Class<? extends QuartzJobBean> jobClass, Date date,
			String userid) {
		log.info("Request received to scheduleJob");
		log.info("JobKey : " + jobName);
		log.info("groupKey : " + userid);

		String jobKey = jobName;
		String groupKey = userid;
		String triggerKey = jobName;

		JobDetail jobDetail = JobUtil.createJob(jobClass, false, context, jobKey, groupKey, userid);

		log.info("creating trigger for key :" + jobKey + " at date :" + date);
		Trigger cronTriggerBean = JobUtil.createSingleTrigger(triggerKey, date, groupKey,
				SimpleTrigger.MISFIRE_INSTRUCTION_FIRE_NOW );

		try {
			Scheduler scheduler = schedulerFactoryBean.getScheduler();
			schedulerFactoryBean.setSchedulerName("nimble_scheduler");
			Date dt = scheduler.scheduleJob(jobDetail, cronTriggerBean);
			log.info("Job with key jobKey :" + jobKey + " and group :" + groupKey
					+ " scheduled successfully for date :" + dt);
			return dt != null;
		} catch (SchedulerException e) {
			log.error("SchedulerException while scheduling job with key :" + jobKey + " message :" + e.getMessage());
		}

		return false;
	}

	/**
	 * Schedule a job by jobName at given date.
	 */
	@Override
	public boolean scheduleRepeatJob(String jobName, Class<? extends QuartzJobBean> jobClass, Date date, String userid,
			String repeatedTime, int intervel) {
		log.info("Request received to scheduleJob");

		String jobKey = jobName;
		String groupKey = userid;
		String triggerKey = jobName;

		JobDetail jobDetail = JobUtil.createJob(jobClass, false, context, jobKey, groupKey, userid);

		log.info("creating trigger for key :" + jobKey + " at date :" + date);

		Trigger cronTriggerBean = JobUtil.createCronTrigger(triggerKey, date, userid, repeatedTime, intervel);

		try {

			Scheduler scheduler = schedulerFactoryBean.getScheduler();
			schedulerFactoryBean.setSchedulerName("nimble_scheduler");
			Date dt = scheduler.scheduleJob(jobDetail, cronTriggerBean);
			log.info("Job with key jobKey :" + jobKey + " and group :" + groupKey
					+ " scheduled successfully for date :" + dt);
			return dt != null;
		} catch (SchedulerException e) {
			log.error(
					"SchedulerException while scheduling job with key :" + jobKey + " message :" + e.getMessage());
		}

		return false;
	}

	/**
	 * Update one time scheduled job.
	 */
	@Override
	public boolean updateOneTimeJob(String jobName, Date date, String groupKey) {
		log.info("Request received for updating one time job.");

		String jobKey = jobName;

		log.info("Parameters received for updating one time job : jobKey :" + jobKey + ", date: " + date);
		try {
			Trigger newTrigger = JobUtil.createSingleTrigger(jobKey, date, groupKey,
					SimpleTrigger.MISFIRE_INSTRUCTION_FIRE_NOW);

			TriggerKey oldTrigger = TriggerKey.triggerKey(jobKey, groupKey);

			Date dt = schedulerFactoryBean.getScheduler().rescheduleJob(oldTrigger, newTrigger);
			log.info("Trigger associated with jobKey :" + jobKey + " rescheduled successfully for date :" + dt);

			return dt != null;
		} catch (Exception e) {
			log.error("SchedulerException while updating one time job with key :" + jobKey + " message :"
					+ e.getMessage());
			return false;
		}
	}

	/**
	 * Update scheduled cron job.
	 */
	@Override
	public boolean updateRepeatJob(String jobName, Date date, String userid, String repeatedType, int intervel) {
		log.info("Request received for updating cron job.");

		String jobKey = jobName;

		log.info("Parameters received for updating cron job : jobKey :" + jobKey + ", date: " + date);
		try {

			Trigger newTrigger = JobUtil.createCronTrigger(jobKey, date,  userid, repeatedType, intervel);

			TriggerKey oldTrigger = TriggerKey.triggerKey(jobKey, userid);

			Date dt = schedulerFactoryBean.getScheduler().rescheduleJob(oldTrigger, newTrigger);
			log.info("Trigger associated with jobKey :" + jobKey + " rescheduled successfully for date :" + dt);
			return dt != null;
		} catch (Exception e) {
			log.error(
					"SchedulerException while updating cron job with key :" + jobKey + " message :" + e.getMessage());
			return false;
		}
	}

	/**
	 * Remove the indicated Trigger from the scheduler. If the related job does not
	 * have any other triggers, and the job is not durable, then the job will also
	 * be deleted.
	 */
	@Override
	public boolean unScheduleJob(String jobName) {
		log.info("Request received for Unscheduleding job.");

		String jobKey = jobName;

		TriggerKey tkey = new TriggerKey(jobKey);
		log.info("Parameters received for unscheduling job : tkey :" + jobKey);
		try {
			boolean status = schedulerFactoryBean.getScheduler().unscheduleJob(tkey);
			log.info("Trigger associated with jobKey :" + jobKey + " unscheduled with status :" + status);
			return status;
		} catch (SchedulerException e) {
			log.error(
					"SchedulerException while unscheduling job with key :" + jobKey + " message :" + e.getMessage());
			return false;
		}
	}

	/**
	 * Delete the identified Job from the Scheduler - and any associated Triggers.
	 */
	@Override
	public boolean deleteJob(String jobName, String userid) {
		log.info("Request received for deleting job.");

		String jobKey = jobName;
		String groupKey = userid;
		
		JobKey jkey = new JobKey(jobKey, groupKey);
		log.info("Parameters received for deleting job : jobKey :" + jobKey);

		try {
			boolean status = schedulerFactoryBean.getScheduler().deleteJob(jkey);
			log.info("Job with jobKey :" + jobKey + " deleted with status :" + status);
			return status;
		} catch (SchedulerException e) {
			log.error(
					"SchedulerException while deleting job with key :" + jobKey + " message :" + e.getMessage());
			return false;
		}
	}

	/**
	 * Pause a job
	 */
	@Override
	public boolean pauseJob(String jobName) {
		log.info("Request received for pausing job.");

		String jobKey = jobName;
		String groupKey = "SampleGroup";
		JobKey jkey = new JobKey(jobKey, groupKey);
		log.info("Parameters received for pausing job : jobKey :" + jobKey + ", groupKey :" + groupKey);

		try {
			schedulerFactoryBean.getScheduler().pauseJob(jkey);
			log.info("Job with jobKey :" + jobKey + " paused succesfully.");
			return true;
		} catch (SchedulerException e) {
			log.info(
					"SchedulerException while pausing job with key :" + jobName + " message :" + e.getMessage());
			return false;
		}
	}

	/**
	 * Resume paused job
	 */
	@Override
	public boolean resumeJob(String jobName) {
		log.info("Request received for resuming job.");

		String jobKey = jobName;
		String groupKey = "SampleGroup";

		JobKey jKey = new JobKey(jobKey, groupKey);
		log.info("Parameters received for resuming job : jobKey :" + jobKey);
		try {
			schedulerFactoryBean.getScheduler().resumeJob(jKey);
			log.info("Job with jobKey :" + jobKey + " resumed succesfully.");
			return true;
		} catch (SchedulerException e) {
			log.info(
					"SchedulerException while resuming job with key :" + jobKey + " message :" + e.getMessage());
			return false;
		}
	}

	/**
	 * Start a job now
	 */
	@Override
	public boolean startJobNow(String jobName) {
		log.info("Request received for starting job now.");

		String jobKey = jobName;
		String groupKey = "SampleGroup";

		JobKey jKey = new JobKey(jobKey, groupKey);
		log.info("Parameters received for starting job now : jobKey :" + jobKey);
		try {
			schedulerFactoryBean.getScheduler().triggerJob(jKey);
			log.info("Job with jobKey :" + jobKey + " started now succesfully.");
			return true;
		} catch (SchedulerException e) {
			log.info(
					"SchedulerException while starting job now with key :" + jobKey + " message :" + e.getMessage());
			return false;
		}
	}

	/**
	 * Check if job is already running
	 */
	@Override
	public boolean isJobRunning(String jobName, String userid) {
		log.info("Request received to check if job is running");

		String jobKey = jobName;
		String groupKey = userid;

		log.info("Parameters received for checking job is running now : jobKey :" + jobKey);
		try {
			
			List<JobExecutionContext> currentJobs = schedulerFactoryBean.getScheduler().getCurrentlyExecutingJobs();
			if (currentJobs != null) {
				for (JobExecutionContext jobCtx : currentJobs) {
					String jobNameDB = jobCtx.getJobDetail().getKey().getName();
					String groupNameDB = jobCtx.getJobDetail().getKey().getGroup();
				
					if (jobKey.equalsIgnoreCase(jobNameDB) && groupKey.equalsIgnoreCase(groupNameDB)) {
						return true;
					}
				}
			}
		} catch (SchedulerException e) {
			log.info("SchedulerException while checking job with key :" + jobKey
					+ " is running. error message :" + e.getMessage());
			return false;
		}
		return false;
	}

	/**
	 * Get all jobs
	 */
	@Override
	public List<Map<String, Object>> getAllJobs(String userid, String remainder) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		log.info("getalljobs :: "+userid);
		try {
			Scheduler scheduler = schedulerFactoryBean.getScheduler();

			for (JobKey jobKey : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(userid))) {

				String jobName = jobKey.getName();
				String jobGroup = jobKey.getGroup();
				// get job's trigger
				List<Trigger> triggers = (List<Trigger>) scheduler.getTriggersOfJob(jobKey);
				Date scheduleTime = triggers.get(0).getStartTime();
				Date nextFireTime = triggers.get(0).getNextFireTime();
				Date lastFiredTime = triggers.get(0).getPreviousFireTime();

				Map<String, Object> map = new HashMap<String, Object>();
				map.put("jobName", jobName);
				map.put("groupName", jobGroup);
				map.put("scheduleTime", scheduleTime);
				map.put("lastFiredTime", lastFiredTime);
				map.put("nextFireTime", nextFireTime);

				if (isJobRunning(jobName, userid)) {
					map.put("jobStatus", "RUNNING");
				} else {
					String jobState = getJobState(jobName, userid);
					map.put("jobStatus", jobState);
				}

				if (remainder == null || remainder.isEmpty())
					list.add(map);
				else if (remainder.equals(jobName))
					list.add(map);

				log.info("Job details:");
				log.info("Job Name:" + jobName + ", Group Name:" + userid + ", Schedule Time:" + scheduleTime);
			}

		} catch (SchedulerException e) {
			log.error("SchedulerException while fetching all jobs. error message :" + e.getMessage());
		}
		return list;
	}

	/**
	 * Check job exist with given name
	 */
	@Override
	public boolean isJobWithNamePresent(String jobName, String groupKey) {
		log.info("Entering isJobWithNamePresent :: ");
		try {
			JobKey jobKey = new JobKey(jobName, groupKey);
			Scheduler scheduler = schedulerFactoryBean.getScheduler();
			if (scheduler.checkExists(jobKey)) {
				return true;
			}
		} catch (SchedulerException e) {
			log.error("SchedulerException while checking job with name and group exist:" + e.getMessage());
		}
		return false;
	}

	/**
	 * Get the current state of job
	 */
	public String getJobState(String jobName, String groupKey) {
		log.info("JobServiceImpl.getJobState()");

		try {
			JobKey jobKey = new JobKey(jobName, groupKey);

			Scheduler scheduler = schedulerFactoryBean.getScheduler();
			JobDetail jobDetail = scheduler.getJobDetail(jobKey);

			List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobDetail.getKey());
			if (triggers != null && triggers.size() > 0) {
				for (Trigger trigger : triggers) {
					TriggerState triggerState = scheduler.getTriggerState(trigger.getKey());

					if (TriggerState.PAUSED.equals(triggerState)) {
						return "PAUSED";
					} else if (TriggerState.BLOCKED.equals(triggerState)) {
						return "BLOCKED";
					} else if (TriggerState.COMPLETE.equals(triggerState)) {
						return "COMPLETE";
					} else if (TriggerState.ERROR.equals(triggerState)) {
						return "ERROR";
					} else if (TriggerState.NONE.equals(triggerState)) {
						return "NONE";
					} else if (TriggerState.NORMAL.equals(triggerState)) {
						return "SCHEDULED";
					}
				}
			}
		} catch (SchedulerException e) {
			log.error("SchedulerException while checking job with name and group exist:" + e.getMessage());
		}
		return null;
	}

	/**
	 * Stop a job
	 */
	@Override
	public boolean stopJob(String jobName) {
		log.info("JobServiceImpl.stopJob()");
		try {
			String jobKey = jobName;
			String groupKey = "SampleGroup";

			Scheduler scheduler = schedulerFactoryBean.getScheduler();
			JobKey jkey = new JobKey(jobKey, groupKey);

			return scheduler.interrupt(jkey);

		} catch (SchedulerException e) {
			log.error("SchedulerException while stopping job. error message :" + e.getMessage());
		}
		return false;
	}

	@Override
	public boolean ScheduleRetry(String iccid, String imei, String reqId, String skuNumber, String action,int repeatCount, int scheduleInterval) {
		log.info("Request received to scheduleJob");
		log.info("JobKey : " + iccid);
		log.info("groupKey : " + action);

		String jobKey = iccid;
		String groupKey = action;
		String triggerKey = iccid;

		JobDetail jobDetail = JobUtil.createSIMRetryJob(SIMRetryJob.class, false, context, jobKey, groupKey,imei,reqId,skuNumber);

		log.info("creating trigger for key :" + jobKey + " at date :" + new Date(System.currentTimeMillis()));
		Trigger cronTriggerBean = JobUtil.createSIMRetryTrigger(triggerKey, new Date(System.currentTimeMillis()), groupKey,
				SimpleTrigger.MISFIRE_INSTRUCTION_FIRE_NOW , repeatCount,  scheduleInterval);

		try {
			Scheduler scheduler = schedulerFactoryBean.getScheduler();
			schedulerFactoryBean.setSchedulerName("nimble_scheduler");
			Date dt = scheduler.scheduleJob(jobDetail, cronTriggerBean);
			log.info("Job with key jobKey :" + jobKey + " and group :" + groupKey
					+ " scheduled successfully for date :" + dt);
			return dt != null;
		} catch (SchedulerException e) {
			log.error("SchedulerException while scheduling job with key :" + jobKey + " message :" + e.getMessage());
		}

		return false;
	}

	@Override
	public boolean scheduleRepeatJobWithMultipleTriggers(String jobName, Class<? extends QuartzJobBean> jobClass,
			List<Date> dateList, String groupKey, String userid, String repeatedTime, int intervel, HashMap<String, Object> jobDetailMap ) {
		log.info("Entered into scheduleRepeatJobWithMultipleTriggers :: job_name : "+jobName+" :: group_key : "+ userid);
		try {
			String jobKey = jobName;
			String triggerKey = jobName;

			JobDetail jobDetail = JobUtil.createJob(jobClass, true, context, jobKey, groupKey, userid);
			
			if( jobDetail == null )
				log.info("Job creation failed");
			
			log.info("Job created successfully");
			
			if( jobDetailMap != null )
			jobDetail.getJobDataMap().putAll(jobDetailMap);
			
			Scheduler scheduler = schedulerFactoryBean.getScheduler();

			boolean isJobExist = scheduler.checkExists(jobDetail.getKey());
			log.info("Is job exist : "+ isJobExist);
			
			if( isJobExist ) {
				boolean status = scheduler.deleteJob( jobDetail.getKey() );
				log.info("delete job : "+jobDetail.getKey() +" :: status : "+ status);
			}
			
			scheduler.addJob(jobDetail, true);
			log.info("Check job exist or not :: job : "+ jobDetail.getKey());
			
			int count = 1;
			for( Date date : dateList ) {
				log.info("creating trigger for key :" + jobKey + " at date :" + date);

				Trigger cronTriggerBean = JobUtil.createCronTrigger(triggerKey+"_"+count++, date, userid, "day", 1);
				Date dt = null;
				try {
					
					dt = scheduler.scheduleJob(jobDetail, cronTriggerBean);	
				
					log.info("Job with key jobKey :" + jobKey + " and group :" + groupKey
							+ " scheduled successfully for date :" + dt);
				}
				catch (ObjectAlreadyExistsException ex) {
					dt = scheduler.scheduleJob(cronTriggerBean);
					log.info("Job with key jobKey :" + jobKey + " and group :" + groupKey
							+ " scheduled successfully for date :" + dt);
				} 
				catch (SchedulerException e) {
					log.error(
							"SchedulerException while scheduling job with key :" + jobKey + " message :" + e.getMessage());
				}
				
			}

			return true;
		} catch (Exception e) {
			log.error("Error in scheduleRepeatJobWithMultipleTriggers :: Error : "+ e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public boolean checkJobExist(String jobName, String groupName) {
		log.info("Entered into checkJobExist :: jobName : "+jobName);
		try {
			Scheduler scheduler = schedulerFactoryBean.getScheduler();
			JobKey jobKey = new JobKey(jobName, groupName);
			boolean status = scheduler.checkExists(jobKey);
			return status;
		} catch (Exception e) {
			log.error("Error in checkJobExist :: Error : "+e.getLocalizedMessage());
		}
		return false;
	}
	
	@Override
	public boolean scheduleRepeatJobWithAfrerTriggers(String jobName, Class<? extends QuartzJobBean> jobClass,
			List<Date> dateList, String groupKey, String userid, String repeatedTime, int intervel, HashMap<String, Object> jobDetailMap ) {
		log.info("Entered into scheduleRepeatJobWithAfrerTriggers :: job_name : "+jobName+" :: group_key : "+ userid);
		try {
			String jobKey = jobName;
			String triggerKey = jobName;

			JobDetail jobDetail = JobUtil.createJob(jobClass, true, context, jobKey, groupKey, userid);
			
			if( jobDetail == null )
				log.info("Job creation failed");
			
			log.info("Job created successfully");
			
			if( jobDetailMap != null )
			jobDetail.getJobDataMap().putAll(jobDetailMap);
			
			Scheduler scheduler = schedulerFactoryBean.getScheduler();

			boolean isJobExist = scheduler.checkExists(jobDetail.getKey());
			log.info("Is job exist : "+ isJobExist);
			
			if( isJobExist ) {
				boolean status = scheduler.deleteJob( jobDetail.getKey() );
				log.info("delete job : "+jobDetail.getKey() +" :: status : "+ status);
			}
			
			scheduler.addJob(jobDetail, true);
			log.info("Check job exist or not :: job : "+ jobDetail.getKey());
			
			int count = 1;
			for( Date date : dateList ) {
				log.info("creating trigger for key :" + jobKey + " at date :" + date);

				Trigger cronTriggerBean = JobUtil.createCronTrigger(triggerKey+"_"+count++, date, userid,  "day", 1);
				Date dt = null;
				try {
					
					dt = scheduler.scheduleJob(jobDetail, cronTriggerBean);	
				
					log.info("Job with key jobKey :" + jobKey + " and group :" + groupKey
							+ " scheduled successfully for date :" + dt);
				}
				catch (ObjectAlreadyExistsException ex) {
					dt = scheduler.scheduleJob(cronTriggerBean);
					log.info("Job with key jobKey :" + jobKey + " and group :" + groupKey
							+ " scheduled successfully for date :" + dt);
				} 
				catch (SchedulerException e) {
					log.error(
							"SchedulerException while scheduling job with key :" + jobKey + " message :" + e.getMessage());
				}
				
			}

			return true;
		} catch (Exception e) {
			log.error("Error in scheduleRepeatJobWithAfrerTriggers :: Error : "+ e.getLocalizedMessage());
			return false;
		}
	}
}
