package com.nimble.irisservices.quartz;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.JobListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.nimble.irisservices.service.IReminderService;

@Component
public class JobsListener implements JobListener {

	private static final Logger log = LogManager.getLogger(JobsListener.class);
	
	@Autowired
	IReminderService remainderservice;

	@Override
	public String getName() {
		return "globalJob";
	}

	@Override
	public void jobToBeExecuted(JobExecutionContext context) {
		log.info("jobToBeExecuted : " + context.getJobDetail().getJobDataMap().get("userid") );
	}

	@Override
	public void jobExecutionVetoed(JobExecutionContext context) {
		log.info("JobsListener.jobExecutionVetoed()");
//		JobKey key = context.getJobDetail().getKey();
//		boolean check = remainderservice.disableReminderDetails(key.getGroup(), key.getName(), "pending", 0);

	}

	@Override
	public void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException) {
		log.info("JobsListener.jobWasExecuted()");
		JobKey key = context.getJobDetail().getKey();
		if( !key.getGroup().equalsIgnoreCase("PET_FEED_REMAINDER") )
			remainderservice.disableReminderDetails(key.getGroup(), key.getName(), "completed", 0);
	}

}
