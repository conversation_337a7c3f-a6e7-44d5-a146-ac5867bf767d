package com.nimble.irisservices.quartz;

import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.CalendarIntervalScheduleBuilder;
import org.quartz.CalendarIntervalTrigger;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.quartz.JobDetailFactoryBean;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.scheduling.quartz.SimpleTriggerFactoryBean;

class JobUtil {

	private static final Logger log = LogManager.getLogger(JobUtil.class);
	
	/**
	 * Create Quartz Job.
	 * 
	 * @param jobClass  Class whose executeInternal() method needs to be called.
	 * @param isDurable Job needs to be persisted even after completion. if true,
	 *                  job will be persisted, not otherwise.
	 * @param context   Spring application context.
	 * @param jobName   Job name.
	 * @param jobGroup  Job group.
	 * @param userid
	 * 
	 * @return JobDetail object
	 */
	protected static JobDetail createJob(Class<? extends QuartzJobBean> jobClass, boolean isDurable,
			ApplicationContext context, String jobName, String jobGroup, String userid) {
		log.info("Entered into createJob");
		
		JobDetailFactoryBean factoryBean = new JobDetailFactoryBean();
		factoryBean.setJobClass(jobClass);
		factoryBean.setDurability(isDurable);
		factoryBean.setApplicationContext(context);
		factoryBean.setName(jobName);
		factoryBean.setGroup(jobGroup);

		// set job data map
		JobDataMap jobDataMap = new JobDataMap();
		jobDataMap.put("myKey", "myValue");
		jobDataMap.put("userid", userid);
		jobDataMap.put("jobname", jobName);
		factoryBean.setJobDataMap(jobDataMap);

		factoryBean.afterPropertiesSet();

		return factoryBean.getObject();
	}

	protected static JobDetail createSIMRetryJob(Class<? extends QuartzJobBean> jobClass, boolean isDurable,
			ApplicationContext context, String jobName, String jobGroup, String imei, String reqId, String skuNumber) {
		JobDetailFactoryBean factoryBean = new JobDetailFactoryBean();
		factoryBean.setJobClass(jobClass);
		factoryBean.setDurability(isDurable);
		factoryBean.setApplicationContext(context);
		factoryBean.setName(jobName);
		factoryBean.setGroup(jobGroup);

		// set job data map
		JobDataMap jobDataMap = new JobDataMap();
		jobDataMap.put("Meid", imei);
		jobDataMap.put("IccId", jobName);
		jobDataMap.put("ReqId", reqId);
		jobDataMap.put("SkuNumber", skuNumber);
		
		factoryBean.setJobDataMap(jobDataMap);

		factoryBean.afterPropertiesSet();

		return factoryBean.getObject();
	}

	
	/**
	 * Create cron trigger.
	 * 
	 * @param triggerName        Trigger name.
	 * @param startTime          Trigger start time.
	 * @param userid
	 * @param repeattime
	 * @param cronExpression     Cron expression.
	 * @param misFireInstruction Misfire instruction (what to do in case of misfire
	 *                           happens).
	 * 
	 * @return Trigger
	 */
	protected static Trigger createCronTrigger(String triggerName, Date startTime,  String userid,
			String repeattime, int intervel) {

		CalendarIntervalScheduleBuilder cal = CalendarIntervalScheduleBuilder.calendarIntervalSchedule();

		if (repeattime.equalsIgnoreCase("hour"))
			cal = cal.withIntervalInHours(1);
		else if (repeattime.equalsIgnoreCase("day"))
			cal = cal.withIntervalInDays(intervel);
		else if (repeattime.equalsIgnoreCase("week"))
			cal = cal.withIntervalInWeeks(intervel);
		else if (repeattime.equalsIgnoreCase("month"))
			cal = cal.withIntervalInMonths(intervel);
		else if (repeattime.equalsIgnoreCase("year"))
			cal = cal.withIntervalInYears(intervel);
		else if (repeattime.equalsIgnoreCase("NA"))
			System.out.println("nothing to repeat");

		CalendarIntervalTrigger trigger = TriggerBuilder.newTrigger().withIdentity(triggerName, userid)
				.startAt(startTime).withSchedule(cal.withMisfireHandlingInstructionIgnoreMisfires()).build();

		return trigger;

	}

	/**
	 * Create a Single trigger.
	 * 
	 * @param triggerName        Trigger name.
	 * @param startTime          Trigger start time.
	 * @param groupKey
	 * @param misFireInstruction Misfire instruction (what to do in case of misfire
	 *                           happens).
	 * 
	 * @return Trigger
	 */
	protected static Trigger createSingleTrigger(String triggerName, Date startTime, String groupKey,
			int misFireInstruction) {
		SimpleTriggerFactoryBean factoryBean = new SimpleTriggerFactoryBean();
		factoryBean.setName(triggerName);
		factoryBean.setStartTime(startTime);
		factoryBean.setMisfireInstruction(misFireInstruction);
		factoryBean.setRepeatCount(0);
		factoryBean.setGroup(groupKey);
		JobDataMap jbDataMap = new JobDataMap();
		jbDataMap.put("userid", groupKey);
		jbDataMap.put("repeated", 0);
		factoryBean.setJobDataMap(jbDataMap);

		factoryBean.afterPropertiesSet();
		return factoryBean.getObject();
	}

	public static Trigger createSIMRetryTrigger(String triggerName, Date startTime, String groupKey,
			int misFireInstruction, int repeatCount, int scheduleInterval) {
		SimpleTriggerFactoryBean factoryBean = new SimpleTriggerFactoryBean();
		factoryBean.setName(triggerName);
		factoryBean.setStartTime(startTime);
		factoryBean.setMisfireInstruction(misFireInstruction);
		factoryBean.setRepeatCount(repeatCount);
		factoryBean.setRepeatInterval(scheduleInterval*60*1000L);
		factoryBean.setGroup(groupKey);
		JobDataMap jbDataMap = new JobDataMap();
		jbDataMap.put("userid", groupKey);
		jbDataMap.put("repeated", 0);
		factoryBean.setJobDataMap(jbDataMap);

		factoryBean.afterPropertiesSet();
		return factoryBean.getObject();
	}

}
