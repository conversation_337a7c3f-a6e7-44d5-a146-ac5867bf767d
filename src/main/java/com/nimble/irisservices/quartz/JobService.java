package com.nimble.irisservices.quartz;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.scheduling.quartz.QuartzJobBean;

public interface JobService {
	boolean scheduleOneTimeJob(String jobName, Class<? extends QuartzJobBean> jobClass, Date date, String userId);

	boolean scheduleRepeatJob(String jobName, Class<? extends QuartzJobBean> jobClass, Date date, String userid, String repeatedTime, int intervel);
	
	boolean scheduleRepeatJobWithMultipleTriggers(String jobName, Class<? extends QuartzJobBean> jobClass, List<Date> dateList, String groupKey, String userid, String repeatedTime, int intervel, HashMap<String, Object> jobDetailMap );

	boolean updateOneTimeJob(String jobName, Date date, String userId);

	boolean updateRepeatJob(String jobName, Date date, String userId, String repeatedType, int intervel);

	boolean unScheduleJob(String jobName);

	boolean deleteJob(String jobName, String userId);

	boolean pauseJob(String jobName);

	boolean resumeJob(String jobName);

	boolean startJobNow(String jobName);

	boolean isJobRunning(String jobName, String userId);

	List<Map<String, Object>> getAllJobs(String userId, String remainder);

	boolean isJobWithNamePresent(String jobName, String userId);

	String getJobState(String jobName, String userId);

	boolean stopJob(String jobName);
	
	boolean ScheduleRetry(String iccid, String imei, String reqId, String skuNumber, String action, int repeatCount, int scheduleInterval);

	boolean checkJobExist(String jobName, String groupName);

	boolean scheduleRepeatJobWithAfrerTriggers(String jobName, Class<? extends QuartzJobBean> jobClass, List<Date> dateList, String groupKey, String userid, String repeatedTime, int intervel, HashMap<String, Object> jobDetailMap );
}
