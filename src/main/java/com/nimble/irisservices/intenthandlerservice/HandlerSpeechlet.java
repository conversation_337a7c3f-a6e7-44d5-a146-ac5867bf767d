package com.nimble.irisservices.intenthandlerservice;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.amazon.speech.json.SpeechletRequestEnvelope;
import com.amazon.speech.slu.Intent;
import com.amazon.speech.speechlet.IntentRequest;
import com.amazon.speech.speechlet.LaunchRequest;
import com.amazon.speech.speechlet.Session;
import com.amazon.speech.speechlet.SessionEndedRequest;
import com.amazon.speech.speechlet.SessionStartedRequest;
import com.amazon.speech.speechlet.SpeechletResponse;
import com.amazon.speech.speechlet.SpeechletV2;
import com.amazon.speech.ui.Card;
import com.amazon.speech.ui.LinkAccountCard;
import com.amazon.speech.ui.PlainTextOutputSpeech;
import com.chargebee.org.json.JSONObject;
import com.nimble.irisservices.Util.AlexaUtils;
import com.nimble.irisservices.helper.AES;
import com.nimble.irisservices.service.IAsyncService;

@Service
public class HandlerSpeechlet implements SpeechletV2{

	private static final org.apache.logging.log4j.Logger log = LogManager.getLogger(HandlerSpeechlet.class);
	
	@Autowired
	private BeanFactory beanFactory;
	
	@Autowired
	@Lazy
	IAsyncService async;

	@Override
	public void onSessionStarted(SpeechletRequestEnvelope<SessionStartedRequest> requestEnvelope) {
		log.info("Entered into onSessionStarted");		
	}

	@Override
	public SpeechletResponse onLaunch(SpeechletRequestEnvelope<LaunchRequest> requestEnvelope) {		
		log.info("Entered into onLaunch");
		Session session = requestEnvelope.getSession();

		String userid = getuserIdBySession(session);
		
		if (userid == null) {
			log.info(" userid is null ");
			String speechText = "You must have a waggle account and a waggle pet monitor to know the environment temperature. "
					+ "Please use the Alexa app to link your Amazon account with your waggle Account.";

			PlainTextOutputSpeech speech = new PlainTextOutputSpeech();
			speech.setText(speechText);
			log.info("speechText :  " + speechText);

			SpeechletResponse response = SpeechletResponse.newTellResponse(speech, new LinkAccountCard());
			return response;
		}

		AlexaUtils.setConversationMode(session, true);

		// Create the initial greeting speech.
		String speechText = "Howdy! Welcome to " + AlexaUtils.Skilname + " . " + AlexaUtils.onLaunchText;

		Card card = AlexaUtils.newCard("Welcome!", speechText);
		PlainTextOutputSpeech speech = AlexaUtils.newSpeech(speechText, false);
		log.info("speechText :  " + speechText);

		return AlexaUtils.newSpeechletResponse(card, speech, session, false);
	}

	@Override
	public SpeechletResponse onIntent(SpeechletRequestEnvelope<IntentRequest> requestEnvelope) {
		log.info("Entered into onIntent");
		IntentRequest request = requestEnvelope.getRequest();
		Session session = requestEnvelope.getSession();
		String userid = getuserIdBySession(session);

		if (userid == null) {
			log.info(" userid is null ");
			String speechText = "You must have a waggle account and a waggle pet monitor to know the environment temperature. "
					+ "Please use the Alexa app to link your Amazon account with your waggle Account.";
			
			log.info("speechText :  " + speechText);

			PlainTextOutputSpeech speech = new PlainTextOutputSpeech();
			speech.setText(speechText);

			SpeechletResponse response = SpeechletResponse.newTellResponse(speech, new LinkAccountCard());
			return response;
		}
		// Get the intent
				Intent intent = request.getIntent();
				if (intent != null) {
					// Derive the handler's bean name
					String intentName = intent.getName();
					log.info("intentName : "+intentName);
					String handlerBeanName = intentName + "Handler";

					// If this is an Amazon Intent, change the handler name to better
					// match up to a Spring bean name. For example, the intent AMAZON.HelpIntent
					// should
					// be changed to AmazonHelpIntent.
					handlerBeanName = StringUtils.replace(handlerBeanName, "AMAZON.", "Amazon");
					handlerBeanName = handlerBeanName.substring(0, 1).toLowerCase() + handlerBeanName.substring(1);

					// Handle the intent by delegating to the designated handler.
					try {
						Object handlerBean = beanFactory.getBean(handlerBeanName);

						if (handlerBean != null) {

							if (handlerBean instanceof IntentHandler) {
								IntentHandler intentHandler = (IntentHandler) handlerBean;
								return intentHandler.handleIntent(intent, request, session, userid);
							}
						}
					} catch (Exception e) {
						log.error("Exception occured while getting handlerBeanName : "+e.getLocalizedMessage());
					}
				}

				// Handle unknown intents. Ask the user for more info.
				// Start a conversation (if not started already) and say that we did not
				// understand the intent
				AlexaUtils.setConversationMode(session, true);

				String errorText = "Whoops! that was unexpected. You can ask for things like temperature, humidity. What sounds good to you? ";

				Card card = AlexaUtils.newCard("Dazed and Confused", errorText);
				PlainTextOutputSpeech speech = AlexaUtils.newSpeech(errorText, false);
				return AlexaUtils.newSpeechletResponse(card, speech, session, false);

	}

	@Override
	public void onSessionEnded(SpeechletRequestEnvelope<SessionEndedRequest> requestEnvelope) {
		log.info("Entered into onSessionEnded");				
	}
	
	private String getuserIdBySession(Session session) {
		log.info("Entered into getuserIdBySession");
		String userIdMetaData = null;

		try {
			log.info("------------ Decode JWT ------------");
			String[] split_string = session.getUser().getAccessToken().split("\\.");
			String base64EncodedHeader = split_string[0];
			String base64EncodedBody = split_string[1];
			String base64EncodedSignature = split_string[2];
			log.info("~~~~~~~~~ JWT Header ~~~~~~~");
			Base64 base64Url = new Base64(true);

			String header = new String(base64Url.decode(base64EncodedHeader));
			log.info("JWT Header : " + header);
			log.info("~~~~~~~~~ JWT Body ~~~~~~~");

			String body = new String(base64Url.decode(base64EncodedBody));
			log.info("JWT Body : " + body);

			JSONObject jObj = new JSONObject(body.toString());
			userIdMetaData = AES.decrypt(jObj.getString("meta_data"));

			async.updateAlexaId(userIdMetaData, session.getUser().getUserId());
			return userIdMetaData;

		} catch (Exception e) {
			log.info("Error While Getting Token " + e.getMessage());
			return null;
		}
	}
}
