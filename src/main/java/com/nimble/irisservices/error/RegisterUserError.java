package com.nimble.irisservices.error;

public class RegisterUserError {

	public static final String ER001 = "Exception while reading niom URL from iris3.propertiesfile.";

	public static final String ER002 = "Exception while getting MEID details respective to qrcode from NIOM.";

	public static final String ER003 = "No product found for scanned QR Code.";
	public static final String ER004 = "Exception while getting Mapped meid response from NIOM.";
	public static final String ER005 = "Device is already mapped to some other orders . Need to check in the NIOM order map table.";

	public static final String ER006 = "Monitor already registered with your account.";

	public static final String ER007 = "Username/Email/Name/Company Name already existed.";

	public static final String ER008 = "Exception while creating the user account.";

	public static final String ER009 = "Pet name should not contains special characters other than hyphen(-) and underscore(_).";

	public static final String ER010 = "Invalid Asset id.";
	public static final String ER011 = "Invalid assetgroupid exception";
	public static final String ER012 = "Invalid subgroupid exception";
	public static final String ER013 = "Invalid groupid exception.";
	public static final String ER014 = "Invalid modelid exception.";
	public static final String ER015 = "Asset name or MEID already exits.";

	public static final String ER016 = "Gateway name cannot be empty , and unique for every group.";

	public static final String ER017 = "UnExcepted Error in Signup.";

	public static final String ER018 = "No product found for respective qrccode.";

	public static final String ER019 = "Error while getting meid for the respective qrc code.";
	public static final String ER020 = "UnExcepted Error during activation.";
	public static final String ER021 = "Error while getting niomPostOrderMappedURL.";
	public static final String ER022 = "Error while getting response from NIOM during pushing order.";
	public static final String ER023 = "Error while getting response from NIOM during pushing order.";
	public static final String ER024 = "Error while getting response from NIOM during pushing order.";
	public static final String ER025 = "Exception while getting user by user name.";
	public static final String ER026 = "Error while getting response from NIOM during activation of sim card.";
	public static final String ER027 = "Error while getting response from NIOM during activation of sim card.";
	public static final String ER028 = "Error while getting response from NIOM during activation of sim card.";
	public static final String ER029 = "Error while getting response from NIOM during activation of sim card.";
	public static final String ER030 = "User Needs to subscribe since they got device from reseller.";

	public static final String ER031 = "Error while deleting gateway from Sanjeev or QRC login user account.";

	public static final String ER032 = "Error while deleting gateway from sanjeev user account.";

	public static final String ER033 = "Monitor already registered with other user account.";
	
	public static final String ER035 = "Exception while getting order details respective to order channel and order id from NIOM.";

	public static final String ER036 = "Sorry your First/Last Name does not match with the order ID you provided.. Please contact us at #SP#  or email to #SM#.";

	public static final String ER037 = "Sorry your First/Last Name does not match with the order ID you provided.. Please contact us at #SP#  or email to #SM#.";

	public static final String ER038 = "Sorry your First/Last Name does not match with the order ID you provided.. Please contact us at #SP#  or email to #SM#.";

	public static final String ER039 = "Order ID not found. Please contact us at #SP#  or email to #SM#.";

	public static final String ER040 = "Given order id has already activated the products.";

	public static final String ER041 = "Unable to map the orders.";

	public static final String ER042 = "This is purchased from other than our external seller account. User needs to contact our support team to register the product.";

	public static final String ER043 = "Username for product registration from application account page is empty.";

	public static final String ER044 = "Gateway name already used. User needs to choose the different Pet Name.";

	public static final String ER045 = "Account not activated successfully . Check the service logs.";

	public static final String ER046 = "Unable to register the QRC user . Please check the logs for more details.";
	
	public static final String ER047 = "Scanned QR Code & Login ID you've entered do not match. Please use your product's QR Code as your Login ID.";
	
	public static final String ER048 = "Invalid QR Code scanned. Please scan the QR code found in product back panel.";

	public static final String ER049 = "Old device need to recall"; 
	// User Error Messages
	public static final String petNameUserMessage = "Please enter a new name that differs from the existing one.";

	public static final String otherUserMessage = "Please email to #SM# to register your product.";

	public static final String orderIDNotMatched = "Sorry your First/Last Name does not match with the order ID you provided.. Please email to #SM#.";

	public static final String loginPageActivationMessage = "Registration Successful. Keep your Pet Monitor charged for 24 hours.";
	
	public static final String loginPageActivationWithVPM_Message = "Congratulations! on getting a free WaggleVet session, and please check your email for login details. It may take up to 24 hours for your Pet Monitor to show the latest temperature.";

	public static final String loginPageActivationWithVPM_DisplayMessage = "<p style=\"font-size:13pt\"><b>Congratulations! on getting a free WaggleVet session</b>, and please check your email for login details. It may take up to 24 hours for your Pet Monitor to show the latest temperature.</p>";

	public static final String homePageActivationMessage = "Registration Successful. Keep your Pet Monitor charged for 24 hours.";
	
	public static final String homePageActivationWithVPM_Message = "Congratulations! on getting a free WaggleVet session, and please check your email for login details. It may take up to 24 hours for your Pet Monitor to show the latest temperature.";

	public static final String homePageActivationWithVPM_DisplayMessage = "<p style=\"font-size:13pt\"><b>Congratulations! on getting a free WaggleVet session</b>, and please check your email for login details. It may take up to 24 hours for your Pet Monitor to show the latest temperature.</p>";

	public static final String accountPageActivationMessage = "New Monitor added successfully!! Please keep your Pet Monitor on charge for at least 24 hours to get the latest temperature.";
	
	public static final String accountPageActivationWithVPM_Message = "New Monitor added successfully!! Congratulations on getting a free WaggleVet session.It may take up to 24 hours for your Pet Monitor to show the latest temperature";
	
	public static final String accountPageActivationWithVPM_DisplayMessage = "<p style=\"font-size:13pt\">New Monitor added successfully!! <b>Congratulations on getting a free WaggleVet session.</b>It may take up to 24 hours for your Pet Monitor to show the latest temperature.</p>";

	public static final String loginPageFurbitActivationMessage = "Please check your email for login details. Your Furbit monitor will start calibrating now and it may take up to an hour to show the latest activity data.";

	public static final String accountPageFurbitActivationMessage = "New Monitor added successfully!! Your Furbit monitor will start calibrating now and it may take up to an hour to show the latest activity data";

	public static final String deviceCountMessage = "You can add up to 5 monitors.  For more details, please contact our support team.";
	
	public static final String commonErrorMessage = "Unable to register the product. Please contact us at #SP#  or email to #SM#.";

	public static final String contactMsg = "<br>Please contact us at <br><font> <a href=\"tel: #SP#\" style=\"color:#007AFF;\"><b>#SP#</b></a></font> or <font><a href=\"mailto:#SM#\" style=\"color:#007AFF;\"><b>#SM#</b></a></font>";
	public static final String contactMsgNew = "Please contact us at <br><font> <a href=\"tel: #SP#\" style=\"color:#007AFF;\"><b>#SP#</b></a></font> or <font><a href=\"mailto:#SM#\" style=\"color:#007AFF;\"><b>#SM#</b></a></font>";
	// public static final String ER002="Exception while getting MEID details
	// respective to qrccode from NIOM.";

	public static final String recallQRCMsg = "Woohoo! Free Upgrade Product awaits just for You. Please contact us at #SP#  or email to #Sm#.";

	public static final String recallQRCInfoMsg = "Yippee! Upgrade Product awaits just for you. Still wanna register?";
	
	public static final String contectMsgNormalTxt = "Please contact us at #SP#  or email to #SM#";
	
	public static final String warrantyErrorMsg = "Please contact support to register your warranty";
	
	public static final String warrantySupportMsg = "Please contact our support";	

	public static final String loginWCActivationMessage = "Registration Complete! WaggleCam Pro+ added successfully!";

	public static final String loginSMActivationMessage = "Registration Complete! Smart AI Bowl added successfully!";

	public static final String loginRVActivationMessage = "Registration Complete! RV Cam AI Mini added successfully!";
	
	public static final String loginWCProActivationMessage = "Registration Complete! Waggle Cam Pro added successfully!";
	
	public static final String loginWCUltraActivationMessage = "Registration Complete! WaggleCam Ultra added successfully!";
	
	public static final String loginM10ActivationMessage = "Registration Complete! Sensor added successfully!";
	
	public static final String loginRVSActivationMessage = "Registration Complete! RV 4G Camera added successfully!";

	public static final String loginRVMiniSActivationMessage = "Registration Complete! RV 4G Mini added successfully!";

}
