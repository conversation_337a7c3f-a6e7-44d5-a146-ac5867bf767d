package com.nimble.irisservices;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;

import com.nimble.irisservices.configuration.DataSourceHolder;


public class DbConfig extends Application
{
	@Autowired
	public static DataSource niomDs;
	
	public static Connection giveConnetcion()
	{
		Connection con = null;
		
		DataSourceHolder dataSourceHolder = (DataSourceHolder) context.getBean("dataSourceHolder");
		
		Map<String, DataSource> map = dataSourceHolder.getListOfDataSources();
		
		niomDs = map.get("dataSourceMySQL");
		
		for (Map.Entry<String, DataSource> entry1 : map.entrySet()) {
			System.out.println("Key : " + entry1.getKey());
			System.out.println("Value : " + entry1.getValue());
		}

		try {
			con = (Connection) niomDs.getConnection();
		} catch (SQLException e) {
		}
		
		return con;		
	}
	
	public Connection getNiomDatasource() {
		try {
			return (Connection) niomDs.getConnection();
		} catch (SQLException e) {
		}
		return null;
	}

}









