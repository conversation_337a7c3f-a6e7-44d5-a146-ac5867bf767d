package com.nimble.irisservices.aspect;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import com.nimble.irisservices.dto.AdvanceLog;
import com.nimble.irisservices.dto.ApiContext;
import com.nimble.irisservices.helper.AdvanceLogHelper;

/**
 * AdvanceLog Aspect Event
 */
@Aspect
public class AdvanceLogAspect {

	private final AdvanceLogHelper _helper = new AdvanceLogHelper();
	private static final Logger log = LogManager.getLogger(AdvanceLogAspect.class);


	/**	
	 * Profile methods execution time
	 *
	 * @param joinPoint
	 * @return
	 * @throws Exception
	 * @throws Throwable
	 */
	@Around("execution(public * com.nimble.irisservices.controller.UserController.getUserByIdV2(..))"
			+ " || execution(public * com.nimble.irisservices.service.impl.UserServiceImpl.verifyAuthKey(..))"
			+ " || execution(public * com.nimble.irisservices.service.impl.UserServiceImpl.getUser(..))"
//			+ " || execution(public * com.nimble.irisservices.controller.UserController.getnumber(..))"

//			v3.0/getgeneraldata 
+ " || execution(public * com.nimble.irisservices.controller.GeneralController.getGeneraldata(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.GeneralConfigServiceImpl.getLatestReferEarn())"
+ " || execution(public * com.nimble.irisservices.helper.Helper.getExternalConfigValue(..))"

//			v3.0/socialsignup  
+ " || execution(public * com.nimble.irisservices.controller.UserController.socialSignUp(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.UserServiceImpl.getUserByName(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.UserServiceImpl.getUserByEmail(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.UserServiceImpl.updateLastLoginTypeAndTime(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.UserServiceImpl.loginViaGoogleOrFacebook(..))"
+ " || execution(public * com.nimble.irisservices.dao.ISignTypeDao.getSignType(..))"
+ " || execution(public * com.nimble.irisservices.dao.impl.UserDaoImpl.getUserBySignUpToken(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.UserServiceImpl.signUp(..))"

//			v3.0/updateusercompletesetup
+ " || execution(public * com.nimble.irisservices.controller.UserController.updateUserCompleteSetUp(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.ZipCodeDetailsServiceImpl.getZipCodeDetails(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.ZipCodeDetailsServiceImpl.saveZipCode(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.UserServiceImpl.updateUser(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.UserServiceImpl.createEmailVerificationToken(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.MailServiceImpl.sendVerificationMail(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.UserServiceImpl.getUserById(..))"

//			v3.0/usertoken
+ " || execution(public * com.nimble.irisservices.controller.UserController.usertoken(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.UserServiceImpl.saveOrUpdateUserToken(..))"

//			v3.0/loginV2
+ " || execution(public * com.nimble.irisservices.controller.UserController.getUserByNameV2(..))"

//			v3.0/gatewaysummaryTest
+ " || execution(public * com.nimble.irisservices.controller.ReportController.getAssetSummaryTest(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.CompanyServiceImpl.getCompanyConfig(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.ReportServiceImpl.getLastgatewayreport(..))"
+ " || execution(public * com.nimble.irisservices.dao.impl.ReportDaoImpl.getLstGatrpt(..))"
+ " || execution(public * com.nimble.irisservices.dao.impl.ReportDaoImpl.getLastGatewayReports(..))"
+ " || execution(public * com.nimble.irisservices.dao.impl.ReportDaoImpl.getLastGateway(..))"
+ " || execution(public * com.nimble.irisservices.dao.impl.ReportDaoImpl.getLastGroups(..))"
+ " || execution(public * com.nimble.irisservices.dao.impl.ReportDaoImpl.getLastAssetinformation(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.ReportServiceImpl.getAssetDescription(..))"

//			v4.0/gatewaysummaryTest
+ " || execution(public * com.nimble.irisservices.controller.ReportControllerV4.getGatewaySummaryTestV4(..))"
//+ " || execution(public * com.nimble.irisservices.service.impl.UserServiceImpl.getUserId_cmpIdByAuth(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.ReportServiceImplV4.getLastgatewayreportV4(..))"

//			v3.0/companyconfig
+ " || execution(public * com.nimble.irisservices.controller.CompanyController.getCompanyConfig(..))"
//			+" || execution(public * com.nimble.irisservices.service.ICompanyService.getCompanyConfig(..))"
+ " || execution(public * com.nimble.irisservices.entity.User.getGateways())"
+ " || execution(public * com.nimble.irisservices.helper.Helper.getURL(..))"


//			v3.0/alertcfg
+ " || execution(public * com.nimble.irisservices.controller.AlertCfgController.getAlertCfgById(..))"
+ " || execution(public * com.nimble.irisservices.service.IAlertCfgService.getAlertCfg(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.GatewayServiceImpl.getGateway(..))"

//	v4.0/alertcfg
+ " || execution(public * com.nimble.irisservices.controller.OptimizedV4Controller.getAlertCfgByIdV4(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.UserServiceImplV4.getAlertCfgV4(..))"
+ " || execution(public * com.nimble.irisservices.service.IGatewayService.getGatewayConfig(..))"

// 	v3.0/getcurrentsubscriptionplan/
+ " || execution(public * com.nimble.irisservices.controller.ChargebeeController.getCurrentSubscriptionPlan(..))"
+ " || execution(public * com.nimble.irisservices.controller.ChargebeeController.getSubscription(..))"
+ " || execution(public * com.nimble.irisservices.service.ICreditSystemService.getChargebeePlanById(..))"
+ " || execution(public *  com.nimble.irisservices.service.ICreditSystemService.getPlanAndPeriod(..))"
+ " || execution(public * com.nimble.irisservices.service.ICreditSystemService.getSubsPlanById(..))"
+ " || execution(public * com.nimble.irisservices.controller.ChargebeeController.checkDeviceConfigStatus(..))"
+ " || execution(public * com.nimble.irisservices.service.IGatewayService.getGatewayByUser(..))"
+ " || execution(public * com.nimble.irisservices.service.ICreditSystemService.getGatewaysByReportTime(..))"

//v3.0/furbitdailyreport
+ " || execution(public * com.nimble.irisservices.controller.FurBitController.getFurBitDailyReport(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.GatewayServiceImpl.getGatewayByMonitorType(..))"
+ " || execution(public * com.nimble.irisservices.service.IFurBitReportService.getFurBitDailyReport(..))"
+ " || execution(public * com.nimble.irisservices.service.IFurBitReportService.getFurBitBatteryLife(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.GatewayServiceImpl.getPetProfile(..))"

//v3.0/getPetSafetyUrls
+ " || execution(public * com.nimble.irisservices.controller.UserController.getPetSafetyUrls(..))"
+ " || execution(public *  com.nimble.irisservices.service.impl.UserServiceImpl.getRvPetSafetyBlogUrl(..))"

//v3.0/getAdvertisements
+ " || execution(public * com.nimble.irisservices.controller.AdvertisementController.getadvertisementinfo())"
+ " || execution(public * com.nimble.irisservices.service.IAdvertisementService.getAdvertismentUrl())"

//v3.0/getnearbyservices
+ " || execution(public * com.nimble.irisservices.controller.GooglePetServiceController.enabledisablealertcfg(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.AccountServiceImpl.getApi(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.AccountServiceImpl.getCmpAccount(..))"
+ " || execution(public * com.nimble.irisservices.service.IAccountService.update(..))"
+ " || execution(public * com.nimble.irisservices.service.IAccountService.saveOrUpdateCmpAccount(..))"

//v3.0/getdevicesummary/
+ " || execution(public * com.nimble.irisservices.controller.GatewayController.getAssetSummaryv3(..))"
+ " || execution(public * com.nimble.irisservices.service.IGatewayService.getProbeCategory(..))"
+ " || execution(public * com.nimble.irisservices.service.IDeviceSubscriptionService.getDeviceSubscription(..))"

//v3.0/usernotifications
+ " || execution(public * com.nimble.irisservices.controller.PushNotificatonController.getNotificaitonByUserID(..))"
+ " || execution(public * com.nimble.irisservices.service.IPushNotificationService.userNotifications(..))"
+ " || execution(public * com.nimble.irisservices.service.IPushNotificationService.getUserNotificationStatus(..))"
+ " || execution(public * com.nimble.irisservices.service.IUserService.updateUserNotification(..))"

//v3.0/generatereferrallink
+ " || execution(public * com.nimble.irisservices.controller.ReferAndEarnController.generateReferralLink(..))"
+ " || execution(public * com.nimble.irisservices.service.IReferAndEarnService.getLatestReferralCredits())"

//v3.0/trendingvideoslist
+ " || execution(public * com.nimble.irisservices.controller.TrendingVideoController.getTrendingVideosList(..))"
+ " || execution(public * com.nimble.irisservices.service.ITrendingvideoService.getTrendingvideos())"
+ " || execution(public * com.nimble.irisservices.service.ITrendingvideoService.getTrendingvideoInfo(..))"

//Logger 2 //

//v3.0/orderchannel
+ " || execution(public * com.nimble.irisservices.controller.DropdownController.getOrderChannel())"
+ " || execution(public * com.nimble.irisservices.service.impl.FetchDropdownServiceImpl.getOrderChannel())"

//			/v3.0/checkwifistatusv2
+ " || execution(public * com.nimble.irisservices.controller.WifiInfoController.checkWifiStatusV2(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.WifiInfoServiceImpl.isAlreadycontain(..))"
+ " || execution(public * com.nimble.irisservices.service.IFurBitReportService.getFurBitlastreport(..))"

//v3.0/getavailableupgradeplan/
+ " || execution(public * com.nimble.irisservices.controller.ChargebeeController.getAvailableUpgradeSubscriptionPlan(..))"
+ " || execution(public * com.nimble.irisservices.service.ICreditSystemService.getAvailUpgradePlans(..))"
+ " || execution(public * com.nimble.irisservices.service.IUserService.getRemainingDays(..))"

//			/v3.0/alertV3/
+ " || execution(public * com.nimble.irisservices.controller.AlertController.getAlertSummary(..))"
+ " || execution(public * com.nimble.irisservices.service.IAlertService.getackalerts())"

//v3.0/company/
+ " || execution(public * com.nimble.irisservices.controller.CompanyController.getCompanyById(..))"
+ " || execution(public * com.nimble.irisservices.service.ICompanyService.getCompany(..))"

//v3.0/alerttype
+ " || execution(public *  com.nimble.irisservices.controller.DropdownController.getAlertType(..))"
+ " || execution(public * com.nimble.irisservices.service.IFetchDropdownService.getAlertTypes())"

//v3.0/getPetProfile/
+ " || execution(public * com.nimble.irisservices.controller.GatewayController.getPetProfile(..))"
+ " || execution(public * com.nimble.irisservices.service.IGatewayService.getJPetprofilesByUser(..))"

//v3.0/getuserleaderboard/
+ " || execution(public * com.nimble.irisservices.controller.FurBitController.getUserLeaderboardDetails(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.FurBitReportServiceImpl.getUserLeaderBoardDetails(..))"

//			v3.0/getleaderboard/
+ " || execution(public * com.nimble.irisservices.controller.FurBitController.getLeaderboardDetails(..))"
+ " || execution(public * com.nimble.irisservices.service.IGatewayService.getGatewayByUser(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.FurBitReportServiceImpl.getLeaderBoardDetails(..))"

//v3.0/updategoalsettings/ POST
+ " || execution(public * com.nimble.irisservices.controller.GatewayController.updateGoalSetting(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.GatewayServiceImpl.updateGoalSetting(..))"

//v3.0/getspecies
+ " || execution(public * com.nimble.irisservices.controller.PetSpeciesController.getSpecies())"
+ " || execution(public * com.nimble.irisservices.service.IPetSpeciesServices.getPetSpecies())"

//v3.0/getbreeds
+ " || execution(public * com.nimble.irisservices.controller.PetSpeciesController.getBreeds(..))"
+ " || execution(public *  com.nimble.irisservices.service.IPetBreedServices.getAllPetBreeds())"
+ " || execution(public * com.nimble.irisservices.service.IPetSpeciesServices.getPetSpeciesByName(..))"
//+" || execution(public * com.nimble.irisservices.service.IPetSpeciesServices.getPetSpecies(..))"
+ " || execution(public * com.nimble.irisservices.service.IPetBreedServices.getPetBreeds(..))"

//v3.0/getzipcodedetails
+ " || execution(public * com.nimble.irisservices.controller.ZipController.GetTemperature(..))"
//			+" || execution(public * com.nimble.irisservices.service.IZipCodeDetailsService.getZipCodeDetails(..))"
//			+" || execution(public * com.nimble.irisservices.service.IZipCodeDetailsService.saveZipCode(..))"

//v3.0/userupdate/
+ " || execution(public * com.nimble.irisservices.controller.UserController.userUpdate(..))"
//			com.nimble.irisservices.service.IUserService.getUserByEmail
//			com.nimble.irisservices.service.IUserService.getUserByName
//			+" || execution(public *  com.nimble.irisservices.service.IUserService.updateUser(..))"

//v3.0/pwdupdate/
+ " || execution(public * com.nimble.irisservices.controller.UserController.passwordUpdate(..))"

//v3.0/usersignup
+ " || execution(public * com.nimble.irisservices.controller.UserController.userSignup(..))"
//com.nimble.irisservices.service.IUserService.signUp
//			+" || execution(public * com.nimble.irisservices.service.IUserService.createEmailVerificationToken(..))"
//			+" || execution(public * com.nimble.irisservices.service.IMailService.sendVerificationMail(..))"

//v3.0/forceUpdate
+ " || execution(public * com.nimble.irisservices.controller.UserController.isForceUpdate(..))"

//v3.0/getUserByUsernameV2
+ " || execution(public * com.nimble.irisservices.controller.UserController.getUserByUsernameV2(..))"

//v3.0/getsubsplanbymonitortype/
+ " || execution(public * com.nimble.irisservices.controller.ChargebeeController.getSubscriptionPlanByMonitortype(..))"
//+" || execution(public * com.nimble.irisservices.service.IUserService.getUserById(..))"
//+" || execution(public *  com.nimble.irisservices.service.ICreditSystemService.getPlanAndPeriod(..))"
+ " || execution(public * com.nimble.irisservices.service.ICreditSystemService.getAvailUpgradePlanNew(..))"

//v3.0/updatesubscriptionplan/
+ " || execution(public * com.nimble.irisservices.controller.ChargebeeController.updateSubscriptionPlan(..))"
+ " || execution(public * com.nimble.irisservices.service.ICreditSystemService.getChargebeePlanById(..))"
+ " || execution(public * com.nimble.irisservices.service.IUserService.getOrderMappingByUser(..))"
+ " || execution(public * com.nimble.irisservices.service.IUserService.getCreditAmountBySKU(..))"

//v3.0/resendverificationlink
+ " || execution(public *  com.nimble.irisservices.controller.UserController.resendVerificationLink(..))"
//			com.nimble.irisservices.service.IMailService.sendVerificationMail
+ " || execution(public * com.nimble.irisservices.service.IUserService.getUserInRole(..))"

//v3.0/userdeviceinfo/
+ " || execution(public * com.nimble.irisservices.controller.UserController.userdeviceinfo(..))"
+ " || execution(public *  com.nimble.irisservices.service.IUserService.saveOrUpdateUserDeviceInfo(..))"

//v3.0/activateuser
+ " || execution(public * com.nimble.irisservices.controller.UserController.activateUser(..))"
+ " || execution(public * com.nimble.irisservices.dao.IGatewayDao.getAssetModelByName(..))"
+ " || execution(public * com.nimble.irisservices.service.IGroupServices.getGroups(..))"
+ " || execution(public *  com.nimble.irisservices.service.IGatewayService.gatewayExitsinDB(..))"
+ " || execution(public * com.nimble.irisservices.service.IGatewayService.saveORupdateQRCGateway(..))"
+ " || execution(public * com.nimble.irisservices.service.IAlertCfgService.saveORupdateAlertCfg(..))"
+ " || execution(public * com.nimble.irisservices.service.IAsyncService.createUserInChargebee(..))"
+ " || execution(public * com.nimble.irisservices.service.IUserService.saveOrderMappingDetails(..))"
+ " || execution(public * com.nimble.irisservices.service.IAsyncService.saveorupdateofflineUserDetails(..))"
+ " || execution(public * com.nimble.irisservices.controller.UserController.updateRegisterUserEmailStatus(..))"
+ " || execution(public * com.nimble.irisservices.service.IAsyncService.updateRegisterUserEmailStatus(..))"

//			/v3.0/gettemperature/
+ "|| execution(public * com.nimble.irisservices.controller.WeatherController.GetTemperature(..))"

//v3.0/nimbleemail
+"|| execution(public * com.nimble.irisservices.controller.MessagingController.sendEmail(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.MessagingServiceImpl.sendEmail(..))"

//v3.0/configuredevice
+"|| execution(public * com.nimble.irisservices.controller.ChargebeeController.configureDevice(..))"

//v3.0/furbitlastgatewayreport
+"|| execution(public * com.nimble.irisservices.controller.FurBitController.getFurBitLastGatewayReport(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.GatewayServiceImpl.getFLastGatewayReportByUser(..))"
+"|| execution(public * com.nimble.irisservices.controller.FurBitController.convertJFurBitLastGatewayReport(..))"

//v3.0/furbitreport
+"|| execution(public * com.nimble.irisservices.controller.FurBitController.getFurBitReport(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.FurBitReportServiceImpl.getFurBitReport(..))"


// alertCfg post
+"|| execution(public * com.nimble.irisservices.controller.AlertCfgController.saveORupdateGatewayById(..))"

//CompanyConfig POST
+"|| execution(public * com.nimble.irisservices.controller.CompanyController.saveCompanyConfig(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.CompanyServiceImpl.updateCompanyCfg(..))"


//SET-3
//v4.0/company/   V4
+"|| execution(public * com.nimble.irisservices.controller.CompanyControllerV4.getCompanyById_V4(..))"

//v4.0/alertV4/
+"|| execution(public * com.nimble.irisservices.controller.AlertControllerV4.getAlertV3_V4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.UserServiceImplV4.getUnackAlerts(..))"


//v4.0/getgeneraldata
+"|| execution(public * com.nimble.irisservices.controller.GeneralControllerV4.getGeneraldataV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.GeneralConfigServiceImpl.getLatestReferEarnV4())"

///v4.0/generatereferrallink/
+"|| execution(public * com.nimble.irisservices.controller.ReferAndEarnControllerV4.generateReferralLinkV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.ReferAndEarnServiceImplV4.getEmailByAuth(..))"

///v4.0/userV2/
+"|| execution(public * com.nimble.irisservices.controller.UserControllerV4.getUserV2_V4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.UserServiceImplV4.getUsersByUserId_CmpId(..))"

//v4.0/getPetProfile/
+"|| execution(public * com.nimble.irisservices.controller.GatewayControllerV4.getPetProfileV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.GatewayServiceImplV4.getJPetprofilesByUserV4(..))"

//v4.0/trendingvideoslist/
+"|| execution(public * com.nimble.irisservices.controller.TrendingVideoControllerV4.getTrendingVideosListV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.TrendingvideoServiceImplV4.getTrendingvideoInfoV4(..))"
+"|| execution(public * com.nimble.irisservices.helper.Helper.updateYoutubeStatistics(..))"

//v4.0/orderchannel
+"|| execution(public * com.nimble.irisservices.controller.DropdownControllerV4.getOrderChannelV4())"
+"|| execution(public * com.nimble.irisservices.service.impl.FetchDropdownServiceImplV4.getOrderChannelV4())"

//v4.0/getbreeds
+"|| execution(public * com.nimble.irisservices.controller.PetSpeciesControllerV4.getBreedsV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.PetSpeciesServiceImplV4.getPetBreedsV4(..))"

//v4.0/usertoken 		
+"|| execution(public * com.nimble.irisservices.controller.UserControllerV4.usertokenV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.UserServiceImplV4.saveOrUpdateUserTokenV4(..))"

//v4.0/getUserByUsernameV2
+"|| execution(public * com.nimble.irisservices.controller.UserControllerV4.getUserByUsernameV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.OptimizedV4ServiceImpl.getUserByNameV4(..))"

//v4.0/pwdupdate
+"|| execution(public * com.nimble.irisservices.controller.UserControllerV4.passwordUpdateV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.UserServiceImplV4.updateUserV4(..))"

//v4.0/loginV4
+"|| execution(public * com.nimble.irisservices.controller.UserControllerV4.loginV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.OptimizedV4ServiceImpl.getUserByUsernameV4forlogin(..))"


//v4.0/updategoalsettings/
+"|| execution(public * com.nimble.irisservices.controller.GatewayControllerV4.updateGoalSettingsV4(..))"


//v4.0/usernotifications/
+"|| execution(public * com.nimble.irisservices.controller.PushNotificatonControllerV4.getNotificaitonByUserIDV4(..))"
+"|| execution(public *  com.nimble.irisservices.service.impl.PushNotificationServiceImplV4.userNotificationsV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.OptimizedV4ServiceImpl.getUserNotificationStatusV4(..))"

//v4.0/userupdate/
+"|| execution(public * com.nimble.irisservices.controller.UserControllerV4.userUpdateV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.UserServiceImplV4.getUserId_cmpIdByAuth(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.UserServiceImplV4.getUserByUNameOrEmailV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.UserServiceImplV4.updateUserv4byuserid(..))"

//v4.0/updatevideostatus/
+"|| execution(public * com.nimble.irisservices.controller.TrendingVideoControllerV4.updateVideoStatusV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.TrendingvideoServiceImplV4.UpdateVideoInfoTransaction(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.TrendingvideoServiceImplV4.CreateVideoInfoTransaction(..))"

//v4.0/alerttype/
+"|| execution(public * com.nimble.irisservices.controller.DropdownControllerV4.getAlertType_V4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.FetchDropdownServiceImplV4.getAlertTypesV4(..))"

//v4.0/userdeviceinfo/
+"|| execution(public * com.nimble.irisservices.controller.UserControllerV4.userdeviceinfoV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.UserServiceImplV4.saveOrUpdateUserDeviceInfoV4(..))"

//v4.0/companyconfigv2/
+"|| execution(public * com.nimble.irisservices.service.impl.CompanyServiceImplV4.updateCompanyCfg(..))"

//v5.0/companyconfig/{autho}
+"|| execution(public * com.nimble.irisservices.service.impl.OptimizedV4ServiceImpl.getCompanyConfigAndComapny(..))"

//VerifyAuth v3
+"|| execution(public *  com.nimble.irisservices.service.impl.UserServiceImplV4.verifyAuthV3(..))"

//VerifyAuth v4
+"|| execution(public * com.nimble.irisservices.service.impl.UserServiceImplV4.verifyAuthV4(..))"

//v4.0/companyconfig
+ " || execution(public * com.nimble.irisservices.controller.CompanyControllerV4.getCompanyConfigV4(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.CompanyServiceImplV4.getCompanyConfigAndCompany(..))"
//+ " || execution(public * com.nimble.irisservices.service.impl.OptimizedV4ServiceImpl.getCompanyConfigsForCmpCfgResponse(..))"
+ " || execution(public * com.nimble.irisservices.service.impl.NiomDatabaseImpl.isMeidMappedInOrdermap(..))"

//v4.0/alertcfg/ Get
+"|| execution(public * com.nimble.irisservices.controller.AlertCfgControllerV4.getAlertCfgV4(..))"

//v4.0/alertcfg/ post

+"|| execution(public * com.nimble.irisservices.controller.AlertCfgControllerV4.updateAlertcfg(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.AlertCfgServiceImplV4.enabledisablealertcfg(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.AlertCfgServiceImplV4.updateEmailPhone(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.AlertCfgServiceImplV4.updateNotify(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.AlertCfgServiceImplV4.updateAlertCfg(..))"

//v4.0/saveorupdatePetProfile/
+"|| execution(public * com.nimble.irisservices.controller.GatewayControllerV4.saveorupdatePetProfileV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.GatewayServiceImplV4.saveorupdatePetprofileV4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.GatewayServiceImpl.updateGatewayName(..))"

+"|| execution(public * com.nimble.irisservices.service.impl.UserServiceImplV4.getUserId_cmpIdByAuthV2(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.GatewayServiceImplV4.getGatewayV4(..))"

// SET 4

//v3.0/checkqrcexist/
+"|| execution(public * com.nimble.irisservices.controller.GatewayController.checkQrcExistInDb(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.RegisterUserEmailServiceImpl.externalQrcActivationStatus(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.GatewayServiceImpl.checkQrcExist(..))"

//v3.0/savewifiinfo/
+"|| execution(public * com.nimble.irisservices.controller.WifiInfoController.saveWifiInfo(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.GatewayServiceImpl.getGatewayAndUserDetails(..))"
//+"|| execution(public * com.nimble.irisservices.service.impl.WifiInfoServiceImpl.isAlreadycontain(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.WifiInfoServiceImpl.saveOrUpdateWifiInfo(..))"

//v3.0/getwifiinfolist/
+"|| execution(public * com.nimble.irisservices.controller.WifiInfoController.getWifiinfoList(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.WifiInfoServiceImpl.getWifiList(..))"

//v3.0/forgetpassword
+"|| execution(public * com.nimble.irisservices.controller.UserController.forgetPassword(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.MailServiceImpl.sendForgotPasswordMail(..))"

//v4.0/getAdvertisements
+"|| execution(public * com.nimble.irisservices.controller.AdvertisementControllerV4.getadvertisementinfoV4(..))"

//v4.0/getspecies
+"|| execution(public * com.nimble.irisservices.controller.PetSpeciesControllerV4.getSpeciesV4(..))"

+"|| execution(public * com.nimble.irisservices.controller.FurBitControllerV4.getFurBitDailyReportv4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.FurBitReportServiceImplV4.getFurBitDailyReport(..))"
+"|| execution(public * com.nimble.irisservices.controller.FurBitControllerV4.getFurBitReportv4(..))"
+"|| execution(public * com.nimble.irisservices.service.impl.FurBitReportServiceImplV4.getFurBitReport(..))"		
			
			)
	public Object logTime(ProceedingJoinPoint joinPoint) {
		ApiContext apiContext = _helper.getApiContext();

		AdvanceLog advanceLog = apiContext.getAdvanceLog();

		long startTime = System.currentTimeMillis();

		Object retVal = null;
		try {
			retVal = joinPoint.proceed();
		} catch (Throwable e) {
			log.error("Error : " + e.getLocalizedMessage());
		}
		double elapsedTime = ((System.currentTimeMillis() - startTime) / 1000.0);

		//System.out.println("JOINPOINT :: "+joinPoint.getSignature().getDeclaringTypeName() +" :: "+joinPoint.getSignature().getName());

		if (joinPoint.getSignature().getName().equalsIgnoreCase("verifyAuthKey")) {
			advanceLog.setAuthenticationValidation(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getUser")) {
			advanceLog.setGetUserElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getnumber")) {
			advanceLog.setGetNumberElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getUserByIdV2")) {
			apiContext.setApi("getUserByIdV2");
		}

		// SIV
		// Get General start
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getGeneraldata")) {
			apiContext.setApi("getGeneraldata");
			advanceLog.setGetGeneralDataElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getLatestReferEarn")) {
			apiContext.setApi("getLatestReferEarn");
			advanceLog.setGetLatestReferEarnElapTime(elapsedTime);
		}
		// Social sign up
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getUserByName")) {
			advanceLog.setGetUserByNameElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getUserByEmail")) {
			advanceLog.setGetUserByEmailElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("updateLastLoginTypeAndTime")) {
			advanceLog.setUpdateLastLoginTypeAndTimeElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("loginViaGoogleOrFacebook")) {
			advanceLog.setLoginViaGoogleOrFacebook(elapsedTime);
		}

		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getSignType")) {
			advanceLog.setGetSignTypeElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getUserBySignUpToken")) {
			advanceLog.setGetUserBySignUpTokenElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("signUp")) {
			advanceLog.setSignUpElapTime(elapsedTime);
		}

		// Complete set up
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("updateUserCompleteSetUp")) {
			advanceLog.setUpdateUserCompleteSetUpElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getZipCodeDetails")) {
			advanceLog.setGetZipCodeDetailsElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("saveZipCode")) {
			advanceLog.setSaveZipCodeElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("updateUser")) {
			advanceLog.setUpdateUserElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("createEmailVerificationToken")) {
			advanceLog.setCreateEmailVerificationTokenElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("sendVerificationMail")) {
			advanceLog.setSendVerificationMailElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getUserById")) {
			advanceLog.setGetUserByIdElapTime(elapsedTime);
		}
		// usertoken
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("usertoken")) {
			advanceLog.setUsertokenElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("saveOrUpdateUserToken")) {
			advanceLog.setSaveOrUpdateUserTokenElapTime(elapsedTime);
		}

		// loginV2
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getUserByNameV2")) {
			advanceLog.setGetUserByNameV2(elapsedTime);
		}

		// getdeviceSummaryTestV3
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAssetSummaryTest")) {
			advanceLog.setGetAssetSummaryTestElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.service.impl.CompanyServiceImpl")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getCompanyConfig")) {
			advanceLog.setGetCompanyConfigServiceElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getLastgatewayreport")) {
			advanceLog.setGetLastgatewayreportElapTime(elapsedTime);

		} 
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getLstGatrpt")) {
			advanceLog.setGetLstGatrptElapTime(elapsedTime);

		}else if (joinPoint.getSignature().getName().equalsIgnoreCase("getLastGatewayReports")) {
			advanceLog.setGetLastGatewayReportsElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getLastGateway")) {
			advanceLog.setGetLastGatewayElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getLastGroups")) {
			advanceLog.setGetLastGroupsElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getLastAssetinformation")) {
			advanceLog.setGetLastAssetinformationElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAssetDescription")) {
			advanceLog.setGetAssetDescriptionElapTime(elapsedTime);
		}

		// getdeviceSummaryTestV4
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getGatewaySummaryTestV4")) {
			advanceLog.setGetGatewaySummaryTestV4ElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.service.impl.UserServiceImplV4")
				&&  joinPoint.getSignature().getName().equalsIgnoreCase("getUserId_cmpIdByAuth")) {
			advanceLog.setGetUserId_cmpIdByAuthElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getLastgatewayreportV4")) {
			advanceLog.setGetLastgatewayreportV4ElapTime(elapsedTime);
		}
		
		// company config v3
		else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.controller.CompanyController")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getCompanyConfig")) {
			advanceLog.setGetCompanyConfigControllerElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getGateways")) {
			advanceLog.setGetGatewaysElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getURL")) {
			advanceLog.setGetURLElapTime(elapsedTime);
		}

		// company config v4
		else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.controller.CompanyControllerV4")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getCompanyConfigV4")) {
			advanceLog.setGetCompanyConfigV4ElapTime(elapsedTime);
		} 
//		else if (joinPoint.getSignature().getDeclaringTypeName()
//				.equalsIgnoreCase("com.nimble.irisservices.service.impl.OptimizedV4ServiceImpl")
//				&& joinPoint.getSignature().getName().equalsIgnoreCase("getCompanyConfigAndCompany")) {
//			advanceLog.setGetCompanyConfigAndCompanyElaptime(elapsedTime);
//		} 
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("isMeidMappedInOrdermap")) {
			advanceLog.setIsMeidMappedInOrdermapElapTime(elapsedTime);
		}

		
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("verifyAuthKeyV2")) {
			advanceLog.setVerifyAuthKeyV2ElapTime(elapsedTime);
		} 
		
		
		// Alertcfg v3
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAlertCfgById")) {
			advanceLog.setGetAlertCfgByIdElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAlertCfg")) {
			advanceLog.setGetAlertCfgElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getGateway")) {
			advanceLog.setGetGatewayElapTime(elapsedTime);
		}
		// Alertcfg v4

		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAlertCfgByIdV4")) {
			advanceLog.setGetAlertCfgByIdV4ElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.service.impl.UserServiceImplV4")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getAlertCfgV4")) {
			advanceLog.setGetAlertCfgV4ElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getGatewayConfig")) {
			advanceLog.setGetGatewayConfigElapTime(elapsedTime);
		}

		// get current subscription plan
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getCurrentSubscriptionPlan")) {
			advanceLog.setGetCurrentSubscriptionPlanElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getSubscription")) {
			advanceLog.setGetSubscriptionElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getChargebeePlanById")) {
			advanceLog.setGetChargebeePlanByIdElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getPlanAndPeriod")) {
			advanceLog.setGetPlanAndPeriodElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getSubsPlanById")) {
			advanceLog.setGetSubsPlanByIdElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("checkDeviceConfigStatus")) {
			advanceLog.setCheckDeviceConfigStatusElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getGatewayByUser")) {
			advanceLog.setGetGatewayByUserElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getGatewaysByReportTime")) {
			advanceLog.setGetGatewaysByReportTime(elapsedTime);
		}

		// getFurBitDailyReport
		else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.controller.FurBitController")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getFurBitDailyReport")) {
			advanceLog.setGetFurBitDailyReportServiceElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.service.IFurBitReportService")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getFurBitDailyReport")) {
			advanceLog.setGetFurBitDailyReportServiceElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getGatewayByMonitorType")) {
			advanceLog.setGetGatewayByMonitorTypeElapTime(elapsedTime);
		}

		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getFurBitBatteryLife")) {
			advanceLog.setGetFurBitBatteryLifeElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.service.impl.GatewayServiceImpl")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getPetProfile")) {
			advanceLog.setGetPetProfileElapTime(elapsedTime);
		}

		// getPetSafetyUrls
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getPetSafetyUrls")) {
			advanceLog.setGetPetSafetyUrlsElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getRvPetSafetyBlogUrl")) {
			advanceLog.setGetRvPetSafetyBlogUrlElapTime(elapsedTime);
		}

		// getadvertisementinfo
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getadvertisementinfo")) {
			advanceLog.setGetadvertisementinfoElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAdvertismentUrl")) {
			advanceLog.setGetAdvertismentUrlElapTime(elapsedTime);
		}

		// Google nearby : enabledisablealertcfg
		else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.controller.GooglePetServiceController")
				&&joinPoint.getSignature().getName().equalsIgnoreCase("enabledisablealertcfg")) {
			advanceLog.setEnabledisablealertcfgElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getApi")) {
			advanceLog.setGetApiElpaTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getCmpAccount")) {
			advanceLog.setGetCmpAccountElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("update")) {
			advanceLog.setUpdateElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("saveOrUpdateCmpAccount")) {
			advanceLog.setSaveOrUpdateCmpAccountElapTime(elapsedTime);
		}

		// getAssetSummaryv3 - getdevicesummany
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAssetSummaryv3")) {
			advanceLog.setGetAssetSummaryv3ElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getProbeCategory")) {
			advanceLog.setGetProbeCategoryElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getDeviceSubscription")) {
			advanceLog.setGetDeviceSubscriptionElapTime(elapsedTime);
		}

		// user notifications
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getNotificaitonByUserID")) {
			advanceLog.setGetNotificaitonByUserIDElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("userNotifications")) {
			advanceLog.setUserNotificationsElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getUserNotificationStatus")) {
			advanceLog.setGetUserNotificationStatusElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("updateUserNotification")) {
			advanceLog.setUpdateUserNotificationElapTime(elapsedTime);
		}

		// generatereferrallink
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("generateReferralLink")) {
			advanceLog.setGenerateReferralLinkElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getLatestReferralCredits")) {
			advanceLog.setGetLatestReferralCreditsElapTime(elapsedTime);
		}

		// getTrendingVideosList
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getTrendingVideosList")) {
			advanceLog.setGetTrendingVideosListElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getTrendingvideos")) {
			advanceLog.setGetTrendingvideosElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getTrendingvideoInfo")) {
			advanceLog.setGetTrendingvideoInfoElapTime(elapsedTime);
		}

		// log 2
		// getOrderChannel
		else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.controller.DropdownController")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getOrderChannelV4")) {
			advanceLog.setGetOrderChannelElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.service.impl.FetchDropdownServiceImpl")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getOrderChannel")) {
			advanceLog.setGetOrderChannelServiceElapTime(elapsedTime);
		}

		// checkWifiStatusV2
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("checkWifiStatusV2")) {
			advanceLog.setCheckWifiStatusV2ElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("isAlreadycontain")) {
			advanceLog.setIsAlreadycontainElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getFurBitlastreport")) {
			advanceLog.setGetFurBitlastreportElapTime(elapsedTime);
		}

		// getAvailableUpgradeSubscriptionPlan
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAvailableUpgradeSubscriptionPlan")) {
			advanceLog.setGetAvailableUpgradeSubscriptionPlanElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAvailUpgradePlans")) {
			advanceLog.setGetAvailUpgradePlansElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getRemainingDays")) {
			advanceLog.setGetRemainingDaysElapTime(elapsedTime);
		}

		// alert v3
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAlertSummary")) {
			advanceLog.setGetAlertSummaryElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getackalerts")) {
			advanceLog.setGetackalertsElapTime(elapsedTime);
		}

		// get company - by id

		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getCompanyById")) {
			advanceLog.setGetCompanyByIdElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getCompany")) {
			advanceLog.setGetCompanyElapTime(elapsedTime);
		}

		// getAlertTypes
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAlertType")) {
			advanceLog.setGetAlertTypeElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAlertTypes")) {
			advanceLog.setGetAlertTypesElapTime(elapsedTime);
		}

		// getPetprofile
		else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.controller.GatewayController")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getPetProfile")) {
			advanceLog.setGetPetProfile_ElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getJPetprofilesByUser")) {
			advanceLog.setGetJPetprofilesByUserElapTime(elapsedTime);
		}

		// getUserLeaderboardDetails
		else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.controller.FurBitController")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getUserLeaderboardDetails")) {
			advanceLog.setGetUserLeaderboardDetails_c_ElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.service.impl.FurBitReportServiceImpl")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getUserLeaderboardDetails")) {
			advanceLog.setGetUserLeaderboardDetails_s_ElapTime(elapsedTime);
		}

		// getLeaderboardDetails
		else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.controller.FurBitController")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getLeaderboardDetails")) {
			advanceLog.setGetLeaderboardDetails_C_ElepTime(elapsedTime);
		} else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.service.impl.FurBitReportServiceImpl")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getLeaderboardDetails")) {
			advanceLog.setGetLeaderBoardDetails_S_ElepTime(elapsedTime);
		}

		// updateGoalSetting
		else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.controller.GatewayController")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("updateGoalSetting")) {
			advanceLog.setUpdateGoalSetting_C_ElepTime(elapsedTime);
		} else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.service.impl.GatewayServiceImpl")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("updateGoalSetting")) {
			advanceLog.setUpdateGoalSetting_S_ElepTime(elapsedTime);
		}

		// getSpecies
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getSpecies")) {
			advanceLog.setGetSpeciesElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getPetSpecies")) {
			advanceLog.setGetPetSpeciesElapTime(elapsedTime);
		}

		// getBreeds
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getBreeds")) {
			advanceLog.setGetBreedsElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAllPetBreeds")) {
			advanceLog.setGetAllPetBreedsElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getPetSpeciesByName")) {
			advanceLog.setGetPetSpeciesByNameElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getPetBreeds")) {
			advanceLog.setGetPetBreedsElapTime(elapsedTime);
		}

		// getZipCodeDetails
		else if (joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.controller.ZipController") && joinPoint.getSignature().getName().equalsIgnoreCase("GetTemperature")) {
			advanceLog.setGetZipcodeElapTime(elapsedTime);
		}

		// userUpdate
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("userUpdate")) {
			advanceLog.setUserUpdateElapTime(elapsedTime);
		}

		// passwordUpdate
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("passwordUpdate")) {
			advanceLog.setPasswordUpdateElapTime(elapsedTime);
		}

		// userSignup
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("userSignup")) {
			advanceLog.setUserSignupElapTime(elapsedTime);
		}

		// isForceUpdate

		else if (joinPoint.getSignature().getName().equalsIgnoreCase("isForceUpdate")) {
			advanceLog.setIsForceUpdateElapTime(elapsedTime);
		}

		// getUserByUsernameV2

		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getUserByUsernameV2")) {
			advanceLog.setGetUserByNameV2(elapsedTime);
		}

		// getSubscriptionPlanByMonitortype
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("getSubscriptionPlanByMonitortype")) {
			advanceLog.setGetSubscriptionPlanByMonitortypeElapTime(elapsedTime);

		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAvailUpgradePlanNew")) {
			advanceLog.setGetAvailUpgradePlanNewElapTime(elapsedTime);
		}

		// updateSubscriptionPlan
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("updateSubscriptionPlan")) {
			advanceLog.setUpdateSubscriptionPlanElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getOrderMappingByUser")) {
			advanceLog.setGetOrderMappingByUserElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getCreditAmountBySKU")) {
			advanceLog.setGetCreditAmountBySKUElapTime(elapsedTime);
		}

		// resendVerificationLink
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("resendVerificationLink")) {
			advanceLog.setResendVerificationLinkElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getUserInRole")) {
			advanceLog.setGetUserInRoleElapTime(elapsedTime);
		}

		// userdeviceinfo
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("userdeviceinfo")) {
			advanceLog.setUserdeviceinfoElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("saveOrUpdateUserDeviceInfo")) {
			advanceLog.setSaveOrUpdateUserDeviceInfoElapTime(elapsedTime);
		}

		// activateUser
		else if (joinPoint.getSignature().getName().equalsIgnoreCase("activateUser")) {
			advanceLog.setActivateUserElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getAssetModelByName")) {
			advanceLog.setGetAssetModelByNameElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("getGroups")) {
			advanceLog.setGetGroupsElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("gatewayExitsinDB")) {
			advanceLog.setGatewayExitsinDBElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("saveORupdateQRCGateway")) {
			advanceLog.setSaveORupdateQRCGatewayElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("saveORupdateAlertCfg")) {
			advanceLog.setSaveORupdateAlertCfgElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("createUserInChargebee")) {
			advanceLog.setCreateUserInChargebeeElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("saveOrderMappingDetails")) {
			advanceLog.setSaveOrderMappingDetailsElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("saveorupdateofflineUserDetails")) {
			advanceLog.setSaveorupdateofflineUserDetailsElapTime(elapsedTime);
		} else if (joinPoint.getSignature().getName().equalsIgnoreCase("updateRegisterUserEmailStatus")) {
			advanceLog.setUpdateRegisterUserEmailStatusElapTime(elapsedTime);
		}

		///v3.0/gettemperature/
		else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.controller.WeatherController")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("GetTemperature")) {
			advanceLog.setGetTempoeratureElapTime(elapsedTime);
		}

		//v3.0/nimbleemail
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.controller.MessagingController") && joinPoint.getSignature().getName().equalsIgnoreCase("sendEmail")) {
			advanceLog.setSendEmailControllerElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.MessagingServiceImpl") && joinPoint.getSignature().getName().equalsIgnoreCase("sendEmail")) {
			advanceLog.setSendEmailServiceElapTime(elapsedTime);
		}

		//configureDevice
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("configureDevice")) {
			advanceLog.setConfigureDeviceElapTime(elapsedTime);
		}

		//         getFurBitLastGatewayReport
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getFurBitLastGatewayReport")) {
			advanceLog.setGetFurBitLastGatewayReportElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getFLastGatewayReportByUser")) {
			advanceLog.setGetFLastGatewayReportByUserElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("convertJFurBitLastGatewayReport")) {
			advanceLog.setConvertJFurBitLastGatewayReportElapTime(elapsedTime);
		}

		//		furbitreport
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.controller.FurBitController")  && joinPoint.getSignature().getName().equalsIgnoreCase("getFurBitReport")) {
			advanceLog.setGetFurBitReportControllerElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.FurBitReportServiceImpl")  && joinPoint.getSignature().getName().equalsIgnoreCase("getFurBitReport")) {
			advanceLog.setGetFurBitReportServiceElapTime(elapsedTime);
		}

		// alertCgf - POST
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("saveORupdateGatewayById")) {
			advanceLog.setSaveORupdateGatewayByIdElapTime(elapsedTime);
		}

		//CompanyConfig POST
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("saveCompanyConfig")) {
			advanceLog.setSaveCompanyConfigElapTime(elapsedTime);
		}
		else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.service.impl.CompanyServiceImpl") && joinPoint.getSignature().getName().equalsIgnoreCase("updateCompanyCfg")) {
			advanceLog.setUpdateCompanyCfgElapTime(elapsedTime);
		}

		//		SET - 3
		//getCompanyById_V4
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getCompanyById_V4")) {
			advanceLog.setGetCompanyById_V4_C_ElapTime(elapsedTime);
		}

		//alertV4
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getAlertV3_V4")) {
			advanceLog.setGetAlertV3_V4Elaptimes(elapsedTime);
		}else if(joinPoint.getSignature().getName().equalsIgnoreCase("getUnackAlerts")) {
			advanceLog.setGetUnackAlertsV4ElapTime(elapsedTime);
		}

		//v4.0/getgeneraldata V4
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getGeneraldataV4")) {
			advanceLog.setGetGeneraldataV4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getLatestReferEarnV4")) {
			advanceLog.setGetLatestReferEarnV4ElapTime(elapsedTime);
		}		


		///v4.0/generatereferrallink/
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("generateReferralLinkV4")) {
			advanceLog.setGenerateReferralLinkV4ElapTime(elapsedTime);
		}

		//v4.0/userV2
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getUserV2_V4")) {
			advanceLog.setGetUserV2_V4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getUsersByUserId_CmpId")) {
			advanceLog.setGetUserById_CmpIdElapTime(elapsedTime);
		}

		//v4.0/getPetProfile/
		else if ( joinPoint.getSignature().getName().equalsIgnoreCase("getPetProfileV4")) {
			advanceLog.setGetPetProfileV4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getEmailByAuth")) {
			advanceLog.setGetEmailByAuthElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getJPetprofilesByUserv4")) {
			advanceLog.setGetJPetprofilesByUserV4ElapTime(elapsedTime);
		}

		//v4.0/trendingvideoslist/
		else if ( joinPoint.getSignature().getName().equalsIgnoreCase("getTrendingVideosListV4")) {
			advanceLog.setGetTrendingVideosListV4ElapTime(elapsedTime);
		} else if ( joinPoint.getSignature().getName().equalsIgnoreCase("getTrendingvideoInfoV4")) {
			advanceLog.setGetTrendingvideoInfoserviceV4ElapTime(elapsedTime);
		}
		else if ( joinPoint.getSignature().getName().equalsIgnoreCase("updateYoutubeStatistics")) {
			advanceLog.setUpdateYoutubeStatisticsElapTime(elapsedTime);
		}

		//v4.0/orderchannel
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.controller.DropdownControllerV4") 
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getOrderChannelV4")) {
			advanceLog.setGetOrderChannelV4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.FetchDropdownServiceImplV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getOrderChannelV4")) {
			advanceLog.setGetOrderChannelV4ServiceElapTime(elapsedTime);
		}

		//v4.0/getbreeds
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.controller.PetSpeciesControllerV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getBreedsV4")) {
			advanceLog.setGetBreedsV4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.PetSpeciesServiceImplV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getPetBreedsV4")) {
			advanceLog.setGetBreedsV4ServiceElapTime(elapsedTime);
		}

		//v4.0/usertoken 		
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("usertokenV4")) {
			advanceLog.setUsertokenV4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("saveOrUpdateUserTokenV4")) {
			advanceLog.setSaveOrUpdateUserTokenV4ElapTime(elapsedTime);
		}

		//v4.0/getUserByUsernameV2
		else if( joinPoint.getSignature().getName().equalsIgnoreCase("getUserByUsernameV4")) {
			advanceLog.setGetUserByUsernameV4ElapTime(elapsedTime);
		}
		else if( joinPoint.getSignature().getName().equalsIgnoreCase("getUserByNameV4")) {
			advanceLog.setGetUserByNameV4ElapTime(elapsedTime);
		}

		//v4.0/pwdupdate
		else if( joinPoint.getSignature().getName().equalsIgnoreCase("passwordUpdateV4")) {
			advanceLog.setPasswordUpdateV4ElapTime(elapsedTime);
		}
		else if( joinPoint.getSignature().getName().equalsIgnoreCase("updateUserV4")) {
			advanceLog.setUpdateUserV4ElapTime(elapsedTime);
		}

		//v4.0/loginV4
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("loginV4")) {
			advanceLog.setLoginV4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getUserByUsernameV4forlogin")) {
			advanceLog.setGetUserByUsernameV4forloginElapTime(elapsedTime);
		}

		//v4.0/updategoalsettings/
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("updateGoalSettingsV4")) {
			advanceLog.setUpdateGoalSettingsV4ElapTime(elapsedTime);
		}

		//v4.0/usernotifications/
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getNotificaitonByUserIDV4")) {
			advanceLog.setGetNotificaitonByUserIDV4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getUserNotificationStatusV4")) {
			advanceLog.setGetUserNotificationStatusV4ElapTime(elapsedTime);
		}else if (joinPoint.getSignature().getName().equalsIgnoreCase("userNotificationsV4")) {
			advanceLog.setUserNotificationsV4ElapTime(elapsedTime);
		}
		//v4.0/userupdate/
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("userUpdateV4")) {
			advanceLog.setUserUpdateV4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.UserServiceImplV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getUserId_cmpIdByAuth")) {
			advanceLog.setGetUserId_cmpIdByAuthV4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getUserByUNameOrEmailV4")) {
			advanceLog.setGetUserByUNameOrEmailV4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("updateUserv4byuserid")) {
			advanceLog.setUpdateUserV4byuseridbyuseridV4forloginElapTime(elapsedTime);
		}

		//v4.0/updatevideostatus/
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("updateVideoStatusV4")) {
			advanceLog.setUpdateVideoStatusV4V4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("UpdateVideoInfoTransaction")) {
			advanceLog.setUpdateVideoInfoTransactionV4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("CreateVideoInfoTransaction")) {
			advanceLog.setCreateVideoInfoTransactionV4ElapTime(elapsedTime);
		}
		
		//v4.0/alerttype/
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getAlertType_V4")) {
			advanceLog.setGetAlertType_V4(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("getAlertTypesV4")) {
			advanceLog.setGetAlertTypesV4(elapsedTime);
		}
		

		//v4.0/userdeviceinfo/
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("userdeviceinfoV4")) {
			advanceLog.setUserdeviceinfoV4ElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("saveOrUpdateUserDeviceInfoV4")) {
			advanceLog.setSaveOrUpdateUserDeviceInfoV4ElapTime(elapsedTime);
		}
		

		//v4.0/companyconfigv2/
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.CompanyServiceImplV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("updateCompanyCfg")) {
			advanceLog.setUpdateCompanyCfgV4ElapTime(elapsedTime);
		}
		
		//v4.0/companyconfig/{autho}
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.CompanyServiceImplV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getCompanyConfigAndCompany")) {
			advanceLog.setGetCompanyConfigV2V4ElapTime(elapsedTime);
		}
		
		//VerifyAuth v3
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("verifyAuthV3")) {
			advanceLog.setVerifyAuthV3(elapsedTime);
		}
		
		//VerifyAuth v4
		else if(joinPoint.getSignature().getName().equalsIgnoreCase("verifyAuthV4")) {
			advanceLog.setVerifyAuthV4(elapsedTime);
		}
		
		//v4.0/alertcfg/  get
		else if (joinPoint.getSignature().getDeclaringTypeName()
				.equalsIgnoreCase("com.nimble.irisservices.controller.AlertCfgControllerV4")
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getAlertCfgV4")) {
			advanceLog.setGetAlertCfgV4ControllerV4ElapTime(elapsedTime);
		}
		
		
		//v4.0/alertcfg/  post
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.controller.AlertCfgControllerV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("updateAlertcfg")) {
			advanceLog.setUpdateAlertcfgControllerV4ElapTime(elapsedTime);
		}else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.AlertCfgServiceImplV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("enabledisablealertcfg")) {
			advanceLog.setEnabledisablealertcfgV4ElapTime(elapsedTime);
		}else if( joinPoint.getSignature().getName().equalsIgnoreCase("updateEmailPhone")) {
			advanceLog.setUpdateEmailPhoneV4ElapTime(elapsedTime);
		}else if( joinPoint.getSignature().getName().equalsIgnoreCase("updateNotify")) {
			advanceLog.setUpdateNotifyV4ElapTime(elapsedTime);
		}else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.AlertCfgServiceImplV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("updateAlertCfg")) {
			advanceLog.setUpdateAlertCfgServiceV4ElapTime(elapsedTime);
		}
		
		//v4.0/saveorupdatePetProfile/
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.controller.GatewayControllerV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("saveorupdatePetProfileV4")) {
			advanceLog.setSaveorupdatePetProfileV4ControllerElapTime(elapsedTime);
		}
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.GatewayServiceImplV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("saveorupdatePetprofileV4")) {
			advanceLog.setSaveorupdatePetProfileV4ServiceElapTime(elapsedTime);
		}		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.GatewayServiceImpl")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("updateGatewayName")) {
			advanceLog.setUpdateGatewayNameElapTime(elapsedTime);
		}
		
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.UserServiceImplV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getUserId_cmpIdByAuthV2")) {
			advanceLog.setGetUserId_cmpIdByAuthV2(elapsedTime);
		}
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.GatewayServiceImplV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getGatewayV4")) {
			advanceLog.setGetGatewayV4ElapTime(elapsedTime);
		}
		
		//v3.0/checkqrcexist/
		else if( joinPoint.getSignature().getName().equalsIgnoreCase("checkQrcExistInDb")) {
			advanceLog.setCheckQrcExistInDbElapTime(elapsedTime);
		}else if( joinPoint.getSignature().getName().equalsIgnoreCase("externalQrcActivationStatus")) {
			advanceLog.setExternalQrcActivationStatusElapTime(elapsedTime);
		}else if( joinPoint.getSignature().getName().equalsIgnoreCase("checkQrcExist")) {
			advanceLog.setCheckQrcExistElapTime(elapsedTime);
		}

		//v3.0/savewifiinfo/
		else if( joinPoint.getSignature().getName().equalsIgnoreCase("saveWifiInfo")) {
			advanceLog.setSaveWifiInfoElapTime(elapsedTime);
		}else if( joinPoint.getSignature().getName().equalsIgnoreCase("getGatewayAndUserDetails")) {
			advanceLog.setGetGatewayAndUserDetailsElapTime(elapsedTime);
		}else if( joinPoint.getSignature().getName().equalsIgnoreCase("saveOrUpdateWifiInfo")) {
			advanceLog.setSaveOrUpdateWifiInfoElapTime(elapsedTime);
		}

		//v3.0/getwifiinfolist/
		else if( joinPoint.getSignature().getName().equalsIgnoreCase("getWifiinfoList")) {
			advanceLog.setGetWifiinfoListElapTime(elapsedTime);
		}else if( joinPoint.getSignature().getName().equalsIgnoreCase("getWifiList")) {
			advanceLog.setGetWifiListElapTime(elapsedTime);
		}

		//v3.0/forgetpassword	
		else if( joinPoint.getSignature().getName().equalsIgnoreCase("forgetPassword")) {
			advanceLog.setForgetPasswordElapTime(elapsedTime);
		}else if( joinPoint.getSignature().getName().equalsIgnoreCase("sendForgotPasswordMail")) {
			advanceLog.setSendForgotPasswordMailElapTime(elapsedTime);
		}
		

		//v4.0/getAdvertisements
		else if( joinPoint.getSignature().getName().equalsIgnoreCase("getadvertisementinfoV4")) {
			advanceLog.setGetadvertisementinfoV4(elapsedTime);
		}
		//v4.0/getspecies
		else if( joinPoint.getSignature().getName().equalsIgnoreCase("getSpeciesV4")) {
			advanceLog.setGetSpeciesV4(elapsedTime);
		}
		
		//v4.0/furbitdailyreport/	
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.controller.FurBitControllerV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getFurBitDailyReportv4")) {
			advanceLog.setGetFurBitDailyReportv4_controller(elapsedTime);
		}
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.FurBitReportServiceImplV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getFurBitDailyReport")) {
			advanceLog.setGetFurBitDailyReport_Service(elapsedTime);
		}
		
		//v4.0/furbitreport/
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.controller.FurBitControllerV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getFurBitReportv4")) {
			advanceLog.setGetFurBitReportv4_controller(elapsedTime);
		}
		else if(joinPoint.getSignature().getDeclaringTypeName().equalsIgnoreCase("com.nimble.irisservices.service.impl.FurBitReportServiceImplV4")  
				&& joinPoint.getSignature().getName().equalsIgnoreCase("getFurBitReport")) {
			advanceLog.setGetFurBitReport_Service(elapsedTime);
		}
	
		
		
		apiContext.setAdvanceLog(Boolean.TRUE);
		apiContext.setAdvanceLog(advanceLog);

		_helper.setApiContext(apiContext);
		return retVal;
	}
}
