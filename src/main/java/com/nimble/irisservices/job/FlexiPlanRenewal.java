package com.nimble.irisservices.job;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.nimble.irisservices.quartz.JobService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.InterruptableJob;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.Trigger;
import org.quartz.UnableToInterruptJobException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import com.nimble.irisservices.controller.PushNotificatonController;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSendNotification;
import com.nimble.irisservices.entity.FlexiPlanHistory;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IUserServiceV4;

@Component
public class FlexiPlanRenewal extends QuartzJobBean implements InterruptableJob{

	private static final Logger log = LogManager.getLogger(FlexiPlanRenewal.class);

	@Autowired
	PushNotificatonController pushNotificatonController;
	
	@Autowired
	IUserServiceV4 userServiceV4;
	
	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	@Lazy
	JobService jobService;

	@Autowired
	@Lazy
	IGatewayService iGatewayServiceV4;

    @Autowired
    private Helper _helper;


    @Value("${sheduled_feed_remainder_pushnoti_id}")
	private int sheduled_feed_remainder_pushnoti_id;

    @Value("${txnservice_url}")
    private String txnservice_url;
	
	@Override
	protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
		JobKey key = jobExecutionContext.getJobDetail().getKey();
		log.info("Flexi Plan Renewal Job started with key: " + key.getName() + ", Group: " + key.getGroup()
				+ ", Thread: " + Thread.currentThread().getName() + ", Time: " + new Date());

		JobDataMap dataMap = jobExecutionContext.getJobDetail().getJobDataMap();

		if (dataMap != null) {
			long[] user_id = { (long) dataMap.get("user_id") };
			String[] email = { (String) dataMap.getString("email") };
			String auth = (String) dataMap.getString("auth");
			String subscriptionId = dataMap.containsKey("subscriptionId") ? ((String) dataMap.getString("subscriptionId")) : null;

            String chargebeeid = userServiceV4.getChargebeeid( user_id[0]);

			log.info("Flexi Plan Renewal data :: userId: " + user_id[0] + ", Email: " + email[0] + ", SubscriptionId: " + subscriptionId + ", Auth: " + auth);

			if (subscriptionId != null) {
				FlexiPlanHistory flexiplan = crService.getFlexiplandetailsbySubid(subscriptionId);

				if (flexiplan != null && flexiplan.getIs_paused() == 0 && flexiplan.getCurrent_cycle() < 3) {
					try {
						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						sdf.setTimeZone(TimeZone.getTimeZone("UTC"));

						Date today = new Date();
						flexiplan.setCurrent_cycle_started_at(sdf.format(today));

						Date thirtyDaysLater = new Date(today.getTime() + (30L * 24 * 60 * 60 * 1000));
						flexiplan.setCurrent_cycle_end_at(sdf.format(thirtyDaysLater));
						flexiplan.setCurrent_cycle(flexiplan.getCurrent_cycle() + 1);
						flexiplan.setUpdated_on(new Helper().getCurrentTimeinUTC());
						crService.saveorupdateflexiplanhistory(flexiplan);

						// Reschedule Job
						List<Date> dateList = new ArrayList<>();
						dateList.add(thirtyDaysLater);

						String jobName = user_id[0] + "_" + subscriptionId;
						String groupName = "FLEXI_PLAN_RENEWAL";

						HashMap<String, Object> userDetails = new HashMap<>();
						userDetails.put("user_id", user_id[0]);
						userDetails.put("email", email[0]);
						userDetails.put("auth", auth);
						userDetails.put("subscriptionId", subscriptionId);

						// Delete existing job before scheduling a new one
						jobService.deleteJob(jobName, groupName);
						boolean jobCreatedStatus = jobService.scheduleRepeatJobWithMultipleTriggers(
								jobName, FlexiPlanRenewal.class, dateList, groupName, String.valueOf(user_id[0]), groupName, 0, userDetails
						);

						log.info("Flexi Plan Renewal job scheduled: " + jobCreatedStatus);
					} catch (Exception e) {
						log.error("Error scheduling Flexi Plan Renewal job: ", e);
					}
				} else if(flexiplan != null && flexiplan.getIs_paused() == 1) {
					log.info("Flexi plan paused for the device : "+flexiplan.getGateway_id());
					iGatewayServiceV4.enableOrDisableGateway(flexiplan.getGateway_id()+"",
							user_id[0]+"",false);
					String jobName = user_id[0] + "_" + subscriptionId;
					boolean deleted = jobService.deleteJob(jobName, "FLEXI_PLAN_RENEWAL");
					log.info("Flexi Plan Renewal job deleted: " + deleted);

                    String msurl = txnservice_url + "/v3.0/cancelsubscriptionbygateway?"
                            + "cbid=" +chargebeeid
                            + "&gatewayId=" + flexiplan.getGateway_id()
                            +"&FlexiPause=true";
                    log.info("Call txnservice gatewayfeature API :" + msurl);

                    _helper.httpPOSTRequest(msurl, null, null);
				} else if (flexiplan != null && flexiplan.getIs_paused() == 0 && flexiplan.getCurrent_cycle() == 3) {
					try {
						flexiplan.setIs_paused(1);
						flexiplan.setUpdated_on(new Helper().getCurrentTimeinUTC());
						crService.saveorupdateflexiplanhistory(flexiplan);

						String jobName = user_id[0] + "_" + subscriptionId;
						boolean deleted = jobService.deleteJob(jobName, "FLEXI_PLAN_RENEWAL");
						log.info("Flexi Plan Renewal job deleted: " + deleted);
					} catch (Exception e) {
						log.error("Error deleting Flexi Plan Renewal job: ", e);
					}
				}
			}
		} else {
			log.error("No job data found");
		}
	}


	@Override
	public void interrupt() throws UnableToInterruptJobException {
		// TODO Auto-generated method stub
		
	}

}
