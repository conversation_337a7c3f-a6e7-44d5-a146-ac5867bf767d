package com.nimble.irisservices.job;

import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.InterruptableJob;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.Trigger;
import org.quartz.UnableToInterruptJobException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.nimble.irisservices.helper.Thinkspace;
import com.nimble.irisservices.quartz.JobService;
import com.nimble.irisservices.service.IVerizonService;

public class SIMRetryJob extends QuartzJobBean implements InterruptableJob {

	private static final Logger log = LogManager.getLogger(SIMRetryJob.class);
	@Autowired
	JobService jobService;
	
	@Autowired
	Thinkspace thinkSpace;
	
	@Value("${cust_name}")
	private String cust_name;

	@Value("${acc_name}")
	private String acc_name;

	@Value("${plan}")
	private String plan;

	@Value("${retrycount}")
	private int deafaultRetryCount;
	

	@Autowired
	@Lazy
	private IVerizonService verizonService;


	@Override
	protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
		JobKey key = jobExecutionContext.getJobDetail().getKey();
		Trigger trigger = jobExecutionContext.getTrigger();
		log.info("Verizon SIM Retry Job started with key :" + key.getName() + ", Group :" + key.getGroup() + " , Thread Name :"
				+ Thread.currentThread().getName() + " ,Time now :" + new Date());
		log.info("======================================");

		JobDataMap dataMap = jobExecutionContext.getMergedJobDataMap();
		Date triggerDate = jobExecutionContext.getScheduledFireTime();

//		long userid = Long.parseLong(key.getName());
		String jobType = key.getGroup()+"";

		String simReactivationStatus = jobType;
		String imei = dataMap.get("Meid") + "";
		String iccid = dataMap.get("IccId") + "";
		String reqId = dataMap.get("ReqId") + "";
		String skuNumber = dataMap.get("SkuNumber") + "";

		log.info("MEID: "+imei + " | iccid : "+iccid + " | reqId : "+reqId+"  | skuNumber : "+skuNumber );
		String simState = null;
		String responseStatus = "";
		
		if (simReactivationStatus.equalsIgnoreCase("activate")) {
			responseStatus = thinkSpace.SimActivation(imei, cust_name, acc_name, plan, iccid, reqId,
					verizonService, skuNumber);
			simState = "Retrying for Activation";
		} else if (simReactivationStatus.equalsIgnoreCase("deactivate")) {
			responseStatus = thinkSpace.simDeactivate(imei, cust_name, acc_name, plan, iccid, reqId,
					verizonService);
			simState = "Retrying for Deactivation";
		} else if (simReactivationStatus.equalsIgnoreCase("restore")) {
			responseStatus = thinkSpace.simRestore(imei, cust_name, acc_name, plan, iccid, reqId,
					verizonService);
			simState = "Retrying for Reactivation";
		}
//		 else if (simReactivationStatus.equalsIgnoreCase("suspend")) {
//			responseStatus = thinkSpace.SimSuspend(imei, cust_name, acc_name, plan, iccid, reqId,
//					verizonService);
//			simState = "Retrying for Suspend";
//		}


		log.info(">>>>>>>>>>>>>>>>>>>>> Verizon SIM Retry Job:");
		log.info(responseStatus);
		log.info(">>>>>>>>>>>>>>>>>>>>>");

	}


	@Override
	public void interrupt() throws UnableToInterruptJobException {
	}
}
