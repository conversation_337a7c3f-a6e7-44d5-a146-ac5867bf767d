package com.nimble.irisservices.job;

import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.InterruptableJob;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.Trigger;
import org.quartz.UnableToInterruptJobException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import com.nimble.irisservices.controller.PushNotificatonController;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSendNotification;
import com.nimble.irisservices.service.IUserServiceV4;

@Component
public class PetFeedRemainder extends QuartzJobBean implements InterruptableJob{

	private static final Logger log = LogManager.getLogger(PetFeedRemainder.class);

	@Autowired
	PushNotificatonController pushNotificatonController;
	
	@Autowired
	IUserServiceV4 userServiceV4;
	
	@Value("${sheduled_feed_remainder_pushnoti_id}")
	private int sheduled_feed_remainder_pushnoti_id;
	
	@Override
	protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
		JobKey key = jobExecutionContext.getJobDetail().getKey();
		Trigger trigger = jobExecutionContext.getTrigger();
		log.info("Pet Feed Reminder Job started with key :" + key.getName() + ", Group :" + key.getGroup() + " , Thread Name :"
				+ Thread.currentThread().getName() + " ,Time now :" + new Date());
		log.info("======================================");	
		
		JSendNotification sendNotification  = new JSendNotification();
		
		JobDataMap dataMap = jobExecutionContext.getJobDetail().getJobDataMap();
		
		if( dataMap != null ) {
			long[] user_id = { (long) dataMap.get("user_id") };
			String[] email = { (String) dataMap.getString("email") };
			String auth = (String) dataMap.getString("auth");
			long gateway_id = dataMap.containsKey("gateway_id") ? ((long) dataMap.getLong("gateway_id")) : 0;
			
			log.info("pet_feed_deails remainder data :: user_id : "+ user_id[0] + " :: email : "+ email[0] + " :: gateway_id : "+ gateway_id+" :: auth : "+ auth);
			
			if( gateway_id != 0 && userServiceV4.checkDeviceNotify( user_id[0], gateway_id ) ) {
				
				sendNotification.setPushNotificationId( sheduled_feed_remainder_pushnoti_id );
				sendNotification.setUserID(user_id);
				sendNotification.setEmaiID(email);
				
				JResponse res = pushNotificatonController.sendNotifications( auth, sendNotification);	
			}
				
		} else {
			log.error("No job data found");
		}
		
		
		
	}

	@Override
	public void interrupt() throws UnableToInterruptJobException {
		// TODO Auto-generated method stub
		
	}

}
