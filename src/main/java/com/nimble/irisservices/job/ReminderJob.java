package com.nimble.irisservices.job;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.InterruptableJob;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.Trigger;
import org.quartz.UnableToInterruptJobException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.nimble.irisservices.dto.JReminderDetails;
import com.nimble.irisservices.quartz.JobService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IReminderService;

public class Reminder<PERSON>ob extends QuartzJobBean implements InterruptableJob {

	private static final Logger log = LogManager.getLogger(ReminderJob.class);
	@Autowired
	JobService jobService;

	@Autowired
	IReminderService reminderservice;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Value("${enable_reminder}")
	private boolean enable_reminder;

	@Override
	protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
		JobKey key = jobExecutionContext.getJobDetail().getKey();
		Trigger trigger = jobExecutionContext.getTrigger();
		log.info("Reminder Job started with key :" + key.getName() + ", Group :" + key.getGroup() + " , Thread Name :"
				+ Thread.currentThread().getName() + " ,Time now :" + new Date());
		log.info("======================================");

		JobDataMap dataMap = jobExecutionContext.getMergedJobDataMap();
		Date triggerDate = jobExecutionContext.getScheduledFireTime();
		long userid = Long.parseLong(key.getGroup());
		String remainderName = dataMap.get("jobname") + "";
		String viewkey = null;
		String viewId = null;

		boolean checkIsLive = CheckIsCurrentTrigger(triggerDate);
		JReminderDetails joblist = reminderservice.getReminderDetails(userid, 0, remainderName, "UTC").get(0);

		if (jobExecutionContext.getNextFireTime() != null) {
			boolean updatestatus = reminderservice.updateReminderDetails(joblist.getId(), userid, joblist.getTimezone(),
					joblist.getReminderName(), joblist.getReminderName(),
					new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(jobExecutionContext.getNextFireTime()),
					joblist.getRepeatId(), joblist.getRepeattype(), joblist.getReminderMsg(), "started", 1);
			log.info("Next Trigger Time Update Status " + updatestatus);
		}

		if (key.getGroup() != null && dataMap.get("jobname") != null) {

			int ontime = checkIsLive ? 1 : 0;
			viewkey = reminderservice.saveReminderViewDetails(userid, remainderName, triggerDate, 0, ontime);

		}

		if (viewkey != null && checkIsLive && enable_reminder) {

			viewId = reminderservice.getRemainderViewDetails(userid, joblist.getId(), triggerDate);
			String title = "Reminder : " + joblist.getReminderName();

			try {
				log.info("Set one Second Delay");
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
			}
			log.info("SendPushNotification:  viewid - " + viewId);
			async.sendPushNotification(userid + "", title, joblist.getReminderMsg(), Long.parseLong(viewId),
					"Reminder");
		}

		log.info(">>>>>>>>>>>>>>>>>>>>> Pushnotification id :" + viewId);

	}

	private boolean CheckIsCurrentTrigger(Date triggerDate) {

		try {
			long trigger = triggerDate.getTime();
			long curr = System.currentTimeMillis();

			long diff = curr - trigger;
			if (diff < 60000)
				return true;
			else
				return false;

		} catch (Exception e) {
			log.error("Error While Check Is CurrentTrigger :" + e.getMessage());
		}
		return false;
	}

	@Override
	public void interrupt() throws UnableToInterruptJobException {
	}
}
