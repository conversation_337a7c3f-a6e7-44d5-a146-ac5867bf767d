package com.nimble.irisservices.controller;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.validation.ConstraintViolationException;
import javax.validation.Valid;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JAlertCfg;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JGatewayConfig;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAlertTypeException;
import com.nimble.irisservices.exception.InvalidAsseIdException;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IThrottlingService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class AlertCfgController {

	private static final Logger log = LogManager.getLogger(AlertCfgController.class);

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IAlertCfgService alertCfgService;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	IUserServiceV4 userServiceV4;
	
	@Autowired
	IThrottlingService throttlingService;

	// Used in web
	// ========get AlertCFGs================
	@RequestMapping(value = "v3.0/alertcfg/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertCfgById(@PathVariable String autho, @RequestParam("alertcfgid") String alertcfgid,
			@RequestParam("alerttypeid") String alerttypeid, @RequestParam("assetid") String assetid) {
		JResponse response = new JResponse();

		try {
			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());

			List<JAlertCfg> alertcfgs = alertCfgService.getAlertCfg(alertcfgid, alerttypeid, assetid, user.getId(),
					cmp_cfg.getTemperatureunit());

			Set<Long> assetIds = new HashSet<Long>();

			List<JGateway> userGateway = gatewayService.getGateway(null, null, null, assetid, user.getId(), null);
			List<JGatewayConfig> gatewayConfigs = new ArrayList<JGatewayConfig>();
			if (userGateway != null) {
				for (JGateway jgateway : userGateway) {
					assetIds.add(jgateway.getId());
				}
			}
			
			for (Long id : assetIds) {

				JGatewayConfig config = new JGatewayConfig();
				Gateway gatewayDetails = gatewayService.getGateway(id);
				config.setGatewayConfig(gatewayDetails.getGatewayConfig());
				config.setOnOffStatus(gatewayDetails.isOnOffStatus());
				config.setAssetid(id);

				gatewayConfigs.add(config);
			}
			
//			for (JAlertCfg cfg : alertcfgs) {
//				for (Asset asset : cfg.getAssets()) {
//					assetIds.add(asset.getId());
//				}
//			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("alertcfg", alertcfgs);
			response.put("gatewayConfig", gatewayConfigs);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	// Used in web
	// ========save or update
	// alertcfg================headers="Content-Type=application/json"
	@RequestMapping(value = "v3.0/alertcfg/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveORupdateGatewayById(@PathVariable String autho, @ModelAttribute @Valid JAlertCfg jalertcfg,
			BindingResult res) {
		JResponse response = new JResponse();

		try {

			/* Create or Update Validation */
			if (res.hasErrors())
				return alertcfgValidation(res);

			User user = userService.verifyAuthKey(autho);

			// Fix to send error response for RV guest User when updating the
			// alert config
			if (user.getUsername().equalsIgnoreCase("RVGuestUser")) {
				response.put("Status", 0);
				// log.info("Invalid**********");
				log.info("RVGuestUser : Need full access? Buy and Subscribe to RV PetSafety!");
				response.put("Msg", "Need full access? Buy and Subscribe to RV PetSafety!");
				return response;

			}

			String mobileNos[]=jalertcfg.getMobilenos().split(",");
			
			for(String mobileNo : mobileNos) {
				if (!mobileNo.trim().matches("(\\+1-|1-|91-|\\+91-).*")) {
					response.put("Status", 0);
					response.put("Msg", "You are not allowed to enter mobile number other than (+1) US,(+1) Canada and (+91)India.");
					return response;
				}
			}
			
			Company company = user.giveCompany();
			ThrottlingSettings throttle = company.getThrotsettings();

			long mobile_count = throttle.getMobileNos();
			long email_count = throttle.getEmailIds();
			
			String mailids= jalertcfg.getEmailids().replaceAll("\\s", "");		
			long entered_mobileNos = jalertcfg.getMobilenos().split(",").length;
			long entered_emailIds = mailids.split(",").length;

			if (mobile_count >= entered_mobileNos && email_count >= entered_emailIds || throttle.getId() == 5) {
				log.info("alert name: " + jalertcfg.getName());
				log.info("alert type id: " + jalertcfg.giveAlerttypeid());
				boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(),"NA");
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "You are allowed to enter " + mobile_count + " mobile numbers and " + email_count
						+ " email address");
			}

		} catch (ConstraintViolationException e) {
			log.error("ConstraintViolationException");
			response.put("Status", 0);
			response.put("Msg", "invalid asset id");
			return response;

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (InvalidAlertTypeException e) {
			log.error("invalid alerttype id");
			response.put("Status", 0);
			response.put("Msg", "invalid alerttype id");
			return response;
		} catch (InvalidAsseIdException e) {
			log.error("ConstraintViolationException");
			response.put("Status", 0);
			response.put("Msg", "invalid asset id");
			return response;
		} catch (Exception e) {
			log.info("saveORupdateAlertcfg::::" + e.getMessage());
			log.error("Exception" + e.getMessage());

			response.put("Status", 0);
			response.put("Msg", "alert name cannot be empty and should be unique");
			return response;
		}

		return response;

	}

	/* Create or Update Validation */
	public JResponse alertcfgValidation(BindingResult res) {
		JResponse response = new JResponse();

		response.put("Status", 0);

		if (res.getFieldError("id") != null)
			response.put("Msg", "id should not be empty");
		else if (res.getFieldError("name") != null)
			response.put("Msg", res.getFieldError("name").getDefaultMessage());
		else if (res.getFieldError("minval") != null)
			response.put("Msg", "Min range between -70 to 170");
		else if (res.getFieldError("maxval") != null)
			response.put("Msg", "Max range between -70 to 170");
		else if (res.getFieldError("severity") != null)
			response.put("Msg", "Severity range between 1 to 4");
		else if (res.getFieldError("enable") != null)
			response.put("Msg", "Enable must be true or false");
		else if (res.getFieldError("notifyfreq") != null)
			response.put("Msg", "Alert Frequency Range should be minimum of 10 minutes and maximum of 5 days");
		else if (res.getFieldError("intermittentfreq") != null)
			response.put("Msg", "Intermittent Frequency range between 1 and 1000");
		else if (res.getFieldError("lat") != null)
			response.put("Msg", "Lat should not be empty");
		else if (res.getFieldError("lon") != null)
			response.put("Msg", "Lon should not be empty");
		else if (res.getFieldError("notificationtype") != null)
			response.put("Msg", res.getFieldError("notificationtype").getDefaultMessage());
		else if (res.getFieldError("radius") != null)
			response.put("Msg", "Radius should not be empty");
		else if (res.getFieldError("fencetype") != null)
			response.put("Msg", "Fencetype should not be empty");

		return response;
	}

	// ========save or update
	// alertcfg================headers="Content-Type=application/json"
	@RequestMapping(value = "v3.0/enabledisablealertcfg/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse enabledisablealertcfg(@PathVariable String autho, @RequestParam("alertcfgids") String alertcfgids,
			@RequestParam("isenable") String isenable) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			int result = alertCfgService.enabledisablealertcfg(alertcfgids, Integer.valueOf(isenable));
			if (result > 0) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Alert cfgs not updated");
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}

//	// Used in web
//	@RequestMapping(value = "v3.0/furbitalertcfg/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getFurbitAlertCfg(@PathVariable String autho, @ModelAttribute @Valid JFurbitAlertCfg jfurbitAlertCfg,
//			BindingResult res) {
//		log.info("Entered into saveOrUpdateFurbitAlertCfg : "+autho);
//		JResponse response = new JResponse();
//		try {
//
//			/* Create or Update Validation */
//			if (res.hasErrors())
//				return alertcfgValidation(res);
//
//			UserV4 user = userServiceV4.verifyAuthV3("authkey", autho);
//
//			long cmp_id = user.getCmpId();
//			// Fix to send error response for RV guest User when updating the
//			// alert config
//			if (user.getUsername().equalsIgnoreCase("RVGuestUser")) {
//				response.put("Status", 0);
//				// log.info("Invalid**********");
//				log.error("RVGuestUser : Need full access? Buy and Subscribe to RV PetSafety!");
//				response.put("Msg", "Need full access? Buy and Subscribe to RV PetSafety!");
//				return response;
//			}
//
//			Company company = companyService.getCompany(cmp_id);
//			
//			// get directly using cmp id
//			ThrottlingSettings throttle = throttlingService.getThrotSettingsById(Long.valueOf(company.getThrotsettingsid()));
//
//			long mobile_count = throttle.getMobileNos();
//			long email_count = throttle.getEmailIds();
//
//			long entered_mobileNos = jfurbitAlertCfg.getMobilenos().split(",").length;
//			long entered_emailIds = jfurbitAlertCfg.getEmailids().split(",").length;
//
//			if (mobile_count >= entered_mobileNos && email_count >= entered_emailIds || throttle.getId() == 5) {
//				log.info("alert name: " + jfurbitAlertCfg.getName());
//				log.info("alert type id: " + jfurbitAlertCfg.getAlerttypeid());
//				//
//				boolean result = alertCfgService.saveOrUpdateFurbitAlertCfg(jfurbitAlertCfg, company);
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "You are allowed to enter " + mobile_count + " mobile numbers and " + email_count
//						+ " email address");
//			}
//
//		} catch (ConstraintViolationException e) {
//			log.error("ConstraintViolationException");
//			response.put("Status", 0);
//			response.put("Msg", "invalid asset id");
//			return response;
//
//		} catch (InvalidAuthoException e) {
//			log.error("in valid auth");
//			response.put("Status", 0);
//			response.put("Msg", "invalid authentication key");
//			return response;
//		} catch (InvalidAlertTypeException e) {
//			log.error("invalid alerttype id");
//			response.put("Status", 0);
//			response.put("Msg", "invalid alerttype id");
//			return response;
//		} catch (InvalidAsseIdException e) {
//			log.error("ConstraintViolationException");
//			response.put("Status", 0);
//			response.put("Msg", "invalid asset id");
//			return response;
//		} catch (Exception e) {
//			log.error("saveORupdateAlertcfg::::" + e.getMessage());
//			response.put("Status", 0);
//			response.put("Msg", "alert name cannot be empty and should be unique");
//			return response;
//		}
//		return response;
//	}
//	
//	// Used in web
//	@RequestMapping(value = "v3.0/furbitalertcfgweb/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getFurbitalertCfgWeb(@PathVariable String autho, @RequestParam("alertcfgid") String alertcfgid,
//			@RequestParam("alerttypeid") String alerttypeid, @RequestParam("assetid") String assetid) {
//		JResponse response = new JResponse();
//
//		try {
//			User user = userService.verifyAuthKey(autho);
//			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());
//
//			List<JFurbitAlertCfg> alertcfgs = alertCfgService.getFurbitAlertCfg(alertcfgid, alerttypeid, assetid, user.getId(),
//					cmp_cfg.getTemperatureunit());
//
//			Set<Long> assetIds = new HashSet<Long>();
//
//			List<JGateway> userGateway = gatewayService.getGateway(null, null, null, assetid, user.getId(), null);
//			List<JGatewayConfig> gatewayConfigs = new ArrayList<JGatewayConfig>();
//			if (userGateway != null) {
//				for (JGateway jgateway : userGateway) {
//					assetIds.add(jgateway.getId());
//				}
//			}
//			
//			for (Long id : assetIds) {
//
//				JGatewayConfig config = new JGatewayConfig();
//				Gateway gatewayDetails = gatewayService.getGateway(id);
//				config.setGatewayConfig(gatewayDetails.getGatewayConfig());
//				config.setOnOffStatus(gatewayDetails.isOnOffStatus());
//				config.setAssetid(id);
//
//				gatewayConfigs.add(config);
//			}
//
//			response.put("Status", 1);
//			response.put("Msg", "Success");
//			response.put("furbitalertcfg", alertcfgs);
//			response.put("gatewayConfig", gatewayConfigs);
//
//		} catch (InvalidAuthoException e) {
//			response.put("Status", 0);
//			response.put("Msg", "invalid authentication key");
//			return response;
//		}
//
//		return response;
//
//	}

}
