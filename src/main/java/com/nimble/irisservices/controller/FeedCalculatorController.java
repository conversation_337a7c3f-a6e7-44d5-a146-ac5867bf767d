package com.nimble.irisservices.controller;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dto.JBMIinfo;
import com.nimble.irisservices.dto.JBreed;
import com.nimble.irisservices.dto.JCaloriesinfo;
import com.nimble.irisservices.dto.JGatewayForPet;
import com.nimble.irisservices.dto.JInjuryinfo;
import com.nimble.irisservices.dto.JObject;
import com.nimble.irisservices.dto.JPetprofileV2;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.PetBreeds;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.PetSpecies;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IPetBreedServices;
import com.nimble.irisservices.service.IPetSpeciesServices;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@RestController
public class FeedCalculatorController {
	private static final Logger log = LogManager.getLogger(FeedCalculatorController.class);
	@Autowired
	IUserService userService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Value("${maxpetprofile}")
	private int maxprofilecnt;

	@Autowired 
	@Lazy
	IUserServiceV4 userServiceV4;
	
	@Autowired 
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;
	
	@Autowired
	@Lazy
	IPetSpeciesServices petSpeciesServices;
	
	@Autowired
	@Lazy
	IPetBreedServices breedServices;

	@Value("${bmi_uw}")
	private String bmi_uw;
	
	@Value("${bmi_cw}")
	private String bmi_cw;
	
	@Value("${bmi_ow}")
	private String bmi_ow;
	
	@Value("${injury_lr}")
	private String injury_lr;
	
	@Value("${injury_mr}")
	private String injury_mr;
	
	@Value("${injury_hr}")
	private String injury_hr;
		
	@Value("${calories_msg}")
	private String calories_insight;
	
	@Value("${note_msg}")
	private String note;
	
	@Value("${disclaimer_msg}")
	private String disclaimer_msg;
	
	//kalai
//	@RequestMapping(value = "v3.0/createpetprofile/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse createPetProfile(@PathVariable String autho, @RequestBody List<JPetprofileV2> jpetprofileList,@RequestParam("os") String os) {
//		JResponse response = new JResponse();
//		log.info("Entering createPetProfile : "+autho);
//		try {
//			if (!jpetprofileList.isEmpty()) {
//
//				Map<String, String> map = new HashMap<String, String>();
//
//				try {
//					map = userServiceV4.getUserId_cmpIdByAuthV2(autho);	
//				}catch(InvalidAuthoException ex) {
//					response.put("Status", 0);
//					response.put("Msg", "Invalid authkey");
//					response.put("Error", ex.getLocalizedMessage());
//					log.error("Exception while getting user for authkey : "+autho);
//					return response;
//				}
//
//				long userId = Long.valueOf(map.get("user_id"));
//				long cmpType_id = Long.valueOf(map.get("cmpType_id"));
//
//				if (cmpType_id == 3) {
//					
//					List<JGateway> gateways = gatewayServiceV4.getGatewayV4(null, null, null, null, userId, null);
//					for(JPetprofileV2 jpetprofile : jpetprofileList) {
//						for (JGateway jGateway : gateways) {
//	
//							if (jpetprofile.getGatewayId()!=(jGateway.getId()) && jpetprofile.getGatewayId()!=0) {
//								if (jpetprofile.getName().equalsIgnoreCase(jGateway.getName())) {
//									response.put("Status", 0);
//									response.put("Msg", RegisterUserError.petNameUserMessage);
//									return response;
//	
//								}
//							}
//						}
//						
//						int status =0;
//						String gateway_name = jpetprofile.getName();
//						Pattern validateName= Pattern.compile("^[A-Za-z0-9_-]+$");
//						Matcher matcher = validateName.matcher(gateway_name);
//						
//						if (!matcher.matches()) {
//							log.info("pet name contains special chars");
//							response.put("Status", 0);
//							response.put("Msg", RegisterUserError.ER009);
//							return response;
//						}
//						if (gateway_name.length() >20 ) {
//							response.put("Status", 0);
//							response.put("Msg", "Pet Name should not exceed 20 char");
//							log.info(gateway_name + " Exceeds 20 chars");
//							return response;
//						}
//	
//						boolean isExist = false;
//						if(jpetprofile.getGatewayId() !=0) {
//							isExist = petSpeciesServices.checkGatewayExist(jpetprofile.getGatewayId(), jpetprofile.getId());
//							if(isExist == true) {
//								response.put("Status", 0);
//								response.put("Msg", "Monitor already mapped");
//								return response;
//							}
//						}
//
//						isExist = petSpeciesServices.checkPetnameExist(jpetprofile.getName(), jpetprofile.getUser_id(), jpetprofile.getId());
//						if(isExist == true) {
//							response.put("Status", 0);
//							response.put("Msg", RegisterUserError.petNameUserMessage);
//							return response;
//						}
//					}
//					int status = 0;
//					for(JPetprofileV2 jpetprofile : jpetprofileList) {
//						if (!jpetprofile.getBreed().isEmpty() &&!jpetprofile.getName().isEmpty() && !jpetprofile.getSex().isEmpty()) {
//							
//							int month = (Integer.parseInt(jpetprofile.getAgeYr()) * 12) + Integer.parseInt(jpetprofile.getAgeMonth());
//							
//							Date birth_date = gatewayService.getBirthDate("MONTH", String.valueOf(month));
//							SimpleDateFormat sf =new SimpleDateFormat("yyyy-MM-dd");
//							String date =sf.format(birth_date);
//							
//							jpetprofile.setBirth_date(date);
//							jpetprofile.setUser_id(userId);
//							
//							PetSpecies species = petSpeciesServices.getPetSpeciesByName(jpetprofile.getSpecieName());
//							long speciesid = 1;
//							
//							if (species != null) 
//								speciesid = species.getId();
//							
//							jpetprofile.setSpeciesid(speciesid);
//							
//							PetProfile pp = petSpeciesServices.createPetprofile(jpetprofile);
//							
//							if(pp != null)
//								status = 1;
//							
//							if(jpetprofile.getGatewayId() != 0) {
//								log.info("Enter Into Gateway");
//								boolean issuccess = gatewayService.updateGatewayName(jpetprofile.getName(),String.valueOf(jpetprofile.getGatewayId()));
//								log.info("GatewayName updated with respect to petname");
//							}
//						} 
//					}
//
//					if (status == 1) {
//						response.put("Status", 1);
//						response.put("Msg", "success");
//						response.put("Msg", "Pet Profile saved successfully");
//					} 
//					else {
//						response.put("Status", 0);
//						response.put("Msg", "Error, Not saved");
//					}
//
//				} else {
//					response.put("Status", 0);
//					response.put("Msg", "Invalid company type");
//				}
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Empty pet profile received");
//			}
//
//		}  catch (DataIntegrityViolationException e) {
//			log.error("createPetProfile :" + e.getLocalizedMessage());
//			response.put("Status", 0);
//			response.put("Msg", "Unknown error, Please try after sometime");
//			response.put("Error", e.getLocalizedMessage());
//		} catch (Exception e) {
//			response.put("Status", 0);
//			log.error("createPetProfile :" +  e.getLocalizedMessage());
//			response.put("Msg", "Unknown error, Please try after sometime");
//			response.put("Error", e.getLocalizedMessage());
//
//		}
//		log.info("Exit createPetProfile");
//		return response;
//	}

	//kalai
	@RequestMapping(value = "v3.0/listpetprofile/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPetProfile(@PathVariable String autho,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {

		JResponse response = new JResponse();
		log.info("Entering listPetProfile : "+autho);

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey",autho);
			if(usr==null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				return response;
			}
			List<JPetprofileV2> profilelist = petSpeciesServices.getJPetprofilesByUser(usr.getId(),"1,2");
			int temp_maxprofilecnt = maxprofilecnt;
			int remaincnt = temp_maxprofilecnt;
			boolean woglistflag = false;
			boolean glistflag = false;
			boolean profileflag = false;
			
			List<JPetprofileV2> withoutgatewaylist = new ArrayList<JPetprofileV2>();
			List<JGatewayForPet> gatewaylist = gatewayService.getNotMappedGateway(usr.getId());
			
			if(!profilelist.isEmpty()) {
				if(profilelist.size() > temp_maxprofilecnt)
					temp_maxprofilecnt = profilelist.size();
				if(gatewaylist.size() > temp_maxprofilecnt)
					temp_maxprofilecnt = gatewaylist.size();
				
				remaincnt = temp_maxprofilecnt - profilelist.size();
				
				for(JPetprofileV2 jp : profilelist)
				{
					if(jp.getGatewayId() == 0)
						withoutgatewaylist.add(jp);
				}				
			}			
			if(!withoutgatewaylist.isEmpty())
				woglistflag = true;
			
			if(!gatewaylist.isEmpty()) {
				glistflag = true;
			}
			
			if(!profilelist.isEmpty()) {
				profileflag = true;
			}
			
			response.put("Msg", "Success");
			response.put("Status", 1);
			response.put("maxprofilecnt", temp_maxprofilecnt);
			response.put("remainprofilecnt", remaincnt);
			response.put("wog_plistflag", woglistflag);
			response.put("wopp_glistflag", glistflag);
			response.put("profileflag", profileflag);
			response.put("wopp_glist", gatewaylist);
			response.put("gnotmapped_plist", withoutgatewaylist);
			response.put("profilelist", profilelist);
			response.put("petname_msg", " This pet profile already selected. Please select some other profile");
			response.put("ppselect_msg", "Please select anyone from the list or create new pet profile");

		}catch(Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Unknown error, Please try after sometime");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : listPetProfile : "+ ex.getLocalizedMessage());
		}

		return response;
	}
	
	//kalai
	@RequestMapping(value = "v3.0/listfeedcalcprofile/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listFeedCalcProfile(@PathVariable String autho,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
			) {

		JResponse response = new JResponse();
		log.info("Entering listPetProfile : "+autho);

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey",autho);
			if(usr==null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				return response;
			}
			List<JPetprofileV2> profilelist = petSpeciesServices.getJPetprofilesByUser(usr.getId(),"1");
			
			int remaincnt = maxprofilecnt;
			int temp_maxprofilecnt = maxprofilecnt;
			
			if(!profilelist.isEmpty()) {
				
				if(temp_maxprofilecnt<profilelist.size())
					temp_maxprofilecnt = profilelist.size();
				
				remaincnt = temp_maxprofilecnt - profilelist.size();
			}			

			response.put("Msg", "Success");
			response.put("Status", 1);
			response.put("maxprofilecnt", temp_maxprofilecnt);
			response.put("remainprofilecnt", remaincnt);
			response.put("profilelist", profilelist);

		}catch(Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Unknown error, Please try after sometime");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getPetProfileV4 : "+ ex.getLocalizedMessage());
		}

		return response;
	}

	//kalai
	@RequestMapping(value = "v3.0/generatehealthreport/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse generateHealthReport(@PathVariable String autho, @RequestBody JPetprofileV2 jpetprofile,@RequestParam("os") String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
			) {
		JResponse response = new JResponse();
		log.info("Entering createPetProfile : "+autho);
		try {
			if (jpetprofile != null) {

				Map<String, String> map = new HashMap<String, String>();

				try {
					map = userServiceV4.getUserId_cmpIdByAuthV2(autho);	
				}catch(InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for authkey : "+autho);
					return response;
				}

				long userId = Long.valueOf(map.get("user_id"));
				long cmpType_id = Long.valueOf(map.get("cmpType_id"));
				
				jpetprofile.setUser_id(userId);
				
				if (cmpType_id == 3) {
					String gateway_name = jpetprofile.getName();
					Pattern validateName= Pattern.compile("^[A-Za-z0-9_-]+$");
					Matcher matcher = validateName.matcher(gateway_name);
					
					if (!matcher.matches()) {
						log.info("pet name contains special chars");
						response.put("Status", 0);
						response.put("Msg", RegisterUserError.ER009);
						return response;
					}
					if (gateway_name.length() >20 ) {
						response.put("Status", 0);
						response.put("Msg", "Pet Name should not exceed 20 char");
						log.info(gateway_name + " exceeds 20 char");
						return response;
					}
					boolean isExist = false;
					
					if(jpetprofile.getGatewayId() !=0) {
						isExist = petSpeciesServices.checkGatewayExist(jpetprofile.getGatewayId(), jpetprofile.getId());
						if(isExist == true) {
							response.put("Status", 0);
							response.put("Msg", "Monitor already mapped");
							return response;
						}
					}
					
					isExist = petSpeciesServices.checkPetnameExist(jpetprofile.getName(), jpetprofile.getUser_id(), jpetprofile.getId());
					if(isExist == true) {
						response.put("Status", 0);
						response.put("Msg", RegisterUserError.petNameUserMessage);
						return response;
					}

					PetProfile pp = null;
					if ( !jpetprofile.getBreed().isEmpty() && !jpetprofile.getName().isEmpty() && !jpetprofile.getSex().isEmpty()) {
						
						int month = (Integer.parseInt(jpetprofile.getAgeYr()) * 12) + Integer.parseInt(jpetprofile.getAgeMonth());
						
						Date birth_date = gatewayService.getBirthDate("MONTH", String.valueOf(month));
						SimpleDateFormat sf =new SimpleDateFormat("yyyy-MM-dd");
						String date =sf.format(birth_date);
						
						jpetprofile.setBirth_date(date);
						jpetprofile.setUser_id(userId);
						PetSpecies species = petSpeciesServices.getPetSpeciesByName(jpetprofile.getSpecieName());
						long speciesid = 1;
						
						if (species != null) 
							speciesid = species.getId();
						
						jpetprofile.setSpeciesid(speciesid);
						
						pp = petSpeciesServices.createPetprofile(jpetprofile);
						
						jpetprofile.setId(pp.getId());
						
					} else {
						log.info("createPetProfile: Error : mandatory fields are missing");
						response.put("Status", 0);
						response.put("Msg", "Enter all the mandatory fields like petname,age,sex & breed");
						return response;
					}
	
					if(jpetprofile.getGatewayId() != 0) {
						log.info("Enter Into Gateway");
						boolean issuccess = gatewayService.updateGatewayName(jpetprofile.getName(),String.valueOf(jpetprofile.getGatewayId()));
						log.info("GatewayName updated with respect to petname");
					}
					if (pp != null)		
						response = generateReport(jpetprofile);
					

				} else {
					response.put("Status", 0);
					response.put("Msg", "Invalid company type");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Empty pet profile received");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			log.error("createPetProfile :" +  e.getLocalizedMessage());
			response.put("Msg", "Unknown error, Please try after sometime");
			response.put("Error", e.getLocalizedMessage());

		}
		log.info("Exit createPetProfile");
		return response;
	}
	
	public JResponse generateReport(JPetprofileV2 jpetprofile) {
		JResponse response = new JResponse();
		HashMap<String, String> formulaList = petSpeciesServices.getFormulas();
		try {
			
			// BMI = (weight/(height*height))* 703;
			String formula =formulaList.get("bmi"); 
			
			ExpressionParser parser = new SpelExpressionParser();  
			Expression exp = parser.parseExpression(formula);
			EvaluationContext context = new StandardEvaluationContext(jpetprofile);
			float bmivalue = exp.getValue(context, Float.class);
			
			PetBreeds breed = breedServices.getPetBreedsByBName(jpetprofile.getBreed());
			
			long breedid = 0; // breedid = 0 has default bmi look data
			if(breed!= null)
				breedid = breed.getId();
			long classificationid = petSpeciesServices.getClassification(Integer.parseInt(jpetprofile.getAgeYr()),
					jpetprofile.getWeight());
			
			JBMIinfo bmiinfo = petSpeciesServices.getBMIinfo(breedid, jpetprofile.getSex(), bmivalue);
			String displaymsg = "";
			if(bmiinfo != null) {
				displaymsg = bmiinfo.getBmidesc();
				if(classificationid==1 && bmivalue> bmiinfo.getCw_max()) {
					bmiinfo.setBmivalue( bmiinfo.getCw_max());
					bmiinfo.setInsight(bmi_cw);
					displaymsg = " Ideal Weight";
				}
				else { 
					if(bmiinfo.getOw_max() < bmivalue) {
						int temp_owmax = (int)(Math.ceil((float)bmivalue/50f)*50);
						bmiinfo.setOw_max(temp_owmax);
					}
					
					if(bmivalue <= bmiinfo.getUw_max())					
						bmiinfo.setInsight(bmi_uw);
					else if(bmivalue <= bmiinfo.getCw_max())					
						bmiinfo.setInsight(bmi_cw);
					else				
						bmiinfo.setInsight(bmi_ow);				
				}
				
				bmiinfo.setBmidesc(displaymsg);
			}
			
			//injury = "weight/height";
			formula =formulaList.get("injury"); 
			exp = parser.parseExpression(formula);
			context = new StandardEvaluationContext(jpetprofile);			
			float injuryvalue = exp.getValue(context, Float.class);
			
			JInjuryinfo injuryinfo = petSpeciesServices.getInjuryinfo(injuryvalue);
			
			if(injuryinfo != null) {
				if(classificationid==1 && injuryvalue >= injuryinfo.getMr_max()) {
					injuryinfo.setInjuryindex(injuryinfo.getMr_max());
					injuryinfo.setInjurydesc("Moderate Risk");
					injuryinfo.setInsight(injury_mr);
				}else {
					if(injuryvalue <= injuryinfo.getLr_max())					
						injuryinfo.setInsight(injury_lr);
					else if(injuryvalue <= injuryinfo.getMr_max())					
						injuryinfo.setInsight(injury_mr);
					else				
						injuryinfo.setInsight(injury_hr);				
				}
			}
			// RER = 70*((weight/2.2)^0.75)
			formula =formulaList.get("rer"); 
			exp = parser.parseExpression(formula);
			context = new StandardEvaluationContext(jpetprofile);			
			float rer = exp.getValue(context, Float.class);
			
			JCaloriesinfo caloriesinfo = petSpeciesServices.getFactor(jpetprofile);
			float factor = caloriesinfo.getFactor();
			long id = caloriesinfo.getId();
			//MER = "rer*factor";
			formula =formulaList.get("mer"); 
			short rerValue = (short)Math.round(rer);
			caloriesinfo.setRer(rerValue);
			exp = parser.parseExpression(formula);
			context = new StandardEvaluationContext(caloriesinfo);			
			
			float mer = exp.getValue(context, Float.class);
			
			short merValue = (short)Math.round(mer);
			
			short range1 = (short)Math.round(rerValue * 1.1);
			short range2 = (short)Math.round(rerValue*2);
			short range3 = (short)Math.round(rerValue*3);
			short range4 = (short)Math.round(rerValue*4);
			short range5 = (short)Math.round(rerValue*5);
			short index =1;
			
			if(factor>=1.2 && factor <=2) {
				range2 = (short)Math.round(rer*factor);
				index = 2;
				merValue = range2;
			}else if(factor>=2.1 && factor <=3) {
				range3 = (short)Math.round(rer*factor);
				index = 3;
				merValue = range3;
			}else if(factor>=3.1 && factor <=4) {
				range4 = (short)Math.round(rer*factor);
				index = 4;
				merValue = range4;
			}else if(factor>=4.1 && factor <=5) {
				range5 = (short)Math.round(rer*factor);
				index = 5;
				merValue = range5;
			}
			String calories_msg = merValue + " Kcal Per Day";

			caloriesinfo = new JCaloriesinfo(range1,range2,range3,range4,range5,index,calories_msg);	
			caloriesinfo.setInsight(calories_insight);
			caloriesinfo.setRer(rerValue);
			caloriesinfo.setFactor(factor);
			caloriesinfo.setId(id);
			
			String curUTC = IrisservicesUtil.getUtcDateTime();
			String qry = "INSERT INTO `healthreport_history` (`user_id`,`petprofile_id`, `bmi_id`, `bmiindex`, `bmi_desc`, `injury_id`, `injuryindex`, "
				+ "`injury_desc`, `calories_id`,  `calories_desc`, `merindex`, `range1`, `range2`, `range3`, `range4`, `range5`,`create_date`) VALUES" + 
				"("+jpetprofile.getUser_id()+","+jpetprofile.getId()+","+bmiinfo.getId()+","+bmiinfo.getBmivalue()+",'"+bmiinfo.getBmidesc()+"',"+injuryinfo.getId()
				+ ","+injuryinfo.getInjuryindex()+",'"+injuryinfo.getInjurydesc()+"',"+caloriesinfo.getId()+",'"+caloriesinfo.getCalories_desc()+"',"
				+ caloriesinfo.getMer_index()+","+caloriesinfo.getRange1()+","+caloriesinfo.getRange2()+","+caloriesinfo.getRange3()+","+caloriesinfo.getRange4()
				+","+caloriesinfo.getRange5()+",'"+curUTC+"');";
			boolean rptStatus = petSpeciesServices.saveHealthReport(qry);
			
			NumberFormat formatter = new DecimalFormat("00");
			jpetprofile.setAgeYr(formatter.format(Integer.parseInt(jpetprofile.getAgeYr())));
			jpetprofile.setAgeMonth(formatter.format(Integer.parseInt(jpetprofile.getAgeMonth())));
			String banner_msg = jpetprofile.getName()+"'s health report";
			response.put("Msg", "Success");
			response.put("Status", 1);
			response.put("jpetprofile", jpetprofile);
			response.put("bmiinfo", bmiinfo);
			response.put("injuryinfo", injuryinfo);
			response.put("caloriesinfo", caloriesinfo);
			response.put("banner_msg", banner_msg);
			response.put("disclaimer_msg", disclaimer_msg);
			
		}catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Unknown error, Please try after sometime");
			response.put("Error", e.getLocalizedMessage());
			
			log.error("Exception generateReport: "+e.getLocalizedMessage());
		}
		return response;
	}

	//kalai
	@RequestMapping(value = "v3.0/retrievehealthreport/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse retrieveReport(@PathVariable String autho,@RequestParam("os") String os) {
		JResponse response = new JResponse();
		log.info("Entering retrievehealthreport : "+autho);

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey",autho);
			if(usr==null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				return response;
			}
			response = petSpeciesServices.retrieveHealthReport(usr.getId());
		}catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Unknown error, Please try after sometime");
			response.put("Error", e.getLocalizedMessage());
			
			log.error("Exception retrievehealthreport: "+e.getLocalizedMessage());
		}
		log.info("Exit retrievehealthreport");
		return response;
	}

	//kalai
	@RequestMapping(value = "v3.0/ppdropdownlist/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse ppDropdownList(@PathVariable String autho,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {

		JResponse response = new JResponse();
		log.info("Entering listPetProfile : "+autho);

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey",autho);
			if(usr==null) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key");
				return response;
			}
			//List<PetSpecies> speciesList = petSpeciesServices.getPetSpecies();
			List<JBreed> dogBreedList = breedServices.listBreeds(1);
			List<JBreed> catBreedList = breedServices.listBreeds(2);
			
			List<JObject> activitylevel = new ArrayList<JObject>();
			activitylevel.add(new JObject(1,"Low"));
			activitylevel.add(new JObject(2,"Moderate"));
			activitylevel.add(new JObject(3,"High"));
			
			List<JObject> working = new ArrayList<JObject>();
			working.add(new JObject(1,"Yes"));
			working.add(new JObject(0,"No"));
			
			List<JObject> speciesList = new ArrayList<JObject>();
			speciesList.add(new JObject(1,"Dog"));
			speciesList.add(new JObject(2,"Cat"));
			
			List<JObject> intact = new ArrayList<JObject>();
			intact.add(new JObject(1,"Intact"));
			intact.add(new JObject(0,"Spayed/Neutered"));
			
			List<JObject> gender = new ArrayList<JObject>();
			gender.add(new JObject(1,"Male"));
			gender.add(new JObject(2,"Female"));
			
			HashMap<String, Integer> ageyear = new HashMap<String, Integer>();
			ageyear.put("min", 0);
			ageyear.put("max",20);
			
			response.put("Msg", "Success");
			response.put("Status", 1);
			response.put("species", speciesList);
			response.put("dogbreeds", dogBreedList);
			response.put("catbreeds", catBreedList);
			response.put("activitylevel", activitylevel);
			response.put("working", working);
			response.put("intact", intact);
			response.put("ageyear", ageyear);
			response.put("gender", gender);
			response.put("note", note);

		}catch(Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Unknown error, Please try after sometime");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getPetProfileV4 : "+ ex.getLocalizedMessage());
		}

		return response;
	}

}
