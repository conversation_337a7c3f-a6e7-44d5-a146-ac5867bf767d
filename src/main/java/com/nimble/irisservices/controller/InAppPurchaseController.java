package com.nimble.irisservices.controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.sql.Timestamp;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chargebee.Environment;
import com.chargebee.Result;
import com.chargebee.models.Subscription;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.InAppPurchase;
import com.nimble.irisservices.entity.InAppSubscription;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IInAppPurchseService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class InAppPurchaseController {

	private static final Logger log = LogManager.getLogger(InAppPurchaseController.class);

	@Autowired
	Helper helper;

	@Autowired
	@Lazy
	IUserServiceV4 userServicev4;

	@Autowired
	@Lazy
	IInAppPurchseService inAppPurchse;

	@Autowired
	Helper _helper;

	@RequestMapping(value = "v4.0/updateinAppPurchase/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateInAppPurchase(@PathVariable String autho, @RequestBody InAppPurchase inAppPurchase) {
		JResponse response = new JResponse();

		try {
			UserV4 user = userServicev4.verifyAuthV3("authkey", autho);

			String verifyresult = helper.verifyReceiptThruAppStore(inAppPurchase.getReceipt());

			JSONObject res = new JSONObject(verifyresult.toString());
			InAppSubscription inappSub = intialPacketParse(res);
			inappSub.setUser_id(inAppPurchase.getUser_id());

			int appleStoreIdMappingresult = inAppPurchse.updateInAppPurchseInfo(inAppPurchase);
			log.info("iris User And AppleStore tr_id Mapping status : " + appleStoreIdMappingresult);

			boolean updateUser = userServicev4.updateInAppPurchaseInfo(2, inAppPurchase.getUser_id());
			log.info("User Updated As inapp purchase Status : " + updateUser);

			boolean subCheck = inAppPurchse.saveOrUpdateSubscriptionInfo(inappSub);
			log.info("inapp_purchase_Subscription Table Update Status : " + subCheck);

			if (appleStoreIdMappingresult > 0 && updateUser) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("updateSubscription", subCheck);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failure");
				response.put("updateUser", updateUser);
				response.put("AppleStoreMapping", appleStoreIdMappingresult);
				response.put("updateSubscription", subCheck);
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid user authkey : " + autho);
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error("Error While Updating InAppPurchase :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error Occur :" + e.getLocalizedMessage());
			return response;
		}

		return response;

	}

	@RequestMapping(value = "/v1.0/waggle/inAppPurchase", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public String inAppPurchasewebhooks(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {

		try {

			BufferedReader rd = request.getReader();
			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}

			rd.close();

			log.info("Auto-Renew response : " + result);

			//System.out.println(result);

			JSONObject res = new JSONObject(result.toString());

			InAppSubscription inappSub = parsepacket(res);

			if (inappSub != null) {
				String transactionId = inappSub.getOriginal_transaction_id();

				InAppPurchase iap = inAppPurchse.getInAppPurchseInfo(transactionId);

				if (iap != null) {
					inappSub.setUser_id(iap.getUser_id());
					log.info("Userid : " + iap.getUser_id() + "   trasaction id : " + transactionId);
				} else {
					inappSub.setUser_id(1);
					log.error("Unavailable User infomation in Database for this transactionId :" + transactionId);
				}

				boolean subCheck = inAppPurchse.saveOrUpdateSubscriptionInfo(inappSub);
				//System.out.println("inapp purchase Subscription Update Status : " + subCheck);

			}

		} catch (Exception e) {
			log.error("Exception - checkinAppPurchase : " + e.getLocalizedMessage());
			return "Exception - checkinAppPurchase :\n" + e.getLocalizedMessage();
		}

		return "Success";
	}

	private InAppSubscription parsepacket(JSONObject res) {

		InAppSubscription inAppSub = new InAppSubscription();

		try {

			String product_id;
			int quantity;
			String original_purchase_date;
			String original_transaction_id;
			boolean auto_renew;
			String renew_status = "pending";
			String curr_plan_purchase_date;
			String transaction_id;
			String expires_date;
			boolean enable = true;
			String sub_notification_type;

			JSONObject unified_receipt = res.getJSONObject("unified_receipt");

			JSONObject last_rec = (JSONObject) unified_receipt.getJSONArray("latest_receipt_info").get(0);

			auto_renew = res.getString("auto_renew_status").equalsIgnoreCase("true");
			product_id = res.getString("auto_renew_product_id");
			sub_notification_type = res.getString("notification_type");

			if (sub_notification_type.equalsIgnoreCase("CANCEL"))
				renew_status = "inactive";
			else if (res.getString("auto_renew_product_id").equalsIgnoreCase(last_rec.getString("product_id")))
				renew_status = "active";
			else
				renew_status = "inactive";

			quantity = last_rec.getInt("quantity");
			original_transaction_id = last_rec.getString("original_transaction_id");
			transaction_id = last_rec.getString("transaction_id");

			original_purchase_date = _helper.getUTCDatev2(
					last_rec.getString("original_purchase_date_pst").replace(" America/Los_Angeles", ""),
					"America/Los_Angeles");

			curr_plan_purchase_date = _helper.getUTCDatev2(
					last_rec.getString("purchase_date_pst").replace(" America/Los_Angeles", ""), "America/Los_Angeles");

			expires_date = _helper.getUTCDatev2(
					last_rec.getString("expires_date_pst").replace(" America/Los_Angeles", ""), "America/Los_Angeles");

			inAppSub.setEnable(enable);
			inAppSub.setRenewal_transaction_id(transaction_id);
			inAppSub.setCreated_date(original_purchase_date);
			inAppSub.setStart_date(curr_plan_purchase_date);
			inAppSub.setExpires_date(expires_date);
			inAppSub.setOriginal_transaction_id(original_transaction_id);
			inAppSub.setQuantity(quantity);
			inAppSub.setRenew_status(renew_status);
			inAppSub.setAuto_renew(auto_renew);
			inAppSub.setProduct_id(product_id);
			inAppSub.setSub_type(sub_notification_type);

			return inAppSub;

		} catch (Exception e) {
			log.error("Error While parsepacket thru WebHook : " + e.getMessage());
		}
		return null;
	}

	private InAppSubscription intialPacketParse(JSONObject res) {

		InAppSubscription inAppSub = new InAppSubscription();

		try {

			String product_id;
			int quantity;
			String original_purchase_date;
			String original_transaction_id;
			boolean auto_renew = true;
			String renew_status = "active";
			String curr_plan_purchase_date;
			String transaction_id;
			String expires_date;
			boolean enable = true;
			String sub_notification_type = "Plan Created thru API";

			JSONObject last_rec = (JSONObject) res.getJSONArray("latest_receipt_info").get(0);

			product_id = last_rec.getString("product_id");
			quantity = last_rec.getInt("quantity");
			original_transaction_id = last_rec.getString("original_transaction_id");
			transaction_id = last_rec.getString("transaction_id");

			original_purchase_date = _helper.getUTCDatev2(
					last_rec.getString("original_purchase_date_pst").replace(" America/Los_Angeles", ""),
					"America/Los_Angeles");

			curr_plan_purchase_date = _helper.getUTCDatev2(
					last_rec.getString("purchase_date_pst").replace(" America/Los_Angeles", ""), "America/Los_Angeles");

			expires_date = _helper.getUTCDatev2(
					last_rec.getString("expires_date_pst").replace(" America/Los_Angeles", ""), "America/Los_Angeles");

			inAppSub.setEnable(enable);
			inAppSub.setRenewal_transaction_id(transaction_id);
			inAppSub.setCreated_date(original_purchase_date);
			inAppSub.setStart_date(curr_plan_purchase_date);
			inAppSub.setExpires_date(expires_date);
			inAppSub.setOriginal_transaction_id(original_transaction_id);
			inAppSub.setQuantity(quantity);
			inAppSub.setRenew_status(renew_status);
			inAppSub.setAuto_renew(auto_renew);
			inAppSub.setProduct_id(product_id);
			inAppSub.setSub_type(sub_notification_type);

			return inAppSub;

		} catch (Exception e) {
			log.error("Error While parse intialpacket thru WebHook : " + e.getMessage());
		}
		return null;
	}

	private void updateChargebeeSubscription(String string) {
		// TODO Auto-generated method stub

		Environment.configure("nimblepetapp-test", "test_Hupy8td63cYyP3moTGeYZouL4oNNYZQL");
		try {
			Timestamp ts = new Timestamp(System.currentTimeMillis());
			Result suv = Subscription.update("Azyu7WSTJT8fWGaP").planId("buddy-plus-quartely").request();

		} catch (Exception e) {
			log.error("Error While updateChargebeeSubscriptions thru WebHook : " + e.getMessage());
		}

	}

	private void createChargebeeSubscription(String string, String purchaseInfo) {
		// TODO Auto-generated method stub

		Environment.configure("nimblepetapp-test", "test_Hupy8td63cYyP3moTGeYZouL4oNNYZQL");
		try {

//			JSONObject jres =  new JSONObject(purchaseInfo);
//			
//			JSONObject receiptInfo = jres.getJSONObject("receipt");

			Timestamp ts = new Timestamp(10000);
			Result suv = Subscription.createForCustomer("6olzBSSeX5MTZcU").planId("chum").planUnitPrice(0).request();
//			startDate(ts)Azyu7WSTJT8fWGaP
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error("Error While createChargebeeSubscription thru WebHook : " + e.getMessage());
		}

	}

}
