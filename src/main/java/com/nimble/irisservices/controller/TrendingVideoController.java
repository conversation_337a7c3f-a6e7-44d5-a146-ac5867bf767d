package com.nimble.irisservices.controller;

import java.sql.Timestamp;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JTrendingVideo;
import com.nimble.irisservices.entity.Trendingvideo;
import com.nimble.irisservices.entity.Trendingvideotransaction;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.ITrendingvideoService;
import com.nimble.irisservices.service.IUserService;

@Controller
public class TrendingVideoController {

	private static final Logger log = LogManager.getLogger(TrendingVideoController.class);

	Helper _helper = new Helper();

	@Autowired
	ITrendingvideoService trendingService;

	@Autowired
	IUserService userService;

	@Value("${youtubeStatisticsAPI}")
	private String youtubeAPI;


	@RequestMapping(value="v3.0/trendingvideoslist/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getTrendingVideosList(@PathVariable String autho)
	{

		JResponse response = new JResponse();
		try {
			User user =  null;
			try {
				user = userService.verifyAuthKey(autho);
			}catch(Exception e)
			{
				log.error("user not exists");
			}
			if (user != null) {
				List<JTrendingVideo> trendvideoList = trendingService.getTrendingvideos();

				trendvideoList = trendingService.getTrendingvideoInfo(trendvideoList,user.getId());

				trendvideoList = updateYoutubeStatistics(trendvideoList,youtubeAPI);

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("TrendingVideoList",trendvideoList);
				log.info("getTrendingVideo completed");

			} else {
				response.put("Status", 0);	
				response.put("Msg", "User not exists");
			}

		}
		catch(Exception e)
		{
			response.put("Status", 0);
			response.put("Msg", "failure");
			log.error("getTrendingVideo : "+e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value="v3.0/updatevideostatus/{autho}",method = RequestMethod.POST, headers="Accept=application/json")
	@ResponseBody
	public JResponse updateVideoStatus(@PathVariable String autho,@RequestParam("videoid") long videoid,
			@RequestParam("like") int like,@RequestParam("viewcount") int viewcount)
	{
		JResponse response = new JResponse();
		try {
			User user =  null;
			try {
				user = userService.verifyAuthKey(autho);
			}catch(Exception e)
			{
				log.error("user not exists");
			}

			if (user != null) {

				Trendingvideo videoInfo = trendingService.getvideoinfoByvideoid(videoid);

				if(videoInfo != null) {
					Trendingvideotransaction trendVideoStaus = trendingService.getTrendingVideosByuser(user,videoInfo);

					if(trendVideoStaus==null)
					{
						trendVideoStaus=new Trendingvideotransaction();
						trendVideoStaus.setVideoid(videoInfo);
						trendVideoStaus.setUserId(user);
						trendVideoStaus.setEnable(true);
					}	

					if(like==1)
					{
						trendVideoStaus.setLike(true);
						trendVideoStaus.setDislike(false);
					}
					else if(like == -1)
					{
						trendVideoStaus.setLike(false);
						trendVideoStaus.setDislike(true);
					}
					else if(like == -2)
					{
						trendVideoStaus.setDislike(false);
					}
					else if(like == 2)
					{
						trendVideoStaus.setLike(false);
					}
					else
					{
						// do nothing
					}

					if(viewcount==1)
					{
						trendVideoStaus.setViewCount(trendVideoStaus.getViewCount()+1);
						trendVideoStaus.setLastSeen( IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC()) );
					}
					trendingService.CreateVideoInfoTransaction(trendVideoStaus);		

					response.put("Status", 1);
					response.put("Msg","Success");
					response.put("TrendingVideo",trendVideoStaus);
					log.info("getTrendingVideostatus completed");
				}else {
					response.put("Status", 0);
					response.put("Msg", "Video not exists");
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}
		}catch(ConstraintViolationException ex){
			response.put("Status", 0);
			response.put("Msg","Videostatus already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("create Videostatus : "+ex.getLocalizedMessage());
		}
		catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg","Videostatus creation failed");
			log.error("create Videostatus : " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
		}

		return response;
	}


	@RequestMapping(value="v3.0/createvideoinfo/{autho}",method = RequestMethod.POST, headers="Accept=application/json")
	@ResponseBody
	public JResponse createVideoInfo(@PathVariable String autho, @RequestBody Trendingvideo videoObj)
	{
		JResponse response = new JResponse();
		boolean status = false;
		try {
			Trendingvideo videoInfo = trendingService.getvideoinfoByurl(videoObj.getUrl());

			if(videoInfo==null) {
				videoInfo = new Trendingvideo();
				videoInfo.setUrl(videoObj.getUrl());
			}

			Timestamp createdDt = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());

			videoInfo.setCreatedon(createdDt);
			videoInfo.setTitle(videoObj.getTitle());
			videoInfo.setVideo_Status(videoObj.isVideo_Status());

			status = trendingService.createTrendingvideoInfo(videoInfo);

			if(status == true)
				response.put("Msg","Success");
			else
				response.put("Msg","failure");

			response.put("Status", status);
		}
		catch(ConstraintViolationException ex)
		{
			response.put("Status", status);
			response.put("Msg","videoinfo already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("create videoinfo : "+ex.getLocalizedMessage());
		}
		catch (Exception e) {
			response.put("Status", status);
			response.put("Msg","videoinfo creation failed");
			log.error("create videoinfo : " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
		}

		return response;
	}

	public List<JTrendingVideo> updateYoutubeStatistics(List<JTrendingVideo> trendvideoList, String youtubeAPI2) {

		if(youtubeAPI2.equalsIgnoreCase("false"))
			return trendvideoList;

		System.out.println("update Statistics from Youtube..");
		log.info("update Statistics from Youtube..");

		for(JTrendingVideo tvideos : trendvideoList)
		{
			try {

				String videoID =tvideos.getVideoUrl().split("=")[1];
				String URl = youtubeAPI2.replace("VIDEOID", videoID);
				System.out.println("URl : "+URl);
				String resp = _helper.getURL(URl);

				if(resp.contains("error"))
				{
					log.info("Bad Request : Invalid Access key or video id");
					System.out.println("Bad Request : Invalid Access key or video id");
					continue;
				}

				JSONObject res = new JSONObject(resp);
				JSONObject items =  (JSONObject) res.getJSONArray("items").get(0);
				JSONObject statistics = items.getJSONObject("statistics");

				String viewcount =statistics.getString("viewCount");
				String likeCount =statistics.getString("likeCount");
				String dislikeCount =statistics.getString("dislikeCount");

				tvideos.setTotalvideoViewCount(Integer.parseInt(viewcount));
				tvideos.setTotalvideoLike(Integer.parseInt(likeCount));
				tvideos.setTotalvideoDislike(Integer.parseInt(dislikeCount));

			} catch (NullPointerException | NumberFormatException e) {
				log.error("updateYoutubeStatistics : invalid video id :"+e.getLocalizedMessage());
			} catch (Exception e) {
				log.error("updateYoutubeStatistics : invalid video id :"+e.getLocalizedMessage());
			}
		}

		return trendvideoList;
	}


}
