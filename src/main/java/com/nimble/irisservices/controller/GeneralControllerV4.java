package com.nimble.irisservices.controller;

import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.AppImage;
import com.nimble.irisservices.entity.ReferEarn;
import com.nimble.irisservices.entity.VersionMapping;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAdvertisementService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGeneralConfigService;
import com.nimble.irisservices.service.IUserService;

@Controller
public class GeneralControllerV4 {

	private static final Logger log = LogManager.getLogger(GeneralControllerV4.class);

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IGeneralConfigService generalConfigService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	@Lazy
	IAdvertisementService advService;

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;
	
	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;

	@Value("${petguideurl}")
	private String petGuideURL;

	@Value("${mapboxtoken}")
	private String mapBoxKeyToken;

	@Value("${nimbleauthkey}")
	private String nimbleAuthKey;

	@Value("${Facebook}")
	private String facebook;

	@Value("${Twitter}")
	private String twitter;

	@Value("${Instagram}")
	private String instagram;

	@Value("${Pinterest}")
	private String pinterest;

	@Value("${support}")
	private String support;

	@Value("${Support_Appstore}")
	private String Support_Appstore;

	@Value("#{${Marketing_Appstore}}")
	private Map<String,String> Marketing_Appstore;

	@Value("#{${Privacy_policy}}")
	private Map<String,String> privacy_policy;

	@Value("${buynowfurbit}")
	private String buynowFurbit;

	@Value("#{${buynowpetsafety}}")
	private Map<String,String> buynowpetsafety;
	
	@Value("${blogUrl}")
	private String blogUrl;

	@Value("#{${faqUrl}}")
	private Map<String,String> faqUrl;
	
	@Value("${showpopupregisteration}")
	private boolean showPopUpRegisteration;

	@Value("${amplitude_andriod}")
	private String amplitude_andriod;

	@Value("${amplitude_ios}")
	private String amplitude_ios;

	@Value("${hr_rateus}")
	private boolean hr_rateus;

	@Value("${rateuscount}")
	private int rateuscount;

	@Value("#{${terms_conditions}}")
	private Map<String,String>  terms_conditions;

	@Value("${fb_live}")
	private String fb_live;

	@Value("${chatbot}")
	private boolean chatbot;

	@Value("${showamazonrateus}")
	private boolean showamazonrateus;

	@Value("${showfeedback}")
	private boolean showfeedback;

	@Value("${redirectamazon}")
	private boolean redirectamazon;

	@Value("${amazonredirecturl}")
	private String amazonredirecturl;

	private boolean enablegoogle = true;

	private boolean enablefb = true;

	private boolean enableapple = true;

	@Value("${upgrade4G_url}")
	private String upgrade4G_url;

	@Value("${upgrade_msg}")
	private String upgrade_msg;

	private boolean hide_subscription = false;

	@Value("${sub_title}")
	private String sub_title;

	private boolean ios_hide_subscription = false;

	@Value("${ios_sub_title}")
	private String ios_sub_title;

	@Value("${amazon.alexa.app_url}")
	private String alexaAppURL;

	@Value("${amazon.alexa.lwa_fallback_url}")
	private String LWA_fallback_URL;

	@Value("${amazon.alexa.clientid}")
	private String alexaClientId;

	@Value("${amazon.alexa.state}")
	private String alexaState;

	@Value("${waggle.universal_url}")
	private String waggleUniversalUrl;

	@Value("${waggle.redirect_uri}")
	private String waggleRedirectUrl;

	@Value("${show_alexa}")
	private boolean show_alexa = true;

	@Value("${show_user_story}")
	private boolean show_user_story = true;

	private String petdashboard_img = "NA";

	@Value("#{${petdashboard_url}}")
	private Map<String,String> petdashboard_url;

	@Value("${enable_reminder}")
	private boolean enable_reminder = true;

	// @Value("${enable_tips}")
	private boolean enable_tips = true;

	private boolean enable_powerloss = true;

	@Value("${enable_event}")
	private boolean enable_event = true;
	
	private boolean show_orderid = true;
	
	private String plan_version = "V1";

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";
	
	// v4.0/getgeneraldata - SIV
	@RequestMapping(value = "v4.0/getgeneraldata", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGeneraldataV4(
			@RequestParam(value = "userid", defaultValue = "", required = false) String userid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "backing", defaultValue = "MT", required = false) String backing,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		JResponse response = new JResponse();

		Helper _helper = new Helper();

		log.info("backing key : "+backing);
//		AES aes = new AES();
//		String auth = null;
//		if (backing != null) {
//			if (!backing.equals("MT")) {
//				String[] credential = _helper.decodeInternalKey(backing);
//				String finalOut = aes.decode(credential[0], credential[1]);
//				
//				if (finalOut == null) {
//					response.put("Status", 0);
//					response.put("Msg", "Authentication Error");
//					return response;
//				}
//				log.info("AES decryption success : "+backing+" : "+finalOut);
//			}
//		} else {
//			response.put("Status", 0);
//			response.put("Msg", "Authentication Error");
//			return response;
//		}

		try {
			ReferEarn latestReferEarn = new ReferEarn();

			latestReferEarn = generalConfigService.getLatestReferEarn();

			String updatepaymentmethod = _helper.getExternalConfigValue("updatepaymentmethod", externalConfigService);
			
			if(device_country.equalsIgnoreCase("US")|| device_country.equalsIgnoreCase("NA")
					|| device_country.toLowerCase().contains("india") || device_country.equalsIgnoreCase("in")
					|| device_country.isEmpty() || device_country == null) {
				device_country = "US";
			}
			
			String supportPhone = supportContactNumber.get(device_country);
			String supportEmail = supportContactEmail.get(device_country);
			String guideURL = petGuideURL;
			String mapBoxToken = mapBoxKeyToken;

			if (latestReferEarn != null) {

				response.put("referEarn", latestReferEarn);

			} else {

				response.put("referEarn", "NA");
			}

			if (!supportPhone.isEmpty()) {

				response.put("supportPhone", supportPhone);

			} else {

				response.put("supportPhone", "NA");
			}

			if (!supportEmail.isEmpty()) {

				response.put("supportEmail", supportEmail);

			} else {

				response.put("supportEmail", "NA");
			}

			if (!guideURL.isEmpty()) {

				response.put("petGuideURL", guideURL);

			} else {

				response.put("petGuideURL", "NA");
			}

			if (!mapBoxToken.isEmpty()) {

				response.put("mapBoxToken", mapBoxToken);

			} else {

				response.put("mapBoxToken",
						"pk.eyJ1IjoibmltYmxld2lyZWxlc3MiLCJhIjoiY2preHBjcnV3MGE0MjNwcjBucmNnZzd5MSJ9.iEpIubbVkS8Wc8t4UlIybQ");
			}

			if (!nimbleAuthKey.isEmpty()) {

				response.put("nimbleAuthKey", nimbleAuthKey);

			} else {

				response.put("nimbleAuthKey",
						"pk.eyJ1IjoibmltYmxld2lyZWxlc3MiLCJhIjoiY2preHBjcnV3MGE0MjNwcjBucmNnZzd5MSJ9.iEpIubbVkS8Wc8t4UlIybQ");
			}

			if (!facebook.isEmpty()) {

				response.put("facebook", facebook);

			} else {

				response.put("facebook", "NA");
			}
			if (!twitter.isEmpty()) {

				response.put("twitter", twitter);

			} else {

				response.put("twitter", "NA");
			}

			if (!instagram.isEmpty()) {

				response.put("instagram", instagram);

			} else {

				response.put("instagram", "NA");
			}
			if (!pinterest.isEmpty()) {

				response.put("pinterest", pinterest);

			} else {

				response.put("pinterest", "NA");
			}

			if (!Support_Appstore.isEmpty()) {

				response.put("support_appstore", Support_Appstore);

			} else {

				response.put("support_appstore", "NA");
			}

			if (!Marketing_Appstore.isEmpty()) {

				response.put("marketing_Appstore", Marketing_Appstore.get(device_country));

			} else {

				response.put("marketing_Appstore", "NA");
			}

			if (!privacy_policy.isEmpty()) {

				response.put("privacy_policy", privacy_policy.get(device_country));

			} else {

				response.put("privacy_policy", "NA");
			}

			if (!app_ver.isEmpty() && !os.isEmpty()) {
				VersionMapping verObj = crService.getVersionMapping(app_ver, os);
				if (verObj != null) {
					hide_subscription = verObj.isHide_subscription();
					ios_hide_subscription = verObj.isHide_subscription();
					enableapple = verObj.isEnableapple();
					enablegoogle = verObj.isEnablegoogle();
					enablefb = verObj.isEnablefb();
					enable_powerloss = verObj.isEnable_powerloss();
					show_user_story = verObj.isShow_user_story();
					enable_tips = verObj.isEnable_tips();
					show_orderid = verObj.isShow_orderid();
					plan_version = verObj.getPlan_version();
				}
			}

			AppImage appImgObj = advService.getAppImages("common", "petdashboard");
			if (appImgObj != null) {
				petdashboard_img = appImgObj.getImg_path();
			}
			response.put("buynowfurbit", buynowFurbit);
			response.put("buynowpetsafety", buynowpetsafety.get(device_country));
			response.put("showPopUpRegisteration", showPopUpRegisteration);
			response.put("blogUrl", blogUrl);
			response.put("faqUrl", faqUrl.get(device_country));
			response.put("enablegoogle", enablegoogle);
			response.put("enablefb", enablefb);
			response.put("enableapple", enableapple);
			response.put("updatepaymentmethod", updatepaymentmethod);
			response.put("amplitude_andriod", amplitude_andriod);
			response.put("amplitude_ios", amplitude_ios);
			response.put("petservicemsg", "Available in next release");
			response.put("rateuscount", rateuscount);
			response.put("hr_rateus", hr_rateus);
			response.put("fb_live", fb_live);
			response.put("chatbot", chatbot);
			response.put("terms_conditions", terms_conditions.get("US"));
			response.put("showamazonrateus", showamazonrateus);
			response.put("showfeedback", showfeedback);
			response.put("redirectamazon", redirectamazon);
			response.put("amazonredirecturl", amazonredirecturl);
			response.put("temp_maxinfo",
					"* We recommend setting the max limit atleast <b>2&#176 F/1&#176C</b> above the desired ambient temperature.");
			response.put("temp_mininfo",
					"* We recommend setting the min limit atleast <b>2&#176 F/1&#176C</b> below the desired ambient temperature.");
			response.put("temp_range", 2);
			response.put("upgrade_msg", upgrade_msg);
			response.put("upgrade4g_url", upgrade4G_url);
			response.put("hide_subscription", hide_subscription);
			response.put("sub_title", sub_title);
			response.put("ios_hide_subscription", ios_hide_subscription);
			response.put("ios_sub_title", ios_sub_title);

			if (!alexaAppURL.isEmpty()) {
				String alexaAppURL_ = alexaAppURL + "?fragment=skill-account-linking-consent"
				// check the client Id
						+ "&client_id=" + alexaClientId + "&scope=alexa::skills:account_linking" + "&skill_stage="
						+ alexaState + "&response_type=code" + "&redirect_uri=" + waggleUniversalUrl + "&state="
						+ alexaState;
				response.put("alexa_app_url", alexaAppURL_);
			} else {
				response.put("alexa_app_url", "NA");
			}

			if (!LWA_fallback_URL.isEmpty()) {
				String fallbackUrl = LWA_fallback_URL + "?client_id=" + alexaClientId
						+ "&scope=alexa::skills:account_linking" + "&response_type=code" + "&redirect_uri="
						+ waggleUniversalUrl + "&state=" + alexaState;
				response.put("lwa_fallback_url", fallbackUrl);
			} else {
				response.put("lwa_fallback_url", "NA");
			}

			response.put("show_alexa", show_alexa);
			response.put("show_user_story", show_user_story);
			response.put("petdashboard_img", petdashboard_img);
			response.put("petdashboard_url", petdashboard_url.get(device_country));
			response.put("enable_reminder", enable_reminder);
			response.put("enable_tips", enable_tips);
			response.put("enable_powerloss", enable_powerloss);
			response.put("enable_event", enable_event);
			response.put("show_orderid", show_orderid);
			response.put("plan_version", plan_version);
			response.put("Status", 1);

			response.put("Msg", "Success");

			return response;

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting general data.");
			return response;
		}
	}

	@RequestMapping(value = "v4.0/getlogindata", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getLogindataV4(@RequestHeader HttpHeaders header,@RequestParam("os") String os,@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		log.info("auth key : "+auth);
//		Helper _helper = new Helper();

//		log.info("backing key : "+backing);
//		AES aes = new AES();
//		String auth = null;
//		if (backing != null) {
//			if (!backing.equals("MT")) {
//				String[] credential = _helper.decodeInternalKey(backing);
//				String finalOut = aes.decode(credential[0], credential[1]);
//				
//				if (finalOut == null) {
//					response.put("Status", 0);
//					response.put("Msg", "Authentication Error");
//					return response;
//				}
//				log.info("AES decryption success : "+backing+" : "+finalOut);
//			}
//		} else {
//			response.put("Status", 0);
//			response.put("Msg", "Authentication Error");
//			return response;
//		}


		try {
			
			if(!auth.equalsIgnoreCase(validation_authkey)) {
				response.put("Status", 0);
				response.put("Msg", "Authentication Error");
			}
			else {

				if (!app_ver.isEmpty() && !os.isEmpty()) {
					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
					
					if (verObj != null) {						
						enableapple = verObj.isEnableapple();
						enablegoogle = verObj.isEnablegoogle();
						enablefb = verObj.isEnablefb();
						enable_tips = verObj.isEnable_tips();
						plan_version = verObj.getPlan_version();
					}
				}
				
				if(device_country.equalsIgnoreCase("US")|| device_country.equalsIgnoreCase("NA")
						|| device_country.toLowerCase().contains("india") || device_country.equalsIgnoreCase("in")
						|| device_country.isEmpty() || device_country == null) {
					device_country = "US";
				}
				
				String supportPhone = supportContactNumber.get(device_country);
				String supportEmail = supportContactEmail.get(device_country);
				
				response.put("supportPhone", supportPhone);
				response.put("supportEmail", supportEmail);
				
				response.put("enablegoogle", enablegoogle);
				response.put("enablefb", enablefb);
				response.put("enableapple", enableapple);
				response.put("terms_conditions", terms_conditions.get(device_country));
				response.put("privacy_policy", privacy_policy.get(device_country));
				response.put("facebook", facebook);
				response.put("enable_tips", enable_tips);
				response.put("amplitude_andriod", amplitude_andriod);
				response.put("amplitude_ios", amplitude_ios);
				response.put("nimbleAuthKey", nimbleAuthKey);
				response.put("plan_version", plan_version);
				response.put("Status", 1);
				response.put("Msg", "Success");
			}
			return response;

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting login data.");
			return response;
		}
	}

}
