package com.nimble.irisservices.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JBacking;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.ReferEarn;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGeneralConfigService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

import freemarker.template.Configuration;
import freemarker.template.Template;

@Controller
public class GeneralController {

	@Autowired
	@Lazy
	IUserService userService;
	
	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IGeneralConfigService generalConfigService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;
	
	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;
	
	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;

	@Value("${petguideurl}")
	private String petGuideURL;

	@Value("${mapboxtoken}")
	private String mapBoxKeyToken;

	@Value("${nimbleauthkey}")
	private String nimbleAuthKey;

	@Value("${Facebook}")
	private String facebook;

	@Value("${Twitter}")
	private String twitter;

	@Value("${Instagram}")
	private String instagram;

	@Value("${Pinterest}")
	private String pinterest;

	@Value("${support}")
	private String support;

	@Value("${Support_Appstore}")
	private String Support_Appstore;

	@Value("#{${Marketing_Appstore}}")
	private Map<String,String> Marketing_Appstore;

	@Value("#{${Privacy_policy}}")
	private Map<String,String> privacy_policy;

	@Value("${buynowfurbit}")
	private String buynowFurbit;

	@Value("#{${buynowpetsafety}}")
	private Map<String,String> buynowpetsafety;
	
	@Value("${blogUrl}")
	private String blogUrl;
	
	@Value("#{${faqUrl}}")
	private Map<String,String> faqUrl;
	
	@Value("${showpopupregisteration}")
	private boolean showPopUpRegisteration;
	
	@Value("${amplitude_andriod}")
	private String amplitude_andriod;

	@Value("${amplitude_ios}")
	private String amplitude_ios;
	
	@Value("${hr_rateus}")
	private boolean hr_rateus;
	
	@Value("${rateuscount}")
	private int rateuscount;
	
	@Value("#{${terms_conditions}}")
	private Map<String,String>  terms_conditions;
	
	@Value("${fb_live}")
	private String fb_live;
	
	@Value("${chatbot}")
	private boolean chatbot;
	
	@Value("${showamazonrateus}")
	private boolean showamazonrateus;
	
	@Value("${redirectamazon}")
	private boolean redirectamazon;
	
	@Value("${amazonredirecturl}")
	private String amazonredirecturl;
	
	@Autowired
	Helper _helper;

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";
	
	@Value("${return_username}")
	private String return_username = "NA";
	
	@Value("${upgrade_username}")
	private String upgrade_username = "NA";


	@Value("${show_pet_monitor_location}")
	private boolean showPetMonitor;

	@Autowired
	Configuration templates;
	
	private static final Logger log = LogManager.getLogger(GeneralController.class);

	@RequestMapping(value = "v3.0/referearn/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveOrUpdate(@PathVariable String autho, @RequestBody ReferEarn referEarn) {
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);

			if (user.getRole().getId() == 1) {

				boolean isSuccess = false;

				isSuccess = generalConfigService.saveOrUpdateReferEarn(referEarn);

				if (isSuccess) {
					response.put("Status", 1);
					log.info("Refer Earn inserted/updated successfully. ");
					response.put("Msg", "Refer & Earn inserted/updated successfully.");
				} else {
					response.put("Status", 0);
					log.info("Refer & Earn not inserted/updated successfully.");
					response.put("Msg", "Refer & Earn not inserted/updated successfully.");
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "User Does not have permission to create company Accounts");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error("Excepitoin while creating/updating refer and earn details.");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while creating/updating refer and earn details.");
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v3.0/getAllReferEarn", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllReferEarn() {
		JResponse response = new JResponse();
		try {

			List<ReferEarn> referList = new ArrayList<ReferEarn>();

			referList = generalConfigService.getAllReferEarn();

			if (referList != null) {
				if (referList.size() > 1) {
					response.put("Status", 1);
					response.put("referEarn", referList);
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "No refer earn found");
					return response;
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "No refer earn found");
				return response;
			}
		} catch (Exception e) {
			log.error("Excepitoin while getting refer & earn.");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting refer & earn.");
			return response;
		}
	}

	@RequestMapping(value = "v3.0/getgeneraldata", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGeneraldata(@RequestParam(value ="userid",defaultValue = "", required = false) String userid,
			 @RequestParam(value ="os",defaultValue = "", required = false) String os,
				@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		JResponse response = new JResponse();

		Helper _helper = new Helper();
		try {

			ReferEarn latestReferEarn = new ReferEarn();

			latestReferEarn = generalConfigService.getLatestReferEarn();

			//			String supportPhone = _helper.getExternalConfigValue("supportcontactnumber", externalConfigService);
			//			String supportEmail = _helper.getExternalConfigValue("supportcontactemail", externalConfigService);

			String enablegoogle = _helper.getExternalConfigValue("enablegoogle", externalConfigService);
			String enablefb = _helper.getExternalConfigValue("enablefb", externalConfigService);
			String enableapple = _helper.getExternalConfigValue("enableapple", externalConfigService);
			String updatepaymentmethod = _helper.getExternalConfigValue("updatepaymentmethod", externalConfigService);
			
			if(device_country.equalsIgnoreCase("US")|| device_country.equalsIgnoreCase("NA")
					|| device_country.toLowerCase().contains("india") || device_country.equalsIgnoreCase("in")
					|| device_country.isEmpty() || device_country == null) {
				device_country = "US";
			}
			
			String supportPhone = supportContactNumber.get(device_country);
			String supportEmail = supportContactEmail.get(device_country);
			String guideURL=petGuideURL;
			String mapBoxToken=mapBoxKeyToken;
			
			if (latestReferEarn != null) {

				response.put("referEarn", latestReferEarn);

			} else {

				response.put("referEarn", "NA");
			}

			if (!supportPhone.isEmpty()) {

				response.put("supportPhone", supportPhone);

			} else {

				response.put("supportPhone", "NA");
			}

			if (!supportEmail.isEmpty()) {

				response.put("supportEmail", supportEmail);

			} else {

				response.put("supportEmail", "NA");
			}

			if (!guideURL.isEmpty()) {

				response.put("petGuideURL", guideURL);

			} else {

				response.put("petGuideURL", "NA");
			}

			if (!mapBoxToken.isEmpty()) {

				response.put("mapBoxToken", mapBoxToken);

			} else {

				response.put("mapBoxToken", "pk.eyJ1IjoibmltYmxld2lyZWxlc3MiLCJhIjoiY2preHBjcnV3MGE0MjNwcjBucmNnZzd5MSJ9.iEpIubbVkS8Wc8t4UlIybQ");
			}

			if (!nimbleAuthKey.isEmpty()) {

				response.put("nimbleAuthKey", nimbleAuthKey);

			} else {

				response.put("nimbleAuthKey", "pk.eyJ1IjoibmltYmxld2lyZWxlc3MiLCJhIjoiY2preHBjcnV3MGE0MjNwcjBucmNnZzd5MSJ9.iEpIubbVkS8Wc8t4UlIybQ");
			}

			if (!facebook.isEmpty()) {

				response.put("facebook", facebook);

			} else {

				response.put("facebook", "NA");
			}
			if (!twitter.isEmpty()) {

				response.put("twitter", twitter);

			} else {

				response.put("twitter", "NA");
			}


			if (!instagram.isEmpty()) {

				response.put("instagram", instagram);

			} else {

				response.put("instagram", "NA");
			}
			if (!pinterest.isEmpty()) {

				response.put("pinterest", pinterest);

			} else {

				response.put("pinterest", "NA");
			}

			if (!Support_Appstore.isEmpty()) {

				response.put("support_appstore", Support_Appstore);

			} else {

				response.put("support_appstore", "NA");
			}

			if (!Marketing_Appstore.isEmpty()) {

				response.put("marketing_Appstore", Marketing_Appstore.get(device_country));

			} else {

				response.put("marketing_Appstore", "NA");
			}

			if (!privacy_policy.isEmpty()) {

				response.put("privacy_policy", privacy_policy.get(device_country));

			} else {

				response.put("privacy_policy", "NA");
			}

			response.put("buynowfurbit",buynowFurbit);
			response.put("buynowpetsafety", buynowpetsafety.get(device_country));
			response.put("showPopUpRegisteration", showPopUpRegisteration);
			response.put("blogUrl", blogUrl);
			response.put("faqUrl", faqUrl.get(device_country));
			response.put("enablegoogle", enablegoogle);
			response.put("enablefb", enablefb);
			response.put("enableapple", enableapple);
			response.put("updatepaymentmethod", updatepaymentmethod);
			response.put("amplitude_andriod", amplitude_andriod);
			response.put("amplitude_ios", amplitude_ios);
			response.put("petservicemsg", "Coming Soon!");
			response.put("rateuscount", rateuscount);
			response.put("hr_rateus", hr_rateus);
			response.put("fb_live",fb_live);
			response.put("chatbot", chatbot);
			response.put("terms_conditions", terms_conditions.get(device_country));
			response.put("showamazonrateus", showamazonrateus);
			response.put("amazonredirecturl", amazonredirecturl);

			response.put("showPMLocation",showPetMonitor);
			response.put("Status", 1);
			response.put("Msg", "Success");

			return response;

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting general data.");
			return response;
		}
	}

	//Related to BLE
	@RequestMapping(value = "v3.0/getfurbitdevicecmd/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFurbitDeviceCmd(@PathVariable String auth, @RequestParam("os") String os) {
		JResponse response = new JResponse();
		try {
			User user = null;
			try {
				user = userService.verifyAuthKey(auth);
			} catch (Exception e) {
				log.error("getCurrentSubscriptionPlan:userby auth : " + e.getMessage());
			}
			
			if (user != null ) {
				String cmd = "%setkey=f5g6s8k6g#,%setqrc=400000#,%reset=0x0#";
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("cmd", cmd);
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exist");
			}

		} catch (Exception e) {
			log.error("getFurbitDeviceCmd : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Excepition while sending FurbitDeviceCmd");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}
	
	@RequestMapping(value = "v3.0/getchatbot/{auth}", method = RequestMethod.GET,produces = MediaType.TEXT_HTML_VALUE, headers = "Accept=application/json")
	@ResponseBody
	public ResponseEntity<String> getChatBot(@PathVariable String auth, @RequestParam("os") String os) {
		JResponse response = new JResponse();
		try {
			String body = "";

//			Template t = templates.getTemplate("chatbot.ftl");
//			Map<String, String> map = new HashMap<>();
//			//map.put("FNAME", user.getFirstname() );
//			body = FreeMarkerTemplateUtils.processTemplateIntoString(t, map);
//			return ResponseEntity.badRequest().body(body);

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV3("authkey", auth);
			} catch (Exception e) {
				log.error("getCurrentSubscriptionPlan:userby auth : " + e.getMessage());
			}
			
			if (user != null ) {
				
				if(chatbot == true) {
					Template t = templates.getTemplate("chatbot.ftl");
					Map<String, String> map = new HashMap<>();
					map.put("UNAME",user.getUsername());
					map.put("EMAIL",user.getEmail());
					map.put("PHONENO",user.getMobileno());
					map.put("FNAME", user.getFirstname() );
					body = FreeMarkerTemplateUtils.processTemplateIntoString(t, map);
					return ResponseEntity.badRequest().body(body);
				}else {
					log.info("Chatbot not enabled");
				}
				
			} else {
				log.info("User not exists");
			}

		} catch (Exception e) {
			log.error("getChatBot : "+e.getLocalizedMessage());
			return ResponseEntity.badRequest().body("");
		}
		return ResponseEntity.badRequest().body("");
	}
	
	@RequestMapping(value = "v4.0/updatereturndevices", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateReturnDevices(@RequestParam("order_id") long order_id,
			@RequestParam("isChecked") Boolean isChecked, @RequestParam("meid") String meid, 
			@RequestParam("user_id") long userid, @RequestHeader HttpHeaders header) {
			
		JResponse response = new JResponse();
		boolean updatedCmpInGateway = false;
		boolean isAssignedToLogin=false;
		boolean updateOrderMap=false;
		try {
			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidationNiom(backing);

			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (!validation_authkey.equals(jBacking.getAuthKey())) {
				response.put("Status", -2);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			
			log.info("Entered Main::updateReturnDevices");
			Orders getOrder = niomDbservice.getOrderDetails(order_id);
			
			if(isChecked) {

				boolean isUpdatedOrderTable = niomDbservice.updateOrdersTable(order_id, getOrder.getQuantity(),
						getOrder.getReturn_units() + 1, "Refunded");
				
				if(isUpdatedOrderTable) {
					
					Gateway gateway = gatewayService.getGatewayDetails(meid);
					
					if(gateway != null) {

						String gatewayId = String.valueOf(gateway.getId());

						boolean removeUsergateway = userService.deleteUserGateway(userid, gateway.getId());

						UserV4 userv4 = companyService.getCompanyId(return_username);

						updatedCmpInGateway = companyService.updateCmpIdInGateway(userv4.getCmpId(), meid);

						isAssignedToLogin = gatewayService.assignGatewayToLogin(gatewayId, userv4.getId());

						updateOrderMap = niomDbservice.updateOrderMap(order_id, meid, "returned_device");

						if (updateOrderMap) {
							response.put("Status", 1);
							response.put("Msg", "Successfuly Updated");
							response.put("Return Time", System.currentTimeMillis());
							log.info("Successfuly updated return");
						} else {
							response.put("Status", 0);
							response.put("Msg", "Return Device not Updated in table");
							response.put("Return Time", System.currentTimeMillis());
						}
					} else {
						log.info("Device not found in gateway : "+meid);
						niomDbservice.updateOrdersTable(order_id, getOrder.getQuantity(),
								getOrder.getReturn_units() - 1, "NA");
						
						response.put("Status", 0);
						response.put("Msg", "No Gateway Found");
						response.put("Return Time", System.currentTimeMillis());
					}	
				} else {
					response.put("Status", 0);
					response.put("Msg", "All Units have been already returned!!");
					response.put("Return Time", System.currentTimeMillis());
				}
				
			}		

		} catch (Exception e) {
			log.error("Exception occured while updateReturnDevices - " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured : " + e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
		}
		return response;
	}
	
	@RequestMapping(value = "v4.0/upgradedevice", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse upgradeDevice(@RequestParam("order_id") long order_id, @RequestParam("meid") String meid, 
			@RequestParam("user_id") long userid, @RequestHeader HttpHeaders header) {
		log.info("Entered upgradeDevice ::");
		JResponse response = new JResponse();
		try {
			String backing = header.getFirst("backing");
			JBacking jBacking = _helper.backingKeyValidationNiom(backing);

			if (jBacking.getStatus() <= 0) {
				response.put("Status", jBacking.getStatus());
				response.put("Msg", jBacking.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (!validation_authkey.equals(jBacking.getAuthKey())) {
				response.put("Status", -2);
				response.put("Msg", "Invalid Authkey");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			Orders getOrder = niomDbservice.getOrderDetails(order_id);

			boolean isUpdatedOrderTable = niomDbservice.updateOrdersTable(order_id, getOrder.getQuantity(),
					getOrder.getReturn_units(), "Upgraded");

			log.info("Orders update status : "+isUpdatedOrderTable);

			long gatewayId = gatewayService.getGatewayId(meid);

			if(gatewayId > 0) {

				String gatewayIdStr = String.valueOf(gatewayId);

				userService.deleteUserGateway(userid, gatewayId);

				UserV4 userv4 = companyService.getCompanyId(upgrade_username);

				companyService.updateCmpIdInGateway(userv4.getCmpId(), meid);

				gatewayService.assignGatewayToLogin(gatewayIdStr, userv4.getId());
				
				boolean updateOrderMap = niomDbservice.updateOrderMap(order_id, meid, "upgraded_device");

				if (updateOrderMap) {
					log.info("Successfuly updated upgrade device");
					response.put("Status", 1);
					response.put("Msg", "Updated Successfully");
					response.put("Return Time", System.currentTimeMillis());
				} else {
					response.put("Status", 0);
					response.put("Msg", "Not updated. Please try again");
					response.put("Return Time", System.currentTimeMillis());
				}	
			} else {
				log.info("Device not found in gateway : "+meid);
				niomDbservice.updateOrdersTable(order_id, getOrder.getQuantity(),
						getOrder.getReturn_units(), "NA");
				
				response.put("Status", 0);
				response.put("Msg", "Device not found");
				response.put("Return Time", System.currentTimeMillis());
			}	

		} catch (Exception e) {
			log.error("Exception occured while upgradeDevice - " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured : " + e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
		}
		return response;
	}
}
