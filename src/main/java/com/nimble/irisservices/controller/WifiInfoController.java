package com.nimble.irisservices.controller;

import java.sql.Timestamp;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.ocpsoft.prettytime.PrettyTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dto.BluetoothDeviceList;
import com.nimble.irisservices.dto.JGatewayUserDetails;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.WifiInfo;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IFurBitReportService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IWifiInfoService;

@Controller
public class WifiInfoController {

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IWifiInfoService wifiService;

	@Autowired
	IFurBitReportService iFurBitReportService;

	@Value("${lastrpt_max}")
	private String lastrpt_max;

	@Value("${lastwifirpt_max}")
	private String lastWifirptMax;
	
	@Autowired 
	@Lazy
	IUserServiceV4 userServiceV4;

	Helper _helper = new Helper();

	PrettyTime prettyTime = new PrettyTime();

	private static final Logger log = LogManager.getLogger(WifiInfoController.class);

	@RequestMapping(value = "v3.0/savewifiinfo/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveWifiInfo(@PathVariable String autho, @RequestBody WifiInfo wifi) {
		JResponse response = new JResponse();
		log.info("Entering saveWifiInfo "+autho);
		try {
			String macid=wifi.getMeid().split("_")[1];
			
			try {
				UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
			}catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			
			JGatewayUserDetails gateways = gatewayService.getGatewayByMAC(macid, 0);
			boolean status = false;
			
			try {
				if(gateways != null) {
					String CurrentTime = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT, IrisservicesConstants.UTCFORMAT);
					Timestamp currTimeStamp=IrisservicesUtil.getDateTime_TS(CurrentTime);
					if(wifi.getGatewayid() != gateways.getGatewayid()) {
						response.put("Status", 0);
						response.put("Msg", "Gateway id or Macid mismatch");
						return response;
					}
					wifi.setUserid(gateways.getUserid());
					wifi.setGatewayid(gateways.getGatewayid());
					wifi.setUpdatedOn(currTimeStamp);
					
					wifi.setSsidCategory(wifi.getSsidCategory());
					wifi.setLat(wifi.getLat());
					wifi.setLon(wifi.getLon());
					wifi.setBleVersion(wifi.getBleVersion());
					wifi.setSsidName(wifi.getSsidName());
					wifi.setAddress(wifi.getAddress());
					wifi.setTimezone(wifi.getTimezone());
					
					wifi.setCreatedOn(currTimeStamp);
					
					status = wifiService.saveOrUpdateWifiInfo(wifi);
					
					if(status) {
						response.put("Status", 1);
						response.put("Msg", "Success");						
					}else {
						response.put("Status", 0);
						response.put("Msg", "Could not save wifi info");		
					}
					
					log.info("Exit :: savewifiinfo::::wificontroller::[{}]");
				}else {
					response.put("Status", 0);
					response.put("Msg", "Macid not found");
				}
				
				return response;
			} catch (ConstraintViolationException ex) {
				response.put("Status", 0);
				response.put("Msg", "Wifiinfo already exist");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception : saveWifiInfo : " + ex.getLocalizedMessage());
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "wifiinfo creation failed");
				response.put("Error", e.getLocalizedMessage());
				log.error("Exception : saveWifiInfo : " + e.getLocalizedMessage());
			}
		}
		catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "wifiinfo creation failed");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : saveWifiInfo : " + e.getLocalizedMessage());
		}
		return response;
	}

//	@RequestMapping(value = "v3.0/checkwifistatusv2/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse checkWifiStatusV2(@PathVariable String autho, @RequestParam("gatewayId") String gatewayId,
//			@RequestParam("timezone") String timezone) {
//		JResponse response = new JResponse();
//		log.info("Entering checkWifiStatusV2 : "+autho);
//
//		timezone = timezone.replaceAll("\\s+","");
//		if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
//			timezone = "+" + timezone;
//
//		try {
//			WifiInfo lastwifiinfo = wifiService.isAlreadycontain(Long.parseLong(gatewayId));
//
//			List<FurbitLastGatewayReport> getwayreport = iFurBitReportService.getFurbitLastGatewayReport(gatewayId);
//			List<FurBitReport> furBitReprts = iFurBitReportService.getFurBitlastreport(gatewayId);
//
//			SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
//			SimpleDateFormat formatter1 = new SimpleDateFormat("dd MMM yyyy, hh:mm aa");
//
//			String currenttime =IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT,"GMT" +timezone);
//
//			String outputlastreportdate = "NA";
//			String wifioutputlastreportdate = "NA";
//			String outputLastfurbitreportDate = "NA";
//			String SSID="No WiFi Details found!!";
//			String Password="No WiFi Details found!!";
//
//			boolean wifistatus=false;
//
//			//Getting last wifi info 
//
//			if(lastwifiinfo !=null )
//			{
//				SSID=lastwifiinfo.getSsid();
//
//				Password=lastwifiinfo.getPassword();
//
//				Timestamp ts_wifi = lastwifiinfo.getUpdatedOn();
//
//				Date lastreportdate = new Date(ts_wifi.getTime());
//				String lastdatetime = formatter.format(lastreportdate);
//				String outputreportdate = IrisservicesUtil.getDateime_Timezone(lastdatetime,
//						IrisservicesConstants.UTCTIMEZONE, timezone);
//
//				Date finaldate = formatter.parse(outputreportdate);
//
//				wifioutputlastreportdate = formatter1.format(finaldate);
//
//				Date pre = formatter.parse(outputreportdate);
//				Date curr = formatter.parse(currenttime);
//
//				long diff =  (curr.getTime() - pre.getTime());
//				long limit = (Integer.parseInt(lastWifirptMax) * 60000);
//
//				if (diff < limit && diff >= 0 && lastwifiinfo.isStatus()) {
//					wifistatus=true;
//				}
//
//			}
//
//			//Getting last furbit report
//
//			if (furBitReprts != null && !furBitReprts.isEmpty()) {
//
//				Timestamp ts = furBitReprts.get(0).getEnddatetime();
//				Date lastreportdate = new Date(ts.getTime());
//
//				String lastdatetime = formatter.format(lastreportdate);
//				outputlastreportdate = IrisservicesUtil.getDateime_Timezone(lastdatetime,
//						IrisservicesConstants.UTCFORMAT,timezone );
//
//				Date finaldate = formatter.parse(outputlastreportdate);
//				outputlastreportdate = formatter1.format(finaldate);
//			}
//
//			//Getting last furbit last report
//			String signalstat = "W";
//			double lat =0;
//			double lon =0;
//			if (getwayreport != null && !getwayreport.isEmpty()) {
//
//				Date ldate = getwayreport.get(0).getDate();
//				Time ltime = getwayreport.get(0).getTime();
//
//				String ldatetime = ldate.toString() + " " + ltime.toString();
//				String ltimezone = getwayreport.get(0).getTimezone();
//
//				String lastdatetime = IrisservicesUtil.getDateime_Timezone(ldatetime, ltimezone,
//						timezone);
//
//				String outputwifidate = IrisservicesUtil.getDateime_Timezone(ldatetime, ltimezone, timezone);
//
//				Date pre = formatter.parse(lastdatetime);
//				Date curr = formatter.parse(currenttime);
//				outputLastfurbitreportDate = formatter1.format(formatter.parse(outputwifidate));
//
//				long diff =curr.getTime() - pre.getTime();
//				long limit = (Integer.parseInt(lastrpt_max) * 3600000);
//
//				if (diff < limit && diff >= 0) {
//					wifistatus=true;
//				}
//				
//				if(getwayreport.get(0).getTxnMode()==1)
//					signalstat ="C" ;
//				else
//					signalstat = "W";
//				
//				lat = getwayreport.get(0).getLat();
//				lon = getwayreport.get(0).getLon();
//			}
//
//			if (wifistatus)
//			{
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//				response.put("Lastreportedtime", outputLastfurbitreportDate);
//				response.put("WifiStatus", "WiFi connected");
//				response.put("Signalstatus", wifistatus);
//				response.put("Signal", signalstat);
//				response.put("Ssid", SSID);
//				response.put("Password", Password);
//				response.put("Lastwifireporttime", wifioutputlastreportdate);
//				response.put("Furbitlastreporttime", outputlastreportdate);
//				response.put("lat", lat);
//				response.put("lon", lon);
//			}
//			else if(lastwifiinfo !=null && lastwifiinfo.isStatus()) 
//			{
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//				response.put("Lastreportedtime", outputLastfurbitreportDate);
//				response.put("WifiStatus", "WiFi Not connected");
//				response.put("Signalstatus", wifistatus);
//				response.put("Signal", signalstat);
//				response.put("Ssid", SSID);
//				response.put("Password", Password);
//				response.put("Lastwifireporttime", wifioutputlastreportdate);
//				response.put("Furbitlastreporttime", outputlastreportdate);
//				response.put("lat", lat);
//				response.put("lon", lon);
//			} 
//			else 
//			{
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//				response.put("Lastreportedtime", "NA");
//				response.put("WifiStatus", "WiFi Not connected");
//				response.put("Signalstatus", wifistatus);
//				response.put("Signal", signalstat);
//				response.put("Ssid", SSID);
//				response.put("Password", Password);
//				response.put("Lastwifireporttime", wifioutputlastreportdate);
//				response.put("Furbitlastreporttime", outputlastreportdate);
//				response.put("lat", lat);
//				response.put("lon", lon);
//			}
//
//			log.info("Exit : CheckwifiV2");
//		} catch (Exception e) {
//			log.error("Exception: CheckwifiV2 :"+ e.getLocalizedMessage());
//			response.put("Status", 0);
//			response.put("Msg", "Unexpected Error. Please Contact Support ");
//			response.put("Error", e.getLocalizedMessage());
//			return response;
//		}
//		return response;
//	}
	
//	@RequestMapping(value = "v3.0/getgpsandwifistatus/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getgpsandwifistatus(@PathVariable String autho, @RequestParam("gatewayId") String gatewayId,
//			@RequestParam("timezone") String timezone) {
//		JResponse response = new JResponse();
//		log.info("Entering getgpsandwifistatus : "+autho);
//		UserV4 user =null;
//		try {
//			user = userServiceV4.verifyAuthV4("authkey",autho);
//		}catch(InvalidAuthoException ex) {
//			response.put("Status", 0);
//			response.put("Msg", "invalid authkey");
//			response.put("Error", ex.getLocalizedMessage());
//			log.error("Exception while getting user for auth : "+ex.getLocalizedMessage());
//			return response;
//		}
//		timezone = timezone.replaceAll("\\s+","");
//		if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
//			timezone = "+" + timezone;
//
//		try {
//			JGateway jgateway = gatewayService.getJGateway("id",gatewayId);
//			AssetModel assetModel =null;
//			boolean gps = false;
//			boolean advmode = false;
//			boolean powermode = false;
//			boolean signalstatus=false;
//			String lastwifireporttime = "NA";
//			String furbitLastRptDt = "NA";
//			String lastgpsdatetime = "NA";
//			String SSID="No WiFi Details found!!";
//			String password="No WiFi Details found!!";
//			String timeago = "";
//			String gpstimeago = "";
//			String addr = "";
//			String signal = "w";
//			String wifistatus = "";
//			String zonename = "";
//			String zonedesc = "";
//			double lat =0;
//			double lon =0;
//			int zonelevel = -1;
//			String ble_version="v3";
//			
//			try {
//				assetModel = gatewayService.getAssetModel(jgateway.getModelid());
//			}catch (Exception e) {
//			}
//			
//			if(assetModel != null) {
//				if(assetModel.getIsgps().equalsIgnoreCase("1"))
//					gps = true;
//				else
//					gps = false;
//				
//				advmode = assetModel.isAdvmode();
//				powermode = assetModel.isPowermode();
//			}
//
//
//			List<FurbitLastGatewayReport> getwayreport = iFurBitReportService.getFurbitLastGatewayReport(gatewayId);
//
//			SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
//			SimpleDateFormat formatter1 = new SimpleDateFormat("dd MMM yyyy, hh:mm aa");
//
//			//String currenttime =IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT,"GMT" +timezone);
//			String curUTC = IrisservicesUtil.getCurrentTimeUTC();
//			String currenttime = IrisservicesUtil.getDateime_Timezone(curUTC, IrisservicesConstants.UTCTIMEZONE,timezone);
//			Date curr = formatter.parse(currenttime);
//			WifiInfo wifiinfo = null;
//			int wifirssi = 99;
//			String rssi = "NA";
//			
//			//Getting last furbit last report
//			if (getwayreport != null && !getwayreport.isEmpty()) {
//				
//				wifirssi = getwayreport.get(0).getWifirssi();
//				rssi =getwayreport.get(0).getRssi();
//				Date ldate = getwayreport.get(0).getDate();
//				Time ltime = getwayreport.get(0).getTime();
//
//				String ldatetime = ldate.toString() + " " + ltime.toString();
//
//				String lastdatetime = IrisservicesUtil.getDateime_Timezone(ldatetime, IrisservicesConstants.UTCTIMEZONE,timezone);
//				
//				Date pktDate = formatter.parse(lastdatetime);
//				furbitLastRptDt = formatter1.format(formatter.parse(lastdatetime));
//				timeago = prettyTime.format(new Date(getwayreport.get(0).getDatetime().getTime())).replace("minute", "min");
//				
//				//last valid gps dattime
//				if(getwayreport.get(0).getLastgpsdatetime() != null){
//					Date gpsdate=new Date(getwayreport.get(0).getLastgpsdatetime().getTime());
//					
//					lastgpsdatetime = IrisservicesUtil.getDateime_Timezone(formatter.format(gpsdate), IrisservicesConstants.UTCTIMEZONE,timezone);
//					lastgpsdatetime = formatter1.format(formatter.parse(lastgpsdatetime));
//					gpstimeago = prettyTime.format(new Date(getwayreport.get(0).getLastgpsdatetime().getTime())).replace("minute", "min");
//				}
//				
//				long diff =curr.getTime() - pktDate.getTime();
//				long limit = (Integer.parseInt(lastrpt_max) * 3600000);
//
//				if (diff < limit && diff >= 0) {
//					signalstatus=true;
//				}
//				
//				if(getwayreport.get(0).getTxnMode()==1) {
//					signal ="c" ;
//				}
//				else if(getwayreport.get(0).getTxnMode()==0){
//					signal = "w";
//				}else {
//					signal = "";
//				}
//				
//				lat = getwayreport.get(0).getLastvalidlat();
//				lon = getwayreport.get(0).getLastvalidlon();
//				zonename = getwayreport.get(0).getZonename();
//				zonedesc = getwayreport.get(0).getZonedesc();
//				zonelevel= getwayreport.get(0).getZonelevel();
//				//TODO: needed oly for wifi - txnmode
//				String ssiscat = "SSID_1";
//				
//				if(getwayreport.get(0).getWifissid() == 1)
//					ssiscat = "SSID_1";
//				else if(getwayreport.get(0).getWifissid() == 2)
//					ssiscat = "SSID_2";
//					
//				//Getting last wifi info 
//				wifiinfo = wifiService.isAlreadycontain(Long.parseLong(gatewayId),ssiscat,user.getId());
//			}
//			else {
//				//Getting last wifi info 
//				wifiinfo = wifiService.isAlreadycontain(Long.parseLong(gatewayId));				
//			}
//			
//			if(wifiinfo !=null )
//			{
//				SSID=wifiinfo.getSsid();
//				ble_version = wifiinfo.getBleVersion();
//				password=wifiinfo.getPassword();
//				addr = wifiinfo.getAddress();
//
//				Date lastreportdate = new Date(wifiinfo.getUpdatedOn().getTime());
//				String lastdatetime = formatter.format(lastreportdate);
//				String outputreportdate = IrisservicesUtil.getDateime_Timezone(lastdatetime,IrisservicesConstants.UTCTIMEZONE, timezone);
//
//				Date finaldate = formatter.parse(outputreportdate);
//
//				lastwifireporttime = formatter1.format(finaldate);
//
//				Date pre = formatter.parse(outputreportdate);
//				
//				if(!signalstatus) {
//					long diff =  (curr.getTime() - pre.getTime());
//					long limit = (Integer.parseInt(lastWifirptMax) * 60000);
//	
//					if (diff < limit && diff >= 0 && wifiinfo.isStatus()) {
//						signalstatus = true;
//					}else
//						signalstatus = false;
//				}
//			}			
//
//			if (signalstatus)
//				wifistatus = "WiFi connected";
////			else if(wifiinfo !=null && wifiinfo.isStatus()) 
////				wifistatus = "WiFi connected"; 
//			else 
//				wifistatus = "WiFi not connected";
//			
//			JWifiStatus jwifistatus = new JWifiStatus(gps, advmode, powermode, SSID, password, signal, wifistatus, zonename,
//					lat, lon, furbitLastRptDt, timezone, lastwifireporttime, timeago, signalstatus,addr,zonedesc,zonelevel,
//					lastgpsdatetime,gpstimeago,ble_version);
//						
//			jwifistatus.setWifirssi(wifirssi);			
//			jwifistatus.setRssi(rssi);
//			
//			response.put("Status", 1);
//			response.put("Msg", "Success");
//			response.put("wifiinfo", jwifistatus);
//
//			log.info("Exit : getgpsandwifistatus");
//		} catch (Exception e) {
//			log.error("Exception: getgpsandwifistatus :"+ e.getLocalizedMessage());
//			response.put("Status", 0);
//			response.put("Msg", "Unexpected Error. Please Contact Support ");
//			response.put("Error", e.getLocalizedMessage());
//			return response;
//		}
//		return response;
//	}
	
	@RequestMapping(value = "v3.0/getwifiinfolist/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getWifiInfoList(@PathVariable String autho, @RequestParam(value="gatewayid", defaultValue = "", required = false) String gatewayId,
			 @RequestParam("userid") String userId) {
		log.info("Entered :: getWifiList  :: "+autho);
		JResponse response = new JResponse();
		UserV4 user;
		long gatewayID = 0;
		try {
			if(!gatewayId.isEmpty()) {
				gatewayID = Long.valueOf(gatewayId);
			}
			long userID = Long.valueOf(userId);
			try {
			user = userServiceV4.verifyAuthV4("authkey", autho);
			
			if(user.getId() != userID) {
				response.put("Status", 0);
				response.put("Msg", "User id mismatch");
				log.info("User if for authey and user id parameter mismatch! ");
				return response;
			}
			
			}catch(InvalidAuthoException e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				log.error("Invalid authkey : "+autho);
				return response;
			}
			List<BluetoothDeviceList> ble_device_list = wifiService.getWifiList(gatewayID,userID);
			
			if(ble_device_list != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("bledevicelist", ble_device_list);
				log.info("wifi info list found for the gateway id : "+gatewayId+" and user id : "+userId);
			}else {
				response.put("Status", 0);
				response.put("Msg", "Could not get wifi info list!");
				log.info("No wifi info list found for the gateway id : "+gatewayId+" and user id : "+userId );
			}
			log.info("Exit :: getWifiList ::");
		}catch(Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception occured ");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception occured at getWifiList  :", e.getLocalizedMessage());
			return response;
		}
		return response;
	}
	
//	@RequestMapping(value = "v3.0/getadventurereport/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getAdventureReport(@PathVariable String autho, @RequestParam("gatewayId") long gatewayId,
//			@RequestParam("timezone") String timezone) {
//		JResponse response = new JResponse();
//		log.info("getGPSInfo : "+autho);
//
//		try {
//		
//			SimpleDateFormat dtf = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
//			
//			JGateway gateway = gatewayService.getJGateway("id", String.valueOf(gatewayId));
//			
//			AdventureModeStatus advMode = iFurBitReportService.getAdventureModeStatus(gateway.getId());
//			
//			
//			if(advMode != null) {
//				List<JAdventureReport> adventurereportList = new ArrayList<JAdventureReport>();
//				String timeago = "";
//				if(advMode.getCurrent_mode().equalsIgnoreCase("on")) {
//					List<AdventureReport> reportList = iFurBitReportService.getAdventureReport(gatewayId);
//
//					if(!reportList.isEmpty())
//					{
//						int size = reportList.size();
//		
//						for(int i=0; i<size; i++) {
//							
//							String startdate = IrisservicesUtil.getDateime_Timezone(
//									dtf.format(reportList.get(i).getStartdatetime()), IrisservicesConstants.UTCTIMEZONE,
//									timezone);
//		
//							String enddate = IrisservicesUtil.getDateime_Timezone(
//									dtf.format(reportList.get(i).getEnddatetime()), IrisservicesConstants.UTCTIMEZONE,
//									timezone);
//							
//							Date pktDate = dtf.parse(startdate);
//		
//							timeago = prettyTime.format(new Date(reportList.get(i).getStartdatetime().getTime())).replace("minute", "min");
//							
//							JAdventureReport rpt = new JAdventureReport(startdate, enddate, reportList.get(i).getLat(),
//									reportList.get(i).getLon(), gatewayId,gateway.getName(),timeago);
//							
//							adventurereportList.add(rpt);
//						}
//						response.put("Status", 1);
//						response.put("Msg", "Success");
//						response.put("adventurereport", adventurereportList);
//						
//					}else {
//						List<FurbitLastGatewayReport> getwayreport = iFurBitReportService.getFurbitLastGatewayReport(String.valueOf(gatewayId));
//
//						//Getting last furbit last report
//						if (getwayreport != null && !getwayreport.isEmpty()) {
//							String startdate = IrisservicesUtil.getDateime_Timezone(
//									dtf.format(getwayreport.get(0).getDatetime()), IrisservicesConstants.UTCTIMEZONE,
//									timezone);
//							Date pktDate = dtf.parse(startdate);
//
//							timeago = prettyTime.format(new Date(getwayreport.get(0).getDatetime().getTime())).replace("minute", "min");
//							
//							JAdventureReport rpt = new JAdventureReport(startdate, startdate, getwayreport.get(0).getLat(),
//									getwayreport.get(0).getLon(), gatewayId,gateway.getName(),timeago);
//							adventurereportList.add(rpt);
//							response.put("Status", 2);
//							response.put("Msg", "Currently Outdoor Mode data not found. This is your pet's last location.");
//							response.put("adventurereport", adventurereportList);
//						}else
//						{
//							response.put("Status", 3);
//							response.put("Msg", "No data found");
//						}										
//					}
//				}else {
//					List<FurbitLastGatewayReport> getwayreport = iFurBitReportService.getFurbitLastGatewayReport(String.valueOf(gatewayId));
//
//					//Getting last furbit last report
//					if (getwayreport != null && !getwayreport.isEmpty()) {
//						String startdate = IrisservicesUtil.getDateime_Timezone(
//								dtf.format(getwayreport.get(0).getDatetime()), IrisservicesConstants.UTCTIMEZONE,
//								timezone);
//						Date pktDate = dtf.parse(startdate);
//
//						timeago = prettyTime.format(new Date(getwayreport.get(0).getDatetime().getTime())).replace("minute", "min");
//						
//						JAdventureReport rpt = new JAdventureReport(startdate, startdate, getwayreport.get(0).getLat(),
//								getwayreport.get(0).getLon(), gatewayId,gateway.getName(),timeago);
//						adventurereportList.add(rpt);
//						response.put("Status", 2);
//						response.put("Msg", "Currently Outdoor Mode Deactivated. This is your pet's last location.");
//						response.put("adventurereport", adventurereportList);
//					}else {
//						response.put("Status", 3);
//						response.put("Msg", "No data found.");
//					}
//				}
//			}else {
//				AssetModel assetModel =null;
//				try {
//					assetModel = gatewayService.getAssetModel(gateway.getModelid());
//				}catch (Exception e) {
//				}
//				String msg = "";
//				if(assetModel != null) {
//					if(assetModel.isAdvmode()==false )
//						msg = "This feature not applicable for your monitor";
//					else
//						msg = "Currently Outdoor Mode Deactivated. Please Activate to view live location.";
//				}	
//				response.put("Status", 3);
//				response.put("Msg", msg);
//			}
//			
//			log.info("Exit : getGPSInfo");
//		} catch (Exception e) {
//			log.error("Exception: getGPSInfo :"+ e.getLocalizedMessage());
//			response.put("Status", 0);
//			response.put("Msg", "Unexpected Error. Please Contact Support ");
//			response.put("Error", e.getLocalizedMessage());
//			return response;
//		}
//		return response;
//	}

}
