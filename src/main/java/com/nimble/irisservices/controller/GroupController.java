package com.nimble.irisservices.controller;

import java.util.List;

import javax.validation.Valid;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JGroups;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.AssetGroup;
import com.nimble.irisservices.entity.Group;
import com.nimble.irisservices.entity.Groups;
import com.nimble.irisservices.entity.SubGroup;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.exception.InvalidGroupIdException;
import com.nimble.irisservices.service.IGroupServices;
import com.nimble.irisservices.service.IUserService;

@Controller
public class GroupController {
	
	private static final Logger log = LogManager.getLogger(GroupController.class);
	
	@Autowired
	@Lazy
	IUserService userService;
	
	@Autowired
	@Lazy
	IGroupServices groupservices;

	
	//========get assetgroup================
	@RequestMapping(value="v3.0/assetgroup/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getAssetGroupById(@PathVariable String autho, @RequestParam("assetgroupid") String assetgroupid)
	{
		JResponse response = new JResponse();
		
		
		try {
			User user = userService.verifyAuthKey(autho);
			
			List<AssetGroup> users = groupservices.getAssetGroup(assetgroupid,user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("assetgroup", users);
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		
		return response;
		
	}
	
	//========get group================
	@RequestMapping(value="v3.0/group/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getGroupById(@PathVariable String autho, @RequestParam("groupid") String groupid)
	{
		JResponse response = new JResponse();
		
		
		try {
			User user = userService.verifyAuthKey(autho);
			
			List<Groups> users = groupservices.getGroup(groupid,user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("groups", users);
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		
		return response;
		
	}
	
	// Used in web
	//========get subgroup================
	@RequestMapping(value="v3.0/subgroup/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getSubGroupById(@PathVariable String autho, @RequestParam("groupid") String groupid,@RequestParam("subgroupid") String subgroupid)
	{
		JResponse response = new JResponse();
		
		
		try {
			User user = userService.verifyAuthKey(autho);
			
			List<SubGroup> users = groupservices.getSubGroup(groupid,subgroupid,user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("subgroups", users);
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		return response;
		
	}
	
	//========save/update assetgroup================
	@RequestMapping(value="v3.0/assetgroup/{autho}",method = RequestMethod.POST, headers="Accept=application/json")
	@ResponseBody
	public JResponse saveorUpdateAssetGroup(@PathVariable String autho, @ModelAttribute AssetGroup assetGroup)
	{
		JResponse response = new JResponse();
		
		
		try {
			User user = userService.verifyAuthKey(autho);
			
			boolean result = groupservices.saveOrUpdateAssetGroup(assetGroup, user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg","Success");
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		} catch(Exception e)
		{
			log.error("saveorUpdateAssetGroup::::"+e.getMessage());
			response.put("Status", 0);
			response.put("Msg","assetgroup name cannot be empty , and also be unique ");
			return response;
		}
		
		return response;
		
	}
	
	//========save/update group================
	@RequestMapping(value="v3.0/groupold/{autho}",method = RequestMethod.POST,headers="Accept=application/json")
	@ResponseBody
	public JResponse saveorUpdateGroupOld(@PathVariable String autho, @ModelAttribute Group group)
	{
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			
			boolean result = groupservices.saveOrUpdateGroup(group,user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg","Success");
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		} catch(Exception e)
		{
			log.error("saveorUpdateGroupOld::::"+e.getMessage());
			response.put("Status", 0);
			response.put("Msg","group name cannot be empty , and also be unique ");
			return response;
		}
		
		return response;
		
	}
	
	// Used in web
	//========save/update subgroup================
	@RequestMapping(value="v3.0/subgroup/{autho}",method = RequestMethod.POST,headers="Accept=application/json" )
	@ResponseBody
	public JResponse saveorUpdateSubGroup(@PathVariable String autho, @ModelAttribute SubGroup subGroup) 
	{
		JResponse response = new JResponse();
		
		
		try {
			User user = userService.verifyAuthKey(autho);
			
			boolean result = groupservices.saveOrUpdateSubGroup(subGroup, user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg","Success");
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		catch (InvalidGroupIdException e) {
			response.put("Status", 0);
			response.put("Msg","Invalid groupid");
			return response;

		}catch(Exception e)
		{
			log.error("saveorUpdateSubGroup::::"+e.getMessage());
			response.put("Status", 0);
			response.put("Msg","subgroup name cannot be empty , and also be unique for a group");
			return response;
		}
		return response;
		
	}
	
	// Used in web
	//========get group userId================
	@RequestMapping(value="v3.0/usergroup/{autho}/{levelid}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getGroupByUserid(@PathVariable String autho, @PathVariable String levelid, 
			@RequestParam("groupid") String groupid, @RequestParam("topgroupid") String topgroupid)
	{
		JResponse response = new JResponse();


		try {
			User user = userService.verifyAuthKey(autho);
			
			List<JGroups> jgroups =  groupservices.getUserJGroup(groupid, topgroupid, levelid, user.getId());

			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("userGroups", jgroups);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		return response;

	}

	// Used in web
	//========save/update groups================
	@RequestMapping(value="v3.0/group/{autho}",method = RequestMethod.POST,headers="Accept=application/json")
	@ResponseBody
	public JResponse saveorUpdateGroup(@PathVariable String autho, @ModelAttribute @Valid Groups groups, BindingResult res)
	{
		JResponse response = new JResponse();
		try {
			/*Create or Update Validation*/
			if(res.hasErrors()) {
				response.put("Status",0);				
				if(res.getFieldError("name") != null)
					response.put("Msg",res.getFieldError("name").getField()+" "+res.getFieldError("name").getDefaultMessage());
				else if(res.getFieldError("id") != null)
					response.put("Msg","id should not be empty");
				return response;
			}
			
			User user = userService.verifyAuthKey(autho);
			boolean result = groupservices.saveOrUpdateGroups(groups,user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg","Success");

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		} catch(Exception e)
		{
			log.error("saveorUpdateGroup::::"+e.getMessage());
			response.put("Status", 0);
			response.put("Msg","group name cannot be empty , and also be unique ");
			return response;
		}

		return response;

	}

	// Used in web
	//========get groups================
	@RequestMapping(value="v3.0/group/{autho}/{levelid}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getGroupById(@PathVariable String autho, @PathVariable String levelid , @RequestParam("groupid") String groupid,
			@RequestParam("topgroupid") String topgroupid)
	{
		JResponse response = new JResponse();


		try {
			User user = userService.verifyAuthKey(autho);

			List<JGroups> jgroups = groupservices.getGroups(groupid, topgroupid, levelid, user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("groups", jgroups);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}

		return response;

	}

}
