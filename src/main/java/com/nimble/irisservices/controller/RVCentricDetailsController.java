package com.nimble.irisservices.controller;

import org.springframework.stereotype.Controller;

@Controller
public class RVCentricDetailsController {
//
//	private static final Logger log = LogManager.getLogger(RVCentricDetailsController.class);
//
//	@Autowired
//	@Lazy
//	IRVCentricDetailsService rvCentService;
//
//	@Autowired
//	IUserServiceV4 userServiceV4;
//
//	@Autowired
//	Helper _helper = new Helper();
//
//	@Autowired
//	@Lazy
//	private IAsyncService async;
//
//	// Mobile App API - Kalai -
//	@RequestMapping(value = "v4.0/listrvdetails/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse listRVDetails(@PathVariable String auth, @RequestParam("os") String os,
//			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
//			) {
//		log.error(" Entered listRVBlogsAndTips :" + auth);
//		JResponse response = new JResponse();
//		try {
//
//			List<RVCentricDetails> rvList = rvCentService.listRVCentricDetailsList();
//			if (rvList != null) {
//				List<JRVCentricDetails> blogsList = new ArrayList<JRVCentricDetails>();
//				List<JRVCentricDetails> tipsList = new ArrayList<JRVCentricDetails>();
//				List<JRVCentricDetails> storyList = new ArrayList<JRVCentricDetails>();
//				String mediapath = "NA";
//				for (RVCentricDetails rvObj : rvList) {
//
//					if (os.trim().equalsIgnoreCase("andorid"))
//						mediapath = rvObj.getAndroid_mediapath();
//					else
//						mediapath = rvObj.getIos_mediapath();
//
//					JRVCentricDetails jrvObj = new JRVCentricDetails(rvObj.getTitle(), mediapath,
//							rvObj.getDescription(), rvObj.getUrl());
//
//					if (rvObj.getCategory().equalsIgnoreCase("blogs")) {
//						blogsList.add(jrvObj);
//					} else if (rvObj.getCategory().equalsIgnoreCase("tips")) {
//						tipsList.add(jrvObj);
//					} else if (rvObj.getCategory().equalsIgnoreCase("stories")) {
//						storyList.add(jrvObj);
//					}
//				}
//				response.put("Status", 1);
//				response.put("Msg", "success");
//				response.put("blogdesc", "Top RVing Destinations this week");
//				response.put("blogsList", blogsList);
//				response.put("tipsdesc", "RVing Tips");
//				response.put("tipsList", tipsList);
//				response.put("storydesc", "Stories");
//				response.put("storyList", storyList);
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Please try after some time!");
//			}
//
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("listrvblogsandtips : " + e.getLocalizedMessage());
//		}
//
//		return response;
//	}
//
//	// Used in web
//	// Web API - kalai
//	@RequestMapping(value = "v4.0/listallrvdetails/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse listAllRVDetails(@PathVariable String auth, @RequestParam("os") String os) {
//		log.error("Entered listAllRVDetails :" + auth);
//		JResponse response = new JResponse();
//		try {
//
//			List<RVCentricDetails> rvList = rvCentService.listAllRVCentricDetailsList();
//
//			if (rvList != null) {
//				response.put("Status", 1);
//				response.put("Msg", "success");
//				response.put("rvList", rvList);
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Please try after some time!");
//			}
//
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("listAllRVDetails : " + e.getLocalizedMessage());
//		}
//
//		return response;
//	}
//
//	// Used in web
//	// Web API - kalai
//	@RequestMapping(value = "v4.0/savervdetails/{auth}", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse saveRVDetails(@PathVariable String auth, @RequestParam("os") String os,
//			@RequestBody RVCentricDetails rvBlogsTips) {
//		log.info("Entered saveRVDetails: " + auth);
//		JResponse response = new JResponse();
//		boolean status = false;
//		try {
//			status = rvCentService.saveOrUpdateRVCentricDetails(rvBlogsTips);
//
//			if (status == true) {
//				response.put("Msg", "Success");
//				response.put("Status", 1);
//			} else {
//				response.put("Msg", "Please try after some time!");
//				response.put("Status", 0);
//			}
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("saveRVDetails : " + e.getLocalizedMessage());
//		}
//
//		return response;
//	}
//
//	// Used in web
//	// Web API - kalai
//	@RequestMapping(value = "v4.0/getrvdetails/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getRVDetails(@PathVariable String auth, @RequestParam("os") String os, @RequestParam long rvId) {
//		JResponse response = new JResponse();
//		try {
//			log.info("Entered getRVDetails :" + auth);
//
//			RVCentricDetails rvObj = rvCentService.getRVCentricDetailsById(rvId);
//
//			response.put("Status", 1);
//			response.put("Msg", "success");
//			response.put("rvObj", rvObj);
//
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Please try after some time!");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("getRVDetails : " + e.getLocalizedMessage());
//		}
//		return response;
//	}
//
//	// Web API - kalai
//	@RequestMapping(value = "v4.0/listallrvchecklist/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse listAllRVChecklist(@PathVariable String auth, @RequestParam("os") String os) {
//		log.error("Entered listAllRVChecklist :" + auth);
//		JResponse response = new JResponse();
//		try {
//
//			List<RVChecklist> rvList = rvCentService.listAllRVCheckList();
//			if (rvList != null) {
//				response.put("Status", 1);
//				response.put("Msg", "success");
//				response.put("rvCheckList", rvList);
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Please try after some time!");
//			}
//
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("listAllRVDetails : " + e.getLocalizedMessage());
//		}
//
//		return response;
//	}
//
//	// Mobile API - kalai
//	@RequestMapping(value = "v4.0/savervchecklist/{auth}", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse saveRVCheckList(@PathVariable String auth, @RequestParam("os") String os,
//			@RequestBody JRVChecklist jrvChecklist,
//			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
//			) {
//		log.info("Entered saveRVCheckList: " + auth);
//		JResponse response = new JResponse();
//		int status = 0;
//		try {
//			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);
//
//			RVChecklist rvchecklistObj = new RVChecklist();
//
//			rvchecklistObj.setCategory(jrvChecklist.getCategory());
//			rvchecklistObj.setListtype(jrvChecklist.getListtype());
//			rvchecklistObj.setListname(jrvChecklist.getListname());
//
//			if (jrvChecklist.getCategory().equalsIgnoreCase("UserSpecific"))
//				rvchecklistObj.setUser_id(usr.getId());
//
//			String curUTC = IrisservicesUtil.getCurrentTimeUTC();
//			rvchecklistObj.setCreatedon(curUTC);
//
//			status = rvCentService.saveOrUpdateRVChecklist(rvchecklistObj);
//
//			log.info("saveOrUpdateRVChecklist Status : " + status);
//
//			if (status == 1) {
//				response.put("Msg", "Success");
//				response.put("Status", 1);
//			} else if (status == 2 || status == 3) {
//				response.put("Msg", "Item already exists.");
//				response.put("Status", 0);
//			} else {
//				response.put("Msg", "Please try after some time!");
//				response.put("Status", 0);
//			}
//		} catch (DataIntegrityViolationException e) {
//			response.put("Status", 0);
//			response.put("Msg", "Item already exists.");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("saveRVCheckList : " + e.getLocalizedMessage());
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Please try after some time!");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("saveRVCheckList : " + e.getLocalizedMessage());
//		}
//
//		return response;
//	}
//
//	// Mobile App API - Kalai -
//	@RequestMapping(value = "v4.0/listrvchecklist/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse listRVChecklist(@PathVariable String auth, @RequestParam("os") String os,
//			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
//			) {
//		log.error(" Entered listRVChecklist :" + auth);
//		JResponse response = new JResponse();
//		try {
//			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);
//
//			List<JRVChecklist> rvList = rvCentService.listRVCheckList(usr.getId());
//			List<RVChecklistType> chkTypeList = rvCentService.listRVChecklistType();
//			if (rvList != null) {
//
//				response.put("Status", 1);
//				response.put("all_flag", true);
//				response.put("all_label", "Selected Items");
//				response.put("Msg", "success");
//				response.put("checkList", rvList);
//				response.put("chkTypeList", chkTypeList);
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Please try after some time!");
//			}
//
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("listrvchecklist : " + e.getLocalizedMessage());
//		}
//
//		return response;
//	}
//
//	// Web API - kalai
//	@RequestMapping(value = "v4.0/updateuserchecklist/{auth}", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse updateUserChecklist(@PathVariable String auth, @RequestParam("os") String os,
//			@RequestBody ArrayList<Long> chlistIds) {
//		log.info("Entered saveRVCheckList: " + auth);
//		JResponse response = new JResponse();
//		boolean status = false;
//		try {
//			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);
//
//			status = rvCentService.updateRVCheckList(usr.getId(), chlistIds);
//
//			if (status == true) {
//				response.put("Msg", "Success");
//				response.put("Status", 1);
//			} else {
//				response.put("Msg", "Please try after some time!");
//				response.put("Status", 0);
//			}
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("saveRVCheckList : " + e.getLocalizedMessage());
//		}
//
//		return response;
//	}
//
//	// mobile api
//	@RequestMapping(value = "v4.0/deleteuserchecklist/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
//	public @ResponseBody JResponse deleteUserCheckList(@PathVariable String autho, @RequestParam("os") String os,
//			@RequestBody ArrayList<Long> chlistIds) {
//		JResponse response = new JResponse();
//		try {
//			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);
//			boolean stat = rvCentService.deleteUserCheckList(usr.getId(), chlistIds);
//
//			response.put("Status", 1);
//			response.put("Msg", "Success");
//			return response;
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Pls try after some time!");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("saveRVCheckList : " + e.getLocalizedMessage());
//		}
//		return response;
//	}
//
//	//mobile api
//	@RequestMapping(value = "v4.0/listuserbadges/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse listUserBadges(@PathVariable String auth, @RequestParam("os") String os,
//			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
//			) {
//		log.error(" Entered listUserBadges :" + auth);
//		JResponse response = new JResponse();
//		try {
//			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);
//
//			List<JBadges> badgeList = rvCentService.listBadges(usr.getId());
//			if (badgeList != null) {
//
//				response.put("Status", 1);
//				response.put("Msg", "success");
//				response.put("badgeList", badgeList);
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Please try after some time!");
//			}
//
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("listrvchecklist : " + e.getLocalizedMessage());
//		}
//
//		return response;
//	}
//
//	// Mobile API
//	@RequestMapping(value = "v4.0/getuserbadges/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getUserBadges(@PathVariable String auth, @RequestParam("os") String os,
//			@RequestParam(value = "api_ver", defaultValue = "v1", required = false) String api_ver) {
//		log.error(" Entered getUserBadges :" + auth);
//		JResponse response = new JResponse();
//		try {
//			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);
//
//			ArrayList<JUserBadges> userbadge_list = rvCentService.getUserBadges(usr.getId(),api_ver);
//			if (!userbadge_list.isEmpty()) {
//				long cur_badge = userbadge_list.get(0).getBadge_id();
//				for (JUserBadges bdObj : userbadge_list) {
//					for (JUserBadgeStage st : bdObj.getStageList()) {
//						if (st.isStage_status()) {
//							cur_badge = bdObj.getBadge_id();
//						}
//					}
//				}
//				response.put("Status", 1);
//				response.put("Msg", "success");
//				response.put("cur_badge", cur_badge);
//				response.put("userbadge_list", userbadge_list);
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Please try after some time!");
//			}
//
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("getUserBadges : " + e.getLocalizedMessage());
//		}
//
//		return response;
//	}
//
//	//  Mobile API - Get user lifetimetrip - Savitha.
//	@RequestMapping(value = "v4.0/getuserlifetimetrip/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getUserLifetimeTrip(@PathVariable String auth, @RequestParam("os") String os,
//			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
//			) {
//		log.info(" Entered getUserLifetimeTrip :" + auth);
//		JResponse response = new JResponse();
//		try {
//			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);
//			JUserLifetimeTrip userTripdata = rvCentService.getUserLifetimeTrip(usr.getId());
//			ArrayList<RVAnswer> ansList = rvCentService.listRVAnswerByQuest(2L);
//			ArrayList<JRVAnswer> vehicle_type_list = new ArrayList<JRVAnswer>();
//
//			for (int i = 0; i < ansList.size(); i++) {
//				long ans_id = ansList.get(i).getId();
//				String ans_value = ansList.get(i).getAns_value();
//				JRVAnswer ansObj = new JRVAnswer(ans_id, ans_value);
//
//				vehicle_type_list.add(ansObj);
//			}
//
//			response.put("Status", 1);
//			response.put("Msg", "success");
//			response.put("user_lifetime_trip", userTripdata);
//			response.put("vehicle_type_list", vehicle_type_list);
//			response.put("member_cnt", 10);
//			response.put("pet_cnt", 5);
//
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("getUserLifetimeTrip : " + e.getLocalizedMessage());
//		}
//
//		return response;
//	}
//
//	// Mobile API Save user trip - Savitha.
//	@RequestMapping(value = "v4.0/saveusertriphistory/{auth}", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse saveUserTripHistory(@PathVariable String auth, @RequestParam("os") String os,
//			@RequestBody RvTripHistory rvTripHistory,
//			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
//			) {
//		log.info(" Entered saveUserTripHistory :" + auth);
//		JResponse response = new JResponse();
//
//		try {
//			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);
//
//			rvTripHistory.setUser_id(usr.getId());
//			rvTripHistory.setStart_time(_helper.getCurrentTimeinUTC());
//			rvTripHistory.setCreatedon(_helper.getCurrentTimeinUTC());
//			rvTripHistory.setShortFromAdd("NA");
//			rvTripHistory.setShortToAdd("NA");
//			rvTripHistory.setImage_path("NA");
//
//			RvTripHistory rvTrip = rvCentService.saveUserTripHistory(rvTripHistory);
////			JUserLifetimeTrip userTripdata = rvCentService.getUserLifetimeTrip(usr.getId());
//
//			if (rvTrip.getId() != 0) {
//				async.updateMapboxImage(rvTrip);
//
//				response.put("Status", 1);
//				response.put("Msg", "Safe Travels!");
//				response.put("rvTrip", rvTrip);
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Failed to create user trip history!!!");
//			}
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("saveUserTripHistory : " + e.getLocalizedMessage());
//		}
//		return response;
//	}
//
//	//  Mobile API - End user trip - Savitha.
//	@RequestMapping(value = "v4.0/endusertrip/{auth}", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse endUserTrip(@PathVariable String auth, @RequestParam("os") String os,
//			@RequestParam(value = "trip_id", required = true) String trip_id, @RequestParam(value = "plan_id",  defaultValue = "NA",required = false) String plan_id,
//			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
//			) {
//		log.info(" Entered saveUserTripHistory :" + auth);
//		JResponse response = new JResponse();
//
//		try {
//			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);
//			boolean isSuccess = rvCentService.endUserTrip(usr.getId(), trip_id, plan_id);
//
//			if (isSuccess) {
//				response.put("Status", 1);
//				response.put("Msg", "Hope you had a great time! ");
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Failed to update trip summary!!!");
//			}
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("endUserTrip : " + e.getLocalizedMessage());
//		}
//		return response;
//	}
//
}
