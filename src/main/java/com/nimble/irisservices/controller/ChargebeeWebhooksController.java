package com.nimble.irisservices.controller;

import java.io.BufferedReader;
import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.nimble.irisservices.Util.SecretManagerService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.amazonaws.services.sqs.model.SendMessageResult;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.ICbWebhook;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IExternalConfigService;

@Controller
public class ChargebeeWebhooksController {

	private static final Logger log = LogManager.getLogger(ChargebeeWebhooksController.class);

	@Autowired
	private SecretManagerService secretManagerService;

	@Value("${aws_sqs_secret_name}")
	private String SQS_SECRET_NAME;

	@Value("${iris.services.amazonSQS.microserviceQueue.url}")
	private String amazonSQS_microserviceQueue_url;

	@Value("${microservice_url}")
	private String microservice_url;
	
	@Value("${microservice_api_call}")
	private boolean microserviceApiCall;

	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	ICbWebhook cbWebhook;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Autowired
	IExternalConfigService externalConfigService;

	@Autowired
	Helper _helper = new Helper();
	
	@Value("${omitplan}")
	private String omitplan;
	
	@Value("${vpmplan}")
	private String vpmplan;
	
	@Value("${addonplan}")
	private String addonplan;

	@RequestMapping(value = "/v1.0/waggle/chargebeewebhooks", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public String checkChargebeewebhooks(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		JSONObject res = null;

		Boolean validToActivate = false;
		Boolean updateVpm = false;

		try {

			String mailPacket = "";

			String cbId = "";
			String cbEmail = "";

			BufferedReader rd = request.getReader();
			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}

			rd.close();
			log.info("Activate response : " + result);

			res = new JSONObject(result.toString());

			JSONObject content = res.getJSONObject("content");

			if(!content.has("invoice") && !content.getJSONObject("subscription").getString("status").equalsIgnoreCase("in_trial") ){
				return "success";
			}
			
			cbId = content.getJSONObject("customer").getString("id");

			cbEmail = content.getJSONObject("customer").getString("email");

			String event_type = res.getString("event_type");

			String event_id = res.getString("id");

			if (cbWebhook.webHookStatusIsAvailable(event_id, event_type)) {
				log.error("event_id and event_type Already available in cbWebhook history Table.: ");
				return "";
			}

//			String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);

//			String vpmplan = _helper.getExternalConfigValue("vpmplan", externalConfigService);

			if ((!vpmplan.contains(content.getJSONObject("subscription").getString("plan_id")) &&
				!omitplan.contains(content.getJSONObject("subscription").getString("plan_id")) &&
				!addonplan.contains(content.getJSONObject("subscription").getString("plan_id")))
				&& (content.getJSONObject("subscription").getString("status").equalsIgnoreCase("active") || content
							.getJSONObject("subscription").getString("status").equalsIgnoreCase("in_trial"))) {
				if (content.getJSONObject("subscription").getBoolean("has_scheduled_changes") == false
						&& content.getJSONObject("subscription").has("addons")) {
					JSONArray addons = content.getJSONObject("subscription").getJSONArray("addons");

					for (int i = 0; i < addons.length(); i++) {
						JSONObject addon = (JSONObject) addons.get(i);

						if (addon.getString("id").equalsIgnoreCase("setup_charges"))
							validToActivate = true;
					}
				}			

			}else if (vpmplan.contains(content.getJSONObject("subscription").getString("plan_id"))) {
				updateVpm = true;
			} 
			
			if (validToActivate || updateVpm) {

				boolean Status = cbWebhook.saveWebHookStatus(event_id, event_type);

				log.info("Update Data Base Info : " + Status);
			}

			if (updateVpm) {
				int vpm_txn_cnt = crService
						.getVPMPlanTxnCount(content.getJSONObject("subscription").getString("plan_id"));
				String urlParams = "";
				String msurl = microservice_url + "/v3.0/updatevpm/?cbid=" + cbId + "&cbemail=" + cbEmail + "&eventid="
						+ event_id + "&eventname=" + event_type + "&vpmlimit=" + vpm_txn_cnt;
				log.info("update-vpm :" + msurl);

				async.asynPostRequest(msurl, urlParams);
			}

			if (validToActivate) {

				try {

					if (microserviceApiCall) {
						String urlParams = "";
						String msurl = microservice_url + "/v3.0/activateverizon/?cbid=" + cbId + "&cbemail=" + cbEmail
								+ "&eventid=" + event_id + "&eventname=" + event_type;
						log.info("activateverizon :" + msurl);

						async.asynPostRequest(msurl, urlParams);
					} else {

						String SQS_ACCESS_KEY = secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_access_key");
						String SQS_SECRET_KEY = secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_secret_key");
						AWSCredentials credentials = new BasicAWSCredentials(SQS_ACCESS_KEY, SQS_SECRET_KEY);
						AWSStaticCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(
								credentials);
						AmazonSQS sqs = AmazonSQSClientBuilder.standard().withCredentials(credentialsProvider)
								.withRegion(Regions.US_WEST_2).build();

						mailPacket = "nimble|waggle|chargebeewebhooks|" + event_type + "|" + event_id + "|" + cbId + "|"
								+ cbEmail + "#";

						log.info("sendEmailDataToQueue - Packet ::: " + mailPacket);

						SendMessageRequest sendMessageRequest = new SendMessageRequest(amazonSQS_microserviceQueue_url,
								mailPacket);
						SendMessageResult sendMessageResult = sqs.sendMessage(sendMessageRequest);
						String sequenceNumber = sendMessageResult.getSequenceNumber();
						String messageId = sendMessageResult.getMessageId();

						log.info("SendMessage succeed with messageId " + messageId + ",sequence number "
								+ sequenceNumber);
						log.info(result.toString());

					}

				} catch (Exception e) {
					log.error("Exception - checkChargebeewebhooks : " + e.getLocalizedMessage());
					return "Exception - checkChargebeewebhooks :\n" + e.getLocalizedMessage();
				}
			} else {
				return "Normal Subscription changes";
			}
		} catch (Exception e) {
			log.error("Exception - checkChargebeewebhooks : " + e.getLocalizedMessage());
			return "Exception - checkChargebeewebhooks :\n" + e.getLocalizedMessage();
		}

		return "Success";
	}

}
