package com.nimble.irisservices.controller;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Iterator;
import java.util.Properties;
import java.util.Set;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.SimReactivationHistory;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.helper.Email;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.helper.Thinkspace;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IVerizonService;

@Controller
public class VerizonWebhookController extends HttpServlet {
	private static final Logger log = LogManager.getLogger(VerizonWebhookController.class);

	@Autowired
	Email email_helper;// = new Email();

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Value("${cust_name}")
	private String cust_name;

	@Value("${acc_name}")
	private String acc_name;

	@Value("${plan}")
	private String plan;

	@Value("${retrycount}")
	private int deafaultRetryCount;
	
	@Value("${schedule_url}")
	private String scheduleUrl;

	@Value("${enable_sku_based_vsim_activation}")
	private boolean enableSkuBasedActivation;

	@Value("${retry_remainder_enable}")
	private boolean enableRemainderRetry;
	
	@Value("${nimbleservices.url}")
	private String nimbleservicesUrl;

	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	@Autowired
	@Lazy
	private IVerizonService verizonService;

	@Autowired
	Thinkspace thinkSpace;

	@Autowired
	Helper _helper;

	IrisservicesUtil irisUtil;
	
	@Autowired
	IAsyncService asyncs;

	@ResponseBody
	@RequestMapping(value = "/checkverizonservice", headers = "Accept=application/json")
	public JResponse checkVerizonService(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		JResponse res = new JResponse();
		log.info("checkVerizonService");
		String status = "";
		BufferedReader rd = request.getReader();
		StringBuffer result = new StringBuffer();
		String line = "";

		while ((line = rd.readLine()) != null) {

			result.append(line);
		}
		rd.close();
		log.info("Activate response : " + result);

		String reqId = "";
		String imei = "";
		String state = "";
		String errMsg = "";
		try {
			JSONObject json = new JSONObject(result.toString());
			reqId = json.get("requestId").toString();

			JSONArray devList = json.getJSONArray("deviceIds");

			JSONObject meidList = devList.getJSONObject(0);

			// Retrieve MEID
			if (meidList.get("kind").toString().equalsIgnoreCase("imei")
					|| meidList.get("kind").toString().equalsIgnoreCase("meid")) {
				imei = meidList.get("id").toString();
			} else { // meid contains in 0th index or 1 st index
				meidList = devList.getJSONObject(1);

				if (meidList.get("kind").toString().equalsIgnoreCase("imei")
						|| meidList.get("kind").toString().equalsIgnoreCase("meid")) {
					imei = meidList.get("id").toString();
				}
			}

			// Retrieve SIM Status
			if (json.get("status").toString().equalsIgnoreCase("Success")) {

				if (json.has("deviceResponse")) {
					JSONObject lev1 = json.getJSONObject("deviceResponse");

					if (lev1.has("activateResponse")) {
						JSONObject lev2 = lev1.getJSONObject("activateResponse");
						state = lev2.get("state").toString();

						if (state.equalsIgnoreCase("active")) {

						}

					} else if (lev1.has("restoreResponse")) {
						JSONObject lev3 = lev1.getJSONObject("restoreResponse");
						state = lev3.get("restored").toString();

						if (state.equalsIgnoreCase("true")) {
							state = "restored";
						}
					}
				}
			} else {
				JSONObject fault = json.getJSONObject("faultResponse");
				errMsg = fault.get("faultstring").toString();
				state = "failed";
			}
			log.info("errMsg :" + errMsg);
			log.info("state :" + state);
			// Sending Mail
			res = sendMailNotification(imei, state, errMsg);

		} catch (JSONException e) {
			log.error("checkVerizonService : " + e.getLocalizedMessage());
		}
		return res;
	}

	@ResponseBody
	@RequestMapping(value = "/checkverizonserviceV2", headers = "Accept=application/json")
	public JResponse checkVerizonServiceV2(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		
		log.info("Entered checkVerizonServiceRetry");
		JResponse res = new JResponse();
		String status = "";
		BufferedReader rd = request.getReader();
		StringBuffer result = new StringBuffer();
		String line = "";

		while ((line = rd.readLine()) != null) {
			result.append(line);
		}
		rd.close();
		log.info("Activate response : " + result);

		String skuNumber = null;
		String reqId = "";
		String imei = "";
		String iccid = "";
		String simState = "";
		String errMsg = "";
		String retryResponseStatus = "";
		String scheduledInfo = "";
		String product_category = "NA";

		try {
			JSONObject json = new JSONObject(result.toString());
			reqId = json.get("requestId").toString();

			JSONArray devList = json.getJSONArray("deviceIds");

			for (int i = 0; i < devList.length(); i++) {
				JSONObject meidList = devList.getJSONObject(i);
				if (meidList.get("kind").toString().equalsIgnoreCase("imei")
						|| meidList.get("kind").toString().equalsIgnoreCase("meid")) {
					imei = meidList.get("id").toString();
				}
				if (meidList.get("kind").toString().equalsIgnoreCase("iccid")) {
					iccid = meidList.get("id").toString();
				}
			}

			if (imei.isEmpty()) {
				Inventory invent = niomDbservice.getInventoryByIccid(iccid);
				if(invent != null) {
					imei = invent.getMeid();
					product_category = invent.getProduct_category();
				}
			} else {
				Inventory invent = niomDbservice.getInventoryByMeid(imei);
				if(invent != null) {
					product_category = invent.getProduct_category();
				}
			}
			
			if(product_category.equalsIgnoreCase("tempcube")) {
				log.info("tempCube SIM activation");
				
				String url = nimbleservicesUrl + "/checkverizonservice";
				asyncs.asynPostRequestV2(url, json.toString(), null, "application/json");
				
				res.put("Status", 1);
				res.put("Msg", "Redirecting to nimbleservice");
				return res;
			}
				

			if (enableSkuBasedActivation) {
				AssetModel assetModel = gatewayService.getAssetModelByMeid(imei);
				if (assetModel != null && !assetModel.getSkuNumber().equalsIgnoreCase("NA"))
					skuNumber = assetModel.getSkuNumber();
			}
			if(!product_category.equals("NA")) {
				SimReactivationHistory simReactivationStatus = verizonService.getSimReactivationStatus("request_id", reqId);
				String datetime = irisUtil.getUtcDateTime();
	
				boolean isNewSimActivation = false;
	
				if (simReactivationStatus == null) {
					isNewSimActivation = true;
					simReactivationStatus = new SimReactivationHistory();
					simReactivationStatus.setRequestId(reqId);
					simReactivationStatus.setMeid(imei);
					simReactivationStatus.setCreatedOn(datetime);
					simReactivationStatus.setStatus("inprogress");
					simReactivationStatus.setAction("NA");
				}
				String responseStatus = "";
				String retryLimits = "";
				// Retrieve SIM Status
				if (json.get("status").toString().equalsIgnoreCase("Success")) {
	
					if (json.has("deviceResponse")) {
						JSONObject lev1 = json.getJSONObject("deviceResponse");
	
						if (lev1.has("activateResponse")) {
							JSONObject lev2 = lev1.getJSONObject("activateResponse");
							simState = lev2.get("state").toString();
	
							if (simState.equalsIgnoreCase("active")) {
								simReactivationStatus.setAction("activate");
								simState = "Activated Successfully";
							}
						} else if (lev1.has("restoreResponse")) {
							JSONObject lev3 = lev1.getJSONObject("restoreResponse");
							simState = lev3.get("restored").toString();
							if (simState.equalsIgnoreCase("true")) {
	
								simReactivationStatus.setAction("restore");
								simState = "Reactivated Successfully";
							}
						} else if (lev1.has("suspendResponse")) {
							simReactivationStatus.setAction("suspend");
							simState = "Suspended Successfully";
						} else if (lev1.has("deactivateResponse")) {
							JSONObject lev2 = lev1.getJSONObject("deactivateResponse");
							simState = lev2.get("deactivated").toString();
							if (simState.equalsIgnoreCase("true")) {
								simReactivationStatus.setAction("deactivate");
								simState = "Deactivated Successfully";
							}
						} else {
							simReactivationStatus.setAction("NA");
						}
					}
	
					simReactivationStatus.setUpdatedOn(datetime);
					simReactivationStatus.setStatus("success");
					simReactivationStatus.setRetrycount(simReactivationStatus.getRetrycount() + 1);
					retryLimits = simReactivationStatus.getRetrycount() + " /" + deafaultRetryCount;
	
				} else {
					JSONObject fault = json.getJSONObject("faultResponse");
					errMsg = fault.get("faultstring").toString();
					log.info("errMsg :" + errMsg);
	
					// check retry count and status
					if (deafaultRetryCount > simReactivationStatus.getRetrycount()
							&& simReactivationStatus.getStatus().equalsIgnoreCase("inprogress")
							&& !simReactivationStatus.getAction().equalsIgnoreCase("NA")) {
	
	
						// check action
						if (simReactivationStatus.getAction().equalsIgnoreCase("activate")) {
							responseStatus = thinkSpace.SimActivation(imei, cust_name, acc_name, plan, iccid, reqId,
									verizonService, skuNumber);
	
							simState = "Retrying for Activation";
						} else if (simReactivationStatus.getAction().equalsIgnoreCase("deactivate")) {
							responseStatus = thinkSpace.simDeactivate(imei, cust_name, acc_name, plan, iccid, reqId,
									verizonService);
							simState = "Retrying for Deactivation";
						} else if (simReactivationStatus.getAction().equalsIgnoreCase("restore")) {
							responseStatus = thinkSpace.simRestore(imei, cust_name, acc_name, plan, iccid, reqId,
									verizonService);
							simState = "Retrying for Reactivation";
						} else if (simReactivationStatus.getAction().equalsIgnoreCase("suspend")) {
							responseStatus = thinkSpace.SimSuspend(imei, cust_name, acc_name, plan, iccid, reqId,
									verizonService);
							simState = "Retrying for Suspend";
						}
	
						simReactivationStatus.setRetrycount(simReactivationStatus.getRetrycount() + 1);
						retryLimits = simReactivationStatus.getRetrycount() + " /" + deafaultRetryCount;
						retryResponseStatus += "Retry Status : " + responseStatus + "<br><br>";
						simReactivationStatus.setUpdatedOn(datetime);
						simReactivationStatus.setMeid(imei);
	
						if (enableRemainderRetry && !responseStatus.contains("SUCCESS")) {
							try {
								
								String url = scheduleUrl+"/v3.0/verizonschedule?activityType="
										+ simReactivationStatus.getAction() + "&meid=" + imei + "&iccid=" + iccid
										+ "&skunumber=" + skuNumber + "&reqid=" + reqId + "";
	
								String txnRes = _helper.httpPOSTRequest(url, null,null);
								if (txnRes != null) {
									JSONObject res1 = new JSONObject(txnRes);
									JSONObject response1 = res1.getJSONObject("response");
									if (response1.getInt("Status") == 1) {
										scheduledInfo = "Successfully Retry Scheduled";
									} else {
										scheduledInfo = "";
										String errorMsg = response1.has("Msg") ? scheduledInfo += response1.getString("Msg")
												: "";
										String retryMsg = response1.has("ERROR")
												? scheduledInfo += " (" + response1.getString("ERROR") + ")"
												: "";
										scheduledInfo = scheduledInfo.replace("failure:", " Schedule failure ");
									}
	
									log.info("scheduledInfo : " + scheduledInfo);
								}
							} catch (Exception e) {
								log.error("Error While Schedule Retry functionality: " + e.getLocalizedMessage());
							}
	
						}
	
					} else if (simReactivationStatus.getAction().equalsIgnoreCase("NA")) {
						simState = "Failed. ";
	
						retryResponseStatus += "Request ID not found and seems request from direct verizon portal or other medium .Please check the status in the portal <br><br>";
	
					} else {
						if (deafaultRetryCount <= simReactivationStatus.getRetrycount()) {
	
							simState = "Failed during SIM Operation  " + simReactivationStatus.getAction().toUpperCase()
									+ " Retry Over Pls check in Verizon site <br><br>";
							retryLimits = simReactivationStatus.getRetrycount() + " /" + deafaultRetryCount
									+ " Limit Over<br/><br/> Please Activate SIM Manually<br/><br/>";
							simReactivationStatus.setStatus("failed");
	//						retryResponseStatus += "Retry Status : ";
						}
					}
					simReactivationStatus.setUpdatedOn(datetime);
				}
			
				if (isNewSimActivation) {
					boolean isSaved = verizonService.saveVerizonStatus(simReactivationStatus);
					log.info("savedVerizonStatus : " + isSaved);
				} else {
					boolean isUpdated = verizonService.updateVerizonStatus(simReactivationStatus);
					log.info("savedVerizonStatus : " + isUpdated);
				}
				
				log.info("errMsg :" + errMsg);
				log.info("state :" + simState);
				log.info("Action : " + simReactivationStatus.getAction());
				
				// Sending Mail
				res = sendSIMStatusMail(simReactivationStatus.getRequestId(), imei, simState, errMsg, retryLimits,
						simReactivationStatus.getAction(), retryResponseStatus, scheduledInfo);

			}else {
				log.info("Staging/Dev verizon call back maill will not triggered :" + result);
			}

		} catch (JSONException e) {
			log.error(e.getLocalizedMessage());
		}
		return res;
	}

	public JResponse sendMailNotification(String meid, String stat, String errMsg) {

		Properties prop = new Properties();
		JResponse response = new JResponse();
		try {
			/* load a properties file */
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			prop.load(new FileInputStream(file));

			String to_address = prop.getProperty("to_address");
			String cc_address = prop.getProperty("cc_address");
			String bcc_address = prop.getProperty("bcc_address");
			String mailSub = "Verizon SIM Activation Status : ";
			String mailmsg = "";

			if (!meid.trim().isEmpty()) {
				Gateway gateway = gatewayService.getGatewayDetails(meid);
				String email = "";
				if (gateway != null) {
					Set<User> user = gateway.getUsers();
					User usr = null;
					Iterator<User> it = user.iterator();
					while (it.hasNext()) {
						usr = it.next();
						email = usr.getEmail();
						break;
					}

					mailSub = mailSub + email;

					mailmsg = "<html><body>Hi Team,<br/><br/>Find the Verizon SIM Activation Status below,<br/><br/>User Email  :"
							+ email + "<br/><br/>MEID : " + gateway.getMeid() + "<br/><br/>MDN :" + gateway.getMdn()
							+ "<br/><br/>QRC :" + gateway.getQrcode() + "<br/><br/>SIM Status :" + stat;

					if (!errMsg.trim().isEmpty()) {
						mailmsg = mailmsg + "\n Error Msg : " + errMsg;
					}

					mailmsg = mailmsg + "<br/><br/>Thanks,<br/>IrisService</body></html>";

					log.info("mailmsg :" + mailmsg);

					response.put("Msg", mailmsg);
					response.put("Status", 1);
				} else {
					mailmsg = "<html><body>Hi Team,<br/><br/>Find the Verizon SIM Activation Status below,<br/><br/>MEID : "
							+ meid + "<br/><br/>Error Msg : device details not found in DB" + "<br/><br/>SIM Status :"
							+ stat;
					log.info("verizon service :" + meid + "SIM Status :" + stat);
					response.put("Msg", "device details not found in DB");
					response.put("Status", 0);
				}
			} else {
				mailmsg = "<html><body>Hi Team,<br/><br/>Find the Verizon SIM Activation Status below,<br/><br/>MEID : "
						+ meid + "<br/><br/>Error Msg : MEID not found in verizon response" + "<br/><br/>SIM Status :"
						+ stat;

				log.info("verizon service :" + meid);
				response.put("Msg", "meid empty");
				response.put("Status", 0);
			}
			email_helper.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailmsg);
		} catch (Exception e) {
			log.error("errMsg :" + e.getLocalizedMessage());
			response.put("Msg", e.getLocalizedMessage());
			response.put("Status", 0);
		}
		return response;
	}

	public JResponse sendSIMStatusMail(String reqId, String meid, String stat, String errMsg, String retryCount,
			String simAction, String retryResponseStatus, String scheduled) {

		Properties prop = new Properties();
		JResponse response = new JResponse();
		try {
			/* load a properties file */
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			prop.load(new FileInputStream(file));

			String to_address = prop.getProperty("to_address");
			String cc_address = prop.getProperty("cc_address");
			String bcc_address = prop.getProperty("bcc_address");

			String mailSub = "";
			String mailmsg = "";
			String reqProcessStatus = "";

			if (stat.contains("Successfully")) {
				reqProcessStatus = "Success : ";
			} else if (stat.contains("Retrying for") || stat.contains("Failed during SIM Operation")) {
				reqProcessStatus = "Failed : ";
			} else {
				reqProcessStatus = "Failed : ";
			}

			if (simAction.equalsIgnoreCase("activate")) {
				mailSub = reqProcessStatus + "Verizon SIM Activation Status : ";
			} else if (simAction.equalsIgnoreCase("restore")) {
				mailSub = reqProcessStatus + "Verizon SIM Reactivation Status : ";
			} else if (simAction.equalsIgnoreCase("deactivate")) {
				mailSub = reqProcessStatus + "Verizon SIM Deactivation Status : ";
			} else if (simAction.equalsIgnoreCase("suspend")) {
				mailSub = reqProcessStatus + "Verizon SIM Suspend Status : ";
			} else if (simAction.equalsIgnoreCase("NA")) {
				mailSub = reqProcessStatus + "Verizon SIM Operation : ";
			}

			if (!meid.trim().isEmpty()) {
				Gateway gateway = gatewayService.getGatewayDetails(meid);
				String email = "";
				if (gateway != null) {
					Set<User> user = gateway.getUsers();
					User usr = null;
					Iterator<User> it = user.iterator();
					while (it.hasNext()) {
						usr = it.next();
						email = usr.getEmail();
						break;
					}

					mailmsg = "<html><body>Hi Team,<br/><br/>Find the " + mailSub + "<br/><br/>User Email  :" + email
							+ "<br/><br/>MEID : " + gateway.getMeid() + "<br/><br/>" + "Request ID : " + reqId
							+ "<br><br>" + "MDN :" + gateway.getMdn() + "<br/><br/>QRC :" + gateway.getQrcode()
							+ "<br/><br/>SIM Status :" + stat + "<br/><br/>Retry Count : " + retryCount + "<br/><br/>"
							+ retryResponseStatus;

					if (!errMsg.trim().isEmpty()) {
						mailmsg = mailmsg + "<br/><br/> Error Msg : " + errMsg;
					}

					if (!scheduled.trim().isEmpty()) {
						mailmsg = mailmsg + "<br/><br/> scheduled Info : " + scheduled;
					}

					if (email.equalsIgnoreCase("NA") || email.isEmpty()) {
						mailSub = mailSub + meid;
					} else {
						mailSub = mailSub + email;
					}

					mailmsg = mailmsg + "<br/><br/>Thanks,<br/>IrisService</body></html>";

					log.info("mailmsg :" + mailmsg);

					response.put("Msg", mailmsg);
					response.put("Status", 1);
				} else {

					mailmsg = "<html><body>Hi Team,<br/><br/>Find the " + mailSub + "<br/><br/>MEID : " + meid
							+ "<br/><br/>" + "Request ID : " + reqId + "<br><br>"
							+ "Error Msg : device details not found in DB" + "<br/><br/>SIM Status :" + stat
							+ "<br/><br/>Retry Count : " + retryCount + "<br/><br/>" + retryResponseStatus;
					mailSub = mailSub + meid;
					log.info("verizon service :" + meid + "SIM Status :" + stat);

					mailmsg = mailmsg + "<br/><br/>Thanks,<br/>IrisService</body></html>";
					response.put("Msg", "device details not found in DB");
					response.put("Status", 0);
				}
			} else {
				mailmsg = "<html><body>Hi Team,<br/><br/>Find the " + mailSub + "" + "<br/><br/>" + "Request ID : "
						+ reqId + "<br><br>" + "Error Msg : MEID not found in verizon response"
						+ "<br/><br/>SIM Status :" + stat + "<br/><br/>Retry Count : " + retryCount + "<br/><br/>"
						+ retryResponseStatus + "<br/><br/>" + scheduled;
				mailmsg = mailmsg + "<br/><br/>Thanks,<br/>IrisService</body></html>";
				mailSub = mailSub + reqId;
				log.info("verizon service :" + meid);
				response.put("Msg", "meid empty");
				response.put("Status", 0);
			}
			email_helper.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailmsg);
		} catch (Exception e) {
			log.error("errMsg :" + e.getLocalizedMessage());
			response.put("Msg", e.getLocalizedMessage());
			response.put("Status", 0);
		}
		return response;
	}
	
	

}
