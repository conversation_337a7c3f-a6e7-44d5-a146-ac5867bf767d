package com.nimble.irisservices.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.validation.ConstraintViolationException;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dto.AmazonReviewStatus;
import com.nimble.irisservices.dto.AmazonUserReview;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JUserFeedback;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.FeedbackForm;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.OrderMappingDetails;
import com.nimble.irisservices.entity.UserFeedbackTransaction;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IReportServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class FeedbackController {

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	ICreditSystemService crService;

	@Value("${amazonrateuscount}")
	private int amazonrateuscount;

	@Value("${amazonredirecturl}")
	private String amazonredirecturl;

	@Value("${walmartredirecturl}")
	private String walmartredirecturl;
	
	@Value("${showamazonrateus}")
	private boolean showamazonrateus;

	@Value("${showfeedback}")
	private boolean showfeedback;
	
	@Value("${popup_orderid_feedback}")
	private boolean popup_orderid_feedback=false;
	
	@Value("${reportcount_rating}")
	private int reportCount = 1000;

	@Autowired
	@Lazy
	IReportServiceV4 rptServicev4;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	private static final Logger log = LogManager.getLogger(FeedbackController.class);

	public FeedbackController() {
		// TODO Auto-generated constructor stub
	}
	// getRatingAndFeedback - kalai
	@RequestMapping(value = "v4.0/getratingandfeedback/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getRatingAndFeedback(@PathVariable String autho,
			@RequestParam("gatewayid") long gatewayid, @RequestParam("requestfrom") String requestfrom,
			@RequestParam("os") String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
			) {
		log.info("Entered into get ratingandfeedback : autho : " + autho);
		JResponse response = new JResponse();

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);
			Gateway gateway = null;
			if( gatewayid!=0 ) {
				gateway = gatewayService.getGatewayByid(gatewayid);
				if( gateway == null ) {
					response.put("Status", 0);
					response.put("Msg", "Gateway not found");
					return response;
				}
			}
			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
			} else {
				boolean showlink = false;
				String showtype = "";
				String msg = "";
				String lastRptDate = userServiceV4.getLastFeedbackDate(usr.getId(),1);
				String currenttime = IrisservicesUtil.getCurrentTimeUTC();

				final SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
				long diffInHrs = 0;
				Date curr = formatter.parse(currenttime);
				Date pre = null;

				if (lastRptDate != null) {
					pre = formatter.parse(lastRptDate);

					diffInHrs = ((curr.getTime() - pre.getTime()) / 3600000);
				} else {
					diffInHrs = 25; // for very first transaction date will be null. so initializing to 25 hrs
				}
				
				if (requestfrom.equalsIgnoreCase("settingspage-rating")) {
					boolean show_dialog = true;

					String redirecturl = "";
					String redirect_type = "internal";
					msg = "Rate us";

					ArrayList<OrderMappingDetails> orderList = userServiceV4.getOrderMappingListByUser(usr.getId());

					for (OrderMappingDetails orderMap : orderList) {
						if (orderMap.getOrderchannel().equalsIgnoreCase("amazon")) {
							redirecturl = amazonredirecturl;
							redirect_type = "external";
							msg = "Would you like to rate us on Amazon!";
							break;
						} else if (orderMap.getOrderchannel().equalsIgnoreCase("walmart")) {
							redirecturl = walmartredirecturl;
							redirect_type = "external";
							msg = "Would you like to rate us on Walmart!";
						}
					}

					AmazonReviewStatus reviewStatus = new AmazonReviewStatus(redirecturl, show_dialog, 0,
							redirect_type);
					reviewStatus.setAmazon_msg(msg);

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("showlink", true);
					response.put("showtype", "rating");
					response.put("reviewstatus", reviewStatus);

				} else if (showfeedback) {
					// check this feed back sent to user in last x days
					if (diffInHrs >= 24 || (requestfrom.equalsIgnoreCase("vpm"))) {
						JUserFeedback jUserFeedback = userServiceV4.getFeedbackLink(usr.getId(), requestfrom);

						if (jUserFeedback != null) {
							showlink = true;
							showtype = "feedback";
							msg = "Success";

							if (requestfrom.equalsIgnoreCase("vpm"))
								jUserFeedback.setTitle("Do you like our WaggleVet?");

							response.put("feedbackstatus", jUserFeedback);
						} else if ( popup_orderid_feedback && gateway!=null && (!gateway.isShowOrderId() && !gateway.isPurchased_from_others())) {
							Date currTime = formatter.parse(currenttime);
							long gatewayTimeDiff = ((currTime.getTime() - gateway.getInstalled_date().getTime()) / 3600000);
							
							JUserFeedback jUserFeedbackOrderId = userServiceV4.getFeedbackLink(usr.getId(), "orderid");
							if( jUserFeedbackOrderId != null  && gatewayTimeDiff>=5) {
								showlink = true;
								showtype = "orderid";
								msg = "Success";
								response.put("feedbackstatus", jUserFeedbackOrderId);
							} else if(showamazonrateus) {
								JResponse ratingResponse = showAmazonRateUs(usr,gatewayid);
								AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse().get("reviewstatus");
								msg = (String) ratingResponse.getResponse().get("msg");
								if( reviewsStatus != null ) {
									response.put("reviewstatus", reviewsStatus);
									showlink = true;
									showtype="rating";
									msg = "Success";
								} else {
									showlink = false;
									showtype="rating";
								}
							}
							
						} else if (showamazonrateus) {
							JResponse ratingResponse = showAmazonRateUs(usr,gatewayid);
							AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse().get("reviewstatus");
							msg = (String) ratingResponse.getResponse().get("msg");
							if( reviewsStatus != null ) {
								response.put("reviewstatus", reviewsStatus);
								showlink = true;
								showtype="rating";
								msg = "Success";
							} else {
								showlink = false;
								showtype="rating";
							}
						} else {
							showlink = false;
							showtype = "feedback";
							msg = "Feedback/Rating not applicable";
						}
					} // end of >24 hrs
					else if (showamazonrateus) {
						JResponse ratingResponse = showAmazonRateUs(usr, gatewayid);
						AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse()
								.get("reviewstatus");
						msg = (String) ratingResponse.getResponse().get("msg");
						if (reviewsStatus != null) {
							response.put("reviewstatus", reviewsStatus);
							showlink = true;
							showtype = "rating";
							msg = "Success";
						} else {
							showlink = false;
							showtype = "rating";
						}
					}
					else {
						showlink = false;
						showtype = "feedback";
						msg = "Feedback/Rating submitted. will be shown after 24 hrs from last transaction";
					}
					response.put("Status", 1);
					response.put("Msg", msg);
					response.put("showlink", showlink);
					response.put("showtype", showtype);

				} else if (diffInHrs >= 24 && popup_orderid_feedback && gateway!=null && (!gateway.isShowOrderId() && !gateway.isPurchased_from_others()) ) {
					Date currTime = formatter.parse(currenttime);
					long gatewayTimeDiff = ((currTime.getTime() - gateway.getInstalled_date().getTime()) / 3600000);
					JUserFeedback jUserFeedback = userServiceV4.getFeedbackLink(usr.getId(), "orderid");
					if (jUserFeedback != null && gatewayTimeDiff>=5) {
						showlink = true;
						showtype = "orderid";
						msg = "Success";
						response.put("feedbackstatus", jUserFeedback);
					}  else if (showamazonrateus) {
						JResponse ratingResponse = showAmazonRateUs(usr,gatewayid);
						AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse().get("reviewstatus");
						msg = (String) ratingResponse.getResponse().get("msg");
						if( reviewsStatus != null ) {
							response.put("reviewstatus", reviewsStatus);
							showlink = true;
							showtype="rating";
							msg = "Success";
						} else {
							showlink = false;
							showtype="rating";
						}
					} else {
						showlink = false;
						showtype = "orderid";
						msg = "orderid not applicable";
					}
				} else if (showamazonrateus) {
					// check this feed back sent to user in last x days
					if (diffInHrs >= 24 || (requestfrom.equalsIgnoreCase("vpm"))) {
						JResponse ratingResponse = showAmazonRateUs(usr,gatewayid);
						AmazonReviewStatus reviewsStatus = (AmazonReviewStatus) ratingResponse.getResponse().get("reviewstatus");
						msg = (String) ratingResponse.getResponse().get("msg");
						if( reviewsStatus != null ) {
							response.put("reviewstatus", reviewsStatus);
							showlink = true;
							showtype="rating";
							msg = "Success";
						} else {
							showlink = false;
							showtype="rating";
						}
					} // end of >24 hrs
					else {
						showlink = false;
						showtype = "feedback";
						msg = "Feedback/Rating submitted. will be shown after 24 hrs from last transaction";
					}

					response.put("showlink", showlink);
					response.put("showtype", showtype);
					response.put("Status", 1);
					response.put("Msg", msg);
				} else {
					response.put("Status", 1);
					response.put("Msg", "Feedback /Order ID/ Amazon Rating not enabled");
				}
			}
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting feedback/rating");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception : getratingandfeedback : " + e.getLocalizedMessage());
		}
		return response;
	}

	private JResponse showAmazonRateUs(UserV4 usr, long gatewayid) {
		JResponse response = new JResponse();
		AmazonReviewStatus reviewStatus = null;
		try {
			AmazonUserReview amazonReviewObj = userServiceV4.getAmazonUserReview(usr.getId(),1);
			int viewCount = 0;
			int rptCount = 0;
			String lastRptDate = userServiceV4.getLastFeedbackDate(usr.getId(),1);
			String currenttime = IrisservicesUtil.getCurrentTimeUTC();
			final SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
			long diffInHrs = 0;
			Date curr = formatter.parse(currenttime);
			Date pre = null;
			boolean showlink = false;
			String msg = "";
			String showtype = "rating";
			
			if (lastRptDate != null) {
				pre = formatter.parse(lastRptDate);
				diffInHrs = ((curr.getTime() - pre.getTime()) / 3600000);
			} else {
				diffInHrs = 25; // for very first transaction date will be null. so initializing to 25 hrs
			}
			
			if (amazonReviewObj == null || (!amazonReviewObj.isAmazon_rateus())) {
				viewCount = userServiceV4.getViewCount(usr.getId());
				rptCount = rptServicev4.getGatewayReportCount(gatewayid);
				lastRptDate = rptServicev4.getLastGatewayReporttime(gatewayid);
				pre = formatter.parse(lastRptDate);
	
				long diffInMins = ((curr.getTime() - pre.getTime()) / 60000);
				log.info(" diffInMins : " + diffInMins);
	
				int amazon_redirect_cnt = 3;
				if ((viewCount % amazonrateuscount == 0) && (rptCount >= reportCount) && (diffInMins <= 60)) {
					boolean show_dialog = true;
	
					String redirecturl = "";
					String redirect_type = "internal";
	
					ArrayList<OrderMappingDetails> orderList = userServiceV4
							.getOrderMappingListByUser(usr.getId());
					for (OrderMappingDetails orderMap : orderList) {
						if (orderMap.getOrderchannel().equalsIgnoreCase("amazon")) {
							redirecturl = amazonredirecturl;
							redirect_type = "external";
							msg = "Would you like to rate us on Amazon!";
							break;
						} else if (orderMap.getOrderchannel().equalsIgnoreCase("walmart")) {
							redirecturl = walmartredirecturl;
							redirect_type = "external";
							msg = "Would you like to rate us on Walmart!";
						}
					}
	
					reviewStatus = new AmazonReviewStatus(redirecturl, show_dialog, amazon_redirect_cnt,
							redirect_type);
	
					msg = "Success";
					showlink = true;
					response.put("reviewstatus",reviewStatus );
					response.put("msg", msg);
					return response;
				} else {
					msg = "Rating not applicable. Criteria not matched";
					response.put("reviewstatus",reviewStatus );
					response.put("msg", msg);
					return response;
				}
			} else {
				msg = "Rating already submitted.";
				response.put("reviewstatus",reviewStatus );
				response.put("msg", msg);
				return response;
			}
		} catch (Exception e) {
			log.error("Error in showAmazonRateUs : "+e.getLocalizedMessage());
			response.put("reviewstatus",reviewStatus );
			response.put("msg", "Error");
			return response;
		}
	
		
	}
	
	// updatefeedbacklink - kalai
	@RequestMapping(value = "v4.0/updateuserfeedback/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateUserFeedback(@PathVariable String autho, @RequestParam("option") String option,
			@RequestParam("formid") long formid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
			) {
		JResponse response = new JResponse();
		log.info("Entering updateuserfeedback : " + autho);

		// option 1-never; 2-later; 3-now

		UserV4 usr = null;
		try {
			usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr != null) {
				UserFeedbackTransaction uftObj = userServiceV4.getUserFeedbackTransaction(usr.getId(), formid);
				option = option.trim();
				boolean show = false;
				boolean close = false;

				if (option.equalsIgnoreCase("1")) {
					show = false;
					close = true;
				} else if (option.equalsIgnoreCase("2")) {
					show = false;
					close = false;
				} else if (option.equalsIgnoreCase("3")) {
					show = true;
					close = false;
				}

				String cutUtc = IrisservicesUtil.getCurrentTimeUTC();

				if (uftObj != null) {
					int retry = uftObj.getRetry_count() - 1;

					if (retry < 0)
						retry = 0;

					uftObj.setShow(show);
					uftObj.setClose_form(close);
					uftObj.setRetry_count(retry);
					uftObj.setUpdatedon(cutUtc);
				} else {
					uftObj = new UserFeedbackTransaction(usr.getId(), formid, cutUtc, cutUtc, show, close, "NA","NA",false,1l);
					uftObj.setRetry_count(2);
				}
				boolean stat = userServiceV4.saveOrUpdateUserFeedback(uftObj);

				if (stat) {
					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Failed");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
			}
			return response;
		} catch (InvalidAuthoException e) {
			log.error("Invalid Authkey :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
	}
	
	//Used in web
	//Create/Update feedback form web - Savitha.
	@RequestMapping(value = "v4.0/createfeedbackformweb/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createFeedbackFormWeb(@PathVariable String autho, @RequestBody FeedbackForm feedback) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			status = userServiceV4.createFeedbackFormWeb(feedback);
			response.put("Status", status);
			response.put("Msg", "Success");
		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "Feedback form already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createFeedbackFormWeb : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "Feedback form creation failed");
			log.error("createFeedbackFormWeb : " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}
	
	//Used in web
	//List feedback form web - Savitha.
	@RequestMapping(value = "v4.0/listfeedbackweb/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listFeedbackWeb(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			List<FeedbackForm> feedbackList = userServiceV4.listFeedbackWeb();
			
			response.put("feedbackList", feedbackList);
			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in getting feedback list");
			response.put("Error", e.getLocalizedMessage());
			log.error("listPlan : " + e.getLocalizedMessage());
		}
		return response;
	}
	

}