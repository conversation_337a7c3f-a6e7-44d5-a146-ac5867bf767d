package com.nimble.irisservices.controller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.irisservices.dto.EmailData;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IWaggleEmailService;

@RestController 
public class WaggleEmailController {

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;
	
	@Autowired
	IWaggleEmailService waggleEmailService;
	
	@Value("${waggle_cc_address}")
	private String waggle_CC_Address;	
	
	@Value("${waggle_bcc_address}")
	private String waggle_BCC_Address;
		
	@Value("${waggle_from_address}")
	private String waggle_FROM_Address;	
	
	private static final Logger log = LogManager.getLogger(WaggleEmailController.class);
	
	@PostMapping("v4.0/sendgenericemail")
	public JResponse sendGenericWaggleEmail(@ModelAttribute EmailData emaildata /*@RequestParam("attachment") MultipartFile[] mulfiles*/) {
		log.info("Entered into sendGenericWaggleEmail to email to : "+emaildata.getToaddress());
		JResponse response = new JResponse();
		
		try {
			log.info("Attachments length : "+emaildata.getAttachments().length);
			
			if(emaildata.getFromaddress() == null || emaildata.getFromaddress().isEmpty() || emaildata.getFromaddress().equalsIgnoreCase("na")) {
				emaildata.setFromaddress(waggle_FROM_Address);
			}
			if(emaildata.getToaddress() == null || emaildata.getToaddress().isEmpty() || emaildata.getToaddress().equalsIgnoreCase("na")) {
				response.put("Status", 0);
				response.put("Msg", "Please enter a valid recipient");
				return response;
			}
			
			String ccAddress = waggle_CC_Address;
			if(emaildata.getCcaddress() != null && !emaildata.getCcaddress().isEmpty() && !emaildata.getCcaddress().equalsIgnoreCase("na")) {
				ccAddress += ","+emaildata.getCcaddress();
			}
			emaildata.setCcaddress(ccAddress);
			String bccAddress = waggle_BCC_Address;
			if(emaildata.getBccaddress() != null && !emaildata.getBccaddress().isEmpty() && !emaildata.getBccaddress().equalsIgnoreCase("na")) {
				bccAddress+=","+emaildata.getBccaddress();
			}			
			emaildata.setCcaddress(bccAddress);
			
			if(waggleEmailService.sendEmailFromWaggle(emaildata)) {
				response.put("Status", 1);
				response.put("Msg", "Mail sent successfully");
				log.info("Mail sent successfully.");
			}else {
				response.put("Status", 0);
				response.put("Msg", "Failed send email");
				log.info("Failed send email.");
			}
		}catch (Exception e) {
			log.error("Exception occured at sendGenericWaggleEmail : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured");
			response.put("Error", e.getLocalizedMessage());
		}		
		return response;
	}

}
