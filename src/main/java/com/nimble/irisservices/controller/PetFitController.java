package com.nimble.irisservices.controller;

import java.util.ArrayList;
import java.util.List;

//import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JPetFitDailyReport;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.PetFitDailyReport;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IPetFitService;
import com.nimble.irisservices.service.IUserService;

@Controller
public class PetFitController {

	@Autowired
	IUserService userService;

	@Autowired
	IGatewayService gatewayService;

	@Autowired
	IPetFitService iPetFitService;

	private static final Logger log = LogManager.getLogger(PetFitController.class);

	// ========get assetmodels================
	@RequestMapping(value = "v3.0/getfitreport/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetModel(@PathVariable String autho, @RequestParam("datetime") String dateTime,
			@RequestParam("timezone") String timezone,
			@RequestParam(value = "enabletimezone", defaultValue = "0", required = false) String enabletimezone) {
		JResponse response = new JResponse();
		Helper _helper = new Helper();
		try {
			User user = userService.verifyAuthKey(autho);

			log.info("user authenticated");

			log.info("Variable" + dateTime + "  " + timezone + " " + enabletimezone);

			String reportDate = null;

			if (enabletimezone.equalsIgnoreCase("1")) {
				reportDate = _helper.getUTCDate(dateTime, timezone);
				log.info("TimeZone Enabled");
			} else {
				reportDate = dateTime.split(" ")[0];
				log.info("TimeZone Disabled");
			}

			List<JGateway> userGateway = gatewayService.getGateway(null, null, null, null, user.getId(), null);

			log.info("userGateway Size" + userGateway.size());
			
			List<JPetFitDailyReport> jUserPetFitreport = new ArrayList<JPetFitDailyReport>();

			if (userGateway != null) {
				if (userGateway.size() > 0) {

					for (JGateway gateway : userGateway) {

						log.info("Gateway ID : " + gateway.getId() + "Report Date : " + reportDate);

						PetFitDailyReport petFitreport = iPetFitService.getReport(reportDate,
								Long.toString(gateway.getId()));

						if (petFitreport != null) {

							JPetFitDailyReport jpetreport = new JPetFitDailyReport(
									Long.toString(petFitreport.getGateway().getId()),
									petFitreport.getGateway().getName(), petFitreport.getUtcDate(),
									petFitreport.getTotalStepCount(), petFitreport.getTotalIdleSecs(),
									petFitreport.getTotalwalksecs(), petFitreport.getTotalRunSecs());
							log.info("Got PetFitreport : " + gateway.getId());
							jUserPetFitreport.add(jpetreport);
						}
					}

				}

				if (jUserPetFitreport.size() > 0) {
					log.info("Got Report");
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("petFitReport", jUserPetFitreport);
				} else {
					log.info("No Report");
					response.put("Status", 0);
					response.put("Msg", "No Report found.");
					response.put("petFitReport", jUserPetFitreport);
				}
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No Device Configured. Please contact our support for further assitance.");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

	}

}
