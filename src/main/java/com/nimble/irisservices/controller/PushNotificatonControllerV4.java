package com.nimble.irisservices.controller;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JDeviceNotification;
import com.nimble.irisservices.dto.JFcmNotification;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.PushNotificationStatus;
import com.nimble.irisservices.entity.PushNotifications;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserToken;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.pojo.JSendNotifications;
import com.nimble.irisservices.pojo.SendNotifications;
import com.nimble.irisservices.service.IAlertServiceV4;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IFirebaseService;
import com.nimble.irisservices.service.IPushNotificationService;
import com.nimble.irisservices.service.IPushNotificationServiceV4;
import com.nimble.irisservices.service.IReminderService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class PushNotificatonControllerV4 {
	private static final Logger log = LogManager.getLogger(PushNotificatonController.class);

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IPushNotificationServiceV4 notificationService;

	@Autowired
	IUserService userService;

	@Autowired
	IReminderService reminderservice;
	
	@Autowired
	IAlertServiceV4 alertService;

	@Autowired
	@Lazy
	private IAsyncService async;
	
	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IPushNotificationService iNotificationService;
	
	@Autowired
	IFirebaseService iFirebaseService;
	
	Helper _helper = new Helper();
	
	// v4.0/usernotifications/ - SIV
	@RequestMapping(value = "v4.0/usernotifications/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getNotificaitonByUserIDV4(@PathVariable String autho, @RequestParam("userid") String userId,
			@RequestParam("status") String status,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		log.info("Entering  getNotificaitonByUserIDV4 : " + autho);
		UserV4 user = null;
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);
		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getNotificaitonByUserIDV4 : " + ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		try {
			List<SendNotifications> sendNotificationList = new ArrayList<SendNotifications>();
			//List<PushNotifications> notifications = new ArrayList<PushNotifications>();
			sendNotificationList = notificationService.userNotificationsV4(userId, status, 0, false);
			
			ArrayList<Long> notifyIds = new ArrayList<Long>();
			
			//update pushnotification viewes status as 1
			for(SendNotifications notifyObj : sendNotificationList) {
				notifyIds.add(notifyObj.getNotificationId());
			}			
			notificationService.updateNotificationStatus(user.getId(), notifyIds);
			
			if (sendNotificationList != null) {

				/* To reverse the collection of latest reported gateway */
				Collections.sort(sendNotificationList, new Comparator<SendNotifications>() {
					@Override
					public int compare(SendNotifications a, SendNotifications b) {
						DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						try {
							return format.parse(b.getSendDate()).compareTo(format.parse(a.getSendDate()));
						} catch (ParseException e) {
							log.error(e.getMessage());
						}
						return 0;
					}
				});
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("userPushNotification", sendNotificationList);
				userService.updateUserNotification(userId, "false");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No notifications found for user.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Error in getting the notifications.");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getNotificaitonByUserIDV4 : " + ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}

	//  Mobile API - kalai
	@RequestMapping(value = "v4.0/getnotificationcounts/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getNotificaitonCounts(@PathVariable String autho, @RequestParam("app_ver") String app_ver,
			@RequestParam("os") String os) {
		log.info("Entering  getnotificationcounts : " + autho);

		JResponse response = new JResponse();
		UserV4 user = null;
		
		int remainderOverdueCount = -1;
		int alert_cnt = 0;
		int notification_cnt = 0;
		
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);
		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getNotificaitonCount : " + ex.getLocalizedMessage());
			return response;
		}
		
		try {
			remainderOverdueCount = reminderservice.getremainderOverDueCount(user.getId());
			
			alert_cnt = alertService.getUnAckAlertCount(user.getCmpId());
			
			notification_cnt = notificationService.getNotViewedNotificationCount(user.getId());
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			
			response.put("remainder_overdue_count", remainderOverdueCount);
			response.put("alert_cnt", alert_cnt);
			response.put("notification_cnt", notification_cnt);
			
			return response;
		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Error in getting the notification count.");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getnotificationcounts : " + ex.getLocalizedMessage());
			return response;
		}
	}

	@PostMapping(value = "v5.0/notifydevice/{autho}", headers = "Accept=application/json")
	@ResponseBody
	public JResponse notifydevice(@PathVariable String autho, @RequestBody JDeviceNotification jDevNotification) {

		String validateTokenUrl = _helper.getExternalConfigValue("validatetokenurl", externalConfigService);
		String fcmsendUrl = _helper.getExternalConfigValue("fcmsendurl", externalConfigService);
		String authKey = _helper.getExternalConfigValue("fcmauthorization", externalConfigService);
		JResponse response = new JResponse();

		try {
			// User user = userService.verifyAuthKey(autho);

			/*
			 * Notification Type will be as follows, Type 1 - Only Text Message It has only
			 * short description and message body. It does not contain the image url Type 2
			 * : It has short description and message body and it has image url Type 3. It
			 * has only short description and bannerurl
			 */
			boolean notifyDevice = userServiceV4.checkDeviceNotify(jDevNotification.getUser_id(),
					jDevNotification.getGateway_id());
			if (!notifyDevice) {
				log.info("user device notification disabled");
				response.put("Status", 0);
				response.put("Msg", "user device notification disabled");
				return response;
			}

			PushNotifications notifications = iNotificationService
					.getNotification(Integer.toString(jDevNotification.getPushNotificationId()));

			if (notifications != null) {

				JSendNotifications sendNotification = new JSendNotifications();

				sendNotification.setSource("pushNotifications");
				sendNotification.setTitle(notifications.getTitle());
				sendNotification.setShortDescription(notifications.getShortDescription());

				if (notifications.getNotificationtype().getId() == 2) {
					sendNotification.setImageUrl(notifications.getBannerImageUrl());
				} else {
					sendNotification.setImageUrl("NA");
				}

//				CloudNotifications cloudNotifications = new CloudNotifications();
//				cloudNotifications.setTitle(notifications.getTitle());
//				cloudNotifications.setBody(notifications.getShortDescription());
//				cloudNotifications.setSound("default");
//				cloudNotifications.setSource("pushNotifications");
//				cloudNotifications.setShortDescription(notifications.getShortDescription());
//				if (notifications.getNotificationtype().getId() == 2) {
//					cloudNotifications.setImageUrl(notifications.getBannerImageUrl());
//				}

				Set<User> users = new HashSet<User>();
				List<User> user = userService.getUser(Long.toString(jDevNotification.getUser_id()), 3);

				if (user.size() == 0) {
					log.error("user not found for :" + jDevNotification.getUser_id());
					response.put("Status", 0);
					response.put("Msg", "Error in getting the pushnotifications.");
					return response;
				}

				users.add(user.get(0));

				List<UserToken> userToken = new ArrayList<UserToken>();
				userToken = userService.getUserToken(Long.toString(jDevNotification.getUser_id()));
				boolean notificationSent = false;

				if (null != userToken && userToken.size() > 0) {
					ArrayList<String> tokenList = new ArrayList<String>();

					String title = notifications.getTitle();
					String body = notifications.getShortDescription();
					String redirectUrl = notifications.getImageUrl();
					String imageUrl = "NA";
					String source = "pushNotifications";
					String shortDescription = notifications.getShortDescription();
					int monitor_id = (int) notifications.getMonitortype();
					if (notifications.getNotificationtype().getId() == 2) {
						imageUrl = notifications.getBannerImageUrl();
					}
					
					for (UserToken token : userToken) {
						tokenList.add(token.getToken());
					}
					JFcmNotification fcmNotification = new JFcmNotification(title, body, redirectUrl, imageUrl, tokenList,
							source, shortDescription, monitor_id);
					int message = iFirebaseService.sendNotification(fcmNotification);
					log.info(message + " message(s) were sent : userid:"+jDevNotification.getUser_id());
					
					if(message>0)
						notificationSent = true;
				} else {
					notificationSent = false;
				}
				PushNotificationStatus notificationStatus = new PushNotificationStatus();

				notificationStatus.setPushNotificationId(Long.toString(notifications.getId()));
				notificationStatus.setUserId(Long.toString(jDevNotification.getUser_id()));
				notificationStatus.setGateway_id(jDevNotification.getGateway_id());
				notificationStatus.setSendDate(_helper.getCurrentTimeinUTC());
				notificationStatus.setShort_description(notifications.getShortDescription());

				if (notificationSent) {
					notificationStatus.setStatus("Succeed");

				} else {
					notificationStatus.setStatus("Failed");
				}
				iNotificationService.updatePushNotificationStatus(notificationStatus);
				boolean status = iNotificationService.updatePushNotificationsForUsers(notifications, users);

				if (status) {
					log.info("userpushnotifications table updated Success!");
				} else {
					log.info("userpushnotifications table updated Failed!");
				}

				if (status) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error in sending the notifications.");
					return response;
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "Notifications not found for given notification id.");
				return response;
			}
		} catch (Exception ex) {
			log.error("error notifydevice: "+ex.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in sending pushnotifications.");
			return response;
		}
	}
	
	/** <AUTHOR> */
	// FCM API for Pushnotification
	@PostMapping("v4.0/sendfcmnotification/{autho}")
	@ResponseBody
	public JResponse sendFCMNotification(@PathVariable String autho, @RequestBody JFcmNotification fcmNotification) {
		log.info("Entering  sendfcmnotification : " + autho);

		JResponse response = new JResponse();
		UserV4 user = null;

		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);

		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : sendFCMNotification : " + ex.getLocalizedMessage());
			return response;
		}

		try {
			int message = iFirebaseService.sendNotification(fcmNotification);
			log.info(message + " message(s) were sent");
			response.put("Status", 1);
			response.put("Msg", message + " message(s) were sent successfully");
			response.put("MsgCount", message);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Error when sending messages");
			response.put("MsgCount", 0);
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : sendFCMNotification : " + ex.getLocalizedMessage());
			return response;
		}
	}

}
