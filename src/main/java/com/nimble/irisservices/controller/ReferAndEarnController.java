package com.nimble.irisservices.controller;

import java.sql.Timestamp;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.AppImage;
import com.nimble.irisservices.entity.CreditType;
import com.nimble.irisservices.entity.ReferEarn;
import com.nimble.irisservices.entity.ReferralCredits;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAdvertisementService;
import com.nimble.irisservices.service.IGeneralConfigService;
import com.nimble.irisservices.service.IReferAndEarnService;
import com.nimble.irisservices.service.IUserService;

@Controller
public class ReferAndEarnController {
	private static final Logger log = LogManager.getLogger(ReferAndEarnController.class);

	@Lazy
	@Autowired
	IReferAndEarnService refService;

	@Autowired
	@Lazy
	IGeneralConfigService generalConfigService;

	@Lazy
	@Autowired
	IUserService userService;

	@Value("${referralurl}")
	private String referralurl;

	@Value("${weblinkurl}")
	private String weblinkurl;

	@Value("${weblinkflag}")
	private String weblinkflag;

	@Autowired
	@Lazy
	IAdvertisementService advService;

	@Value("${usereferralcandy}")
	private boolean useReferralCandy;

	@Value("${referralcandyAccessKey}")
	private String referralCandyAccessKey;

	@Value("${referralcandysecretkey}")
	private String referralCandySecretKey;
	
	Helper helper = new Helper();

	@RequestMapping(value="v3.0/createrefcredits/{autho}",method = RequestMethod.POST, headers="Accept=application/json")
	@ResponseBody
	public JResponse createReferralCredits(@PathVariable String autho,@RequestBody ReferralCredits refCredit, @RequestParam long  advid,
			@RequestParam long refid, @RequestParam String expired )
	{
		JResponse response = new JResponse();
		boolean status = false;
		try {
			Timestamp curDate =  Timestamp.valueOf(IrisservicesUtil.getCurrentTimeUTC());
			Timestamp expiredDate = Timestamp.valueOf(expired);
			CreditType adv = refService.getCreditTypeById(advid);
			CreditType ref = refService.getCreditTypeById(refid);

			refCredit.setCreatedon(curDate);
			refCredit.setAdvocate_credit_type(adv);
			refCredit.setReferral_credit_type(ref);
			refCredit.setExpiry_date(expiredDate);

			status = refService.saveOrUpdateReferralCredits(refCredit);

			// For backward compatabilty store in ReferEarn object also
			ReferEarn refer = new ReferEarn();
			refer.setId(0L);
			refer.setMessageContent(refCredit.getTitle());
			refer.setOfferOne(refCredit.getAdvocate_msg());
			refer.setOfferTwo(refCredit.getReferral_msg());
			refer.setCreatedOn(IrisservicesUtil.getCurrentTimeUTC());
			refer.setReferalLink(referralurl);
			generalConfigService.saveOrUpdateReferEarn(refer);

			response.put("Status", status);
			response.put("Msg","Success");

		}
		catch(ConstraintViolationException ex)
		{
			response.put("Status", status);
			response.put("Msg","Referral Credits already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("create referral credits : "+ex.getLocalizedMessage());
		}
		catch (Exception e) {
			response.put("Status", status);
			response.put("Msg","Referral Credits creation failed");
			log.error("create referral credits : " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value="v3.0/listrefcredits/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse listReferralCredits(@PathVariable String autho)
	{
		JResponse response = new JResponse();
		try {
			List<ReferralCredits> refList = refService.listReferralCredits();

			response.put("refList", refList);
			response.put("Status", 1);
			response.put("Msg","Success");

		}catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg","Error occured in list");
			response.put("Error", e.getLocalizedMessage());
			log.error("listReferralCredits : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value="v3.0/deleterefcredits/{autho}",method = RequestMethod.POST, headers="Accept=application/json")
	@ResponseBody
	public JResponse deleteReferralCredits(@PathVariable String autho,@RequestParam long id)
	{
		JResponse response = new JResponse();
		boolean status = false;
		try {
			status = refService.deleteReferralCredits(id);

			response.put("Status", status);
			if(status == true)
				response.put("Msg","Success");

		}catch (Exception e) {
			response.put("Status", status);
			response.put("Msg","Error in delete. Invalid plan id");
			response.put("Error", e.getLocalizedMessage());
			log.error("deletePlan : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/generatereferrallink/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse generateReferralLink( @PathVariable String auth,@RequestParam(value ="type",defaultValue = "", required = false) String type,
			@RequestParam(value ="imgname",defaultValue = "", required = false) String imgname) {
		JResponse response = new JResponse();

		try {
			User user = null;
			String imgpath = "";
			try {
				user = userService.verifyAuthKey(auth);
			}catch (Exception e) {
				log.error("generateReferralLink : ",e.getLocalizedMessage());
			}

			if(user != null) {
				String ref_url = referralurl+user.getEmail();
				ReferralCredits ref = refService.getLatestReferralCredits();

				if(!imgname.trim().isEmpty() && !type.trim().isEmpty()) {
					AppImage appimage = advService.getAppImages(type, imgname);
					imgpath = appimage.getImg_path();
				}

				if(useReferralCandy) {
					for(int i=0;i<3;i++) {
						try {
							String referralCandyResponse = helper.signUpInReferralCandy(user.getFirstname(), user.getLastname(), user.getEmail(), referralCandySecretKey, referralCandyAccessKey);
							JSONObject JObj = new JSONObject(referralCandyResponse.toString());
							String status = (String) JObj.get("message");
							String referralcorner_url = (String) JObj.get("referralcorner_url");
							ref_url = (String) JObj.get("referral_link");
							log.info("status : "+status+"\n referralcorner_url : "+referralcorner_url+"\n referral_link : "+ref_url);
							break;
						}catch(Exception e) {
							log.error("Error while getting sign Up In Referral Candy : "+e.getLocalizedMessage());
						}
					}
				}				

				if(ref != null) {
					response.put("Status", 1);
					response.put("referallink", ref_url);
					response.put("offerone", ref.getAdvocate_msg());		
					response.put("offertwo", ref.getReferral_msg());
					response.put("messagecontent", ref.getTitle());
					response.put("referalcode",user.getEmail());
					response.put("imgpath",imgpath);
					response.put("weblinkflag", weblinkflag);
					response.put("weblinkurl", weblinkurl);
				}
				else
				{
					response.put("Status", 0);
					response.put("Msg", "Referral Credits not found");
				}
			}
			else {
				response.put("Status", 0);
				response.put("Msg", "Invalid User");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while generating referral link");
			response.put("Error", e.getLocalizedMessage());
			log.error("generate Referral Link:" + e.getMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/generatewebreferrallink", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse generateWebReferralLink( @RequestParam String email) {
		JResponse response = new JResponse();

		try {
			User user = null;

			try {
				user = userService.getUserByUNameOrEmail(email);
			}catch (Exception e) {
				log.error("generateWebReferralLink : ",e.getLocalizedMessage());
			}

			if(user != null) {
				String ref_url = referralurl+user.getChargebeeid();
				ReferralCredits ref = refService.getLatestReferralCredits();
				if(ref != null) {
					response.put("Status", 1);
					response.put("referallink", ref_url);
					response.put("offerone", ref.getAdvocate_msg());		
					response.put("offertwo", ref.getReferral_msg());
					response.put("messagecontent", ref.getTitle());
					response.put("referalcode",user.getChargebeeid());
				}
				else
				{
					response.put("Status", 0);
					response.put("Msg", "Referral Criteria Record not found");
				}
			}
			else {
				response.put("Status", 0);
				response.put("Msg", "Invalid User");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while generating web referral link");
			response.put("Error", e.getLocalizedMessage());
			log.error("generate Web Referral Link:" + e.getMessage());
		}

		return response;
	}

	@RequestMapping(value="v3.0/listcredittype/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse listCreditType(@PathVariable String autho)
	{
		JResponse response = new JResponse();
		try {
			List<CreditType> credittypeList = refService.listCreditType();

			response.put("credittypeList", credittypeList);
			response.put("Status", 1);
			response.put("Msg","Success");

		}catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg","Error occured in list");
			response.put("Error", e.getLocalizedMessage());
			log.error("listReferralCredits : " + e.getLocalizedMessage());
		}

		return response;
	}
}
