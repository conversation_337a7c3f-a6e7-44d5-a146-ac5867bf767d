package com.nimble.irisservices.controller;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.CompanyConfigResponse;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICompanyServiceV4;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class CompanyControllerV4 {

	@Value("${verificationtime}")
	private long verifytime;

	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;
	
	private static final Logger log = LogManager.getLogger(CompanyControllerV4.class);

	@Autowired
	@Lazy
	ICompanyService companyService;
	
	@Autowired
	@Lazy
	ICompanyServiceV4 companyServicev4;
	
	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;

	
	//v4.0/company/  -  SIV
		@RequestMapping(value = "v4.0/company/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
		@ResponseBody
		public JResponse getCompanyById_V4(@PathVariable String autho,
				@RequestParam(value = "os", defaultValue = "", required = false) String os,
				@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
			JResponse response = new JResponse();

			log.info("Entering  getCompanyById_V4 : "+autho);

			try {
				UserV4 user = null;
				try {
					user = userServiceV4.verifyAuthV4("authkey",autho);
				}catch(InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for authkey : "+autho);
					return response;
				}
				if(user != null) {
					long cmpId = user.getCmpId();
					Company cmp = companyService.getCompany(cmpId);
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("company", cmp);
				}else {
					log.error("Invalid Auth key ");
					response.put("Status", 0);
					response.put("Msg", "Invalid Authkey");
					return response;
				}

			} catch (Exception e) {
				log.error("getCompanyById_V4 : "+e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Exception occured");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}
			return response;
		}

		//saveCompanyConfigV4 - by anand
		@RequestMapping(value = "v4.0/companyconfig/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
		public @ResponseBody JResponse saveCompanyConfigV4(@PathVariable String autho, @RequestParam("temperatureunit") String temperatureunit,
				@RequestParam("cmpcfgid") String cmpcfgid,@RequestParam("cmpid") String cmpid, @RequestParam(value="sttime", defaultValue = "", required = false) String stTime,
				@RequestParam(value = "os", defaultValue = "", required = false) String os,
				@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
			JResponse response = new JResponse();
			log.info("Entering saveCompanyConfigV4 : "+autho);
			try {
				UserV4 usr = userServiceV4.verifyAuthV4( "authkey",autho);
				if (usr==null)
				{
					response.put("Status", 0);
					response.put("Msg", "Invalid AuthKey");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				if (cmpid.isEmpty())
					cmpid = usr.getCmpId()+"";

				companyServicev4.updateCompanyCfg(cmpid,cmpcfgid,temperatureunit);
				response.put("Status", 1);
				response.put("Msg", "Success");
			}catch (Exception e) {
				log.error("Exception : saveCompanyConfigV4 :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "UnExcepted Error in Companyconfig");
				response.put("Error", e.getLocalizedMessage());
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}


		//getCompanyConfigV4 - By Anand
		@RequestMapping(value = "v4.0/companyconfig/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
		public @ResponseBody JResponse getCompanyConfigV4(@PathVariable String autho, @RequestParam(value="sttime", defaultValue = "", required = false) String stTime,
				@RequestParam(value = "os", defaultValue = "", required = false) String os,
				@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
			JResponse response = new JResponse();
			log.info("Entering getCompanyConfig : "+autho);
			try {
				UserV4 user = null;
				try {
					user = userServiceV4.verifyAuthV3("authkey",autho);
				}catch(InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for username : "+autho);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				CompanyConfigResponse  cmp_cfg = companyServicev4.getCompanyConfigAndCompany(user.getCmpId());

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("companyconfig", cmp_cfg);
				if (cmp_cfg.getCmptype_id() == 3) {
					boolean registerProduct = true;				
					if ((user.getUsername().matches("[0-9]+") && user.getUsername().length() == 6))
					{
						List<JGateway> gateways = gatewayServiceV4.getGatewayV4(null, null, null, null, user.getId(),null);

						if (gateways.size() > 1 || gateways.size() < 1) {
							registerProduct = true;
						} else if (gateways.size() == 1) {
							JGateway gat = gateways.get(0);
							registerProduct = niomDbservice.isMeidMappedInOrdermap(gat.getMeid());
						}
					}

					String userStatus = "0";
					try {
						Calendar curCal = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
						DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
						Date date = sdf.parse(user.getCreatedOn());
						Calendar createdCal = Calendar.getInstance();
						createdCal.setTime(date);
						long timeMills = curCal.getTimeInMillis()-createdCal.getTimeInMillis();
						long verifyMills = verifytime * 60 * 60 * 1000;
						//user verification time - config parameter from properties file
						if( user.isVerified()==true) {
							userStatus = "1";
						}
						else if(timeMills<verifyMills && (user.isVerified() == false)) {
							userStatus = "1" ;	
						}				
						else if(timeMills>verifyMills &&( user.isVerified() == false)) {
							userStatus = "1";
						}
					}catch(Exception e) {
						log.error("getCompanyConfig : date calc " + e.getLocalizedMessage());
					}
					response.put("notificationStatus", user.isNotification());
					response.put("isProductRegistered", registerProduct);
					response.put("userverificationStatus", userStatus);
				}
			} catch (Exception e) {
				log.error("Exception : getCompanyConfigV4 :" +e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Error Occur");
				response.put("Error", e.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

	// Used in web
	// Get company List By Filter - Savitha
	@RequestMapping(value = "v4.0/companylistbyfilter/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCompanyListByFilter(@PathVariable String autho,
			@RequestParam(value = "skey", required = false) String sKey,
			@RequestParam(value = "svalue", required = false) String sValue,
			@RequestParam(value = "ftype", defaultValue = "equals", required = false) String fType,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "desc", required = false) String otype,
			@RequestParam(value = "okey", required = false) String oKey,
			@RequestParam(value = "cmpid", required = false) String cmpid) {

		JResponse response = new JResponse();
		try {
			if (cmpid != null && cmpid != "") {
				UserV4 user = null;
				long cmp_id;
				try {
					user = userServiceV4.verifyAuthV4("authkey", autho);
					cmp_id = user.getCmpId();
				} catch (InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "Invalid Authkey!!!");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting company for company id : " + autho);
					return response;
				}
				if (Long.parseLong(cmpid) != cmp_id) {
					response.put("Status", 0);
					response.put("Msg", "Invalid company id!!!");
					return response;
				}
			}

			response = companyServicev4.getCompanyListByFilter(sKey, sValue, fType, otype, offset, limit, oKey, cmpid,
					response);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting company list by fFilter");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : CompanyListByFilter : " + e.getLocalizedMessage());
			return response;
		}
		return response;
	}
	
	// Used in web
	// Get companyconfig web - Savitha
	@RequestMapping(value = "v4.0/companyconfigweb/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getCompanyConfigListWeb(@PathVariable String autho,
			@RequestParam("cmpid") String cmpid) {
		JResponse response = new JResponse();

		try {
			UserV4 user;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for auth : " + autho);
				return response;
			}

			long cmp_id = user.getCmpId();
			if (!cmpid.isEmpty())
				cmp_id = Long.valueOf(cmpid);

			List<CompanyConfig> cmp_cfglist = companyServicev4.getCompanyConfigListWeb(cmp_id);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("companyconfig", cmp_cfglist);

		} catch (Exception e) {
			log.error("Exception : Company config list : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured!!!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}
	
}
