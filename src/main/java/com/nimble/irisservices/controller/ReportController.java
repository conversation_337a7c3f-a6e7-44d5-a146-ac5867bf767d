package com.nimble.irisservices.controller;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.zip.GZIPOutputStream;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.gson.Gson;
import com.nimble.irisservices.dto.DeviceSummary;
import com.nimble.irisservices.dto.JAssetDescription;
import com.nimble.irisservices.dto.JAssetLastReport;
import com.nimble.irisservices.dto.JAssetReport;
import com.nimble.irisservices.dto.JDeviceReport;
import com.nimble.irisservices.dto.JGatewayReport;
import com.nimble.irisservices.dto.JNodeReport;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSreiAssetLastReport;
import com.nimble.irisservices.dto.JSubscritionDeviceReport;
import com.nimble.irisservices.dto.JTempReport;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.Orders;
import com.nimble.irisservices.entity.Subscription;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.exception.InvalidGroupNameException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.IDeviceSubscriptionService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class ReportController {

	private static final Logger log = LogManager.getLogger(ReportController.class);

	@Autowired
	@Lazy
	IReportService reportService;

	@Autowired
	@Lazy
	IUserService userService;
	
	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IDeviceSubscriptionService deviceSubscription;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Value("${niomip}")
	private String niomIP;

	@Value("${niomauthkey}")
	private String niomAuthKey;

	// Used in listener check tool
	// ========get lastgatewayreport ================
	@RequestMapping(value = "v3.0/gatewaysummary/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetSummary(@PathVariable String autho, @RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("offset") String offset,
			@RequestParam("limit") String limit,
			@RequestParam(value = "zip", defaultValue = "1", required = false) String zip) {

		//System.out.println("gateway summary");
		log.info("called gateway summary services" + "groupid: " + groupid + ", subgroupid: " + subgroupid
				+ ", assetgroupid: " + assetgroupid + " , gatewayid: " + gatewayid);
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());
			List<JAssetLastReport> reportsummmary = reportService.getLastgatewayreport(groupid, subgroupid,
					assetgroupid, gatewayid, user.getId(), offset, limit, cmp_cfg.getTemperatureunit(), "");
			log.info("received gateway summary , report length: " + reportsummmary.size());

			JAssetDescription assetDescrip = reportService.getAssetDescription(user.giveCompany().getId());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetdescription", assetDescrip);

			/* To reverse the collection of latest reported gateway */
			Collections.sort(reportsummmary, new Comparator<JAssetLastReport>() {
				@Override
				public int compare(JAssetLastReport a, JAssetLastReport b) {
					DateFormat format = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
					try {
						return format.parse(b.getDatetime()).compareTo(format.parse(a.getDatetime()));
					} catch (ParseException e) {
						log.error("getAssetSummary:"+e.getLocalizedMessage());
					}
					return 0;
				}
			});

			/* If zip params is 1.Report is zipped */
			if (zip.equalsIgnoreCase("1"))
				response.put("lastgatewayreport", zipContent(reportsummmary));
			else
				response.put("lastgatewayreport", reportsummmary);
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (IOException e) {
			log.error(e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;
		}
		return response;
	}

	// ========get lastnodereport ================
	@RequestMapping(value = "v3.0/nodesummary/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getNodeSummary(@PathVariable String autho, @RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("nodeid") String nodeid,
			@RequestParam("offset") String offset, @RequestParam("limit") String limit,
			@RequestParam(value = "zip", defaultValue = "1", required = false) String zip) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());

			List<JNodeReport> reportsummmary = reportService.getLastNodereport(groupid, subgroupid, assetgroupid,
					gatewayid, nodeid, user.getId(), offset, limit, cmp_cfg.getTemperatureunit());

			response.put("Status", 1);
			response.put("Msg", "Success");

			/* To reverse the collection of latest reported gateway */
			Collections.sort(reportsummmary, new Comparator<JNodeReport>() {
				@Override
				public int compare(JNodeReport a, JNodeReport b) {
					DateFormat format = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
					try {
						return format.parse(b.getDatetime()).compareTo(format.parse(a.getDatetime()));
					} catch (ParseException e) {
						log.error("format date:"+e.getLocalizedMessage());
					}
					return 0;
				}
			});

			/* If zip params is 1.Report is zipped */
			if (zip.equalsIgnoreCase("1"))
				response.put("lastnodereport", zipContent(reportsummmary));
			else
				response.put("lastnodereport", reportsummmary);
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (IOException e) {
			log.error(e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;
		}
		return response;
	}

	// Used in web
	// ========get gatewayreport ================
	@RequestMapping(value = "v3.0/gatewayreport/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetReport(@PathVariable String autho, @RequestParam("fromtime") String fromtime,
			@RequestParam("totime") String totime, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("offset") String offset,
			@RequestParam("limit") String limit,
			@RequestParam(value = "zip", defaultValue = "1", required = false) String zip) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());

			List<JAssetReport> reportsummmary = reportService.getgatewayreport(fromtime, totime, assetgroupid,
					gatewayid, user.giveCompany().getId(), offset, limit, cmp_cfg.getTemperatureunit());
			log.info("received gateway summary , report length: " + reportsummmary.size());

			response.put("Status", 1);
			response.put("Msg", "Success");

			/* If zip params is 1.Report is zipped */
			if (zip.equalsIgnoreCase("1"))
				response.put("gatewayreports", zipContent(reportsummmary));
			else
				response.put("gatewayreports", reportsummmary);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (IOException e) {
			// TODO Auto-generated catch block
			log.error(e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;
		}

		return response;

	}

	// ========get nodereport ================
	@RequestMapping(value = "v3.0/nodereport/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getNodeReport(@PathVariable String autho, @RequestParam("fromtime") String fromtime,
			@RequestParam("totime") String totime, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("nodeid") String nodeid,
			@RequestParam("offset") String offset, @RequestParam("limit") String limit,
			@RequestParam(value = "zip", defaultValue = "1", required = false) String zip) {
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());

			List<JNodeReport> reportsummmary = reportService.geNodereport(fromtime, totime, assetgroupid, gatewayid,
					nodeid, user.giveCompany().getId(), offset, limit, cmp_cfg.getTemperatureunit());

			response.put("Status", 1);
			response.put("Msg", "Success");

			/* If zip params is 1.Report is zipped */
			if (zip.equalsIgnoreCase("1"))
				response.put("nodereports", zipContent(reportsummmary));
			else
				response.put("nodereports", reportsummmary);
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (IOException e) {
			// TODO Auto-generated catch block
			log.error(e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;
		}
		return response;

	}

	// Used in web
	// ========get lastgatewayreport for srei ================
	@RequestMapping(value = "v3.0/sreigatewaysummary/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSreiAssetSummary(@PathVariable String autho, @RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("deliquencystatus") String deliquencystatus,
			@RequestParam("levelid") String levelid, @RequestParam("offset") String offset,
			@RequestParam("limit") String limit,
			@RequestParam(value = "zip", defaultValue = "1", required = false) String zip) {
		//System.out.println("gateway summary");
		log.info("called gateway summary services" + "groupid: " + groupid + ", subgroupid: " + subgroupid
				+ ", assetgroupid: " + assetgroupid + " , gatewayid: " + gatewayid);
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);
			List<JSreiAssetLastReport> reportsummmary = reportService.getSreiLastgatewayreport(groupid, subgroupid,
					assetgroupid, gatewayid, user.getId(), deliquencystatus, levelid, offset, limit);
			//System.out.println("received gateway summary , report length: " + reportsummmary.size());
			log.info("received gateway summary , report length: " + reportsummmary.size());

			JAssetDescription assetDescrip = reportService.getAssetDescription(user.giveCompany().getId());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetdescription", assetDescrip);

			/* To reverse the collection of latest reported gateway */
			Collections.sort(reportsummmary, new Comparator<JSreiAssetLastReport>() {
				@Override
				public int compare(JSreiAssetLastReport a, JSreiAssetLastReport b) {
					DateFormat format = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
					try {
						return format.parse(b.getDatetime()).compareTo(format.parse(a.getDatetime()));
					} catch (ParseException e) {
						log.error("getsrei:"+e.getLocalizedMessage());
					}
					return 0;
				}
			});

			if (zip.equalsIgnoreCase("1"))
				response.put("lastgatewayreport", zipContent(reportsummmary));
			else
				response.put("lastgatewayreport", reportsummmary);
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (IOException e) {
			log.error(e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;
		}
		return response;
	}

	// ========get lastgatewayreport for srei is not implement ================
	@RequestMapping(value = "v3.0/cmpsreigatewaysummary/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSreiAssetSummaryByCmp(@PathVariable String autho, @RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("deliquencystatus") String deliquencystatus,
			@RequestParam("levelid") String levelid) {
		//System.out.println("gateway summary");
		log.info("called gateway summary services" + "groupid: " + groupid + ", subgroupid: " + subgroupid
				+ ", assetgroupid: " + assetgroupid + " , gatewayid: " + gatewayid);
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);
			List<JSreiAssetLastReport> reportsummmary = reportService.getSreiLastgatewayreportByCmp(groupid, subgroupid,
					assetgroupid, gatewayid, user.giveCompany().getId(), deliquencystatus, levelid);
			log.info("received gateway summary , report length: " + reportsummmary.size());

			JAssetDescription assetDescrip = reportService.getAssetDescription(user.giveCompany().getId());
			byte[] zippedSummary = zipContent(reportsummmary);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("lastgatewayreport", zippedSummary);
			response.put("assetdescription", assetDescrip);
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (IOException e) {
			// TODO Auto-generated catch block
			log.error(e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;

		}
		return response;

	}

	

	
	@RequestMapping(value = "v3.0/gatewaysummaryTest/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetSummaryTest(@PathVariable String autho, @RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("offset") String offset,
			@RequestParam("limit") String limit) {
		log.info("called gateway summary services" + "groupid: " + groupid + ", subgroupid: " + subgroupid
				+ ", assetgroupid: " + assetgroupid + " , gatewayid: " + gatewayid);

		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());
			List<JAssetLastReport> reportsummmary = reportService.getLastgatewayreport(groupid, subgroupid,
					assetgroupid, gatewayid, user.getId(), offset, limit, cmp_cfg.getTemperatureunit(), "1");
			log.info("received gateway summary , report length: " + reportsummmary.size());

			JAssetDescription assetDescrip = reportService.getAssetDescription(user.giveCompany().getId());

			/* To reverse the collection of latest reported gateway */
			Collections.sort(reportsummmary, new Comparator<JAssetLastReport>() {
				@Override
				public int compare(JAssetLastReport a, JAssetLastReport b) {
					DateFormat format = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
					try {
						return format.parse(b.getDatetime()).compareTo(format.parse(a.getDatetime()));
					} catch (ParseException e) {
						log.error(e.getMessage());
					}
					return 0;
				}
			});
						
			/*
			 * byte[] zippedSummary = zipContent(reportsummmary);
			 */ 
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("lastgatewayreport", reportsummmary);
			response.put("assetdescription", assetDescrip);
		} catch (InvalidAuthoException e) {
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} 
		catch (Exception e) { 
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg",e.getLocalizedMessage());
			return response;  
		}
			 
		return response;

	}
	
	// ========get gatewayreport Test ================
	@RequestMapping(value = "v3.0/gatewayreportTest/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetReportTest(@PathVariable String autho, @RequestParam("fromtime") String fromtime,
			@RequestParam("totime") String totime, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("offset") String offset,
			@RequestParam("limit") String limit) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());
			List<JAssetReport> reportsummmary = reportService.getgatewayreport(fromtime, totime, assetgroupid,
					gatewayid, user.giveCompany().getId(), offset, limit, cmp_cfg.getTemperatureunit());
			log.info("received gateway summary , report length: " + reportsummmary.size());

			/*
			 * byte[] zippedSummary = zipContent(reportsummmary);
			 */ response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("gatewayreports", reportsummmary);
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} 
		return response;

	}

	// ========Test http connection ================
	@RequestMapping(value = "v3.0/testHttpConnection", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse testHttpConnection(@RequestParam String reports) {
		JResponse response = new JResponse();
		response.put("Data", reports);
		response.put("Status", 1);
		response.put("Msg", "Success");
		return response;

	}

	public static byte[] zipContent(Object obj) throws IOException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		GZIPOutputStream gzipOut = new GZIPOutputStream(baos);

		Gson gson = new Gson();
		String json = gson.toJson(obj);
		gzipOut.write(json.getBytes());

		gzipOut.close();
		// objectOut.close();
		byte[] bytes = baos.toByteArray();

		return bytes;
	}

	// ========get gatewayreport Count================
	@RequestMapping(value = "v3.0/grptcnt/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetReportCount(@PathVariable String autho, @RequestParam("fromtime") String fromtime,
			@RequestParam("totime") String totime, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			long reportsummmaryCount = reportService.getgatewayreportCount(fromtime, totime, assetgroupid, gatewayid,
					user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("GReportCount", reportsummmaryCount);
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}

	// ========get nodereport Count================
	@RequestMapping(value = "v3.0/nrptcnt/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getNodeReportCount(@PathVariable String autho, @RequestParam("fromtime") String fromtime,
			@RequestParam("totime") String totime, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("nodeid") String nodeid) {
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);
			long reportsummmaryCount = reportService.getNodereportCount(fromtime, totime, assetgroupid, gatewayid,
					nodeid, user.giveCompany().getId());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("NReportCount", reportsummmaryCount);
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}

	// ========get lastgatewayreport for TVS customer================
	@RequestMapping(value = "v3.0/devicesummary/{groupname}/{autho}", method = RequestMethod.GET, headers = "Accept=application/xml")
	@ResponseBody
	public DeviceSummary getAssetSummary_2(@PathVariable String autho, @PathVariable("groupname") String groupname) {
		//System.out.println("gateway summary");
		DeviceSummary devicesummary = new DeviceSummary();
		boolean isMobileApp = false;
		try {

			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());
			List<JDeviceReport> jdevrpt = reportService.getDeviceSummary(groupname, "", "", "", user.getId(), "", "",
					cmp_cfg.getTemperatureunit(), user.giveCompany().getId(), isMobileApp);
			devicesummary.setDevicereport(jdevrpt);
			devicesummary.setStatus("Success");
			return devicesummary;

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			devicesummary.setStatus("invalid authentication key");
			return devicesummary;
		} catch (InvalidGroupNameException e) {
			devicesummary.setStatus("Invalid group name");
			return devicesummary;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			devicesummary.setStatus("Invalid group name/invalid authentication key ");
			return devicesummary;
		}
	}

	@RequestMapping(value = "v3.0/getdevicesummaryv3/{groupname}/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetSummary(@PathVariable String autho, @PathVariable("groupname") String groupname) {
		JResponse response = new JResponse();
		//System.out.println("gateway summary json response");

		Helper _helper = new Helper();

		boolean isMobileApp = true;

		boolean setupActivate = false;

		String upgradeSubscriptionUrl = _helper.getExternalConfigValue("upgradesubscriptionurl", externalConfigService);

		String activateSubscriptionUrl = _helper.getExternalConfigValue("activatesubscriptionurl",
				externalConfigService);

		try {
			User user = userService.verifyAuthKey(autho);

			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());
			List<JDeviceReport> jdevrpt = reportService.getDeviceSummaryV2("", "", "", "", user.getId(), "", "",
					cmp_cfg.getTemperatureunit(), user.giveCompany().getId(), isMobileApp);

			List<JSubscritionDeviceReport> subDeviceReportList = new ArrayList<JSubscritionDeviceReport>();

			if (jdevrpt.size() > 0) {

				String niomGetSubURL = "";

				// Properties prop = new Properties();

				for (JDeviceReport deviceReport : jdevrpt) {

					JSubscritionDeviceReport subDeviceReport = new JSubscritionDeviceReport();
					subDeviceReport.setId(deviceReport.getId());
					subDeviceReport.setMeid(deviceReport.getMeid());
					subDeviceReport.setAssetid(deviceReport.getAssetid());
					subDeviceReport.setReportdatetime(deviceReport.getReportdatetime());
					subDeviceReport.setTimeoffset(deviceReport.getTimeoffset());
					subDeviceReport.setGpsstatus(deviceReport.getGpsstatus());
					subDeviceReport.setLat(deviceReport.getLat());
					subDeviceReport.setLatdir(deviceReport.getLatdir());
					subDeviceReport.setLon(deviceReport.getLon());
					subDeviceReport.setLondir(deviceReport.getLondir());
					subDeviceReport.setHeading(deviceReport.getHeading());
					subDeviceReport.setBattery(deviceReport.getBattery());
					subDeviceReport.setProbetype(deviceReport.getProbetype());
					subDeviceReport.setTemperature(deviceReport.getTemperature());
					subDeviceReport.setSpeed(deviceReport.getSpeed());
					subDeviceReport.setDistance(deviceReport.getDistance());
					subDeviceReport.setTdisplayunit(deviceReport.getTdisplayunit());
					subDeviceReport.setEventid(deviceReport.getEventid());
					subDeviceReport.setSignalstrength(deviceReport.getSignalstrength());

					// subDeviceReportList.add(subDeviceReport);
					Subscription subDetails = new Subscription();
					try {
						/* load a properties file */
						// prop.load(new FileInputStream("iris3.properties"));
						// niomGetSubURL =
						// prop.getProperty("niomGetSubscriptionURL");
						//// String niomResponse = _helper.getURL(niomGetSubURL
						// + "?meid=" + deviceReport.getMeid());

						// String niomResponse =
						// _helper.getURL(niomIP+"v1.0/getdevicesubscription?meid="
						// + "359486065167999");

						String niomResponse = _helper
								.getURL(niomIP + "v1.0/getdevicesubscription?meid=" + deviceReport.getMeid());
						Gson gson = new Gson();
						JSONObject nresponse = new JSONObject(niomResponse.toString());
						JSONObject jresponse = new JSONObject();

						jresponse = nresponse.getJSONObject("response");
						int status = jresponse.getInt("Status");
						String msg = jresponse.getString("Msg");
						String qrcCode = null;
						if (!jresponse.isNull("Qrccode")) {
							qrcCode = jresponse.getString("Qrccode");
						}

						subDeviceReport.setQrcCode(qrcCode);

						if (status > 0 ? true : false) {
							subDeviceReport.setSubcriptionStatus("true");
							subDeviceReport.setSubscriptionMessage("Subscription details found");

							subDetails = gson.fromJson(jresponse.getJSONObject("subscription").toString(),
									Subscription.class);

							if (subDetails.getBillingPeriod().toLowerCase().equals("year")) {
								subDetails.setBillingPeriod("Yearly");
							}

							if (subDetails.getBillingPeriod().toLowerCase().contains("month")) {
								subDetails.setBillingPeriod("Monthly");
							}

							if (subDetails.getBillingPeriod().toLowerCase().contains("half")) {
								subDetails.setBillingPeriod("Half-Yearly");
							}

							if (subDetails.getBillingPeriod().toLowerCase().contains("quarter")) {
								subDetails.setBillingPeriod("Quarterly");
							}

							if (subDetails.getStatus().equalsIgnoreCase("active")) {
								SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
								Date dateobj = new Date();

								Date nextPaymentDate = df.parse(subDetails.getNextPaymentDate());
								Date todayDate = df.parse(df.format(dateobj));
								long difference = nextPaymentDate.getTime() - todayDate.getTime();
								int daysBetween = (int) (difference / (1000 * 60 * 60 * 24));

								subDeviceReport.setDaysRemaining(Integer.toString(daysBetween));
								if (daysBetween < 0) {
									subDeviceReport.setDaysRemaining("-1");
									subDetails.setStatus("expired");
								}

							} else {
								subDeviceReport.setDaysRemaining("-1");
							}

							if (!subDetails.getStatus().equalsIgnoreCase("active")
									&& (subDetails.getOrderId() > 90000000 || subDetails.getWoocomSubId() > 90000000))

							{

								subDeviceReport.setSetupActivate(false);
							} else {
								subDeviceReport.setSetupActivate(false);
							}

							if (subDetails.getStatus().equalsIgnoreCase("active")
									&& (subDetails.getOrderId() > 90000000 || subDetails.getWoocomSubId() > 90000000))

							{

								String autoRenewalUrl = _helper.getExternalConfigValue("autorenewalurl",
										externalConfigService);
								String autoRenewalDefaultUrl = _helper.getExternalConfigValue("autorenewaldefaulturl",
										externalConfigService);

								subDeviceReport.setSetupAutoRenewal(true);

								if (!jresponse.isNull("Orders")) {

									if (qrcCode == null) {
										qrcCode = "";
									}

									String nextPaymentDate = null;
									if (!jresponse.isNull("nextPaymentDate")) {
										nextPaymentDate = jresponse.getString("nextPaymentDate");
									} else {
										nextPaymentDate = "";
									}

									String purchaseDate = null;
									if (!jresponse.isNull("purchaseDate")) {
										purchaseDate = jresponse.getString("purchaseDate");
									} else {
										purchaseDate = "";
									}

									Orders order = new Orders();
									String orderChannel = "";
									order = gson.fromJson(jresponse.getJSONObject("Orders").toString(), Orders.class);

									String orderId = "";
									if (order.getOrder_acc_type().getAcc_type().contains("rv")) {
										orderChannel = "RVPetSafety.com";
										orderId = Long.toString(order.getOrder_id());
									} else if (order.getOrder_acc_type().getAcc_type().contains("amazon")) {
										orderChannel = "Amazon";

										if (!order.getExternal_order_id().equalsIgnoreCase("NA")) {
											orderId = order.getExternal_order_id();
										}
									} else if (order.getOrder_acc_type().getAcc_type().contains("facebook")) {
										orderChannel = "Facebook";
										if (!order.getExternal_order_id().equalsIgnoreCase("NA")) {
											orderId = order.getExternal_order_id();
										}
									} else {
										orderChannel = order.getOrder_acc_type().getAcc_type();
										if (!order.getExternal_order_id().equalsIgnoreCase("NA")) {
											orderId = order.getExternal_order_id();
										}
									}

									String firstName = "";

									if (!order.getBilling_first_name().equalsIgnoreCase("NA")) {
										firstName = order.getBilling_first_name();
									}

									String lastName = "";

									if (!order.getBilling_last_name().equalsIgnoreCase("NA")) {
										lastName = order.getBilling_last_name();
									}

									String phone = "";
									String email = "";

									if (!order.getBilling_email().equalsIgnoreCase("NA")) {
										email = order.getBilling_email();
									}

									if (!order.getBilling_phone().equalsIgnoreCase("NA")) {
										phone = order.getBilling_phone();
									}
									String parameters = "?f_name=" + firstName + "&l_name=" + lastName + "&orderId="
											+ orderId + "&email=" + email + "&phone=" + phone + "&purchasedfrom="
											+ orderChannel + "&qrc=" + qrcCode + "&paymentdate=" + nextPaymentDate
											+ "&purchasedate=" + purchaseDate;

									subDeviceReport.setAutoRenewalURL(autoRenewalUrl + parameters);

								} else {
									subDeviceReport.setAutoRenewalURL(autoRenewalDefaultUrl);
								}

							} else {

								subDeviceReport.setSetupAutoRenewal(false);

							}

							subDeviceReport.setUpgradeSubscriptionUrl(upgradeSubscriptionUrl);
							subDeviceReport.setActivateSubscriptionUrl(activateSubscriptionUrl);

							subDeviceReport.setSub(subDetails);
						} else {
							subDeviceReport.setUpgradeSubscriptionUrl(upgradeSubscriptionUrl);
							subDeviceReport.setActivateSubscriptionUrl(activateSubscriptionUrl);
							subDeviceReport.setSetupAutoRenewal(false);
							subDeviceReport.setSetupActivate(false);
							subDeviceReport.setSubcriptionStatus("false");
							subDeviceReport.setSubscriptionMessage(
									"Invalid MEID or Subscription Details not found for respective MEID");
							subDeviceReport.setDaysRemaining("-1");
							subDeviceReport.setSub(null);
						}

					} catch (IOException ex) {
						subDeviceReport.setUpgradeSubscriptionUrl(upgradeSubscriptionUrl);
						subDeviceReport.setActivateSubscriptionUrl(activateSubscriptionUrl);

						subDeviceReport.setSubcriptionStatus("false");
						subDeviceReport.setSetupActivate(false);
						subDeviceReport
								.setSubscriptionMessage("Exception while reading niom URL from iris3.propertiesfile");
						log.error("Exception while reading niom URL from iris3.propertiesfile");
						subDeviceReport.setDaysRemaining("-1");
						subDeviceReport.setSetupAutoRenewal(false);
						subDeviceReport.setSub(null);
					} catch (Exception e) {
						subDeviceReport.setUpgradeSubscriptionUrl(upgradeSubscriptionUrl);
						subDeviceReport.setActivateSubscriptionUrl(activateSubscriptionUrl);
						log.error("Exception while getting response from niom"+e.getLocalizedMessage());
						subDeviceReport.setSubcriptionStatus("false");
						subDeviceReport.setSetupActivate(false);
						subDeviceReport.setSubscriptionMessage("Exception while getting response from niom");
						subDeviceReport.setDaysRemaining("-1");
						subDeviceReport.setSetupAutoRenewal(false);
						subDeviceReport.setSub(null);
					}

					subDeviceReportList.add(subDeviceReport);
				}
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("DeviceSummary", subDeviceReportList);
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No Devices are configured for users");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (InvalidGroupNameException e) {
			response.put("Status", 0);
			response.put("Msg", "Invalid group name");
			return response;

		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid group name/invalid authentication key");
			return response;
		}
	}

	@RequestMapping(value = "v3.0/getsubscription/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getsubscription(@PathVariable String autho, @RequestParam("meid") String meid) {
		JResponse response = new JResponse();
		//System.out.println("gateway summary json response");

		Helper _helper = new Helper();

		try {
			User user = userService.verifyAuthKey(autho);

			Subscription subDetails = null;

			String niomGetSubURL = "";

			Properties prop = new Properties();

			try {
				/* load a properties file */
				// prop.load(new FileInputStream("iris3.properties"));
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));
				niomGetSubURL = prop.getProperty("niomGetSubscriptionURL");
			} catch (IOException ex) {
				log.error("Exception while reading niom URL from iris3.propertiesfile");
			}

			String niomResponse = _helper.getURL(niomGetSubURL + "?meid=" + meid);

			Gson gson = new Gson();
			JSONObject nresponse = new JSONObject(niomResponse.toString());

			JSONObject jresponse = nresponse.getJSONObject("response");
			int status = jresponse.getInt("Status");

			String msg = jresponse.getString("Msg");
			subDetails = gson.fromJson(jresponse.getJSONObject("subscription").toString(), Subscription.class);

			response.put("Status", status);
			response.put("Msg", msg);
			response.put("Subscription", subDetails);
			return response;
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error Response From NIOM webservice");
			return response;
		}
	}

	/* ========get lastgatewayreport Version-2 ================ */

	@RequestMapping(value = "v3.0/gatewaysummaryV2/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetSummaryV2(@PathVariable String autho, @RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("offset") String offset,
			@RequestParam("limit") String limit) {
		//System.out.println("gateway summary");
		log.info("called gateway summary services" + "groupid: " + groupid + ", subgroupid: " + subgroupid
				+ ", assetgroupid: " + assetgroupid + " , gatewayid: " + gatewayid);
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());
			List<JAssetLastReport> reportsummmary = reportService.getLastgatewayreport(groupid, subgroupid,
					assetgroupid, gatewayid, user.getId(), offset, limit, cmp_cfg.getTemperatureunit(), "");

			JAssetDescription assetDescrip = reportService.getAssetDescription(user.giveCompany().getId());

			/* To reverse the collection of latest reported gateway */
			Collections.sort(reportsummmary, new Comparator<JAssetLastReport>() {
				@Override
				public int compare(JAssetLastReport a, JAssetLastReport b) {
					DateFormat format = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
					try {
						return format.parse(b.getDatetime()).compareTo(format.parse(a.getDatetime()));
					} catch (ParseException e) {
						log.error("getassetsmry:"+e.getLocalizedMessage());
					}
					return 0;
				}
			});

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("lastgatewayreport", reportsummmary);
			response.put("assetdescription", assetDescrip);
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;

	}

	// ========get gatewayreport Version -2 ================
	@RequestMapping(value = "v3.0/gatewayreportV2/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetReportV2(@PathVariable String autho, @RequestParam("fromtime") String fromtime,
			@RequestParam("totime") String totime, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("offset") String offset,
			@RequestParam("limit") String limit) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());
			List<JAssetReport> reportsummmary = reportService.getgatewayreport(fromtime, totime, assetgroupid,
					gatewayid, user.giveCompany().getId(), offset, limit, cmp_cfg.getTemperatureunit());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("gatewayreports", reportsummmary);
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}

	// ========get gatewayreport Test ================
	@RequestMapping(value = "v3.0/pushgatewayReport", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse gatewayTestReport(@RequestBody JGatewayReport jgatewayreport) {
		JResponse response = new JResponse();
		response.put("Data", jgatewayreport);
		response.put("Status", 1);
		response.put("Msg", "Success");
		return response;
	}

	// Sample Controller to get FitPetReport for demo purpose

	@RequestMapping(value = "v3.0/getfitpetreport", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getFitPetReport() {
		JResponse response = new JResponse();
		response.put("FitPetReport", reportService.getPetFitReport());
		response.put("Status", 1);
		response.put("Msg", "Success");
		return response;
	}
	
	@RequestMapping(value = "v3.0/tempreport/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getTempReport(@PathVariable String autho, @RequestParam("rptdate") String rptdate,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("offset") String offset,
			@RequestParam("limit") String limit,
			@RequestParam(value = "zip", defaultValue = "1", required = false) String zip) {
		JResponse response = new JResponse();
		try {
			Map<String, String> map = userServiceV4.getUserId_cmpIdByAuth(autho);
			List<JTempReport> temprpt = new ArrayList<JTempReport>();
			String tempunit = "";
			
			if(!map.isEmpty()) {
				tempunit = map.get("tempunit");
				temprpt = reportService.getGatewayTempReports(rptdate, gatewayid, tempunit , offset, limit);
			}
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("temprpt", temprpt);
			response.put("tempunit", tempunit);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth: "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid auth key");
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured");
			return response;
		}

		return response;

	}

}
