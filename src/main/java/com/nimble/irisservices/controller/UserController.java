package com.nimble.irisservices.controller;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.GZIPOutputStream;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import com.nimble.irisservices.Util.SecretManagerService;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.model.AddressComponent;
import com.google.maps.model.AddressComponentType;
import com.google.maps.model.ComponentFilter;
import com.google.maps.model.GeocodingResult;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.CountryCode;
import com.nimble.irisservices.dao.IGatewayDao;
import com.nimble.irisservices.dto.ActivateUser;
import com.nimble.irisservices.dto.Configuration;
import com.nimble.irisservices.dto.ExternalLogin;
import com.nimble.irisservices.dto.JAlertCfg;
import com.nimble.irisservices.dto.JAllUser;
import com.nimble.irisservices.dto.JCreateGateway;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JGatewayUserDetails;
import com.nimble.irisservices.dto.JGroups;
import com.nimble.irisservices.dto.JRVAnswer;
import com.nimble.irisservices.dto.JRVUser;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JRvPetsafety;
import com.nimble.irisservices.dto.JUser;
import com.nimble.irisservices.dto.JUserDeviceInfo;
import com.nimble.irisservices.dto.JValidateString;
import com.nimble.irisservices.dto.SignUp;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.CompanyType;
import com.nimble.irisservices.entity.DeviceReplaced;
import com.nimble.irisservices.entity.ForceUpdate;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.Groups;
import com.nimble.irisservices.entity.Offlineuserdetails;
import com.nimble.irisservices.entity.OrderMappingDetails;
import com.nimble.irisservices.entity.RVAnswer;
import com.nimble.irisservices.entity.SignupType;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserDeviceInfo;
import com.nimble.irisservices.entity.UserToken;
import com.nimble.irisservices.entity.UserVerification;
import com.nimble.irisservices.entity.ZipCodeDetails;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.exception.InvalidUsernameException;
import com.nimble.irisservices.helper.Email;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Ordermap;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAlertCfgServiceV4;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.ICcplistService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICompanyTypeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IFetchDropdownService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IGroupServices;
import com.nimble.irisservices.service.IMailService;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IRVCentricDetailsService;
import com.nimble.irisservices.service.IRegisterUserEmailService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IThrottlingService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IZipCodeDetailsService;

import freemarker.template.Template;

@Controller
public class UserController {

	private static final Logger log = LogManager.getLogger(UserController.class);

	@Autowired
	SecretManagerService secretManagerService;

	@Value("${aws_s3_secret_name}")
	private String S3_SECRET_NAME;

	@Autowired
	ICompanyTypeService companyTypeServ;

	@Autowired
	IThrottlingService throttlingService;
	
	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IUserServiceV4 userServV4;

	@Autowired
	@Lazy
	IGatewayDao gatewayDao;

	@Autowired
	@Lazy
	IMessagingService ImsgService;

	@Autowired
	@Lazy
	ICcplistService ccpservice;

	@Autowired
	@Lazy
	IGroupServices groupservices;

	@Autowired
	@Lazy
	IFetchDropdownService fetchDropdownService;

	@Autowired
	@Lazy
	IReportService reportService;

	@Autowired
	@Lazy
	IAlertCfgServiceV4 alertCfgServiceV4;

	@Autowired
	@Lazy
	IAlertCfgService alertCfgService;
	
	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IRegisterUserEmailService registerEmailStatus;

	@Autowired
	@Lazy
	IZipCodeDetailsService iZipCodeDetailsService;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	IMailService mailService;

	@Autowired
	IRVCentricDetailsService rvServ;
	
	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;

	@Value("${niomip}")
	private String niomIP;

	@Value("${niomauthkey}")
	private String niomAuthKey;

	@Value("${verificationtime}")
	private String verificationtime;

	@Value("${google.api.key}")
	private String googleAPIkey;
	
	@Value("${enable_sku_based_vsim_activation}")
	private boolean enableSkuBasedActivation;

	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	@Autowired
	@Lazy
	private IAsyncService async;

	Helper _helper = new Helper();

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;
	
	@Value("${send_registeruseremail_simactivation_to_microservice}")
	private boolean sendActivateUserDataToSQS_Microservice;

	@Value("${add_free_vpm}")
	private boolean addFreeVpmFlag;

	@Value("${vpm_freedays}")
	private String freeVPM_days;

	@Value("${show_orderid_popup_while_register}")
	private boolean show_warranty_popup_config;

	@Value("${warranty_msg}")
	private String warranty_msg;

	@Autowired
	IRVCentricDetailsService rvcentricServ;

	@Autowired
	@Lazy
	IChargebeeService cbService;

	IrisservicesUtil irisUtil;

	@Autowired
	freemarker.template.Configuration templates;
	
	@Value("${return.login.username}")
	private String returnLoginUsername;
	
	@Value("${subscription.buynow.popup}")
	private boolean subscriptionBuyNowPopUp;
	
	@Value("${check.recall.device.qrc}")
	private boolean checkRecallQRC;
	
	@Value("${product_subs_enable}")
	private boolean product_subs_enable;	

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;
	
	@Autowired
	Email email_helper;
	
	@RequestMapping(value = "v3.0/usertest", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public String getShit(@RequestParam(value = "roleid", defaultValue = "", required = false) String roleid,
			@RequestParam(value = "zip", defaultValue = "0", required = false) String zip,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		JResponse response = new JResponse();
		async.runAsync();
		if(device_country.equalsIgnoreCase("US")|| device_country.equalsIgnoreCase("NA")
				|| device_country.toLowerCase().contains("india") || device_country.equalsIgnoreCase("in")
				|| device_country.isEmpty() || device_country == null) {
			device_country = "US";
		}
		
		String sss = supportContactNumber.get(device_country);;
		return sss;
	}

	// Used in web
	// ========get user by userId or company id================
	@RequestMapping(value = "v3.0/user/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserById(@PathVariable String autho, @RequestParam("userid") String userid,
			@RequestParam(value = "roleid", defaultValue = "", required = false) String roleid,
			@RequestParam(value = "zip", defaultValue = "0", required = false) String zip) {
		JResponse response = new JResponse();

		try {
			User user = userService.verifyAuthKey(autho);

			List<User> users = userService.getUserInRole(userid, user.giveCompany().getId(), user.getRole().getId(),
					roleid);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("users", users);

		} catch (InvalidAuthoException e) {
			log.error("InvalidAuthoException:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (InvalidUsernameException e) {
			log.error("in valid userid :" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid userid");
			return response;
		}
		return response;

	}

	@RequestMapping(value = "v3.0/getalluser/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllUser(@PathVariable String autho,
			@RequestParam(value = "zip", defaultValue = "0", required = false) String zip) {
		JResponse response = new JResponse();

		try {
			User user = userService.verifyAuthKey(autho);

			List<JAllUser> users = userService.getAllUser();

			if (users != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Error in getting all the RV users");
				return response;
			}

			/* If zip params is 1.Report is zipped */
			if (zip.equalsIgnoreCase("1")) {
				response.put("users", _helper.zipContent(users));
			} else {
				response.put("users", users);
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (IOException e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;
		}

		return response;

	}

	// ==========signup========
	@RequestMapping(value = "v3.0/signup", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse saveUser(@ModelAttribute @Valid SignUp signUp, BindingResult result) {
		JResponse response = new JResponse();
		Company company = null;
		try {
			if (signUp.getPassword() == null || signUp.getPassword().trim().isEmpty()) {
				String pwd = signUp.getPhoneno();
				pwd = pwd.replaceAll("[^\\d.]", ""); // remove other than numbers
				if (pwd.length() > 10) {
					pwd = pwd.substring(pwd.length() - 10);
				}

				signUp.setPassword(pwd);
			}
			String password = signUp.getPassword();
			signUp.setPassword( _helper.bCryptEncoder(password) );
			long timeMilli = Calendar.getInstance().getTimeInMillis();
			String cmpName = signUp.getCompanyname() + "-" + timeMilli;
			signUp.setCompanyname(cmpName);
			try {
				ThrottlingSettings throtsettings = throttlingService.getThrotSettings(signUp.getThrotsettingsid()).get(0);

				CompanyType cmpType = companyTypeServ.getCompanyType(signUp.getCmptype_id(), 1).get(0);
				company = companyService.createCompany(signUp,throtsettings,cmpType);
			} catch (Exception e) {
				log.error("Error in creating the company . Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Not able to process the request, Please try later");
				return response;
			}

			CompanyConfig cfg = new CompanyConfig(company);
			boolean companyConfigCreationStatus = companyService.saveCompanyConfg(cfg);
			log.info("Company Config Creation Status : "+companyConfigCreationStatus);
			if( !companyConfigCreationStatus && company.getId()!=0 ) {
				companyService.deleteCompany(company.getId());
			}
			
			Groups group = new Groups();
			group.setName("Default");
			boolean groupCreationStatus = groupservices.saveORupdateGroups(group, company.getId());
			if( !groupCreationStatus & company!=null ) {
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
			
			boolean res = userService.signUp(signUp,company);

			User user = userService.getUserByUNameOrEmail(signUp.getEmail());
			//updateChargebeeUser(user);
			if(user.getChargebeeid().trim().equalsIgnoreCase("NA") || user.getChargebeeid()==null)
				userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
						user.getMobileno(), user.getUsername(), 0, "NA");
			
			String cb_planid= "chum";
			if(res)
				async.updateEvalidation(user.getId(), password);
			
//			boolean stat = rvcentricServ.saveUserBadgeTxn(user.getId(), cb_planid, 0, false, "NA");
//			log.info("in signup:" + user.getId() + " Badge created:" + stat);

			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (ConstraintViolationException ce) {
			log.error("signup: ConstraintViolationException:\n" + ce.getStackTrace());
			response.put("Status", 0);
			response.put("Msg", "Username/Company name already exists");
			if( company != null && company.getId()!=0 ) {
				groupservices.deleteGroups( company.getId() );
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
		} catch (Exception e) {
			log.error("signup: " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Signup");
			if( company != null && company.getId()!=0 ) {
				groupservices.deleteGroups( company.getId() );
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
		}
		return response;
	}

	// Used in web
	// ==========save / update user========
	@RequestMapping(value = "v3.0/user/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateUser(@PathVariable String autho, @ModelAttribute @Valid JUser juser,
			BindingResult result) {

		JResponse response = new JResponse();
		try {

			/* Create or Update Validation */
			if (result.hasErrors()) {
				response.put("Status", 0);

				if (result.getFieldError("id") != null)
					response.put("Msg", "id should not be empty");
				else if (result.getFieldError("username") != null)
					response.put("Msg", result.getFieldError("username").getDefaultMessage());
				else if (result.getFieldError("password") != null)
					response.put("Msg", result.getFieldError("password").getDefaultMessage());
				else if (result.getFieldError("roleid") != null)
					response.put("Msg", "Invalid RoleId.Range between 1 and 6");
				else if (result.getFieldError("webappid") != null)
					response.put("Msg", "Invalid Webappid.Range between 1 and 2");
				else if (result.getFieldError("mobileappid") != null)
					response.put("Msg", "Invalid Mobileappid.Range between 1 and 5");
				else if (result.getFieldError("enable") != null)
					response.put("Msg", "Enable must be true or false");
				return response;
			}

			// changes done

			User user = userService.verifyAuthKey(autho);

			if (juser.getEmail().contains("null")) {
				juser.setEmail(null);
			}
			if (juser.getAlternateemail().equalsIgnoreCase("NA") || juser.getAlternateemail().isEmpty()) {
				juser.setAlternateemail(user.getAlternateemail());
			}
			if (juser.getAlternatephone().equalsIgnoreCase("NA") || juser.getAlternatephone().isEmpty()) {
				juser.setAlternatephone(user.getAlternatephone());
			}
			if (juser.getFirstname().equalsIgnoreCase("NA") || juser.getFirstname().isEmpty()) {
				juser.setFirstname(user.getFirstname());
			}
			if (juser.getLastname().equalsIgnoreCase("NA") || juser.getLastname().isEmpty()) {
				juser.setLastname(user.getLastname());
			}
			if (juser.getZipcode().equalsIgnoreCase("NA") || juser.getZipcode().isEmpty()) {
				juser.setZipcode(user.getZipcode());
			}
			if (juser.getCity().equalsIgnoreCase("NA") || juser.getCity().isEmpty()) {
				juser.setCity(user.getCity());
			}
			if (juser.getState().equalsIgnoreCase("NA") || juser.getState().isEmpty()) {
				juser.setState(user.getState());
			}
			if (juser.getCountry().equalsIgnoreCase("NA") || juser.getCountry().isEmpty()) {
				juser.setCountry(user.getCountry());
			}

			if (juser.getEmail() != null) {
				try {
					// User usr = userService.getUserByEmail(juser.getEmail());
					User usr = userService.getUserByUNameOrEmail(juser.getEmail());
					if (usr != null && !(user.getId() == usr.getId())) {

						response.put("Msg", "Email already exist. Please enter alternate Email");
						return response;
					}
				} catch (Exception ex) {

					log.error("user update failed : " + ex.getMessage());
				}
			}

			// Fix to send error response for RV guest User when updating the
			// password

			if (user.getUsername().equalsIgnoreCase("RVGuestUser")) {
				response.put("Status", 0);
				// System.out.println("Invalid**********");
				log.error("RVGuestUser : Need full access? Buy and Subscribe to RV PetSafety!");
				response.put("Msg", "Need full access? Buy and Subscribe to RV PetSafety!");
				return response;

			}

			juser.setCmp(user.giveCompany());
			juser.setGateways(user.getGateways());
			juser.setAuthKey(user.getAuthKey());

			juser.setVerified(user.isVerified());

			juser.setNotification(user.isNotification());

			if (juser.getId() == 0) {
				long cmptype_id = user.giveCompany().getCompanytype().getId();
				if (cmptype_id == 3) {
					juser.setMobileappid(3); // RvPet
				} else if (cmptype_id == 5) {
					juser.setMobileappid(4); // ResMonitor
				} else if (cmptype_id == 6) { // White Label
					juser.setMobileappid(6);
					juser.setWebappid(3);
				}
			}

			// 1- disable, 2 - iris, 3, - RvPet, 4 - ResMonitor,
			// 5-ResMonitor-Background, 6-White Label

			log.info("updateUser: company id - " + juser.giveCmp().getCompanytype().getId() + " mobileappid - "
					+ juser.getMobileappid());

			if ((juser.giveCmp().getCompanytype().getId() == 3 && juser.getMobileappid() != 3)
					|| (juser.giveCmp().getCompanytype().getId() != 3 && juser.getMobileappid() == 3) ||

					(juser.giveCmp().getCompanytype().getId() == 5
							&& ((juser.getMobileappid() != 4) && (juser.getMobileappid() != 5)))
					|| (juser.giveCmp().getCompanytype().getId() != 5
							&& ((juser.getMobileappid() == 4) || (juser.getMobileappid() == 5)))
					||

					(juser.giveCmp().getCompanytype().getId() == 6 && juser.getMobileappid() != 6)
					|| (juser.giveCmp().getCompanytype().getId() != 6 && juser.getMobileappid() == 6)) {
				response.put("Status", 0);
				log.error("updateUser:Invalid combination of company type id and mobileapp id");
				response.put("Msg", "Invalid Mobile Application. Please Change Your Company Type!");
				return response;
			}
			
			String password = juser.getPassword();
			juser.setPassword( _helper.bCryptEncoder( password ) );
			
			boolean savedRes = userService.saveOrUpdateJUser(juser);

			if(savedRes)
			{
				UserV4 getUser = userServiceV4.verifyAuthV4("email", juser.getEmail());
				async.updateEvalidation(getUser.getId(), password);
			}
			response.put("Status", 1);
			response.put("Msg", "success");

		} catch (InvalidAuthoException e) {
			log.error("updateUser :" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid authentication key");

		} catch (DataIntegrityViolationException e) {
			response.put("Status", 0);
			response.put("Msg", "Username already exits");
			log.error("updateUser :" + e.getMessage());
		}

		catch (Exception e) {
			log.error("updateUser :" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExpected Error in Create User");
		}
		return response;
	}

	// ==========assign gateways for user========
	@RequestMapping(value = "v3.0/usergateway/{userid}/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse assignUserGateway(@PathVariable String autho, @PathVariable String userid,
			@RequestParam("gatewayids") String gatewayids) {

		JResponse response = new JResponse();
		try {

			//System.out.println("gatewayIds: " + gatewayids);
			User auser = userService.verifyAuthKey(autho);
			User user = userService.getUser(userid, auser.giveCompany().getId()).get(0);
			//System.out.println("Assign to user - " + user.getUsername());
			//System.out.println("gatewayIds: " + gatewayids);
			String[] gatewayId = gatewayids.split(",");
			Set<String> gatIds = new HashSet<String>(Arrays.asList(gatewayId));

			boolean res = userService.assignGatewaysToUser(gatIds, auser.getId(), user);

			response.put("Status", 1);
			response.put("Msg", "success");

		} catch (InvalidAuthoException e) {
			log.error("assignUserGateway :" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid authentication key");

		} catch (Exception e) {
			log.error("assignUserGateway:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in assign assets");
		}
		return response;
	}

	// ==========delete user========
	@RequestMapping(value = "v3.0/user/{user_id}/{autho}", method = RequestMethod.DELETE, headers = "Accept=application/json")
	public @ResponseBody JResponse deleteUser(@PathVariable String autho, @PathVariable String user_id,
			BindingResult result) {
		JResponse response = new JResponse();
		return response;
	}

	@RequestMapping(value = "v3.0/deleteuser/{user_id}/{autho}", method = RequestMethod.DELETE, headers = "Accept=application/json")
	public @ResponseBody JResponse deleteUser(@PathVariable String autho, @PathVariable String user_id) {
		JResponse response = new JResponse();
		try {

			List<User> auser = userService.getUser(user_id, 3);
			userService.deleteUser(auser.get(0));
		} catch (Exception e) {
			log.error("deleteUser:::" + e.getMessage());
		}
		return response;
	}

	@RequestMapping(value = "v3.0/deleteuserV2/{user_id}/{autho}", method = RequestMethod.DELETE, headers = "Accept=application/json")
	public @ResponseBody JResponse deleteUserV2(@PathVariable String autho, @PathVariable String user_id)
			throws InvalidUsernameException {
		JResponse response = new JResponse();
		try {
			long userid = Long.parseLong(user_id);
			User user = userService.getUserById(userid);

			String status = "";

			if (!user.isEnable())
				status = userService.deleteUserv2(user);
			else {
				response.put("Status", 0);
				response.put("Msg", "failure");
				response.put("Error", "user is enabled");
				return response;
			}

			if (status.contains("Success")) {
				response.put("Status", 1);
				response.put("Msg", "success");
				response.put("Info", "User Delete info  - " + status.split(":")[0] + " : " + "Company Delete info - "
						+ status.split(":")[1]);
			} else {
				response.put("Status", 0);
				response.put("Msg", "failure");
				response.put("Error", "while deleting user");
			}

			return response;

		} catch (InvalidUsernameException e) {
			log.error("deleteUser:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "failure");
			response.put("Error", "User already deleted");
		} catch (Exception e) {
			log.error("deleteUser:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "failure");
			response.put("Error", e.getMessage());
		}

		return response;
	}

	// ========Login========
	@RequestMapping(value = "v3.0/login", headers = "Accept=application/json", method = RequestMethod.POST)
	@ResponseBody

	public JResponse getUserByName(@RequestParam("username") String userName,
			@RequestParam("mobiletype") String mobiletype, @RequestParam("mobileid") String mobileid,
			@RequestParam("password") String passwo) {
		JResponse response = new JResponse();
		log.info(userName + "userName" + passwo + "passwo");
		String username = userName.trim();
		String password = passwo.trim();
		log.info(username + "username" + password + "password");
		// check for username
		if (username.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Please enter username");

			return response;
		} else if (password.isEmpty()) {

			response.put("Status", 0);
			response.put("Msg", "Please enter Password");

			return response;
		}
		try {
			User user = userService.getUserByName(username);
			if (!(user.getUsername().equals(username))) {
				response.put("Status", 0);
				response.put("Msg", "Invalid username");
				return response;
			}
			String pwd = user.getPassword().toString();
			boolean passwordMatch = _helper.checkUserCredencial(password, pwd, user.getPassword_ver());
			if ( passwordMatch ) {
				log.info(mobileid.length() + "   " + mobiletype.length());

				if (!(mobileid.isEmpty()) && !(mobiletype.isEmpty())) {
					try {
						int mobtype = Integer.parseInt(mobiletype);
						long mobid = Integer.parseInt(mobileid);
						// userService.saveMobileInfo(mobtype, mobid, username);
					} catch (NumberFormatException e) {
						response.put("Status", 0);
						response.put("Msg", "Invalid mobileid/mobiletype");

						return response;
					}
				}
				if (!user.isEnable()) {
					response.put("Status", 0);
					response.put("Msg", "Invalid username or password!!");
					return response;
				}
				response.put("Status", 1);
				response.put("Msg", "success");
				response.put("User", user);

			} else {
				if (password.isEmpty()) {
					response.put("Status", 0);
					response.put("Msg", "Please enter password");

					return response;
				}
				response.put("Status", 0);
				response.put("Msg", "Invalid password");
			}
		}

		catch (InvalidUsernameException e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid username");

		} catch (Exception e) {
			log.error("login::::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in login");
		}
		return response;

	}

	// ========check version================
	@RequestMapping(value = "v3.0/config", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getVersionValid() {
		JResponse response = new JResponse();

		String ip = "***************";
		String rv_android_V = "4.0.8";
		String rv_android_force = "0";
		String rv_android_force_V = "0";

		String rv_ios_V = "6.0.8";
		String rv_ios_force = "0";
		String rv_ios_force_V = "0";

		String iris3_android_V = "3.0.6";
		String iris3_android_force = "0";
		String iris3_android_force_V = "0";

		String iris3_ios_V = "5.0.8";
		String iris3_ios_force = "0";
		String iris3_ios_force_V = "0";

		String iris3_wl_android_V = "3.0.6";
		String iris3_wl_android_force = "0";
		String iris3_wl_android_force_V = "0";

		String iris3_wl_ios_V = "5.0.8";
		String iris3_wl_ios_force = "0";
		String iris3_wl_ios_force_V = "0";

		String rm_android_V = "1.0.4";
		String rm_android_force = "0";
		String rm_android_force_V = "0";

		String rm_ios_V = "1.0.4";
		String rm_ios_force = "0";
		String rm_ios_force_V = "0";

		String webservice_V = "3.2.0";
		String listener_V = "3.1.19";
		String webapp_V = "3.1.13";
		String database_V = "3.1.19";

		String rv_hotline_AppId = "cedda3bc-c628-4bac-8f65-9b408d437614_1"; /*
																			 * hotline username - mmarimut
																			 * 
																			 * @ nimblewireless . com
																			 */
		String rv_hotline_AppKey = "3ef7a41f-9419-4bbd-ba18-437f8eb83341_1"; /*
																				 * hotline username - mmarimut
																				 * 
																				 * @ nimblewireless . com
																				 */

		String rm_bg_ios_V = "1.0.2";
		String rm_bg_ios_force = "1.0.2";

		String rm_bg_android_V = "1.0.1";
		String rm_bg_android_force = "1.0.1";

		String rv_petprofile_force = "1";

		String s3bucketname_iris = "iris3.nimblewireless.com";
		String s3_key = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_access_key");
		String s3_secret = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_secret_key");

		Configuration config = new Configuration(ip, rv_android_V, rv_android_force, rv_android_force_V, rv_ios_V,
				rv_ios_force, rv_ios_force_V, iris3_android_V, iris3_android_force, iris3_android_force_V, iris3_ios_V,
				iris3_ios_force, iris3_ios_force_V, iris3_wl_android_V, iris3_wl_android_force,
				iris3_wl_android_force_V, iris3_wl_ios_V, iris3_wl_ios_force, iris3_wl_ios_force_V, rm_android_V,
				rm_android_force, rm_android_force_V, rm_ios_V, rm_ios_force, rm_ios_force_V, listener_V, webservice_V,
				webapp_V, database_V, rv_hotline_AppId, rv_hotline_AppKey, rm_bg_ios_V, rm_bg_ios_force,
				rm_bg_android_V, rm_bg_android_force, rv_petprofile_force, s3bucketname_iris, s3_key, s3_secret);
		try {
			Properties prop = new Properties();

			try {
				/* load a properties file */
				// prop.load(new FileInputStream("iris3.properties"));
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));
				ip = prop.getProperty("ip");
				rv_android_V = prop.getProperty("rv_android_V");
				rv_android_force = prop.getProperty("rv_android_force");
				rv_android_force_V = prop.getProperty("rv_android_force_V");

				rv_ios_V = prop.getProperty("rv_ios_V");
				rv_ios_force = prop.getProperty("rv_ios_force");
				rv_ios_force_V = prop.getProperty("rv_ios_force_V");

				iris3_android_V = prop.getProperty("iris3_android_V");
				iris3_android_force = prop.getProperty("iris3_android_force");
				iris3_android_force_V = prop.getProperty("iris3_android_force_V");

				iris3_ios_V = prop.getProperty("iris3_ios_V");
				iris3_ios_force = prop.getProperty("iris3_ios_force");
				iris3_ios_force_V = prop.getProperty("iris3_ios_force_V");

				iris3_wl_android_V = prop.getProperty("iris3_wl_android_V");
				iris3_wl_android_force = prop.getProperty("iris3_wl_android_force");
				iris3_wl_android_force_V = prop.getProperty("iris3_wl_android_force_V");

				iris3_wl_ios_V = prop.getProperty("iris3_wl_ios_V");
				iris3_wl_ios_force = prop.getProperty("iris3_wl_ios_force");
				iris3_wl_ios_force_V = prop.getProperty("iris3_wl_ios_force_V");

				rm_android_V = prop.getProperty("rm_android_V");
				rm_android_force = prop.getProperty("rm_android_force");
				rm_android_force_V = prop.getProperty("rm_android_force_V");

				rm_ios_V = prop.getProperty("rm_ios_V");
				rm_ios_force = prop.getProperty("rm_ios_force");
				rm_ios_force_V = prop.getProperty("rm_ios_force_V");

				webservice_V = prop.getProperty("webservice_V");
				listener_V = prop.getProperty("listener_V");
				webapp_V = prop.getProperty("webapp_V");
				database_V = prop.getProperty("database_V");

				rv_hotline_AppId = prop.getProperty("rv_hotline_AppId");
				rv_hotline_AppKey = prop.getProperty("rv_hotline_AppKey");

				rm_bg_ios_V = prop.getProperty("rm_bg_ios_V");
				rm_bg_ios_force = prop.getProperty("rm_bg_ios_force");

				rm_bg_android_V = prop.getProperty("rm_bg_android_V");
				rm_bg_android_force = prop.getProperty("rm_bg_android_force");

				rv_petprofile_force = prop.getProperty("rv_petprofile_force");

				s3bucketname_iris = prop.getProperty("s3bucketname_iris");
				s3_key = prop.getProperty("s3_key");
				s3_secret = prop.getProperty("s3_secret");

				config = new Configuration(ip, rv_android_V, rv_android_force, rv_android_force_V, rv_ios_V,
						rv_ios_force, rv_ios_force_V, iris3_android_V, iris3_android_force, iris3_android_force_V,
						iris3_ios_V, iris3_ios_force, iris3_ios_force_V, iris3_wl_android_V, iris3_wl_android_force,
						iris3_wl_android_force_V, iris3_wl_ios_V, iris3_wl_ios_force, iris3_wl_ios_force_V,
						rm_android_V, rm_android_force, rm_android_force_V, rm_ios_V, rm_ios_force, rm_ios_force_V,
						listener_V, webservice_V, webapp_V, database_V, rv_hotline_AppId, rv_hotline_AppKey,
						rm_bg_ios_V, rm_bg_ios_force, rm_bg_android_V, rm_bg_android_force, rv_petprofile_force,
						s3bucketname_iris, s3_key, s3_secret);

			} catch (IOException ex) {
				log.error("exception catched 1111");
				log.error(ex.getLocalizedMessage());
			}

			response.put("Status", 1);
			response.put("Msg", "success");
			response.put("config", config);
			return response;
		} catch (Exception e) {
			log.error("GetVersion::::::" + e.getMessage());
			response.put("Status", 1);
			response.put("Msg", "success");
			return response;
		}
	}

	// ==========save / update usertoken========
	@RequestMapping(value = "v3.0/usertoken/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse usertoken(@PathVariable String autho, @ModelAttribute UserToken usertoken) {

		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);
			userService.saveOrUpdateUserToken(user, usertoken);
			response.put("Status", 1);
			response.put("Msg", "success");

		} catch (DataIntegrityViolationException e) {
			response.put("Status", 0);
			response.put("Msg", "userid and token also be unique");
			log.error("usertoken:::" + e.getMessage());
		}

		catch (Exception e) {
			log.error("usertoken:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Create Usertoken");
		}
		return response;
	}

	// ==========save / update userdevice info========
	@RequestMapping(value = "v3.0/userdeviceinfo/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse userdeviceinfo(@PathVariable String autho,
			@RequestBody JUserDeviceInfo jUserDeviceInfo, HttpServletRequest request) {

		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);

			log.info("IP Address  :" + request.getRemoteAddr());

			userService.saveOrUpdateUserDeviceInfo(user, jUserDeviceInfo, request.getRemoteAddr());
			response.put("Status", 1);
			response.put("Msg", "success");

		} catch (DataIntegrityViolationException e) {
			response.put("Status", 0);
			response.put("Msg", "userid and device id also be unique");
			log.error(e.getLocalizedMessage());
		}

		catch (Exception e) {
			log.error("usertoken:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Create user device info");
		}
		return response;
	}

	// ==========Get all userdevice info========
	@RequestMapping(value = "v3.0/getuserdeviceinfo/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getUserdeviceinfo(@PathVariable String autho) {

		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);

			List<UserDeviceInfo> deviceInfo = userService.getUserDeviceInfo();

			if (deviceInfo != null) {

				if (deviceInfo.size() > 0) {
					response.put("Status", 1);
					response.put("userDeviceInfo", deviceInfo);
					return response;

				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Unable to get the all the user device info");
				return response;
			}

		} catch (DataIntegrityViolationException e) {
			response.put("Status", 0);
			response.put("Msg", "userid and token also be unique");
			log.error(e.getLocalizedMessage());
		}

		catch (Exception e) {
			log.error("usertoken:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Create Usertoken");
		}
		return response;
	}
	// Used in web
	// ========Login Version3.1========
	@RequestMapping(value = "v3.0/loginV2", headers = "Accept=application/json", method = RequestMethod.POST)
	@ResponseBody
	public JResponse getUserByNameV2(@RequestParam("username") String userName,
			@RequestParam("mobiletype") String mobiletype, @RequestParam("mobileid") String mobileid,
			@RequestParam("password") String passwo, @RequestParam("webappid") String webappid,
			@RequestParam("mobileappid") String mobileappid) {
		JResponse response = new JResponse();
		log.info(userName + "userName" + passwo + "passwo");
		String username = userName.trim();
		String password = passwo.trim();
		log.info(username + "username" + password + "password");
		/* check for username */
		if (username.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Please enter username");
			return response;
		} else if (password.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Please enter Password");
			return response;
		}
		try {
			User user = userService.getUserByName(username);
			if (!(user.getUsername().equalsIgnoreCase(username))) {
				response.put("Status", 0);
				response.put("Msg", "Invalid username");
				return response;
			}
			String pwd = user.getPassword().toString();
			boolean passwordMatch = _helper.checkUserCredencial(password, pwd, user.getPassword_ver());
			if ( passwordMatch ) {
				if (!(mobileid.isEmpty()) && !(mobiletype.isEmpty())) {
					try {
						int mobtype = Integer.parseInt(mobiletype);
						long mobid = Integer.parseInt(mobileid);
						// userService.saveMobileInfo(mobtype, mobid, username);
					} catch (NumberFormatException e) {
						response.put("Status", 0);
						response.put("Msg", "Invalid mobileid/mobiletype");
						log.error(e.getLocalizedMessage());
						return response;
					}
				}
				if (!user.isEnable()) {
					response.put("Status", 0);
					response.put("Msg", "Account Disabled. Please contact support!");
					return response;
				}
				if (!webappid.isEmpty()) {
					if (!webappid.equalsIgnoreCase(String.valueOf(user.getWebappid()))) {
						response.put("Status", 0);
						response.put("Msg", "Invalid username or password!");
						return response;
					}
				}
				if (!mobileappid.isEmpty()) {
					if (!mobileappid.equalsIgnoreCase(String.valueOf(user.getMobileappid()))) {
						response.put("Status", 0);
						response.put("Msg", "Invalid username or password!");
						return response;
					}
				}
				response.put("Status", 1);
				response.put("Msg", "success");
				response.put("User", user);
			} else {
				if (password.isEmpty()) {
					response.put("Status", 0);
					response.put("Msg", "Please enter password");
					return response;
				}
				response.put("Status", 0);
				response.put("Msg", "Invalid password");
			}
		} catch (InvalidUsernameException e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid username");
		} catch (Exception e) {
			log.error("login::::" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in login");
		}
		return response;
	}

	// Used in web
	// ========get user by userId or company id ================
	@RequestMapping(value = "v3.0/userV2/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserByIdV2(@PathVariable String autho, @RequestParam("userid") String userid,
			@RequestParam("cmpid") String cmpid) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			long cmp_id = user.giveCompany().getId();
			if (!cmpid.isEmpty())
				cmp_id = Long.valueOf(cmpid);
			List<User> users = userService.getUser(userid, cmp_id);
			List<User> users1 = new ArrayList<>();
			for (User user1 : users) {
				String mobile = "";
				if (user1 != null) {
					if (userid.trim().isEmpty())
						userid = String.valueOf(user1.getId());

					Map<Integer, String> getnumber = _helper.getPhoneAndCountryCode(user1.getMobileno());
					if (user1.getCountry().equalsIgnoreCase("NA")) {
						if (getnumber.get(0).equalsIgnoreCase("+91") || getnumber.get(0).equalsIgnoreCase("91")) {
							user1.setCountry("IN");
						} else if (getnumber.get(0).equalsIgnoreCase("+44")
								|| getnumber.get(0).equalsIgnoreCase("44")) {
							user1.setCountry("GB");
						} else {
							user1.setCountry("US");
						}
					}
					mobile = getnumber.get(1);
					if (mobile == null) {
						user1.setMobileno("NA");
					} else {
						user1.setMobileno(getnumber.get(1));
					}
					users1.add(user1);
				}
			}

			ArrayList<RVAnswer> ansList = new ArrayList<RVAnswer>();//rvServ.listRVAnswer();
			ArrayList<JRVAnswer> rv_cat_list = new ArrayList<JRVAnswer>();
			ArrayList<JRVAnswer> rv_type_list = new ArrayList<JRVAnswer>();
			ArrayList<JRVAnswer> pet_avail_list = new ArrayList<JRVAnswer>();
			ArrayList<JRVAnswer> no_travels_list = new ArrayList<JRVAnswer>();

			if (ansList != null) {

				for (int i = 0; i < ansList.size(); i++) {
					long ques_id = ansList.get(i).getQues_id();
					long ans_id = ansList.get(i).getId();
					String ans_value = ansList.get(i).getAns_value();
					JRVAnswer ansObj = new JRVAnswer(ans_id, ans_value);

					if (ques_id == 1) {
						rv_cat_list.add(ansObj);
					} else if (ques_id == 2) {
						rv_type_list.add(ansObj);
					} else if (ques_id == 3) {
						pet_avail_list.add(ansObj);
					} else {
						no_travels_list.add(ansObj);
					}
				}
			}
			boolean is_travelprofile = false;

//			UserRvDetails rvObj = userServV4.getUserRvDetails(Long.parseLong(userid));
//			JUserRVDetail travelprofile = new JUserRVDetail();
//
//			if (rvObj != null) {
//				travelprofile = new JUserRVDetail(rvObj.getId(), rvObj.getOwn_rv(), rvObj.getRvtype(),
//						rvObj.getWithPet(), rvObj.getHow_often(), rvObj.getOthers_type());
//				is_travelprofile = true;
//			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("users", users1);
			response.put("travelprofile", null);
			response.put("is_travelprofile", is_travelprofile);

			response.put("rv_cat_list", rv_cat_list);
			response.put("rv_type_list", rv_type_list);
			response.put("pet_avail_list", pet_avail_list);
			response.put("no_travels_list", no_travels_list);

		} catch (InvalidAuthoException e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid userid");
			return response;
		}
		return response;
	}

	/* ==================== resetpassword ==================== */

	@RequestMapping(value = "v3.0/resetpassword/{autho}", method = RequestMethod.GET)
	@ResponseBody
	public RedirectView resetPassword(@PathVariable String autho, RedirectAttributes redirectAttrs) {
		RedirectView redirectView = new RedirectView();
		try {
			User user = userService.verifyAuthKey(autho);
			String ip = ImsgService.getMessageIp();
			if (user.getResetpassword() == 1) {
				redirectAttrs.addAttribute("auth", user.getAuthKey());
				redirectAttrs.addAttribute("userid", user.getId());
				redirectView.setContextRelative(true);
				redirectView.setUrl(ip + "/irisngp/forgot.jsp");
			} else
				redirectView.setUrl(ip + "/irisngp/tokenexpired.jsp");

		} catch (InvalidAuthoException e) {
			log.error("forgetpassword " + e.getLocalizedMessage());
		}
		return redirectView;
	}

	// ========get user by name ================
	@RequestMapping(value = "v3.0/username/{name}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserByUsername(@PathVariable String name) {
		JResponse response = new JResponse();

		try {
			User user = userService.getUserByName(name);
			if (!(user.getUsername().equals(name))) {
				response.put("Status", 0);
				response.put("Msg", "Invalid username");
				return response;
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("users", user);

		} catch (InvalidUsernameException e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Username");
			return response;
		}
		return response;
	}

	// ========get user by name ================
	@RequestMapping(value = "v3.0/getUserByUsernameV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserByUsernameV2(@RequestParam("name") String name) {
		JResponse response = new JResponse();
		name = name.toLowerCase().trim();
		name = name.replaceAll("\\s", "");
		try {
			User user = userService.getUserByName(name);
			if (!(user.getUsername().equalsIgnoreCase(name))) {
				response.put("Status", 0);
				response.put("Msg", "Invalid username");
				return response;
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("users", user);

		} catch (InvalidUsernameException e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Username");
			return response;
		}
		return response;
	}

	// ========force version================
	@RequestMapping(value = "v3.0/forceUpdate", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse isForceUpdate(@RequestParam("app") String app, @RequestParam("version") String version,
			@RequestParam("mobiletype") String mobiletype,
			@RequestParam(value = "userid", defaultValue = "", required = false) String userid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "backing", defaultValue = "MT", required = false) String backing) {
		JResponse response = new JResponse();

		Helper _helper = new Helper();
		log.info("backing key : "+backing);
		
//		AES aes = new AES();
//		String auth = null;
//		if (backing != null) {
//			if (!backing.equals("MT")) {
//				String[] credential = _helper.decodeInternalKey(backing);
//				String finalOut = aes.decode(credential[0], credential[1]);
//
//				if (finalOut == null) {
//					response.put("Status", 0);
//					response.put("Msg", "Authentication Error");
//					return response;
//				}
//				log.info("AES decryption success : " + backing + " : " + finalOut);
//			}
//		} else {
//			response.put("Status", 0);
//			response.put("Msg", "Authentication Error");
//			return response;
//		}

		if (app.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Application Type Should Not Be Empty!");
			return response;
		} else if (version.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Application verison Should Not Be Empty!");
			return response;
		} else if (mobiletype.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Mobile Type Should Not Be Empty!");
			return response;
		}

		// App id
		// 5 RFS_BG - Background
		// 4 RM-Restaurant Monitoring

		int app_i = Integer.parseInt(app);
		int version_i = Integer.parseInt(version.replaceAll("\\.", ""));
		int mobiletype_i = Integer.parseInt(mobiletype);

		String ip = "***************";

		String rv_android_V = "4.0.8";
		String rv_android_force = "0";
		String rv_android_force_V = "4.0.8";

		String rv_ios_V = "6.0.8";
		String rv_ios_force = "0";
		String rv_ios_force_V = "6.0.8";

		String iris3_android_V = "3.0.6";
		String iris3_android_force = "0";
		String iris3_android_force_V = "3.0.6";

		String iris3_ios_V = "5.0.8";
		String iris3_ios_force = "0";
		String iris3_ios_force_V = "5.0.8";

		String iris3_wl_android_V = "3.0.6";
		String iris3_wl_android_force = "0";
		String iris3_wl_android_force_V = "3.0.6";

		String iris3_wl_ios_V = "5.0.8";
		String iris3_wl_ios_force = "0";
		String iris3_wl_ios_force_V = "5.0.8";

		String rm_android_V = "1.0.4";
		String rm_android_force = "1";
		String rm_android_force_V = "1.0.2";

		String rm_ios_V = "1.0.4";
		String rm_ios_force = "1";
		String rm_ios_force_V = "1.0.2";

		String webservice_V = "3.2.0";
		String listener_V = "3.1.19";
		String webapp_V = "3.1.13";
		String database_V = "3.1.19";
		/*
		 * hotline username - mmarimut
		 * 
		 * @ nimblewireless . com
		 */
		String rv_hotline_AppId = "cedda3bc-c628-4bac-8f65-9b408d437614_1";
		/*
		 * hotline username - mmarimut
		 * 
		 * @ nimblewireless . com
		 */

		String rv_hotline_AppKey = "3ef7a41f-9419-4bbd-ba18-437f8eb83341_1";
		String rm_bg_ios_V = "1.0.2";
		String rm_bg_ios_force = "1.0.2";

		String rm_bg_android_V = "1.0.1";
		String rm_bg_android_force = "1.0.1";

		String rv_petprofile_force = "1";

		String s3bucketname_iris = "iris3.nimblewireless.com";
		String s3_key = "********************";
		String s3_secret = "4GidEgA09FO97x7EvIOEbFoRJ/DNkEdtWiAUY+LC";

		Configuration config = new Configuration(ip, rv_android_V, rv_android_force, rv_android_force_V, rv_ios_V,
				rv_ios_force, rv_ios_force_V, iris3_android_V, iris3_android_force, iris3_android_force_V, iris3_ios_V,
				iris3_ios_force, iris3_ios_force_V, iris3_wl_android_V, iris3_wl_android_force,
				iris3_wl_android_force_V, iris3_wl_ios_V, iris3_wl_ios_force, iris3_wl_ios_force_V, rm_android_V,
				rm_android_force, rm_android_force_V, rm_ios_V, rm_ios_force, rm_ios_force_V, listener_V, webservice_V,
				webapp_V, database_V, rv_hotline_AppId, rv_hotline_AppKey, rm_bg_ios_V, rm_bg_ios_force,
				rm_bg_android_V, rm_bg_android_force, rv_petprofile_force, s3bucketname_iris, s3_key, s3_secret);

		/*
		 * Configuration config = new Configuration(ip, rv_android_V,
		 * rv_ios_V,iris3_android_V,webservice_V,listener_V,webapp_V,database_V,
		 * iris3_ios_V,rv_android_force,rv_ios_force,iris3_android_force,
		 * iris3_ios_force,rv_hotline_AppId,rv_hotline_AppKey,rm_android_V,
		 * rm_android_force,rm_ios_V, rm_ios_force);
		 */

		response.put("Status", 1);
		response.put("Msg", "success");

		try {
			Properties prop = new Properties();
			try {
				/* load a properties file */
				// prop.load(new FileInputStream("iris3.properties"));
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));
				ip = prop.getProperty("ip");
				rv_android_V = prop.getProperty("rv_android_V");
				rv_android_force = prop.getProperty("rv_android_force");
				rv_android_force_V = prop.getProperty("rv_android_force_V");

				rv_ios_V = prop.getProperty("rv_ios_V");
				rv_ios_force = prop.getProperty("rv_ios_force");
				rv_ios_force_V = prop.getProperty("rv_ios_force_V");

				iris3_android_V = prop.getProperty("iris3_android_V");
				iris3_android_force = prop.getProperty("iris3_android_force");
				iris3_android_force_V = prop.getProperty("iris3_android_force_V");

				iris3_ios_V = prop.getProperty("iris3_ios_V");
				iris3_ios_force = prop.getProperty("iris3_ios_force");
				iris3_ios_force_V = prop.getProperty("iris3_ios_force_V");

				iris3_wl_android_V = prop.getProperty("iris3_wl_android_V");
				iris3_wl_android_force = prop.getProperty("iris3_wl_android_force");
				iris3_wl_android_force_V = prop.getProperty("iris3_wl_android_force_V");

				iris3_wl_ios_V = prop.getProperty("iris3_wl_ios_V");
				iris3_wl_ios_force = prop.getProperty("iris3_wl_ios_force");
				iris3_wl_ios_force_V = prop.getProperty("iris3_wl_ios_force_V");

				rm_android_V = prop.getProperty("rm_android_V");
				rm_android_force = prop.getProperty("rm_android_force");
				rm_android_force_V = prop.getProperty("rm_android_force_V");

				rm_ios_V = prop.getProperty("rm_ios_V");
				rm_ios_force = prop.getProperty("rm_ios_force");
				rm_ios_force_V = prop.getProperty("rm_ios_force_V");

				webservice_V = prop.getProperty("webservice_V");
				listener_V = prop.getProperty("listener_V");
				webapp_V = prop.getProperty("webapp_V");
				database_V = prop.getProperty("database_V");

				rv_hotline_AppId = prop.getProperty("rv_hotline_AppId");
				rv_hotline_AppKey = prop.getProperty("rv_hotline_AppKey");

				rm_bg_ios_V = prop.getProperty("rm_bg_ios_V");
				rm_bg_ios_force = prop.getProperty("rm_bg_ios_force");

				rm_bg_android_V = prop.getProperty("rm_bg_android_V");
				rm_bg_android_force = prop.getProperty("rm_bg_android_force");

				rv_petprofile_force = prop.getProperty("rv_petprofile_force");

				s3bucketname_iris = prop.getProperty("s3bucketname_iris");
				s3_key = prop.getProperty("s3_key");
				s3_secret = prop.getProperty("s3_secret");
				
				ForceUpdate forceUpt = userService.getForceUpdate(userid);
				if(forceUpt != null) {
					if (0 < versionCompare(forceUpt.getAndroidVersion(), rv_android_force_V))
						rv_android_force_V = forceUpt.getAndroidVersion();
					if (0 < versionCompare(forceUpt.getIosVersion(), rv_ios_force_V))
						rv_ios_force_V = forceUpt.getIosVersion();
				}
					

				config = new Configuration(ip, rv_android_V, rv_android_force, rv_android_force_V, rv_ios_V,
						rv_ios_force, rv_ios_force_V, iris3_android_V, iris3_android_force, iris3_android_force_V,
						iris3_ios_V, iris3_ios_force, iris3_ios_force_V, iris3_wl_android_V, iris3_wl_android_force,
						iris3_wl_android_force_V, iris3_wl_ios_V, iris3_wl_ios_force, iris3_wl_ios_force_V,
						rm_android_V, rm_android_force, rm_android_force_V, rm_ios_V, rm_ios_force, rm_ios_force_V,
						listener_V, webservice_V, webapp_V, database_V, rv_hotline_AppId, rv_hotline_AppKey,
						rm_bg_ios_V, rm_bg_ios_force, rm_bg_android_V, rm_bg_android_force, rv_petprofile_force,
						s3bucketname_iris, s3_key, s3_secret);

				/*
				 * config = new Configuration(ip, rv_android_V,
				 * rv_ios_V,iris3_android_V,webservice_V,listener_V,webapp_V, database_V,
				 * iris3_ios_V,rv_android_force,rv_ios_force,iris3_android_force
				 * ,iris3_ios_force,rv_hotline_AppId,rv_hotline_AppKey,
				 * rm_android_V,rm_android_force, rm_ios_V, rm_ios_force);
				 */

				if (app_i == 6) // White label App
				{
					if (mobiletype_i == 2) {
						System.out.println("check force update");
						response.put("forceUpdate", checkForForceupdate(iris3_wl_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(iris3_wl_ios_force_V, version));
					}
				} else if (app_i == 5) // Background App
				{
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(rm_bg_android_force, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(rm_bg_ios_force, version));
					}
				} else if (app_i == 4) {
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(rm_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(rm_ios_force_V, version));
					}
				} else if (app_i == 3) {
					if (mobiletype_i == 2) {
						response.put("forceUpdate", checkForForceupdate(rv_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(rv_ios_force_V, version));
					}
				} else if (app_i == 2) {
					if (mobiletype_i == 2) {
						System.out.println("check force update");
						response.put("forceUpdate", checkForForceupdate(iris3_android_force_V, version));
					} else if (mobiletype_i == 3) {
						response.put("forceUpdate", checkForForceupdate(iris3_ios_force_V, version));
					}
				} else {
					response.put("forceUpdate", "Invalid Version");
				}

			} catch (IOException ex) {
				log.error(ex.getLocalizedMessage());
			}

			response.put("config", config);

			return response;
		} catch (Exception e) {
			log.error("GetVersion::::::" + e.getMessage());
			response.put("Status", 1);
			response.put("Msg", "success");
			return response;
		}
	}

	public boolean checkForForceupdate(String forceVersion, String appVersion) {
		log.info("checkForForceupdate : forceVersion = " + forceVersion + "appVersion = " + appVersion);
		/*
		 * int a_version = Integer.parseInt(appVersion.replaceAll("\\.","")); int
		 * f_version = Integer.parseInt(forceVersion.replaceAll("\\.",""));
		 * 
		 * if(f_version > a_version) { log.info("checkForForceupdate:f_version: "
		 * +f_version+" is greater than app-version:" +a_version+" Force update needed"
		 * ); return true; }
		 */
		int result = versionCompare(forceVersion, appVersion);
		if (result > 0)// forceVersion >appVersion
			return true;

		return false;
	}

	/*
	 * The result is a negative integer if str1 is numerically less than str2. The
	 * result is a positive integer if str1 is numerically greater than str2. The
	 * result is zero if the strings are numerically equal
	 */
	public static int versionCompare(String str1, String str2) {
		String[] vals1 = str1.split("\\.");
		String[] vals2 = str2.split("\\.");
		int i = 0;
		// set index to first non-equal ordinal or length of shortest version
		// string
		while (i < vals1.length && i < vals2.length && vals1[i].equals(vals2[i])) {
			i++;
		}

		// compare first non-equal ordinal number
		if (i < vals1.length && i < vals2.length) {

			int diff = Integer.valueOf(vals1[i]).compareTo(Integer.valueOf(vals2[i]));

			return Integer.signum(diff);
		}

		// the strings are equal or one string is a substring of the other
		// e.g. "1.2.3" = "1.2.3" or "1.2.3" < "1.2.3.4"

		// compare e.g."1.2.3" == "1.2.3.0"
		if (vals1.length - vals2.length == -1) {

			if (vals2[i].equals("0"))
				return 0;
		} else if (vals1.length - vals2.length == 1) { // compare e.g."1.2.3.0"
			// == "1.2.3"

			if (vals1[i].equals("0"))
				return 0;
		}

		return Integer.signum(vals1.length - vals2.length);
	}

	// ========Validate license expiry for the given user device
	// ================
	@RequestMapping(value = "v3.0/checklicensefordevice/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse checklicensefordevice(@PathVariable String autho,
			@RequestParam(value = "userid", defaultValue = "", required = false) String userid,
			@RequestParam("deviceName") String deviceName, @RequestParam("deviceType") String deviceType) {
		JResponse response = new JResponse();
		// deviceName is unique
		// deviceType is "N5" or "F5"

		// TODO
		/*
		 * Create a table to store the below values user_id device_name device_type
		 * license_valid (0/1) start_datetime expiry_datetime
		 * 
		 * Check the license_valid and return its value
		 */
		long user_id = 0;
		boolean licenseEnable = false;
		try {
			/*
			 * if(deviceName == null || deviceName.isEmpty()){ System.out.
			 * println("checklicensefordevice - deviceName must not be null "); }else{
			 * if(autho != null && autho.equalsIgnoreCase("dummyUserAuthKey")){ //Called
			 * from background app. User information is not available }else{//Called from
			 * Nimble F5 App User user = userService.verifyAuthKey(autho); if(user != null){
			 * if(userid == null || userid.isEmpty()) user_id = user.getId(); else user_id =
			 * Long.valueOf(userid);
			 * //System.out.println("checklicensefordevice - user_id = "+ user_id);
			 * licenseEnable = userService.getDeviceLicense(user_id, deviceName,
			 * deviceType); }
			 * 
			 * } response.put("Status", 1); response.put("Msg","Success"); }
			 */
			if (autho != null && !autho.equalsIgnoreCase("dummyUserAuthKey")) {
				// Called from Nimble F5 App
				User user = userService.verifyAuthKey(autho);
				if (user == null) {
					response.put("Status", 0);
					response.put("Msg", "Invalid auth key");
					return response;
				} else {
					user_id = user.getId();
				}
			}
			if (deviceName != null && !deviceName.isEmpty()) {
				if (userid != null && !userid.isEmpty())
					user_id = Long.valueOf(userid);
				licenseEnable = userService.getDeviceLicense(user_id, deviceName, deviceType);
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("license", licenseEnable);

			} else {
				response.put("Status", 0);
				response.put("Msg", "DeviceName must not be empty");
			}

		} catch (InvalidAuthoException e) {
			// TODO Auto-generated catch block
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid auth key");
		}

		return response;
	}

	// ========get the RV Pet safety blog URL details================
	@RequestMapping(value = "v3.0/getPetSafetyUrls/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getPetSafetyUrls(@PathVariable String autho,
			@RequestParam(value = "startRow", defaultValue = "", required = false) String startRow,
			@RequestParam(value = "maxNoOfRows", defaultValue = "", required = false) String maxNoOfRows) {

		JResponse response = new JResponse();
		long userId = 0;

		try {
			User user = userService.verifyAuthKey(autho);
			if (user != null)
				userId = user.getId();
		} catch (InvalidAuthoException e) {
			response.put("Status", 0);
			response.put("Msg", "Invalid auth key");
			return response;
		}

		List<JRvPetsafety> jrvPetsafetyList = userService.getRvPetSafetyBlogUrl(userId, 5);

		response.put("Status", 1);
		response.put("Msg", "success");
		response.put("RvPetsafety", jrvPetsafetyList);
		// response.put("RvBlogUrl", "https://blog.rvpetsafety.com/");
		return response;
	}

	// ========get all the RV Pet safety blog URL details for tool rvblog
	// ================
	@RequestMapping(value = "v3.0/getpetSafetyblogurls/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getPetSafetyBlogUrls(@PathVariable String autho,
			@RequestParam(value = "startRow", defaultValue = "", required = false) String startRow,
			@RequestParam(value = "maxNoOfRows", defaultValue = "", required = false) String maxNoOfRows) {

		JResponse response = new JResponse();
		long userId = 0;

		try {
			User user = userService.verifyAuthKey(autho);
			if (user != null)
				userId = user.getId();
		} catch (InvalidAuthoException e) {

			response.put("Status", 0);
			response.put("Msg", "Invalid auth key");
			return response;
		}

		List<JRvPetsafety> jrvPetsafetyList = userService.getRvPetSafetyBlogUrl(userId, 0);

		response.put("Status", 1);
		response.put("Msg", "success");
		response.put("RvPetsafety", jrvPetsafetyList);
		// response.put("RvBlogUrl", "https://blog.rvpetsafety.com/");
		return response;
	}

	// ========save or update RvPetsafety blog url details================
	@RequestMapping(value = "v3.0/saveorupdateRvPetsafety", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveorupdateRvPetsafety(@RequestBody List<JRvPetsafety> jRvPetsafety) {
		JResponse response = new JResponse();
		int status = userService.saveorupdateRvpetsafetyUrl(jRvPetsafety);
		if (status != 0) {
			response.put("Status", 1);
			response.put("Msg", "success");
			response.put("Msg", "RvPetsafety saved successfully");
		} else {
			response.put("Status", 0);
			response.put("Msg", "Error, Contents not saved");
		}
		return response;
	}

	// ========save or update RV user details================
	@RequestMapping(value = "v3.0/saveorupdateRvUser", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveorupdateRvUser(@RequestBody JRVUser jRvUser) {
		JResponse response = new JResponse();
		if (jRvUser != null) {
			if (jRvUser.getUserId() != 0 && jRvUser.getRvBlogId() != 0) {

				int status = userService.saveorupdateRvUser(jRvUser);
				if (status != 0) {
					response.put("Status", 1);
					response.put("Msg", "success");
					response.put("Msg", "RvUser saved successfully");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error, Contents not saved");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User id and Blog id must neither be empty nor 0");
			}

		} else {
			response.put("Status", 0);
			response.put("Msg", "Error, Input JSON is null");
		}

		return response;
	}

	@RequestMapping(value = "v3.0/deleteblog/{blogid}/{autho}", method = RequestMethod.DELETE, headers = "Accept=application/json")
	public @ResponseBody JResponse deleteblog(@PathVariable String autho, @PathVariable String blogid) {
		JResponse response = new JResponse();

		boolean status = userService.delBlogUrl(Long.valueOf(blogid));

		if (status) {
			response.put("Status", 1);
			response.put("Msg", "Blog url deleted successfully");
		} else {
			response.put("Status", 0);
			response.put("Msg", "Unable to delete the blog url");
		}

		return response;
	}

	public static byte[] zipContent(Object obj) throws IOException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		GZIPOutputStream gzipOut = new GZIPOutputStream(baos);

		Gson gson = new Gson();
		String json = gson.toJson(obj);
		gzipOut.write(json.getBytes());

		gzipOut.close();
		// objectOut.close();
		byte[] bytes = baos.toByteArray();

		return bytes;
	}

	@RequestMapping(value = "v3.0/enabledisableuser", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse enabledisableuser(@RequestParam("userid") String userid,
			@RequestParam("enablestatus") String enablestatus) {
		JResponse response = new JResponse();
		System.out.println("userid = " + userid);
		if (userid != null && !userid.isEmpty() && !userid.equals("0")
				&& (enablestatus.equalsIgnoreCase("true") || enablestatus.equalsIgnoreCase("false"))) {
			boolean result = userService.enabledisableuser(userid, enablestatus);
			if (result) {
				response.put("Status", 1);
				response.put("Msg", "enabledisableuser:User updated successfully");
			} else {
				response.put("Status", 0);
				response.put("Msg", "enabledisableuser: user not updated ");
			}
		} else {
			response.put("Status", 0);
			response.put("Msg", "Invalid Input,Userid must be valid and enablestaus must be true or false");
		}

		return response;
	}


	// Controller to call NIOM DB directly for activation
	@RequestMapping(value = "v3.0/activateuser", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse activateUserV2(@ModelAttribute @Valid ActivateUser activateUser,BindingResult result) {

		log.info("Activate user:" + activateUser.getEmail() + " OrderId:" + activateUser.getOrderid() + " MobilePage:"
				+ activateUser.getMobilepage() + "QRC : " + activateUser.getQrcCode()  + " lat:" + activateUser.getLat() + " lon:"+ activateUser.getLon());

		boolean show_orderid = activateUser.isShow_orderid();
		boolean show_warranty_popup = show_warranty_popup_config;
		
		String device_country = activateUser.getDevice_country();			
		if(device_country.equalsIgnoreCase("US")|| device_country.equalsIgnoreCase("NA")
				|| device_country.toLowerCase().contains("india") || device_country.equalsIgnoreCase("in")
				|| device_country.isEmpty() || device_country == null) {
			device_country = "US";
		}
		String supportM = supportContactEmail.get(device_country);
		String supportP = supportContactNumber.get(device_country);
		
		if(show_orderid) 
			show_warranty_popup = false;
		
		if(activateUser.getLat() == 270.0 && activateUser.getLon() == 270.0)
		{
			double lat = 32.8914656, lon = -117.1506437;
			activateUser.setLat(lat);
			activateUser.setLon(lon);
		}
		
		JResponse response = new JResponse();
		
		JValidateString validString = userServiceV4.checkAlphabetOnly(activateUser.getFirstname(),activateUser.getLastname());	
		if( !validString.isValid() ) {
			response.put("Status", 0);
			response.put("Msg", validString.getMsg());
			return response;
		}
		
		if( activateUser.isPwd_update() ) {
			String pwd = activateUser.getPassword();
			JValidateString validatePassword = userServiceV4.validatePassword(pwd);
			if( !validatePassword.isValid() ) {
				response.put("Status", 0);
				response.put("Msg", validatePassword.getMsg());
				return response;
			}
		}

		int initialDeviceStateid = 9;
		int model_id = 6;
		long monitortypeid = 0;
		String to_address = null;
		String cc_address = null;
		String bcc_address = null;
		String alertCreationStatus = null;
		String orderChannel = null;
		String subscriptionStatus = "0";
		String petName = activateUser.getGatewayName().trim();
		String orderID = activateUser.getOrderid().trim();
		String companyType = activateUser.getCmptype_id();
		String eRes = RegisterUserError.commonErrorMessage;
		String errorMsg = eRes.replace("#SP#", supportP).replace("#SM#", supportM);
		String qrcCode = activateUser.getQrcCode().trim();
		String errorResponse = "No Error";
		String errorCode = "ER000";
		String passwordType = "1";
		String sensor_available = null;
		String userName = null;
		String mobilePage = null;
		String userEmail = activateUser.getEmail().trim();
		userEmail = userEmail.toLowerCase();
		userEmail = userEmail.replaceAll("\\s+", "");
		String userPhone = activateUser.getPhoneno().trim();
		userPhone = userPhone.replace("(", "");
		userPhone = userPhone.replace(")", "");
		Gateway replacedGateway = null;

		String country = userServiceV4.getContryByPhoneNo(userPhone);
		int index = StringUtils.ordinalIndexOf(userPhone, "-", 2);
		if (index > 0) {
			userPhone = userPhone.substring(0, index) + userPhone.substring(index + 1);
		}

		userPhone = userPhone.replaceAll("\\s+", "");

		Long group_id = null;

		boolean activateSim = false;
		boolean updateExternalOrderDataStatus = false;
		boolean accountActivatedStatus = false;
		boolean orderMappedtatus = false;
		boolean activateSimStatus = false;
		boolean isTestUserDevice = false;
		boolean isQrcUserDevice = false;
		boolean provisionStatus = false;
		boolean loginCreationIssue = false;
		boolean gatewayCreationIssue = false;
		boolean accountCreated = false;
		String monitortype = "NA";
		boolean isAlreadyUser = Boolean.parseBoolean(activateUser.getAlreadyuser());
		boolean enableDelayFreq = false;
		int delayFreqSecs = 0;
		boolean orderid_given = false;

		Properties prop = new Properties();
		Gson gson = new Gson();
		Orders order = new Orders();
		Inventory inventory = new Inventory();
		Helper _helper = new Helper();
		User intialUser = new User();
		User orderMapUser = null;
		long createuser_id = 0l;
		int devicecount = 0;
		
		long recalUserId = 0;
		String oldMeid = "NA";
		int isRecallDevice = 0;
		long gatewayId = 0;
		
		
		if (result.hasErrors()) {
			return activateUserValidation(result);
		}
		
		long oldGatewayId = 0;

		try {
			
			try {
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));
				to_address = prop.getProperty("to_address");
				cc_address = prop.getProperty("cc_address");
				bcc_address = prop.getProperty("bcc_address");

			} catch (Exception e) {
				log.info("signup:::" + e.getMessage() + "" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", errorMsg);
				errorCode = "ER001";
				errorResponse = RegisterUserError.ER001;
				return response;
			}
			
			if( checkRecallQRC ) {
				boolean qrcAvailable = gatewayServiceV4.checkRecallQRC(activateUser.getQrcCode());
				if( qrcAvailable ) {
					response.put("Status", 0);
					response.put("Msg", RegisterUserError.recallQRCMsg);
					errorResponse = "Old device need to recall";
					return response;
				}
			}

			// Validate QRC Code
			if (!(qrcCode.matches("[0-9]+")) || !(qrcCode.length() == 6 || (qrcCode.length() == 18 && qrcCode.substring(0, 4).equalsIgnoreCase("BSCN"))) || qrcCode.startsWith("8") ) {
				log.info("Invalid QRCode . " + qrcCode);
				response.put("Status", 0);
				response.put("Msg", RegisterUserError.ER048);
				errorCode = "ER048";
				errorResponse = RegisterUserError.ER048;
				return response;
			}	

			mobilePage = activateUser.getMobilepage();

			if (mobilePage.equalsIgnoreCase("login") && !isAlreadyUser) {
				if (!_helper.isValidEmail(activateUser.getEmail().trim())) {
					response.put("Status", 0);
					response.put("Msg", "Please enter valid Email!");
					return response;
				}
			}
			if (mobilePage.equalsIgnoreCase("account")) {

				if (activateUser.getUsername().trim().equals("") || activateUser.getUsername().trim().isEmpty()) {
					log.info("Username for product registration from account page is empty.");
					response.put("Status", 0);
					response.put("Msg", errorMsg);
					errorCode = "ER043";
					errorResponse = RegisterUserError.ER043;
					return response;
				} else {
					userName = activateUser.getUsername().trim();
				}
			} else if (mobilePage.equalsIgnoreCase("home") && !isAlreadyUser) {

				if (!activateUser.getQrcCode().trim().equals(activateUser.getUsername())) {
					response.put("Status", 0);
					response.put("Msg", RegisterUserError.ER047);
					errorCode = "ER047";
					errorResponse = RegisterUserError.ER047;
					return response;
				}

				userName = activateUser.getQrcCode().trim();
			} else {
				userName = activateUser.getEmail().trim();
				userName = userName.toLowerCase();
				userName = userName.replaceAll("\\s+", "");
			}

			orderChannel = setOrderChannel(activateUser);
			if (show_orderid) {
				if (orderChannel.equalsIgnoreCase("rv")) {
					subscriptionStatus = "1";
				} else if (activateUser.getPurchasedfrom().equalsIgnoreCase("others")) {
					response.put("Status", 0);
					String eRes1 = RegisterUserError.otherUserMessage;
					errorResponse = eRes1.replace("#SP#", supportP).replace("#SM#", supportM);
					response.put("Msg", errorResponse);
					errorCode = "ER042";
					//errorResponse = RegisterUserError.ER042;
					return response;
				}
			}

			log.info("Validating Pet Name");
			if (isAlreadyUser) {
				User petUser = new User();
				try {
					petUser = userService.getUserByName(userName);
					recalUserId = petUser.getId();
					DeviceReplaced deviceReplaced = userService.getRecallDeviceDetails(petUser.getId());
										
					if( deviceReplaced != null ) {
						oldMeid = deviceReplaced.getMeid();
						isRecallDevice = deviceReplaced.getIsReplaced();
						
						if( isRecallDevice == 1 ) {
							show_warranty_popup = false;
						}
					}
					
					List<JGateway> userGateway = gatewayService.getGateway(null, null, null, null, petUser.getId(),
							null);
					
					if (userGateway != null && userGateway.size() > 0) {
						final String tempOldMeid = oldMeid;
						
						 if(!userGateway.stream().anyMatch(gateway -> gateway.getMeid().equalsIgnoreCase(tempOldMeid))) {
							 isRecallDevice=0;
							 oldMeid = "NA";
						}
						
						if (userGateway.size() >= 5 && !(isRecallDevice>0) ) {
							response.put("Status", 0);
							response.put("Msg", RegisterUserError.deviceCountMessage);
							errorCode = "deviceCountMessage";
							errorResponse = "Device Count Restriction : " + RegisterUserError.deviceCountMessage;
							return response;
						}
						response = checkGateway(userGateway, qrcCode, petName);

						if (response.getResponse().containsValue("ER006")) {
							errorCode = "ER006";
							errorResponse = RegisterUserError.ER006;
							return response;
						} else {
							int i = 1;
							String newPetname = petName;
							Loop: while (response != null) {
								if (response.getResponse().containsValue("ER044")) {
									newPetname = petName + "-" + i;
									response = checkGateway(userGateway, qrcCode, newPetname);
									i = i + 1;
								} else {
									activateUser.setGatewayName(newPetname);
									break Loop;
								}
							}
						}
					} else {
						 isRecallDevice=0;
						 oldMeid = "NA";
					}
				} catch (InvalidUsernameException ex) {
					response.put("Status", 0);
					response.put("Msg", errorMsg);
					errorCode = "ER025";
					errorResponse = "Validating Pet Name : " + RegisterUserError.ER025;
					return response;
				}
			}

			// Check if gateway name contains special
			String gateway_name = activateUser.getGatewayName().trim();
			Pattern special = Pattern.compile("[!@#$%&*()+=|<>?{}.,\\[\\]~'\"]");
			Matcher hasSpecial = special.matcher(gateway_name);
			if (hasSpecial.find()) {
				response.put("Status", 0);
				errorCode = "ER009";
				errorResponse = RegisterUserError.ER009;
				return response;
			}

			JSONObject jorderIdCheckResponse = new JSONObject();
			if (show_orderid) {// hide order id
				jorderIdCheckResponse = userService.getNiomGetOrderCount(orderChannel, orderID);

				if (jorderIdCheckResponse == null) {
					response.put("Status", 0);
					response.put("Msg", errorMsg);
					log.info("Error Code : ER035. " + RegisterUserError.ER035);
					errorResponse = RegisterUserError.ER035;
					errorCode = "ER035";
					return response;
				}

				int orderIdCheckStatus = jorderIdCheckResponse.getInt("Status");

				if (orderIdCheckStatus > 0 ? true : false) {
					order = gson.fromJson(jorderIdCheckResponse.getJSONObject("Order").toString(), Orders.class);
					int totalOrderCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalordered"));
					int totalMappedCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalmapped"));

					if (!(totalMappedCount < totalOrderCount)) {
						response.put("Status", 0);
						response.put("Msg", errorMsg);
						log.info("Error Code : ER040. " + RegisterUserError.ER040);
						errorResponse = RegisterUserError.ER040;
						errorCode = "ER040";
						return response;
					}
					log.info("Order id found in niom. Order Channel : " + order.getOrder_acc_type().getAcc_type()
							+ "Order ID : " + order.getOrder_id() + "Out Order ID  :" + order.getExternal_order_id());
					if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("amazon") || order.getOrder_acc_type().getAcc_type().toLowerCase().contains("walmart")) {
						if (order.getBilling_email().toLowerCase().contains("na")
								|| order.getBilling_phone().toLowerCase().contains("na")) {
							order.setBilling_email(userEmail);
							order.setBilling_phone(userPhone);
							order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
							order.setWelcome_status(order.getWelcome_status());
							boolean updateExternalOrderData = niomDbservice.updateExternalOrdersInNiom(order);
						}
					} 
//					else if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("walmart")) {
//						if (order.getBilling_email().toLowerCase().contains("na")
//								|| order.getBilling_phone().toLowerCase().contains("na")) {
//							order.setBilling_email(userEmail);
//							order.setBilling_phone(userPhone);
//							order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
//							order.setWelcome_status(order.getWelcome_status());
//							boolean updateExternalOrderData = niomDbservice.updateExternalOrdersInNiom(order);
//						}
//					} 
					else if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("facebook")) {

						if (nameCheck(order, activateUser)) {
							response.put("Status", 0);
							String eRes1 = RegisterUserError.orderIDNotMatched;
							String msg = eRes1.replace("#SP#", supportP).replace("#SM#", supportM);
							response.put("Msg", msg);
							
							log.info("Error Code : ER038 : " + RegisterUserError.ER038);
							errorCode = "ER038";
							String eRes2 = RegisterUserError.ER038;
							errorResponse = eRes2.replace("#SP#", supportP).replace("#SM#", supportM);
							return response;
						}
					}
				} else {
					response.put("Status", 0);
					
					String eRes1 = RegisterUserError.ER039;
					errorResponse = eRes1.replace("#SP#", supportP).replace("#SM#", supportM);
					response.put("Msg", errorResponse);
					log.info("Order id not found in niom, Error Code : ER039 :" + errorResponse);
					errorCode = "ER039";
					return response;
				}
			}

			// Getting Inventory thru DataBase
			JSONObject jresponse = new JSONObject();
			jresponse = userService.getInventory(qrcCode);

			if (jresponse == null) {
				response.put("Status", 0);
				response.put("Msg", errorMsg);
				log.info("Error Code : ER002. : " + RegisterUserError.ER002);
				errorResponse = RegisterUserError.ER002;
				errorCode = "ER002";
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			int status = jresponse.getInt("Status");

			if (status > 0 ? true : false) {
				try {
					Type inventoryList = new TypeToken<ArrayList<Inventory>>() {
					}.getType();

					List<Inventory> _inventoryList = gson.fromJson(jresponse.getJSONArray("inventory").toString(),
							inventoryList);

					if (_inventoryList.size() == 0) {
						response.put("Status", 0);
						response.put("Msg",	"No device found for scanned QR Code : " + activateUser.getQrcCode()
										+ ". Please contact us at " + supportContactNumber.get(device_country) + " / or send email to "
										+ supportContactEmail.get(device_country) + ".");

						errorResponse = "No device found for respective QR Code : " + activateUser.getQrcCode();

						log.info("No device found for respective qrccode" + activateUser.getQrcCode());
						errorCode = "ER003";
						return response;
					} else {

						inventory = gson.fromJson(jresponse.getJSONArray("inventory").get(0).toString(),
								Inventory.class);

						monitortype = inventory.getDevicemodelnumber();
						initialDeviceStateid = (int) inventory.getDevicestate().getId();

						AssetModel assetModel = gatewayDao.getAssetModelByName(monitortype);

						if (assetModel != null) {
							model_id = (int) assetModel.getId();
							monitortypeid = assetModel.getMonitor_type().getId();
							sensor_available = assetModel.getSensoravailable();
							enableDelayFreq = assetModel.isEnableDelayFreq();
							delayFreqSecs = assetModel.getDelayFreqSecs();

							response.put("Monitortypeid", monitortypeid);
						} else {
							response.put("Status", 0);
							response.put("Msg", errorMsg);

							log.info("Device Model not found in Assetmodel table :" + monitortype);

							errorCode = "ER014";
							errorResponse = "Assetmodel not found for " + monitortype;
							return response;
						}

						// #8-shipped to reseller : Check whether the device is sold by reseller
						if (inventory.getDevicestate().getId() == 8 && show_orderid) {
							String emailContent = new com.nimble.irisservices.helper.EmailContent()
									.sucscripeResellerContent(activateUser);

							if (email_helper.SendEmail_SES(to_address, cc_address,
									bcc_address, "Offline Order User Details", emailContent)) {
								log.info("Offline Reseller User details send Successfully to internal team .");
							}

							response.put("Status", 0);
							response.put("Msg", errorMsg);
							log.info(errorMsg);
							errorResponse = RegisterUserError.ER030;
							errorCode = "ER030";
							return response;
						}

						// #9-shipped to amazon #2-shipped full filment
						if (inventory.getDevicestate().getId() != 9 && inventory.getDevicestate().getId() != 2) {
							response.put("Status", 0);
							response.put("Msg", errorMsg);

							log.info("Device State ID is not suitable to activate the product . Device State ID :"
									+ inventory.getDevicestate().getId() + "  QRC Code : " + qrcCode);

							errorCode = "ER032";
							errorResponse = "Device State ID is not suitable to activate the product . Device State ID :"
									+ inventory.getDevicestate().getId() + "  QRC Code : " + qrcCode;
							return response;
						}

						if (show_orderid) { // hide order id
							List<Ordermap> orderMapList = new ArrayList<Ordermap>();
							Gson _gson = new Gson();

							Type orderListType = new TypeToken<ArrayList<Ordermap>>() {
							}.getType();
							orderMapList = _gson.fromJson(jorderIdCheckResponse.getJSONArray("Ordermap").toString(),
									orderListType);

							if (orderMapList.size() > 0) {
								for (Ordermap map : orderMapList) {
									if (map.getMeid().trim().equals(inventory.getMeid().trim())) {
										response.put("Status", 0);
										response.put("Msg", errorMsg);
										log.info(
												"Device is already mapped to some other orders . Need to check in the NIOM order map table.");
										response.put("ErrorCode", "ER005");
										errorCode = "ER005";
										errorResponse = RegisterUserError.ER005;
										return response;
									}
								}
							}
						}
						// Check Device is already activated
						JGatewayUserDetails gatewayUserDetails = gatewayService.getGateway(inventory.getMeid());

						if (gatewayUserDetails != null) {
							// isDeviceAlredyActivated = true;

							if (gatewayUserDetails.getUserName().toLowerCase().contains("sanjeevitest")) {
								isTestUserDevice = true;
							} else if ((gatewayUserDetails.getUserName().equalsIgnoreCase(qrcCode)
									&& gatewayUserDetails.getUserName().matches("[0-9]+")
									&& gatewayUserDetails.getUserName().length() == 6)) {
								isQrcUserDevice = true;
							}

							if (isTestUserDevice || (isQrcUserDevice && !mobilePage.equalsIgnoreCase("home"))
									|| (isQrcUserDevice && mobilePage.equalsIgnoreCase("home") && isAlreadyUser)) {
								log.info("getGateway: Gateway " + inventory.getMeid() + " is mapped to username - "
										+ gatewayUserDetails.getUserName());

								try {
									User _user = userService.verifyAuthKey(gatewayUserDetails.getAuthkey());
									gatewayService.delGateway(_user, Long.toString(gatewayUserDetails.getGatewayid()));
								} catch (Exception e) {
									response.put("Status", 0);
									errorCode = "ER031";
									response.put("Msg", errorMsg);
									log.error("Error while deleting gateway :" + e.getLocalizedMessage());
									errorResponse = RegisterUserError.ER031;
									return response;
								}

							} else if (isQrcUserDevice && mobilePage.equalsIgnoreCase("home") && !isAlreadyUser) {
								isQrcUserDevice = true;
							} else {

								log.info("getGateway: Gateway " + inventory.getMeid() + " is mapped to username - "
										+ gatewayUserDetails.getUserName());
								response.put("Status", 0);
								response.put("Msg", errorMsg);
								errorCode = "ER033";
								errorResponse = RegisterUserError.ER033;
								return response;
							}
						}

						// check MEID is already activated if activated return
						// the response

						if (false) {
							return null;
						} else {
							
							boolean pushResponse = false;
							
							if( isRecallDevice == 1 ) { // 1 - Replaced Device
								boolean isAvailableInOrderMap = niomDbservice.checkMeidIsAvailableInOrderMap(oldMeid);
								
								if( isAvailableInOrderMap ) {
									pushResponse = niomDbservice.changeMeidAndReplaceInOrderMap(oldMeid,inventory.getMeid());
								}
								
							}
							
							if (show_orderid) { // hide order id
						
								if( isRecallDevice != 1 ) {
									pushResponse = userService.orderMapping(inventory.getMeid(),
											_helper.getCurrentTimeinUTC(), inventory.getDevicestate().getId(),
											order.getOrder_id() + "", order.getDevicemodel(), subscriptionStatus,
											"1", "auto", gatewayId);
								}

								if (pushResponse) {
									orderMappedtatus = true;
									orderid_given = true;
								} else {
									response.put("Status", 0);
									response.put("SpecificMsg", "Unable to map the order");
									response.put("Msg", errorMsg);
									errorCode = "ER041";
									errorResponse = RegisterUserError.ER041;
									return response;
								}

								log.info("Push Data Response : " + orderMappedtatus);
								log.info("Activate Sim from Config : " + activateSim);
							} else {
								orderMappedtatus = true;
							}

							// Create Gateway
							try {

								if ((!isAlreadyUser && (isQrcUserDevice && !mobilePage.equalsIgnoreCase("home")))
										|| (!isAlreadyUser && mobilePage.equalsIgnoreCase("login")
												&& !isQrcUserDevice)) {

									JResponse jResponseSignUp = userSignUp(activateUser, userName,country);
									if ((Integer) jResponseSignUp.getResponse().get("Status") == 0) {
										return jResponseSignUp;
									}
								}
								log.info("==Get user===========");
								User user = new User();
								try {
									user = userService.getUserByName(userName);
									intialUser = user;
									orderMapUser = user;
									provisionStatus = true;
									createuser_id = user.getId();
								} catch (InvalidUsernameException ex) {
									log.error("==Invalid username exception===========");
									loginCreationIssue = true;
									response.put("Status", 0);
									response.put("Msg", errorMsg);
									errorCode = "ER025";
									errorResponse = "Getting user Details : " + RegisterUserError.ER025;
									return response;
								}

								String levelid = "1";
								String group = "";

								List<JGroups> jgroups = groupservices.getGroups(group, group, levelid,
										user.giveCompany().getId());

								if (jgroups.get(0) != null) {
									group_id = jgroups.get(0).getId();
								}

								Timestamp orderDate = Timestamp.valueOf(order.getDatetime());
								Timestamp curDateTime = Timestamp.valueOf(IrisservicesUtil.getCurrentTimeUTC());
								long daysDiff = TimeUnit.DAYS.convert(curDateTime.getTime() - orderDate.getTime(),
										TimeUnit.MILLISECONDS);
								Template welcomeEmailTemplate = (Template) templates.getTemplate("welcomeemail.ftl");
								Map<String, String> welcomeEmailParams = new HashMap<>();
								welcomeEmailParams.put("FIRSTNAME", activateUser.getFirstname());
								welcomeEmailParams.put("USERNAME", user.getEmail());
								welcomeEmailParams.put("FREEVPMNOTE", "");
								welcomeEmailParams.put("COPYRIGHT_YEAR", _helper.getCurrentYear());
								boolean isFreeVpmAdded = false;

								// Register QRC User
								if (isQrcUserDevice && mobilePage.equalsIgnoreCase("home") && !isAlreadyUser) {
									log.info("==QRC User===========");

									Gateway gateway = new Gateway();

									List<JGateway> gateways = gatewayService.getGateway("", "", "", "", user.getId(),
											inventory.getMeid());

									CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());

									boolean updateQRCGateway = false;

									if (gateways != null) {

										log.info("==Get Gateways not null===========");

										if (gateways.size() > 0) {

											JGateway jGateway = gateways.get(0);
											try {
												jGateway = gatewayService.gatewayExitsinDB(jGateway,
														user.giveCompany().getId());
												jGateway.setName(gateway_name);
												jGateway.setModelid(model_id);
												jGateway.setSensorEnable(sensor_available);

												if (jGateway.getQrcode().equals("NA")) {
													jGateway.setQrcode(qrcCode);
												}

												gateway = gatewayService.saveORupdateQRCGateway(jGateway,
															user.giveCompany().getId());
											
												
												updateQRCGateway = true;
											} catch (Exception ex) {

												updateQRCGateway = false;
												gatewayCreationIssue = true;

												log.error("Exception while updating gateway for QRC Login : " + userName
														+ "Exception : " + ex.getMessage());
											}

											try {

												log.info("==Updated Alerts ===========");
												List<JAlertCfg> alertcfgs = alertCfgService.getAlertCfg("", "",
														Long.toString(jGateway.getId()), user.getId(),
														cmp_cfg.getTemperatureunit());

												for (JAlertCfg alerts : alertcfgs) {

													Long[] assetIds = new Long[1];
													assetIds[0] = jGateway.getId();

													alerts.setEmailids(userEmail);
													alerts.setMobilenos(userPhone);
													//alerts.setAlerttypeid(alerts.getAlerttype().getId());
													alerts.setAssetids(assetIds);

													boolean result1 = alertCfgService.saveORupdateAlertCfg(alerts,
															user.giveCompany().getId(), "registerproduct");
												}
												updateQRCGateway = true;

											} catch (Exception ex) {
												log.error("==Excepion  Alerts Saved==========="
														+ ex.getLocalizedMessage());
												gatewayCreationIssue = true;
												updateQRCGateway = false;
												log.error("Exception while updating  alerts for QRC Login : " + userName
														+ "Exception : " + ex.getMessage());
											}
										}
									}
									boolean updateQRCLogin = false;
									if (updateQRCGateway) {
										try {
											log.info("==2.Update QRC User name===========");
											user = userService.getUserByName(qrcCode.trim());
											user.setUsername(userEmail.trim());

											Set<Gateway> _gateway = new HashSet<Gateway>();
											_gateway.add(gateway);
											String password = "";

											if (activateUser.isPwd_update()) {
												password = activateUser.getPassword().trim();
											} else {
												password = activateUser.getPhoneno();
												password = password.replaceAll("\\W", "");

												if (password.length() > 10) {
													password = password.substring(password.length() - 10);
												}
											}
											user.setPassword( _helper.bCryptEncoder(password) );
											user.setPassword_ver("V2");
											user.setGateways(_gateway);
											user.setEmail(userEmail.trim());
											user.setMobileno(userPhone);
											user.setAuthKey(_helper.encryptAndSetUser(userEmail.trim()));
											user.setVerified(true);
											user.setCountry(country);
											userService.updateUser(user);
											
											//update evalidation
											async.updateEvalidation(user.getId(), password);

											orderMapUser = user;

											Company cmp = companyService.getCompany(user.giveCompany().getId());
											cmp.setMobileno(userPhone);
											cmp.setPhoneno(userPhone);
											cmp.setEmail(userEmail.trim());
											companyService.updateCompany(cmp);
											updateQRCLogin = true;
											sendDynamicMsg(gateway);

										} catch (Exception ex) {
											loginCreationIssue = true;
											log.error("Exception in updating the QRC User Name ="
													+ ex.getLocalizedMessage());
											log.error("Exception while updating username and password for QRC Login : "
													+ userName + "Exception : " + ex.getMessage());
										}
									}

									if (updateQRCLogin) {

										alertCreationStatus = "UpdateGatewayCreditStatus : True"
												+ "<br/><br/>SaveGatewayReportStatus : True"
												+ "<br/><br/>SaveLastGatewayReport : True";

										alertCreationStatus = alertCreationStatus + "<br/><br/>CreateTempAlertStatus :"
												+ "True" + "<br/><br/>CreateBatteryAlertStatus :" + "True"
												+ "<br/><br/>CreateOnBatteryAlertStatus : " + "True"
												+ "<br/><br/>CreateNetworkAlertStatus : " + "True";

										if (show_orderid) {
											orderMappedtatus = niomDbservice.updateOrdermapUserDetails(
													order.getOrder_id(), user.getUsername(), user.getId() + "",
													user.getPassword());

											updateExternalOrderDataStatus = userService.updateOrdersDataV2(orderChannel,
													order, inventory);
										}

										accountActivatedStatus = true;

										String emailContent = "";

										if (daysDiff <= Long.valueOf(freeVPM_days) && addFreeVpmFlag) {
											welcomeEmailParams.put("FREEVPMNOTE",
													"<li>By registering, you just got a free WaggleVet session. Grab it on the Kennel screen.</li>");
											async.addFreeVPM("vpm", user.getId());
											isFreeVpmAdded = true;
										}

										ResponseEntity<String> newEmailContent = ResponseEntity.ok(
												FreeMarkerTemplateUtils.processTemplateIntoString(welcomeEmailTemplate,
														welcomeEmailParams));
										emailContent = newEmailContent.getBody();
										if (email_helper.SendEmail_SES(user.getEmail(),
												null, bcc_address, "Welcome to Waggle!", emailContent)) {
											log.info("User Created Successfully : " + user.getEmail());
											provisionStatus = true;
										}

										User updatedUser = userService.getUserByName(userEmail.trim());

										response.put("Status", 1);

										response.put("Msg",
												(isFreeVpmAdded ? RegisterUserError.homePageActivationWithVPM_Message
														: RegisterUserError.homePageActivationMessage));
										response.put("display_msg",
												(isFreeVpmAdded
														? RegisterUserError.homePageActivationWithVPM_DisplayMessage
														: RegisterUserError.homePageActivationMessage));
										response.put("gatewayid", gateway.getId());
										response.put("show_warranty_popup", show_warranty_popup);
										response.put("warranty_msg", warranty_msg);
										response.put("User", updatedUser);
										response.put("subscription_buynow_popup", subscriptionBuyNowPopUp);
										accountCreated = true;
										
										gatewayId = gateway.getId();
										
										return response;
									} else {
										log.info("==2.Error Update QRC User name DONE===========");
										response.put("Status", 0);
										response.put("Msg", errorMsg);
										errorResponse = "Updating QRC user Details : " + RegisterUserError.ER046;
										errorCode = "ER046";
										return response;
									}
								}
								// Create Gateway and Assign to the user
								//JGateway jgateway = new JGateway();

								Gateway gateway = new Gateway();
								boolean updateCreaditStatus = false;
								boolean saveGatewayReportStatus = false;
								boolean saveLastGatewayReport = false;

								JCreateGateway jcreateGateway = new JCreateGateway();
								
								if (!oldMeid.equalsIgnoreCase("NA"))
									replacedGateway = gatewayService.getGatewayDetails(oldMeid);

								jcreateGateway = createOrUpdateGateway(activateUser, inventory, model_id, group_id,
										sensor_available, passwordType, user,oldMeid,isRecallDevice);

								log.info("== gateway Created and assiged to user===========");

								if (jcreateGateway != null) {
									log.info("== Update credit status===========");
									updateCreaditStatus = jcreateGateway.isUpdateCreaditStatus();
									saveGatewayReportStatus = jcreateGateway.isSaveGatewayReportStatus();
									saveLastGatewayReport = jcreateGateway.isSaveLastGatewayReport();

									alertCreationStatus = "UpdateGatewayCreditStatus : " + updateCreaditStatus
											+ "<br/><br/>SaveGatewayReportStatus : " + saveGatewayReportStatus
											+ "<br/><br/>SaveLastGatewayReport :" + saveLastGatewayReport;

									if (jcreateGateway.isGatewayCreated()) {
										//JGateway jgateway = jcreateGateway.getJgateway();
										gateway = jcreateGateway.getGateway();

										log.info("== Create alerts===========");
//										boolean createBatteryAlertStatus = false;
//										boolean createOnIdleAlertStatus = false;
										long mtype = gateway.getModel().getMonitor_type().getId();

										if (mtype == 1 && isRecallDevice==0 ) {

											String alertStatus = alertCfgServiceV4.createPMAlerts(user, gateway, enableDelayFreq,
													delayFreqSecs, activateUser.getLat(), activateUser.getLon(),"registerproduct",gateway.getModel().isIs_aqi());

											alertCreationStatus = alertCreationStatus + alertStatus;

										}

										if (jcreateGateway.isGatewayCreated()) {
											String emailContent = "";

											if (daysDiff <= Long.valueOf(freeVPM_days) && addFreeVpmFlag) {
												welcomeEmailParams.put("FREEVPMNOTE",
														"<li>By registering, you just got a free WaggleVet session. Grab it on the Kennel screen.</li>");
												async.addFreeVPM("vpm", user.getId());
												isFreeVpmAdded = true;
											}

											ResponseEntity<String> newEmailContent = ResponseEntity
													.ok(FreeMarkerTemplateUtils.processTemplateIntoString(
															welcomeEmailTemplate, welcomeEmailParams));
											emailContent = newEmailContent.getBody();

											if (email_helper.SendEmail_SES(
													user.getEmail(), null, bcc_address, "Welcome to Waggle!",
													emailContent)) {
												log.info("User Created Successfully : " + user.getEmail());
												provisionStatus = true;
											}
										}
										if (show_orderid) { // hide order id
											orderMappedtatus = niomDbservice.updateOrdermapUserDetails(
													order.getOrder_id(), user.getUsername(), user.getId() + "",
													user.getPassword());

											updateExternalOrderDataStatus = userService.updateOrdersDataV2(orderChannel,
													order, inventory);
										}
										accountActivatedStatus = true;

									} else {
										gatewayCreationIssue = true;
									}
								} else {
									gatewayCreationIssue = true;
								}

								if (accountActivatedStatus) {
									
									sendDynamicMsg(gateway);

									response.put("Status", 1);
									response.put("gatewayid", gateway.getId());
									response.put("show_warranty_popup", show_warranty_popup);
									response.put("warranty_msg", warranty_msg);
									response.put("subscription_buynow_popup", subscriptionBuyNowPopUp);
									response = getStatusMsg(response,mobilePage,monitortypeid,isFreeVpmAdded);
									
//									if (mobilePage.toLowerCase().contains("login")) {
//										if (monitortypeid == 1) {
//											response.put("Msg",
//													(isFreeVpmAdded
//															? RegisterUserError.loginPageActivationWithVPM_Message
//															: RegisterUserError.loginPageActivationMessage));
//											response.put("display_msg", (isFreeVpmAdded
//													? RegisterUserError.loginPageActivationWithVPM_DisplayMessage
//													: RegisterUserError.loginPageActivationMessage));
//										} else {
//											response.put("Msg", RegisterUserError.loginPageFurbitActivationMessage);
//											response.put("display_msg",
//													RegisterUserError.loginPageFurbitActivationMessage);
//										}
//
//									} else if (mobilePage.toLowerCase().contains("home")) {
//										if (monitortypeid == 1) {
//											response.put("Msg",
//													(isFreeVpmAdded
//															? RegisterUserError.homePageActivationWithVPM_Message
//															: RegisterUserError.homePageActivationMessage));
//											response.put("display_msg",
//													(isFreeVpmAdded
//															? RegisterUserError.homePageActivationWithVPM_DisplayMessage
//															: RegisterUserError.homePageActivationMessage));
//										} else {
//											response.put("Msg", RegisterUserError.loginPageFurbitActivationMessage);
//											response.put("display_msg",
//													RegisterUserError.loginPageFurbitActivationMessage);
//										}
//
//									} else if (mobilePage.toLowerCase().contains("account")) {
//										if (monitortypeid == 1) {
//											response.put("Msg",
//													(isFreeVpmAdded
//															? RegisterUserError.accountPageActivationWithVPM_Message
//															: RegisterUserError.accountPageActivationMessage));
//											response.put("display_msg", (isFreeVpmAdded
//													? RegisterUserError.accountPageActivationWithVPM_DisplayMessage
//													: RegisterUserError.accountPageActivationMessage));
//										} else {
//											response.put("Msg", RegisterUserError.accountPageFurbitActivationMessage);
//											response.put("display_msg",
//													RegisterUserError.accountPageFurbitActivationMessage);
//										}
//
//									} else {
//										if (monitortypeid == 1) {
//											response.put("Msg",
//													(isFreeVpmAdded
//															? RegisterUserError.loginPageActivationWithVPM_Message
//															: RegisterUserError.loginPageActivationMessage));
//											response.put("display_msg", (isFreeVpmAdded
//													? RegisterUserError.loginPageActivationWithVPM_DisplayMessage
//													: RegisterUserError.loginPageActivationMessage));
//										} else {
//											response.put("Msg", RegisterUserError.loginPageFurbitActivationMessage);
//											response.put("display_msg",
//													RegisterUserError.loginPageFurbitActivationMessage);
//										}
//
//									}
									response.put("User", user);
									
									accountCreated = true;
									
									gatewayId = gateway.getId();
									
									return response;
								} else {
									response.put("Status", 0);
									response.put("Msg", errorMsg);
									errorResponse = "Getting user Details : " + RegisterUserError.ER045;
									errorCode = "ER045";
									return response;
								}

							} catch (DataIntegrityViolationException e) {
								gatewayCreationIssue = true;
								log.error("== Exception 1===========" + e.getLocalizedMessage());
								response.put("Status", 0);

								if (Integer.parseInt(companyType) == 3) {
									response.put("Msg", "Pet Name already exists. Please enter valid one. ");
									response.put("display_msg", "Pet Name already exists. Please enter valid one. ");
								} else {
									response.put("Msg", "Asset name or MEID already exits");
									response.put("display_msg", "Asset name or MEID already exits");
								}

								errorCode = "ER015";
								errorResponse = RegisterUserError.ER015;
								return response;
							} catch (Exception e) {
								gatewayCreationIssue = true;
								response.put("Status", 0);
								response.put("Msg", errorMsg);
								log.error("saveORupdateGateway::::" + e.getLocalizedMessage());
								errorResponse = RegisterUserError.ER016 + "Exception : " + e.getMessage();
								errorCode = "ER016";
								return response;
							}
						}
					}
				} catch (Exception ex) {
					log.error("Error while getting meid for the respective qrc code. Exception :  " + ex.getMessage());
					response.put("Status", 0);
					response.put("Msg", errorMsg);
					errorCode = "ER019";
					errorResponse = RegisterUserError.ER019 + "Exception : " + ex.getMessage();
					return response;
				}
			} else {
				String msg = jresponse.getString("Msg");
				response.put("Status", 0);
				response.put("Msg", errorMsg);
				errorCode = "ER018";
				errorResponse = RegisterUserError.ER018 + "Response Message : - " + msg;
				log.info("Exception : No device found for respective qrccode. Response Message :  -" + msg);
				return response;
			}
		} catch (Exception e) {
			log.error("signup:::" + e.getMessage() + "" + e.getLocalizedMessage());
			gatewayCreationIssue = true;
			response.put("Status", 0);
			response.put("Msg", errorMsg);
			errorResponse = RegisterUserError.ER020;
			errorCode = "ER020";
			response.put("ErrorCode", "ER020");
			return response;
		} finally {
			
			

			if (alertCreationStatus == null) {
				alertCreationStatus = "Alert Created : false" + "<br/><br/>";
			}

			if (loginCreationIssue || gatewayCreationIssue) {
				orderMappedtatus = false;

				if (show_orderid) {// hide order id
					log.info(
							"Deleted mapping for meid :" + inventory.getMeid() + " User : " + intialUser.getUsername());

					userService.deleteordermap(inventory.getMeid(), initialDeviceStateid);
				}
			}

			if (gatewayCreationIssue && !isAlreadyUser && !mobilePage.equalsIgnoreCase("home")) {
//				long timeMilli = Calendar.getInstance().getTimeInMillis();
//				String newUserName = intialUser.getUsername();
//				
//				if(newUserName.length()>10)
//					newUserName = intialUser.getUsername().substring(0, 9) + "-" + timeMilli;
//
//				log.info("Username updated for new user : " + intialUser.getUsername() + " New UserName : "
//						+ newUserName);
//
//				intialUser.setUsername(newUserName);
//				intialUser.setEmail(newUserName);
//				intialUser.setEnable(false);
//				intialUser.setAuthKey(newUserName);
//				intialUser.setMobileno("**********");
//				orderMappedtatus = false;
//				if (!(intialUser.getSignupType() == null)) {
//					userService.updateUser(intialUser);
//				}
//
//				orderMapUser = intialUser;
				orderMappedtatus = false;
				orderMapUser = updateUser(intialUser);
			}

			if (isQrcUserDevice && !gatewayCreationIssue && isAlreadyUser) {
//				long timeMilli = Calendar.getInstance().getTimeInMillis();
//				
//				String newUserName = intialUser.getUsername();
//				
//				if(newUserName.length()>10)
//					newUserName = intialUser.getUsername().substring(0, 9) + "-" + timeMilli;
//				
//				log.info("Username updated for QRC user : " + intialUser.getUsername() + " New UserName : "
//						+ newUserName);

				User qrcUser = new User();
				try {
					qrcUser = userService.getUserByName(inventory.getQrc());
					orderMapUser = qrcUser;
					createuser_id = qrcUser.getId();
				} catch (InvalidUsernameException e) {
					log.error(e.getLocalizedMessage());
				}
				
				qrcUser = updateUser(qrcUser);
				
//				qrcUser.setUsername(newUserName);
//				qrcUser.setEmail(newUserName);
//				qrcUser.setAuthKey(newUserName);
//				qrcUser.setMobileno("**********");
//				qrcUser.setEnable(false);
//				userService.updateUser(qrcUser);
			}

			if (isQrcUserDevice && !gatewayCreationIssue && !isAlreadyUser && mobilePage.equalsIgnoreCase("login")) {

//				long timeMilli = Calendar.getInstance().getTimeInMillis();
//				String newUserName = intialUser.getUsername();
//				
//				if(newUserName.length()>10)
//					newUserName = intialUser.getUsername().substring(0, 9) + "-" + timeMilli;
//				log.info("Username updated for QRC user : " + intialUser.getUsername() + " New UserName : "
//						+ newUserName);

				User qrcUser = new User();
				try {
					qrcUser = userService.getUserByName(inventory.getQrc());
					createuser_id = qrcUser.getId();
				} catch (InvalidUsernameException e) {
					log.error(e.getLocalizedMessage());
				}
				qrcUser = updateUser(qrcUser);
//				qrcUser.setUsername(newUserName);
//				qrcUser.setEmail(newUserName);
//				qrcUser.setEnable(false);
//				qrcUser.setAuthKey(newUserName);
//				qrcUser.setMobileno("**********");
//				userService.updateUser(qrcUser);

			}
			long gateway_id = 0;
			if (isQrcUserDevice && (loginCreationIssue || gatewayCreationIssue)
					&& !mobilePage.equalsIgnoreCase("home")) {
				orderMappedtatus = false;
				User qrcUser = new User();
				try {
					qrcUser = userService.getUserByName(inventory.getQrc());
					createuser_id = qrcUser.getId();

					String levelid = "1";
					String group = "";

					// for order mapping details
					orderMapUser = qrcUser;
					List<JGroups> jgroups = groupservices.getGroups(group, group, levelid,
							qrcUser.giveCompany().getId());

					if (jgroups.get(0) != null) {
						group_id = jgroups.get(0).getId();
					}
					
					if (!oldMeid.equalsIgnoreCase("NA"))
						replacedGateway = gatewayService.getGatewayDetails(oldMeid);

					JCreateGateway jcreateGateway = createOrUpdateGateway(activateUser, inventory, model_id, group_id,
							sensor_available, passwordType, qrcUser,oldMeid,isRecallDevice);

					if (jcreateGateway.isGatewayCreated() && isRecallDevice==0 ) {
						String status =  alertCfgServiceV4.createPMAlerts(qrcUser, jcreateGateway.getGateway(), enableDelayFreq,
								delayFreqSecs, activateUser.getLat(), activateUser.getLon(), "registerproduct",jcreateGateway.getGateway().getModel().isIs_aqi());
					}
				} catch (InvalidUsernameException e) {
					log.error("QRC alert creation:" + e.getLocalizedMessage());
				}

			}

			String mailSub = "Failed : External Orders User Activation Status-Email : " + activateUser.getEmail();

			Offlineuserdetails offlineUser = new Offlineuserdetails();

			offlineUser.setQrccode(activateUser.getQrcCode());
			offlineUser.setName(activateUser.getFirstname() + " " + activateUser.getLastname());
			offlineUser.setEmail(activateUser.getEmail());
			offlineUser.setCompanytype(companyType);
			if (inventory.getDevicestate() == null) {
				offlineUser.setDevicestateId("NA");
			} else {
				offlineUser.setDevicestateId(Long.toString(inventory.getDevicestate().getId()));
			}

			if (activateUser.getAddress() == "" || activateUser.getAddress().isEmpty()) {
				offlineUser.setAddress("NA");
			} else {
				offlineUser.setAddress(activateUser.getAddress());
			}

			if (accountCreated) {

				if( isRecallDevice > 0 ) {
					async.mapOldGatewayToReturnAC(replacedGateway, returnLoginUsername, userName);
					//async.changeMeidAndReplaceInOrderMap( oldMeid,inventory.getMeid() );
					async.saveRecallHistory( recalUserId,oldMeid,inventory.getMeid(), isRecallDevice );
					async.changePetNameByGatewayId( petName , gatewayId );
				}
				
				devicecount = gatewayService.getDeviceCount(createuser_id);
				log.info("user device count for userid:" + createuser_id + " ,\n device count: " + devicecount);

				mailSub = "Success : External Orders User Activation Status-Email : " + activateUser.getEmail();

				String orderId = Long.toString(order.getOrder_id());
				if (!orderChannel.equals("rv")) {
					orderId = order.getExternal_order_id();
				}

				offlineUser.setStatus("Success");

				int amount = 0;
				String desc = "";

				String chargebeeId ="NA";
				if ( orderid_given) {
					chargebeeId = userServiceV4.createUserInChargebee(activateUser.getFirstname(),
							activateUser.getLastname(), activateUser.getEmail(), activateUser.getPhoneno(), userName,
							amount, desc);
					
					// Activate CB subscription
					if (!chargebeeId.equalsIgnoreCase("NA")) {
//						String cb_planid = crService.getCBPlan(String.valueOf(order.getOrder_id()));
//						if (!cb_planid.equalsIgnoreCase("NA") && !cb_planid.isEmpty() && product_subs_enable) {
//							boolean stat = cbService.createPaidSubscription(chargebeeId, cb_planid);
//		
//							if (stat) {
//								stat = crService.updateSubStatus(String.valueOf(order.getOrder_id()));
//		
//							}
//						}
					}
				}else {
					
					userServiceV4.createUserInChargebee(activateUser.getFirstname(), activateUser.getLastname(),
						activateUser.getEmail(), activateUser.getPhoneno(), userName, amount, desc);
				}
//
//				try {
//					UserV4 usrObj = userServiceV4.verifyAuthV4("id", String.valueOf(createuser_id));
//					if (usrObj != null)
//						cbID = usrObj.getChargebeeid();
//
//				} catch (Exception e) {
//					// TODO: handle exception
//				}

//				UserRvDetails rvObj = userServiceV4.getUserRvDetails(createuser_id);
//				boolean updatedRVProfile = (rvObj != null) ? true : false;

//				boolean userBadgeStat = rvServ.saveUserBadgeTxn(createuser_id, "NA", devicecount, updatedRVProfile,"NA");
//				log.info("in activate user:" + createuser_id + " RVer Badge created:" + userBadgeStat);

			} else {
				offlineUser.setStatus("Failed");
			}

			try {
				// Order Mapping Details
				Timestamp curTime = Timestamp.valueOf(IrisservicesUtil.getCurrentTimeUTC());
				Timestamp orderdate = Timestamp.valueOf(order.getDatetime());
				long userid = (orderMapUser != null ? orderMapUser.getId() : (long) 0L);

				if (accountCreated == false)
					userid = 0L;

				if( orderChannel == null )
					orderChannel = "NA";
				
				OrderMappingDetails orderMapDetail = new OrderMappingDetails(activateUser.getFirstname(),
						activateUser.getEmail(), activateUser.getAddress(), offlineUser.getDevicestateId(),
						activateUser.getQrcCode(), companyType, offlineUser.getStatus(), orderID, orderChannel, userid,
						curTime, orderdate, order.getExternalsku());
				log.info("orderMapDetail block", orderMapDetail);
				userService.saveOrderMappingDetails(orderMapDetail);
			} catch (Exception e) {
				log.error(" ActivateUser: Order Mapping Details: ", e.getLocalizedMessage());
			}

			String statusEmailContent = new com.nimble.irisservices.helper.EmailContent().activationStatus(activateUser,
					provisionStatus, orderMappedtatus, updateExternalOrderDataStatus, activateSimStatus,
					alertCreationStatus, errorResponse, monitortype, devicecount,isRecallDevice,oldMeid,
					inventory.getMeid(),returnLoginUsername,false,"NA");

			async.saveorupdateofflineUserDetails(offlineUser);
			

			String skuNumber =null;
			if (enableSkuBasedActivation) {
				AssetModel assetModel = gatewayService.getAssetModelByMeid(inventory.getMeid());
				if (assetModel != null && !assetModel.getSkuNumber().equalsIgnoreCase("NA"))
					skuNumber = assetModel.getSkuNumber();
			}

			String deviceId = inventory.getDeviceId();
			String tariffId = inventory.getTariffId();
			String qrc = inventory.getQrc();

			async.updateRegisterUserEmailStatus(to_address, cc_address, bcc_address, mailSub, statusEmailContent,
					qrcCode, errorCode, accountCreated, inventory.getMeid(), inventory.getSim_no(),
					inventory.getSim_vendor(),isRecallDevice,skuNumber,activateUser.getEmail(), deviceId, tariffId, qrc);

		}
	}
	public JResponse getStatusMsg(JResponse response, String mobilePage,long monitortypeid, boolean isFreeVpmAdded  ) {
		if (mobilePage.toLowerCase().contains("login")) {
			if (monitortypeid == 1) {
				response.put("Msg",
						(isFreeVpmAdded
								? RegisterUserError.loginPageActivationWithVPM_Message
								: RegisterUserError.loginPageActivationMessage));
				response.put("display_msg", (isFreeVpmAdded
						? RegisterUserError.loginPageActivationWithVPM_DisplayMessage
						: RegisterUserError.loginPageActivationMessage));
			} else {
				response.put("Msg", RegisterUserError.loginPageFurbitActivationMessage);
				response.put("display_msg",
						RegisterUserError.loginPageFurbitActivationMessage);
			}

		} else if (mobilePage.toLowerCase().contains("home")) {
			if (monitortypeid == 1) {
				response.put("Msg",
						(isFreeVpmAdded
								? RegisterUserError.homePageActivationWithVPM_Message
								: RegisterUserError.homePageActivationMessage));
				response.put("display_msg",
						(isFreeVpmAdded
								? RegisterUserError.homePageActivationWithVPM_DisplayMessage
								: RegisterUserError.homePageActivationMessage));
			} else {
				response.put("Msg", RegisterUserError.loginPageFurbitActivationMessage);
				response.put("display_msg",
						RegisterUserError.loginPageFurbitActivationMessage);
			}

		} else if (mobilePage.toLowerCase().contains("account")) {
			if (monitortypeid == 1) {
				response.put("Msg",
						(isFreeVpmAdded
								? RegisterUserError.accountPageActivationWithVPM_Message
								: RegisterUserError.accountPageActivationMessage));
				response.put("display_msg", (isFreeVpmAdded
						? RegisterUserError.accountPageActivationWithVPM_DisplayMessage
						: RegisterUserError.accountPageActivationMessage));
			} else {
				response.put("Msg", RegisterUserError.accountPageFurbitActivationMessage);
				response.put("display_msg",
						RegisterUserError.accountPageFurbitActivationMessage);
			}

		} else {
			if (monitortypeid == 1) {
				response.put("Msg",
						(isFreeVpmAdded
								? RegisterUserError.loginPageActivationWithVPM_Message
								: RegisterUserError.loginPageActivationMessage));
				response.put("display_msg", (isFreeVpmAdded
						? RegisterUserError.loginPageActivationWithVPM_DisplayMessage
						: RegisterUserError.loginPageActivationMessage));
			} else {
				response.put("Msg", RegisterUserError.loginPageFurbitActivationMessage);
				response.put("display_msg",
						RegisterUserError.loginPageFurbitActivationMessage);
			}

		}
		
		return response;
	}
	public User updateUser(User user) {
		
		long timeMilli = Calendar.getInstance().getTimeInMillis();
		String newUserName = user.getUsername();
		
		if(newUserName.length()>10)
			newUserName = user.getUsername().substring(0, 9) + "-" + timeMilli;

		log.info("Username updated for new user : " + user.getUsername() + " New UserName : "
				+ newUserName);

		user.setUsername(newUserName);
		user.setEmail(newUserName);
		user.setEnable(false);
		user.setAuthKey(newUserName);
		user.setMobileno("**********");
		if (!(user.getSignupType() == null)) {
			userService.updateUser(user);
		}
		return user;
	}

//	if (sendActivateUserDataToSQS_Microservice) {
//	async.sendEmailDataToQueue(mailSub, statusEmailContent, qrcCode, errorCode, accountCreated,
//			inventory.getMeid(), inventory.getSim_no(), inventory.getSim_vendor());
//} else {
//	async.updateRegisterUserEmailStatus(to_address, cc_address, bcc_address, mailSub, statusEmailContent,
//			qrcCode, errorCode, accountCreated, inventory.getMeid(), inventory.getSim_no(),
//			inventory.getSim_vendor());
//}
	
	
	private void sendDynamicMsg(Gateway gateway) {
		String messages = "";
		String deviceModel = gateway.getModel().getModel().toLowerCase();
		String gpsDynamicmsg = "gpsmode=standalone", gpsDynamicmsg2 = "SFWSTORAGE=ON",
				gpsDynamicmsg3 = "reportinterval=900", gpsDynamicmsg4 = "maxsleeptime=900",
				gpsDynamicmsg5 = "modemresetint=3600", gpsDynamicmsg6 = "setqrc=";
		String n7Cmd = "batoffset=-0.7,chgoffset=1.9,tempoffset=-1.2,fullchgoffset=-2.5";

		if (!gateway.getQrcode().isEmpty() && !gateway.getQrcode().equalsIgnoreCase("NA"))
			gpsDynamicmsg6 = gpsDynamicmsg6 + gateway.getQrcode().trim();
		else
			gpsDynamicmsg6 = "";

		if (deviceModel.contains("nt3d")) {
			messages = gpsDynamicmsg3 + "," + gpsDynamicmsg4 + "," + gpsDynamicmsg2 + "," + gpsDynamicmsg5;
		} else if (deviceModel.contains("nt3f")) {
			messages = gpsDynamicmsg2 + "," + gpsDynamicmsg3 + "," + gpsDynamicmsg4 + "," + gpsDynamicmsg5;
		} else if (deviceModel.contains("n1")) {
			messages = gpsDynamicmsg + "," + gpsDynamicmsg2;   //N7A-503 NT3K N7-504 NT3K
		} else if (deviceModel.contains("n7")) {
			messages = gpsDynamicmsg3 + "," + gpsDynamicmsg4 + "," + gpsDynamicmsg2 + "," + gpsDynamicmsg5; 
			
			if( !(deviceModel.equalsIgnoreCase("n7a-503 nt3k") || deviceModel.equalsIgnoreCase("n7-504 nt3k")) ) {
				messages = messages + "," + n7Cmd;
			}
		}
		if (!gpsDynamicmsg6.isEmpty())
			messages = messages + "," + gpsDynamicmsg6;

		async.sendDynamicCommand(Long.toString(gateway.getId()), messages, 0L);
	}

	private JResponse userSignUp(@Valid ActivateUser activateUser, String userName,String country) {
		boolean loginCreationIssue = false;
		JResponse response = new JResponse();
		String errorResponse;
		String errorCode;
		if(country.equalsIgnoreCase("US")|| country.equalsIgnoreCase("NA")
				|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")
				|| country.isEmpty() || country == null) {
			country = "US";
		}
		
		String errorMsg = "Unable to register the product. Please send email to " + supportContactEmail.get(country) + ".";
		Company company = null;
		try {

			String userEmail = activateUser.getEmail().trim();
			userEmail = userEmail.toLowerCase();
			userEmail = userEmail.replaceAll("\\s+", "");
			String userPhone = activateUser.getPhoneno().trim();
			userPhone = userPhone.replace("(", "");
			userPhone = userPhone.replace(")", "");
			// Sign up the new USER
			log.info("==Entered into signup Section===========");
			SignUp signUp = new SignUp();
			signUp.setUsername(userName.toLowerCase());

			if (activateUser.getPurchasedfrom().toLowerCase().contains("rv")) {
				signUp.setFirstname(activateUser.getFirstname());
				signUp.setLastname(activateUser.getLastname());
			}

			String password = "";

			if (activateUser.isPwd_update()) {
				password = activateUser.getPassword().trim();
			} else {
				password = activateUser.getPhoneno();
				// password = password.replaceAll("\\W", "");
				password = password.replaceAll("[^\\d.]", ""); // remove other than numbers
				if (password.length() > 10) {
					password = password.substring(password.length() - 10);
				}
			}

			signUp.setPassword( _helper.bCryptEncoder(password) );
			signUp.setEmail(userEmail);
			signUp.setAddress(activateUser.getAddress());
			signUp.setPhoneno(userPhone);
			signUp.setMobileno(userPhone);
			signUp.setCompanyname(activateUser.getFirstname());
			signUp.setCmptype_id(activateUser.getCmptype_id());
			signUp.setSupervisor("PQA");
			signUp.setThrotsettingsid(activateUser.getThrotsettingsid());
			long timeMilli = Calendar.getInstance().getTimeInMillis();
			String cmpName = signUp.getCompanyname() + "-" + timeMilli;
			signUp.setCompanyname(cmpName);
			try {
				ThrottlingSettings throtsettings = throttlingService.getThrotSettings(signUp.getThrotsettingsid()).get(0);

				CompanyType cmpType = companyTypeServ.getCompanyType(signUp.getCmptype_id(), 1).get(0);
				company = companyService.createCompany(signUp,throtsettings,cmpType);
			} catch (Exception e) {
				log.info("Error in company creation ");
				response.put("Status", 0);
				response.put("Msg", errorMsg);
				errorCode = "ER008";
				errorResponse = "Signup Error : " + RegisterUserError.ER008 + "Exception  : " + e.getMessage();
				return response;
			}
			
			CompanyConfig cfg = new CompanyConfig(company);
			boolean companyConfigCreationStatus = companyService.saveCompanyConfg(cfg);
			log.info("Company Config Creation Status : "+companyConfigCreationStatus);
			if( !companyConfigCreationStatus && company.getId()!=0 ) {
				companyService.deleteCompany(company.getId());
			}
			Groups group = new Groups();
			group.setName("Default");
			boolean groupCreationStatus = groupservices.saveORupdateGroups(group, company.getId());
			if( !groupCreationStatus & company!=null ) {
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
			
			boolean signUPResponse = userService.signUp(signUp,company);

			User user = userService.getUserByName(signUp.getEmail());
			user.setVerified(true);
			user.setCountry(country);
			userService.updateUser(user);
			
			//create evalidation
			if(signUPResponse)
				async.updateEvalidation(user.getId(), password);

			log.info("==Signup done into signup Section===========");
			response.put("Status", 1);
			response.put("Msg", "success");

		} catch (ConstraintViolationException ce) {

			loginCreationIssue = true;

			log.info("signup: ConstraintViolationException:\n" + ce.getStackTrace());
			response.put("Status", 0);
			if (Integer.parseInt(activateUser.getCmptype_id()) == 3) {
				response.put("Msg", "Email ID / Name already exists. Please try with alternate one.");// RvPet
			} else {
				response.put("Msg",
						"Username / Company Name /Gateway Name already exists.Please try with alternate one.");
			}
			errorCode = "ER007";
			errorResponse = RegisterUserError.ER007;
			if( company != null && company.getId()!=0 ) {
				groupservices.deleteGroups( company.getId() );
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
		} catch (Exception e) {
			loginCreationIssue = true;
			log.error("signup:::" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", errorMsg);
			errorCode = "ER008";
			errorResponse = "Signup Error : " + RegisterUserError.ER008 + "Exception  : " + e.getMessage();
			if( company != null && company.getId()!=0 ) {
				groupservices.deleteGroups( company.getId() );
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
		} // Sigh up the User ends
		return response;
	}


	private String setOrderChannel(ActivateUser activateUser) {
		String orderChannel = "NA";
		String subscriptionStatus = "";
		if (activateUser.getPurchasedfrom().toLowerCase().contains("rv")) {
			orderChannel = "rv";
			subscriptionStatus = "1";
		} else if (activateUser.getPurchasedfrom().toLowerCase().contains("amazon")) {
			orderChannel = "amazon";
			subscriptionStatus = "0";

		} else if (activateUser.getPurchasedfrom().toLowerCase().contains("walmart")) {
			orderChannel = "walmart";
			subscriptionStatus = "0";

		} else if (activateUser.getPurchasedfrom().toLowerCase().contains("facebook")) {
			orderChannel = "facebook";
			subscriptionStatus = "0";

		} else if (activateUser.getPurchasedfrom().toLowerCase().contains("ebay")) {
			orderChannel = "ebay";
			subscriptionStatus = "0";

		} else if (activateUser.getPurchasedfrom().toLowerCase().contains("campingworld")) {
			orderChannel = "campingworld";
			subscriptionStatus = "0";

		}
		return orderChannel;
	}

	public JResponse checkGateway(List<JGateway> userGateway, String qrcCode, String petName) {
		JResponse response = new JResponse();
		for (JGateway jGateway : userGateway) {
			if (jGateway.getQrcode().toLowerCase().equals(qrcCode.toLowerCase())) {
				response.put("Status", 0);
				response.put("Msg", RegisterUserError.ER006);
				response.put("errorcode", "ER006");
				return response;
			}
			if (jGateway.getName().toLowerCase().equals(petName.toLowerCase())) {
				response.put("Status", 0);
				response.put("Msg", RegisterUserError.petNameUserMessage);
				response.put("errorcode", "ER044");
				return response;
			}
		}
		return response;

	}

	public boolean updateOrdersData(String niomGetOrderCountUrl, Orders order, Inventory inventory) {

		boolean status = false;
		Gson gson = new Gson();

		JSONObject jorderIdCheckResponse = new JSONObject();

		String orderIdCheckResponse;
		try {
			orderIdCheckResponse = _helper.getURL(niomGetOrderCountUrl);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(e.getLocalizedMessage());
			return false;
		}

		jorderIdCheckResponse = _helper.getJSONObject(orderIdCheckResponse);

		if (jorderIdCheckResponse != null) {

			int orderIdCheckStatus;
			try {
				orderIdCheckStatus = jorderIdCheckResponse.getInt("Status");
			} catch (JSONException e) {
				// TODO Auto-generated catch block
				log.error(e.getLocalizedMessage());
				return false;
			}

			if (orderIdCheckStatus > 0 ? true : false) {

				try {
					order = gson.fromJson(jorderIdCheckResponse.getJSONObject("Order").toString(), Orders.class);
				} catch (JsonSyntaxException e) {
					// TODO Auto-generated catch block
					log.error(e.getLocalizedMessage());
					return false;
				} catch (JSONException e) {
					// TODO Auto-generated catch block
					log.error(e.getLocalizedMessage());
					return false;
				}
				order.setProvision_status(1);
				order.setEmail_status("111");
				order.setTracking_summary("NA");
				order.setStatus("completed");
				order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
				order.setWelcome_status(order.getWelcome_status());
				if (inventory.getDevicestate().getName().toLowerCase().contains("amazon")) {
					order.setFulfillmentchannel("amazon");
				} else {
					order.setFulfillmentchannel("rv");
				}

				JResponse updateExternalOrderData = updateExternalOrders(order);
				Object updateExternalOrderResponseStatus = updateExternalOrderData.getResponse().get("Status");
				String _updateAmazonOrderMsg = (String) updateExternalOrderData.getResponse().get("Msg");
				String _updateAmazonOrderError = (String) updateExternalOrderData.getResponse().get("ErrorCode");

				if (updateExternalOrderResponseStatus.toString().equals("1")) {
					status = true;
				} else {
					status = false;
				}
			} else {
				return false;
			}
		}

		return status;
	}

	public boolean updateProvisionStaus(String niomIP, String niomAuthKey, User user, Orders order) {

		boolean status = false;

		// String niomUpdateIrisaccountURL = niomIP
		// + _helper.getExternalConfigValue("updateirisaccount",
		// externalConfigService) + niomAuthKey;

		String niomUpdateIrisaccountURL = niomIP + "v1.0/saveorupdateirisuseraccount/" + niomAuthKey;

		niomUpdateIrisaccountURL = niomUpdateIrisaccountURL + "?order_id=" + order.getOrder_id() + "&username="
				+ URLEncoder.encode(user.getUsername()) + "&userid=" + user.getId() + "&pswd="
				+ URLEncoder.encode(user.getPassword());

		JResponse updateIrisAccountResponse = postData(niomUpdateIrisaccountURL);
		Object updateIrisAccountStatus = updateIrisAccountResponse.getResponse().get("Status");

		if (updateIrisAccountStatus.toString().equals("1")) {
			status = true;
		} else {
			status = false;
		}

		return status;
	}

	public JCreateGateway createOrUpdateGateway(ActivateUser activateUser, Inventory inventory, int model_id,
			long group_id, String sensor_available, String passwordType, User user,String oldMeid,int recallReplaceAction) {

		boolean updateCreaditStatus = false;
		boolean saveGatewayReportStatus = false;
		boolean saveLastGatewayReport = false;
		boolean isGatewayCreated = false;

		JCreateGateway jcreateGateway = new JCreateGateway();

		JGateway jgateway = new JGateway();
		jgateway.setName(activateUser.getGatewayName());
		jgateway.setMeid(inventory.getMeid());
		jgateway.setMdn(inventory.getMdn());
		jgateway.setCarrier("NA");
		jgateway.setModelid(model_id);
		jgateway.setGroupid(group_id);
		jgateway.setEnable(true);
		jgateway.setAlive(false);
		jgateway.setSensorEnable(sensor_available);
		jgateway.setPasswordtype(Long.parseLong(passwordType));
		jgateway.setDescription("");
		jgateway.setQrcode(activateUser.getQrcCode().trim());
		jgateway.setMacid(inventory.getMac_id());
		jgateway.setShowOrderId(activateUser.isShow_orderid());
		jgateway.setOldMeid(oldMeid);
		jgateway.setRecallReplaceAction(recallReplaceAction);
		jgateway = gatewayService.gatewayExitsinDB(jgateway, user.giveCompany().getId());

		Gateway gateway = new Gateway();
		try {
			if( oldMeid.equalsIgnoreCase("NA") ) {
				gateway = gatewayService.saveORupdateGateway(jgateway, user.giveCompany().getId());
			} else {
				gateway = gatewayService.saveORupdateRecallGateway(jgateway, user.giveCompany().getId());
				boolean recallGatewaySaved = gatewayService.saveRecallGateway(gateway);
				boolean updatedInLastGatewayReport = gatewayService.changeDefalutRptInLastGateway(gateway.getId(),irisUtil.getCurrentTimeUTC(),1);
				log.info("updated date saved in last gateway rpt : "+ updatedInLastGatewayReport);
			}
			
			isGatewayCreated = true;
		} catch (Exception e1) {
			// TODO Auto-generated catch bl ock
			return null;
		}

		if (jgateway.isUserGatDis()) {
			if( !oldMeid.equalsIgnoreCase("NA") ) {
	
				Object[] gatewayListO = user.getGateways().toArray() ;
				List<Gateway> gatewayList = new ArrayList<Gateway>();
				for( Object o : gatewayListO) {
					gatewayList.add( (Gateway) o );
				}
				user.getGateways().removeAll(gatewayList);

				int index = 0;
				for( Gateway gate : gatewayList ) {
					if( gate.getMeid().equalsIgnoreCase(oldMeid) ) {
						break;
					}
					index++;
				}
				gatewayList.remove(index);
				gatewayList.add(gateway);
				user.getGateways().addAll(gatewayList);
				
			} else {
				user.getGateways().add(gateway);
			}
				
			
			
			user.setFirstname(activateUser.getFirstname());
			user.setLastname(activateUser.getLastname());

			userService.updateUser(user);
		}

		try {
			gatewayService.updateGatewayCredit(gateway.getId(), user.giveCompany().getId());
			updateCreaditStatus = true;

		} catch (Exception e) {
			log.error("Gateway credits not updated for Gateway: " + gateway.getId() + "  : " + e.getMessage());
			log.error("Gateway credits not updated for Gateway: " + gateway.getId() + "  : " + e.getLocalizedMessage());
		}

		if (jgateway.getId() == 0) {
			try {
				// gatewayService.updateGatewayCredit(gateway.getId(),
				// user.giveCompany().getId());
				// updateCreaditStatus = true;
				
				reportService.saveGatewayReport(gateway, activateUser.getLat(), activateUser.getLon());
				saveGatewayReportStatus = true;
				
				reportService.saveLastGatewayReport(gateway, activateUser.getLat(), activateUser.getLon());
				saveLastGatewayReport = true;
			} catch (Exception e) {
				log.error("4saveORupdateGateway-Default Reports Canot be Generated::::" + e.getLocalizedMessage());
			}
		} else {
			boolean gatewayIdIsThere = reportService.checkRecordInLastGatewayReport( gateway );
			
			if( !gatewayIdIsThere ) {
				reportService.saveLastGatewayReport(gateway, activateUser.getLat(), activateUser.getLon());
				saveLastGatewayReport = true;
			} else {
				reportService.updateLastGatewayReport( gateway );
			}
		}

		jcreateGateway.setUpdateCreaditStatus(updateCreaditStatus);
		jcreateGateway.setSaveGatewayReportStatus(saveGatewayReportStatus);
		jcreateGateway.setSaveLastGatewayReport(saveLastGatewayReport);
		jcreateGateway.setGatewayCreated(isGatewayCreated);
		jcreateGateway.setJgateway(jgateway);
		jcreateGateway.setGateway(gateway);
		return jcreateGateway;
	}

	public JResponse postData(String postURL) {

		JResponse response = new JResponse();

		Gson gson = new Gson();
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpPost post = new HttpPost(postURL);

		post.setHeader("Content-type", "application/json");
		try {
			HttpResponse niomresponse = httpClient.execute(post);
			String niomRes = EntityUtils.toString(niomresponse.getEntity());
			JSONObject _response = new JSONObject();
			Gson _gson = new Gson();
			try {
				JSONObject _res = new JSONObject(niomRes);
				_response = _res.getJSONObject("response");
				int _status = _response.getInt("Status");
				response.put("Status", _status);
				response.put("ErrorCode", "0");
			} catch (JSONException e) {
				response.put("Status", 0);
				response.put("Msg", "Error while getting response from NIOM during pushing order ");
				response.put("ErrorCode", "ER023");
				log.error(e.getLocalizedMessage());
			}
		} catch (ClientProtocolException e) {
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during pushing order ");
			response.put("ErrorCode", "ER022");
			log.error(e.getLocalizedMessage());
		} catch (IOException e) {
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during pushing order ");
			response.put("ErrorCode", "ER024");
			log.error(e.getLocalizedMessage());
		}

		return response;
	}

	public JResponse updateExternalOrders(Orders order) {

		JResponse response = new JResponse();

		String niomPostOrderURL = niomIP + "v1.0/orderV2/" + niomAuthKey;

		Gson gson = new Gson();
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpPost post = new HttpPost(niomPostOrderURL);
		StringEntity postingString = null;
		try {
			postingString = new StringEntity(gson.toJson(order));
		} catch (UnsupportedEncodingException e1) {
			// TODO Auto-generated catch block
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during updating ordermap data. ");

			log.error("Error while getting response from NIOM during updating ordermap data. Exception : -->"
					+ e1.getMessage() + "  Exception --> " + e1.getLocalizedMessage());

			log.error("Response Status : " + 0);
			return response;
		} // gson.tojson() converts your pojo to json
		post.setEntity(postingString);
		post.setHeader("Content-type", "application/json");
		try {
			HttpResponse niomresponse = httpClient.execute(post);
			String niomRes = EntityUtils.toString(niomresponse.getEntity());
			JSONObject _response = new JSONObject();
			Gson _gson = new Gson();
			try {
				JSONObject _res = new JSONObject(niomRes);
				_response = _res.getJSONObject("response");
				int _status = _response.getInt("Status");
				response.put("Status", _status);

				response.put("ErrorCode", "0");
			} catch (JSONException e) {
				response.put("Status", 0);
				response.put("Msg", "Error while updating amazon order");
				response.put("ErrorCode", "ER037");
				log.error(e.getLocalizedMessage());
			}
		} catch (ClientProtocolException e) {
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during pushing order ");
			response.put("ErrorCode", "ER038");
			log.error(e.getLocalizedMessage());
		} catch (IOException e) {
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during pushing order ");
			response.put("ErrorCode", "ER039");
			log.error(e.getLocalizedMessage());
		}

		return response;
	}

	public JResponse activateEseyeSim(String niomActivateEseyeSimURL, String iccids, String tariff, String groupname) {
		JResponse response = new JResponse();

		try {
			niomActivateEseyeSimURL = niomActivateEseyeSimURL + "?iccids=" + URLEncoder.encode(iccids.trim(), "UTF-8")
					+ "&tariff=" + tariff + "&groupname=" + groupname;
			HttpClient httpClient = HttpClientBuilder.create().build();
			HttpPost post = new HttpPost(niomActivateEseyeSimURL);

			post.setHeader("Content-type", "application/json");
			try {
				HttpResponse niomresponse = httpClient.execute(post);
				String niomRes = EntityUtils.toString(niomresponse.getEntity());
				JSONObject _response = new JSONObject();
				Gson _gson = new Gson();
				try {
					JSONObject _res = new JSONObject(niomRes);
					_response = _res.getJSONObject("response");
					int _status = _response.getInt("Status");
					String msg = _response.getString("Msg");
					response.put("Status", _status);
					response.put("Msg", msg);
					response.put("ErrorCode", "0");
				} catch (JSONException e) {
					response.put("Status", 0);
					response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
					response.put("ErrorCode", "ER026");
					log.error(e.getLocalizedMessage());
				}

			} catch (ClientProtocolException e) {
				response.put("Status", 0);
				response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
				response.put("ErrorCode", "ER027");
				log.error("Error " + e.getLocalizedMessage());
			} catch (IOException e) {
				response.put("Status", 0);
				response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
				response.put("ErrorCode", "ER028");
				log.error(e.getLocalizedMessage());
			}

		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
			response.put("ErrorCode", "ER029");
			log.error("Error " + ex.getLocalizedMessage());
		}
		return response;
	}

//	private JResponse furbitCreateIdleAlert(User user, Gateway gateway) {
//		JResponse response = new JResponse();
//		long monitortype = gateway.getModel().getMonitor_type().getId();
//		if (monitortype != 1) {
//			String notify_freq = "43200";
//
//			String minval = "3600";
//
//			String maxval = "36000";
//
//			String alertname = "IdleAlert" + "_" + gateway.getId();
//
//			String notify_type = "000";
//
//			boolean enable = false;
//
//			try {
//				int freq = Integer.parseInt(notify_freq) * 60;
//
//				String rule = "(idleTime > " + Integer.parseInt(maxval) + ")";
//
//				Long[] assetids = new Long[5];
//
//				assetids[0] = gateway.getId();
//
//				JFurbitAlertCfg jFurbitalertcfg = new JFurbitAlertCfg();
//
//				jFurbitalertcfg.setName(alertname);
//				jFurbitalertcfg.setMinval(Float.valueOf(minval));
//				jFurbitalertcfg.setMaxval(Float.valueOf(maxval));
//				jFurbitalertcfg.setNotifyfreq(freq);
//				jFurbitalertcfg.setMobilenos(user.getMobileno());
//				jFurbitalertcfg.setEmailids(user.getEmail());
//				jFurbitalertcfg.setNotificationtype(notify_type);
//				jFurbitalertcfg.setAssetids(assetids);
//				jFurbitalertcfg.setAlerttypeid(16);// Alert Type ID for Idle Alert
//				jFurbitalertcfg.setSeverity(4);
//				jFurbitalertcfg.setEnable(enable);
//				jFurbitalertcfg.setRule(rule);
//				jFurbitalertcfg.setCountry("NA");
//
//				boolean result = alertCfgService.saveOrUpdateFurbitAlertCfg(jFurbitalertcfg, user.giveCompany());
//
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//
//			} catch (ConstraintViolationException e) {
//				log.error("ConstraintViolationException");
//				response.put("Status", 0);
//				response.put("Msg", "invalid asset id");
//				return response;
//
//			} catch (InvalidAlertTypeException e) {
//				log.error("invalid alerttype id");
//				response.put("Status", 0);
//				response.put("Msg", "invalid alerttype id");
//				return response;
//			} catch (InvalidAsseIdException e) {
//				log.error("ConstraintViolationException");
//				response.put("Status", 0);
//				response.put("Msg", "invalid asset id");
//				return response;
//			} catch (Exception e) {
//				log.error("saveORupdateAlertcfg::::" + e.getMessage());
//				System.out.println("Exception" + e.getMessage());
//				response.put("Status", 0);
//				response.put("Msg", "alert name cannot be empty and should be unique");
//				return response;
//			}
//		} else {
//			log.info("Invalid Monitor Type ID");
//			response.put("Status", 0);
//			response.put("Msg", "Invalid Monitor Type ID");
//		}
//		return response;
//	}
//
//	private JResponse furbitCreateBatteryAlert(User user, Gateway gateway) {
//		JResponse response = new JResponse();
//		long monitortype = gateway.getModel().getMonitor_type().getId();
//		if (monitortype != 1) {
//			String notify_freq = "60";
//
//			String minval = "30";
//
//			String maxval = "100";
//
//			String alertname = "BatteryAlert" + "_" + gateway.getId();
//
//			String notify_type = "000";
//
//			boolean enable = false;
//
//			try {
//				int freq = Integer.parseInt(notify_freq) * 60;
//
//				String rule = "(battStatus <= 30 and battStatus >= 0) or (battStatus == -2)";
//
//				Long[] assetids = new Long[5];
//
//				assetids[0] = gateway.getId();
//
//				JFurbitAlertCfg jFurbitalertcfg = new JFurbitAlertCfg();
//
//				jFurbitalertcfg.setName(alertname);
//				jFurbitalertcfg.setMinval(Float.valueOf(minval));
//				jFurbitalertcfg.setMaxval(Float.valueOf(maxval));
//				jFurbitalertcfg.setNotifyfreq(freq);
//				jFurbitalertcfg.setMobilenos(user.getMobileno());
//				jFurbitalertcfg.setEmailids(user.getEmail());
//				jFurbitalertcfg.setNotificationtype(notify_type);
//				jFurbitalertcfg.setAssetids(assetids);
//				jFurbitalertcfg.setAlerttypeid(15);// Alert Type ID for Battery Alert
//				jFurbitalertcfg.setSeverity(4);
//				jFurbitalertcfg.setEnable(enable);
//				jFurbitalertcfg.setRule(rule);
//				jFurbitalertcfg.setCountry("NA");
//
//				boolean result = alertCfgService.saveOrUpdateFurbitAlertCfg(jFurbitalertcfg, user.giveCompany());
//
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//
//			} catch (ConstraintViolationException e) {
//				log.error("ConstraintViolationException");
//				response.put("Status", 0);
//				response.put("Msg", "invalid asset id");
//				return response;
//
//			} catch (InvalidAlertTypeException e) {
//				log.error("invalid alerttype id");
//				response.put("Status", 0);
//				response.put("Msg", "invalid alerttype id");
//				return response;
//			} catch (InvalidAsseIdException e) {
//				log.error("ConstraintViolationException");
//				response.put("Status", 0);
//				response.put("Msg", "invalid asset id");
//				return response;
//			} catch (Exception e) {
//				log.error("saveORupdateAlertcfg::::" + e.getMessage());
//				System.out.println("Exception" + e.getMessage());
//				response.put("Status", 0);
//				response.put("Msg", "alert name cannot be empty and should be unique");
//				return response;
//			}
//		} else {
//			log.info("Invalid Monitor Type ID");
//			response.put("Status", 0);
//			response.put("Msg", "Invalid Monitor Type ID");
//		}
//		return response;
//	}
//
//	private JResponse furbitCreateNetworkAlert(User user, Gateway gateway) {
//		JResponse response = new JResponse();
//		long monitortype = gateway.getModel().getMonitor_type().getId();
//		if (monitortype != 1) {
//			String notify_freq = "60";
//
//			String minval = "-70";
//
//			String maxval = "170";
//
//			String alertname = "NetworkAlert" + "_" + gateway.getId();
//
//			String notify_type = "1100";
//
//			boolean enable = false;
//
//			try {
//				int freq = Integer.parseInt(notify_freq) * 60;
//
//				Long[] assetids = new Long[5];
//
//				assetids[0] = gateway.getId();
//
//				JFurbitAlertCfg jFurbitalertcfg = new JFurbitAlertCfg();
//
//				jFurbitalertcfg.setName(alertname);
//				jFurbitalertcfg.setMinval(Float.valueOf(minval));
//				jFurbitalertcfg.setMaxval(Float.valueOf(maxval));
//				jFurbitalertcfg.setNotifyfreq(freq);
//				jFurbitalertcfg.setMobilenos(user.getMobileno());
//				jFurbitalertcfg.setEmailids(user.getEmail());
//				jFurbitalertcfg.setNotificationtype(notify_type);
//				jFurbitalertcfg.setAssetids(assetids);
//				jFurbitalertcfg.setAlerttypeid(11);// Alert Type ID for Network Alert
//				jFurbitalertcfg.setSeverity(4);
//				jFurbitalertcfg.setEnable(enable);
//
//				jFurbitalertcfg.setCountry("NA");
//
//				boolean result = alertCfgService.saveOrUpdateFurbitAlertCfg(jFurbitalertcfg, user.giveCompany());
//
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//
//			} catch (ConstraintViolationException e) {
//				log.error("ConstraintViolationException");
//				response.put("Status", 0);
//				response.put("Msg", "invalid asset id");
//				return response;
//
//			} catch (InvalidAlertTypeException e) {
//				log.error("invalid alerttype id");
//				response.put("Status", 0);
//				response.put("Msg", "invalid alerttype id");
//				return response;
//			} catch (InvalidAsseIdException e) {
//				log.error("ConstraintViolationException");
//				response.put("Status", 0);
//				response.put("Msg", "invalid asset id");
//				return response;
//			} catch (Exception e) {
//				log.error("saveORupdateAlertcfg::::" + e.getMessage());
//				System.out.println("Exception" + e.getMessage());
//				response.put("Status", 0);
//				response.put("Msg", "alert name cannot be empty and should be unique");
//				return response;
//			}
//		} else {
//			log.info("Invalid Monitor Type ID");
//			response.put("Status", 0);
//			response.put("Msg", "Invalid Monitor Type ID");
//		}
//		return response;
//	}

	public JResponse activateUserValidation(BindingResult result) {
		JResponse response = new JResponse();

		response.put("Status", 0);
		if (result.getFieldError("firstname") != null)
			response.put("Msg", result.getFieldError("name").getDefaultMessage());
		else if (result.getFieldError("lastname") != null)
			response.put("Msg", result.getFieldError("cmptype_id").getDefaultMessage());
		else if (result.getFieldError("cmptype_id") != null)
			response.put("Msg", result.getFieldError("cmptype_id").getDefaultMessage());
		else if (result.getFieldError("isAlreadyUser") != null)
			response.put("Msg", "isAlreadyUser must be true or false");
		else if (result.getFieldError("qrcCode") != null)
			response.put("Msg", "QR Code should not be empty");
		else if (result.getFieldError("throtsettingsid") != null)
			response.put("Msg", result.getFieldError("throtsettingsid").getDefaultMessage());
		else if (result.getFieldError("phoneno") != null)
			response.put("Msg", "Phone Number should not be empty");
		else if (result.getFieldError("email") != null)
			response.put("Msg", "Email should not be empty");
		else if (result.getFieldError("gatewayName") != null) {
			response.put("Msg", "Gateway Name should not be empty");
		} else if (result.getFieldError("purchasedfrom") != null) {
			response.put("Msg", "Purchasedfrom should not be empty");
		}
		return response;
	}

	public long modelID(String deviceModel) {

		int count = 0;

		long modelId = 7;
		int devTempSensorModel = Integer.parseInt(deviceModel.substring(deviceModel.length() - 1));

		String[] device = Arrays.copyOf(deviceModel.split("-"), 2);

		String _deviceModel = device[0] + "-" + device[1];

		List<AssetModel> assetmodelList = fetchDropdownService.getAssetModel();
		for (AssetModel assetModel : assetmodelList) {
			int assetTempSensorModel = Integer
					.parseInt(assetModel.getSensoravailable().substring(assetModel.getSensoravailable().length() - 1));
			if (devTempSensorModel == assetTempSensorModel) {
				if (assetModel.getModel().toLowerCase().trim().contains(_deviceModel.toLowerCase().trim())) {
					modelId = assetModel.getId();
					count++;
				}
			}
		}
		return modelId;
	}


	public boolean nameCheck(Orders order, ActivateUser activateUser) {
		if (!order.getBilling_first_name().equalsIgnoreCase(activateUser.getFirstname().toLowerCase())
				|| !order.getBilling_last_name().equalsIgnoreCase(activateUser.getLastname().toLowerCase())) {
			return true;
		} else {
			return false;
		}

	}

	// Used in web
	// New Controller DIV//
	@RequestMapping(value = "v3.0/usersignup", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse userSignup(@RequestBody @Valid SignUp signUp, BindingResult result,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		Company company = null;
		try {
			String email = signUp.getEmail().toLowerCase().trim();
			email = email.replaceAll("\\s", "");

			try {
				User user = userService.getUserByUNameOrEmail(email);

				if (user != null) {
					response.put("Status", 0);
					response.put("Msg", "Email id already found. Please use different email id");
					return response;
				}
			} catch (Exception ex) {
				log.error("Sign up : Email Id not found . Create user account for user ");
			}

			// try {
			// User user = userService.getUserByName(email);
			// if (user != null) {
			// response.put("Status", 0);
			// response.put("Msg",
			// "Username already found with the provided email id. Please use different
			// email id");
			// return response;
			// }
			//
			// } catch (InvalidUsernameException ex) {
			// log.info("Sign up : Email Id not found . Create user account for user ");
			// }

			// Assign Default things

			int rand = _helper.getRandomNumber(100, 9999999);
			
			JValidateString validString = userServiceV4.checkAlphabetOnly(signUp.getFirstname(),signUp.getLastname());
			if( !validString.isValid() ) {
				response.put("Status", 0);
				response.put("Msg", validString.getMsg());
				return response;
			}
			
			String password = signUp.getPassword();
			JValidateString validatePassword = userServiceV4.validatePassword(password);
			if( !validatePassword.isValid() ) {
				response.put("Status", 0);
				response.put("Msg", validatePassword.getMsg());
				return response;
			}
			
			signUp.setUsername(email);
			signUp.setCompanyname(signUp.getFirstname() + "_" + rand);
			signUp.setSupervisor("Nimble");
			signUp.setThrotsettingsid("5");
			signUp.setCmptype_id("3");
			signUp.setAddress("NA");
			signUp.setPhoneno(signUp.getMobileno());
			signUp.setPassword( _helper.bCryptEncoder(password) );

			String mobileNo = signUp.getMobileno();

			if (!mobileNo.contains("-") && mobileNo.length() > 0) {

				mobileNo = new CountryCode().getCountryCode(signUp.getCountry().toUpperCase()) + signUp.getMobileno();
				signUp.setPhoneno(mobileNo);
				signUp.setMobileno(mobileNo);
			}
			long timeMilli = Calendar.getInstance().getTimeInMillis();
			String cmpName = signUp.getCompanyname() + "-" + timeMilli;
			signUp.setCompanyname(cmpName);
			try {
				ThrottlingSettings throtsettings = throttlingService.getThrotSettings(signUp.getThrotsettingsid()).get(0);

				CompanyType cmpType = companyTypeServ.getCompanyType(signUp.getCmptype_id(), 1).get(0);
				company = companyService.createCompany(signUp,throtsettings,cmpType);
			} catch (Exception e) {
				log.error("Error in creating the company . Error : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Not able to process the request, Please try later");
				return response;
			}
			
			CompanyConfig cfg = new CompanyConfig(company);
			boolean companyConfigCreationStatus = companyService.saveCompanyConfg(cfg);
			log.info("Company Config Creation Status : "+companyConfigCreationStatus);
			if( !companyConfigCreationStatus && company.getId()!=0 ) {
				companyService.deleteCompany(company.getId());
			}
			Groups group = new Groups();
			group.setName("Default");
			boolean groupCreationStatus = groupservices.saveORupdateGroups(group, company.getId());
			if( !groupCreationStatus & company!=null ) {
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
			
			boolean res = userService.signUp(signUp,company);

			if (res) {
				User user = userService.getUserByName(email);
				UserVerification userVerificationToken = userService.createEmailVerificationToken(user);

				if (userVerificationToken.getStatus().equalsIgnoreCase("pending")) {
					mailService.sendVerificationMail(user, userVerificationToken.getToken());
				}

				// Updating chargebee customerid / creating customer details in chargebee
				//updateChargebeeUser(user);
				if(user.getChargebeeid().trim().equalsIgnoreCase("NA") || user.getChargebeeid()==null)
					async.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
				
				String cb_planid= "chum";

//				boolean stat = rvcentricServ.saveUserBadgeTxn(user.getId(), cb_planid, 0, false, "NA");
//				log.info("in user signup:" + user.getId() + " Badge created:" + stat);
				
				//create evalidation
				async.updateEvalidation(user.getId(), password);

				response.put("Status", 1);
				response.put("Msg", "success");
				response.put("User", user);
			}

		} catch (ConstraintViolationException ce) {
			log.error("signup: ConstraintViolationException:\n" + ce.getStackTrace());
			response.put("Status", 0);
			response.put("Msg", "Username/Company name already exists");
			if( company != null && company.getId()!=0 ) {
				groupservices.deleteGroups( company.getId() );
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
		} catch (DataIntegrityViolationException ce) {
			log.error("signup: DataIntegrityViolationException:\n" + ce.getStackTrace());
			response.put("Status", 0);
			response.put("Msg", "Username/Company name already exists");
			if( company != null && company.getId()!=0 ) {
				groupservices.deleteGroups( company.getId() );
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
		} catch (Exception e) {
			log.error("signup:::" + e.getStackTrace());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Signup");
			if( company != null && company.getId()!=0 ) {
				groupservices.deleteGroups( company.getId() );
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
		}
		return response;
	}

	// updated Service ==========User Signup========
	@RequestMapping(value = "v3.0/resendverificationlink", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse resendVerificationLink(@RequestParam("userid") String userid) {
		JResponse response = new JResponse();
		try {

			try {
				List<User> users = userService.getUserInRole(userid, 2, 2, "");
				User user = null;
				if (users != null && users.size() > 0) {
					user = users.get(0);
				} else {
					response.put("Status", 0);
					response.put("Msg", "No user found for given ID. Please contact support.");
					return response;
				}
				if (user.isVerified()) {
					response.put("Status", 0);
					response.put("Msg", "User is already verified.");
					return response;
				}

				if (user != null) {
					UserVerification userVerificationToken = userService.createEmailVerificationToken(user);

					mailService.sendVerificationMail(user, userVerificationToken.getToken());
				}
				String msg = "Email verification mail has been sent to " + user.getEmail() + " successfully !";
				response.put("Status", 1);
				response.put("Msg", msg);
			} catch (InvalidUsernameException ex) {
				log.error(ex.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Unable to resend the verification link . Please contact support.");
				return response;
			}
		} catch (ConstraintViolationException ce) {
			log.error("signup: ConstraintViolationException:\n" + ce.getStackTrace());
			response.put("Status", 0);
			response.put("Msg", "Username/Company name already exists");
		} catch (DataIntegrityViolationException ce) {
			log.error("signup: DataIntegrityViolationException:\n" + ce.getStackTrace());
			response.put("Status", 0);
			response.put("Msg", "Username/Company name already exists");
		} catch (Exception e) {
			log.error("signup:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to process the request at this time. Please contact our support team.");

		}
		return response;
	}

	@RequestMapping(value = "v3.0/verify-email", headers = "Accept=application/json", method = RequestMethod.GET, produces = MediaType.TEXT_HTML_VALUE)
	@ResponseBody
	public String verifyEmail(@RequestParam("code") String code) {
		return userService.verifyEmail(code).getBody();
	}

	// ========Login Version3.1========
	/*
	 * This function is specific for RV mobile app . IN order to store the user
	 * device status we have created the new rest controller and make the other
	 * application functionality untouchable.
	 */
	@RequestMapping(value = "v3.0/loginV3", headers = "Accept=application/json", method = RequestMethod.GET)
	@ResponseBody
	public JResponse rvUserLogin(@RequestParam("username") String userName, @RequestParam("password") String passwo,
			@RequestParam("webappid") String webappid, @RequestParam("mobileappid") String mobileappid) {
		JResponse response = new JResponse();
		log.info(userName + "userName" + passwo + "passwo");
		String username = userName.trim();
		String password = passwo.trim();
		log.info(username + "username" + password + "password");
		/* check for username */
		if (username.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Please enter username");
			return response;
		} else if (password.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Please enter Password");
			return response;
		}
		try {
			User user = userService.getUserByName(username);
			if (!(user.getUsername().equals(username))) {
				response.put("Status", 0);
				response.put("Msg", "Invalid username");
				return response;
			}
			String pwd = user.getPassword().toString();
			if (password.equals(pwd)) {
				if (!user.isEnable()) {
					response.put("Status", 0);
					response.put("Msg", "Invalid username or password!!");
					return response;
				}
				if (!webappid.isEmpty()) {
					if (!webappid.equalsIgnoreCase(String.valueOf(user.getWebappid()))) {
						response.put("Status", 0);
						response.put("Msg", "Invalid username or password!");
						return response;
					}
				}
				if (!mobileappid.isEmpty()) {
					if (!mobileappid.equalsIgnoreCase(String.valueOf(user.getMobileappid()))) {
						response.put("Status", 0);
						response.put("Msg", "Invalid username or password!");
						return response;
					}
				}
				response.put("Status", 1);
				response.put("Msg", "success");
				response.put("User", user);
			} else {
				if (password.isEmpty()) {
					response.put("Status", 0);
					response.put("Msg", "Please enter password");
					return response;
				}
				response.put("Status", 0);
				response.put("Msg", "Invalid password");
			}
		} catch (InvalidUsernameException e) {
			response.put("Status", 0);
			response.put("Msg", "Invalid username");
		} catch (Exception e) {
			log.error("login::::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in login");
		}
		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/userupdate/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse userUpdate(@PathVariable String autho, @RequestBody User user1) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			try {
				User usr = userService.getUserByUNameOrEmail(user1.getEmail());

				if (usr != null && !(user.getId() == usr.getId())) {

					response.put("Msg", "Email already exist. Please enter alternate Email");
					return response;
				}
			} catch (Exception ex) {

				log.error("user update failed " + ex.getMessage());
			}

			// try {
			// User usr = userService.getUserByName(user1.getEmail());
			// if (usr != null && !(user.getId() == usr.getId())) {
			// response.put("Msg", "Email already exist. Please enter alternate Email");
			// return response;
			// }
			//
			// } catch (Exception ex) {
			//
			// log.info("user update failed" + ex.getMessage());
			// }

			user.setUpdatedOn(_helper.getCurrentTimeinUTC());
			user.setFirstname(user1.getFirstname());
			user.setLastname(user1.getLastname());
			user.setMobileno(user1.getMobileno());
			user.setZipcode(user1.getZipcode());
			user.setCity(user1.getCity());
			user.setState(user1.getState());
			user.setCountry(user1.getCountry());
			String mobileNo = user.getMobileno();
			
			String password = user1.getPassword();
			user.setPassword_ver("V2");
			user.setPassword( _helper.bCryptEncoder(password) );
			

			if (!mobileNo.contains("-") && mobileNo.length() > 0) {

				mobileNo = new CountryCode().getCountryCode(user.getCountry().toUpperCase()) + user.getMobileno();
				user.setMobileno(mobileNo);
			}

			if (user.getEmail().equalsIgnoreCase("NA") || user.getEmail() == null
					|| user.getEmail().contains("appleid.com"))
				user.setEmail(user1.getEmail());

			userService.updateUser(user);
			
			//update evalidation
			async.updateEvalidation(user.getId(), password);

			// update cb customer details
			if(!user.getChargebeeid().trim().equalsIgnoreCase("NA"))
				async.updateCBCustomerDetails(user);
			else if(user.getChargebeeid().trim().equalsIgnoreCase("NA") || user.getChargebeeid()==null)
				userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
					user.getMobileno(), user.getUsername(), 0, "NA");
			
			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (Exception e) {
			log.error("userupdate : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in User Updation");
		}
		return response;
	}

	@RequestMapping(value = "v3.0/pwdupdate/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse passwordUpdate(@PathVariable String autho,
			@RequestParam("password") String password) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			
			JValidateString validatePassword = userServiceV4.validatePassword(password);
			if( !validatePassword.isValid() ) {
				response.put("Status", 0);
				response.put("Msg", validatePassword.getMsg());
				return response;
			}
			
			user.setPassword( _helper.bCryptEncoder(password) );
			user.setPassword_ver("V2");
			userService.updateUser(user);
			
			//update evalidation
			async.updateEvalidation(user.getId(), password);
			
			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (Exception e) {
			log.error("passwordUpdate : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in User Password Updation");
		}
		return response;
	}

//	public boolean updateChargebeeUser(User user) {
//		String chargebeeCusId = "";
//		try {
//			if (user != null) {
//				Environment.configure(chargebeeSiteName, chargebeeSiteKey);
//
//				ListResult resultSet = Customer.list().email().is(user.getEmail()).request();
//				if (resultSet.size() > 0) {
//					for (ListResult.Entry entry : resultSet) {
//						Customer customer = entry.customer();
//						chargebeeCusId = customer.id();
//
//						// Update chargebeeid for user
//						user.setChargebeeid(chargebeeCusId);
//						user.setUpdatedOn(_helper.getCurrentTimeinUTC());
//						userService.updateUser(user);
//
//						break;
//					}
//				} else {
//
//					String fName = user.getFirstname().equalsIgnoreCase("NA") ? "" : user.getFirstname();
//					String lName = user.getLastname().equalsIgnoreCase("NA") ? "" : user.getLastname();
//
//					Result customerCreateResult = Customer.create().firstName(fName).lastName(lName)
//							.email(user.getEmail()).phone(user.getMobileno())
//							// .billingAddressFirstName(user.getFirstname()).billingAddressLastName(user.getLastname())
//							// .billingAddressEmail(user.getEmail()).billingAddressPhone(user.getMobileno())
//							.request();
//
//					Customer customer = customerCreateResult.customer();
//					chargebeeCusId = customer.id();
//
//					// Update chargebeeid for user
//					user.setChargebeeid(chargebeeCusId);
//					user.setUpdatedOn(_helper.getCurrentTimeinUTC());
//					userService.updateUser(user);
//				}
//			}
//			return true;
//		} catch (Exception e) {
//			log.error("updateChargebeeUser : " + e.getMessage());
//			return false;
//		}
//
//	}

//	public void updateCBCustomerDetails(User user) {
//		try {
//			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
//
//			Result result1 = Customer.update(user.getChargebeeid()).firstName(user.getFirstname())
//					.lastName(user.getLastname()).request();
//
//		} catch (Exception e) {
//			log.error(" update CB customer detail:" + e.getMessage());
//		}
//
//	}

	@RequestMapping(value = "v3.0/getuserbyid", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getUserById(@RequestParam("userid") String userId) {
		JResponse response = new JResponse();
		User user = userService.getUserById(userId);
		String mobile = "";
		if (user != null) {
			Map<Integer, String> getnumber = _helper.getPhoneAndCountryCode(user.getMobileno());
			if (user.getCountry().equalsIgnoreCase("NA")) {
				if (getnumber.get(0).equalsIgnoreCase("+91") || getnumber.get(0).equalsIgnoreCase("91")) {
					user.setCountry("IN");
				} else if (getnumber.get(0).equalsIgnoreCase("+44") || getnumber.get(0).equalsIgnoreCase("44")) {
					user.setCountry("GB");
				} else {
					user.setCountry("US");
				}
			}
			mobile = getnumber.get(1);
			if (mobile == null) {
				user.setMobileno("NA");
			} else {
				user.setMobileno(getnumber.get(1));
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("User", user);
			return response;
		} else {
			response.put("Status", 0);
			response.put("Msg", "No user found for specific user id.");
			return response;
		}
	}

	// Social sign up main controller
	@PostMapping(value = "v3.0/socialsignup", headers = "Accept=application/json")
	public @ResponseBody JResponse socialSignUp(@RequestBody ExternalLogin externalLogin,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		log.info("Entered socialSignUp : " + externalLogin.getFirstname());
		JResponse response = new JResponse();
		try {

			User user = null;
			String userEmail = externalLogin.getEmail().toLowerCase().trim().replaceAll("\\s", "");
			externalLogin.setEmail(userEmail);
			try {
				if (!userEmail.equalsIgnoreCase("NA") && userEmail != null && !(userEmail.isEmpty())
						&& !userEmail.contains("facebook.com") && !userEmail.contains("privaterelay.appleid.com")) {
					try {
						user = userService.getUserByUNameOrEmail(userEmail);
					} catch (Exception e) {
						log.error("Exception occured while getting user by username : " + e.getLocalizedMessage());
					}
					if (user != null) {
						response.put("Status", 1);
						response.put("Msg", "Success");
						response.put("alreadyuser", true);
						if (user.getEmail().equalsIgnoreCase("NA")) {
							response.put("emaileditable", true);
						} else {
							response.put("emaileditable", false);
						}
						if (user.getMobileno() == null || user.getMobileno().isEmpty()) {
							user.setMobileno(externalLogin.getPhone());
						}
						if (user.getVerificationToken() == null) {
							user.setVerificationToken(new UserVerification());
						}
						
						String cb_planid= "chum";
						
//						boolean stat = rvcentricServ.saveUserBadgeTxn(user.getId(), cb_planid, 0, false, "NA");
//						log.info("in socialsignup:" + user.getId() + " Badge created:" + stat);

						response.put("User", user);
						userService.updateLastLoginTypeAndTime(user.getId(),
								Integer.valueOf(externalLogin.getSignuptype()), _helper.getCurrentTimeinUTC());
						return response;
					}
				}
			} catch (Exception e) {
				log.error("Exception occured while getting user by email / username : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid email!");
			}
			try {
				JResponse jres = userService.loginViaGoogleOrFacebook(externalLogin);
				if (jres != null) {
					return jres;
				}
			} catch (Exception e) {
				log.error("Exception occured! " + e.getMessage());
				response.put("Status", 0);
				response.put("Msg", "Unexcepted error!");
			}
		} catch (Exception e) {
			log.error("Exception occured : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Unexcepted error!");
		}
		return response;
	}

	@RequestMapping(value = "v3.0/updateusercompletesetup/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateUserCompleteSetUp(@PathVariable String autho, @RequestBody User user1) {
		log.info("Entered updateUserCompleteSetUp, autho :  " + autho);

		JResponse response = new JResponse();

		try {
			User user = null;
			try {
				user = userService.verifyAuthKey(autho);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authkey!");
				return response;
			}
			try {
				// Need to remove in next release
				if ((user1.getEmail().trim().isEmpty()) || (user1.getEmail() == null)) {
					user1.setEmail(user.getEmail());
				}

				User usr = userService.getUserByUNameOrEmail(user1.getEmail());
				if (usr != null && !(user.getId() == usr.getId())) {
					response.put("Status", 0);
					response.put("Msg", "Email already exist. Please enter alternate Email");
					return response;
				}
			} catch (Exception ex) {
				log.error("user update failed" + ex.getMessage());
			}

			/*if (user1.getZipcode().length() > 6 || user1.getZipcode().length() < 5) {
				log.info("ZIP Code Length : " + user1.getZipcode().length() + ", Zipcode : " + user1.getZipcode());
				SignupType signUp_type = user.getSignupType();
				if (!(signUp_type.getName().equalsIgnoreCase("Apple") && user1.getZipcode().equalsIgnoreCase("NA"))) {
					log.info("Invalid Zipcode : " + user1.getZipcode());
					response.put("Status", 0);
					response.put("Msg", "Invalid zipcode!");
					return response;
				}
			}*/
			user.setUpdatedOn(_helper.getCurrentTimeinUTC());
			// user.setFirstname(user1.getFirstname());
			// user.setLastname(user1.getLastname());
			String zipCode = user1.getZipcode();
			String country = user1.getCountry();
			user.setCountry(country);
			String city = "NA";
			String state = "NA";
			ZipCodeDetails zipDetails = null;
			try {
				List<ZipCodeDetails> zipCodeDetails = iZipCodeDetailsService.getZipCodeDetails(zipCode, country);
				if (zipCodeDetails.size() > 0) {
					log.info("Zip Cpode found in database : " + zipCode + country);
					zipDetails = zipCodeDetails.get(0);
					city = zipDetails.getCity();
					state = zipDetails.getState();
				} else {
					log.info("Search Zip Cpode in Google : " + zipCode + country);
					ZipCodeDetails newZipCodeDetails = new ZipCodeDetails();
					GeoApiContext context = new GeoApiContext.Builder().apiKey(googleAPIkey).build();
					GeocodingResult[] results = GeocodingApi.newRequest(context)
							// .components(new ComponentFilter("country", countryCode))
							.components(new ComponentFilter("postal_code", zipCode),
									new ComponentFilter("country", country))
							.await();
					if (results.length > 0) {
						for (GeocodingResult gr : results) {
							for (AddressComponent ac : gr.addressComponents) {
								for (AddressComponentType acType : ac.types) {
									if (acType == AddressComponentType.ADMINISTRATIVE_AREA_LEVEL_1) {
										newZipCodeDetails.setState(ac.shortName);
										state = ac.shortName;
									} else if (acType == AddressComponentType.LOCALITY) {
										newZipCodeDetails.setCity(ac.shortName);
										city = ac.shortName;
									}
								}
							}
							if (newZipCodeDetails.getCity().equalsIgnoreCase("NA")) {
								for (AddressComponent ac : gr.addressComponents) {
									for (AddressComponentType acType : ac.types) {
										if (acType == AddressComponentType.ADMINISTRATIVE_AREA_LEVEL_2) {
											newZipCodeDetails.setCity(ac.shortName);
											city = ac.shortName;
										}
									}
								}
							}
							break;
						}
					}
					newZipCodeDetails.setZipcode(zipCode.trim());
					newZipCodeDetails.setCountry(country.toString().toUpperCase());
					newZipCodeDetails.setCreatedOn(_helper.getCurrentTimeinUTC());
					boolean saveZipDetails = iZipCodeDetailsService.saveZipCode(newZipCodeDetails);
					log.info("Save Zip Code from Google into databases : " + zipCode + country + "Status : ->"
							+ saveZipDetails);
				}
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Zipcode error!");
				log.error("Error - . Error in Zip Code Details.");
			}
			user.setCity(city);
			user.setState(state);
			if (!city.equalsIgnoreCase("NA") || !state.equalsIgnoreCase("NA")) {
				user.setZipcode(zipCode);
			} else {
				user.setZipcode("NA");
			}

			String mobileNo = "1-**********";

			if (!user1.getMobileno().equalsIgnoreCase("NA") && !user1.getMobileno().isEmpty()
					&& user1.getMobileno() != null) {
				mobileNo = user1.getMobileno();
			}

			String password = mobileNo;
			password = password.replaceAll("\\W", "");
			if (password.length() > 10) {
				password = password.substring(password.length() - 10);
			}
			user.setPassword( _helper.bCryptEncoder(password) );
			if (!mobileNo.contains("-") && mobileNo.length() > 0) {
				mobileNo = new CountryCode().getCountryCode(user1.getCountry().toUpperCase()) + user1.getMobileno();
				user.setMobileno(mobileNo);
			}

			boolean sendVerifyEmail = false;
			if ((user.getEmail().equalsIgnoreCase("NA") || user.getEmail() == null)
					|| (!user.getEmail().equalsIgnoreCase(user1.getEmail())
							&& user.getSignupType().getName().equalsIgnoreCase("Apple"))) {
				user.setEmail(user1.getEmail());
				user.setUsername(user1.getEmail());
				user.setAuthKey(_helper.encryptAndSetUser(user1.getEmail().trim()));
				user.setVerified(false);
				sendVerifyEmail = true;
			}

			user.setCompletesetup(true);
			boolean isUserUpdated = userService.updateUser(user);
			if (isUserUpdated) {
				if (sendVerifyEmail) {
					UserVerification userVerificationToken = userService.createEmailVerificationToken(user);
					if (userVerificationToken.getStatus().equalsIgnoreCase("pending")) {
						boolean verificationEmailSend = mailService.sendVerificationMail(user,
								userVerificationToken.getToken());
						log.info("verificationEmailSend : " + verificationEmailSend);
					}
				}
				User updatedUser = userService.getUserById(user.getId());
				response.put("User", updatedUser);
				
				//update evalidation
				async.updateEvalidation(user.getId(), password);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Could not update user!");
				return response;
			}
			// update cb customer details
			if(!user.getChargebeeid().trim().equalsIgnoreCase("NA"))
				async.updateCBCustomerDetails(user);
			
//			UserRvDetails rvObj = userServiceV4.getUserRvDetails(user.getId());
//
//			boolean rvStatus = (rvObj != null) ? true : false;
//
//			boolean stat1 = rvcentricServ.saveUserBadgeTxn(user.getId(), "NA", 0, rvStatus, user.getChargebeeid());
//			log.info("in user Completesetup:" + user.getId() + " Badge created:" + stat1);

			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (Exception e) {
			log.error("userupdate : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in User Updation");
		}
		return response;
	}

	@RequestMapping(value = "v3.0/forgetpassword", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse forgetPassword(@RequestParam("name") String username,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		log.info("Entered into forgetPassword : username : " + username);
		JResponse response = new JResponse();
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("username", username);
			} catch (InvalidAuthoException e) {
				log.error("Invalid Username :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}

			if (!user.isEnable()) {
				response.put("Status", 0);
				response.put("Msg", "Account Disabled. Please contact support!");
				return response;
			}

			boolean status = userService.resetPasswordRequest(user);

			String email = user.getEmail();
			StringBuilder hiddenEmail = new StringBuilder();

			for (int i = 0; i < email.length(); i++)
				hiddenEmail.append((i >= 2 && i < email.indexOf('@')) ? '*' : email.charAt(i));

			if (status) {
				response.put("Status", 1);
				response.put("Msg", "Please check your " + hiddenEmail.toString() + " email to reset your password!");
				log.info("Username and password send to email : " + user.getEmail() + " Hidden : " + hiddenEmail);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Couldn't request to reset password");
				log.info("Couldn't request to reset password");
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while sending forgot password email");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : forgetPassword : " + e.getLocalizedMessage());
		}
		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/userlistbyfilter/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse userlistbyfilter(@PathVariable String autho,
			@RequestParam(value = "ukey", required = false) String uKey,
			@RequestParam(value = "uvalue", required = false) String uValue,
			@RequestParam(value = "ftype", defaultValue = "equals", required = false) String fType,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "asc", required = false) String otype,
			@RequestParam(value = "okey", required = false) String oKey) {

		log.info("Entered into userlistbyfilter : " + uKey + " - " + uValue);
		JResponse response = new JResponse();
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.error("Invalid Authkey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}

			response = userServiceV4.getuserlistbyfilter(uKey, uValue, fType, otype, offset, limit, oKey, response);

			return response;

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting userlistbyfilter");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : userlistbyfilter : " + e.getLocalizedMessage());
		}
		return response;
	}



}
