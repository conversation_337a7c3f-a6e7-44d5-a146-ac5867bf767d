package com.nimble.irisservices.controller;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.Advertisement;
import com.nimble.irisservices.entity.AppImage;
import com.nimble.irisservices.service.IAdvertisementService;

@Controller
public class AdvertisementController {


	private static final Logger log = LogManager.getLogger(AdvertisementController.class);

	@Autowired
	@Lazy
	IAdvertisementService advService;

	@RequestMapping(value = "v3.0/getAdvertisements", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getadvertisementinfo() {

		JResponse response=new JResponse();
		try {

			List<Advertisement> advList = advService.getAdvertismentUrl("v1");
			response.put("Status", 1);
			response.put("Msg", "success");
			response.put("advList",advList);
			log.error("getadvertisementinfo Created");

		}catch(Exception e)
		{
			response.put("Status", 0);
			response.put("Msg", "failure");
			log.error("getadvertisementinfo : "+e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value="v3.0/createAdvertisement",method = RequestMethod.POST, headers="Accept=application/json")
	@ResponseBody
	public JResponse createAdvertisement(@RequestBody Advertisement advertisement)
	{
		JResponse response = new JResponse();
		boolean status = false;
		try {
			status = advService.createAdvertisement(advertisement);

			if(status == true)
				response.put("Msg","Success");
			else
				response.put("Msg","Failed");
			
			response.put("Status", status);
		}
		catch(ConstraintViolationException ex)
		{
			response.put("Status", status);
			response.put("Msg","Advertisement already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("create Advertisement : "+ex.getLocalizedMessage());
		}
		catch (Exception e) {
			response.put("Status", status);
			response.put("Msg","Advertisement creation failed");
			log.error("create Advertisement : " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/getalladvertisements", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllAdvertisements() {
		JResponse response=new JResponse();
		try {
			//full data for web api
			List<Advertisement> advList = advService.getAllAdvertismentUrl();
			response.put("Status", 1);
			response.put("Msg", "success");
			response.put("advList",advList);
			log.error("getadvertisementinfo Created");
		}catch(Exception e)
		{
			response.put("Status", 0);
			response.put("Msg", "failure");
			log.error("getadvertisementinfo : "+e.getLocalizedMessage());
		}
		return response;
	}
	
	@RequestMapping(value = "v3.0/getappimage", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAppImage(@RequestParam("type") String type,@RequestParam("imgname") String img_name,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
			) {
		JResponse response=new JResponse();
		try {
			AppImage appimage = advService.getAppImages(type, img_name);
			response.put("Status", 1);
			response.put("Msg", "success");
			response.put("appimage",appimage);
			log.info("getAppImages Collected");
		}catch(Exception e)
		{
			response.put("Status", 0);
			response.put("Msg", "Image not found");
			response.put("Error", e.getLocalizedMessage());
			log.error("getAppImagesinfo : "+e.getLocalizedMessage());
		}
		return response;
	}
	
	@RequestMapping(value = "v3.0/testapi", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse testApi(@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			Authentication authentication, @RequestHeader HttpHeaders header,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		response.put("Status", 1);
		response.put("Msg", "success");
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
}
