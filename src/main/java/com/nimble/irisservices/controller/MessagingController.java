package com.nimble.irisservices.controller;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.zip.GZIPOutputStream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.gson.Gson;
import com.nimble.irisservices.dto.JMultipleEmail;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSentMessage;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Email;
import com.nimble.irisservices.helper.Thinkspace;
import com.nimble.irisservices.service.IDynamicCmdService;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IVerizonService;
import com.nimble.irisservices.service.impl.Mail;
import com.plivo.api.xml.Response;
import com.plivo.api.xml.Speak;
import com.twilio.twiml.TwiMLException;
import com.twilio.twiml.VoiceResponse;
import com.twilio.twiml.voice.Say;

import freemarker.template.Template;

@Controller
public class MessagingController {
	
	private static final Logger log = LogManager.getLogger(MessagingController.class);
	
	@Autowired
	@Lazy
	IMessagingService  messagingService;
	
	@Autowired
	@Lazy
	IDynamicCmdService dynamicServ;
	
	@Autowired
	@Lazy
	IUserService userService;
	
	@Autowired
	@Lazy
	IVerizonService verizonService;
	
	@Autowired
	Email email_helper;
	
	Mail mail = new Mail();
	
	@Value("${cust_name}")
	private String cust_name;

	@Value("${acc_name}")
	private String acc_name;

	@Value("${plan}")
	private String plan;
	
	@Value("${verizonactivation}")
	private Boolean verizonactivation;
	
	@Value("${validation_authkey}")
	private String validation_authkey;
	
	@Value("${alert_email}")
	private String alert_email;
	
	@Autowired
	Thinkspace thinkSpace;
	
	@Autowired
	freemarker.template.Configuration templates;
	
	
	
	// Used in web
	//========send message through ota or plivo gateway================
	@RequestMapping(value = "v3.0/message/{autho}", method = RequestMethod.POST, 
			headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendMessageversion(@PathVariable String autho,
			@RequestParam("gatewayid") String gatewayid, 
			@RequestParam("transporttype") int transporttype,
			@RequestParam("message") String message) {

		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			long cmpId = user.giveCompany().getId();
			boolean isvalid;
			int status = 1;
			String res_msg = "success";
			/*
			 * if(message.contains("maxadcthr") || message.contains("minadcthr")) { message
			 * = messagingService.formMessage(gatewayid, message, cmpId); }
			 */

			String gatewayIds[] = gatewayid.split(","); /* Select Multiple Assets */

			for (int i = 0; i < gatewayIds.length; i++) {
				isvalid = true;

				if (message.contains("fotaupg=upgrade")) {
					String[] params = message.split("\\$");
					String upgVer = params[1];
					
					isvalid = dynamicServ.checkVaildUpgradePacket(Long.parseLong(gatewayIds[i]), upgVer);
					
					if (isvalid == false) {
						log.info("saveDynamicCmd: " + message + " : "+ "Firmware mapping data not configured. Pls configure version & model.");

						status = 0;
						res_msg = "Firmware mapping data not configured";
					}
				}
				if (isvalid) {
					if (transporttype == 0) {
						messagingService.sendMessage(gatewayIds[i], message, user.getId());
					} else if (transporttype == 1) {
						messagingService.saveMessage(gatewayIds[i], message, user.getId());
					}
				}
			}
			response.put("Status", status);
			response.put("Msg", res_msg);
			response.put("gatewayid", gatewayid);
			response.put("sms", message);
			return response;
		} catch (InvalidAuthoException e) {
			log.error("sendMessageversion:"+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("User", "invalid authentication key");

			return response;
		} catch (Exception e) {
			log.error("sendMessageversion:"+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalidmeid");
			return response;
		}
//		try{
//			User user = userService.verifyAuthKey(autho);
//			long cmpId = user.giveCompany().getId();
//			
//			/*if(message.contains("maxadcthr") || message.contains("minadcthr"))
//			{
//				message = messagingService.formMessage(gatewayid, message, cmpId);			
//			}*/
//			
//			String gatewayIds [] = gatewayid.split(","); /*Select Multiple Assets*/
//			
//			for(int i=0;i<gatewayIds.length;i++)
//			{
//				System.out.println("Assetid :::: " +gatewayIds[i]);
//				if(transporttype == 0){
//					messagingService.sendMessage(gatewayIds[i], message, user.getId());
//				}
//				else if(transporttype == 1){
//					messagingService.saveMessage(gatewayIds[i], message, user.getId()); 
//				}
//			}
//			response.put("Status", 1);
//			response.put("Msg", "success");
//			response.put("gatewayid", gatewayid);
//			response.put("sms", message);
//			return response;
//		}
//		catch(InvalidAuthoException iae)
//		{
//
//			iae.printStackTrace();
//			
//			response.put("Status", 0);
//			response.put("User", "invalid authentication key");
//			
//			return response;
//		}
//		catch(InvalidGatewayIdException ide)
//		{
//			response.put("Status", 0);
//			response.put("Msg", "invalidmeid");
//			return response;
//		} 
		
	}
	
	// Used in web
	//========sent messages================
	@RequestMapping(value = "v3.0/message/{autho}", method = RequestMethod.GET, 
			headers = "Accept=application/json")
	@ResponseBody
	public JResponse sentMessages(@PathVariable String autho,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("subgroupid") String subgroupid,
			@RequestParam("groupid") String groupid, @RequestParam(value = "zip", defaultValue = "0", required = false) String zip
			) {

		JResponse response = new JResponse();
		try{
			User user = userService.verifyAuthKey(autho);
			long cmpId = user.giveCompany().getId();
				
			List<JSentMessage> messages = messagingService.sentMessages(groupid, subgroupid, gatewayid,user.getId());
	
			response.put("Status", 1);
			response.put("Msg", "success");
			
			if(zip.equalsIgnoreCase("1"))
				response.put("sms", zipContent(messages));
			else
				response.put("sms", messages);
			
			return response;
		}
		catch(InvalidAuthoException iae)
		{

			
			response.put("Status", 0);
			response.put("User", "invalid authentication key");
			
			return response;
		}catch (IOException e) {
			response.put("Status", 0);
			response.put("Msg","invalid content for compress"); 
			return response;
		}
	}
	
	// Used in web
	/*================== send EMAIL================ */
	@RequestMapping(value = "v3.0/nimbleemail", method = RequestMethod.POST, 
			headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendEmail(@RequestParam("emailids") String emailids,@RequestParam("subject") String subject,
			@RequestParam("content") String content,
			@RequestParam(value = "isRvEmailId", defaultValue = "false", required = false) String isRvEmailId){
			//@RequestParam("isRvEmailId") String isRvEmailId) {
		JResponse response = new JResponse();
		try{
//			log.info("Entered into sendEmail :: email_ids : "+ emailids +" :: subject : "+ subject +" :: content : "+ content +" :: from_email : "+alert_email);
			
			boolean isRVEmailId = false;
			if(isRvEmailId.equalsIgnoreCase("true"))
				isRVEmailId = true;
			messagingService.sendEmail(alert_email, emailids, subject, content,isRVEmailId);
			response.put("Status", 1);
			response.put("Msg", "success");

			return response;
		}
		catch(Exception e)
		{
			log.info("sendEmail::" +e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", e.getLocalizedMessage());
			return response;
		}
	}
	
	// Used in web
	/*================== send EMAIL================ */
	@RequestMapping(value = "v4.0/nimbleemail", method = RequestMethod.POST, 
			headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendEmailV2(@RequestParam("emailids") String emailids,@RequestParam("subject") String subject,
			@RequestParam("content") String content,
			@RequestParam(value = "isRvEmailId", defaultValue = "false", required = false) String isRvEmailId){
			//@RequestParam("isRvEmailId") String isRvEmailId) {
		JResponse response = new JResponse();
		try{
//			log.info("Entered into sendEmail :: email_ids : "+ emailids +" :: subject : "+ subject +" :: content : "+ content +" :: from_email : "+alert_email);
			
			boolean isRVEmailId = false;
			if(isRvEmailId.equalsIgnoreCase("true"))
				isRVEmailId = true;
			messagingService.sendEmailV2(alert_email, emailids, subject, content,isRVEmailId);
			response.put("Status", 1);
			response.put("Msg", "success");

			return response;
		}
		catch(Exception e)
		{
			log.info("sendEmail::" +e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", e.getLocalizedMessage());
			return response;
		}
	}
	
	/*============== send SMS ================ */
	@RequestMapping(value = "v3.0/nimblesms", method = RequestMethod.POST, 
			headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendsms(@RequestParam("phoneno") String phonenumber,@RequestParam("msg") String message,
			@RequestParam("cmpid") String companyid,@RequestParam("cmpname") String companyname,
			@RequestParam("appname") String appname,@RequestParam("type") String type) {

		log.info("sendsms called");

		JResponse response = new JResponse();
		try{
			
			boolean status = messagingService.savePlivoData(phonenumber, message, companyid, companyname, appname,type);
			if(status){
				response.put("Status", 1);
				response.put("Msg", "success");
			}else{
				response.put("Status", 0);
				response.put("Msg", "sendsms failed");
			}
			
		}
		catch(Exception e)
		{
			log.info("sendsms::" +e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", e.getLocalizedMessage());
			return response;
		}

		return response;
	}
	
	@RequestMapping(value="v3.0/texttoSpeech",method = RequestMethod.GET, headers="Accept=application/xml")
	@ResponseBody
	public void texttoSpeech(@RequestParam("msg") String message,HttpServletRequest httprequest,
			HttpServletResponse httpresponse,@RequestParam("phoneno") String phonenumber,
			@RequestParam("cmpid") String companyid,@RequestParam("cmpname") String companyname,
			@RequestParam("appname") String appname,@RequestParam("type") String type)
	{
		try {
			String response1 = new Response().children(
					new Speak(message, "MAN", "en-US", 1)).toXmlString();
			log.info("texttoSpeech : "+response1);
			httpresponse.addHeader("Content-Type", "text/xml");
			httpresponse.getWriter().print(response1);
			
		}catch (Exception e) {
			log.error("texttoSpeech : Exp : "+e.getLocalizedMessage());
		}
		
//		PlivoResponse response1 = new PlivoResponse();
//		try
//		{
//			// Add Speak XML Tag with English text
//			Speak spk1 = new Speak(message);
//			spk1.setLanguage("en-US"); // Language used to read out the text.
//			spk1.setVoice("MAN"); // The tone to be used for reading out the text.
//			response1.append(spk1);
//			System.out.println(response1.toXML());
//			log.info(response1.toXML());
//			httpresponse.addHeader("Content-Type", "text/xml");
//			httpresponse.getWriter().print(response1.toXML());
//
//		} catch (PlivoException e) {
//			e.printStackTrace();
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
//		catch (Exception e) {
//			log.info("texttoSpeech::" +e.getLocalizedMessage());
//		}
	}
	
	@RequestMapping(value="v3.0/twilio_texttoSpeech",method = RequestMethod.GET, headers="Accept=application/xml")
	@ResponseBody
	public void twilio_texttoSpeech(@RequestParam("msg") String message,HttpServletRequest httprequest,
			HttpServletResponse httpresponse)
	{
			System.out.println("v3.0/twilio_texttoSpeech");
			
		
			try {
				
					String msg_decode  = URLDecoder.decode(message.trim(),"UTF-8");
				
					Say say = new Say.Builder(message).voice(Say.Voice.MAN)
			            .build();
			        VoiceResponse response = new VoiceResponse.Builder().say(say).build();
					
	
			       
			        System.out.println("twilio_texttoSpeech:: Response.xml = "+response.toXml());
			        httpresponse.addHeader("Content-Type", "text/xml");
					httpresponse.getWriter().print(response.toXml());
					
					
					
		        } catch (UnsupportedEncodingException e1) {
					// TODO Auto-generated catch block
				}catch (TwiMLException e) {
		        } catch (IOException e) {
					// TODO Auto-generated catch block
				}

		
	}
	
	//========send message through ota or plivo gateway.This service only using for Listener================
	@RequestMapping(value = "v3.0/messageV2", method = RequestMethod.POST, 
			headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendMessageversionV2(@RequestParam("gatewayid") String gatewayid,@RequestParam("transporttype") 
		int transporttype,@RequestParam("message") String message,@RequestParam("seqno") long seqno) {
		log.info("Entered into sendMessageversionV2 :: gateway ID : "+gatewayid+" :: transporttype : "+transporttype+" :: message : "+message+" :: seqno : "+seqno);
		JResponse response = new JResponse();
		try{
			
			String gatewayIds [] = gatewayid.split(","); /*Select Multiple Assets*/

			for(int i=0;i<gatewayIds.length;i++)
			{
				if(transporttype == 1){
					messagingService.saveMessageV2(gatewayIds[i], message, seqno); 
				}
			}
			response.put("Status", 1);
			response.put("Msg", "success");
			response.put("gatewayid", gatewayid);
			response.put("sms", message);
			return response;
		}
		catch(Exception e)
		{
			log.error("sendMessageversionV2::" +e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalidmeid");
			return response;
		} 

	}
	
	/*================== send VOICE================ */
	@RequestMapping(value="v3.0/nimblevoice",method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendVoice(@RequestParam("msg") String message,HttpServletRequest httprequest,
			HttpServletResponse httpresponse,@RequestParam("phoneno") String phonenumber,
			@RequestParam("cmpid") String companyid,@RequestParam("cmpname") String companyname,
			@RequestParam("appname") String appname,@RequestParam("type") String type,
			@RequestParam("ip") String ip)
	{

		JResponse response = new JResponse();
		try{
			log.info("nimblevoice service");
			messagingService.savePlivoVoiceData(phonenumber, message, companyid, companyname, appname, type,ip);
			response.put("Status", 1);
			response.put("Msg", "success");
			return response;
		}
		catch(Exception e){
			log.info("sendVoice::" +e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", e.getLocalizedMessage());
			return response;
		}
	}
	
	public static byte[] zipContent(Object obj) throws IOException
	{
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		GZIPOutputStream gzipOut = new GZIPOutputStream(baos);

		Gson gson = new Gson();
		String json = gson.toJson(obj);
		gzipOut.write(json.getBytes());

		gzipOut.close();
		//objectOut.close();
		byte[] bytes = baos.toByteArray();

		return bytes;
	}
	
	@RequestMapping(value="v3.0/verizonservice",method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse verizonservice(@RequestParam("activityType") String activityType,
			@RequestParam("meid") String meid,@RequestParam("iccid") String iccid)	{
		JResponse response = new JResponse();
		String simStatus = "";
		try{
			if(activityType.equalsIgnoreCase("activate"))
				simStatus = thinkSpace.SimActivation(meid, cust_name, acc_name, plan, iccid,"",verizonService,null);
			else if(activityType.equalsIgnoreCase("suspend"))
				simStatus = thinkSpace.SimSuspend(meid, cust_name, acc_name, plan, iccid,"",verizonService);
			else if(activityType.equalsIgnoreCase("restore"))
				simStatus = thinkSpace.simRestore(meid, cust_name, acc_name, plan, iccid,"",verizonService);
			else if(activityType.equalsIgnoreCase("deactivate"))
				simStatus = thinkSpace.simDeactivate(meid, cust_name, acc_name, plan, iccid,"",verizonService);
			else
				simStatus = "Invaild activityType:NA";
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("opStatus", simStatus.split(":")[0]);
			response.put("CurrSimStatus", simStatus.split(":")[1]);
			log.info("verizonservice: "+simStatus);
		}
		catch(Exception e){
			log.error("verizon sim activity log ::" +e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", e.getLocalizedMessage());
			
		}
		return response;
	}
	
	// Used in web
	@RequestMapping(value = "v3.0/fotamessage/{autho}", method = RequestMethod.POST, 
			headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendMessageFotaVersion(@PathVariable String autho,
			@RequestParam("gatewayid") String gatewayid, 
			@RequestParam("transporttype") int transporttype,
			@RequestParam("message") String message) {

		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			boolean isvalid;
			int status = 1;
			String res_msg = "success";
			String gatewayIds[] = gatewayid.split(","); /* Select Multiple Assets */

			for (int i = 0; i < gatewayIds.length; i++) {
				isvalid = true;

				if (message.contains("fotaupg=upgrade")) {
					String[] params = message.split("\\$");
					String upgVer = params[1];
					
					isvalid = dynamicServ.checkVaildUpgradePacket(Long.parseLong(gatewayIds[i]), upgVer);
					
					if (isvalid == false) {
						log.info("saveDynamicCmd: " + message + " : "+ "Firmware mapping data not configured. Pls configure version & model.");

						status = 0;
						res_msg = "Firmware mapping data not configured";
					}
				}
				if (isvalid) {
					if (transporttype == 1) {
						long gatewayId = Long.parseLong(gatewayIds[i]);
						messagingService.saveFotaMessage(gatewayId, message);
					}
				}
			}
			response.put("Status", status);
			response.put("Msg", res_msg);
			response.put("gatewayid", gatewayid);
			response.put("sms", message);
			return response;
		} 
		catch (InvalidAuthoException iae) {
			response.put("Status", 0);
			response.put("User", "invalid authentication key");

			return response;
		} 
		catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "invalidmeid");
			return response;
		}
	}
	
	@RequestMapping(value="v3.0/sendmultipleemail",method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse multipleEmailService(@RequestBody JMultipleEmail jRequest,	@RequestHeader HttpHeaders header)	{
		log.info("Entered into multipleEmailService...");
		log.info("type : "+jRequest.getType());
		JResponse response = new JResponse();
		String autho = header.getFirst("auth");
		try{
			if(!validation_authkey.equalsIgnoreCase(autho)) {
				log.error("Invalid authkey");
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				return response;
			}
			
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			Properties prop = new Properties();
			prop.load(new FileInputStream(file));
			
			Template template = null;
			String emailSub = "";
			if(jRequest.getType().toLowerCase().contains("avatar")) {
				if(jRequest.getType().contains("welcome")) {
					template = (Template) templates.getTemplate("welcomeavatar.ftl");
					emailSub = prop.getProperty("welcomeavatarsub");
				} else {
					template = (Template) templates.getTemplate("successavatar.ftl");
					emailSub = prop.getProperty("successavatarsub");
				}
			}
			
			Map<String, String> deleteRequestParams = new HashMap<>();
			String ccEmail = prop.getProperty("ccemail_avatar");
			String bccEmail = prop.getProperty("bccemail_avatar");
			
			String emailSplit[] = jRequest.getEmailids().split(",");
			String firstNameSplit[] = jRequest.getUsername().split(",");
			int firstNameLength = firstNameSplit.length;
			deleteRequestParams.put("firstname", "Dear");
			for(int i=0;i<emailSplit.length;i++) {
				if(firstNameLength > 1)
					deleteRequestParams.put("firstname", firstNameSplit[i]);
				
				deleteRequestParams.put("email", emailSplit[i]);
				
				ResponseEntity<String> deleteRequestContent = ResponseEntity
						.ok(FreeMarkerTemplateUtils.processTemplateIntoString(template, deleteRequestParams));
				String emailContent = deleteRequestContent.getBody();
				email_helper.SendEmail_SES_V2(emailSplit[i], ccEmail,
						bccEmail, emailSub, emailContent);
			}
			
			response.put("Status", 1);
			response.put("Msg", "Success");
		}
		catch(Exception e){
			log.error("multipleEmailService ::" +e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", e.getLocalizedMessage());
			
		}
		return response;
	}
	
}
