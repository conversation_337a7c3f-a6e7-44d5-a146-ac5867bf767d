package com.nimble.irisservices.controller;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.AlertCfgV4;
import com.nimble.irisservices.dto.CompanyConfigResponse;
import com.nimble.irisservices.dto.JAlertCfgV4;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JGatewayConfig;
import com.nimble.irisservices.dto.JGeofence;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AlertCfg;
import com.nimble.irisservices.entity.AppImage;
import com.nimble.irisservices.entity.Asset;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.service.IAdvertisementService;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAlertCfgServiceV4;
import com.nimble.irisservices.service.ICompanyServiceV4;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class AlertCfgControllerV4 {

	private static final Logger log = LogManager.getLogger(AlertCfgControllerV4.class);

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	ICompanyServiceV4 companyServicev4;

	@Autowired
	@Lazy
	IAlertCfgServiceV4 alertCfgServiceV4;
	
	@Autowired
	@Lazy
	IAlertCfgService alertCfgService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;
	
	@Value("#{${notifyfreq_info_url}}")
	private Map<String,String> notifyfreq_info_url;
	
	@Autowired
	@Lazy
	IAdvertisementService advService;
	
	@Value("${gps_info}")
	private String gps_info = "NA";

	@RequestMapping(value = "v4.0/alertcfg/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertCfgV4(@PathVariable String autho, @RequestParam("assetid") String assetid , 
			@RequestParam(value="sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		log.info("Entering getAlertCfgV4 : "+ autho );
		try {
			Map<String, String> map = null;
			try {
				map = userServiceV4.getUserId_cmpIdByAuth(autho);
			}catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key : " + e.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			if(!map.isEmpty()) {
				long userId = Long.valueOf(map.get("user_id"));

				UserV4 user = userServiceV4.verifyAuthV3("id", map.get("user_id"));
				boolean restrict_alert = true;
		
				if(user.getPlan_ver().equalsIgnoreCase("V1"))
					restrict_alert = false;
				
				response.put("restrict_alert", restrict_alert);

				String tempunit = map.get("tempunit");
				
				boolean  geofence_enable = false;
				
				if(map.get("geofence_enable").equalsIgnoreCase("1"))
					geofence_enable = true;
				
				int temp_range = 1; // for celsius 
				
				if(tempunit.equalsIgnoreCase("F"))
					temp_range = 2;
				long asset_id = 0;
				
				if(!assetid.isEmpty())
					asset_id = Long.parseLong(assetid);
				List<AlertCfgV4> alertcfgs = alertCfgServiceV4.getAlertCfgV4(userId,tempunit,asset_id,geofence_enable);
				
//				int notify = 0;
				int powerloss_min = 30;
				
//				for (AlertCfgV4 alertcfgObj : alertcfgs) {					
//					if(alertcfgObj.getAlerttypeid() == 1l) {
//						notify = alertcfgObj.getNotifyfreq();
//					}
//					
//					if(alertcfgObj.getAlerttypeid() == 3l && (alertcfgObj.getNotifyfreq() < (powerloss_min* 60))  ) {
//						powerloss_min =( alertcfgObj.getNotifyfreq()/60);
//					}
//				}
//				
//				for (AlertCfgV4 alertcfgObj : alertcfgs) {										
//					if(alertcfgObj.getAlerttypeid() == 2l && notify>0) {
//						alertcfgObj.setNotifyfreq(notify);
//						break;
//					}					
//				}
					
				Set<Long> assetIds = new HashSet<Long>();
				List<JGateway> userGateway = gatewayService.getGateway(null, null, null, assetid, userId, null);
				List<JGatewayConfig> gatewayConfigs = new ArrayList<JGatewayConfig>();
				if (userGateway != null) {
					for (JGateway jgateway : userGateway) {
						assetIds.add(jgateway.getId());
					}
				}
				
				for (Long id : assetIds) {
					JGatewayConfig config = new JGatewayConfig();
					Gateway gatewayDetails = gatewayService.getGateway(id);
					config.setGatewayConfig(gatewayDetails.getGatewayConfig());
					config.setOnOffStatus(gatewayDetails.isOnOffStatus());
					config.setAssetid(id);
					gatewayConfigs.add(config);
				}

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("gps_info", gps_info);
				response.put("temp_range", temp_range);
				response.put("alertcfg", alertcfgs);
				response.put("gatewayConfig", gatewayConfigs);
				response.put("powerloss_min", powerloss_min);
				response.put("powerloss_msg", "Power Loss Alert is currently turned off. Do you want to turn it on?");
				response.put("powerback_msg", "Power Recovery Alert is currently turned off. Do you want to turn it on?");

				String country = user.getCountry().toUpperCase();
				if(country.equalsIgnoreCase("US")|| country.equalsIgnoreCase("NA")
						|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")
						|| country.isEmpty() || country == null) {
					country = "US";
				}
				
				response.put("notifyfreq_info", "<p style=text-align:justify>Waggle alerts you Instantly once the temperature exceeds set limits."
						+ " Once an alert is sent, you also get reminders every 60 minutes by default until it goes back to the set range."
						+ " This setting allows you to set a different value.<br><br><a href="+notifyfreq_info_url.get(country)+">Click Here!</a> For more details</p>");
				response.put("powerloss_info", "<p style=text-align:justify>Waggle alerts you Instantly once the RV/Car/Home loses power."
						+ " Once an alert is sent, you also get reminders every 1 hour by default until the power is resumed."
						+ " This setting allows you to set a different value.<br><br><a href="+notifyfreq_info_url.get(country)+">Click Here!</a> For more details</p>");
				
				String notifyfreq_img = "NA";			
				AppImage notifyfreqObj = advService.getAppImages("common", "notify-frequency");
				if(notifyfreqObj != null)
				{
					notifyfreq_img = notifyfreqObj.getImg_path();
				}
				response.put("notifyfreq_img", notifyfreq_img);
				response.put("notifyfreq_url", notifyfreq_info_url.get(country));
				
				String powerloss_img = "NA";
				AppImage powerlossObj = advService.getAppImages("common", "powerloss-msg");
				if(powerlossObj != null)
				{
					powerloss_img = powerlossObj.getImg_path();
				}
				response.put("powerloss_img", powerloss_img);
				response.put("powerloss_url", notifyfreq_info_url.get(country));

			}else
			{
				response.put("Status", 0);
				response.put("Msg", "User Details not found");
			}
		} catch (Exception e) {
			log.error("Exception : getAlertCfgV4 : " +e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to get Alert Configuration");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	// Kalai
	@RequestMapping(value = "v4.0/alertcfg/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateAlertcfg(@PathVariable String autho, @RequestBody JAlertCfgV4 jalertCfg,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		log.info("Entering updateAlertcfg : " + autho);
		JResponse response = new JResponse();
		response.put("Msg_content","");

		try {
			UserV4 user = null;

			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (Exception e) {
				log.error("verifyAuthKey : " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Please try after some time!");
				response.put("Error", e.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (user != null) {
				int result = 0;
				String updatefor = jalertCfg.getUpdatefor();
				log.info("updatefor: "+updatefor);
				String[] assetCfgLst = jalertCfg.getAlertcfgids().split(",");

				AlertCfg alertcfg = alertCfgServiceV4.getAlertCfg(Long.parseLong(assetCfgLst[0]));

				Set<Asset> listassets = alertcfg.getAssets();
				boolean checkAsset = false;

				for (Asset ast : listassets) {
					if (ast.getId() == jalertCfg.getAssetid()) {
						checkAsset = true;
						break;
					}
				}

				if (checkAsset == false) {
					response.put("Status", 0);
					response.put("Msg", "Please try after some time!");
					response.put("Error", "Asset configuration not matched");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				if (!jalertCfg.getAlerttypeids().contains(String.valueOf(alertcfg.getAlerttype().getId()))) {
					response.put("Status", 0);
					response.put("Msg", "Please try after some time!");
					response.put("Error", "Alert type not matched");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
/*
 Email   =  Email updated successfully
Mobile Number = Mobile number  updated successfully
Minimum temperature =  Minimum temperature updated successfully
Maximum temperature = Maximum temperature updated successfully
Battery = Battery updated successfully
Alert frequency = Alert frequency updated successfully
Maximum Humidity = Maximum Humidity updated successfully
Minimum Humidity = Minimum Humidity updated successfully
Enable all Alert = Alert updated successfully
Email Alert = Alert updated successfully
Mobile Number alert = Alert updated successfully
 */				String msg = "Success";
				String country = user.getCountry().toUpperCase();
				if (country.equalsIgnoreCase("US") || country.equalsIgnoreCase("NA")
						|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")
						|| country.isEmpty() || country == null) {
					country = "US";
				}
				
				if (updatefor.equalsIgnoreCase("enabledisable")) {
					result = alertCfgServiceV4.enabledisablealertcfg(jalertCfg.getAlertcfgids(), jalertCfg.isEnable());
					msg ="Alert updated successfully";
				} else if (updatefor.equalsIgnoreCase("updateemailphone")) {
					CompanyConfigResponse cmp_cfg = companyServicev4.getCompanyConfigAndCompany(user.getCmpId());

					long mobile_count = cmp_cfg.getMobileNos();
					long email_count = cmp_cfg.getEmailIds();

					long entered_mobileNos = jalertCfg.getMobilenos().split(",").length;
					long entered_emailIds = jalertCfg.getEmailids().split(",").length;

					if ((mobile_count >= entered_mobileNos && email_count >= entered_emailIds)
							|| cmp_cfg.getThrottsettings_id() == 5) {
						String phnos= jalertCfg.getMobilenos().replaceAll("\\s", "");
						
						String mailids= jalertCfg.getEmailids().replaceAll("\\s", "");						

						result = alertCfgServiceV4.updateEmailPhone(jalertCfg.getAlertcfgids(),	phnos, mailids);
						msg ="Updated successfully";
						
						if (result > 0) {
							log.info("update Phno/mail: "+phnos +" : "+mailids);
							
							response.put("Status", 1);
							response.put("Msg", msg);
						} else {
							response.put("Status", 0);
							response.put("Msg", "Please try after some time!");
							response.put("Error", "Alert cfgs not updated");
						}
					} else {
						response.put("Status", 0);
						response.put("Msg", "You are allowed to enter " + mobile_count + " mobile numbers and "
								+ email_count + " email address");
					}
				} 
				else if (updatefor.equalsIgnoreCase("updatenotifyfreq")) {
					result = alertCfgServiceV4.updateNotify(jalertCfg.getAlertcfgids(), "1",jalertCfg.getNotifyfreq());
					msg ="Temp Alert frequency updated successfully";
				} 
				else if (updatefor.equalsIgnoreCase("powerlossnotifyfreq")) {
					result = alertCfgServiceV4.updateNotify(jalertCfg.getAlertcfgids(), "3",jalertCfg.getNotifyfreq());
					msg ="Power Loss Alert frequency updated successfully";
				}
				else if (updatefor.equalsIgnoreCase("minmax")) {
					result = alertCfgServiceV4.updateAlertCfg(Long.parseLong(jalertCfg.getAlertcfgids()),
							Long.parseLong(jalertCfg.getAlerttypeids()), jalertCfg.getMinval(), jalertCfg.getMaxval(),
							jalertCfg.getAssetid(), user.getCmpId(), "NA");
					msg = "Alert value updated successfully";
				}
				else if (updatefor.equalsIgnoreCase("notificationtype")) {
					if(jalertCfg.getNotificationtype().length() == 3)
						jalertCfg.setNotificationtype(jalertCfg.getNotificationtype()+"0");
					result = alertCfgServiceV4.updateNotificationType(jalertCfg.getAlertcfgids(),jalertCfg.getNotificationtype());
					msg = "Alert updated successfully";
				}
				else if (updatefor.equalsIgnoreCase("geofence")) {
					response = alertCfgServiceV4.updateGeofenceDetails(jalertCfg,false);
					result = (int)response.get("Status");
					//while updating radius ,based on distance fence state will be update
//					if(!jalertCfg.isRecenter()) {
//						boolean res2 = alertCfgServiceV4.findGeofenceState(Long.parseLong(jalertCfg.getAlertcfgids()), jalertCfg.getAssetid(), jalertCfg.getLat(),
//										jalertCfg.getLon(), jalertCfg.getRadius());
//					}
					msg = "Geofence data updated successfully";
				}

				if(result > 0 && updatefor.equalsIgnoreCase("updatenotifyfreq")) {
					response.put("Status", 1);
					response.put("Msg", msg);
					response.put("Msg_content", "<p style=text-align:justify>Waggle alerts you Instantly once the temperature exceeds set limits."
							+ " Once an alert is sent, you also get reminders every 60 minutes by default until it goes back to the set range."
							+ " This setting allows you to set a different value.<br><br><a href="+notifyfreq_info_url.get(country)+">Click Here!</a> For more details</p>");
					
					String notifyfreq_img = "NA";			
					AppImage notifyfreqObj = advService.getAppImages("common", "notify-frequency");
					if(notifyfreqObj != null)
					{
						notifyfreq_img = notifyfreqObj.getImg_path();
					}
					response.put("notifyfreq_img", notifyfreq_img);
					response.put("notifyfreq_url", notifyfreq_info_url.get(country));
				}
				else if(result > 0 && updatefor.equalsIgnoreCase("powerlossnotifyfreq")) {
					response.put("Status", 1);
					response.put("Msg", msg);
					response.put("Msg_content", "<p style=text-align:justify>Waggle alerts you Instantly once the RV/Car/Home loses power."
							+ " Once an alert is sent, you also get reminders every 1 hour by default until the power is resumed."
							+ " This setting allows you to set a different value.<br><br><a href="+notifyfreq_info_url.get(country)+">Click Here!</a> For more details</p>");
					
					String powerloss_img = "NA";
					AppImage powerlossObj = advService.getAppImages("common", "powerloss-msg");
					if(powerlossObj != null)
					{
						powerloss_img = powerlossObj.getImg_path();
					}
					response.put("powerloss_img", powerloss_img);
					response.put("powerloss_url", notifyfreq_info_url.get(country));
				}
				else if (result > 0) {
					response.put("Status", 1);
					response.put("Msg", msg);
				} else {
					response.put("Status", 0);
					response.put("Error", "Alert config updation failed");
					response.put("Msg", "Please try after some time!");
				}
				
				log.info("Alertcfg update: "+msg);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid User");
			}
		} catch (Exception e) {
			log.error("Exception : updateAlertcfg : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Alert Config updation failed");
			response.put("Msg_content","");

			response.put("Error", e.getLocalizedMessage());
		}
		log.info("Exit updateAlertcfg");
	
		response.put("Return Time", System.currentTimeMillis());
		return response;

	}

	@RequestMapping(value = "v4.0/getgeofence/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGeofence(@PathVariable String autho, @RequestParam("assetid") long assetid , 
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		log.info("Entering getAlertCfgV4 : " + autho);
		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Invalid authentication key : " + e.getLocalizedMessage());
				return response;
			}
			if (usr != null) {

				JGeofence geofence = alertCfgServiceV4.getGeofenceDetails(assetid, "v4");
				if (geofence != null) {

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("geofence", geofence);
				} else {
					response.put("Status", 0);
					response.put("Msg", "unable to get fence details");
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "User Details not found");
			}
		} catch (Exception e) {
			log.error("Exception : getgeofence : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to get geofence Configuration");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}
}
