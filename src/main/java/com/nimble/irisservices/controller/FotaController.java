package com.nimble.irisservices.controller;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dto.FotaUpgrade;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.FotaModel;
import com.nimble.irisservices.entity.FotaVersion;
import com.nimble.irisservices.entity.LastFotaRpt;
import com.nimble.irisservices.service.IFotaService;

@Controller
public class FotaController {
	private static final Logger log = LogManager.getLogger(FotaController.class);

	@Autowired
	@Lazy
	IFotaService fotaService;

	// Used in web
	/* listfotamodel API */
	@RequestMapping(value = "v4.0/listfotamodel/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listFotaModel(@PathVariable String autho) {
		JResponse response = new JResponse();
		log.info("Entering listFotaModel : ");

		try {
			List<FotaModel> fotaModelList = fotaService.getFotaModelList();

			for (FotaModel model : fotaModelList) {
				long modelid = model.getAssetmodel().getId();
				model.setModel_id(modelid);
			}

			if (fotaModelList != null && !fotaModelList.isEmpty()) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("fotamodellist", fotaModelList);
				log.info("Fota Model list found!");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Exception Occurred/No fotamodel found!");
				log.info("No fotamodel found!");
			}
			log.info("Exit :: fotaModelList ::");

		} catch (Exception e) {
			log.error("Exception : listfotamodel :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;

	}

	// Used in web
	/* listfotaversion API */
	@RequestMapping(value = "v4.0/listfotaversion/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listFotaVersion(@PathVariable String autho) {
		JResponse response = new JResponse();
		log.info("Entering listFotaVersion : ");

		try {
			List<FotaVersion> fotaVersionList = fotaService.getFotaVersionList();

			for (FotaVersion model : fotaVersionList) {
				long modelid = model.getAssetmodel().getId();
				model.setCurr_modelid(modelid);
			}

			if (fotaVersionList != null && !fotaVersionList.isEmpty()) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("fotaversionlist", fotaVersionList);
				log.info("Fota Version list found!");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Exception Occurred/No fotaversion found!");
				log.info("No fotaversion found!");
			}
			log.info("Exit :: fotaVersionList ::");

		} catch (Exception e) {
			log.error("Exception : listfotaversion :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;

	}

	// Used in web
	/* savefotamodel API */
	@RequestMapping(value = "v4.0/savefotamodel/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse saveFotaModel(@PathVariable String autho, @RequestBody FotaModel ftModel) {
		JResponse response = new JResponse();
		log.info("Entering saveFotaModel : ");

		try {
			FotaModel fota = new FotaModel();

			long id = ftModel.getId();
			fota.setId(id);

			String fota_ver = ftModel.getFota_ver();
			long model_id = ftModel.getModel_id();

			if (fota_ver == "NA" || model_id == 0) {
				response.put("Status", 0);
				response.put("Msg", "Invalid data");
				return response;
			}
			fota.setFota_ver(fota_ver);
			fota.setModel_id(model_id);

			AssetModel assetModel = fotaService.getAssetModelById(model_id);

			if (assetModel == null) {
				response.put("Status", 0);
				response.put("Msg", "modelid does not exist");
				return response;
			}

			fota.setAssetmodel(assetModel);

			boolean Status = fotaService.createOrUpdateModel(fota);

			if (Status) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Fotaversion already exist!!!");
			}

		} catch (DataIntegrityViolationException e) {
			log.error("createOrUpdateModel : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Fotaversion already exist!!!");
			response.put("Error", e.getLocalizedMessage());
		} catch (ConstraintViolationException e) {
			log.error("createOrUpdateModel : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Fotaversion already exist!!!");
			response.put("Error", e.getLocalizedMessage());
		} catch (Exception e) {
			log.error("Exception : saveFotaModel : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExpected Error in Fota Model");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	// Used in web
	/* savefotaversion API */
	@RequestMapping(value = "v4.0/savefotaversion/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse saveFotaVersion(@PathVariable String autho, @RequestBody FotaVersion ftVersion) {
		JResponse response = new JResponse();
		log.info("Entering saveFotaVersion : ");
		try {
			FotaVersion version = new FotaVersion();

			long id = ftVersion.getId();
			String fota_ver = ftVersion.getFota_version();
			String file_path = ftVersion.getFilename();
			long file_size = ftVersion.getFilesize();
			String curr_ver = ftVersion.getCurr_version();
			String create_date = ftVersion.getCreatedon();
			if (create_date == "NA" || create_date == "") {
				create_date = IrisservicesUtil.getCurrentTimeUTC();
			}
			long currModelId = ftVersion.getCurr_modelid();
			int enable = ftVersion.getEnable();

			if (fota_ver == "NA" || file_path == "NA" || file_size == 0 || curr_ver == "NA" || create_date == "NA"
					|| currModelId == 0 || enable == -1) {
				response.put("Status", 0);
				response.put("Msg", "Invalid data");
				return response;
			}

			version.setId(id);
			version.setFota_version(fota_ver);
			version.setFilename(file_path);
			version.setFilesize(file_size);
			version.setCurr_version(curr_ver);
			version.setCreatedon(create_date);
			version.setCurr_modelid(currModelId);
			version.setEnable(enable);

			AssetModel assetModel = fotaService.getAssetModelById(currModelId);

			if (assetModel == null) {
				response.put("Status", 0);
				response.put("Msg", "curr_modelid does not exist");
				return response;
			}

			version.setAssetmodel(assetModel);

			boolean Status = fotaService.createOrUpdateVersion(version);

			if (Status) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "New and Old Fotaversion already exist!!!");
			}

		} catch (DataIntegrityViolationException e) {
			log.error("createOrUpdateModel : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "New and Old Fotaversion already exist!!!");
			response.put("Error", e.getLocalizedMessage());
		} catch (ConstraintViolationException e) {
			log.error("createOrUpdateModel : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "New and Old Fotaversion already exist!!!");
			response.put("Error", e.getLocalizedMessage());
		} catch (Exception e) {
			log.error("Exception : saveFotaVersion : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExpected Error in Fota Model");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

//	/* getallfotaversion API to get all versions from fota_version_mapping*/
//	@RequestMapping(value = "v4.0/getallfotaversion/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getAllFotaVersion(@PathVariable String autho) {
//		JResponse response = new JResponse();
//		log.info("Entering getallfotaversion : ");
//
//		try {
//			String[] fotaVersionList = fotaService.getAllFotaVersion();
//			if (fotaVersionList != null && fotaVersionList.length > 0) {
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//				response.put("getallfotaversion", fotaVersionList);
//				log.info("Fota Version list found!");
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Exception Occurred/No fotaversion found!");
//				log.info("No fotaversion found!");
//			}
//			log.info("Exit :: fotaVersionList ::");
//
//		} catch (Exception e) {
//			log.error("Exception : getallfotaversion :" + e.getLocalizedMessage());
//			response.put("Status", 0);
//			response.put("Msg", "Exception occured!");
//			response.put("Error", e.getLocalizedMessage());
//			return response;
//		}
//		return response;
//
//	}

	// Used in web
	// Get fota details - Savitha
	@RequestMapping(value = "v4.0/getfotadetails/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFotaDetails(@PathVariable String autho,
			@RequestParam(value = "skey", required = false) String sKey,
			@RequestParam(value = "svalue", required = false) String sValue,
			@RequestParam(value = "ftype", defaultValue = "equals", required = false) String fType,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "desc", required = false) String otype,
			@RequestParam(value = "okey", required = false) String oKey,
			@RequestParam(value = "os", required = false) String os,
			@RequestParam(value = "model_id", required = true) long model_id) {
		JResponse response = new JResponse();
		log.info("Entering getfotadetails : ");

		try {
			response = fotaService.getFotaDetails(sKey, sValue, fType, otype, offset, limit, oKey, model_id, response, null);
		} catch (Exception e) {
			log.error("Exception : getfotadetails : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	// By karthik
	@RequestMapping(value = "v4.0/getfotadetailsV2/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllMeids(@PathVariable String autho, @RequestBody(required = false) FotaUpgrade meid,
			@RequestParam(value = "skey", required = false) String sKey,
			@RequestParam(value = "svalue", required = false) String sValue,
			@RequestParam(value = "ftype", defaultValue = "equals", required = false) String fType,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "desc", required = false) String otype,
			@RequestParam(value = "okey", required = false) String oKey,
			@RequestParam(value = "os", required = false) String os,
			@RequestParam(value = "model_id", required = true) long model_id) {
		JResponse response = new JResponse();
		log.info("Entering getfotadetails : ");

		try {
			response = fotaService.getFotaDetails(sKey, sValue, fType, otype, offset, limit, oKey, model_id, response,
					meid);
		} catch (Exception e) {
			log.error("Exception : getfotadetails : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}
	
	@RequestMapping(value = "v4.0/getlastfotareport/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getLastfotareport(@PathVariable String autho, @RequestParam(value = "meid") String meid) {
		JResponse response = new JResponse();
		log.info("Entering getLastfotareport : ");

		try {
			LastFotaRpt fotaRpt = fotaService.getLastfotareport(meid);
			if(fotaRpt != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("lastfotalist", fotaRpt);
				log.info("last fota list found!");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed");
				response.put("lastfotalist", fotaRpt);
				log.info("last fota list empty!");
			}
			
		}catch (Exception e) {
			log.error("getLastfotareport : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

}
