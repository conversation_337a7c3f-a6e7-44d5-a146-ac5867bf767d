package com.nimble.irisservices.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JAssetLastReportV4;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IReminderService;
import com.nimble.irisservices.service.IReportServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class ReportControllerV4 {

	@Autowired
	IUserServiceV4 userServiceV4;
	

	@Autowired
	IReportServiceV4 reportServiceV4;

	@Autowired
	IReminderService reminderservice;

	@Value("${notify_freq_msg}")
	private String notify_freq_msg;
	
	@Value("${oldDevicePlanPurchase}")
	private boolean oldDevicePlanPurchase;
	
	@Value("${oldDevicePlanPurchaseMSG}")
	private String oldDevicePlanPurchaseMSG;

	private static final Logger log = LogManager.getLogger(ReportControllerV4.class);

	// V4 - External Consultant
	@RequestMapping(value = "v4.0/gatewaysummaryTest/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGatewaySummaryTestV4(@PathVariable String autho, @RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("offset") String offset,
			@RequestParam("limit") String limit,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "backing", defaultValue = "MT", required = false) String backing) {

		log.info("called gateway summary services" + "groupid: " + groupid + ", subgroupid: " + subgroupid
				+ ", assetgroupid: " + assetgroupid + " , gatewayid: " + gatewayid);
		JResponse response = new JResponse();
		log.info("Entering getAssetSummaryTestV4 : " + autho);
		try {
			Map<String, String> map = new HashMap<String, String>();

			try {
				
//				Helper _helper = new Helper();
				log.info("backing key : "+backing);
				
//				AES aes = new AES();
//				String auth = null;
//				if (backing != null) {
//					if (!backing.equals("MT")) {
//						String[] credential = _helper.decodeInternalKey(backing);
//						String finalOut = aes.decode(credential[0], credential[1]);
//						
//						if (finalOut == null) {
//							response.put("Status", 0);
//							response.put("Msg", "Authentication Error");
//							return response;
//						}
//						log.info("AES decryption success : "+backing+" : "+finalOut);
//					}
//				} else {
//					response.put("Status", 0);
//					response.put("Msg", "Authentication Error");
//					return response;
//				}
				map = userServiceV4.getUserId_cmpIdByAuth(autho);

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			long userId = Long.valueOf(map.get("user_id"));
			long cmp_id = Long.valueOf(map.get("cmp_id"));
			int remainderOverdueCount = -1;

			List<JAssetLastReportV4> reportsummmary = reportServiceV4.getLastgatewayreportV4(groupid, subgroupid,
					assetgroupid, gatewayid, userId, offset, limit, map.get("tempunit"), "US");
			log.info("received gateway summary , report length: " + reportsummmary.size());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("lastgatewayreport", reportsummmary);
			response.put("oldDevicePlanPurchase",oldDevicePlanPurchase);
			response.put("oldDevicePlanPurchaseMSG",oldDevicePlanPurchaseMSG);
			
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in gatewaysummaryTest");
			response.put("Error", e.getLocalizedMessage());
			response.put("oldDevicePlanPurchase",oldDevicePlanPurchase);
			response.put("oldDevicePlanPurchaseMSG",oldDevicePlanPurchaseMSG);
			
			log.error("Exception : getGatewaySummaryTestV4 : " + e.getLocalizedMessage());

		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
}
	
	
	
