package com.nimble.irisservices.controller;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JAlertOverview;
import com.nimble.irisservices.dto.JAlertRange;
import com.nimble.irisservices.dto.JGatewayOverview;
import com.nimble.irisservices.dto.JNodeOverview;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSreiAlertOverview;
import com.nimble.irisservices.dto.JSreiGatewayOverview;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IAlertService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.INodeService;
import com.nimble.irisservices.service.IUserService;

@Controller
public class OverviewController {
	
	private static final Logger log = LogManager.getLogger(OverviewController.class);
	
	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	INodeService nodeService;
	
	@Autowired
	@Lazy
	IAlertService alertService;
	
	// Used in web
	/* =============== Asset Overview ================ */
	@RequestMapping(value="v3.0/gatewayoverview/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getGatewayOverview(@PathVariable String autho, @RequestParam("groupid") String groupid, 
			@RequestParam("subgroupid") String subgroupid ,@RequestParam("levelid") String levelid)	{
		
		JResponse response = new JResponse();
		
		try {
			User user = userService.verifyAuthKey(autho);
			
			JGatewayOverview goverview = gatewayService.getgatewayoverview(groupid,subgroupid,user.getId(),levelid);
			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("gatewayoverview", goverview);
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		
		return response;
	}
	
	//========Sensor Overview================
	@RequestMapping(value="v3.0/nodeoverview/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getNodeOverview(@PathVariable String autho, @RequestParam("groupid") String groupid, 
			@RequestParam("subgroupid") String subgroupid) {
		
		JResponse response = new JResponse();
		
		try {
			User user = userService.verifyAuthKey(autho);
			JNodeOverview noverview = nodeService.getNodeoverview(groupid,subgroupid,user.getId());
			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("nodeoverview", noverview);
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		return response;
	}
	
	// Used in web
	//========Alert Overview================
	@RequestMapping(value="v3.0/alertoverview/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getAlertOverview(@PathVariable String autho, @RequestParam("groupid") String groupid, 
			@RequestParam("subgroupid") String subgroupid)
	{
		JResponse response = new JResponse();
		
		try {
			User user = userService.verifyAuthKey(autho);
			
			JAlertOverview altoverview = alertService.getalertoverview(groupid,subgroupid,user.getId());
			JSreiAlertOverview saltoverview = alertService.getsreialertoverview(groupid,subgroupid,user.getId());
			
			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("gatewayoverview", altoverview);
			response.put("sreialtoverview", saltoverview);
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		
		return response;
		
	}

	// Used in web
	//========Srei Asset Overview================
	@RequestMapping(value="v3.0/sreigatewayoverview/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getSreiGatewayOverview(@PathVariable String autho, @RequestParam("groupid") String groupid, 
			@RequestParam("subgroupid") String subgroupid,@RequestParam("levelid") String levelid)
	{
		JResponse response = new JResponse();

		try {
			User user = userService.verifyAuthKey(autho);

			JSreiGatewayOverview sgoverview 	= gatewayService.getSreiGatewayOverview(groupid,subgroupid,user.getId(),levelid);
			JSreiGatewayOverview sRgoverview 	= gatewayService.getSreiRGatewayOverview(groupid,subgroupid,user.getId(),levelid);
			JSreiGatewayOverview sNRgoverview 	= gatewayService.getSreiNRGatewayOverview(groupid,subgroupid,user.getId(),levelid);
			JSreiGatewayOverview sInbuiltDevice = gatewayService.getSreiInbuiltDevicesOverview(groupid,subgroupid,user.getId(),levelid);

			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("sreigatewayoverview", sgoverview);
			response.put("sreiRgatewayoverview", sRgoverview);
			response.put("sreiNRgatewayoverview", sNRgoverview);
			response.put("sreiInBuiltDevice", sInbuiltDevice);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}

		return response;
	}
	
	//========Alert Overview Version-2================
	@RequestMapping(value="v3.0/alertoverviewV2/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getAlertOverviewV2(@PathVariable String autho, @RequestParam("groupid") String groupid, 
			@RequestParam("subgroupid") String subgroupid)
	{
		JResponse response = new JResponse();

		try {
			User user = userService.verifyAuthKey(autho);

			JAlertOverview altoverview = alertService.getalertoverview(groupid,subgroupid,user.getId());

			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("gatewayoverview", altoverview);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		return response;
	}

	//========Gateway Alert Range ================
	@RequestMapping(value="v3.0/assetalertrange/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getGatewayAlertRange(@PathVariable String autho, @RequestParam("assetid") String assetid)
	{
		JResponse response = new JResponse();

		try {
			User user = userService.verifyAuthKey(autho);

			List<JAlertRange> altrange = alertService.getAlertRange(assetid, user);

			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("assetalertrange", altrange);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		return response;
	}
}
