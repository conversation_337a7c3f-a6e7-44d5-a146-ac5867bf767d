package com.nimble.irisservices.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chargebee.Environment;
import com.chargebee.ListResult;
import com.chargebee.filters.enums.SortOrder;
import com.chargebee.models.Subscription.Status;
import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dto.JATCount;
import com.nimble.irisservices.dto.JAlertRemaining;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyCreditMonitor;
import com.nimble.irisservices.entity.Feature;
import com.nimble.irisservices.entity.FeatureType;
import com.nimble.irisservices.entity.PlanToFeature;
import com.nimble.irisservices.entity.PlanToMonitorType;
import com.nimble.irisservices.entity.PlanToPeriod;
import com.nimble.irisservices.entity.PlanToUpgrade;
import com.nimble.irisservices.entity.ResetType;
import com.nimble.irisservices.entity.SubscriptionPeriod;
import com.nimble.irisservices.entity.SubscriptionPlan;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserTransaction;
import com.nimble.irisservices.entity.UserTransactionHistory;
import com.nimble.irisservices.entity.UsertoFeature;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IThrottlingService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class CreditSystemController {
	private static final Logger log = LogManager.getLogger(CreditSystemController.class);

	@Autowired
	@Lazy
	ICompanyService cmpService;

	@Autowired
	@Lazy
	IThrottlingService thrService;

	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	IUserService userService;

	@Autowired
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	Helper _helper = new Helper();

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Value("${feature_plan_flag}")
	private String feature_plan_flag;

	@Value("${freeplan}")
	private String freeplan;

	@Value("${omitplan}")
	private String omitplan;

	@Value("${vpmplan}")
	private String vpmplan;

	// Used in web
	@RequestMapping(value = "v3.0/createplan/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createPlan(@PathVariable String autho, @RequestBody SubscriptionPlan plan) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			plan.setPlan_name(plan.getPlan_name().trim());
			status = crService.createSubscriptionPlan(plan);

			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "SubscriptionPlan already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createPlan : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "SubscriptionPlan creation failed");
			log.error("createPlan : " + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/listplan/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPlan(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			List<SubscriptionPlan> planList = crService.listSubscriptionPlan();

			response.put("planList", planList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in list");
			response.put("Error", e.getLocalizedMessage());
			log.error("listPlan : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/deleteplan/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deletePlan(@PathVariable String autho, @RequestParam long id) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			status = crService.deleteSubscriptionPlan(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "Error in delete. Invalid plan id");
			response.put("Error", e.getLocalizedMessage());
			log.error("deletePlan : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/createplantomonitor/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createPlanToMonitor(@PathVariable String autho, @RequestBody PlanToMonitorType plan,
			@RequestParam long splanid) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			SubscriptionPlan sp = crService.getSubsPlanById(splanid);
			plan.setPlan_id(sp);
			status = crService.createPlanToMonitorType(plan);

			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "Selected combination already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createPlanToMonitor : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "creation of PlanToMonitor failed");
			response.put("Error", e.getLocalizedMessage());
			log.error("createPlanToMonitor : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/listplantomonitortype/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPlanToMonitorType(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			List<PlanToMonitorType> planMTypeList = crService.listPlanToMonitorType();

			response.put("planMTypeList", planMTypeList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in list");
			response.put("Error", e.getLocalizedMessage());
			log.error("listPlanToMonitorType : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/deleteplantomonitortype/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deletePlanToMonitorType(@PathVariable String autho, @RequestParam long id) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			status = crService.deletePlanToMonitorType(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Invalid Plan to monitor type id");
			log.error("delete plantomonitortype : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/createplantoperiod/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createPlanToPeriod(@PathVariable String autho, @RequestBody PlanToPeriod plan,
			@RequestParam long planid, @RequestParam long periodid) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			SubscriptionPlan sp = crService.getSubsPlanById(planid);
			SubscriptionPeriod period = crService.getSubsPeriodById(periodid);
			plan.setPlan_id(sp);
			plan.setSub_period_id(period);

			status = crService.createPlanToPeriod(plan);

			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "Selected combination already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createPlanToPeriod : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "creation of PlanToPeriod failed");
			response.put("Error", e.getLocalizedMessage());
			log.error("create PlanToPeriod : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/listplantoperiod/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPlanToPeriod(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			List<PlanToPeriod> ptpList = crService.listPlanToPeriod();

			response.put("ptpList", ptpList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured. Unable list plantoperiod");
			response.put("Error", e.getLocalizedMessage());
			log.error("listPlanToPeriod : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/deleteplantoperiod/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deletePlanToPeriod(@PathVariable String autho, @RequestParam long id) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			status = crService.deletePlanToPeriod(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "Error occured in delete .Invalid Plantoperiod id");
			response.put("Error", e.getLocalizedMessage());
			log.error("deleteplantoperiod : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/listsubperiod/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listSubPeriod(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			List<SubscriptionPeriod> periodList = crService.listSubPeriod();

			response.put("periodList", periodList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in subs period list");
			response.put("Error", e.getLocalizedMessage());

			log.error("periodList : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/createplantoupgrade/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createPlanToUpgrade(@PathVariable String autho, @RequestBody PlanToUpgrade plan,
			@RequestParam long ptpid, @RequestParam String upgradeid) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			String[] upgradeidList = upgradeid.split(",");
			PlanToPeriod ptp = crService.viewPlanToPeriod(ptpid);

			for (int i = 0; i < upgradeidList.length; i++) {
				PlanToPeriod upgradeplan = crService.viewPlanToPeriod(Long.parseLong(upgradeidList[i]));

				PlanToUpgrade upgradeObj = new PlanToUpgrade();

				upgradeObj.setPlan_to_period_id(ptp);
				upgradeObj.setUpgradeplan_id(upgradeplan);
				upgradeObj.setPrice(plan.getPrice());
				upgradeObj.setDescription(plan.getDescription());

				status = crService.createPlanToUpgrade(upgradeObj);
			}

			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "Selected combination already exist");
			response.put("Error", ex.getLocalizedMessage());

			log.error("create PlanToUpgrade : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "creation of PlanToUpgrde failed");
			response.put("Error", e.getLocalizedMessage());

			log.error("create PlanToUpgrade : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/listplantoupgrade/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPlanToUpgrade(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			List<PlanToUpgrade> ptuList = crService.listPlanToUpgrade();

			response.put("pupList", ptuList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured. Unable list PlanToUpgrade");
			response.put("Error", e.getLocalizedMessage());

			log.error("list PlanToPeriod : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/deleteplantoupgrade/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deletePlanToUpgrade(@PathVariable String autho, @RequestParam long id) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			status = crService.deletePlanToUpgrade(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "Error occured in delete.Invalid Plan to Upgrade id");
			response.put("Error", e.getLocalizedMessage());
			log.error("delete plantoupgrade : " + e.getLocalizedMessage());
		}

		return response;
	}

	public void updateCreditMonitor(long cmpid, int cr, int excr) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			Company cmpy = cmpService.getCompany(cmpid);
			ThrottlingSettings throtsettings = thrService.getThrotSettingsById(cmpy.getThrotsettings().getId());
			CompanyCreditMonitor ccm = crService.getCompanyCreditMonitorByCmpy(cmpy.getId());

			if (ccm == null) {
				ccm = new CompanyCreditMonitor();
				ccm.setCmp_id(cmpy);
				ccm.setCredit_assigned((int) throtsettings.getCredits());
				ccm.setEx_credit_assigned((int) throtsettings.getExtra_credits());
				ccm.setCredit_spent(0);
				ccm.setEx_credit_spent(0);
				ccm.setResetmonthly(false);

			}
			// add credit spent with existing cr spent and extra credit spent with existing
			// extra cr spent
			int cr_spent = ccm.getCredit_spent() + cr;
			int ex_cr_spent = ccm.getEx_credit_spent() + excr;

			ccm.setCredit_spent(cr_spent);
			ccm.setEx_credit_spent(ex_cr_spent);

			status = crService.upateCredits(ccm);

			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "invalid plantomonitorid");
			log.error("updateCreditMonitor : " + e.getLocalizedMessage());
		}

	}

	// Used in web
	// Plan vs Feature
	@RequestMapping(value = "v3.0/listresettype/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listResetType(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			List<ResetType> resetList = crService.listResetType();

			response.put("resetList", resetList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in reset type list");
			response.put("Error", e.getLocalizedMessage());

			log.error("reset List : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/listfeaturetype/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listFeatureType(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			List<FeatureType> featuretypelist = crService.listFeatureType();

			response.put("featuretypelist", featuretypelist);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in feature type list");
			response.put("Error", e.getLocalizedMessage());

			log.error("periodList : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/createfeature/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createFeature(@PathVariable String autho, @RequestBody Feature featureObj) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			String featurename = featureObj.getFeature_name().trim();
			featurename = featurename.replaceAll("\\s", "");

			FeatureType type = crService.viewFeatureType(featureObj.getFeaturetype());
			featureObj.setType_id(type);
			featureObj.setFeature_name(featurename);

			status = crService.createFeature(featureObj);

			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "Feature already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("create Feature : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "invalid plan id");
			response.put("Error", e.getLocalizedMessage());
			log.error("create feature : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/deletefeature/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deleteFeature(@PathVariable String autho, @RequestParam long id) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			status = crService.deleteFeature(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "invalid featureid");
			log.error("delete Feature : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/listfeature/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listFeature(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			List<Feature> featureList = crService.listFeature();

			response.put("featureList", featureList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in list feature");
			response.put("Error", e.getLocalizedMessage());
			log.error("list Feature : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/createusertofeature/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createUsertoFeature(@PathVariable String autho, @RequestBody List<UsertoFeature> userFeatureList) {
		JResponse response = new JResponse();
		boolean status = false;
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (Exception e) {
				log.error("createusertofeature:user by auth : " + e.getMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid User");
				response.put("Error", e.getLocalizedMessage());
			}

			long userid = user.getId();

			for (int i = 0; i < userFeatureList.size(); i++) {
				ResetType reset = crService.viewResetType(userFeatureList.get(i).getResettype());
				Feature feature = crService.viewFeature(userFeatureList.get(i).getFeatureid());
				UsertoFeature userFeature = null;

				userFeature = crService.getUFByUserFeature(user.getId(), feature.getId());

				if (userFeature == null)
					userFeature = new UsertoFeature();

				userFeature.setFeature_id(feature);
				userFeature.setUser_id(userid);
				userFeature.setResettype_id(reset);
				userFeature.setTxn_limit(userFeatureList.get(i).getTxn_limit());
				userFeature.setExtra_txn_limit(userFeatureList.get(i).getExtra_txn_limit());
				userFeature.setEnable(userFeatureList.get(i).isEnable());
				userFeature.setRemaining_limit(userFeatureList.get(i).getRemaining_limit());
				userFeature.setEnable(userFeatureList.get(i).isEnable());
				userFeature.setLast_reset_on(userFeatureList.get(i).getLast_reset_on());
				userFeature.setFeature_code(userFeatureList.get(i).getFeature_code());
				userFeature.setAddon_limit(userFeatureList.get(i).getAddon_limit());
				userFeature.setUnlimited_cr(userFeatureList.get(i).isUnlimited_cr());
//				userFeature.setMonitortype_id(userFeatureList.get(i).getMonitortype_id());
//				userFeature.setDevice_config(userFeatureList.get(i).getDevice_config());

				status = crService.createUsertoFeature(userFeature);
			}
			response.put("Status", status);
			response.put("Msg", "Success");
		} catch (ConstraintViolationException ex) {
			response.put("Status", 0);
			response.put("Msg", "Selected combination already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createUsertoFeature : " + ex.getLocalizedMessage());
		} catch (Exception e) {

			response.put("Status", status);
			response.put("Msg", "creation of UsertoFeature failed");
			response.put("Error", e.getLocalizedMessage());
			log.error("create UsertoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/listusertofeature/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listUsertoFeature(@PathVariable String autho, @RequestParam long userid) {
		JResponse response = new JResponse();
		try {

			if (userid > 0) {
				List<UsertoFeature> userFeatureList = crService.listUsertoFeature(userid);

				response.put("userFeatureList", userFeatureList);
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not found");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in list UsertoFeature");
			response.put("Error", e.getLocalizedMessage());
			log.error("list UsertoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/deleteusertofeature/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deleteUsertoFeature(@PathVariable String autho, @RequestParam long id) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			status = crService.deleteUsertoFeature(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "Error occured in delete. Invalid UsertoFeature id");
			response.put("Error", e.getLocalizedMessage());
			log.error("delete UsertoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/createplantofeature/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createPlantoFeature(@PathVariable String autho, @RequestBody List<PlanToFeature> planFeatureList) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			if (!planFeatureList.isEmpty()) {
				SubscriptionPlan plan = crService.getSubsPlanById(planFeatureList.get(0).getPlanid());

				for (int i = 0; i < planFeatureList.size(); i++) {
					ResetType reset = crService.viewResetType(planFeatureList.get(i).getResettype());
					Feature feature = crService.viewFeature(planFeatureList.get(i).getFeatureid());

					PlanToFeature planFeature = null;
					planFeature = crService.getPFByPlanFeature(plan.getId(), feature.getId());

					if (planFeature == null)
						planFeature = new PlanToFeature();

					planFeature.setFeature_id(feature);
					planFeature.setPlan_id(plan);
					planFeature.setResettype_id(reset);
					planFeature.setTxn_limit(planFeatureList.get(i).getTxn_limit());
					planFeature.setExtra_txn_limit(planFeatureList.get(i).getExtra_txn_limit());
					planFeature.setEnable(planFeatureList.get(i).isEnable());
					planFeature.setUnlimited_cr(planFeatureList.get(i).isUnlimited_cr());
//					planFeature.setPlan_period_id(planFeatureList.get(i).getPlan_period_id());

					status = crService.createPlantoFeature(planFeature);
				}
			}
			response.put("Status", status);
			response.put("Msg", "Success");

		} catch (ConstraintViolationException ex) {
			response.put("Status", status);
			response.put("Msg", "Selected combination already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("createPlantoFeature : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "creation of PlantoFeature failed");
			response.put("Error", e.getLocalizedMessage());
			log.error("create PlantoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/listplantofeature/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPlantoFeature(@PathVariable String autho, @RequestParam long planid) {
		JResponse response = new JResponse();
		try {
			List<PlanToFeature> ptfList = crService.listPlantoFeature(planid);

			response.put("ptfList", ptfList);
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in list PlantoFeature");
			response.put("Error", e.getLocalizedMessage());
			log.error("list PlantoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/deleteplantofeature/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deletePlantoFeature(@PathVariable String autho, @RequestParam long id) {
		JResponse response = new JResponse();
		boolean status = false;
		try {
			status = crService.deletePlantoFeature(id);

			response.put("Status", status);
			if (status == true)
				response.put("Msg", "Success");
			else
				response.put("Msg", "Selected data not exists");

		} catch (Exception e) {
			response.put("Status", status);
			response.put("Msg", "Error occured in delete .Invalid PlantoFeature id");
			response.put("Error", e.getLocalizedMessage());
			log.error("delete PlantoFeature : " + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/getfeaturebyuser/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFeatureListByUser(@PathVariable String autho) {
//		String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
//		String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);

		User user = null;
		response.put("feature_plan", feature_plan_flag);
		if (feature_plan_flag.equalsIgnoreCase("true")) {
			try {
				try {
					user = userService.verifyAuthKey(autho);
				} catch (Exception e) {
					log.error("get FeatureList :userby auth : " + e.getMessage());
				}

				if (user != null) {
					ListResult result = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
							.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL)
							.sortByUpdatedAt(SortOrder.DESC).request();
					com.chargebee.models.Subscription subscrip = null;
					int ssize = 0;

					if (!result.isEmpty()) {
						ssize = result.size();
						for (ListResult.Entry subs : result) {
							if (ssize == 1 && !omitplan.contains(subs.subscription().planId())) {
								subscrip = subs.subscription();
							} else if (!freeplan.contains(subs.subscription().planId())
									&& !omitplan.contains(subs.subscription().planId())) {
								subscrip = subs.subscription();
								break;
							}
						}
					}

					int plan_id = 1; // chum plan - ptpid
					if (subscrip != null) {
						ArrayList<Integer> ids = crService.getPlanAndPeriod(subscrip.planId().trim());
						plan_id = ids.get(0);
					}

					ArrayList<String> feaList = crService.getFeatureList(user.getId(), plan_id);

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("plan_id", plan_id);
					response.put("featureList", feaList);

				} else {
					response.put("Status", 0);
					response.put("Msg", "Invalid User");
				}

			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Unable to get feature list");
				response.put("Error", e.getLocalizedMessage());
			}
		} else {
			response.put("Status", 0);
			response.put("Msg", "Feature vs Plan not enabled");
		}

		return response;

	}

	@RequestMapping(value = "v3.0/updateusertransaction/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateUserTransaction(@PathVariable String autho, @RequestParam String feature) {
		JResponse response = new JResponse();
		User user = null;
		try {
			try {
				user = userService.verifyAuthKey(autho);
			} catch (Exception e) {
				log.error("updateUserTransaction :user by auth : " + e.getMessage());
			}

			if (user != null) {
				response = updateUserTransaction(user.getId(), feature);
			}

		} catch (Exception e) {
			log.error("update UserTransaction : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to update Transaction & History");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	public JResponse updateUserTransaction(long user_id, String feature) {
		JResponse response = new JResponse();
		try {
			Calendar calendar = Calendar.getInstance();
			Date d = calendar.getTime();

			SimpleDateFormat df = new SimpleDateFormat(IrisservicesConstants.DATEFORMAT);
			df.setTimeZone(TimeZone.getTimeZone(IrisservicesConstants.UTCFORMAT));

			String currentdate = df.format(d);
			boolean status1 = false;
			boolean status2 = false;
			try {
				UserTransaction uTxn = crService.getUserTransactionByUserFeature(user_id, feature);
				if (uTxn != null) {
					uTxn.setNo_txn(uTxn.getNo_txn() + 1);
				} else {
					Feature feat = crService.viewFeatureByname(feature);
					uTxn = new UserTransaction();
					uTxn.setFeature_id(feat);
					uTxn.setLast_reset(d);
					uTxn.setNo_txn(1);
					uTxn.setUser_id(user_id);
				}

				status1 = crService.updateUserTransaction(uTxn);
			} catch (Exception e) {
				log.error("UserTransaction : " + e.getLocalizedMessage());
			}

			try {
				UserTransactionHistory uTxnHis = crService.getUserTransactionHistoryByUserFeature(user_id, feature,
						currentdate);

				if (uTxnHis != null) {
					uTxnHis.setNo_txn(uTxnHis.getNo_txn() + 1);
				} else {
					Feature feat = crService.viewFeatureByname(feature);
					uTxnHis = new UserTransactionHistory();
					uTxnHis.setFeature_id(feat);
					uTxnHis.setTxn_date(d);
					uTxnHis.setNo_txn(1);
					uTxnHis.setUser_id(user_id);
				}
				status2 = crService.updateUserTransactionHistory(uTxnHis);
			} catch (Exception e) {
				log.error("UserTransactionHistory : " + e.getLocalizedMessage());
			}

			if (status1 == true && status2 == true) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Unable to update Transaction & History");
			}
		} catch (Exception e) {
			log.error("UserTransactionHistory : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to update Transaction & History");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v3.0/getfeatureavailability/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFeatureAvailabilty(@PathVariable String autho, @RequestParam long user_id,
			@RequestParam String feature, @RequestParam long plan_id) {
		JResponse response = new JResponse();
		try {
			int availCnt = crService.getUserFeatureAvailabilty(user_id, feature, plan_id);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("availCnt", availCnt);

		} catch (Exception e) {
			// e.printStackTrace();
			log.error("getfeatureavailability : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to update Transaction & History");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	// Used in web -- Savitha
	@RequestMapping(value = "v3.0/listplantoperiodbyplanid/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listPlanToPeriodByPlanId(@PathVariable String autho,
			@RequestParam(value = "plan_id", required = true) long plan_id) {
		log.info("Entered get plan to period by plan id.");
		JResponse response = new JResponse();
		try {
			List<PlanToPeriod> ptpList = crService.listPlanToPeriodByPlanId(plan_id);

			if (ptpList != null) {
				response.put("ptpList", ptpList);
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Unable list plan to period!!!");
				log.error("Error in getting list plan to period by plan id");
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured. Unable list plantoperiod");
			response.put("Error", e.getLocalizedMessage());
			log.error("listPlanToPeriod : " + e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v4.0/getalertslimit", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertsLimit(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver) {
		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		log.info("getalertslimit :" + auth);

		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (Exception e) {
			log.error("getalertslimit:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid User");
			response.put("Error", e.getMessage());
		}

		try {
			if (user != null) {
				StringBuffer alertLimitHtml = new StringBuffer();
				String alertlimit_basedon = _helper.getExternalConfigValue("alertcount", externalConfigService);
				String caution_desc = "80% of Alerts Exhausted";

				ArrayList<JAlertRemaining> alertsCntList = crService.getalertslimit(user.getId(),alertlimit_basedon);

				boolean upgrade_flag = false;
				String upgrade_label = "";
				boolean unlimited_plan = false;
				String alertRemainingHtml = "";
				if (!alertsCntList.isEmpty()) {
					unlimited_plan = alertsCntList.get(0).getAtCountList().get(0).isUnlimited();
					alertLimitHtml.append("<link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css' integrity='sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm' crossorigin='anonymous'"+"> ");
					alertLimitHtml.append("<div class='container-fluid' style='padding-left: 0px; padding-right: 0px;background: #F1F3F4;overflow:hidden;'>");
					
					if (!unlimited_plan) {
						for (JAlertRemaining alertObj : alertsCntList) {
							String alertname = alertObj.getAlertname();

							if (alertname.equalsIgnoreCase("Device Not Reporting"))
								alertname = "Network Alerts";
							else
								alertname =alertname+"s";

							ArrayList<JATCount> jATCountList = alertObj.getAtCountList();

							alertLimitHtml.append("<div class='row'><div class='col-lg-12 col-md-12 col-xs-12 col-12'><p style='padding-top: 5px;font-weight:400;'>"+alertname+"</p>");

							for (JATCount jATC : jATCountList) {
								alertLimitHtml.append("<div class='row'><div class='col-md-6 col-xs-6 col-6'><p style='color:#848484; font-weight:400;'>"+
									jATC.getAt_name() +"</p></div><div class='col-md-6 col-xs-6 col-6'><p style='color:#848484; font-weight:400;float:right;'>"+
									jATC.getRemaining_cnt()+" Alerts</p></div></div>");
								if(jATC.getRemaining_cnt() < 1) {
									caution_desc = "100% of Alerts Exhausted";
								}
								if ((jATC.getTotal_cnt() * 0.25) > jATC.getRemaining_cnt()) {
									upgrade_flag = true;
									upgrade_label = "Unlock Pet Protection";
								}

							}

							alertLimitHtml.append("</div></div>");
						}
						
					} else {
						alertLimitHtml.append("<div class='row'><div class='col-lg-12 col-md-12 col-xs-12 col-12'>");
						
						for (JAlertRemaining alertObj : alertsCntList) {
							String alertname = alertObj.getAlertname();

							if (alertname.equalsIgnoreCase("Device Not Reporting"))
								alertname = "Network Alerts";
							else
								alertname =alertname+"s";

							alertLimitHtml.append("<div class='row'><div class='col-md-6 col-xs-6 col-6'><p style='color:#000; font-weight:400;'>"+ alertname
									+ "</p></div><div class='col-md-6 col-xs-6 col-6'><p style='color:#848484; font-weight:400;float:right;'>Unlimited</p></div></div>");
						}
						
						alertLimitHtml.append("</div></div>");
						if (unlimited_plan)
							upgrade_label = "All Alerts unlimited";

					}
					alertLimitHtml.append("</div>");
					alertRemainingHtml = alertLimitHtml.toString();
				} else {
					alertLimitHtml.append("<center><table" + " style=" + "-webkit-user-select:none;" + ">");
					alertLimitHtml.append("<tr style=" + "height:40px;" + "><td>No Alerts</td></tr>");
					alertLimitHtml.append("</table></center>");
					alertRemainingHtml = alertLimitHtml.toString();
				}

				System.out.println(alertRemainingHtml);
				boolean addon_buynow = (unlimited_plan == true ? false : true);
				
				if(alertsCntList.isEmpty())
					addon_buynow = false;
				
				response.put("addon_buynow", addon_buynow);
				response.put("alerts_limit", alertRemainingHtml);
				response.put("alertsCntList", alertsCntList);
				response.put("upgrade_flag", upgrade_flag);
				response.put("upgrade_label", upgrade_label);
				response.put("unlimited_plan", unlimited_plan);
				response.put("caution_desc", caution_desc);
				response.put("Status", 1);
				response.put("Msg", "Success");

			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			log.error("getalertslimit Excep:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());

		}
		return response;
	}

}
