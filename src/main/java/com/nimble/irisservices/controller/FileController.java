package com.nimble.irisservices.controller;

import java.io.IOException;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IFileStorageService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IPetSpeciesServices;
import com.nimble.irisservices.service.IUserService;

@RestController
public class FileController {

	private static final Logger log = LogManager.getLogger(FileController.class);
	Helper _helper = new Helper();

	private static String runningenvironment = null;

	@Autowired
	@Lazy
	IFileStorageService fileStorageService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;
	
	@Autowired
	@Lazy
	IPetSpeciesServices petService;
	
	@Value("${profileimages_bucketname}")
	private String bucketName;

	@Value("${profileimages_url}")
	private String profileimages_url;

//	@PostMapping("v3.0/uploaduserprofileimage/{autho}")    //add authkey
//	public JResponse uploadUserProfileImage(@PathVariable String autho,@RequestParam("file") MultipartFile mulfile,@RequestParam("userid") long userid,
//			@RequestParam(value = "os", defaultValue = "", required = false) String os,
//			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) throws IOException 
//	{	
//		runningenvironment = _helper.getExternalConfigValue("s3_environment", externalConfigService);
//		if(runningenvironment.equals(null))
//			runningenvironment = "development";
//		
//		JResponse jres = new JResponse();
//		if (mulfile.isEmpty()) {
//			jres.put("Status", 0);
//			jres.put("Msg", "File is empty");
//			return jres;
//		}
//		User user = null;
//		try {
//			user = userService.verifyAuthKey(autho);
//			if(user.getId() != userid) {
//				jres.put("Status", 0);
//				jres.put("Msg", "User mismatch!");
//				return jres;
//			}
//		}catch(InvalidAuthoException e) {
//			log.error("Invalid user!");
//			jres.put("Status", 0);
//			jres.put("Msg", "Invalid user!");
//			return jres;
//		}
//		String originalFileName = StringUtils.cleanPath(mulfile.getOriginalFilename());
//		String imagePath= runningenvironment+"/"+"userprofile/";
//		log.info("imagePath : "+imagePath);
//		try {
//			boolean isStoredInS3 = fileStorageService.uploadImageToS3Bucket(mulfile, imagePath, bucketName);						
//			if(isStoredInS3) {
//				log.info("Image successfully stored in S3! local file deleted!");
//				String resultApi = profileimages_url+bucketName+"/"+imagePath+originalFileName;		
//				log.info(resultApi);
//				user.setImageUrl(resultApi);
//				boolean isSaved = userService.updateUser(user);
//				if(isSaved) {
//					jres.put("Status", 1);
//					jres.put("Msg", "Success");
//					jres.put("Imageurl", resultApi);
//				}
//			}
//			else {
//				log.info("Save image failed!");
//				jres.put("Status", 0);
//				jres.put("Msg", "Failed to save image!");
//			}
//		}catch(Exception e) {
//			log.error("uploadUserProfileImage : "+e.getLocalizedMessage());
//			jres.put("Status", 0);
//			jres.put("Msg", "Error occured!");
//		}
//		return jres;
//	}

	@PostMapping("v3.0/uploadpetprofileimage/{autho}")
	public JResponse uploadPetProfileImage(@PathVariable String autho,@RequestParam("file") MultipartFile mulfile,@RequestParam("petid") long petid,@RequestParam("gatewayid") long gatewayid ,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) throws IOException 
	{
		runningenvironment = _helper.getExternalConfigValue("s3_environment", externalConfigService);
		if(runningenvironment.equals(null))
			runningenvironment = "development";

		JResponse jres = new JResponse();
		if (mulfile.isEmpty()) {
			jres.put("Status", 0);
			jres.put("Msg", "File is empty");
			return jres;
		}
		User user = null;
		try {
			user = userService.verifyAuthKey(autho);
		}catch(InvalidAuthoException e) {
			log.error("Invalid user!");
			jres.put("Status", 0);
			jres.put("Msg", "Invalid user!");
			return jres;
		}
		PetProfile petProfile = null;
		try {
			
			if(gatewayid==0)
			{
				petProfile = petService.getPetprofile("id", String.valueOf(petid)).get(0);
			}else{
				petProfile = gatewayService.getPetProfile(gatewayid);
			}
			
			if(petProfile != null) {
				petProfile.setUser_id(user.getId());
				if(petProfile.getId() != petid) {
					jres.put("Status", 0);
					jres.put("Msg", "Invalid pet profile!");
					return jres;
				}
			}else {
				jres.put("Status", 0);
				jres.put("Msg", "Pet profile not found!");
				return jres;
			}
		}catch(Exception e) {
			jres.put("Status", 0);
			jres.put("Msg", "Invalid pet profile!");
			log.error("uploadPetProfileImage : "+e.getLocalizedMessage());
			return jres;
		}
		String originalFileName = StringUtils.cleanPath(mulfile.getOriginalFilename());
		String imagePath= runningenvironment+"/"+"petprofile/";
		log.info("imagePath : "+imagePath);	
		try {
			boolean isStoredInS3 = fileStorageService.uploadImageToS3Bucket(mulfile, imagePath, bucketName);						
			if(isStoredInS3) {
				log.info("Image successfully stored in S3! local file deleted!");
				String resultApi = profileimages_url+bucketName+"/"+imagePath+originalFileName;		
				log.info(resultApi);
				try {
					petProfile.setImageurl(resultApi);
					//boolean isPetProfileUpdated = gatewayService.updatePetProfile(petProfile);
					boolean isPetProfileUpdated = gatewayService.updateProfileImgPath(petid, resultApi);
					if(isPetProfileUpdated) {
						jres.put("Status", 1);
						jres.put("Msg", "Success");
						jres.put("Imageurl", resultApi);
					}
				}catch(Exception e) {
					log.error("uploadPetProfileImage : "+e.getLocalizedMessage());
				}
			}else {
				log.info("Save image failed!");
				jres.put("Status", 0);
				jres.put("Msg", "Failed to save image!");
			}
		}catch(Exception e) {
			jres.put("Status", 0);
			jres.put("Msg", "Error occured!");
			log.error("uploadPetProfileImage : "+e.getLocalizedMessage());
		}
		return jres;
	}	
}
