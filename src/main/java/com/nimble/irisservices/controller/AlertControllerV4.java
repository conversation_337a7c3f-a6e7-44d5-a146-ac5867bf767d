package com.nimble.irisservices.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JAlertCount;
import com.nimble.irisservices.dto.JAlertV4;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IAlertServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;		

@Controller
public class AlertControllerV4 {

	private static final Logger log = LogManager.getLogger(AlertControllerV4.class);

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	IAlertServiceV4 alertServiceV4;

	//kalai
	@RequestMapping(value="v4.0/alertV3/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getAlertV3_V4(@PathVariable String autho,@RequestParam("monitortype") String monitortype ,
			@RequestParam(value="sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver){
		JResponse response = new JResponse();
		log.info("Entering getAlertV3_V4 : "+autho);
		try
		{								
			Map<String, String> resMaps = new HashMap<String, String>();

			try {
				resMaps = userServiceV4.getUserId_cmpIdByAuth(autho);	

			}catch(InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("getAlertV3_V4 : Exception : "+autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if(resMaps != null || !resMaps.isEmpty() ) 
			{
				long userId = Long.valueOf(resMaps.get("user_id"));
				String tempUnit = resMaps.get("tempunit");

				List<JAlertV4> alerts = userServiceV4.getUnackAlerts(userId,tempUnit,monitortype);					

				response.put("Status", 1);
				response.put("Msg","Success");
				response.put("alerts", alerts);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		} 
		catch (Exception e) {
			log.error("getAlertV3_V4 : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg"," Exception occured ");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	

	@RequestMapping(value = "v4.0/getalertcount/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertCount(@PathVariable String autho,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		log.info("Entering getalertcount : " + autho);
		try {
			JAlertCount alertCntObj = alertServiceV4.getAlertCnt();
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("alertcount", alertCntObj);
		} catch (Exception e) {
			log.error("getalertcount : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", " Exception occured ");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

}
