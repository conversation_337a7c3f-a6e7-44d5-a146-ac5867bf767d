package com.nimble.irisservices.controller;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import com.nimble.irisservices.exception.InvalidAuthoException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.chargebee.Environment;
import com.chargebee.ListResult;
import com.chargebee.Result;
import com.chargebee.filters.enums.SortOrder;
import com.chargebee.models.Coupon;
import com.chargebee.models.Coupon.DiscountType;
import com.chargebee.models.Coupon.PlanConstraint;
import com.chargebee.models.CreditNote;
import com.chargebee.models.Customer;
import com.chargebee.models.HostedPage;
import com.chargebee.models.HostedPage.CheckoutExistingRequest;
import com.chargebee.models.HostedPage.CheckoutNewRequest;
import com.chargebee.models.PaymentSource;
import com.chargebee.models.Plan;
import com.chargebee.models.Subscription;
import com.chargebee.models.Subscription.CreateForCustomerRequest;
import com.chargebee.models.Subscription.Status;
import com.chargebee.models.Subscription.UpdateRequest;
import com.chargebee.models.enums.CreditOptionForCurrentTermCharges;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dto.JFeatureCredit;
import com.nimble.irisservices.dto.JGatewaySubSetup;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSubscriptionPlanReport;
import com.nimble.irisservices.dto.JVpmSubscription;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.OrderMappingDetails;
import com.nimble.irisservices.entity.SubscriptionPlan;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IRVCentricDetailsService;
import com.nimble.irisservices.service.IReferAndEarnService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class ChargebeeController {
	@Autowired
	IUserService userService;

	@Autowired
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;
	
	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	@Lazy
	IReferAndEarnService refService;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	IRVCentricDetailsService rvcentricServ;
	
	@Autowired
	@Lazy
	IChargebeeService cbService;

	private static final Logger log = LogManager.getLogger(ChargebeeController.class);
	Helper _helper = new Helper();

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportcontactnumber;

	@Value("#{${supportemail}}")
	private Map<String,String> supportemail;
	
	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;
	
	@Value("${show_nextrenewal_popup}")
	private boolean show_nextrenewal_popup;
	
	@Value("${days_tohandle_nextrenewal}")
	private int days_tohandle_nextrenewal;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Value("${redirtPetUrl}")
	private String redirtPetUrl;

	@Value("${redirtFurbitUrl}")
	private String redirtFurbitUrl;

	@Value("${redirtVpmUrl}")
	private String redirtVpmUrl;

	@Value("${ordersuccessurl}")
	private String ordersuccessurl;

	@Value("${orderfailedurl}")
	private String orderfailedurl;

	@Value("${embedv2}")
	private boolean embedv2;

	@Value("${embedupdate}")
	private boolean embedupdate;

	@Value("${referralurl}")
	private String referralurl;

	@Value("${customerdelete}")
	private String customerdelete;

	@Value("${paymentupdateurl}")
	private String paymentupdateurl;

	@Value("${chargebee.addonid}")
	private String activationAddonId;
	
	@Value("${chargebee.updateaddonid}")
	private String updateAddonId;
	
	@Value("${chargebee.reactivationid}")
	private String reactivateAddonId;

	@Value("${checkoutversion}")
	private String checkoutversion;

	@Value("${amazonlaunchpadfreetrialenable}")
	private boolean amazonLaunchPadFreeTrialEnable;

	@Value("${enable_ios_inapp_purchase}")
	private boolean enable_ios_inapp_purchase;
	
	@Value("${freeplan}")
	private String freeplan;
	
	@Value("${omitplan}")
	private String omitplan;
	
	@Value("${vpmplan}")
	private String vpmplan;
	
	@Value("${addonplan}")
	private String addonplan;

	@Value("${show_upgrade_msg}")
	private boolean show_upgrade_msg;

	@Value("${show_addon_button}")
	private boolean show_addon_button;

	@Value("${validation_authkey}")
	private String validation_authkey;

	@RequestMapping(value = "v5.0/getcurrentsubscriptionplanV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCurrentSubscriptionPlanV5(@RequestHeader HttpHeaders header,
			@RequestParam("os") String os,	@RequestParam("app_ver") String app_ver,
			@RequestParam("backing") String backing,@RequestParam("plan_ver") String plan_ver) {
		
		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("start : " + start + " getcurrentsubscriptionplan: " + auth);
		try {
			
//			Helper _helper = new Helper();
			log.info("backing key : "+backing);
			
//			AES aes = new AES();
//			if (backing != null) {
//				if (!backing.equals("MT")) {
//					String[] credential = _helper.decodeInternalKey(backing);
//					String finalOut = aes.decode(credential[0], credential[1]);
//					
//					if (finalOut == null) {
//						response.put("Status", 0);
//						response.put("Msg", "Authentication Error");
//						return response;
//					}
//					log.info("AES decryption success : "+backing+" : "+finalOut);
//				}
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Authentication Error");
//				return response;
//			}
			
			user = userServiceV4.verifyAuthV3("authkey", auth);

		} catch (Exception e) {
			log.error("getCurrentSubscriptionPlan: user by id : " + e.getMessage());
		}

		try {
			if (user != null) {
				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;
//					boolean show_alertlimit = false;
//					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
//
//					if (verObj != null) {
//						inapp_redirect = verObj.getInapp_redirect();
//						cb_checkout = verObj.isCb_checkout();
//					}
//
//					if(!user.getPlan_ver().equalsIgnoreCase("V1"))
//						show_alertlimit = true;
//					
//					if (user.getInapp_purchase() == 1) {
						//response = getSubscriptionV2(user, inapp_redirect);
						response = cbService.getSubscriptionFromCB(user, inapp_redirect, os, app_ver, "-08:00",false, 0L);
						
						response.put("show_addon", show_addon_button);
//						response.put("show_alertlimit", show_alertlimit);
						response.put("cb_checkout", cb_checkout);
//					} else {
//						response.put("Status", 0);
//						response.put("Msg", "Subscription Details not found");
//
//					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Please try again.");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		return response;
	}

	@RequestMapping(value = "v3.0/getcurrentsubscriptionplan/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCurrentSubscriptionPlan(@PathVariable String auth, @RequestParam("userid") long userid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "backing", defaultValue = "MT", required = false) String backing) {
		JResponse response = new JResponse();
		User user = null;
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("start : " + start + " getcurrentsubscriptionplan: " + auth);
		try {
			
//			Helper _helper = new Helper();
			log.info("backing key : "+backing);
			
//			AES aes = new AES();
//			if (backing != null) {
//				if (!backing.equals("MT")) {
//					String[] credential = _helper.decodeInternalKey(backing);
//					String finalOut = aes.decode(credential[0], credential[1]);
//					
//					if (finalOut == null) {
//						response.put("Status", 0);
//						response.put("Msg", "Authentication Error");
//						return response;
//					}
//					log.info("AES decryption success : "+backing+" : "+finalOut);
//				}
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Authentication Error");
//				return response;
//			}
			
			user = userService.getUserById(userid);

		} catch (Exception e) {
			log.error("getCurrentSubscriptionPlan: user by id : " + e.getMessage());
		}

		try {
			if (user == null)
				user = userService.verifyAuthKey(auth);
		} catch (Exception e) {
			log.error("getCurrentSubscriptionPlan:userby auth : " + e.getMessage());
		}

		try {
			if (user != null) {
				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;
//					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
//
//					if (verObj != null) {
//						inapp_redirect = verObj.getInapp_redirect();
//						cb_checkout = verObj.isCb_checkout();
//					}
//
//					if (user.getInapp_purchase() == 1) {
						response = getSubscription(user, inapp_redirect);
						response.put("cb_checkout", cb_checkout);
//					} else if (user.getInapp_purchase() == 2) {
//						response = getIosSubscription(user, inapp_redirect);
//						response.put("cb_checkout", cb_checkout);
//					} else {
//						response.put("Status", 0);
//						response.put("Msg", "Subscription Details not found");

//					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		return response;
	}

	@RequestMapping(value = "v4.0/getcurrentsubscriptionplan/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCurrentSubscriptionPlan(@PathVariable String auth, @RequestParam("userid") long userid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		UserV4 user = null;
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("start : "+start + " getcurrentsubscriptionplan: "+auth);
		
		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);			
			
		} catch (Exception e) {
			log.error("getCurrentSubscriptionPlan:userby auth : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		
		try {	
			if (user != null) {
				if(user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout= true;
					
					response = cbService.getSubscriptionFromDB(user,inapp_redirect);
					response.put("cb_checkout", cb_checkout);
					
//					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
//					
//					if(verObj != null) {
//						inapp_redirect = verObj.getInapp_redirect();
//						cb_checkout= verObj.isCb_checkout();
//					}
//					
//					if(user.getInapp_purchase()==1) {
//						response = cbService.getSubscriptionV2(user,inapp_redirect);
//						response.put("cb_checkout", cb_checkout);
//					}
//					else if(user.getInapp_purchase()==2) {
//						response = cbService.getIosSubscriptionV2(user,inapp_redirect);
//						response.put("cb_checkout", cb_checkout);
//					}
//					else {
//						response.put("Status", 0);
//						response.put("Msg", "Subscription Details not found");
//	
//					}
				}
				else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while getting Subscription plan");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : "+end);
		return response;
	}
	
//	@RequestMapping(value = "v3.0/getavailableupgradeplan/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getAvailableUpgradeSubscriptionPlan(@PathVariable String auth, @RequestParam("planid") long planid,
//			@RequestParam("period") long period,
//			@RequestParam(value = "os", defaultValue = "", required = false) String os,
//			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
//		JResponse response = new JResponse();
//		Map<String, Object> resp = new HashMap<String, Object>();
//		// User user = null;
//		UserV4 user = null;
//		try {
//			user = userServiceV4.verifyAuthV3("authkey", auth);
//			// user = userService.verifyAuthKey(auth);
//		} catch (Exception e) {
//			log.error("get AvailableUpgrade:userblock : " + e.getMessage());
//			response.put("Status", 0);
//			response.put("Msg", e.getMessage());
//		}
//
//		try {
//			if (user != null) {
//				if (user.getInapp_purchase() == 1) {
//					String free_trial = "NA";
//					String note = "NA";
//
//					if(show_upgrade_msg && user.getPlan_ver().equalsIgnoreCase("V1"))
//					{
//						String country = user.getCountry().toUpperCase();
//						if(country.equalsIgnoreCase("US")|| country.equalsIgnoreCase("NA")
//								|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")
//								|| country.isEmpty() || country == null) {
//							country = "US";
//						}
////						response.put("Status", 0);
////						response.put("Msg", "Please upgrade your App to purchase the subscription ");
//						HashMap<String, Object> plan = new HashMap<String, Object>();
//						List<HashMap<String, Object>> planlist = new ArrayList<HashMap<String, Object>>();
//
//						plan = new HashMap<String, Object>();
//						plan.put("planname", "Please update your App to purchase the Subscription");
//						plan.put("contactnumber", supportcontactnumber.get(country));
//						plan.put("email", supportemail.get(country));
//						resp.put("Status", 1);
//						resp.put("Msg", "Success");
//						resp.put("plan", planlist);
//						resp.put("defaultplan", plan);
//						resp.put("free_trial", free_trial);
//						resp.put("note", note);
//						response.setResponse(resp);
//						return response;
//					}
//						
//					resp = crService.getAvailUpgradePlans(planid, period, user.getId(),"US");
//					// resp = crService.getAvailUpgradePlanV4(planid, period,user.getId(),0);
//					response.setResponse(resp);
//
//					// checking for free subscription
//					int days = userService.getRemainingDays(user.getId());
//					if (days > 0) {
//						free_trial = String.valueOf(days).concat(" Days");
//						note = "* Please buy a subscription to proceed further. You will be billed only after the FREE subscription period ends and "
//								+ " you can choose to cancel anytime before the FREE subscription ends.";
//					}
//					response.put("free_trial", free_trial);
//					response.put("note", note);
//					
//
//				} else if (user.getInapp_purchase() == 2) {
//					resp = crService.getIosAvailUpgradePlan(planid, period, user.getId(), 0,"US");
//					response.setResponse(resp);
//
//					// checking for free subscription
//					int days = userService.getRemainingDays(user.getId());
//					String free_trial = "NA";
//					String note = "NA";
////					if(days>0) {
////						free_trial = String.valueOf(days).concat( " Days");
////						note = "* Please buy a subscription to proceed further. You will be billed only after the FREE subscription period ends and "
////								+ " you can choose to cancel anytime before the FREE subscription ends.";
////					}
//					response.put("free_trial", free_trial);
//					response.put("note", note);
//				} else {
//					response.put("Status", 0);
//					response.put("Msg", "Upgrade list not found");
//				}
//
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "User not exists");
//			}
//
//		} catch (Exception e) {
//			log.error("get AvailableUpgrade plans:" + e.getMessage());
//			response.put("Status", 0);
//			response.put("Msg", "Please try again later.");
//
//		}
//		return response;
//	}
//
//	@RequestMapping(value = "v3.0/getsubsplanbymonitortype/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getSubscriptionPlanByMonitortype(@PathVariable String auth, @RequestParam("userid") long userid,
//			@RequestParam("monitortype") long monitortype) {
//
//		JResponse response = new JResponse();
//		Map<String, Object> resp = new HashMap<String, Object>();
//
//		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
////		String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
////		String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);
//
//		try {
//			User user = userService.getUserById(userid);
//			if (user == null)
//				user = userService.verifyAuthKey(auth);
//			if (user != null) {
//
//				ListResult result = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
//						.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL).request();
//				Subscription subscription = null;
//
//				if (result.isEmpty()) {
////					log.info("CB sub_create : getSubscriptionPlanByMonitortype : userid : "+ user.getId());
////					subscription = createDefaultSubsPlan(user);
////					result = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
////							.status().in(Status.ACTIVE,Status.NON_RENEWING,Status.IN_TRIAL).request();
//				} else {
//					int ssize = result.size();
//					for (ListResult.Entry subs : result) {
//
//						if (ssize == 1 && !omitplan.contains(subs.subscription().planId())) {
//							subscription = subs.subscription();
//						} else if (!freeplan.contains(subs.subscription().planId())
//								&& !omitplan.contains(subs.subscription().planId())) {
//							subscription = subs.subscription();
//						}
//					}
//				}
//
//				int planid = 1;
//				int period = 1;
//				if (subscription != null) {
//					ArrayList<Integer> ids = crService.getPlanAndPeriod(subscription.planId());
//					if (!ids.isEmpty()) {
//						planid = ids.get(0);
//						period = ids.get(1);
//					}
//				}
//
//				resp = crService.getAvailUpgradePlanNew(planid, period, user.getId(), monitortype,"US");
//				response.setResponse(resp);
//
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "User not found");
//			}
//		} catch (Exception e) {
//			log.error("getPlanByMonitorType : ", e.getLocalizedMessage());
//			response.put("Status", 0);
//			response.put("Msg", "Error occured, while getting subscription");
//		}
//		return response;
//	}

	@RequestMapping(value = "v4.0/getavailableupgradeplan/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAvailableUpgradeSubscriptionPlanV4(@PathVariable String auth,
			@RequestParam("planid") long planid, @RequestParam("period") long period) {
		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();
		User user = null;
		try {
			user = userService.verifyAuthKey(auth);
		} catch (Exception e) {
			log.error("get AvailableUpgrade:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		try {
			if (user != null) {
				resp = crService.getAvailUpgradePlanV4(planid, period, user.getId(), 0,"US");
				response.setResponse(resp);

				// checking for free subscription
				int days = userService.getRemainingDays(user.getId());
				String free_trial = "NA";
				String note = "NA";
				if (days > 0) {
					free_trial = String.valueOf(days).concat(" Days");
					note = "* Please buy a subscription to proceed further. You will be billed only after the FREE subscription period ends and "
							+ " you can choose to cancel anytime before the FREE subscription ends.";
				}
				response.put("free_trial", free_trial);
				response.put("note", note);

			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			log.error("get AvailableUpgrade plans:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());

		}
		return response;
	}

	@RequestMapping(value = "v4.0/getsubsplanbymonitortype/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSubscriptionPlanByMonitortypeV4(@PathVariable String auth, @RequestParam("userid") long userid,
			@RequestParam("monitortype") long monitortype) {

		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();

		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
//		String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
//		String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);

		try {
			User user = userService.getUserById(userid);
			if (user == null)
				user = userService.verifyAuthKey(auth);
			if (user != null) {

				ListResult result = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
						.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL).request();
				Subscription subscription = null;

				if (result.isEmpty()) {
//					log.info("CB sub_create : getSubscriptionPlanByMonitortypeV4 : userid : "+ user.getId());
//					subscription = createDefaultSubsPlan(user);
				} else {
					int ssize = result.size();
					for (ListResult.Entry subs : result) {

						if (ssize == 1 && !omitplan.contains(subs.subscription().planId())) {
							subscription = subs.subscription();
						} else if (!freeplan.contains(subs.subscription().planId())
								&& !omitplan.contains(subs.subscription().planId())) {
							subscription = subs.subscription();
						}
					}
				}

				int planid = 1;
				int period = 1;
				if (subscription != null) {
					ArrayList<Integer> ids = crService.getPlanAndPeriod(subscription.planId());
					if (!ids.isEmpty()) {
						planid = ids.get(0);
						period = ids.get(1);
					}
				}

				resp = crService.getAvailUpgradePlanV4(planid, period, user.getId(), monitortype,"US");
				response.setResponse(resp);

			} else {
				response.put("Status", 0);
				response.put("Msg", "User not found");
			}
		} catch (Exception e) {
			log.error("getPlanByMonitorType : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error occured, while getting subscription");
		}
		return response;
	}

	/*@RequestMapping(value = "v3.0/configuredevice/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse configureDevice(@PathVariable String auth, @RequestParam("userid") long userid,
			@RequestParam("monitortype") long monitortype) {
		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();

		response.setResponse(resp);
		response.put("allowAddDevice", true);
		response.put("description", "Continue to Add Monitor");
		response.put("Status", 1);
		response.put("Msg", "Success");

// 		This feature currently disabled for amazon launchpad		
//		
//		try {
//			User user = userService.verifyAuthKey(auth);
//			if (user == null)
//				user = userService.getUserById(userid);
//
//			if (user != null) {
//				Environment.configure(chargebeeSiteName, chargebeeSiteKey);
//				ArrayList<String> config = null;
//				String mType = null;
//				String[] dConfig = null;
//				String[] devCount = null;
//				int maxDevCnt = 0;
//				boolean allowAddDevice = false;
//				String description = "Please buy a subscription to Add Monitor";
//				HashMap<String, Long> existDevice = new HashMap<String, Long>();
//				HashMap<String, Long> newDevice = new HashMap<String, Long>();
//				
//				String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
//				String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);
//
//				// if count 0 means no device for this user
//				if (user.getChargebeeid().equalsIgnoreCase("NA"))
//					user = createOrupdateChargebeeid(user);
//
//				ListResult sresult = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
//						.status().in(Status.ACTIVE,Status.NON_RENEWING,Status.IN_TRIAL).request();
//
//				com.chargebee.models.Subscription subscription = null;
//				int ssize = 0;
//
//				if (!sresult.isEmpty()) {
//					ssize = sresult.size();
//					for (ListResult.Entry subs : sresult) {
//						log.info(subs.subscription().planId());
//						if (ssize == 1 && !omitplan.contains(subs.subscription().planId())) {
//							subscription = subs.subscription();
//						} else if (!freeplan.contains(subs.subscription().planId())
//								&& !omitplan.contains(subs.subscription().planId())) {
//							subscription = subs.subscription();
//						}
//					}
//					
//					if(subscription == null) {
//						for (ListResult.Entry subs : sresult) {
//							if (freeplan.contains(subs.subscription().planId())) {
//								subscription = subs.subscription();
//								break;
//							}
//						}
//					}
//
//					if (sresult.isEmpty() || subscription == null) {
//						subscription = createDefaultSubsPlan(user);
//					}
//					
//					ArrayList<Integer> ids = crService.getPlanAndPeriod(subscription.planId());
//
//					if (!ids.isEmpty()) {
//						config = crService.getDeviceConfig(ids.get(0));
//						mType = config.get(0);
//						dConfig = config.get(1).split(",");
//						maxDevCnt = Integer.parseInt(config.get(2));
//					}
//				}
//
//				long cnt = crService.getDeviceCountByUser(userid);
//
//				if (cnt < maxDevCnt) {
//
//					int size = 0;
//					int devicecount = 0;
//					StringBuffer monitortypeid = new StringBuffer();
//
//					List result = crService.getDeviceCountByMonitorType(userid, monitortype);
//
//					if (!result.isEmpty()) {
//						size = result.size();
//						long count = 0;
//						String type = "";
//						
//						if (size == 1) {
//							Object[] tuple = (Object[]) result.get(0);
//							type = ((BigInteger) tuple[0]).toString();
//							monitortypeid.append(type);
//							count = ((BigInteger) tuple[1]).longValue();
//							devicecount = (int) count;
//
//							existDevice.put(type, count);
//						} else {
//
//							for (int i = 0; i < size; i++) {
//								Object[] tuple = (Object[]) result.get(i);
//								type = ((BigInteger) tuple[0]).toString();
//								long tempcount = ((BigInteger) tuple[1]).longValue();
//
//								devicecount = devicecount + (int) tempcount;
//								monitortypeid.append(type);
//
//								existDevice.put(type, tempcount);
//
//								if (tempcount > count)
//									count = tempcount;
//
//								monitortypeid.append(",");
//							}
//
//							monitortypeid.deleteCharAt(monitortypeid.length() - 1);
//						}
//
//						if (mType.contains(String.valueOf(monitortype))) {
//							long existCnt = existDevice.get(String.valueOf(monitortype));
//							long curCnt = 0;
//							// if dConfig contains 0 means can add any type upto no_cnt value
//							if (dConfig.length == 1 && (dConfig[0].equalsIgnoreCase("0"))) {
//
//								if (maxDevCnt > devicecount) {
//									allowAddDevice = true;
//									description = "Continue to Add Monitor";
//								}
//							} else {
//								// for (int i = 0; i < dConfig.length; i++) {
//								devCount = dConfig[0].split("/");
//								newDevice.put("1", Long.parseLong(devCount[0].trim()));
//								newDevice.put("2", Long.parseLong(devCount[1].trim()));
//
//								curCnt = newDevice.get(String.valueOf(monitortype));
//
//								if (curCnt > existCnt) {
//									allowAddDevice = true;
//									description = "Continue to Add Monitor";
//								}
//							}
//
//						} else {
//							allowAddDevice = false;
//							description = "Please buy a subscription to Add Monitor";
//						}
//					} else {
//						if (mType.contains(String.valueOf(monitortype))) {
//							allowAddDevice = true;
//							description = "Continue to Add Monitor";
//						} else {
//							allowAddDevice = false;
//							description = "Please buy a subscription to Add Monitor";
//						}
//
//					}
//				}
//				response.setResponse(resp);
//				response.put("allowAddDevice", allowAddDevice);
//				response.put("description", description);
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "User not exists");
//			}
//
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Error occured in configure Monitor ");
//			log.error("configure Monitor outer try block:" + e.getMessage());
//		}
		return response;
	}*/

	public JResponse getSubscription(User user, int inapp_redirect) {
//		String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
//		String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);
//		String vpmplan = _helper.getExternalConfigValue("vpmplan", externalConfigService);

		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		try {
			String cbId = "NA";
			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
				ListResult rs = null;
				int i =1;
				Loop: while ( rs == null || i<=3) {
					i=i+1;
					Thread.sleep(2000);
					rs = Customer.list().email().is(user.getEmail()).sortByCreatedAt(SortOrder.ASC).request();
					
					if(!rs.isEmpty()) {
						for (ListResult.Entry entry : rs) {
							Customer customer = entry.customer();
							user.setChargebeeid(customer.id());
						}
						break Loop;
					}
				}
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}
			}
			com.chargebee.models.Subscription subscrip = null;
			com.chargebee.models.Subscription vpmSubscrip = null;
			boolean isPaidPlan = false;
			boolean vpm_addon = false;
			boolean payment_due = false;
			if (!user.getChargebeeid().equalsIgnoreCase("NA")) {
				ListResult result = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
						.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL)
						.sortByUpdatedAt(SortOrder.DESC).request();
				int ssize = 0;

				if (!result.isEmpty()) {
					ssize = result.size();
					for (ListResult.Entry subs : result) {
						if (ssize == 1 && !omitplan.contains(subs.subscription().planId())
								&& !vpmplan.contains(subs.subscription().planId())
								&& !addonplan.contains(subs.subscription().planId())) {
							subscrip = subs.subscription();

							if (!freeplan.contains(subs.subscription().planId())
									&& !vpmplan.contains(subs.subscription().planId())
									&& !addonplan.contains(subs.subscription().planId()))
								isPaidPlan = true;

						} else if (!freeplan.contains(subs.subscription().planId())
								&& !omitplan.contains(subs.subscription().planId())
								&& !vpmplan.contains(subs.subscription().planId())
								&& !addonplan.contains(subs.subscription().planId())) {
							subscrip = subs.subscription();
							isPaidPlan = true;
							
//							List<Addon> addonList = subscrip.addons();
//							for(Addon addon : addonList) {
//								if(addon.id().contains("vetchat")) {
//									vpm_addon = true;
//									log.info(subs.subscription().planId() + ": vpm included with plan");
//								}
//							}
							break;
						}
					}

					if (subscrip == null) {
						for (ListResult.Entry subs : result) {
							if (freeplan.contains(subs.subscription().planId())) {
								subscrip = subs.subscription();
								break;
							}
						}
					}
//					if (vpmSubscrip == null) {
//						for (ListResult.Entry subs : result) {
//							if (vpmplan.contains(subs.subscription().planId())) {
//								vpmSubscrip = subs.subscription();
//								break;
//							}
//						}
//					}
				}
				if ((result.isEmpty() || subscrip == null) && (inapp_redirect != 2)) {
					// ios inapp user.here checking is there any CB subscription available
					log.info("CB sub_create : getSubscription : userid : " + user.getId());
					subscrip = cbService.createDefaultSubsPlan(user.getChargebeeid());
				}
			}
			// initialize values
			JVpmSubscription vpmSubs = new JVpmSubscription();
			String strprice1 = "NA";
			int availCnt = 0;
			int totalCnt = 0;
			int usedCnt = 0;

			String billingPeriod = "NA";
			String periodUnit = "MONTH";
			String planid = "chum";
			String planname = "Chum";
			String availCredit = "0";
			int period = 0;

			float price = (float) 0.0;
			String strprice = "$0.0";
			String status = "ACTIVE";
			int daysBetween = -1;
			int days_remaining = -1;
			String startedAt = "NA";
			String nextPaymentDate = "NA";
			String createDate = "NA";
			String updateDate = "NA";
			String cbSubId = "NA";
			String cbSubStatus = "NA";
			boolean setupAutoRenewal = false;
			String autoRenewalStatus = "NA";
			boolean cancel = false;
			boolean vetchat_cancel = false;
			String cbvet_cancelId = "";
			String cbvet_planId ="";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date dateobj = new Date();
			Timestamp nextpaymentTS;
			boolean cb_vetchat =false;

			int iris_splan = 1;
			int iris_speriod = 1;
			String desc = "Free Plan";
			JSubscriptionPlanReport rpt = new JSubscriptionPlanReport();
			boolean vpm_enable = false;// companyService.getVetCallStaus(user.giveCompany().getId());
			int substatus_code = 0;

			if (subscrip != null || vpmSubscrip != null) {
				if (subscrip != null) {
					CreditNote cr = null;
					ListResult crLst = CreditNote.list().customerId().is(user.getChargebeeid()).request();
					cr = crLst.isEmpty() ? null : crLst.get(0).creditNote();
					ListResult planRes = Plan.list().id().is(subscrip.planId()).request();
					availCredit = (cr != null) ? String.valueOf(cr.amountAvailable() / 100.0) : "0";

					for (ListResult.Entry planR : planRes) {
						Plan plan = planR.plan();
						period = plan.period();
						periodUnit = plan.periodUnit().name();
						planid = plan.id();
						planname = plan.name();

						if (periodUnit.equalsIgnoreCase("YEAR")) {
							if (period == 2)
								billingPeriod = "2 Year";
							else if (period >= 5)
								billingPeriod = planname;
							else
								billingPeriod = "Yearly";
						} else if (periodUnit.equalsIgnoreCase("MONTH")) {
							if (period == 3)
								billingPeriod = "Quarterly";
							else if (period == 6)
								billingPeriod = "Half-Yearly";
							else if (period == 12)
								billingPeriod = "Yearly";
							else if (period == 24)
								billingPeriod = "2 Year";
							else
								billingPeriod = "Monthly";
						} else if (periodUnit.equalsIgnoreCase("DAY")) {
							billingPeriod = "Daily";
						} else if (periodUnit.equalsIgnoreCase("WEEK")) {
							billingPeriod = "Weekly";
						}

						price = (float) subscrip.planUnitPrice() / 100;
						strprice = "$" + String.valueOf(price);
						status = subscrip.status().name();

						if ((status.equalsIgnoreCase("ACTIVE")) || (status.equalsIgnoreCase("IN_TRIAL"))) {
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							if (status.equalsIgnoreCase("IN_TRIAL")) {
								cancel = true;
							}
							substatus_code = 1;
							try {
								nextpaymentTS = subscrip.nextBillingAt();

								if (nextpaymentTS == null)
									nextpaymentTS = subscrip.currentTermEnd();

							} catch (Exception ex) {
								nextpaymentTS = subscrip.currentTermEnd();
							}
							nextPaymentDate = sdf.format(nextpaymentTS.getTime());

							Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
							Date todayDate = sdf.parse(sdf.format(dateobj));

							long difference = nextPaymentDate1.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							days_remaining = daysBetween;

							sdf = new SimpleDateFormat("yyyy-MM-dd");
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							nextPaymentDate = sdf.format(nextpaymentTS.getTime());
							// status = "ACTIVE";
							autoRenewalStatus = "Enabled";

							if (freeplan.contains(subscrip.planId())) {
								nextPaymentDate = "NA";
								days_remaining = -1;
								autoRenewalStatus = "NA";
								billingPeriod = "NA";
							}

						} else if (status.equalsIgnoreCase("NON_RENEWING")) {
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							status = "ACTIVE";
							nextPaymentDate = sdf.format(subscrip.cancelledAt().getTime());
							Date cancelledAt = sdf.parse(nextPaymentDate);
							Date todayDate = sdf.parse(sdf.format(dateobj));

							long difference = cancelledAt.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
							days_remaining = daysBetween;

							sdf = new SimpleDateFormat("yyyy-MM-dd");
							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
							nextPaymentDate = sdf.format(subscrip.cancelledAt().getTime());
							substatus_code = 1;
							autoRenewalStatus = "Disabled";
						}

						if (daysBetween < 0)
							days_remaining = -1;

						ArrayList<Integer> ids = crService.getPlanAndPeriod(planid);
						boolean alert_setting = true;
						if (!ids.isEmpty()) {
							iris_splan = ids.get(0);
							iris_speriod = ids.get(1);

							SubscriptionPlan splan = crService.getSubsPlanById(ids.get(0));
							desc = splan.getDescription();
							planname = splan.getPlan_name();
							alert_setting = splan.isAlert_setting();
						}

						// safety
						try {
							sdf = new SimpleDateFormat("yyyy-MM-dd");
							createDate = sdf.format(subscrip.createdAt().getTime());
							updateDate = sdf.format(subscrip.updatedAt().getTime());
							startedAt = sdf.format(subscrip.startedAt().getTime());
							cbSubId = subscrip.id();
							cbSubStatus = subscrip.status().name();
						} catch (Exception e) {
							log.error("subs dates: ", e.getLocalizedMessage());
						}

						List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(String.valueOf(iris_splan),
								user.getId(), days_remaining);

						rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
								days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus,
								createDate, updateDate, cbSubId, cbSubStatus, availCredit, alert_setting,
								user.getChargebeeid(), startedAt, planid, String.valueOf(iris_speriod), cancel,
								substatus_code,payment_due);
					}
				} else {
					List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(String.valueOf(iris_splan), user.getId(),
							days_remaining);

					rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
							days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
							updateDate, cbSubId, cbSubStatus, availCredit, true, user.getChargebeeid(), startedAt, "",
							String.valueOf(iris_speriod), cancel, substatus_code,payment_due);
				}
				int vpmstatus_code = 0;
				String str_total_cnt = "0";
				float price1 = 0;
//				if (vpmSubscrip != null) {
//					price1 = (float) vpmSubscrip.planUnitPrice() / 100;
//					if (vpmSubscrip.planId().equalsIgnoreCase("vet-chat")) {
//						strprice1 = "$" + String.valueOf(price1);
//						nextPaymentDate = "NA";
//						autoRenewalStatus = "Disabled";
//						billingPeriod = "NA";
//						vpmstatus_code = 1;
//					} else {
//						sdf = new SimpleDateFormat("yyyy-MM-dd");
//						sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//						cb_vetchat = true;
//
//						if (status.equalsIgnoreCase("ACTIVE") || (status.equalsIgnoreCase("IN_TRIAL"))) {
//							if (status.equalsIgnoreCase("IN_TRIAL")) {
//								vetchat_cancel = true;
//								cbvet_cancelId = vpmSubscrip.id();
//								cbvet_planId = vpmSubscrip.planId();
//							}
//							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//							try {
//								autoRenewalStatus = "Enabled";
//								nextpaymentTS = vpmSubscrip.nextBillingAt();
//
//								if (nextpaymentTS == null)
//									nextpaymentTS = vpmSubscrip.currentTermEnd();
//
//							} catch (Exception ex) {
//								nextpaymentTS = vpmSubscrip.currentTermEnd();
//							}
//
//							nextPaymentDate = sdf.format(nextpaymentTS.getTime());
//
//							Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
//							Date todayDate = sdf.parse(sdf.format(dateobj));
//
//							long difference = nextPaymentDate1.getTime() - todayDate.getTime();
//							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
//							days_remaining = daysBetween;
//							autoRenewalStatus = "Enabled";
//							vpmstatus_code = 1;
//
//						} else if (status.equalsIgnoreCase("NON_RENEWING")) {
//							autoRenewalStatus = "Disabled";
//							status = "ACTIVE";
//							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//							nextPaymentDate = sdf.format(vpmSubscrip.cancelledAt().getTime());
//							Date cancelledAt = sdf.parse(nextPaymentDate);
//							Date todayDate = sdf.parse(sdf.format(dateobj));
//
//							long difference = cancelledAt.getTime() - todayDate.getTime();
//							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
//							days_remaining = daysBetween;
//							vpmstatus_code = 1;
//							autoRenewalStatus = "Disabled";
//						} else {
//							status = "INACTIVE";
//							vpmstatus_code = 0;
//							strprice1 = "$0.0";
//						}
//						billingPeriod = "Monthly";
//						strprice1 = "$" + String.valueOf(price1);
//						str_total_cnt = "Unlimited";
//					}
//
//					ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0,
//							vpmSubscrip.planId());
//
//					if (!vpmlist.isEmpty()) {
//						availCnt = vpmlist.get(0);
//						totalCnt = vpmlist.get(1);
//						usedCnt = totalCnt - availCnt;
//
//						if (availCnt == 0) {
//							status = "INACTIVE";
//							vpmstatus_code = 0;
//							strprice1 = "$0.0";
//							nextPaymentDate = "NA";
//							billingPeriod = "NA";
//							str_total_cnt = "0";
//						}
//						if (vpmSubscrip.planId().equalsIgnoreCase("vet-chat") && totalCnt > 0)
//							str_total_cnt = totalCnt + "";
//					}
//
//					vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
//							str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod,vetchat_cancel,cbvet_cancelId,cbvet_planId,cb_vetchat);
//				} else {

//					if (vpm_enable) {
//						ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0, "");
//						availCnt = vpmlist.get(0);
//						totalCnt = vpmlist.get(1);
//						usedCnt = totalCnt - availCnt;
//						status = "ACTIVE";
//						vpmstatus_code = 1;
//						str_total_cnt = availCnt + "";
//						if(vpm_addon)
//							str_total_cnt = "Unlimited";
//						if (availCnt == 0) {
//							status = "INACTIVE";
//							vpmstatus_code = 0;
//							strprice1 = "$0.0";
//							nextPaymentDate = "NA";
//							billingPeriod = "NA";
//							str_total_cnt = "0";
//						}
//					} else {
						availCnt = 0;
						totalCnt = 0;
						usedCnt = 0;
						status = "INACTIVE";
						vpmstatus_code = 0;
						strprice1 = "$0.0";
						nextPaymentDate = "NA";
						billingPeriod = "NA";
						str_total_cnt = "0";
//					}
					autoRenewalStatus = "Disabled";
					nextPaymentDate = "NA";
					billingPeriod = "NA";
					strprice1 = "$0.0";
					vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
							str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod,vetchat_cancel,cbvet_cancelId,cbvet_planId,cb_vetchat);
//				}

			} else {

				List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(String.valueOf(iris_splan), user.getId(),
						days_remaining);

				rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
						days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
						updateDate, cbSubId, cbSubStatus, availCredit, true, user.getChargebeeid(), startedAt, "",
						String.valueOf(iris_speriod), cancel, substatus_code,payment_due);

				availCnt = 0;
				totalCnt = 0;
				usedCnt = 0;
				status = "INACTIVE";
				autoRenewalStatus = "Disabled";
				nextPaymentDate = "NA";
				int vpmstatus_code = 0;
				String str_total_cnt = "0";
				billingPeriod = "NA";
				strprice1 = "0";

				vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
						str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod,vetchat_cancel,cbvet_cancelId,cbvet_planId,cb_vetchat);

			}

			boolean is_upgrade = true;
			int redirect = user.getInapp_purchase(); // default 1
			String upgrade_msg = "Success";

			if (isPaidPlan == true && (inapp_redirect == 2 || user.getInapp_purchase() == 2)) {
				inapp_redirect = 1;
				redirect = inapp_redirect;
				user.setInapp_purchase(inapp_redirect);
				// call user update method
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());

			} else if ((isPaidPlan == false && inapp_redirect == 2 && user.getInapp_purchase() == 1)
					|| (isPaidPlan == false && inapp_redirect == 1 && user.getInapp_purchase() == 2)) {
				// update CB purchase status to 2[inapp] in user table
				user.setInapp_purchase(inapp_redirect);
				redirect = inapp_redirect;
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
			}

			redirect = user.getInapp_purchase();

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("planid", iris_splan);
			response.put("periodid", iris_speriod);
			response.put("subscriptionplan", rpt);
			response.put("vpm_enable", vpm_enable);
			response.put("vpmsubs", vpmSubs);

			response.put("is_upgrade", is_upgrade);
			response.put("redirect_inapp", redirect);
			response.put("upgrade_msg", upgrade_msg);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while getting Subscription plan");
			log.error("getSubscription:" + e.getMessage());
		}

		return response;
	}

	public JResponse getIosSubscription(User user, int inapp_redirect) {
		//String vpmplan = _helper.getExternalConfigValue("vpmplan", externalConfigService);

		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		try {

			// initialize values
			JVpmSubscription vpmSubs = new JVpmSubscription();
			String strprice1 = "NA";
			int availCnt = 0;
			int totalCnt = 0;
			int usedCnt = 0;

			int iris_splan = 1;
			int iris_speriod = 1;
			boolean vpm_enable = companyService.getVetCallStaus(user.giveCompany().getId());

			JSubscriptionPlanReport rpt = crService.getInappSubscriptionByUser(user.getId());

			if (rpt != null) {

				iris_splan = Integer.valueOf(rpt.getPlanid());
				iris_speriod = Integer.valueOf(rpt.getPeriodid());

				List<JGatewaySubSetup> setupList = checkDeviceConfigStatus(rpt.getPlanid(), user.getId(),
						rpt.getDays_remaining());
				rpt.setListJGatewaySubSetup(setupList);

			} else {
				response = getSubscription(user, inapp_redirect);
				return response;
			}

			if (vpm_enable) {
				ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0,"NA");
				availCnt = vpmlist.get(0);
				totalCnt = vpmlist.get(1);
				usedCnt = totalCnt - availCnt;
			}
			vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, "NA", "NA", "NA", 0,"NA","NA",false,"","",false);

			boolean is_upgrade = true;
			int redirect = user.getInapp_purchase();
			String upgrade_msg = "Success";

			if (iris_splan != 1 && user.getInapp_purchase() == 2 && inapp_redirect == 1) {
				is_upgrade = false;
				redirect = user.getInapp_purchase();
				upgrade_msg = " You have purchased in App Store. Pls cancel subscription in app store and then purchase in android ";
			} else if (iris_splan == 1 && user.getInapp_purchase() == 2 && inapp_redirect == 1) {
				user.setInapp_purchase(inapp_redirect);
				redirect = inapp_redirect;
				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("planid", iris_splan);
			response.put("periodid", iris_speriod);
			response.put("subscriptionplan", rpt);
			response.put("vpm_enable", vpm_enable);
			response.put("vpmsubs", vpmSubs);
			response.put("is_upgrade", is_upgrade);
			response.put("redirect_inapp", redirect);
			response.put("upgrade_msg", upgrade_msg);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while getting Subscription plan");
			log.error("getSubscription:" + e.getMessage());
		}

		return response;
	}

	public List<JGatewaySubSetup> checkDeviceConfigStatus(String planid, long userid, int days_remaining) {
		List<JGatewaySubSetup> setupList = new ArrayList<JGatewaySubSetup>();
		try {
			ArrayList<String> config = null;
			String mType = null;
			String[] dConfig = null;
			String[] devCount = null;
			int maxDevCnt = 0;
			boolean setupActivate = false;
			HashMap<String, Long> newDevice = new HashMap<String, Long>();
			List<Gateway> gateways = gatewayService.getGatewayByUser("", "", "", "", userid, "");

			long cnt = 0;
			long curCnt = 0;
			long add_device_cnt = 0;
			int remaindays = -1;
			String showNextRenewal_withContent = "";

			if (!gateways.isEmpty()) {
				cnt = gateways.size();
			}

			// ArrayList<Integer> ids = crService.getPlanAndPeriod(planid);

//			if (!planid.isEmpty()) {
//				config = crService.getDeviceConfig(Long.parseLong(planid));
//				mType = config.get(0);
//				dConfig = config.get(1).split(",");
//				maxDevCnt = Integer.parseInt(config.get(2));
//
//				if (dConfig[0].length() > 1) {
//					devCount = dConfig[0].split("/");
//
//					newDevice.put("1", Long.parseLong(devCount[0])); // key - monitortype , value - device count
//					newDevice.put("2", Long.parseLong(devCount[1]));
//				}
//			}
			
			// Reading device count from plan table
			if (!planid.isEmpty()) {
				mType = "1";
				dConfig = new String[1];
				dConfig[0] = "0";
				
				maxDevCnt = crService.getMaxDeviceCount(Long.parseLong(planid));
			}

			LinkedHashMap<String, Gateway> maplastGateway = crService.getGatewaysByReportTime(userid, 1);
			//LinkedHashMap<String, Gateway> mapfurbitlastGateway = crService.getGatewaysByReportTime(userid, 2);

			//maplastGateway.putAll(mapfurbitlastGateway);

			for (Gateway gateway : gateways) {
				maplastGateway.put(gateway.getId() + "", gateway);
			}

			gateways = new ArrayList<Gateway>(maplastGateway.values());

			for (Gateway gateway : gateways) {
				if(gateway.getModel().getMonitor_type().getId() == 1) {
					if (!planid.isEmpty()) {
						if (Long.parseLong(planid) == 1) {
							setupActivate = true;
						} else {
							String gmonitor = String.valueOf(gateway.getModel().getMonitor_type().getId());

							if (mType.contains(gmonitor)) {
								// if dConfig contains 0 means can add any type upto no_cnt value
								if (dConfig[0].length() == 1 && dConfig[0].equalsIgnoreCase("0")) {

									if (maxDevCnt > add_device_cnt) {
										setupActivate = false;
										add_device_cnt = add_device_cnt + 1;
										remaindays = days_remaining;
									} else {
										setupActivate = true;
										remaindays = -1;
									}

								} else {
									curCnt = newDevice.get(gmonitor);

									if (curCnt >= 1) {
										newDevice.put(gmonitor, curCnt - 1);
										add_device_cnt = add_device_cnt + 1;
										setupActivate = false;
										remaindays = days_remaining;
									} else {
										setupActivate = true;
										remaindays = -1;
									}
								}
							} else {
								setupActivate = true;
								remaindays = -1;
							}
						}
					} else {
						setupActivate = true;
						remaindays = -1;
					}

					if ((remaindays < days_tohandle_nextrenewal) && (remaindays > 0) && show_nextrenewal_popup) {
						showNextRenewal_withContent = "Subscription renews in " + remaindays + " days";
					}

					JGatewaySubSetup setup = new JGatewaySubSetup(gateway.getId(), setupActivate, remaindays,
							gateway.getMeid(), "NA");

					setupList.add(setup);
				}
			}
		}catch (Exception e) {
			log.error("checkDeviceConfigStatus:" + e.getMessage());
		}
		return setupList;
	}


	@RequestMapping(value = "v3.0/chargebeepurchase", headers = "Accept=application/json", method = RequestMethod.POST)
	@ResponseBody
	public JResponse chargebeePurchase(@RequestParam("subPlanId") String subPlanId,
			@RequestParam("sQuantity") String sQuantity, @RequestParam("addonId") String addonId,
			@RequestParam("aQuantity") String aQuantity,
			@RequestParam(value = "source", defaultValue = "", required = false) String source,
			@RequestParam(value = "referrerid", defaultValue = "", required = false) String referrerid,
			@RequestParam(value = "purchasefrom", defaultValue = "", required = false) String purchasefrom,
			@RequestParam(value = "enableFreeTrial", defaultValue = "false", required = false) boolean enableFreeTrial) {
		JResponse response = new JResponse();

		try {

			Environment.configure(chargebeeSiteName, chargebeeSiteKey);

			Result result = null;
			HostedPage hostedPage = null;
			int freeTrialDays = 0;

			int sQuantityList = Integer.parseInt(sQuantity);
			List<String> addonIdList = Arrays.asList(addonId.split(","));
			List<String> aQuantityList = Arrays.asList(aQuantity.split(","));

			// this for customer merge
			HashMap<String, Object> metaData = new HashMap<String, Object>();
			String cancel_subid = "NA";
			
			String[] planDetails =crService.getCouponId(subPlanId);
			String cb_couponid = planDetails[0];
			String cb_addonid = planDetails[1];
			metaData.put("regarding", "WebPurchase");
			metaData.put("chargebeeid", "NA");
			metaData.put("referrerid", referrerid);
			metaData.put("source", source);
			com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);

			int qSize = addonIdList.size();

			CheckoutNewRequest CheckoutNewRequest = HostedPage.checkoutNew().subscriptionPlanId(subPlanId)
					.subscriptionPlanQuantity(sQuantityList);

			if (enableFreeTrial) {
				freeTrialDays = crService.getPlanFreeTrialPeriodByName(subPlanId);
				if (freeTrialDays > 0) {
					Timestamp trialEndDate = new Timestamp(
							Instant.now().plus(freeTrialDays, ChronoUnit.DAYS).toEpochMilli());
					CheckoutNewRequest.subscriptionTrialEnd(trialEndDate);
				}
			}

			if(!cb_couponid.equalsIgnoreCase("NA"))
				CheckoutNewRequest.subscriptionCoupon(cb_couponid);
			
			if (!addonId.isEmpty()) {

				for (int j = 0; j < qSize; j++) {
					if (addonIdList.get(j).toString().equalsIgnoreCase(activationAddonId)) {
						CheckoutNewRequest = CheckoutNewRequest.addonId(j, addonIdList.get(j).toString())
								.addonQuantity(j, Integer.valueOf(aQuantityList.get(j))).addonBillingCycles(0, 1);
					} else {
						CheckoutNewRequest = CheckoutNewRequest.addonId(j, addonIdList.get(j).toString())
								.addonQuantity(j, Integer.valueOf(aQuantityList.get(j)));
					}
				}
			}
			
			if(!cb_addonid.equalsIgnoreCase("NA")) {
				List<String> cbaddonIdList = Arrays.asList(cb_addonid.split(","));
				int i = qSize;
				for (int j = 0; j < cbaddonIdList.size(); j++) {
					CheckoutNewRequest.addonId(i, cbaddonIdList.get(j)).addonQuantity(i, 1).addonBillingCycles(i, 1);
					i = i+1;
				}
			}
			
			if (purchasefrom.equalsIgnoreCase("furbitsite"))
				result = CheckoutNewRequest.redirectUrl(redirtFurbitUrl).embed(embedv2)
						.passThruContent(jsonObj.toString()).request();
			else if (purchasefrom.equalsIgnoreCase("vpmsite"))
				result = CheckoutNewRequest.redirectUrl(redirtVpmUrl).embed(embedv2).passThruContent(jsonObj.toString())
						.request();
			else
				result = CheckoutNewRequest.redirectUrl(redirtPetUrl).embed(embedv2).passThruContent(jsonObj.toString())
						.request();
			hostedPage = result.hostedPage();
			response.put("checkOutURL", hostedPage.url());
			response.put("Status", 1);
			response.put("Msg", "Success");

		} catch (Exception e) {
			log.error("chargebee purchase: " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "UnExcepted Error while purchasing. Please contact our support team.");
		}

		return response;
	}

	@RequestMapping(value = "v3.0/updatesubscriptionplan/{auth}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateSubscriptionPlan(@PathVariable String auth, @RequestParam("userid") long userid,
			@RequestParam("planid") long planid, @RequestParam("periodid") long periodid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);

		try {
			User user = userService.getUserById(userid);

			if (user != null) {
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					String cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}
				
//				String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
//				String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);
//				String vpmplan = _helper.getExternalConfigValue("vpmplan", externalConfigService);

				boolean disableUpgrade = false;
				String country = user.getCountry();
				country = country.equalsIgnoreCase("CA") ? "CA" : "US";

				String[] cbPlanAndTrialPeriod = crService.getChargebeePlanById(planid, periodid, country);

				String cb_plan = cbPlanAndTrialPeriod[0];
				int freeTrialPeriod = Integer.parseInt(cbPlanAndTrialPeriod[1]);
				
				String cb_coupon_id = cbPlanAndTrialPeriod[2];
				String cb_addon_id = cbPlanAndTrialPeriod[3];
				List<String> addonIdList = Arrays.asList(cb_addon_id.split(","));
				
				com.chargebee.models.Subscription subscription = null;
				int size = 0;

				if (!cb_plan.equalsIgnoreCase("NA")) {
					ListResult result = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
							.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL).request();
					if (!result.isEmpty())
						size = result.size();

					for (ListResult.Entry subs : result) {
						if (size == 1 && !omitplan.contains(subs.subscription().planId())
								&& !vpmplan.contains(subs.subscription().planId())
								&& !addonplan.contains(subs.subscription().planId())) {
							subscription = subs.subscription();
						} else if (!freeplan.contains(subs.subscription().planId())
								&& !omitplan.contains(subs.subscription().planId())
								&& !vpmplan.contains(subs.subscription().planId())
								&& !addonplan.contains(subs.subscription().planId())) {

							subscription = subs.subscription();
						}
					}

					if (subscription == null) {
						for (ListResult.Entry subs : result) {
							if (freeplan.contains(subs.subscription().planId())) {
								subscription = subs.subscription();
							}
						}
					}

					if (subscription != null && freeplan.contains(subscription.planId())) {
						disableUpgrade = true;
					}

					int days = 0;
					int daysBetween = 0;
					OrderMappingDetails order = null;
					Timestamp trialEnd = null;

					// this for customer merge
					HashMap<String, Object> metaData = new HashMap<String, Object>();
					metaData.put("userid", user.getId());
					metaData.put("chargebeeid", user.getChargebeeid());
					String cancel_subid = "NA";
					String order_id = "NA";
					String orderchannel = "RV";
					CheckoutNewRequest checkoutNewRequest;
					CheckoutExistingRequest checkoutExitingRequest;
					Result res;

					if (amazonLaunchPadFreeTrialEnable) {
						order = userService.getOrderMappingByUser(user.getId());
					}
					if (order != null) {
						orderchannel = order.getOrderchannel();
						days = (int) userService.getCreditAmountBySKU(order.getExternalsku());

						if (days > 0) {
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							Date todayDate = sdf.parse(sdf.format(new Date()));

							Timestamp orderdate = order.getOrderdate();
							log.info("orderdate: " + orderdate);
							Date expirydate = new Date(orderdate.getTime());

							Calendar cal = Calendar.getInstance();
							cal.setTime(expirydate);
							cal.add(Calendar.DATE, days);

							expirydate = cal.getTime();

							long difference = expirydate.getTime() - todayDate.getTime();
							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));

							if (daysBetween > 0) {
								String trialendDt = sdf.format(expirydate);
								// tialendDt =
								// tialendDt.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.ENDTIME);
								trialEnd = Timestamp.valueOf(trialendDt);
							} else
								daysBetween = 0;
						}
					}
					// String addonId = "setup_charge";
					if (daysBetween > 0) {

						if (subscription != null)
							cancel_subid = subscription.id();

						order_id = String.valueOf(order.getId());

						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
						
						checkoutNewRequest = HostedPage.checkoutNew().subscriptionTrialEnd(trialEnd)
								.subscriptionPlanId(cb_plan).subscriptionPlanQuantity(1).addonId(0, activationAddonId)
								.addonQuantity(0, 1).addonBillingCycles(0, 1).customerFirstName(user.getFirstname())
								.customerLastName(user.getLastname()).customerEmail(user.getEmail())
								.customerPhone(user.getMobileno());

						if(!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutNewRequest.subscriptionCoupon(cb_coupon_id);
						}
						
						if(!cb_addon_id.equalsIgnoreCase("NA")) {
							int i =0;
							// index - 0 updateAddonId/activation addon. remaining addon starts with 1
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j+1;
								checkoutNewRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1).addonBillingCycles(i, 1);
							}
						}
						res = checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
								.embed(embedupdate).request();

					} else if (subscription != null && !disableUpgrade) {
						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
						checkoutExitingRequest = HostedPage.checkoutExisting().subscriptionId(subscription.id())
							.subscriptionPlanId(cb_plan).addonId(0, updateAddonId).addonQuantity(0, 1)
							.addonBillingCycles(0, 1).replaceAddonList(true).redirectUrl(redirtPetUrl)
							.passThruContent(jsonObj.toString()).embed(embedupdate).forceTermReset(true);
						
						if(!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutExitingRequest.subscriptionCoupon(cb_coupon_id);
						}
						
						if(!cb_addon_id.equalsIgnoreCase("NA")) {
							int i =0;
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j+1;
								checkoutExitingRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1).addonBillingCycles(i, 1);
							}
						}
						
						res = checkoutExitingRequest.request();
						
					} else if (subscription != null && disableUpgrade && freeTrialPeriod == 0) {
						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
						
						checkoutExitingRequest = HostedPage.checkoutExisting().subscriptionId(subscription.id())
							.subscriptionPlanId(cb_plan).addonId(0, activationAddonId).addonQuantity(0, 1)
							.addonBillingCycles(0, 1).replaceAddonList(true).redirectUrl(redirtPetUrl)
							.passThruContent(jsonObj.toString()).embed(embedupdate).forceTermReset(true);
						
						if(!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutExitingRequest.subscriptionCoupon(cb_coupon_id);
						}
						
						if(!cb_addon_id.equalsIgnoreCase("NA")) {
							int i =0;
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j+1;
								checkoutExitingRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1).addonBillingCycles(i, 1);
							}
						}
						
						res = checkoutExitingRequest.request();
						
					} else {
						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);

//						checkoutNewRequest = HostedPage.checkoutNew().subscriptionPlanId(cb_plan).subscriptionPlanQuantity(1).addonId(0,activationAddonId).addonQuantity(0, 1).addonBillingCycles(0, 1)
//							.customerFirstName(user.getFirstname())
//							.customerLastName(user.getLastname()).customerEmail(user.getEmail()).customerPhone(user.getMobileno());

						// In CB - v3 we can create multiple subscription for a single user

						checkoutNewRequest = HostedPage.checkoutNew().subscriptionPlanId(cb_plan)
								.subscriptionPlanQuantity(1).addonId(0, activationAddonId).addonQuantity(0, 1)
								.addonBillingCycles(0, 1).customerId(user.getChargebeeid());

						if (freeTrialPeriod > 0) {
							Timestamp trialEndDate = new Timestamp(
									Instant.now().plus(freeTrialPeriod, ChronoUnit.DAYS).toEpochMilli());
							checkoutNewRequest.subscriptionTrialEnd(trialEndDate);
						}
						
						if(!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutNewRequest.subscriptionCoupon(cb_coupon_id);
						}

						if(!cb_addon_id.equalsIgnoreCase("NA")) {
							int i =0;
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j+1;
								checkoutNewRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1).addonBillingCycles(i, 1);
							}
						}
						
						res = checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
								.embed(embedupdate).request();

					}

					HostedPage hostedPage = res.hostedPage();

					response.put("checkOutURL", hostedPage.url());
					response.put("Status", 1);
					response.put("Msg", "Success");

				} else {
					response.put("Status", 0);
					response.put("Msg", "chargebee plan id not available in DB");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "user details not found");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Failed to update subscription");
			log.error("update subscriptionplan:" + e.getMessage());
		}
		return response;
	}

	@RequestMapping(value = "v3.0/updatehostedpagdetails", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateHostedPageDetails(@RequestParam("hostedpageid") String hostedpageid) {
		JResponse response = new JResponse();
		try {
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);

			HostedPage hpDetail = HostedPage.retrieve(hostedpageid).request().hostedPage();
			Subscription subscription = hpDetail.content().subscription();
			String plan_id = subscription.planId();
			Customer customer = hpDetail.content().customer();
			String pass_content = hpDetail.passThruContent();
			String old_chargebeeid = "NA";
			HashMap<String, Object> metaData = new HashMap<String, Object>();
			com.chargebee.org.json.JSONObject jsonCust = null;
			com.chargebee.org.json.JSONObject jsonSub =  new com.chargebee.org.json.JSONObject();;

			String plan_ver = crService.getPlanVersion(plan_id);
			User user;

			log.info("hosted page detail : iris-service : " + hostedpageid + " : "+ pass_content);

			try {
				user = userService.getUserByUNameOrEmail(customer.email());
				old_chargebeeid = user.getChargebeeid();
			} catch (Exception ex) {
				user = null;
				log.error("updatehostedpagdetails: user not exists in db" + customer.email());
			}

			try {
				if (pass_content != null) {
					JSONObject json = new JSONObject(pass_content);
					String regarding = json.getString("regarding");

					if (old_chargebeeid.equalsIgnoreCase("NA"))
						old_chargebeeid = json.getString("chargebeeid");

					Customer old_customer = null;

					if (!old_chargebeeid.equalsIgnoreCase("NA")) {

						old_customer = Customer.retrieve(old_chargebeeid).request().customer();
						jsonCust = old_customer.metaData();
					}

					if (jsonCust == null)
						jsonCust = new com.chargebee.org.json.JSONObject();

					if (regarding.equalsIgnoreCase("WebPurchase")) {
						String src = json.getString("source") != null ? json.getString("source") : "";
						String referrerid = json.getString("referrerid") != null ? json.getString("referrerid") : "NA";
						metaData.put("regarding", regarding);
						metaData.put("referrerid", referrerid);
						metaData.put("source", src);
						metaData.put("plan_detail", subscription.planId() + "$" + subscription.startedAt());
						jsonCust.put(customer.id(), metaData);
						jsonSub.put(customer.id(), metaData);

						if (user == null) {
							String phno = (customer.phone()).replaceAll("[^\\d.]", "");
							phno = phno.substring(phno.length() - 10, phno.length());
							String addr = "NA";
							String city = "NA";
							String state = "NA";
							String country = "NA";

							if (customer.billingAddress() != null) {
								addr = customer.billingAddress().line1();
								city = customer.billingAddress().city();
								state = customer.billingAddress().state();
								country = customer.billingAddress().country();

								if (customer.billingAddress().country().equalsIgnoreCase("United States")
										|| customer.billingAddress().country().equalsIgnoreCase("US")) {
									phno = "1-" + phno;
								}
							}

							String email = customer.email().replaceAll("\\s", "").toLowerCase();
							async.createChargebeeUser(customer.firstName(), customer.lastName(), email, phno,
									customer.id(), false, addr, city, state, country);
							log.info("new user in db" + customer.email() + " CBid:" + customer.id());

							response.put("Msg", " WebPurchase - Hosted Page Details updated");
							response.put("Status", 1);
						}

					} else {
						metaData.put("regarding", regarding);
						metaData.put("userid", json.getLong("userid"));
						metaData.put("chargebeeid", old_chargebeeid);
						metaData.put("cancel_subid", json.getString("cancel_subid"));
						metaData.put("order_id", json.getString("order_id"));
						metaData.put("plan_detail", subscription.planId() + "$" + subscription.updatedAt());
						String metaKey = customer.id();

						metalabel: for (int i = 1; i < 999; i++) {
							if (!jsonCust.has(metaKey))
								break metalabel;
							else {
								metaKey = customer.id() + "-" + String.valueOf(i);
							}
						} // end of outer loop

						jsonCust.put(metaKey, metaData);
						jsonSub.put(customer.id(), metaData);

						response.put("Msg", " Hosted Page Details updated : " + regarding);
						response.put("Status", 1);
					}
					if (user != null) {
						if (user.getChargebeeid().equalsIgnoreCase("NA")) {
							user.setChargebeeid(customer.id());
							user.setPlan_ver(plan_ver);
							userService.updateUser(user);
							log.info("chargebeeid updated in user table : " + customer.id());
						} else {
							old_chargebeeid = user.getChargebeeid();
						}

//						UserRvDetails rvObj = userServiceV4.getUserRvDetails(user.getId());
//						boolean rvStatus = (rvObj != null) ? true : false;
//						int devicecount = gatewayService.getDeviceCount(user.getId());

//						boolean stat1 = rvcentricServ.saveUserBadgeTxn(user.getId(), plan_id, devicecount, rvStatus,
//								old_chargebeeid);
//						log.info("in updated hostedpage:" + user.getId() + " Badge created:" + stat1);

					}

					if (!(customer.id().equalsIgnoreCase(old_chargebeeid))
							&& !(old_chargebeeid.equalsIgnoreCase("NA"))) {
						String fromCustId = customer.id();
						Customer mergeCustomer = Customer.merge().fromCustomerId(fromCustId)
								.toCustomerId(old_chargebeeid).request().customer(); // customer = result.customer(); //

						log.info("customer merged : " + regarding + " :from : " + fromCustId + " To : "
								+ old_chargebeeid);
						response.put("Msg", "customer merged : " + regarding + " :from : " + customer.id() + " To : "
								+ old_chargebeeid);

					}

					// update meta data in subscription
					// TODO: have to check ip address
					if (jsonCust != null && !(old_chargebeeid.equalsIgnoreCase("NA"))) {
						log.info("Meta Data updated in customer : " + customer.id());
						Customer.update(old_chargebeeid).metaData(jsonCust).request();
						Subscription.update(subscription.id()).metaData(jsonSub).request();
					}
					
//					if(user != null && !plan_ver.isEmpty() && plan_ver.equalsIgnoreCase("V2") && 
//							user.getPlan_ver().equalsIgnoreCase("V1")) {
//						user.setPlan_ver(plan_ver);
//						userService.updateUser(user);
//					}
					
					response.put("Msg", "Success");
					response.put("Status", 1);
				} else {
					response.put("Msg", "Pass thru content not available");
					response.put("Status", 0);
				}

			} catch (JSONException e) {
				log.error("HostedPage Details : jsondata: ", e.getLocalizedMessage());
				response.put("Msg", "Pass thru content parsing error");
				response.put("error", e.getLocalizedMessage());
				response.put("Status", 0);
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted error in hosted page details");
			response.put("Error", e.getLocalizedMessage());
			log.error("update HostedPage Details :" + e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v4.0/configuredevice/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse configureDeviceV4(@PathVariable String auth, @RequestParam("userid") long userid,
			@RequestParam("monitortype") long monitortype) {
		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();

		try {
			User user = null;
			user = userService.verifyAuthKey(auth);
			if (user == null)
				user = userService.getUserById(userid);

			if (user != null) {
				Environment.configure(chargebeeSiteName, chargebeeSiteKey);
				ArrayList<String> config = null;
				String mType = null;
				String[] dConfig = null;
				String[] devCount = null;
				int maxDevCnt = 0;
				boolean allowAddDevice = false;
				String description = "Please buy a subscription to Add Monitor";
				HashMap<String, Long> existDevice = new HashMap<String, Long>();
				HashMap<String, Long> newDevice = new HashMap<String, Long>();

//				String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
//				String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);

				// if count 0 means no device for this user
				String cbID = "NA";
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					cbID = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbID);
				}

				ListResult sresult = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
						.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL).request();

				com.chargebee.models.Subscription subscription = null;
				int ssize = 0;

				if (!sresult.isEmpty()) {
					ssize = sresult.size();
					for (ListResult.Entry subs : sresult) {
						log.info(subs.subscription().planId());
						if (ssize == 1 && !omitplan.contains(subs.subscription().planId())) {
							subscription = subs.subscription();
						} else if (!freeplan.contains(subs.subscription().planId())
								&& !omitplan.contains(subs.subscription().planId())) {
							subscription = subs.subscription();
						}
					}

					if (subscription == null) {
						for (ListResult.Entry subs : sresult) {
							if (freeplan.contains(subs.subscription().planId())) {
								subscription = subs.subscription();
								break;
							}
						}
					}

					if (sresult.isEmpty() || subscription == null) {
						log.info("CB sub_create : configureDeviceV4 : userid : " + user.getId());
						subscription = cbService.createDefaultSubsPlan(user.getChargebeeid());
					}

					ArrayList<Integer> ids = crService.getPlanAndPeriod(subscription.planId());

//					if (!ids.isEmpty()) {
//						config = crService.getDeviceConfig(ids.get(0));
//						mType = config.get(0);
//						dConfig = config.get(1).split(",");
//						maxDevCnt = Integer.parseInt(config.get(2));
//					}
					// Reading device count from plan table
					if (!ids.isEmpty()) {
						mType = "1";
						dConfig = new String[1];
						dConfig[0] = "0";
						maxDevCnt = crService.getMaxDeviceCount((long)ids.get(0));
					}
				}

				long cnt = crService.getDeviceCountByUser(userid,monitortype);

				if (cnt < maxDevCnt) {

					int size = 0;
					int devicecount = 0;
					StringBuffer monitortypeid = new StringBuffer();

					List result = crService.getDeviceCountByMonitorType(userid, monitortype);

					if (!result.isEmpty()) {
						size = result.size();
						long count = 0;
						String type = "";

						if (size == 1) {
							Object[] tuple = (Object[]) result.get(0);
							type = ((BigInteger) tuple[0]).toString();
							monitortypeid.append(type);
							count = ((BigInteger) tuple[1]).longValue();
							devicecount = (int) count;

							existDevice.put(type, count);
						} else {

							for (int i = 0; i < size; i++) {
								Object[] tuple = (Object[]) result.get(i);
								type = ((BigInteger) tuple[0]).toString();
								long tempcount = ((BigInteger) tuple[1]).longValue();

								devicecount = devicecount + (int) tempcount;
								monitortypeid.append(type);

								existDevice.put(type, tempcount);

								if (tempcount > count)
									count = tempcount;

								monitortypeid.append(",");
							}

							monitortypeid.deleteCharAt(monitortypeid.length() - 1);
						}

						if (mType.contains(String.valueOf(monitortype))) {
							long existCnt = existDevice.get(String.valueOf(monitortype));
							long curCnt = 0;
							// if dConfig contains 0 means can add any type upto no_cnt value
							if (dConfig.length == 1 && (dConfig[0].equalsIgnoreCase("0"))) {

								if (maxDevCnt > devicecount) {
									allowAddDevice = true;
									description = "Continue to Add Monitor";
								}
							} else {
								devCount = dConfig[0].split("/");

								for (int i = 0; i < devCount.length; i++) {
									newDevice.put(String.valueOf((i + 1)), Long.parseLong(devCount[i].trim()));
								}

								curCnt = newDevice.get(String.valueOf(monitortype));

								if (curCnt > existCnt) {
									allowAddDevice = true;
									description = "Continue to Add Monitor";
								}
							}

						} else {
							allowAddDevice = false;
							description = "Please buy a subscription to Add Monitor";
						}
					} else {
						if (mType.contains(String.valueOf(monitortype))) {
							allowAddDevice = true;
							description = "Continue to Add Monitor";
						} else {
							allowAddDevice = false;
							description = "Please buy a subscription to Add Monitor";
						}

					}
				}
				response.setResponse(resp);
				response.put("allowAddDevice", allowAddDevice);
				response.put("description", description);
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured in configure Monitor ");
			log.error("configure Monitor outer try block:" + e.getMessage());
		}
		return response;
	}

//	@RequestMapping(value = "v3.0/updatecbpaymentmethod/{auth}", headers = "Accept=application/json", method = RequestMethod.GET)
//	@ResponseBody
//	public JResponse updateCBPaymentMethod(@PathVariable String auth) {
//		JResponse response = new JResponse();
//		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
//
//		log.info("auth :" + auth);
//		String chargebeeid = "NA";
//		try {
//			UserV4 user = null;
//
//			try {
//				user = userServiceV4.verifyAuthV4("authkey", auth);
//			} catch (Exception e) {
//				log.error("updatePaymentMethod:" + e.getMessage());
//			}
//
//			if (user != null) {
//				
//				if( !user.getRecharge_custid().equalsIgnoreCase("NA") ) {
//					log.info("It is recharge customer chargebee payment method not applicable");
//					response.put("portalUrl", "");
//					response.put("Status", 0);
//					response.put("Msg", "Please contact support to update payment method.");
//					return response;
//				}
//				
//				Environment.configure(chargebeeSiteName, chargebeeSiteKey);
//
//				chargebeeid = user.getChargebeeid();
//
//				if (!chargebeeid.equalsIgnoreCase("NA")) {
//					ListResult resultSet = Customer.list().id().is(chargebeeid).request();
//
//					if (resultSet.size() > 0) {
//						HostedPage hostedPage = null;
//
//						if (checkoutversion.equalsIgnoreCase("v2")) {
//							// v2
//							Result res = HostedPage.updatePaymentMethod().customerId(chargebeeid).embed(false)
//									.request();
//							hostedPage = res.hostedPage();
//						} else {
//							// v3
//							Result res = HostedPage.managePaymentSources().customerId(chargebeeid).request();
//							hostedPage = res.hostedPage();
//						}
//
//						response.put("portalUrl", hostedPage.url());
//						response.put("Status", 1);
//						response.put("Msg", "Success");
//					} else {
//						response.put("portalUrl", "");
//						response.put("Status", 0);
//						response.put("Msg", "Unable to update payment method. Please try after some time.");
//					}
////					
////					if (resultSet.size() > 0) {					
////						Result result = PortalSession.create().customerId(chargebeeid).redirectUrl(paymentupdateurl).request();
////						PortalSession portalSession = result.portalSession();
////						response.put("Status", 1);
////						response.put("Msg", "Success");
////						response.put("portalUrl", portalSession.accessUrl());
////					} 
//				}
//			}
//
//		} catch (Exception e) {
//			log.error("updatePaymentMethod:" + e.getMessage());
//			response.put("Status", 0);
//			response.put("Msg", "UnExcepted Error");
//		}
//		return response;
//	}

	@RequestMapping(value = "v4.0/purchaseviapaymentsource/{auth}", headers = "Accept=application/json", method = RequestMethod.POST)
	@ResponseBody
	public JResponse purchaseViaPaymentSource(@PathVariable String auth, @RequestParam("tokenid") String tokenId,
			@RequestParam("planid") String planid, @RequestParam("couponid") String couponId) {
		JResponse response = new JResponse();
		planid = planid.toLowerCase();

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);

			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				return response;
			} else if (usr.getChargebeeid() == null && usr.getChargebeeid().equalsIgnoreCase("NA")) {
				response.put("Status", 0);
				response.put("Msg", "invalid chargebee id ");
				return response;
			}
			log.info("tokenId : " + tokenId);
			log.info("payment Source Inserted to : " + usr.getChargebeeid());

			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
			Result result = PaymentSource.createUsingToken().customerId(usr.getChargebeeid()).tokenId(tokenId)
					.request();
			if (result == null) {
				log.error("Error while Attach payment Source");
				response.put("Status", 0);
				response.put("Msg", "UnExcepted Error in Attach payment Source. Please contact our support");
				return response;
			}

			Subscription sub = null;

			sub = getvaildsubscription(usr.getChargebeeid());

			if (sub == null) {
				CreateForCustomerRequest customercriteria = Subscription.createForCustomer(usr.getChargebeeid())
						.planId(planid);

				if (couponId != null && !couponId.isEmpty())
					customercriteria.coupon(couponId);

				Result custoSub = customercriteria.request();

				Subscription newsub = custoSub.subscription();

				log.info("Subscription created for " + usr.getChargebeeid() + " Customer");
				log.info("new Subscription plan : " + newsub.id());
			} else {
				UpdateRequest customercriteria = Subscription.update(sub.id()).planId(planid);

				if (couponId != null && !couponId.isEmpty())
					customercriteria.coupon(couponId);

				Result custoSub = customercriteria.forceTermReset(true).request();

				Subscription newsub = custoSub.subscription();
				log.info("Subscription updated for " + usr.getChargebeeid() + " Customer");
				log.info(sub.planId() + " subscription updated to " + planid);
			}

			log.info("Success");

			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (Exception e) {
			log.error("createCBpaymentUsingToken:" + e.getMessage());

			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "UnExcepted Error in Attach payment Source. Please contact our support");
		}
		return response;
	}

	private Subscription getvaildsubscription(String chargebeeid) {
		try {
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);

//			String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
//			String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);

			ListResult result = com.chargebee.models.Subscription.list().customerId().is(chargebeeid).status()
					.in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL).request();

			Subscription subscription = null;

			if (result.isEmpty()) {
				return null;
			} else {
				int ssize = result.size();
				for (ListResult.Entry subs : result) {
					if (ssize == 1 && !omitplan.contains(subs.subscription().planId())) {
						subscription = subs.subscription();
					} else if (!freeplan.contains(subs.subscription().planId())
							&& !omitplan.contains(subs.subscription().planId())) {
						subscription = subs.subscription();
					}
				}
			}

			return subscription;
		} catch (Exception e) {
			log.error("getvaildsubscription:" + e.getLocalizedMessage());
			return null;
		}
	}

	@RequestMapping(value = "v4.0/applychargebeecoupon/{auth}", headers = "Accept=application/json", method = RequestMethod.GET)
	@ResponseBody
	public JResponse createCBpaymentUsingToken(@PathVariable String auth, @RequestParam("couponid") String couponId,
			@RequestParam("planid") String planId) {
		JResponse response = new JResponse();
		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);

			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				return response;
			} else if (usr.getChargebeeid() == null && usr.getChargebeeid().equalsIgnoreCase("NA")) {
				response.put("Status", 0);
				response.put("Msg", "invalid chargebee id ");
				return response;
			}

			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
			Coupon coupon = null;
			try {
				Result result = Coupon.retrieve(couponId).request();
				coupon = result.coupon();
			} catch (Exception e) {
				log.error("Error While fetch Coupon:" + e.getMessage());
				response.put("Status", 0);
				response.put("Msg", "Invaild Coupon");
				response.put("Error", e.getMessage());
				return response;
			}

			if (coupon != null) {

				if (coupon.planConstraint().ordinal() != PlanConstraint.SPECIFIC.ordinal()
						|| coupon.planIds().contains(planId)) {
					Plan plan = null;
					try {
						Result planResult = Plan.retrieve(planId).request();
						plan = planResult.plan();

					} catch (Exception e) {
						log.error("Error While fetch PlanId:" + e.getMessage());
						response.put("Status", 0);
						response.put("Msg", "Invaild Plan");
						response.put("Error", e.getMessage());
						return response;
					}

					if (plan != null) {

						Double discountPercentage = 0.0;
						float discountAmount = 0;

						float planPrice = (float) plan.price() / 100;

						if (coupon.discountType().ordinal() == DiscountType.PERCENTAGE.ordinal()) {
							discountPercentage = coupon.discountPercentage();
							discountAmount = (float) (((float) planPrice * (discountPercentage)) / 100);
						} else {
							discountAmount = (float) coupon.discountAmount() / 100;
						}

						float finalprice = planPrice - discountAmount;

						finalprice = ((float) ((int) (finalprice * 100))) / 100;
						discountAmount = ((float) ((int) (discountAmount * 100))) / 100;

						Double temp = discountPercentage * 100;
						discountPercentage = ((double) temp.intValue()) / 100;

						log.info("Success");
						response.put("Status", 1);
						response.put("Msg", "Success");
						response.put("DiscountPercentage", discountPercentage);
						response.put("DiscountAmount", discountAmount);
						response.put("PlanPrice", planPrice);
						response.put("finalprice", finalprice);

					} else {
						log.info("failure : PlanId not found : " + planId);
						response.put("Status", 0);
						response.put("Msg", "Plan not found");
					}
				} else {
					log.info("failure : coupon not applicable for this plan : " + planId);
					response.put("Status", 0);
					response.put("Msg", "Coupon not applicable for this plan");
				}
			} else {
				log.info("failure : Coupon not found : " + couponId);
				response.put("Status", 0);
				response.put("Msg", "Coupon not found");
			}

		} catch (Exception e) {
			log.error("createCBpaymentUsingToken:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error while Apply Coupon.");
			response.put("Error", e.getMessage());
		}
		return response;
	}

	// Cancel Trial Subscription - Savitha.
	@RequestMapping(value = "v4.0/cancelsubscription/{auth}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse cancelTrialSubscription(@PathVariable String auth, @RequestParam("os") String os,
			@RequestParam(value = "cancel_id", required = true) String cancel_id,
			@RequestParam(value = "product_id", required = true) String product_id,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
			) {
		log.info(" Entered cancelTrialSubscription :" + auth);
		JResponse response = new JResponse();

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", auth);
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);

			if (usr != null) {
				Result res = Subscription.cancel(cancel_id)
						.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.PRORATE).request();
				
				Subscription cancelSubscription = res.subscription();

				if (cancelSubscription.status().toString().equalsIgnoreCase("cancelled")) {
					
					Subscription createdSub = cbService.createDefaultSubsPlan(usr.getChargebeeid());
					
					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Failed to cancel subscription!!!");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occured");
			response.put("Error", e.getLocalizedMessage());
			log.error("cancelTrialSubscription : " + e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v4.0/getavailableupgradeplanV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAvailableUpgradeSubscriptionPlanV2(@RequestHeader HttpHeaders header, @RequestParam("planid") long planid,
			@RequestParam("period") long period,@RequestParam("os") String os,
			@RequestParam( "app_ver") String app_ver,	@RequestParam("plan_ver") String plan_ver,
			@RequestParam(value = "freetrial", defaultValue = "false", required = false) boolean freetrial) {
		
		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();
		UserV4 user = null;
		String auth = header.getFirst("auth");

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);
		} catch (Exception e) {
			log.error("get AvailableUpgrade:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		try {
			if (user != null) {
				if (user.getInapp_purchase() == 1) {
					
					String country = user.getCountry();
					country = country.equalsIgnoreCase("CA") ? "CA" : "US";
					response = crService.upgradePlanList( user.getId(),planid, period,freetrial,country);
	
//						int days = userService.getRemainingDays(user.getId());
//						String free_trial = "NA";
//						String note = "NA";
//						if (days > 0) {
//							free_trial = String.valueOf(days).concat(" Days");
//							note = "* Please buy a subscription to proceed further. You will be billed only after the FREE subscription period ends and "
//									+ " you can choose to cancel anytime before the FREE subscription ends.";
//						}
//						response.put("free_trial", free_trial);
//						response.put("note", note);

				}else {
					response.put("Status", 0);
					response.put("Msg", "Upgrade list not found");
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			log.error("get AvailableUpgrade plans:" + e.getMessage());
			response.put("Error", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");

		}
		return response;
	}


	@RequestMapping(value = "v4.0/updatesubscriptionplanV2", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateSubscriptionPlanV2(@RequestHeader HttpHeaders header,
			@RequestParam("planid") long planid, @RequestParam("periodid") long periodid,
			@RequestParam("os") String os,@RequestParam("app_ver") String app_ver,
			@RequestParam("plan_ver") String plan_ver) {
		JResponse response = new JResponse();
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		UserV4 user = null;
		try {
			String auth = header.getFirst("auth");

			user = userServiceV4.verifyAuthV3("authkey", auth);
			if (user != null) {
				if (user.getChargebeeid().equalsIgnoreCase("NA")) {
					String cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
							user.getMobileno(), user.getUsername(), 0, "NA");
					user.setChargebeeid(cbId);
				}
				
//				String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
//				String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);
//				String vpmplan = _helper.getExternalConfigValue("vpmplan", externalConfigService);

				boolean disableUpgrade = false;
				String country = user.getCountry();
				country = country.equalsIgnoreCase("CA") ? "CA" : "US";
				String[] cbPlanAndTrialPeriod = crService.getChargebeePlanById(planid, periodid, country);

				String cb_plan = cbPlanAndTrialPeriod[0];
				int freeTrialPeriod = Integer.parseInt(cbPlanAndTrialPeriod[1]);
				String cb_coupon_id = cbPlanAndTrialPeriod[2];
				String cb_addon_id = cbPlanAndTrialPeriod[3];
				List<String> addonIdList = Arrays.asList(cb_addon_id.split(","));

				com.chargebee.models.Subscription subscription = null;
				int size = 0;

				if (!cb_plan.equalsIgnoreCase("NA")) {
					ListResult result = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
							.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL).request();
					if (!result.isEmpty())
						size = result.size();

					for (ListResult.Entry subs : result) {
						if (size == 1 && !omitplan.contains(subs.subscription().planId())
								&& !vpmplan.contains(subs.subscription().planId())
								&& !addonplan.contains(subs.subscription().planId())) {
							subscription = subs.subscription();
						} else if (!freeplan.contains(subs.subscription().planId())
								&& !omitplan.contains(subs.subscription().planId())
								&& !vpmplan.contains(subs.subscription().planId())
								&& !addonplan.contains(subs.subscription().planId())) {

							subscription = subs.subscription();
						}
					}

					if (subscription == null) {
						for (ListResult.Entry subs : result) {
							if (freeplan.contains(subs.subscription().planId())) {
								subscription = subs.subscription();
							}
						}
					}

					if (subscription != null && freeplan.contains(subscription.planId())) {
						disableUpgrade = true;
					}

					boolean free_trial_applied  = userServiceV4.getFreeTrialDays(user.getChargebeeid());
					
					int days = 0;
					int daysBetween = 0;
					OrderMappingDetails order = null;
					Timestamp trialEnd = null;

					// this for customer merge
					HashMap<String, Object> metaData = new HashMap<String, Object>();
					metaData.put("userid", user.getId());
					metaData.put("chargebeeid", user.getChargebeeid());
					String cancel_subid = "NA";
					String order_id = "NA";
					String orderchannel = "RV";
					CheckoutNewRequest checkoutNewRequest;
					CheckoutExistingRequest checkoutExitingRequest;
					Result res;

					if (amazonLaunchPadFreeTrialEnable) {
						order = userService.getOrderMappingByUser(user.getId());
					}
//					if (order != null) {
//						orderchannel = order.getOrderchannel();
//						days = (int) userService.getCreditAmountBySKU(order.getExternalsku());
//
//						if (days > 0) {
//							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//							Date todayDate = sdf.parse(sdf.format(new Date()));
//
//							Timestamp orderdate = order.getOrderdate();
//							log.info("orderdate: " + orderdate);
//							Date expirydate = new Date(orderdate.getTime());
//
//							Calendar cal = Calendar.getInstance();
//							cal.setTime(expirydate);
//							cal.add(Calendar.DATE, days);
//
//							expirydate = cal.getTime();
//
//							long difference = expirydate.getTime() - todayDate.getTime();
//							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
//
//							if (daysBetween > 0) {
//								String trialendDt = sdf.format(expirydate);
//								// tialendDt = tialendDt.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.ENDTIME);
//								trialEnd = Timestamp.valueOf(trialendDt);
//							} else
//								daysBetween = 0;
//						}
//					}
					// String addonId = "setup_charge";
					if (daysBetween > 0) {

						if (subscription != null)
							cancel_subid = subscription.id();

						order_id = String.valueOf(order.getId());

						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
						
						checkoutNewRequest = HostedPage.checkoutNew().subscriptionTrialEnd(trialEnd)
								.subscriptionPlanId(cb_plan).subscriptionPlanQuantity(1).addonId(0, activationAddonId)
								.addonQuantity(0, 1).addonBillingCycles(0, 1).customerFirstName(user.getFirstname())
								.customerLastName(user.getLastname()).customerEmail(user.getEmail())
								.customerPhone(user.getMobileno());

						if(!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutNewRequest.subscriptionCoupon(cb_coupon_id);
						}
						
						if(!cb_addon_id.equalsIgnoreCase("NA")) {
							int i =0;
							// index - 0 updateAddonId/activation addon. remaining addon starts with 1
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j+1;
								checkoutNewRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
							}
						}
						res = checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
								.embed(embedupdate).request();

					}else if (subscription != null && !disableUpgrade) {
						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
						checkoutExitingRequest = HostedPage.checkoutExisting().subscriptionId(subscription.id())
							.subscriptionPlanId(cb_plan).addonId(0, updateAddonId).addonQuantity(0, 1)
							.addonBillingCycles(0, 1).replaceAddonList(true).redirectUrl(redirtPetUrl)
							.passThruContent(jsonObj.toString()).embed(embedupdate).forceTermReset(true);
						
						if(!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutExitingRequest.subscriptionCoupon(cb_coupon_id);
						}
						
						if(!cb_addon_id.equalsIgnoreCase("NA")) {
							int i =0;
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j+1;
								checkoutExitingRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
							}
						}
						
						res = checkoutExitingRequest.request();
						
					} else if (subscription != null && disableUpgrade && freeTrialPeriod == 0) {
						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);
						
						checkoutExitingRequest = HostedPage.checkoutExisting().subscriptionId(subscription.id())
							.subscriptionPlanId(cb_plan).addonId(0, activationAddonId).addonQuantity(0, 1).addonBillingCycles(0, 1)
							.replaceAddonList(true).redirectUrl(redirtPetUrl)
							.passThruContent(jsonObj.toString()).embed(embedupdate).forceTermReset(true);
						
						if(!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutExitingRequest.subscriptionCoupon(cb_coupon_id);
						}
						
						if(!cb_addon_id.equalsIgnoreCase("NA")) {
							int i =0;
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j+1;
								checkoutExitingRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
							}
						}
						
						res = checkoutExitingRequest.request();
						
					} else if(subscription != null && disableUpgrade && freeTrialPeriod>0 && !free_trial_applied) {
						metaData.put("order_id", order_id);
						metaData.put("regarding", orderchannel);

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);

//						checkoutNewRequest = HostedPage.checkoutNew().subscriptionPlanId(cb_plan).subscriptionPlanQuantity(1).addonId(0,activationAddonId).addonQuantity(0, 1).addonBillingCycles(0, 1)
//							.customerFirstName(user.getFirstname())
//							.customerLastName(user.getLastname()).customerEmail(user.getEmail()).customerPhone(user.getMobileno());

						// In CB - v3 we can create multiple subscription for a single user

						Result res1 = Subscription.cancel(subscription.id())
								.creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.NONE).request();
						
						subscription = res1.subscription();
						Timestamp trialEndDate = new Timestamp(Instant.now().plus(freeTrialPeriod, ChronoUnit.DAYS).toEpochMilli());
								
						checkoutExitingRequest = HostedPage.checkoutExisting().subscriptionId(subscription.id())
								.subscriptionPlanId(cb_plan).addonId(0, activationAddonId).addonQuantity(0, 1)
								.addonBillingCycles(0, 1).replaceAddonList(true).redirectUrl(redirtPetUrl)
								.passThruContent(jsonObj.toString()).embed(embedupdate).forceTermReset(true)
								.subscriptionTrialEnd(trialEndDate);
						
						if(!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutExitingRequest.subscriptionCoupon(cb_coupon_id);
						}

						if(!cb_addon_id.equalsIgnoreCase("NA")) {
							int i =0;
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j+1;
								checkoutExitingRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
							}
						}
						
						res = checkoutExitingRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
								.embed(embedupdate).request();
					}else {
						metaData.put("order_id", order_id);
						metaData.put("cancel_subid", cancel_subid);
						metaData.put("regarding", orderchannel);

						com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);

//						checkoutNewRequest = HostedPage.checkoutNew().subscriptionPlanId(cb_plan).subscriptionPlanQuantity(1).addonId(0,activationAddonId).addonQuantity(0, 1).addonBillingCycles(0, 1)
//							.customerFirstName(user.getFirstname())
//							.customerLastName(user.getLastname()).customerEmail(user.getEmail()).customerPhone(user.getMobileno());

						// In CB - v3 we can create multiple subscription for a single user

						checkoutNewRequest = HostedPage.checkoutNew().subscriptionPlanId(cb_plan)
								.subscriptionPlanQuantity(1).addonId(0, activationAddonId).addonQuantity(0, 1)
								.addonBillingCycles(0, 1).customerId(user.getChargebeeid());

						if (freeTrialPeriod > 0 && !free_trial_applied) {
							Timestamp trialEndDate = new Timestamp(
									Instant.now().plus(freeTrialPeriod, ChronoUnit.DAYS).toEpochMilli());
							checkoutNewRequest.subscriptionTrialEnd(trialEndDate);
						}
						
						if(!cb_coupon_id.equalsIgnoreCase("NA")) {
							checkoutNewRequest.subscriptionCoupon(cb_coupon_id);
						}

						if(!cb_addon_id.equalsIgnoreCase("NA")) {
							int i =0;
							for (int j = 0; j < addonIdList.size(); j++) {
								i = j+1;
								checkoutNewRequest.addonId(i, addonIdList.get(j)).addonQuantity(i, 1);
							}
						}
						
						res = checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
								.embed(embedupdate).request();

					}

					HostedPage hostedPage = res.hostedPage();

					response.put("checkOutURL", hostedPage.url());
					response.put("Status", 1);
					response.put("Msg", "Success");

				} else {
					response.put("Status", 0);
					response.put("Msg", "chargebee plan id not available in DB");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "user details not found");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Failed to update subscription");
			log.error("update subscriptionplan:" + e.getMessage());
		}
		return response;
	}
	
	@RequestMapping(value = "v4.0/getcurrentsubscriptionplanV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCurrentSubscriptionPlanV2(@RequestHeader HttpHeaders header,
			@RequestParam("os") String os,	@RequestParam("app_ver") String app_ver,
			@RequestParam("backing") String backing,@RequestParam("plan_ver") String plan_ver) {
		
		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("start : " + start + " getcurrentsubscriptionplan: " + auth);
		try {
			
//			Helper _helper = new Helper();
			log.info("backing key : "+backing);
			
//			AES aes = new AES();
//			if (backing != null) {
//				if (!backing.equals("MT")) {
//					String[] credential = _helper.decodeInternalKey(backing);
//					String finalOut = aes.decode(credential[0], credential[1]);
//					
//					if (finalOut == null) {
//						response.put("Status", 0);
//						response.put("Msg", "Authentication Error");
//						return response;
//					}
//					log.info("AES decryption success : "+backing+" : "+finalOut);
//				}
//			} else {
//				response.put("Status", 0);
//				response.put("Msg", "Authentication Error");
//				return response;
//			}
			
			user = userServiceV4.verifyAuthV3("authkey", auth);

		} catch (Exception e) {
			log.error("getCurrentSubscriptionPlan: user by id : " + e.getMessage());
		}

		try {
			if (user != null) {
				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;
//					boolean show_alertlimit = false;
//					VersionMapping verObj = crService.getVersionMapping(app_ver, os);
//
//					if (verObj != null) {
//						inapp_redirect = verObj.getInapp_redirect();
//						cb_checkout = verObj.isCb_checkout();
//					}
//
//					if(!user.getPlan_ver().equalsIgnoreCase("V1"))
//						show_alertlimit = true;
//					
//					if (user.getInapp_purchase() == 1) {
						//response = getSubscriptionV2(user, inapp_redirect);
						response = cbService.getSubscriptionFromCB(user, inapp_redirect, os, app_ver, "-08:00",false, 0L);
						
						response.put("show_addon", show_addon_button);
//						response.put("show_alertlimit", show_alertlimit);
						response.put("cb_checkout", cb_checkout);
//					} else {
//						response.put("Status", 0);
//						response.put("Msg", "Subscription Details not found");
//
//					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Please try again.");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		return response;
	}

//	public JResponse getSubscriptionV2(UserV4 user, int inapp_redirect) {
////		String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
////		String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);
////		String vpmplan = _helper.getExternalConfigValue("vpmplan", externalConfigService);
//
//		JResponse response = new JResponse();
//		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
//		try {
//			String cbId = "NA";
//			if (user.getChargebeeid().equalsIgnoreCase("NA")) {
//				cbId = userServiceV4.createUserInChargebee(user.getFirstname(), user.getLastname(), user.getEmail(),
//						user.getMobileno(), user.getUsername(), 0, "NA");
//				user.setChargebeeid(cbId);
//			}
//			com.chargebee.models.Subscription subscrip = null;
//			com.chargebee.models.Subscription vpmSubscrip = null;
//			boolean isPaidPlan = false;
//			boolean vpm_addon = false;
//			
//			if (!user.getChargebeeid().equalsIgnoreCase("NA")) {
//				ListResult result = com.chargebee.models.Subscription.list().customerId().is(user.getChargebeeid())
//						.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL)
//						.sortByUpdatedAt(SortOrder.DESC).request();
//				int ssize = 0;
//
//				if (!result.isEmpty()) {
//					ssize = result.size();
//					for (ListResult.Entry subs : result) {
//						String subs_planid = subs.subscription().planId();
//						if (ssize == 1 && !omitplan.contains(subs_planid)
//									&& !vpmplan.contains(subs_planid)
//									&& !addonplan.contains(subs_planid)) {
//							subscrip = subs.subscription();
//
//							if (!freeplan.contains(subs_planid)
////									&& !vpmplan.contains(subs.subscription().planId())
////									&& !addonplan.contains(subs.subscription().planId())
//									) {
//								isPaidPlan = true;
//								
//								List<Addon> addonList = subscrip.addons();
//								for(Addon addon : addonList) {
//									if(addon.id().contains("vetchat")) {
//										vpm_addon = true;
//										log.info(subs_planid + ": vpm included with plan");
//										break;
//									}
//								}
//								break;
//							}
//
//						} else if (!freeplan.contains(subs_planid)
//								&& !omitplan.contains(subs_planid)
//								&& !vpmplan.contains(subs_planid)
//								&& !addonplan.contains(subs_planid)) {
//							subscrip = subs.subscription();
//							isPaidPlan = true;
//							
//							List<Addon> addonList = subscrip.addons();
//							for(Addon addon : addonList) {
//								if(addon.id().contains("vetchat")) {
//									vpm_addon = true;
//									log.info(subs_planid + ": vpm included with plan");
//									break;
//								}
//							}
//							break;
//						}
//					}
//
//					if (subscrip == null) {
//						for (ListResult.Entry subs : result) {
//							if (freeplan.contains(subs.subscription().planId())) {
//								subscrip = subs.subscription();
//								break;
//							}
//						}
//					}
//					if (vpmSubscrip == null && !vpm_addon) {
//						for (ListResult.Entry subs : result) {
//							if (vpmplan.contains(subs.subscription().planId())) {
//								vpmSubscrip = subs.subscription();
//								break;
//							}
//						}
//					}
//				}
//				if ((result.isEmpty() || subscrip == null) && (inapp_redirect != 2)) {
//					// ios inapp user.here checking is there any CB subscription available
//					log.info("CB sub_create : getSubscriptionv2 : userid : " + user.getId());
//					subscrip = cbService.createDefaultSubsPlan(user.getChargebeeid());
//
//				}
//			}
//			// initialize values
//			JVpmSubscription vpmSubs = new JVpmSubscription();
//			String strprice1 = "NA";
//			int availCnt = 0;
//			int totalCnt = 0;
//			int usedCnt = 0;
//
//			String billingPeriod = "NA";
//			String periodUnit = "MONTH";
//			String planid = "chum";
//			String planname = "Chum";
//			String availCredit = "0";
//			int period = 0;
//
//			float price = (float) 0.0;
//			String strprice = "$0.0";
//			String status = "ACTIVE";
//			int daysBetween = -1;
//			int days_remaining = -1;
//			String startedAt = "NA";
//			String nextPaymentDate = "NA";
//			String createDate = "NA";
//			String updateDate = "NA";
//			String cbSubId = "NA";
//			String cbSubStatus = "NA";
//			boolean setupAutoRenewal = false;
//			String autoRenewalStatus = "NA";
//			boolean cancel = false;
//			boolean vetchat_cancel = false;
//			String cbvet_cancelId = "";
//			String cbvet_planId = "";
//			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//			Date dateobj = new Date();
//			Timestamp nextpaymentTS;
//			boolean cb_vetchat =false;
//
//			int iris_splan = 1;
//			int iris_speriod = 1;
//			String desc = "Free Plan";
//			JSubscriptionPlanReport rpt = new JSubscriptionPlanReport();
//			boolean vpm_enable = companyService.getVetCallStaus(user.getCmpId());
//			int substatus_code = 0;
//
//			if (subscrip != null || vpmSubscrip != null) {
//				if (subscrip != null) {
//					CreditNote cr = null;
//					ListResult crLst = CreditNote.list().customerId().is(user.getChargebeeid()).request();
//					cr = crLst.isEmpty() ? null : crLst.get(0).creditNote();
//					ListResult planRes = Plan.list().id().is(subscrip.planId()).request();
//					availCredit = (cr != null) ? String.valueOf(cr.amountAvailable() / 100.0) : "0";
//
//					for (ListResult.Entry planR : planRes) {
//						Plan plan = planR.plan();
//						period = plan.period();
//						periodUnit = plan.periodUnit().name();
//						planid = plan.id();
//						planname = plan.name();
//
//						if (periodUnit.equalsIgnoreCase("YEAR")) {
//							if (period == 2)
//								billingPeriod = "2 Year";
//							else if (period >= 5)
//								billingPeriod = planname;
//							else
//								billingPeriod = "Yearly";
//						} else if (periodUnit.equalsIgnoreCase("MONTH")) {
//							if (period == 3)
//								billingPeriod = "Quarterly";
//							else if (period == 6)
//								billingPeriod = "Half-Yearly";
//							else if (period == 12)
//								billingPeriod = "Yearly";
//							else if (period == 24)
//								billingPeriod = "2 Year";
//							else
//								billingPeriod = "Monthly";
//						} else if (periodUnit.equalsIgnoreCase("DAY")) {
//							billingPeriod = "Daily";
//						} else if (periodUnit.equalsIgnoreCase("WEEK")) {
//							billingPeriod = "Weekly";
//						}
//
//						price = (float) subscrip.planUnitPrice() / 100;
//						strprice = "$" + String.valueOf(price);
//						status = subscrip.status().name();
//
//						if ((status.equalsIgnoreCase("ACTIVE")) || (status.equalsIgnoreCase("IN_TRIAL"))) {
//							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//							if (status.equalsIgnoreCase("IN_TRIAL")) {
//								cancel = true;
//							}
//							substatus_code = 1;
//							try {
//								nextpaymentTS = subscrip.nextBillingAt();
//
//								if (nextpaymentTS == null)
//									nextpaymentTS = subscrip.currentTermEnd();
//
//							} catch (Exception ex) {
//								nextpaymentTS = subscrip.currentTermEnd();
//							}
//							nextPaymentDate = sdf.format(nextpaymentTS.getTime());
//
//							Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
//							Date todayDate = sdf.parse(sdf.format(dateobj));
//
//							long difference = nextPaymentDate1.getTime() - todayDate.getTime();
//							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
//							days_remaining = daysBetween;
//
//							sdf = new SimpleDateFormat("yyyy-MM-dd");
//							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//							nextPaymentDate = sdf.format(nextpaymentTS.getTime());
//							// status = "ACTIVE";
//							autoRenewalStatus = "Enabled";
//
//							if (freeplan.contains(subscrip.planId())) {
//								nextPaymentDate = "NA";
//								days_remaining = -1;
//								autoRenewalStatus = "NA";
//								billingPeriod = "NA";
//							}
//
//						} else if (status.equalsIgnoreCase("NON_RENEWING")) {
//							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//							status = "ACTIVE";
//							nextPaymentDate = sdf.format(subscrip.cancelledAt().getTime());
//							Date cancelledAt = sdf.parse(nextPaymentDate);
//							Date todayDate = sdf.parse(sdf.format(dateobj));
//
//							long difference = cancelledAt.getTime() - todayDate.getTime();
//							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
//							days_remaining = daysBetween;
//
//							sdf = new SimpleDateFormat("yyyy-MM-dd");
//							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//							nextPaymentDate = sdf.format(subscrip.cancelledAt().getTime());
//							substatus_code = 1;
//							autoRenewalStatus = "Disabled";
//						}
//
//						if (daysBetween < 0)
//							days_remaining = -1;
//
//						ArrayList<Integer> ids = crService.getPlanAndPeriod(planid);
//						boolean alert_setting = true;
//						if (!ids.isEmpty()) {
//							iris_splan = ids.get(0);
//							iris_speriod = ids.get(1);
//
//							SubscriptionPlan splan = crService.getSubsPlanById(ids.get(0));
//							desc = splan.getDescription();
//							planname = splan.getPlan_name();
//							alert_setting = splan.isAlert_setting();
//						}
//
//						// safety
//						try {
//							sdf = new SimpleDateFormat("yyyy-MM-dd");
//							createDate = sdf.format(subscrip.createdAt().getTime());
//							updateDate = sdf.format(subscrip.updatedAt().getTime());
//							startedAt = sdf.format(subscrip.startedAt().getTime());
//							cbSubId = subscrip.id();
//							cbSubStatus = subscrip.status().name();
//						} catch (Exception e) {
//							log.error("subs dates: ", e.getLocalizedMessage());
//						}
//
//						List<JGatewaySubSetup> setupList = crService.checkDeviceConfigStatusV2(iris_splan,user.getId(), days_remaining);
//
//						rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
//								days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus,
//								createDate, updateDate, cbSubId, cbSubStatus, availCredit, alert_setting,
//								user.getChargebeeid(), startedAt, planid, String.valueOf(iris_speriod), cancel,
//								substatus_code);
//					}
//				} else {
//					List<JGatewaySubSetup> setupList = crService.checkDeviceConfigStatusV2(iris_splan,user.getId(), days_remaining);
//
//					rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
//							days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
//							updateDate, cbSubId, cbSubStatus, availCredit, true, user.getChargebeeid(), startedAt, "",
//							String.valueOf(iris_speriod), cancel, substatus_code);
//				}
//				int vpmstatus_code = 0;
//				String str_total_cnt = "0";
//				float price1 = 0;
//				
//				if(vpm_addon) {
//					vpmstatus_code = 1;
//					str_total_cnt = "Unlimited";
//					vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
//							str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod,vetchat_cancel,cbvet_cancelId,cbvet_planId,cb_vetchat);
//
//				}
//				else if (vpmSubscrip != null) {
//					price1 = (float) vpmSubscrip.planUnitPrice() / 100;
//					status = vpmSubscrip.status().name();
//					if (vpmSubscrip.planId().equalsIgnoreCase("vet-chat")) {
//						strprice1 = "$" + String.valueOf(price1);
//						nextPaymentDate = "NA";
//						autoRenewalStatus = "Disabled";
//						billingPeriod = "NA";
//						vpmstatus_code = 1;
//					} else {
//						sdf = new SimpleDateFormat("yyyy-MM-dd");
//						sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//						cb_vetchat = true;
//
//						if (status.equalsIgnoreCase("ACTIVE") || (status.equalsIgnoreCase("IN_TRIAL"))) {
//							if (status.equalsIgnoreCase("IN_TRIAL")) {
//								vetchat_cancel = true;
//								cbvet_cancelId = vpmSubscrip.id();
//								cbvet_planId = vpmSubscrip.planId();
//							}
//							
//							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//							try {
//								autoRenewalStatus = "Enabled";
//								nextpaymentTS = vpmSubscrip.nextBillingAt();
//
//								if (nextpaymentTS == null)
//									nextpaymentTS = vpmSubscrip.currentTermEnd();
//
//							} catch (Exception ex) {
//								nextpaymentTS = vpmSubscrip.currentTermEnd();
//							}
//
//							nextPaymentDate = sdf.format(nextpaymentTS.getTime());
//
//							Date nextPaymentDate1 = sdf.parse(nextPaymentDate);
//							Date todayDate = sdf.parse(sdf.format(dateobj));
//
//							long difference = nextPaymentDate1.getTime() - todayDate.getTime();
//							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
//							days_remaining = daysBetween;
//							autoRenewalStatus = "Enabled";
//							vpmstatus_code = 1;
//
//						} else if (status.equalsIgnoreCase("NON_RENEWING")) {
//							autoRenewalStatus = "Disabled";
//							status = "ACTIVE";
//							sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//							nextPaymentDate = sdf.format(vpmSubscrip.cancelledAt().getTime());
//							Date cancelledAt = sdf.parse(nextPaymentDate);
//							Date todayDate = sdf.parse(sdf.format(dateobj));
//
//							long difference = cancelledAt.getTime() - todayDate.getTime();
//							daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
//							days_remaining = daysBetween;
//							vpmstatus_code = 1;
//							autoRenewalStatus = "Disabled";
//						} else {
//							status = "INACTIVE";
//							vpmstatus_code = 0;
//							strprice1 = "$0.0";
//						}
//						billingPeriod = "Monthly";
//						strprice1 = "$" + String.valueOf(price1);
//						str_total_cnt = "Unlimited";
//					}
//
//					ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0,
//							vpmSubscrip.planId());
//
//					if (!vpmlist.isEmpty()) {
//						availCnt = vpmlist.get(0);
//						totalCnt = vpmlist.get(1);
//						usedCnt = totalCnt - availCnt;
//
//						if (availCnt == 0) {
//							status = "INACTIVE";
//							vpmstatus_code = 0;
//							strprice1 = "$0.0";
//							nextPaymentDate = "NA";
//							billingPeriod = "NA";
//							str_total_cnt = "0";
//						}
//						if (vpmSubscrip.planId().equalsIgnoreCase("vet-chat") && totalCnt > 0)
//							str_total_cnt = totalCnt + "";
//					}
//
//					vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
//							str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod,vetchat_cancel,cbvet_cancelId,cbvet_planId,cb_vetchat);
//				} else {
//
//					if (vpm_enable) {
//						ArrayList<Integer> vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0, "");
//						availCnt = vpmlist.get(0);
//						totalCnt = vpmlist.get(1);
//						usedCnt = totalCnt - availCnt;
//						status = "ACTIVE";
//						vpmstatus_code = 1;
//						str_total_cnt = availCnt + "";
//
//						if (availCnt == 0) {
//							status = "INACTIVE";
//							vpmstatus_code = 0;
//							strprice1 = "$0.0";
//							nextPaymentDate = "NA";
//							billingPeriod = "NA";
//							str_total_cnt = "0";
//						}
//					} else {
//						availCnt = 0;
//						totalCnt = 0;
//						usedCnt = 0;
//						status = "INACTIVE";
//						vpmstatus_code = 0;
//						strprice1 = "$0.0";
//						nextPaymentDate = "NA";
//						billingPeriod = "NA";
//						str_total_cnt = "0";
//					}
//					autoRenewalStatus = "Disabled";
//					nextPaymentDate = "NA";
//					billingPeriod = "NA";
//					strprice1 = "$0.0";
//					vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
//							str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod,vetchat_cancel,cbvet_cancelId,cbvet_planId,cb_vetchat);
//				}
//
//			} else {
//
//				List<JGatewaySubSetup> setupList = crService.checkDeviceConfigStatusV2(iris_splan,user.getId(), days_remaining);
//
//				rpt = new JSubscriptionPlanReport(planid, planname, strprice, billingPeriod, nextPaymentDate,
//						days_remaining, status, setupAutoRenewal, setupList, desc, autoRenewalStatus, createDate,
//						updateDate, cbSubId, cbSubStatus, availCredit, true, user.getChargebeeid(), startedAt, "",
//						String.valueOf(iris_speriod), cancel, substatus_code);
//
//				availCnt = 0;
//				totalCnt = 0;
//				usedCnt = 0;
//				status = "INACTIVE";
//				autoRenewalStatus = "Disabled";
//				nextPaymentDate = "NA";
//				int vpmstatus_code = 0;
//				String str_total_cnt = "0";
//				billingPeriod = "NA";
//				strprice1 = "0";
//
//				vpmSubs = new JVpmSubscription(strprice1, totalCnt, availCnt, usedCnt, status, nextPaymentDate,
//						str_total_cnt, vpmstatus_code, autoRenewalStatus, billingPeriod,vetchat_cancel,cbvet_cancelId,cbvet_planId,cb_vetchat);
//
//			}
//
//			boolean is_upgrade = true;
//			int redirect = user.getInapp_purchase(); // default 1
//			String upgrade_msg = "Success";
//
//			if (isPaidPlan == true && (inapp_redirect == 2 || user.getInapp_purchase() == 2)) {
//				inapp_redirect = 1;
//				redirect = inapp_redirect;
//				user.setInapp_purchase(inapp_redirect);
//				// call user update method
//				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
//
//			} else if ((isPaidPlan == false && inapp_redirect == 2 && user.getInapp_purchase() == 1)
//					|| (isPaidPlan == false && inapp_redirect == 1 && user.getInapp_purchase() == 2)) {
//				// update CB purchase status to 2[inapp] in user table
//				user.setInapp_purchase(inapp_redirect);
//				redirect = inapp_redirect;
//				userServiceV4.updateInAppPurchaseInfo(inapp_redirect, user.getId());
//			}
//
//			redirect = user.getInapp_purchase();
//
//			response.put("Status", 1);
//			response.put("Msg", "Success");
//			response.put("planid", iris_splan);
//			response.put("periodid", iris_speriod);
//			response.put("subscriptionplan", rpt);
//			response.put("vpm_enable", vpm_enable);
//			response.put("vpmsubs", vpmSubs);
//
//			response.put("is_upgrade", is_upgrade);
//			response.put("redirect_inapp", redirect);
//			response.put("upgrade_msg", upgrade_msg);
//			response.put("Status", 1);
//			response.put("Msg","Success");
//
//		} catch (Exception e) {
//			e.printStackTrace();
//			response.put("Status", 0);
//			response.put("Error", e.getLocalizedMessage());
//			response.put("Msg", "Please try again.");
//			log.error("getSubscription:" + e.getMessage());
//		}
//
//		return response;
//	}
	
	@RequestMapping(value = "v4.0/getsettingfeaturesV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSettingFeaturesV2(@RequestHeader HttpHeaders header,@RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,@RequestParam("plan_ver") String plan_ver) {
		
		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		log.info("getsettingfeaturesV2: " + auth);
		try {
			
			user = userServiceV4.verifyAuthV4("authkey", auth);

		} catch (Exception e) {
			log.error("getsettingfeaturesV2: user by id : " + e.getMessage());
		}

		try {
			if (user != null) {
				ArrayList<JFeatureCredit> setting_features = crService.getSettingFeatures(user.getId());
				response.put("setting_features", setting_features);
				response.put("alert_msg", "Upgrade to Waggler plan to unlock all the alerts.");
				response.put("Status", 1);
				response.put("Msg", "Success");
			}else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Please try again later.");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		return response;
	}

	@RequestMapping(value = "v4.0/getaddonplans", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAddonPlans(@RequestHeader HttpHeaders header,	@RequestParam("os") String os,	@RequestParam("app_ver") String app_ver,
			@RequestParam long plan_id, @RequestParam long period_id,@RequestParam("addon_ver") String addon_ver) {
		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		log.info("getAddonPlans :"+ auth);
				
		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (Exception e) {
			log.error("getAddonPlans:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		try {
			if (user != null) {
				if(addon_ver.equalsIgnoreCase("V1")) {
					response.put("Status", 0);
					response.put("Msg", "Please upgrade your waggle app!");

				}
//				else {
//					String alertsLimit = crService.getalertslimit(user.getId());
//					boolean addon_buynow = false;
//					List<JAddonPlan> plan_list= new ArrayList<JAddonPlan>();
//					
//					if(!alertsLimit.contains("Unlimited")) {
//						plan_list= crService.getAddonPlanList(plan_id,period_id);
//						addon_buynow = true;
//					}else {
//						addon_buynow = false;
//					}
//					response.put("addon_buynow", addon_buynow);
//					response.put("alerts_limit", alertsLimit);
//					response.put("plan_list", plan_list);
//					response.put("addon_label", "Please select the alerts");
//					response.put("addon_note", "The Add-on alerts will be cancelled if your plans expires or cancelled");
//					response.put("Status", 1);
//					response.put("Msg", "Success");
//					
//				}
				
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			log.error("getAddonPlans Excep:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());

		}
		return response;
	}

	@RequestMapping(value = "v4.0/generateaddonpurchaselink", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse generateAddonPurchaseLink(@RequestHeader HttpHeaders header,	@RequestParam("os") String os,	@RequestParam("app_ver") String app_ver,
			@RequestParam("cb_planid") String cb_planid,@RequestParam("cb_addonid") String cb_addonid) {
		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		String[] addonIdList = cb_addonid.trim().split(",");
		cb_planid = cb_planid.trim();
		
		log.info("generateaddonpurchaselink :"+ auth);
		
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);

		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (Exception e) {
			log.error("generateaddonpurchaselink:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		try {
			if (user != null) {			
				
				HashMap<String, Object> metaData = new HashMap<String, Object>();
				metaData.put("regarding", cb_planid);

				com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);

				
				CheckoutExistingRequest checkoutExitingRequest = HostedPage.checkoutExisting().subscriptionId(cb_planid)
						.replaceAddonList(false).redirectUrl(redirtPetUrl)
						.passThruContent(jsonObj.toString()).embed(embedupdate);					
					
				if(!cb_addonid.equalsIgnoreCase("NA")) {
					for (int j = 0; j < addonIdList.length; j++) {
						checkoutExitingRequest = checkoutExitingRequest.addonId(j, addonIdList[j]).addonQuantity(j, 1);
					}
				}
				
				Result res = checkoutExitingRequest.request();
				HostedPage hostedPage = res.hostedPage();

				response.put("addon_checkout_url", hostedPage.url());
				response.put("Status", 1);
				response.put("Msg", "Success");

			}else {
				response.put("Status", 0);
				response.put("Msg", "Addon is not available at the moment. Please try again later.");
			}

		} catch (Exception e) {
			log.error("generateaddonpurchaselink Excep:" + e.getMessage());
			response.put("Status", 0);
			response.put("Error", e.getMessage());
			response.put("Msg", "Addon is not available at the moment. Please try again later.");

		}
		return response;
	}
	
//	@RequestMapping(value = "/app/v5.0/managesubscription", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse managesubscription(@RequestHeader HttpHeaders header,Authentication authentication,
//			@RequestParam("os") String os, @RequestParam("app_ver") String app_ver,
//			@RequestBody JSubManage subMap) {
//
//		JResponse response = new JResponse();
//		UserV4 user = null;
//		String auth = header.getFirst("auth").trim();
//
//		try {
//			user = userServiceV4.verifyAuthV3("authkey", auth);
//		} catch (Exception e) {
//			log.error("getupgradesubplans:userblock : " + e.getMessage());
//			response.put("Status", 0);
//			response.put("Msg", e.getMessage());
//		}
//
//		JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//		if (errResponse != null) {
//			return errResponse;
//		}
//
//		try {
//			if (user != null) {
//				boolean stat = crService.assignGatewayFeature(user, subMap);
//				response.put("Status", 1);
//				response.put("Msg", "Success");
//			}
//
//		} catch (Exception e) {
//			log.error("managesubscription :" + e.getMessage());
//			response.put("Error", e.getLocalizedMessage());
//			response.put("Status", 0);
//			response.put("Msg", "Please try again later.");
//
//		}
//		response.put("Return Time", System.currentTimeMillis());
//		return response;
//	}

//	public int issueCBRefund(String crId,int amount) {
//		try {
//			//https://nimblepetapp-test.chargebee.com/api/v2/credit_notes/TEST-CN-2076/refund
//            String url = "https://"+chargebeeSiteName+".chargebee.com/api/v2/credit_notes/"+crId+"/refund";
//            String user = chargebeeSiteKey;
//            String password = "";
//            
//            String auth = user + ":" + password;
//            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
//            String authHeaderValue = "Basic " + new String(encodedAuth);
//
//            URL obj = new URL(url);
//            HttpURLConnection connection = (HttpURLConnection) obj.openConnection();
//            connection.setRequestMethod("POST");
//            connection.setRequestProperty("Authorization", authHeaderValue);
//            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
//            connection.setDoOutput(true);
//
//            String urlParameters = "refund_amount="+amount;
//
//            try (OutputStream os = connection.getOutputStream()) {
//                byte[] input = urlParameters.getBytes(StandardCharsets.UTF_8);
//                os.write(input, 0, input.length);
//            }
//
//            int responseCode = connection.getResponseCode();
//            return responseCode;
//        } catch (Exception e) {
//        	log.error("issueCBRefund: "+e.getLocalizedMessage());
//           // e.printStackTrace();
//            return 404;
//        }
//	}
	//used in txn service for feature mapping in subscription
	@RequestMapping(value = "v4.0/getproductsubscription", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getProductSubscription(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "timezone", defaultValue = "", required = false) String timezone,
			@RequestParam(defaultValue = "0", required = false) Long gateway_id,
			@RequestParam(defaultValue = "0", required = false) Long monitor_id,
			@RequestParam("backing") String backing,@RequestParam("plan_ver") String plan_ver) {

		JResponse response = new JResponse();
		UserV4 user = null;
		String auth = header.getFirst("auth");
		Timestamp start = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("start : " + start + " getcurrentsubscriptionplan: " + auth);

		try {
			user = userServiceV4.verifyAuthV3("authkey", auth);

		} catch (Exception e) {
			log.error("getCurrentSubscriptionPlan: user by authkey : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		try {
			if (user != null) {

				if (user.isEnable()) {
					int inapp_redirect = 1;
					boolean cb_checkout = true;

					if (timezone == null || timezone.isEmpty() || timezone.equalsIgnoreCase("NA"))
						timezone = "-08:00";

					if (!timezone.contains("-") && !timezone.contains("+"))
						timezone = "+" + timezone.trim();

					response = cbService.getProductSubscriptionFromDB(user, os, app_ver, timezone,
							gateway_id, monitor_id);
				} else {
					response.put("Status", 0);
					response.put("Msg", "User not enabled");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "Please try again.");
			log.error("getCurrentSubscriptionPlan:" + e.getLocalizedMessage());
		}
		Timestamp end = IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC());
		log.info("end : " + end);
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@PostMapping("v5.0/chargebee/createsubscription")
	@ResponseBody
	public ResponseEntity<String> createSubscription(@RequestHeader HttpHeaders header,
													 @RequestBody Map<String, String> userList) {
		log.info("Entered createSubscription ::");
		try {
			String auth = header.getFirst("auth");
			if (!auth.equalsIgnoreCase(validation_authkey)) {
				return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Invalid authkey");
			}
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Authkey not found");
		}

		try {
			userList.forEach((userName, couponCode) -> {
                try {
					UserV4 user = userServiceV4.verifyAuthV4("username", userName);
					cbService.createVetChatSubscription(user.getChargebeeid(), couponCode);
					log.info("created for user : "+user.getUsername());
                } catch (InvalidAuthoException e) {
					log.error("Error in verifyAuthV4 : "+ userName+" : "+e.getLocalizedMessage());
				}
			});

		} catch (Exception e) {
			log.error("createSubscription : " + e.getLocalizedMessage());
		}
		return ResponseEntity.status(HttpStatus.OK).body("Success");
	}

}
