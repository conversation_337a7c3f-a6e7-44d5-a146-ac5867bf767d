package com.nimble.irisservices.controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.DatatypeConverter;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chargebee.Environment;
import com.chargebee.Result;
import com.chargebee.models.HostedPage;
import com.chargebee.models.HostedPage.CheckoutNewRequest;
import com.google.gson.Gson;
import com.nimble.irisservices.dto.JPetprofileV2;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JVPMPlan;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.dto.VPMPetData;
import com.nimble.irisservices.dto.VPMPetDataV2;
import com.nimble.irisservices.dto.VPMRefreshReq;
import com.nimble.irisservices.dto.VPMRefreshResp;
import com.nimble.irisservices.dto.VPMVetRequest;
import com.nimble.irisservices.dto.VPMVetRequestV2;
import com.nimble.irisservices.dto.VPMVetResponse;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.PetSpecies;
import com.nimble.irisservices.entity.VPMChatHistory;
import com.nimble.irisservices.entity.VersionMapping;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IPetSpeciesServices;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IVPMService;

@Controller
public class VPMController {
	private static final Logger log = LogManager.getLogger(VPMController.class);

	@Autowired 
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	ICompanyService companyService;
	
	@Autowired
	@Lazy
	IPetSpeciesServices petSpeciesServices;

	@Autowired
	@Lazy
	IGatewayService gatewayService;
	
	@Autowired
	@Lazy
	ICreditSystemService crService;
	
	@Value("${vpm_ver}")
	private String vpm_ver;

	@Value("${vpm_api_key}")
	private String vpm_api_key;

	@Value("${vpm_url}")
	private String vpm_url;
	
	@Value("${vpm_refresh}")
	private String vpm_refresh;
	
	@Value("${vpm_api_key2}")
	private String vpm_api_key2;

	@Value("${vpm_url2}")
	private String vpm_url2;
	
	@Value("${vpm_refresh2}")
	private String vpm_refresh2; 
	
	private boolean show_vpmdialog=true;
	
	@Value("${vpmbuynow_url}")
	private String vpmbuynow_url;
	
	@Value("${redirtPetUrl}")
	private String redirtPetUrl;

	@Value("${embedupdate}")
	private boolean embedupdate;

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;
	
	@Value("${vpm_user_pwd}")
	private String vpm_user_pwd;
	
	@Value("${vpm_end_chat_url}")
	private String vpm_end_chat_url;
	
	@Value("${vpm_channel_retrive_minutes}")
	private int vpmChannelRetriveMinutes;
	
	@Value("${vpm_continue_chat}")
	private boolean vpmContinueChat;
	
	@Value("${chargebee.addonid}")
	private String activationAddonId;

	@Autowired
	@Lazy
	IVPMService vpmService;
	
	// ========get vetplus details ================
	@RequestMapping(value = "v3.0/getvpmstatus/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getvpmstatus(@PathVariable String autho,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "vpmversion", defaultValue = "v1", required = false) String vpmversion,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
			) {
		
		DateFormat date = new SimpleDateFormat("HH:mm:ss.SSS");
		Calendar cal = Calendar.getInstance();
		//log.info("TimingCheck getVetStatus in: "+date.format(cal.getTime()));
		
		JResponse response = new JResponse();
		log.info("Entering getvpmstatus : "+autho );
		UserV4 user = null;
		ArrayList<Integer> vpmlist = new ArrayList<Integer>();
		try {
			
			boolean vetplus_enable = false;
			cal = Calendar.getInstance();
			//log.info("TimingCheck verifyAuth code in: "+date.format(cal.getTime()));
			user = userServiceV4.verifyAuthV4("authkey", autho);
			cal = Calendar.getInstance();
			//log.info("TimingCheck verifyAuth code out: "+date.format(cal.getTime()));
			if(user != null) {
				cal = Calendar.getInstance();
				//log.info("TimingCheck getVetCallStatus in: "+date.format(cal.getTime()));
				vetplus_enable = companyService.getVetCallStaus(user.getCmpId());
				cal = Calendar.getInstance();
				//log.info("TimingCheck getVetCallStatus out: "+date.format(cal.getTime()));
				int availCnt = 0;
				int tot_cnt = 0;
				String msg = "";
				String buynow_url = "";
				int vpm_redirect = 0;
				
				if(vpm_ver.equalsIgnoreCase("vpm2") && vpmversion.equalsIgnoreCase("v1")) {
					vetplus_enable = false;
					msg = "Update your Waggle app to use WaggleVet.";
				}else {
					if(vetplus_enable) {
						cal = Calendar.getInstance();
						//log.info("TimingCheck getVPMAvailabilty in: "+date.format(cal.getTime()));
						vpmlist = crService.getVPMAvailabilty(user.getId(), "VPM", 0,"NA");
						cal = Calendar.getInstance();
						//log.info("TimingCheck getVPMAvailabilty out: "+date.format(cal.getTime()));
						availCnt = vpmlist.get(0);
						tot_cnt =  vpmlist.get(1);
						if(availCnt > 0) {
							//msg =  " You have " + availCnt + "  VetChat left in your current plan";
							msg = "Welcome to WaggleVet!\n" + 
									"Our vet will get in touch with you shortly!";
							vetplus_enable = true;
						}else {		
							if (!app_ver.isEmpty() && !os.isEmpty()) {
								VersionMapping verObj = crService.getVersionMapping(app_ver, os);
								if (verObj != null) {
									show_vpmdialog = verObj.isShow_vpmdialog();
								}
							}
							//vetplus_enable = false;
							if(show_vpmdialog == true) {							
								//String note = "\n To purchase VetChat credits, use the same email ID ("+ user.getEmail()+") that was used to create your Waggle App account.";
								msg = "You need WaggleVet Credits. Use your Waggle account's registered email to buy credits.\n[" +user.getEmail()+ "]" ;
								buynow_url = vpmbuynow_url;
								vpm_redirect = 1; // 0 - web , 1-CB , 2-ios, 3-android
							}
							else{
								// cmsg = "Thanks for testing our Waggle VetChat. Our Team will be contacting you to get your feedback.";
								msg = "Not available at this moment. Please try later.";
							}
						}
					}else {
						vetplus_enable = false;
						msg = "Contact support to enable this feature.";
					}
				}
				response.put("Msg",msg);
				response.put("show_vpmdialog", show_vpmdialog);
				response.put("Status", 1);
				response.put("is_vpm", vetplus_enable);
				response.put("avail_cnt", availCnt);
				response.put("tot_cnt",tot_cnt);
				response.put("buynow_url",buynow_url);
				response.put("cb_id",user.getChargebeeid());
				response.put("vpm_redirect",vpm_redirect);
				response.put("cancel_button","Later");
				response.put("okay_button","Start trial");

				
			}else {
				response.put("Status", 0);
				response.put("Msg", "Invalid user");
			}
	
		} catch (InvalidAuthoException e) {
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			response.put("Error", e.getLocalizedMessage());
			log.error("getvpmstatus : "+e.getLocalizedMessage());
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
			response.put("Msg", "WaggleVet is not available at the moment. Please try again later.");
			log.error("getvpmstatus : "+e.getLocalizedMessage());
			return response;
		}
		cal = Calendar.getInstance();
		//log.info("TimingCheck getVetStatus out: "+date.format(cal.getTime()));
		
		return response;
	}
	
	@RequestMapping(value = "v3.0/updatevpmcount/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateVPMCount(@PathVariable String autho, @RequestParam("feature") String feature,@RequestParam("os") String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "channelsid",defaultValue = "NA", required = false) String channelId
			) {
		JResponse response = new JResponse();
		log.info("Entering updateVPMCount : "+autho);
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
			
			if(user != null) {
				long feature_id = crService.getFeatureId(feature.trim());		
					
				if( !vpmContinueChat ) {
					boolean status3 = crService.saveOrUpdateUserFeatureCount(user.getId(), feature_id);
					boolean status1 = crService.saveOrUpdateUserTxn(user.getId(), feature_id);
					boolean status2 = crService.saveOrUpdateUserTxnHistory(user.getId(), feature_id);
					
					log.info("updatevpmcount : "+status1+" : "+status2);

					response.put("Msg", "Your chat has been ended.Please start a new chat.");
					response.put("Status", 1);
					
				} else {
					
					if( channelId.trim().isEmpty() || channelId.equalsIgnoreCase("NA") ) {
						channelId = vpmService.getchannelIdByUserId(user.getId());
					}
					
					if( channelId.equalsIgnoreCase("NA") ) {
						response.put("Msg", "Your chat has been ended.Please start a new chat.");
						response.put("Status", 1);
						return response;
					}
					
					JSONObject endChatParams = new JSONObject();
					endChatParams.putOnce("channel_sid", channelId);
					String endChatRes = postV2(vpm_end_chat_url, endChatParams.toString());
					JSONObject endChatResJson = new JSONObject(endChatRes);
					if( endChatResJson.getJSONArray("message").get(0).toString().equalsIgnoreCase("End chat successful") ) {
						response.put("Msg", "Your chat has been ended.Please start a new chat.");
						response.put("Status", 1);
						log.info("chat ended for channel id : ");
					} else {
						response.put("Status", 0);
						response.put("Msg", "Unknown error, Please try after sometime.");
					}
				}
				
			}
			else {
				response.put("Msg", "Invalid User");
				response.put("Status", 0);
			}
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Unknown error, Please try after sometime");
			response.put("Error", e.getLocalizedMessage());

			log.error("updateVPMCount :" +  e.getLocalizedMessage());

			return response;
		}
	}
	
//	@RequestMapping(value = "v4.0/updatevpmtransaction", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public void updatevpmtransaction(@PathVariable String apikey,@RequestParam("vpmuserid") String vpmuserid) {
//		log.info("Entering updatevpmtransaction : "+vpmuserid);
//		try {
//			if(apikey.equalsIgnoreCase("rthawki")) {
//				long userid = userServiceV4.getUserbyVPMid(vpmuserid);
//				
//				if(userid>0) {
//					long feature_id = crService.getFeatureId("vpm");		
//							
//					boolean status1 = crService.saveOrUpdateUserTxn(userid, feature_id);
//					boolean status2 = crService.saveOrUpdateUserTxnHistory(userid, feature_id);
//					
//					log.info("updatevpmtransaction : "+status1+" : "+status2);
//					
//				}
//				else {
//					log.info("updatevpmtransaction : vpm user not found.");
//				}
//			}else {
//				log.info("updatevpmtransaction : apikey not matched");
//			}
//		} catch (Exception e) {
//			log.error("updateVPMCount :" +  e.getLocalizedMessage());
//		}
//	}

	@ResponseBody
	@RequestMapping(value = "/updatevpmtransaction", headers = "Accept=application/json")
	public void updateVPMtransaction(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		log.info("Entered updatevpmtransaction.");

		BufferedReader rd = request.getReader();
		StringBuffer result = new StringBuffer();
		String line = "";

		while ((line = rd.readLine()) != null) {
			result.append(line);
		}
		rd.close();
		log.info("VPM response : "+result);

		
		try {
			JSONObject json = new JSONObject(result.toString());

			JSONObject lev1 = json.getJSONObject("vpmdata");
			String vpmuserid = lev1.get("vpmuserid").toString();
			String apikey = lev1.get("apikey").toString();
			
			log.info("Entering updatevpmtransaction block : "+vpmuserid);

			if(apikey.equalsIgnoreCase("rthawki123")) {
				long userid = userServiceV4.getUserbyVPMid(vpmuserid);
				
				if(userid>0) {
					long feature_id = crService.getFeatureId("vpm");		
							
					boolean status1 = crService.saveOrUpdateUserTxn(userid, feature_id);
					boolean status2 = crService.saveOrUpdateUserTxnHistory(userid, feature_id);
					
					log.info("updatevpmtransaction : "+status1+" : "+status2);
					
				}
				else {
					log.info("updatevpmtransaction : vpm user not found.");
				}
			}else {
				log.info("updatevpmtransaction : apikey not matched");
			}
		} catch (Exception e) {
			log.error("updateVPMCount :" +  e.getLocalizedMessage());
		}
	}

	@RequestMapping(value = "v3.0/getvpmdetails/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getvpmdetails(@PathVariable String autho, @RequestBody JPetprofileV2 jpetprofile,
			@RequestParam("os") String os,
			@RequestParam(value = "vpmversion", defaultValue = "oldvpm", required = false) String vpmversion) {

		JResponse response = new JResponse();
		log.info("Entering getvpmdetails : " + autho);
		try {

			if (jpetprofile != null) {
				Map<String, String> map = new HashMap<String, String>();
				UserV4 user = userServiceV4.verifyAuthV3("authkey", autho);
				try {
					map = userServiceV4.getUserId_cmpIdByAuthV2(autho);

				} catch (InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for authkey : " + autho);
					return response;
				}

				long userId = Long.valueOf(map.get("user_id"));
				long cmpType_id = Long.valueOf(map.get("cmpType_id"));

				jpetprofile.setUser_id(userId);

				if (cmpType_id == 3) {
					PetProfile pp = null;
					if (vpmversion.equalsIgnoreCase("v2")) {
						if (jpetprofile.isUpdate()) {
							String gateway_name = jpetprofile.getName();
							Pattern validateName = Pattern.compile("^[A-Za-z0-9_-]+$");
							Matcher matcher = validateName.matcher(gateway_name);

							if (!matcher.matches()) {
								log.info("pet name contains special chars");
								response.put("Status", 0);
								response.put("Msg", RegisterUserError.ER009);
								return response;
							}
							if (gateway_name.length() > 20) {
								response.put("Status", 0);
								response.put("Msg", "Pet Name should not exceed 20 char");
								log.info(gateway_name + " exceeds 20 char");
								return response;
							}
							boolean isExist = false;

							isExist = petSpeciesServices.checkPetnameExist(jpetprofile.getName(),
									jpetprofile.getUser_id(), jpetprofile.getId());
							if (isExist == true) {
								response.put("Status", 0);
								response.put("Msg", RegisterUserError.petNameUserMessage);
								return response;
							}

							if (!jpetprofile.getBreed().isEmpty() && !jpetprofile.getName().isEmpty()
									&& !jpetprofile.getSex().isEmpty()) {

								int month = (Integer.parseInt(jpetprofile.getAgeYr()) * 12)
										+ Integer.parseInt(jpetprofile.getAgeMonth());

								Date birth_date = gatewayService.getBirthDate("MONTH", String.valueOf(month));
								SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
								String date = sf.format(birth_date);

								jpetprofile.setBirth_date(date);
								jpetprofile.setUser_id(userId);
								PetSpecies species = petSpeciesServices
										.getPetSpeciesByName(jpetprofile.getSpecieName());
								long speciesid = 1;

								if (species != null)
									speciesid = species.getId();

								jpetprofile.setSpeciesid(speciesid);
								pp = petSpeciesServices.createPetprofile(jpetprofile);
								jpetprofile.setId(pp.getId());

								if (jpetprofile.getGatewayId() != 0) {
									boolean issuccess = gatewayService.updateGatewayName(jpetprofile.getName(),
											String.valueOf(jpetprofile.getGatewayId()));
									log.info("GatewayName updated with respect to petname");
								}

							}
						} else if (jpetprofile.getId() > 0) {
							List<PetProfile> ppList = petSpeciesServices.getPetprofile("id",
									String.valueOf(jpetprofile.getId()));
							if (!ppList.isEmpty())
								pp = ppList.get(0);
						} else {
							log.info("createPetProfile: Error : mandatory fields are missing");
							response.put("Status", 0);
							response.put("Msg", "Enter all the mandatory fields like petname,age,sex & breed");
							return response;
						}
					} else {

						String gateway_name = jpetprofile.getName();
						Pattern validateName = Pattern.compile("^[A-Za-z0-9_-]+$");
						Matcher matcher = validateName.matcher(gateway_name);

						if (!matcher.matches()) {
							log.info("pet name contains special chars");
							response.put("Status", 0);
							response.put("Msg", RegisterUserError.ER009);
							return response;
						}
						if (gateway_name.length() > 20) {
							response.put("Status", 0);
							response.put("Msg", "Pet Name should not exceed 20 char");
							log.info(gateway_name + " exceeds 20 char");
							return response;
						}
						boolean isExist = false;

						isExist = petSpeciesServices.checkPetnameExist(jpetprofile.getName(), jpetprofile.getUser_id(),	jpetprofile.getId());
						if (isExist == true) {
							response.put("Status", 0);
							response.put("Msg", RegisterUserError.petNameUserMessage);
							return response;
						}

						if (!jpetprofile.getBreed().isEmpty() && !jpetprofile.getName().isEmpty()
								&& !jpetprofile.getSex().isEmpty()) {

							int month = (Integer.parseInt(jpetprofile.getAgeYr()) * 12)
									+ Integer.parseInt(jpetprofile.getAgeMonth());

							Date birth_date = gatewayService.getBirthDate("MONTH", String.valueOf(month));
							SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
							String date = sf.format(birth_date);

							jpetprofile.setBirth_date(date);
							jpetprofile.setUser_id(userId);
							PetSpecies species = petSpeciesServices.getPetSpeciesByName(jpetprofile.getSpecieName());
							long speciesid = 1;

							if (species != null)
								speciesid = species.getId();

							jpetprofile.setSpeciesid(speciesid);
							pp = petSpeciesServices.createPetprofile(jpetprofile);
							jpetprofile.setId(pp.getId());

							if (jpetprofile.getGatewayId() != 0) {
								boolean issuccess = gatewayService.updateGatewayName(jpetprofile.getName(),
										String.valueOf(jpetprofile.getGatewayId()));
								log.info("GatewayName updated with respect to petname");
							}

						}

					}
					if (pp != null) {
						if( vpmContinueChat ) {
							VPMChatHistory vpmChatHistory = vpmService
									.checkVPMChatHistory(String.valueOf(jpetprofile.getUser_id()),vpmChannelRetriveMinutes);
							if (vpmChatHistory != null) {
								try {
									VPMVetResponse vpmData = new VPMVetResponse();
									String twilio_token = "NA";
									response = getVPMrefreshToken(map, os, vpmChatHistory.getVpmUserId());

									twilio_token = ((VPMRefreshResp) response.getResponse().get("data")).getTwilio_token();

									vpmData.setTwilio_token(twilio_token);
									vpmData.setIdentity(vpmChatHistory.getVpmUserId());
									vpmData.setVet(vpmChatHistory.getVetId());
									vpmData.setChannel_sid(vpmChatHistory.getChannelSid());
									response.put("data", vpmData);
									response.put("Status", 1);
									response.put("Msg", "Success");
								} catch (Exception e) {
									response.put("Status", 0);
									log.error("getvpmdetails :" + e.getLocalizedMessage());
									response.put("Msg", "WaggleVet is not available at the moment. Please try again later.");
									response.put("Error", e.getLocalizedMessage());
								}
								return response;
							} else {

								response = getVPMData(pp, user, vpmversion, os);
								boolean vpmChannelStored =  vpmService.saveVPMChannel(response);
							}
						} else {
							response = getVPMData(pp, user, vpmversion, os);
							boolean vpmChannelStored =  vpmService.saveVPMChannel(response);
						}
						
					}

				} else {
					response.put("Status", 0);
					response.put("Msg", "Invalid company type");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Empty pet profile received");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			log.error("getvpmdetails :" + e.getLocalizedMessage());
			response.put("Msg", "WaggleVet is not available at the moment. Please try again later.");
			response.put("Error", e.getLocalizedMessage());

		}
		return response;
	}
	
	private JResponse getVPMrefreshToken(Map<String, String> map, String os, String identity) {

		JResponse response = new JResponse();

		long userId = Long.valueOf(map.get("user_id"));
		long cmpType_id = Long.valueOf(map.get("cmpType_id"));

		if (cmpType_id == 3) {
			if (vpm_ver.equalsIgnoreCase("vpm2")) {
				Gson gson = new Gson();
				String url = vpm_refresh2 + "api_key=" + vpm_api_key2 + "&identity=" + identity + "&dev_type=" + os;

				String result = getV2(url, "");
				JSONObject json1 = new JSONObject(result);
				VPMRefreshResp vpmResp = new VPMRefreshResp();

				if (json1.get("success").toString().equalsIgnoreCase("true")) {

					JSONObject vpmResObj = json1.getJSONObject("data");

					vpmResObj.put("identity", identity);
					vpmResObj.put("twilio_token", vpmResObj.get("twilioToken"));

					vpmResp = gson.fromJson(vpmResObj.toString(), VPMRefreshResp.class);

					userServiceV4.saveOrUpdateVPM_id(userId, vpmResp.getIdentity());

					response.put("Status", 1);
					response.put("Msg", "Success");

				} else {
					response.put("Status", 0);
					response.put("Error", json1.get("message").toString());
					response.put("Msg", "WaggleVet is not available at the moment. Please try again later.");
				}
				response.put("data", vpmResp);
			} else {
				VPMRefreshReq vetReq = new VPMRefreshReq(identity, vpm_api_key);

				Gson gson = new Gson();
				String json = gson.toJson(vetReq);

				String result = post(vpm_refresh, json);
				JSONObject json1 = new JSONObject(result);
				VPMRefreshResp vpmResp = new VPMRefreshResp();

				if (json1.get("success").toString().equalsIgnoreCase("true")) {

					JSONArray respList = json1.getJSONArray("data");
					JSONObject vpmResObj = respList.getJSONObject(0);

					vpmResp = gson.fromJson(vpmResObj.toString(), VPMRefreshResp.class);

					userServiceV4.saveOrUpdateVPM_id(userId, vpmResp.getIdentity());

					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					response.put("Status", 0);
					response.put("Error", json1.get("message").toString());
					response.put("Msg", "WaggleVet is not available at the moment. Please try again later.");
				}
				response.put("data", vpmResp);
			}
		} else {
			response.put("Status", 0);
			response.put("Msg", "Invalid company type");
		}
		return response;
	}
	
	
	public JResponse getVPMData(PetProfile petprofile, UserV4 user,String vpmAppVersion,String dev_type) {
		JResponse resp = new JResponse();
		try {
			long user_id = user.getId();
			
			if(vpm_ver.equalsIgnoreCase("vpm2")) {
				
				VPMPetDataV2 petdata = new VPMPetDataV2(Float.parseFloat(petprofile.getWeight()), petprofile.getSex(),
						petprofile.getBreed(),0, petprofile.getName(), null);
				
				String email = user.getEmail();
				
				if(email.equalsIgnoreCase("na"))
					email= user.getUsername();
				
				petdata.setColor("");
				
				VPMVetRequestV2 vetReq = new VPMVetRequestV2(vpm_api_key2, user.getFirstname(), user.getLastname(), email,
						user.getMobileno(),	dev_type, user.getUpdatedOn(), petdata);
				
				Gson gson = new Gson();

				String json = gson.toJson(vetReq);
				
				String result = postV2(vpm_url2, json);
				JSONObject json1 = new JSONObject(result);
				VPMVetResponse vpmResp =new VPMVetResponse();
				
				if(json1.get("success").toString().equalsIgnoreCase("true")) {
					JSONObject dataObj = json1.getJSONObject("data");			
					JSONObject vetObj = dataObj.getJSONObject("vet");
					
					vpmResp =new VPMVetResponse(dataObj.getString("identity"), dataObj.getString("twilioToken"),
							dataObj.getString("channel"), vetObj.getString("id"));
					
					userServiceV4.saveOrUpdateVPM_id(user_id, vpmResp.getIdentity());
					
					resp.put("Status", 1);
					resp.put("Msg", "Success");
				}else {
					resp.put("Status", 0);
					resp.put("Msg","WaggleVet is not available at the moment. Please try again later.");
					resp.put("Error",json1.get("message").toString());
				}
				resp.put("data", vpmResp);
			}else {
				VPMPetData petdata = new VPMPetData(petprofile.getId(), petprofile.getPetSpecies().getId(),
						Float.parseFloat(petprofile.getWeight()), petprofile.getSex(), petprofile.getBreed(), petprofile.getName());
				
				String role = "user";
				String is_vet_protected = "true";
				String email = user.getEmail();
				
				if(email.equalsIgnoreCase("na"))
					email= user.getUsername();
				
				VPMVetRequest vetReq = new VPMVetRequest(vpm_api_key, role, user.getFirstname(), user.getLastname(), email,
						user.getMobileno(), is_vet_protected, petdata) ;
				
				Gson gson = new Gson();
				String json = gson.toJson(vetReq);
				
				String result = post(vpm_url, json);
				JSONObject json1 = new JSONObject(result);
				VPMVetResponse vpmResp =new VPMVetResponse();
				
				if(json1.get("success").toString().equalsIgnoreCase("true")) {
					JSONArray respList = json1.getJSONArray("data");			
					JSONObject vpmrespObj = respList.getJSONObject(0);
					
					vpmResp = gson.fromJson(vpmrespObj.toString(), VPMVetResponse.class);
					
					userServiceV4.saveOrUpdateVPM_id(user_id, vpmResp.getIdentity());
					if(vpmAppVersion.equalsIgnoreCase("oldvpm")) {
						long feature_id = crService.getFeatureId("vpm");
						boolean status1 = crService.saveOrUpdateUserTxn(user_id, feature_id);
						boolean status2 = crService.saveOrUpdateUserTxnHistory(user_id, feature_id);
					}
					resp.put("Status", 1);
					resp.put("Msg", "Success");
				}else {
					resp.put("Status", 0);
					resp.put("Msg","WaggleVet is not available at the moment. Please try again later.");
					resp.put("Error",json1.get("message").toString());
				}
				resp.put("data", vpmResp);	
			}

			
			
			
		}catch (Exception e) {
			resp.put("Status", 0);
			resp.put("Error", e.getLocalizedMessage());
			resp.put("Msg", "WaggleVet is not available at the moment. Please try again later.");
			log.error(" getVPMData: "+e.getLocalizedMessage());
		}
		
		return resp;
	}
	
	public JResponse getVPMDataV2(PetProfile petprofile, UserV4 user,String vpmVersion) {
		JResponse resp = new JResponse();
		try {
			long user_id = user.getId();
	
			VPMPetData petdata = new VPMPetData(petprofile.getId(), petprofile.getPetSpecies().getId(),
					Float.parseFloat(petprofile.getWeight()), petprofile.getSex(), petprofile.getBreed(), petprofile.getName());
			
			String role = "user";
			String is_vet_protected = "true";
			String email = user.getEmail();
			if(email.equalsIgnoreCase("na"))
				email= user.getUsername();
			VPMVetRequest vetReq = new VPMVetRequest(vpm_api_key, role, user.getFirstname(), user.getLastname(), email,
					user.getMobileno(), is_vet_protected, petdata) ;
			
			Gson gson = new Gson();
			String json = gson.toJson(vetReq);
			
			String result = post(vpm_url, json);
			JSONObject json1 = new JSONObject(result);
			VPMVetResponse vpmResp =new VPMVetResponse();
			
			if(json1.get("success").toString().equalsIgnoreCase("true")) {
				JSONObject vpmResObj = json1.getJSONObject("data");
				
				String vet = vpmResObj.getJSONObject("vet").getString("id");
				String twilioToken = vpmResObj.getString("twilioToken");
				String channel = vpmResObj.getString("channel");
				String identity = vpmResObj.getString("identity");
				
				vpmResp = new VPMVetResponse(identity, twilioToken, channel, vet);
				
				userServiceV4.saveOrUpdateVPM_id(user_id, vpmResp.getIdentity());
				
				resp.put("Status", 1);
				resp.put("Msg", "Success");
			}else {
				resp.put("Status", 0);
				resp.put("Msg","WaggleVet is not available at the moment. Please try again later.");
				resp.put("Error",json1.get("message").toString());
			}

			resp.put("data", vpmResp);
			
			
		}catch (Exception e) {
			resp.put("Status", 0);
			resp.put("Error",e.getLocalizedMessage());
			resp.put("Msg", "WaggleVet is not available at the moment. Please try again later.");
			log.error(" getVPMData: "+e.getLocalizedMessage());
		}
		
		return resp;
	}
	
	public String post(String url, String jsonString) {
		try {
			StringEntity postingString = null;
			HttpClient httpclient = HttpClients.createDefault();
			HttpPost httppost = new HttpPost(url);
			postingString = new StringEntity(jsonString);
			httppost.setEntity(postingString);
			httppost.setHeader("Content-type", "application/json");
			HttpResponse response = httpclient.execute(httppost);
			String serviceResponse = EntityUtils.toString(response.getEntity());

			return serviceResponse;
		} catch (Exception e) {
			log.error(e.getMessage());
			return null;
		}
	}
	
	public String postV2(String url, String jsonString) {
		try {
			StringEntity postingString = null;
			HttpClient httpclient = HttpClients.createDefault();
			HttpPost httppost = new HttpPost(url);
			postingString = new StringEntity(jsonString);
			httppost.setEntity(postingString);
	        String encoding = DatatypeConverter.printBase64Binary(vpm_user_pwd.getBytes("UTF-8"));
	        httppost.setHeader("Authorization", "Basic " + encoding);

			httppost.setHeader("Content-type", "application/json");
			HttpResponse response = httpclient.execute(httppost);
			String serviceResponse = EntityUtils.toString(response.getEntity());

			return serviceResponse;
		} catch (Exception e) {
			log.error(e.getMessage());
			return null;
		}
	}
	public String getV2(String url, String jsonString) {
		try {

			HttpClient Client = HttpClients.createDefault();

	        HttpGet httpGet = new HttpGet(url);
	        String encoding = DatatypeConverter.printBase64Binary(vpm_user_pwd.getBytes("UTF-8"));
	        httpGet.setHeader("Authorization", "Basic " + encoding);

	        HttpResponse response = Client.execute(httpGet);

	        BufferedReader breader = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
	        StringBuilder responseString = new StringBuilder();
	        String line = "";
	        
	        while ((line = breader.readLine()) != null) {
	            responseString.append(line);
	        }
	        breader.close();       
	        
	        String repsonseStr = responseString.toString();
	        return  repsonseStr;
	        
		} catch (Exception e) {
			log.error(e.getMessage());
			return null;
		}
		
	}

	@RequestMapping(value = "v3.0/getvpmrefreshtoken/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getvpmRefreshToken(@PathVariable String autho,@RequestParam("identity") String identity, @RequestParam("os") String os,
			@RequestParam(value="vpmversion", defaultValue = "oldvpm", required = false) String vpmversion) {
		JResponse response = new JResponse();
		log.info("Entering getvpmdetails : "+autho);
		try {

			Map<String, String> map = new HashMap<String, String>();
			UserV4 user = userServiceV4.verifyAuthV3("authkey", autho);
			try {
				map = userServiceV4.getUserId_cmpIdByAuthV2(autho);
				
			}catch(InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : "+autho);
				return response;
			}

			if(map.isEmpty()) {
				response.put("Status", 0);
				response.put("Msg", "invalid user");
				return response;
			}
			
			response = getVPMrefreshToken(map, os, identity);			

		} catch (Exception e) {
			response.put("Status", 0);
			log.error("getvpmRefreshToken :" +  e.getLocalizedMessage());
			response.put("Msg", "WaggleVet is not available at the moment. Please try again later.");
			response.put("Error", e.getLocalizedMessage());

		}
		log.info("Exit getvpmRefreshToken");
		return response;
	}

	@RequestMapping(value = "v4.0/getvpmplans/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getVPMPlans(@PathVariable String auth,	@RequestParam("os") String os,	@RequestParam("app_ver") String app_ver) {
		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();
		log.info("getvpmplans :"+ auth);
		UserV4 user = null;
		
		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (Exception e) {
			log.error("getvpmplans:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		try {
			if (user != null) {
				List<JVPMPlan> plan_list= crService.getVPMPlanList("VPM-Plan");
				
				response.put("plan_list", plan_list);
				response.put("Status", 1);
				response.put("Msg", "Success");
				
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			log.error("getVPMPlans Excep:" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());

		}
		return response;
	}

	@RequestMapping(value = "v4.0/generatevpmpurchaselink/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse generateVPMPurchaseLink(@PathVariable String auth,	@RequestParam("os") String os,	@RequestParam("app_ver") String app_ver,
			@RequestParam("cb_planid") String cb_planid) {
		JResponse response = new JResponse();
		Map<String, Object> resp = new HashMap<String, Object>();
		log.info("generateVPMPurchaseLink :"+ auth);
		UserV4 user = null;
		
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);

		try {
			user = userServiceV4.verifyAuthV4("authkey", auth);
		} catch (Exception e) {
			log.error("getvpmplans:userblock : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", e.getMessage());
		}

		try {
			if (user != null) {
				String[] planDetails =crService.getCouponId(cb_planid);
				String cb_couponid = planDetails[0];
				String cb_addonid = planDetails[1];
				
				HashMap<String, Object> metaData = new HashMap<String, Object>();
				metaData.put("regarding", cb_planid);

				com.chargebee.org.json.JSONObject jsonObj = new com.chargebee.org.json.JSONObject(metaData);

				// In CB - v3 we can create multiple subscription for a single user

				CheckoutNewRequest checkoutNewRequest = HostedPage.checkoutNew().subscriptionPlanId(cb_planid)
						.subscriptionPlanQuantity(1).customerId(user.getChargebeeid()).addonId(0, activationAddonId).addonQuantity(0, 1).addonBillingCycles(0, 1);

				if(!cb_addonid.isEmpty() && !cb_addonid.equalsIgnoreCase("NA"))
					checkoutNewRequest = checkoutNewRequest.addonId(1, cb_addonid).addonQuantity(1, 1);
				
				Result res = checkoutNewRequest.redirectUrl(redirtPetUrl).passThruContent(jsonObj.toString())
						.embed(embedupdate).request();
				HostedPage hostedPage = res.hostedPage();

				response.put("vpm-checkout-url", hostedPage.url());
				response.put("Status", 1);
				response.put("Msg", "Success");

			}else {
				response.put("Status", 0);
				response.put("Msg", "WaggleVet is not available at the moment. Please try again later.");
			}

		} catch (Exception e) {
			log.error("getVPMPlans Excep:" + e.getMessage());
			response.put("Status", 0);
			response.put("Error", e.getMessage());
			response.put("Msg", "WaggleVet is not available at the moment. Please try again later.");

		}
		return response;
	}

}
