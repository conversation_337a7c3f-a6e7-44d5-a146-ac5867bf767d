package com.nimble.irisservices.controller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class DynamicCommandController {

	private static final Logger log = LogManager.getLogger(DynamicCommandController.class);

//	@Autowired
//	@Lazy
//	IDynamicCmdService dynamicCmdService;
//
//	@Autowired
//	@Lazy
//	IGatewayService gatewayService;
//
//	@Autowired
//	@Lazy
//	IOptimizedV4Service optimizedService;

//	@RequestMapping(value = "v3.0/setdevicemode/{autho}" , method = RequestMethod.POST, headers="Accept=application/json")
//	public JResponse setDeviceMode(@PathVariable String autho, @RequestParam("devicemode") String devicemode, 
//			@RequestParam("gatewayId") long gatewayId) {
//		log.info("Entered :: setPowerSaveMode :: "+autho);
//		JResponse response = new JResponse();		
//
//		try {
//			UserV4 user = optimizedService.verifyAuthV4("authkey",autho);
//		}catch(InvalidAuthoException ex) {
//			response.put("Status", 0);
//			response.put("Msg", "invalid authkey");
//			response.put("Error", ex.getLocalizedMessage());
//			log.error("Exception while getting user for auth : "+ex.getLocalizedMessage());
//			return response;
//		}
//
//		String cmdValue = "";
//		boolean isSuccess = false;
//		String mode = "";
//		if(devicemode.equalsIgnoreCase("ultrapowersavemode")) {
//			mode = "ultralowpowermode";
//			cmdValue = "%rthawki,,prptinterval=720#";
//			log.info("Request received to set gateway : "+gatewayId +" to ultrapowersavemode");
//		}else if(devicemode.equalsIgnoreCase("powersavemode")) {
//			mode = "normallowpowermode";
//			cmdValue = "%rthawki,,prptinterval=360#";
//			log.info("Request received to set gateway : "+gatewayId +" to powersavemode");
//		}else if(devicemode.equalsIgnoreCase("normalmode")) {
//			mode = "normalmode";
//			cmdValue = "%rthawki,,prptinterval=60#";
//			log.info("Request received to set gateway : "+gatewayId +" to powersavemode");
//		}
//		try {			
//			JGatewayDetails gateway = optimizedService.getJGatewayDetails("id",gatewayId+"");
//
//			if(gateway != null) {
//				response = dynamicCmdService.insertPowerModeDynamicCmd(gateway, cmdValue, 1, "notsent",mode);				
//				if(response == null) {
//					response.put("Status", 0);
//					response.put("Msg", "Failed to insert dynamic command");
//					log.info("Failed to insert dynamic command for gateway id : "+gatewayId);
//				}
//			}else {
//				response.put("Status", 0);
//				response.put("Msg", "Gateway not found");
//				log.info("Gateway not found for gateway id : "+gatewayId);
//			}
//
//		}catch(Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Exception occured while inserting dynamic command for gateway id : "+gatewayId);
//			response.put("Error", e.getLocalizedMessage());
//			log.error("Gateway not found for gateway id : "+gatewayId);
//		}
//		return response ;
//	}
//
//	@RequestMapping(value = "v3.0/getpowermodestatus/{autho}" , method = RequestMethod.GET, headers="Accept=application/json")
//	public JResponse getCurrentPowerMode(@PathVariable String autho, @RequestParam("gatewayId") long gatewayId) {
//		log.info("Entered :: getCurrentPowerMode : "+autho);
//		JResponse response = new JResponse();
//		try {
//			UserV4 user = optimizedService.verifyAuthV4("authkey",autho);
//		}catch(InvalidAuthoException ex) {
//			response.put("Status", 0);
//			response.put("Msg", "invalid authkey");
//			response.put("Error", ex.getLocalizedMessage());
//			log.error("Exception while getting user for auth : "+ex.getLocalizedMessage());
//			return response;
//		}
//		try {
//			response = dynamicCmdService.getDevicePowerMode(gatewayId+"");
//			
//		}catch(Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Exception occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("Error while gatting device power save mode : "+e.getLocalizedMessage() );
//		}
//
//		return response;
//	}
//	
//
//	@RequestMapping(value = "v3.0/setadventuremode/{autho}" , method = RequestMethod.POST, headers="Accept=application/json")
//	public JResponse setAdventureMode(@PathVariable String autho, @RequestParam("enable") String enable, 
//			@RequestParam("gatewayId") long gatewayId) {
//		log.info("Entered :: setAdventureMode :: "+autho);
//		JResponse response = new JResponse();
//
//		try {
//			UserV4 user = optimizedService.verifyAuthV4("authkey",autho);
//		}catch(InvalidAuthoException ex) {
//			response.put("Status", 0);
//			response.put("Msg", "invalid authkey");
//			response.put("Error", ex.getLocalizedMessage());
//			log.error("Exception while getting user for auth : "+ex.getLocalizedMessage());
//			return response;
//		}
//		
//		String cmdValue = "";
//		boolean isSuccess = false;
//		
//		JGatewayDetails gateway = optimizedService.getJGatewayDetails("id",gatewayId+"");
//		if(gateway != null) {
//			
//			
//			if(enable.equalsIgnoreCase("on")) {				
//				cmdValue = "%rthawki,,gpstrack=on#";
//			}else if(enable.equalsIgnoreCase("off")) {
//				cmdValue = "%rthawki,,gpstrack=off#";
//			}
//			
//			response = dynamicCmdService.insertAdventureModeDynamicCmd(gateway, cmdValue, 1, "notsent",enable);				
//			if(response == null ) {
//				response.put("Status", 0);
//				response.put("Msg", "Failed to insert dynamic command");
//				log.info("Failed to insert dynamic command for gateway id : "+gatewayId);
//			}
//		}else {
//			response.put("Status", 0);
//			response.put("Msg", "Gateway not found for gateway id : "+gatewayId);
//			log.info("Gateway not found for gateway id : "+gatewayId);
//		}
//		return response;		
//	}
}
