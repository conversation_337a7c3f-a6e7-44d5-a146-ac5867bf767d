package com.nimble.irisservices.controller;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import javax.servlet.http.HttpServletRequest;

import freemarker.template.Configuration;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.ocpsoft.prettytime.PrettyTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.CountryCode;
import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dao.IGatewayDao;
import com.nimble.irisservices.dto.ActivateUser;
import com.nimble.irisservices.dto.AmazonReviewList;
import com.nimble.irisservices.dto.AmazonReviewStatus;
import com.nimble.irisservices.dto.AmazonUserReview;
import com.nimble.irisservices.dto.JCreateGateway;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JRVAnswer;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JUserDeviceInfo;
import com.nimble.irisservices.dto.JValidateString;
import com.nimble.irisservices.dto.Jorder;
import com.nimble.irisservices.dto.UpdatePassword;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.OneTimePassword;
import com.nimble.irisservices.entity.RVAnswer;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserToken;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Email;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Device_history;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Ordermap;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGroupServices;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IOptimizedV4Service;
import com.nimble.irisservices.service.IRVCentricDetailsService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IReportServiceV4;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

import freemarker.template.Template;

@Controller
public class UserControllerV4 {

	private static final Logger log = LogManager.getLogger(UserControllerV4.class);

	@Autowired
	IReportService reportService;

	@Autowired
	@Lazy
	IReportServiceV4 rptServicev4;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IOptimizedV4Service optimizedService;

	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	@Value("${niomip}")
	private String niomIP;

	@Value("${niomauthkey}")
	private String niomAuthKey;

	@Value("${showamazonrateus}")
	private boolean showamazonrateus;

	@Value("${showfeedback}")
	private boolean showfeedback;

	@Autowired
	@Lazy
	IGroupServices groupservices;

	@Autowired
	@Lazy
	IAlertCfgService alertCfgService;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Value("${send_registeruseremail_simactivation_to_microservice}")
	private boolean sendActivateUserDataToSQS_Microservice;

	@Value("${amazonrateuscount}")
	private int amazonrateuscount;

	@Value("${amazonredirecturl}")
	private String amazonredirecturl;

	@Value("${walmartredirecturl}")
	private String walmartredirecturl;

	PrettyTime prettyTime = new PrettyTime();

	@Autowired
	@Lazy
	IGatewayDao gatewayDao;

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	IUserService userService;

	@Autowired
	UserController userController;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Value("${supportcontactnumber}")
	private String supportContactNumber;

	@Value("${supportemail}")
	private String supportContactEmail;

	@Autowired
	Helper _helper = new Helper();

	@Autowired
	IRVCentricDetailsService rvcentricServ;

	@Value("${plivono}")
	private String plivoNumber;

	@Autowired
	Configuration templates;

	@Value("${valid_minutes_for_OTP}")
	private int validMinutesForOTP;

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";
	
	@Autowired
	@Lazy
	IMessagingService  messagingService;
	
	@Autowired
	Email email_helper;

	// userUpdateV4 - By Anand
	@RequestMapping(value = "v4.0/userupdate/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse userUpdateV4(@PathVariable String autho, @RequestBody User user) {
		JResponse response = new JResponse();
		log.info("Entering userUpdateV4 : " + autho);
		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				return response;
			}
			try {
				long user_id = userServiceV4.getUserByUNameOrEmailV4(user.getEmail());

				if (user_id > 0 && !(usr.getId() == user_id)) {
					response.put("Status", 0);
					response.put("Msg", "Email already exist. Please enter alternate Email");
					return response;
				}
			} catch (Exception ex) {

				log.error("Exception : userUpdateV4" + ex.getLocalizedMessage());
			}

			JValidateString validString = userServiceV4.checkAlphabetOnly(user.getFirstname(),user.getLastname());	
			if( !validString.isValid() ) {
				response.put("Status", 0);
				response.put("Msg", validString.getMsg());
				return response;
			}
			
			user.setUpdatedOn(_helper.getCurrentTimeinUTC());
			String mobileNo = user.getMobileno().replaceAll("\\s", "");

			if (!mobileNo.contains("-") && mobileNo.length() > 0) {
				mobileNo = new CountryCode().getCountryCode(user.getCountry().toUpperCase()) + user.getMobileno();
				user.setMobileno(mobileNo);
			}

			String email = user.getEmail().replaceAll("\\s", "");
			if (email == null || email.equalsIgnoreCase("NA"))
				user.setEmail(user.getEmail());

			boolean Status = userServiceV4.updateUserv4byuserid(user, usr.getId());

			if (usr.getChargebeeid().toString() != null && usr.getChargebeeid().isEmpty())
				user.setChargebeeid(usr.getChargebeeid());

			// update cb customer details
			if (user.getChargebeeid() != null && !user.getChargebeeid().isEmpty()
					&& !user.getChargebeeid().equalsIgnoreCase("NA"))
				_helper.updateCBCustomerDetails(user);

			// update RvDetails in user_rvdetails
//			if (user.isUpdateRvDetails()) {
//
//				if (user.getUserRvDetails() != null) {
//					Status = userServiceV4.updateUserRVDetails(user.getUserRvDetails(), usr.getId());
//
//					if (Status) {
//						int device_cnt = user.getDevice_cnt();
//						String plan_id = user.getPlan_id();
//						UserRvDetails rvObj = userServiceV4.getUserRvDetails(user.getId());
//						boolean rvStatus = (rvObj != null) ? true : false;
//
//						boolean stat1 = rvcentricServ.saveUserBadgeTxn(usr.getId(), plan_id, device_cnt, rvStatus,
//								usr.getChargebeeid());
//						log.info("in userupdate:" + user.getId() + " Badge created:" + stat1);
//					} else
//						log.info("user:" + user.getId() + " RVer Badge not created.");
//				} else
//					Status = false;
//			}

			if (Status) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "UnExcepted Error in User Updation");
			}

		} catch (Exception e) {
			log.error("Exception : userUpdateV4 : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in User Updation");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	// v4.0/loginV2 - SIV
	@RequestMapping(value = "v4.0/loginV2", headers = "Accept=application/json", method = RequestMethod.POST)
	@ResponseBody
	public JResponse loginV4(@RequestParam("username") String userName, @RequestParam("mobiletype") String mobiletype,
			@RequestParam("mobileid") String mobileid, @RequestParam("password") String passwo,
			@RequestParam("webappid") String webappid, @RequestParam("mobileappid") String mobileappid,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {

		JResponse response = new JResponse();
		log.info("Entering loginV4 : " + userName);
		String username = userName.trim();
		String password = passwo.trim();
		log.info(username + "username" + password + "password");
		/* check for username */
		if (username.isEmpty() && username.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Please enter Username & Password");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} else if (username.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Please enter Username");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} else if (password.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Please enter Password");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV3("username", username);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Username/Password");
				response.put("Error", ex.getLocalizedMessage());
				log.error("loginV4 :Exception : " + username);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
//			if (!(user.getUsername().equalsIgnoreCase(username))) {
//				response.put("Status", 0);
//				response.put("Msg", "Invalid username");
//				log.error(" loginV4 : username not matched : " + username);
//
//				response.put("Return Time", System.currentTimeMillis());
//				return response;
//			}

			String pwd = user.getPassword().toString();
			boolean passwordMatch = _helper.checkUserCredencial(password, pwd, user.getPassword_ver());
			
			if ( passwordMatch ) {

				if (!user.isEnable()) {
					response.put("Status", 0);
					response.put("Msg", "Account Disabled. Please contact support!");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("User", user);
			} else {
				if (password.isEmpty()) {
					response.put("Status", 0);
					response.put("Msg", "Please enter Password");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				response.put("Status", 0);
				response.put("Msg", "Invalid Username/Password");
			}
		} catch (Exception e) {
			log.error("loginV4 : Exception : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Username/Password");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// v4.0/userV2/ - SIV
	@RequestMapping(value = "v4.0/userV2/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserV2_V4(@PathVariable String autho, @RequestParam("userid") String userid,
			@RequestParam("cmpid") String cmpid,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		log.info("Entering getUserV2_V4 : " + autho);
		JResponse response = new JResponse();
		UserV4 user = null;
		List<UserV4> usersList = new ArrayList<UserV4>();
		List<UserV4> responseList = new ArrayList<UserV4>();
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);
		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + autho);
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		try {
			long cmp_id = 0;
			if (user != null) {
				cmp_id = user.getCmpId();
				// usersList = userService.getUser(userid, cmp_id);
				usersList = userServiceV4.getUsersByUserId_CmpId(userid, cmp_id);

				if (usersList != null && usersList.size() > 0) {
					for (UserV4 thisUser : usersList) {
						if (userid.trim().isEmpty())
							userid = String.valueOf(thisUser.getId());
						String mobile = "";
						UserV4 newUser = thisUser;
						if (thisUser != null) {
							String mobileNumber = thisUser.getMobileno().trim();
							if (!mobileNumber.isEmpty() && mobileNumber != null
									&& !mobileNumber.equalsIgnoreCase("null")) {
								String[] phoneNumber = null;

								if (!mobileNumber.contains("-")) {
									mobileNumber = "-" + mobileNumber;
								}
								phoneNumber = mobileNumber.split("-");
								String cont = phoneNumber[0];
								String mob = phoneNumber[1];
								mobile = mob.replaceAll("\\W", "");

								if (thisUser.getCountry().equalsIgnoreCase("NA") || thisUser.getCountry() == null) {
									if (cont.equalsIgnoreCase("+91") || cont.equalsIgnoreCase("91")) {
										thisUser.setCountry("IN");
									} else if (cont.equalsIgnoreCase("+44") || cont.equalsIgnoreCase("44")) {
										thisUser.setCountry("GB");
									} else {
										thisUser.setCountry("US");
									}
								}
							} else {
								mobile = "1234567890";
								thisUser.setCountry("US");
							}

							if (mobile.length() > 10) {
								mobile = mobile.substring(mobile.length() - 10);
							}

							thisUser.setMobileno(mobile);
							responseList.add(newUser);
						}
					}
					ArrayList<RVAnswer> ansList = new ArrayList<RVAnswer>();//rvcentricServ.listRVAnswer();
					ArrayList<JRVAnswer> rv_cat_list = new ArrayList<JRVAnswer>();
					ArrayList<JRVAnswer> rv_type_list = new ArrayList<JRVAnswer>();
					ArrayList<JRVAnswer> pet_avail_list = new ArrayList<JRVAnswer>();
					ArrayList<JRVAnswer> no_travels_list = new ArrayList<JRVAnswer>();

					if (ansList != null) {

						for (int i = 0; i < ansList.size(); i++) {
							long ques_id = ansList.get(i).getQues_id();
							long ans_id = ansList.get(i).getId();
							String ans_value = ansList.get(i).getAns_value();
							JRVAnswer ansObj = new JRVAnswer(ans_id, ans_value);

							if (ques_id == 1) {
								rv_cat_list.add(ansObj);
							} else if (ques_id == 2) {
								rv_type_list.add(ansObj);
							} else if (ques_id == 3) {
								pet_avail_list.add(ansObj);
							} else {
								no_travels_list.add(ansObj);
							}
						}
					}
					boolean is_travelprofile = false;

//					UserRvDetails rvObj = userServiceV4.getUserRvDetails(Long.parseLong(userid));
//					JUserRVDetail travelprofile = new JUserRVDetail();
//
//					if (rvObj != null) {
//						travelprofile = new JUserRVDetail(rvObj.getId(), rvObj.getOwn_rv(), rvObj.getRvtype(),
//								rvObj.getWithPet(), rvObj.getHow_often(), rvObj.getOthers_type());
//						is_travelprofile = true;
//					}

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("users", responseList);
					response.put("travelprofile", null);
					response.put("is_travelprofile", is_travelprofile);
					response.put("rv_cat_list", rv_cat_list);
					response.put("rv_type_list", rv_type_list);
					response.put("pet_avail_list", pet_avail_list);
					response.put("no_travels_list", no_travels_list);

				} else {
					response.put("Status", 0);
					response.put("Msg", "User not found!");
					response.put("users", responseList);
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not found!");
				response.put("users", responseList);
			}

		} catch (Exception e) {
			log.error("Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// ========get user by name ================
	// getBreedsV4 - by anand
	@RequestMapping(value = "v4.0/getUserByUsernameV2", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserByUsernameV4(@RequestParam("name") String name,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		log.info("Entering getUserByUsernameV4 : " + name);
		name = name.toLowerCase().trim();
		name = name.replaceAll("\\s", "");
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("username", name);
			} catch (InvalidAuthoException e) {
				log.error("Invalid Username :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				response.put("Error", e.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (user == null || !(user.getUsername().equals(name))) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("users", user);

		} catch (Exception e) {
			log.error("Exception : getUserByUsernameV4 :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// passwordUpdateV4 - by Anand
	@RequestMapping(value = "v4.0/pwdupdate/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse passwordUpdateV4(@PathVariable String autho,
			@RequestParam("password") String password,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		log.info("Entering passwordUpdateV4 : " + autho);
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
			password = _helper.urlDecoder(password);
			
			JValidateString validatePassword = userServiceV4.validatePassword(password);
			if( !validatePassword.isValid() ) {
				response.put("Status", 0);
				response.put("Msg", validatePassword.getMsg());
				return response;
			}
			
			String bCryptPassword = _helper.bCryptEncoder(password);
			
			int status = userServiceV4.updateUserPassword(autho, bCryptPassword);

			if (status <= 0) {
				log.error("Invalid AuthKey : ");
				response.put("Status", 0);
				response.put("Msg", "Password not updated. Please try again later.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			
			
			//update evalidation
			async.updateEvalidation(user.getId(), password);

			response.put("Status", 1);
			response.put("Msg", "Success");
		} catch (Exception e) {
			log.error("Exception : passwordUpdateV4 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Password not updated. Please try again later.");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// v4.0/usertoken/ - SIV
	@RequestMapping(value = "v4.0/usertoken/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse usertokenV4(@PathVariable String autho, @ModelAttribute UserToken usertoken,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "ostype", defaultValue = "", required = false) String ostype,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		log.info("Entering usertokenV4 : " + autho);
		Map<String, String> mapResults = new HashMap<String, String>();
		try {
			
			if( ostype==null || ostype.trim().isEmpty() ) {
				ostype = os;
			}
			
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for auth : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			if (user != null) {
				usertoken.setOstype(ostype);
				boolean status = userServiceV4.saveOrUpdateUserTokenV4(user.getId() + "", usertoken);
				response.put("Status", 1);
				response.put("Msg", "Success");
			}

		} catch (DataIntegrityViolationException e) {
			response.put("Status", 0);
			response.put("Msg", "Userid and token not unique");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : usertokenV4 :" + e.getLocalizedMessage());
		} catch (Exception e) {
			log.error("Exception : usertokenV4 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Usertoken ");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// ==========save / update userdevice info========
	// v4.0/userdeviceinfo/ - SIV
	@RequestMapping(value = "v4.0/userdeviceinfo/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse userdeviceinfoV4(@PathVariable String autho,
			@RequestBody JUserDeviceInfo jUserDeviceInfo, HttpServletRequest request,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {

		JResponse response = new JResponse();
		log.info("Entering userdeviceinfoV4 : " + autho);
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException ee) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Error", ee.getLocalizedMessage());
				log.error("Exception occured : " + ee.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			log.info("IP Address  :" + request.getRemoteAddr());

			boolean status = userServiceV4.saveOrUpdateUserDeviceInfoV4(user.getId(), jUserDeviceInfo,
					request.getRemoteAddr(), _helper.getCurrentTimeinUTC());
			if (status) {
				response.put("Status", 1);
				response.put("Msg", "success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "UnExcepted Error in User deviceinfoV4");
			}

		} catch (DataIntegrityViolationException e) {
			response.put("Status", 0);
			response.put("Msg", "userid and device id also be unique");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : userdeviceinfoV4  " + e.getLocalizedMessage());
		}

		catch (Exception e) {
			log.error("Exception : userdeviceinfoV4 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in User deviceinfoV4");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	/*
	 * @RequestMapping(value = "v4.0/activateuser", method = RequestMethod.POST,
	 * headers = "Accept=application/json") public @ResponseBody JResponse
	 * activateUser(@ModelAttribute @Valid ActivateUser activateUser, BindingResult
	 * result) { // Variable Declaration log.info("Entered into activateUserV4 : " +
	 * activateUser.getEmail()); int deviceModelId = 6;
	 * 
	 * int initialDeviceStateid = 9; int model_id = deviceModelId; long
	 * monitortypeid = 0; String niomActivateEseyeSimURL = null; String to_address =
	 * null; String cc_address = null; String bcc_address = null; String subsURL =
	 * null; String alertCreationStatus = null; String orderChannel = null; String
	 * subscriptionStatus = "0"; String petName =
	 * activateUser.getGatewayName().trim(); String orderID =
	 * activateUser.getOrderid().trim(); String externalsku = ""; String
	 * niomGetOrderCountUrl = "v1.0/getordermapcount/";
	 * 
	 * String companyType = activateUser.getCmptype_id(); String errorMsg =
	 * "Unable to register the product. Please contact us at " +
	 * supportContactNumber + " / or send email to " + supportContactEmail + ".";
	 * String qrcCode = activateUser.getQrcCode().trim(); String errorResponse =
	 * "No Error"; String errorCode = "ER000"; String gpsDynamicmsg =
	 * "gpsmode=standalone"; String gpsDynamicmsg2 = "SFWSTORAGE=ON"; String
	 * gpsDynamicmsg3 = "reportinterval=900"; String gpsDynamicmsg4 =
	 * "maxsleeptime=900"; String gpsDynamicmsg5 = "modemresetint=3600";
	 * 
	 * String passwordType = null; String sensor_available = null; String userName =
	 * null; String mobilePage = null; String userEmail =
	 * activateUser.getEmail().trim(); userEmail = userEmail.toLowerCase();
	 * userEmail = userEmail.replaceAll("\\s+", ""); String userPhone =
	 * activateUser.getPhoneno().trim(); userPhone = userPhone.replace("(", "");
	 * userPhone = userPhone.replace(")", "");
	 * 
	 * int index = StringUtils.ordinalIndexOf(userPhone, "-", 2); if (index > 0) {
	 * userPhone = userPhone.substring(0, index) + userPhone.substring(index + 1); }
	 * 
	 * userPhone = userPhone.replaceAll("\\s+", "");
	 * 
	 * Long group_id = null; boolean signUPResponse = false; boolean isManualModelID
	 * = false; boolean activateSim = false; boolean updateExternalOrderDataStatus =
	 * false; boolean accountActivatedStatus = false; boolean orderMappedtatus =
	 * false; boolean activateSimStatus = false; boolean isDeviceAlredyActivated =
	 * false; boolean isTestUserDevice = false; boolean isQrcUserDevice = false;
	 * boolean provisionStatus = false; boolean loginCreationIssue = false; boolean
	 * gatewayCreationIssue = false; boolean accountCreated = false;
	 * 
	 * String monitortype = "NA";
	 * 
	 * boolean isAlreadyUser = Boolean.parseBoolean(activateUser.getAlreadyuser());
	 * 
	 * Properties prop = new Properties(); Gson gson = new Gson(); Orders order =
	 * new Orders(); JResponse response = new JResponse(); Inventory inventory = new
	 * Inventory(); Helper _helper = new Helper(); User intialUser = new User();
	 * User orderMapUser = null; int devicecount = 0; // Request validation if
	 * (result.hasErrors()) { return userController.activateUserValidation(result);
	 * }
	 * 
	 * try {
	 * 
	 * try { /* load a properties file
	 */
	/*
	 * File file = ResourceUtils.getFile("classpath:iris3.properties");
	 * prop.load(new FileInputStream(file));
	 * 
	 * activateSim = false;
	 * 
	 * to_address = prop.getProperty("to_address"); cc_address =
	 * prop.getProperty("cc_address"); bcc_address =
	 * prop.getProperty("bcc_address");
	 * 
	 * isManualModelID =
	 * Boolean.valueOf(prop.getProperty("isManualModelIdConfiguration"));
	 * 
	 * if (Integer.parseInt(companyType) == 3) { passwordType =
	 * prop.getProperty("rvpetPasswordType");// RvPet deviceModelId =
	 * Integer.parseInt(prop.getProperty("rvmodelID"));// RvPet subsURL =
	 * prop.getProperty("rvPetSubsURL");// RvPet
	 * 
	 * } else if (Integer.parseInt(companyType) == 5) { // restaurant deviceModelId
	 * = Integer.parseInt(prop.getProperty("nimblemodelID")); // monitoring
	 * passwordType = prop.getProperty("resMonitoringPasswordType"); subsURL =
	 * prop.getProperty("nimbleSubsURL");// Nimble } else if
	 * (Integer.parseInt(companyType) == 6) { // White labels passwordType =
	 * prop.getProperty("whiteLabelPasswordType"); deviceModelId =
	 * Integer.parseInt(prop.getProperty("nimblemodelID")); subsURL =
	 * prop.getProperty("rvPetSubsURL");// RvPet } } catch (IOException ex) {
	 * log.error(ex.getLocalizedMessage()); response.put("Status", 0);
	 * response.put("Msg", errorMsg); errorResponse = RegisterUserError.ER001;
	 * errorCode = "ER001"; response.put("Return Time", System.currentTimeMillis());
	 * return response; } catch (Exception e) { log.error("signup:::" +
	 * e.getMessage() + "" + e.getLocalizedMessage()); response.put("Status", 0);
	 * response.put("Msg", errorMsg); errorCode = "ER001"; errorResponse =
	 * RegisterUserError.ER001; response.put("Return Time",
	 * System.currentTimeMillis()); return response; }
	 * 
	 * // Validate QRC Code if (!(qrcCode.matches("[0-9]+")) || (qrcCode.length() !=
	 * 6)) { log.info("Invalid QRCode . " + qrcCode); response.put("Status", 0);
	 * response.put("Msg", RegisterUserError.ER048); errorCode = "ER048";
	 * errorResponse = RegisterUserError.ER048; response.put("Return Time",
	 * System.currentTimeMillis()); return response; }
	 * 
	 * // Parse Username
	 * 
	 * mobilePage = activateUser.getMobilepage();
	 * 
	 * if (mobilePage.equalsIgnoreCase("account")) {
	 * 
	 * if (activateUser.getUsername().trim().equals("") ||
	 * activateUser.getUsername().trim().isEmpty()) {
	 * 
	 * log.info("Username for product registration from account page is empty.");
	 * response.put("Status", 0); response.put("Msg", errorMsg); errorCode =
	 * "ER043"; errorResponse = RegisterUserError.ER043; response.put("Return Time",
	 * System.currentTimeMillis()); return response;
	 * 
	 * } else { userName = activateUser.getUsername().trim(); } } else if
	 * (mobilePage.equalsIgnoreCase("home") && !isAlreadyUser) {
	 * 
	 * if (!activateUser.getQrcCode().trim().equals(activateUser.getUsername())) {
	 * response.put("Status", 0); response.put("Msg", RegisterUserError.ER047);
	 * errorCode = "ER047"; errorResponse = RegisterUserError.ER047;
	 * response.put("Return Time", System.currentTimeMillis()); return response; }
	 * 
	 * userName = activateUser.getQrcCode().trim();
	 * 
	 * } else { userName = activateUser.getEmail().trim(); userName =
	 * userName.toLowerCase(); userName = userName.replaceAll("\\s+", ""); }
	 * 
	 * // Check Order ID
	 * 
	 * // Initialize Variable for get ordermap count
	 * log.info("Initialize variable to get ordermap count");
	 * 
	 * if (activateUser.getPurchasedfrom().toLowerCase().contains("rv")) {
	 * orderChannel = "rv"; subscriptionStatus = "1"; } else if
	 * (activateUser.getPurchasedfrom().toLowerCase().contains("amazon")) {
	 * orderChannel = "amazon"; subscriptionStatus = "0";
	 * 
	 * } else if (activateUser.getPurchasedfrom().toLowerCase().contains("walmart"))
	 * { orderChannel = "walmart"; subscriptionStatus = "0";
	 * 
	 * } else if
	 * (activateUser.getPurchasedfrom().toLowerCase().contains("facebook")) {
	 * orderChannel = "facebook"; subscriptionStatus = "0"; } else if
	 * (activateUser.getPurchasedfrom().equalsIgnoreCase("others")) {
	 * response.put("Status", 0); response.put("Msg",
	 * RegisterUserError.otherUserMessage); errorCode = "ER042"; errorResponse =
	 * RegisterUserError.ER042; response.put("Return Time",
	 * System.currentTimeMillis()); return response; }
	 * 
	 * // Validate Pet Name log.info("Validating Pet Name"); if (isAlreadyUser) {
	 * User petUser = new User(); try { petUser =
	 * userService.getUserByName(userName);
	 * 
	 * List<JGateway> userGateway = gatewayService.getGateway(null, null, null,
	 * null, petUser.getId(), null);
	 * 
	 * if (userGateway != null && userGateway.size() > 0) { response =
	 * userController.checkGateway(userGateway, qrcCode, petName);
	 * 
	 * if (response.getResponse().containsValue("ER006")) { errorCode = "ER006";
	 * errorResponse = RegisterUserError.ER006; response.put("Return Time",
	 * System.currentTimeMillis()); return response; } else { int i = 1; String
	 * newPetname = petName; while (response != null) { if
	 * (response.getResponse().containsValue("ER044")) { newPetname = petName + "-"
	 * + i; response = userController.checkGateway(userGateway, qrcCode,
	 * newPetname); i = i + 1; } else { activateUser.setGatewayName(newPetname);
	 * break; } } }
	 * 
	 * } } catch (InvalidUsernameException ex) { response.put("Status", 0);
	 * response.put("Msg", errorMsg); errorCode = "ER025"; errorResponse =
	 * "Validating Pet Name : " + RegisterUserError.ER025;
	 * response.put("Return Time", System.currentTimeMillis()); return response; } }
	 * 
	 * // Check if gateway name contains special String gateway_name =
	 * activateUser.getGatewayName().trim(); Pattern special =
	 * Pattern.compile("[!@#$%&*()+=|<>?{}.,\\[\\]~'\"]"); Matcher hasSpecial =
	 * special.matcher(gateway_name); if (hasSpecial.find()) {
	 * response.put("Status", 0);
	 * 
	 * if (Integer.parseInt(companyType) == 3) { response.put("Msg",
	 * RegisterUserError.ER009);// RvPet } else { response.put("Msg",
	 * "Gateway name contains special chars other than hyphen(-) and underscore(_)"
	 * ); } errorCode = "ER009"; errorResponse = RegisterUserError.ER009;
	 * response.put("Return Time", System.currentTimeMillis()); return response; }
	 * 
	 * log.info("Checking order id for activation");
	 * 
	 * JSONObject jorderIdCheckResponse = new JSONObject();
	 * 
	 * jorderIdCheckResponse = getNiomGetOrderCount(orderChannel, orderID);
	 * 
	 * if (jorderIdCheckResponse == null) { response.put("Status", 0);
	 * response.put("Msg", errorMsg); log.info("Error Code : ER035. " +
	 * RegisterUserError.ER035); errorResponse = RegisterUserError.ER035; errorCode
	 * = "ER035"; response.put("Return Time", System.currentTimeMillis()); return
	 * response; }
	 * 
	 * int orderIdCheckStatus = jorderIdCheckResponse.getInt("Status");
	 * 
	 * if (orderIdCheckStatus > 0 ? true : false) {
	 * 
	 * order =
	 * gson.fromJson(jorderIdCheckResponse.getJSONObject("Order").toString(),
	 * Orders.class);
	 * 
	 * int totalOrderCount =
	 * Integer.parseInt(jorderIdCheckResponse.getString("Totalordered"));
	 * 
	 * int totalMappedCount =
	 * Integer.parseInt(jorderIdCheckResponse.getString("Totalmapped"));
	 * 
	 * if (!(totalMappedCount < totalOrderCount)) { response.put("Status", 0);
	 * response.put("Msg", errorMsg); log.info("Error Code : ER040. " +
	 * RegisterUserError.ER040); errorResponse = RegisterUserError.ER040; errorCode
	 * = "ER040"; response.put("Return Time", System.currentTimeMillis()); return
	 * response; }
	 * 
	 * log.info("Order id found in niom. Order Channel : " +
	 * order.getOrder_acc_type().getAcc_type() + "Order ID : " + order.getOrder_id()
	 * + "Out Order ID  :" + order.getExternal_order_id());
	 * 
	 * if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("amazon"))
	 * { if (order.getBilling_email().toLowerCase().contains("na") ||
	 * order.getBilling_phone().toLowerCase().contains("na")) {
	 * order.setBilling_email(userEmail); order.setBilling_phone(userPhone);
	 * order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
	 * order.setWelcome_status(order.getWelcome_status()); boolean
	 * updateExternalOrderData = updateExternalOrders(order);
	 * log.info("updateExternalOrderData Status : " + updateExternalOrderData); } }
	 * 
	 * if
	 * (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("facebook"))
	 * {
	 * 
	 * if (nameCheck(order, activateUser)) { response.put("Status", 0);
	 * response.put("Msg", RegisterUserError.orderIDNotMatched);
	 * log.info("Error Code : ER038 : " + RegisterUserError.ER038); errorCode =
	 * "ER038"; errorResponse = RegisterUserError.ER038; response.put("Return Time",
	 * System.currentTimeMillis()); return response; } } } else {
	 * log.info("Order id not found in niom"); response.put("Status", 0);
	 * response.put("Msg", RegisterUserError.ER039); log.info("Error Code : ER039 :"
	 * + RegisterUserError.ER039); errorResponse = RegisterUserError.ER039;
	 * errorCode = "ER039"; response.put("Return Time", System.currentTimeMillis());
	 * return response; }
	 * 
	 * // Getting Inventory thru DataBase JSONObject jresponse = new JSONObject();
	 * jresponse = getInventory(qrcCode);
	 * 
	 * if (jresponse == null) { response.put("Status", 0); response.put("Msg",
	 * errorMsg); log.info("Error Code : ER002. : " + RegisterUserError.ER002);
	 * errorResponse = RegisterUserError.ER002; errorCode = "ER002";
	 * response.put("Return Time", System.currentTimeMillis()); return response; }
	 * 
	 * int status = jresponse.getInt("Status");
	 * 
	 * if (status > 0 ? true : false) {
	 * 
	 * try {
	 * 
	 * Type inventoryList = new TypeToken<ArrayList<Inventory>>() { }.getType();
	 * 
	 * List<Inventory> _inventoryList =
	 * gson.fromJson(jresponse.getJSONArray("inventory").toString(), inventoryList);
	 * 
	 * if (_inventoryList.size() == 0) { response.put("Status", 0);
	 * response.put("Msg", "No device found for scanned QR Code : " +
	 * activateUser.getQrcCode() + ". Please contact us at " + supportContactNumber
	 * + " / or send email to " + supportContactEmail + ".");
	 * 
	 * errorResponse = "No device found for respective QR Code : " +
	 * activateUser.getQrcCode();
	 * 
	 * log.info("No device found for respective qrccode" +
	 * activateUser.getQrcCode()); errorCode = "ER003"; response.put("Return Time",
	 * System.currentTimeMillis()); return response; } else {
	 * 
	 * inventory =
	 * gson.fromJson(jresponse.getJSONArray("inventory").get(0).toString(),
	 * Inventory.class);
	 * 
	 * monitortype = inventory.getDevicemodelnumber();
	 * 
	 * initialDeviceStateid = (int) inventory.getDevicestate().getId();
	 * 
	 * if (inventory.getDevicestate().getId() != 8 &&
	 * inventory.getDevicestate().getId() != 9 && inventory.getDevicestate().getId()
	 * != 2) { response.put("Status", 0); response.put("Msg", errorMsg); log.
	 * info("Device State ID is not suitable to activate the product . Device State ID :"
	 * + inventory.getDevicestate().getId() + "  QRC Code : " + qrcCode); errorCode
	 * = "ER032"; errorResponse =
	 * "Device State ID is not suitable to activate the product . Device State ID :"
	 * + inventory.getDevicestate().getId() + "  QRC Code : " + qrcCode;
	 * response.put("Return Time", System.currentTimeMillis()); return response; }
	 * 
	 * List<Ordermap> orderMapList = new ArrayList<Ordermap>();
	 * 
	 * Gson _gson = new Gson();
	 * 
	 * Type orderListType = new TypeToken<ArrayList<Ordermap>>() { }.getType();
	 * orderMapList =
	 * _gson.fromJson(jorderIdCheckResponse.getJSONArray("Ordermap").toString(),
	 * orderListType); if (orderMapList.size() > 0) {
	 * 
	 * for (Ordermap map : orderMapList) { if
	 * (map.getMeid().trim().equals(inventory.getMeid().trim())) {
	 * response.put("Status", 0); response.put("Msg", errorMsg); log.info(
	 * "Device is already mapped to some other orders . Need to check in the NIOM order map table."
	 * ); response.put("ErrorCode", "ER005"); errorCode = "ER005"; errorResponse =
	 * RegisterUserError.ER005; response.put("Return Time",
	 * System.currentTimeMillis()); return response; } }
	 * 
	 * }
	 * 
	 * // Check whether the device is sold by reseller if
	 * (inventory.getDevicestate().getId() == 8) { String emailContent = new
	 * com.nimble.irisservices.helper.EmailContent()
	 * .sucscripeResellerContent(activateUser);
	 * 
	 * if (new com.nimble.irisservices.helper.Email().SendEmail_SES(to_address,
	 * cc_address, bcc_address, "Offline Order User Details", emailContent)) {
	 * log.info("Offline Reseller User details send Successfully to internal team ."
	 * ); }
	 * 
	 * response.put("Status", 0); String msg =
	 * "Please subscribe for the device using the URL : " + subsURL +
	 * ". Your Device Qrc code : " + activateUser.getQrcCode() +
	 * " .If you have any further queries please contact our support .";
	 * response.put("Msg", msg); log.info(msg); errorResponse =
	 * RegisterUserError.ER030; errorCode = "ER030"; response.put("Return Time",
	 * System.currentTimeMillis()); return response; }
	 * 
	 * // Check Device is already activated JGatewayUserDetails gatewayUserDetails =
	 * gatewayService.getGateway(inventory.getMeid());
	 * 
	 * if (gatewayUserDetails != null) { isDeviceAlredyActivated = true;
	 * 
	 * if (gatewayUserDetails.getUserName().toLowerCase().contains("sanjeevitest"))
	 * { isTestUserDevice = true; }
	 * 
	 * if ((gatewayUserDetails.getUserName().equalsIgnoreCase(qrcCode) &&
	 * gatewayUserDetails.getUserName().matches("[0-9]+") &&
	 * gatewayUserDetails.getUserName().length() == 6)) { isQrcUserDevice = true; }
	 * 
	 * if (isTestUserDevice || (isQrcUserDevice &&
	 * !mobilePage.equalsIgnoreCase("home")) || (isQrcUserDevice &&
	 * mobilePage.equalsIgnoreCase("home") && isAlreadyUser)) {
	 * log.info("getGateway: Gateway " + inventory.getMeid() +
	 * " is mapped to username - " + gatewayUserDetails.getUserName());
	 * 
	 * try { User _user =
	 * userService.verifyAuthKey(gatewayUserDetails.getAuthkey());
	 * gatewayService.delGateway(_user,
	 * Long.toString(gatewayUserDetails.getGatewayid())); } catch (Exception e) {
	 * response.put("Status", 0); errorCode = "ER031"; response.put("Msg",
	 * errorMsg); log.error("Error while deleting gateway :" + e.getMessage());
	 * errorResponse = RegisterUserError.ER031; response.put("Return Time",
	 * System.currentTimeMillis()); return response; }
	 * 
	 * } else if (isQrcUserDevice && mobilePage.equalsIgnoreCase("home") &&
	 * !isAlreadyUser) {
	 * 
	 * isQrcUserDevice = true; } else {
	 * 
	 * log.info("getGateway: Gateway " + inventory.getMeid() +
	 * " is mapped to username - " + gatewayUserDetails.getUserName());
	 * response.put("Status", 0); response.put("Msg", errorMsg); errorCode =
	 * "ER006"; errorResponse = RegisterUserError.ER006; response.put("Return Time",
	 * System.currentTimeMillis()); return response; } }
	 * 
	 * // check MEID is already activated if activated return // the response
	 * 
	 * if (false) { log.info("Moving to unwanted function"); return null; } else {
	 * 
	 * boolean pushResponse = orderMapping(inventory.getMeid(),
	 * URLEncoder.encode(_helper.getCurrentTimeinUTC()),
	 * inventory.getDevicestate().getId(), order.getOrder_id() + "",
	 * order.getDevicemodel(), subscriptionStatus, "1");
	 * 
	 * if (pushResponse) { orderMappedtatus = true; } else { response.put("Status",
	 * 0); response.put("SpecificMsg", "Unable to map the order");
	 * response.put("Msg", errorMsg); errorCode = "ER041"; errorResponse =
	 * RegisterUserError.ER041; response.put("Return Time",
	 * System.currentTimeMillis()); return response; }
	 * 
	 * log.info("Push Data Response : " + orderMappedtatus);
	 * log.info("Activate Sim from Config : " + activateSim);
	 * 
	 * if (activateSim && orderMappedtatus) { if
	 * (inventory.getSim_vendor().equalsIgnoreCase("eseye")) { JResponse
	 * activateSimResponse = userController.activateEseyeSim(
	 * niomActivateEseyeSimURL, inventory.getSim_no(), "9454", "rv_niom"); Object
	 * activateSimResponseStatus = activateSimResponse.getResponse().get("Status");
	 * activateSimResponse.getResponse().get("Msg");
	 * activateSimResponse.getResponse().get("ErrorCode");
	 * 
	 * if (activateSimResponseStatus.equals("1")) { activateSimStatus = true; }
	 * response.put("ActivateSimResponseStatus", activateSimResponseStatus); } else
	 * { response.put("ActivateSimResponseStatus", 0); }
	 * 
	 * } else { response.put("ActivateSimResponseStatus", 0); }
	 * 
	 * // Create Gateway try {
	 * 
	 * if (!isManualModelID) { log.info("isManualModelID2===========" +
	 * isManualModelID);
	 * 
	 * AssetModel assetModel = gatewayDao
	 * .getAssetModelByName(inventory.getDevicemodelnumber());
	 * 
	 * model_id = (int) assetModel.getId(); monitortypeid =
	 * assetModel.getMonitor_type().getId(); sensor_available =
	 * assetModel.getSensoravailable();
	 * 
	 * response.put("Monitortypeid", monitortypeid); }
	 * 
	 * if ((!isAlreadyUser && (isQrcUserDevice &&
	 * !mobilePage.equalsIgnoreCase("home"))) || (!isAlreadyUser &&
	 * mobilePage.equalsIgnoreCase("login") && !isQrcUserDevice)) {
	 * 
	 * try { // Sign up the new USER
	 * log.info("==Entered into signup Section==========="); SignUp signUp = new
	 * SignUp(); signUp.setUsername(userName.toLowerCase());
	 * 
	 * if (activateUser.getPurchasedfrom().toLowerCase().contains("rv")) {
	 * signUp.setFirstname(activateUser.getFirstname());
	 * signUp.setLastname(activateUser.getLastname()); }
	 * 
	 * String password = activateUser.getPhoneno(); // password =
	 * password.replaceAll("\\W", ""); password = password.replaceAll("[^\\d.]",
	 * ""); // remove other than numbers if (password.length() > 10) { password =
	 * password.substring(password.length() - 10); }
	 * 
	 * signUp.setPassword(password); signUp.setEmail(userEmail);
	 * signUp.setAddress(activateUser.getAddress()); signUp.setPhoneno(userPhone);
	 * signUp.setMobileno(userPhone); Random rand = new Random(); int randomID =
	 * rand.nextInt((999 - 10) + 1) + 999;
	 * signUp.setCompanyname(activateUser.getFirstname() + randomID);
	 * signUp.setCmptype_id(activateUser.getCmptype_id());
	 * signUp.setSupervisor("PQA");
	 * signUp.setThrotsettingsid(activateUser.getThrotsettingsid()); signUPResponse
	 * = userService.signUp(signUp);
	 * 
	 * User user = userService.getUserByName(signUp.getEmail());
	 * user.setVerified(true); userService.updateUser(user);
	 * 
	 * log.info("==Signup done into signup Section===========");
	 * 
	 * } catch (ConstraintViolationException ce) {
	 * 
	 * loginCreationIssue = true;
	 * 
	 * log.error("signup: ConstraintViolationException:\n" + ce.getStackTrace());
	 * response.put("Status", 0); if (Integer.parseInt(companyType) == 3) {
	 * response.put("Msg",
	 * "Email ID / Name already exists. Please try with alternate one.");// RvPet }
	 * else { response.put("Msg",
	 * "Username / Company Name /Gateway Name already exists.Please try with alternate one."
	 * ); } // response.put("ErrorCode", "ER007"); errorCode = "ER007";
	 * errorResponse = RegisterUserError.ER007;
	 * 
	 * response.put("Return Time", System.currentTimeMillis()); return response; }
	 * catch (Exception e) { loginCreationIssue = true;
	 * 
	 * log.error("signup:::" + e.getMessage()); log.error("signup:::" +
	 * e.getLocalizedMessage()); response.put("Status", 0); response.put("Msg",
	 * errorMsg); errorCode = "ER008"; errorResponse = "Signup Error : " +
	 * RegisterUserError.ER008 + "Exception  : " + e.getMessage();
	 * response.put("Return Time", System.currentTimeMillis()); return response; }
	 * // Sigh up the User ends
	 * 
	 * }
	 * 
	 * log.info("==Get user==========="); User user = new User(); try { user =
	 * userService.getUserByName(userName); intialUser = user; orderMapUser = user;
	 * provisionStatus = true; } catch (InvalidUsernameException ex) {
	 * log.error("==Invalid username exception==========="); loginCreationIssue =
	 * true; response.put("Status", 0); response.put("Msg", errorMsg); errorCode =
	 * "ER025"; errorResponse = "Getting user Details : " + RegisterUserError.ER025;
	 * response.put("Return Time", System.currentTimeMillis()); return response; }
	 * 
	 * String levelid = "1"; String group = "";
	 * 
	 * List<JGroups> jgroups = groupservices.getGroups(group, group, levelid,
	 * user.giveCompany().getId());
	 * 
	 * if (jgroups.get(0) != null) { group_id = jgroups.get(0).getId(); }
	 * 
	 * log.info("==Get groups==========="); // Register QRC User if (isQrcUserDevice
	 * && mobilePage.equalsIgnoreCase("home") && !isAlreadyUser) {
	 * log.info("==QRC User===========");
	 * 
	 * Gateway gateway = new Gateway();
	 * 
	 * List<JGateway> gateways = gatewayService.getGateway("", "", "", "",
	 * user.getId(), inventory.getMeid());
	 * 
	 * CompanyConfig cmp_cfg =
	 * companyService.getCompanyConfig(user.giveCompany().getId());
	 * 
	 * boolean updateQRCGateway = false;
	 * 
	 * if (gateways != null) {
	 * 
	 * log.info("==Get Gateways not null===========");
	 * 
	 * if (gateways.size() > 0) {
	 * 
	 * JGateway jGateway = gateways.get(0); try {
	 * 
	 * jGateway = gatewayService.gatewayExitsinDB(jGateway,
	 * user.giveCompany().getId()); jGateway.setName(gateway_name);
	 * jGateway.setModelid(model_id); jGateway.setSensorEnable(sensor_available);
	 * 
	 * if (jGateway.getQrcode().equals("NA")) { jGateway.setQrcode(qrcCode); }
	 * 
	 * gateway = gatewayService.saveORupdateQRCGateway(jGateway,
	 * user.giveCompany().getId()); updateQRCGateway = true; } catch (Exception ex)
	 * {
	 * 
	 * updateQRCGateway = false; gatewayCreationIssue = true;
	 * 
	 * log.error("Exception while updating gateway for QRC Login : " + userName +
	 * "Exception : " + ex.getMessage()); }
	 * 
	 * try {
	 * 
	 * log.info("==Updated Alerts ==========="); List<JAlertCfg> alertcfgs =
	 * alertCfgService.getAlertCfg("", "", Long.toString(jGateway.getId()),
	 * user.getId(), cmp_cfg.getTemperatureunit());
	 * 
	 * for (JAlertCfg alerts : alertcfgs) {
	 * 
	 * Long[] assetIds = new Long[1]; assetIds[0] = jGateway.getId();
	 * 
	 * alerts.setEmailids(userEmail); alerts.setMobilenos(userPhone);
	 * alerts.setAlerttypeid(alerts.getAlerttype().getId());
	 * alerts.setAssetids(assetIds);
	 * 
	 * boolean result1 = alertCfgService.saveORupdateAlertCfg(alerts,
	 * user.giveCompany().getId()); } updateQRCGateway = true;
	 * 
	 * } catch (Exception ex) { log.error("==Excepion  Alerts Saved===========" +
	 * ex.getLocalizedMessage()); gatewayCreationIssue = true; updateQRCGateway =
	 * false; log.error("Exception while updating  alerts for QRC Login : " +
	 * userName + "Exception : " + ex.getMessage()); } } } boolean updateQRCLogin =
	 * false; if (updateQRCGateway) { try {
	 * log.info("==2.Update QRC User name==========="); user =
	 * userService.getUserByName(qrcCode.trim());
	 * user.setUsername(userEmail.trim());
	 * 
	 * Set<Gateway> _gateway = new HashSet<Gateway>(); _gateway.add(gateway);
	 * 
	 * String password = activateUser.getPhoneno(); password =
	 * password.replaceAll("\\W", "");
	 * 
	 * if (password.length() > 10) { password = password.substring(password.length()
	 * - 10); } user.setPassword(password); user.setGateways(_gateway);
	 * user.setEmail(userEmail.trim()); user.setMobileno(userPhone);
	 * user.setAuthKey(_helper.encryptAndSetUser(userEmail.trim()));
	 * user.setVerified(true); userService.updateUser(user);
	 * 
	 * orderMapUser = user;
	 * 
	 * Company cmp = companyService.getCompany(user.giveCompany().getId());
	 * cmp.setMobileno(userPhone); cmp.setPhoneno(userPhone);
	 * cmp.setEmail(userEmail.trim()); companyService.updateCompany(cmp);
	 * updateQRCLogin = true;
	 * 
	 * if (gateway.getModel().getInventorymodelname().toLowerCase() .contains("n1"))
	 * { async.sendDynamicCommand(Long.toString(gateway.getId()), gpsDynamicmsg,
	 * 0L); async.sendDynamicCommand(Long.toString(gateway.getId()), gpsDynamicmsg2,
	 * 0L); } else if (gateway.getModel().getInventorymodelname().toLowerCase()
	 * .contains("w5")) { async.sendDynamicCommand(Long.toString(gateway.getId()),
	 * gpsDynamicmsg2, 0L); }
	 * 
	 * } catch (Exception ex) { loginCreationIssue = true;
	 * log.error("Exception in updating the QRC User Name =" +
	 * ex.getLocalizedMessage());
	 * log.error("Exception while updating username and password for QRC Login : " +
	 * userName + "Exception : " + ex.getMessage()); } }
	 * 
	 * if (updateQRCLogin) {
	 * 
	 * alertCreationStatus = "UpdateGatewayCreditStatus : True" +
	 * "<br/><br/>SaveGatewayReportStatus : True" +
	 * "<br/><br/>SaveLastGatewayReport : True";
	 * 
	 * alertCreationStatus = alertCreationStatus +
	 * "<br/><br/>CreateTempAlertStatus :" + "True" +
	 * "<br/><br/>CreateBatteryAlertStatus :" + "True" +
	 * "<br/><br/>CreateOnBatteryAlertStatus : " + "True" +
	 * "<br/><br/>CreateNetworkAlertStatus : " + "True";
	 * 
	 * orderMappedtatus =
	 * niomDbservice.updateOrdermapUserDetails(order.getOrder_id(),
	 * URLEncoder.encode(user.getUsername()), user.getId() + "",
	 * URLEncoder.encode(user.getPassword()));
	 * 
	 * updateExternalOrderDataStatus = updateOrdersData(orderChannel, order,
	 * inventory); accountActivatedStatus = true;
	 * 
	 * String emailContent = new com.nimble.irisservices.helper.EmailContent()
	 * .userCredentialsContent(user.getEmail(), user.getPassword(),
	 * activateUser.getFirstname());
	 * 
	 * if (new com.nimble.irisservices.helper.Email().SendEmail_SES(user.getEmail(),
	 * null, bcc_address, "Welcome to Nimble Pet App!", emailContent)) {
	 * log.info("User Created Successfully : " + user.getEmail()); provisionStatus =
	 * true; }
	 * 
	 * User updatedUser = new User();
	 * 
	 * updatedUser = userService.getUserByName(userEmail.trim());
	 * 
	 * response.put("Status", 1); response.put("Msg",
	 * RegisterUserError.homePageActivationMessage); response.put("User",
	 * updatedUser);
	 * 
	 * accountCreated = true;
	 * 
	 * response.put("Return Time", System.currentTimeMillis()); return response; }
	 * else { log.info("==2.Error Update QRC User name DONE===========");
	 * response.put("Status", 0); response.put("Msg", errorMsg); errorResponse =
	 * "Updating QRC user Details : " + RegisterUserError.ER046; errorCode =
	 * "ER046"; response.put("Return Time", System.currentTimeMillis()); return
	 * response; } } // Create Gateway and Assign to the user JGateway jgateway =
	 * new JGateway();
	 * 
	 * Gateway gateway = new Gateway(); boolean updateCreaditStatus = false; boolean
	 * saveGatewayReportStatus = false; boolean saveLastGatewayReport = false;
	 * 
	 * JCreateGateway jcreateGateway = new JCreateGateway();
	 * 
	 * jcreateGateway = createOrUpdateGateway(activateUser, inventory, model_id,
	 * group_id, sensor_available, passwordType, user);
	 * 
	 * log.info("== gateway Created and assiged to user===========");
	 * 
	 * if (jcreateGateway != null) { log.info("== Update credit status===========");
	 * updateCreaditStatus = jcreateGateway.isUpdateCreaditStatus();
	 * saveGatewayReportStatus = jcreateGateway.isSaveGatewayReportStatus();
	 * saveLastGatewayReport = jcreateGateway.isSaveLastGatewayReport();
	 * 
	 * if (jcreateGateway.isGatewayCreated()) { devicecount =
	 * gatewayService.getDeviceCount(user.getId()); jgateway =
	 * jcreateGateway.getJgateway(); gateway = jcreateGateway.getGateway();
	 * 
	 * log.info("== Create alerts==========="); // Create Temparature Alert boolean
	 * createTempAlertStatus = false;
	 * 
	 * JResponse createTempAlertResponse = createTemperatureAlert(user, gateway); if
	 * (createTempAlertResponse.getResponse().get("Status").toString()
	 * .equalsIgnoreCase("1")) { createTempAlertStatus = true; }
	 * 
	 * // Create Battery Alert boolean createBatteryAlertStatus = false; JResponse
	 * createBatteryAlertResponse = createBatteryAlert(user, gateway); if
	 * (createBatteryAlertResponse.getResponse().get("Status").toString()
	 * .equalsIgnoreCase("1")) { createBatteryAlertStatus = true; }
	 * 
	 * // Create On Battery Alert boolean createOnBatteryAlertStatus = false;
	 * JResponse createOnBatteryAlertResponse = createOnBatteryAlert(user, gateway);
	 * if (createOnBatteryAlertResponse.getResponse().get("Status").toString()
	 * .equalsIgnoreCase("1")) { createOnBatteryAlertStatus = true; }
	 * 
	 * // Create Network Alert boolean createNetworkAlertStatus = false; JResponse
	 * createNetworkAlertResponse = createNetworkAlert(user, gateway); if
	 * (createNetworkAlertResponse.getResponse().get("Status").toString()
	 * .equalsIgnoreCase("1")) { createNetworkAlertStatus = true; }
	 * 
	 * alertCreationStatus = "UpdateGatewayCreditStatus : " + updateCreaditStatus +
	 * "<br/><br/>SaveGatewayReportStatus : " + saveGatewayReportStatus +
	 * "<br/><br/>SaveLastGatewayReport :" + saveLastGatewayReport;
	 * 
	 * alertCreationStatus = alertCreationStatus +
	 * "<br/><br/>CreateTempAlertStatus :" + createTempAlertStatus +
	 * "<br/><br/>CreateBatteryAlertStatus :" + createBatteryAlertStatus +
	 * "<br/><br/>CreateOnBatteryAlertStatus : " + createOnBatteryAlertStatus +
	 * "<br/><br/>CreateNetworkAlertStatus : " + createNetworkAlertStatus;
	 * 
	 * if (jcreateGateway.isGatewayCreated()) {
	 * 
	 * String emailContent = new com.nimble.irisservices.helper.EmailContent()
	 * .userCredentialsContent(user.getUsername(), user.getPassword(),
	 * activateUser.getFirstname());
	 * 
	 * if (new com.nimble.irisservices.helper.Email().SendEmail_SES(
	 * user.getEmail(), null, bcc_address, "Welcome to Nimble Pet App!",
	 * emailContent)) { log.info("User Created Successfully : " + user.getEmail());
	 * provisionStatus = true; }
	 * 
	 * } orderMappedtatus =
	 * niomDbservice.updateOrdermapUserDetails(order.getOrder_id(),
	 * URLEncoder.encode(user.getUsername()), user.getId() + "",
	 * URLEncoder.encode(user.getPassword()));
	 * 
	 * updateExternalOrderDataStatus = updateOrdersData(orderChannel, order,
	 * inventory);
	 * 
	 * accountActivatedStatus = true;
	 * 
	 * } else { gatewayCreationIssue = true; } } else { gatewayCreationIssue = true;
	 * }
	 * 
	 * if (accountActivatedStatus) { if
	 * (gateway.getModel().getModel().toLowerCase().contains("nt3d")) {
	 * async.sendDynamicCommand(Long.toString(gateway.getId()), gpsDynamicmsg3, 0L);
	 * async.sendDynamicCommand(Long.toString(gateway.getId()), gpsDynamicmsg4, 0L);
	 * async.sendDynamicCommand(Long.toString(gateway.getId()), gpsDynamicmsg2, 0L);
	 * async.sendDynamicCommand(Long.toString(gateway.getId()), gpsDynamicmsg5, 0L);
	 * } else if (gateway.getModel().getInventorymodelname().toLowerCase()
	 * .contains("n1")) { async.sendDynamicCommand(Long.toString(gateway.getId()),
	 * gpsDynamicmsg, 0L); async.sendDynamicCommand(Long.toString(gateway.getId()),
	 * gpsDynamicmsg2, 0L); } else if
	 * (gateway.getModel().getInventorymodelname().toLowerCase() .contains("w5")) {
	 * async.sendDynamicCommand(Long.toString(gateway.getId()), gpsDynamicmsg2, 0L);
	 * } else if (gateway.getModel().getModel().toLowerCase().contains("nt3f")) {
	 * async.sendDynamicCommand(Long.toString(gateway.getId()), gpsDynamicmsg2, 0L);
	 * async.sendDynamicCommand(Long.toString(gateway.getId()), gpsDynamicmsg3, 0L);
	 * async.sendDynamicCommand(Long.toString(gateway.getId()), gpsDynamicmsg4, 0L);
	 * async.sendDynamicCommand(Long.toString(gateway.getId()), gpsDynamicmsg5, 0L);
	 * }
	 * 
	 * response.put("Status", 1); if (mobilePage.toLowerCase().contains("login")) {
	 * if (monitortypeid == 1) response.put("Msg",
	 * RegisterUserError.loginPageActivationMessage); else response.put("Msg",
	 * RegisterUserError.loginPageFurbitActivationMessage);
	 * 
	 * } else if (mobilePage.toLowerCase().contains("home")) { if (monitortypeid ==
	 * 1) response.put("Msg", RegisterUserError.homePageActivationMessage); else
	 * response.put("Msg", RegisterUserError.loginPageFurbitActivationMessage);
	 * 
	 * } else if (mobilePage.toLowerCase().contains("account")) { if (monitortypeid
	 * == 1) response.put("Msg", RegisterUserError.accountPageActivationMessage);
	 * else response.put("Msg",
	 * RegisterUserError.accountPageFurbitActivationMessage);
	 * 
	 * } else { if (monitortypeid == 1) response.put("Msg",
	 * RegisterUserError.loginPageActivationMessage); else response.put("Msg",
	 * RegisterUserError.loginPageFurbitActivationMessage);
	 * 
	 * } response.put("User", user);
	 * 
	 * accountCreated = true; response.put("Return Time",
	 * System.currentTimeMillis()); return response; } else { response.put("Status",
	 * 0); response.put("Msg", errorMsg); errorResponse = "Getting user Details : "
	 * + RegisterUserError.ER045; errorCode = "ER045"; response.put("Return Time",
	 * System.currentTimeMillis()); return response; }
	 * 
	 * } catch (DataIntegrityViolationException e) { gatewayCreationIssue = true;
	 * log.error("== Exception 1===========" + e.getStackTrace());
	 * log.error("== Exception 1===========" + e.getLocalizedMessage());
	 * response.put("Status", 0);
	 * 
	 * if (Integer.parseInt(companyType) == 3) { response.put("Msg",
	 * "Pet Name already exists. Please enter valid one. "); } else {
	 * response.put("Msg", "Asset name or MEID already exits");
	 * 
	 * }
	 * 
	 * errorCode = "ER015"; errorResponse = RegisterUserError.ER015;
	 * response.put("Return Time", System.currentTimeMillis()); return response; }
	 * catch (Exception e) { gatewayCreationIssue = true;
	 * log.error("== Exception 2===========" + e.getLocalizedMessage());
	 * log.error("== Exception 2===========" + e.getStackTrace());
	 * response.put("Status", 0); response.put("Msg", errorMsg);
	 * log.error("saveORupdateGateway::::" + e.getMessage()); errorResponse =
	 * RegisterUserError.ER016 + "Exception : " + e.getMessage(); errorCode =
	 * "ER016"; response.put("Return Time", System.currentTimeMillis()); return
	 * response; } }
	 * 
	 * } } catch (Exception ex) { log.
	 * error("Error while getting meid for the respective qrc code. Exception :  " +
	 * ex.getMessage()); response.put("Status", 0); response.put("Msg", errorMsg);
	 * errorCode = "ER019"; errorResponse = RegisterUserError.ER019 + "Exception : "
	 * + ex.getMessage(); response.put("Return Time", System.currentTimeMillis());
	 * return response; }
	 * 
	 * } else { String msg = jresponse.getString("Msg"); response.put("Status", 0);
	 * response.put("Msg", errorMsg); errorCode = "ER018"; errorResponse =
	 * RegisterUserError.ER018 + "Response Message : - " + msg; log.
	 * info("Exception : No device found for respective qrccode. Response Message :  -"
	 * + msg); response.put("Return Time", System.currentTimeMillis()); return
	 * response; } } catch (Exception e) { log.error("signup:::" + e.getMessage() +
	 * "" + e.getLocalizedMessage()); gatewayCreationIssue = true;
	 * response.put("Status", 0); response.put("Msg", errorMsg); errorResponse =
	 * RegisterUserError.ER020; errorCode = "ER020"; response.put("ErrorCode",
	 * "ER020"); response.put("Return Time", System.currentTimeMillis()); return
	 * response; } finally {
	 * 
	 * if (alertCreationStatus == null) { alertCreationStatus =
	 * "Alert Created : false" + "<br/><br/>"; }
	 * 
	 * if (loginCreationIssue || gatewayCreationIssue) { orderMappedtatus = false;
	 * 
	 * log.info("Deleted mapping for meid :" + inventory.getMeid() + " User : " +
	 * intialUser.getUsername());
	 * 
	 * String deleteOrderMapUrl = niomIP + "v1.0/deleteordermap/" + niomAuthKey;
	 * 
	 * deleteOrderMapUrl = deleteOrderMapUrl + "?meid='" + inventory.getMeid() +
	 * "'&devicestateid=" + initialDeviceStateid;
	 * 
	 * // postData(deleteOrderMapUrl); deleteordermap(inventory.getMeid(),
	 * initialDeviceStateid); }
	 * 
	 * if (gatewayCreationIssue && !isAlreadyUser &&
	 * !mobilePage.equalsIgnoreCase("home")) {
	 * 
	 * long timeMilli = Calendar.getInstance().getTimeInMillis(); String newUserName
	 * = intialUser.getUsername();
	 * 
	 * if(newUserName.length()>10) newUserName =
	 * intialUser.getUsername().substring(0, 9) + "-" + timeMilli;
	 * log.info("Username updated for new user : " + intialUser.getUsername() +
	 * " New UserName : " + newUserName);
	 * 
	 * intialUser.setUsername(newUserName); intialUser.setEmail(newUserName);
	 * intialUser.setEnable(false); intialUser.setAuthKey(newUserName);
	 * intialUser.setMobileno("1234567890");
	 * 
	 * orderMappedtatus = false; if (!(intialUser.getSignupType() == null)) {
	 * userService.updateUser(intialUser); }
	 * 
	 * orderMapUser = intialUser; }
	 * 
	 * if (isQrcUserDevice && !gatewayCreationIssue && isAlreadyUser) {
	 * 
	 * long timeMilli = Calendar.getInstance().getTimeInMillis(); String newUserName
	 * = intialUser.getUsername();
	 * 
	 * if(newUserName.length()>10) newUserName =
	 * intialUser.getUsername().substring(0, 9) + "-" + timeMilli;
	 * 
	 * log.info("Username updated for QRC user : " + intialUser.getUsername() +
	 * " New UserName : " + newUserName);
	 * 
	 * User qrcUser = new User(); try { qrcUser =
	 * userService.getUserByName(inventory.getQrc()); orderMapUser = qrcUser; }
	 * catch (InvalidUsernameException e) { log.error(e.getLocalizedMessage()); }
	 * 
	 * qrcUser.setUsername(newUserName); qrcUser.setEmail(newUserName);
	 * qrcUser.setEnable(false); qrcUser.setAuthKey(newUserName);
	 * qrcUser.setMobileno("1234567890"); userService.updateUser(qrcUser); }
	 * 
	 * if (isQrcUserDevice && !gatewayCreationIssue && !isAlreadyUser &&
	 * mobilePage.equalsIgnoreCase("login")) {
	 * 
	 * long timeMilli = Calendar.getInstance().getTimeInMillis(); String newUserName
	 * = intialUser.getUsername();
	 * 
	 * if(newUserName.length()>10) newUserName =
	 * intialUser.getUsername().substring(0, 9) + "-" + timeMilli;
	 * log.info("Username updated for QRC user : " + intialUser.getUsername() +
	 * " New UserName : " + newUserName);
	 * 
	 * User qrcUser = new User(); try { qrcUser =
	 * userService.getUserByName(inventory.getQrc()); } catch
	 * (InvalidUsernameException e) { log.error(e.getLocalizedMessage()); }
	 * 
	 * qrcUser.setUsername(newUserName); qrcUser.setEmail(newUserName);
	 * qrcUser.setEnable(false); qrcUser.setAuthKey(newUserName);
	 * qrcUser.setMobileno("1234567890"); userService.updateUser(qrcUser);
	 * 
	 * }
	 * 
	 * if (isQrcUserDevice && (loginCreationIssue || gatewayCreationIssue) &&
	 * !mobilePage.equalsIgnoreCase("home")) { orderMappedtatus = false; User
	 * qrcUser = new User(); try { qrcUser =
	 * userService.getUserByName(inventory.getQrc()); String levelid = "1"; String
	 * group = "";
	 * 
	 * // for order mapping details orderMapUser = qrcUser; List<JGroups> jgroups =
	 * groupservices.getGroups(group, group, levelid,
	 * qrcUser.giveCompany().getId());
	 * 
	 * if (jgroups.get(0) != null) { group_id = jgroups.get(0).getId(); }
	 * 
	 * JCreateGateway jcreateGateway = createOrUpdateGateway(activateUser,
	 * inventory, model_id, group_id, sensor_available, passwordType, qrcUser);
	 * 
	 * if (jcreateGateway.isGatewayCreated()) { devicecount =
	 * gatewayService.getDeviceCount(qrcUser.getId());
	 * 
	 * JGateway jgateway = jcreateGateway.getJgateway(); Gateway gateway =
	 * jcreateGateway.getGateway();
	 * 
	 * // Create Temparature Alert boolean createTempAlertStatus = false; JResponse
	 * createTempAlertResponse = createTemperatureAlert(qrcUser, gateway); if
	 * (createTempAlertResponse.getResponse().get("Status").toString().
	 * equalsIgnoreCase("1")) { createTempAlertStatus = true; }
	 * 
	 * // Create Battery Alert boolean createBatteryAlertStatus = false; JResponse
	 * createBatteryAlertResponse = createBatteryAlert(qrcUser, gateway); if
	 * (createBatteryAlertResponse.getResponse().get("Status").toString().
	 * equalsIgnoreCase("1")) { createBatteryAlertStatus = true; }
	 * 
	 * // Create On Battery Alert boolean createOnBatteryAlertStatus = false;
	 * JResponse createOnBatteryAlertResponse = createOnBatteryAlert(qrcUser,
	 * gateway); if
	 * (createOnBatteryAlertResponse.getResponse().get("Status").toString().
	 * equalsIgnoreCase("1")) { createOnBatteryAlertStatus = true; }
	 * 
	 * // Create Network Alert boolean createNetworkAlertStatus = false; JResponse
	 * createNetworkAlertResponse = createNetworkAlert(qrcUser, gateway); if
	 * (createNetworkAlertResponse.getResponse().get("Status").toString().
	 * equalsIgnoreCase("1")) { createNetworkAlertStatus = true; } } } catch
	 * (InvalidUsernameException e) { log.error(e.getLocalizedMessage()); }
	 * 
	 * }
	 * 
	 * String mailSub = "Failed : External Orders User Activation Status-Email : " +
	 * activateUser.getEmail();
	 * 
	 * Offlineuserdetails offlineUser = new Offlineuserdetails();
	 * 
	 * offlineUser.setQrccode(activateUser.getQrcCode());
	 * offlineUser.setName(activateUser.getFirstname() + " " +
	 * activateUser.getLastname()); offlineUser.setEmail(activateUser.getEmail());
	 * offlineUser.setCompanytype(companyType); if (inventory.getDevicestate() ==
	 * null) { offlineUser.setDevicestateId("NA"); } else {
	 * offlineUser.setDevicestateId(Long.toString(inventory.getDevicestate().getId()
	 * )); }
	 * 
	 * if (activateUser.getAddress() == "" || activateUser.getAddress().isEmpty()) {
	 * offlineUser.setAddress("NA"); } else {
	 * offlineUser.setAddress(activateUser.getAddress()); }
	 * 
	 * if (accountCreated) {
	 * 
	 * mailSub = "Success : External Orders User Activation Status-Email : " +
	 * activateUser.getEmail();
	 * 
	 * // String assignNiomUrl = niomIP + "v1.0/assignsubscription/" + niomAuthKey;
	 * 
	 * String orderId = Long.toString(order.getOrder_id()); externalsku =
	 * order.getExternalsku();
	 * 
	 * if (orderChannel.equals("rv")) { orderId =
	 * Long.toString(order.getOrder_id()); } else { orderId =
	 * order.getExternal_order_id(); }
	 * 
	 * offlineUser.setStatus("Success");
	 * 
	 * int amount = 0; String desc = "";
	 * 
	 * async.createUserInChargebee(activateUser.getFirstname(),
	 * activateUser.getLastname(), activateUser.getEmail(),
	 * activateUser.getPhoneno(), userName, amount, desc);
	 * 
	 * } else { offlineUser.setStatus("Failed"); }
	 * 
	 * try { // Order Mapping Details Timestamp curTime =
	 * Timestamp.valueOf(IrisservicesUtil.getCurrentTimeUTC());
	 * 
	 * Timestamp orderdate = Timestamp.valueOf(order.getDatetime()); long userid =
	 * (orderMapUser != null ? orderMapUser.getId() : (long) 0L);
	 * 
	 * if (accountCreated == false) userid = 0L;
	 * 
	 * OrderMappingDetails orderMapDetail = new
	 * OrderMappingDetails(activateUser.getFirstname(), activateUser.getEmail(),
	 * activateUser.getAddress(), offlineUser.getDevicestateId(),
	 * activateUser.getQrcCode(), companyType, offlineUser.getStatus(), orderID,
	 * orderChannel, userid, curTime, orderdate, order.getExternalsku());
	 * log.info("orderMapDetail block", orderMapDetail);
	 * userService.saveOrderMappingDetails(orderMapDetail); } catch (Exception e) {
	 * log.error(" ActivateUser: Order Mapping Details: ", e.getLocalizedMessage());
	 * }
	 * 
	 * String statusEmailContent = new
	 * com.nimble.irisservices.helper.EmailContent().activationStatus(activateUser,
	 * provisionStatus, orderMappedtatus, updateExternalOrderDataStatus,
	 * activateSimStatus, alertCreationStatus, errorResponse, monitortype,
	 * devicecount);
	 * 
	 * async.saveorupdateofflineUserDetails(offlineUser);
	 * 
	 * if (sendActivateUserDataToSQS_Microservice) {
	 * async.sendEmailDataToQueue(mailSub, statusEmailContent, qrcCode, errorCode,
	 * accountCreated, inventory.getMeid(), inventory.getSim_no(),
	 * inventory.getSim_vendor()); } else {
	 * async.updateRegisterUserEmailStatus(to_address, cc_address, bcc_address,
	 * mailSub, statusEmailContent, qrcCode, errorCode, accountCreated,
	 * inventory.getMeid(), inventory.getSim_no(), inventory.getSim_vendor()); }
	 * 
	 * }
	 * 
	 * }
	 */

	public boolean deleteordermap(String meid, int initialDeviceStateid) {
		boolean status = niomDbservice.deleteMappedOrder(meid);

		boolean inventory_result = niomDbservice.updateInventoryNewMeid(initialDeviceStateid + "", meid, null);

		if (status && inventory_result) {
			return true;
		} else {
			return false;
		}
	}

	JSONObject getInventory(String qrcCode) {
		JResponse response = new JResponse();
		try {
			List<Inventory> inventory = niomDbservice.getInventory(qrcCode);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("inventory", inventory);

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Invalid authentication key");
			return new JSONObject(response).getJSONObject("response");
		}
		return new JSONObject(response).getJSONObject("response");

	}

	public boolean nameCheck(Orders order, ActivateUser activateUser) {
		if (!order.getBilling_first_name().equalsIgnoreCase(activateUser.getFirstname().toLowerCase())
				|| !order.getBilling_last_name().equalsIgnoreCase(activateUser.getLastname().toLowerCase())) {
			return true;
		} else {
			return false;
		}

	}

	private boolean updateExternalOrders(Orders order) {
		return niomDbservice.saveORupdateOrder(order);
	}

	public JSONObject getNiomGetOrderCount(String orderchannel, String orderid) {
		// TODO Auto-generated method stub

		JResponse response = new JResponse();

		List<Orders> orders = new ArrayList<Orders>();
		List<Ordermap> ordermap = new ArrayList<Ordermap>();

		try {
			orders = niomDbservice.getOrderById(orderchannel, orderid);

			String totalOrderedQuantity = "";

			String totalMappedCount = "";

			if (orders == null) {
				response.put("Status", 0);
				log.info("No Orders found for given orderchannel and order id : OrderChannel : " + orderchannel
						+ "Order ID  :" + orderid);
				response.put("Error", "No order found for respective order id");
				return new JSONObject(response).getJSONObject("response");
			}

			if (orders.size() > 0) {

				log.info("Orders found for given orderchannel and order id : OrderChannel : " + orderchannel
						+ " Order ID  :" + orderid);

				String quantityList[] = orders.get(0).getQuantity().split(":");

				totalOrderedQuantity = quantityList[0];

				ordermap = niomDbservice.checkOrderMappedCount(Long.toString(orders.get(0).getOrder_id()));

				if (ordermap != null) {
					totalMappedCount = Integer.toString(ordermap.size());
				} else {
					totalMappedCount = "0";
				}

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("Order", orders.get(0));
				response.put("Ordermap", ordermap);
				response.put("Totalordered", totalOrderedQuantity);
				response.put("Totalmapped", totalMappedCount);

				return new JSONObject(response).getJSONObject("response");

			} else {
				response.put("Status", 0);
				log.info("No Orders found for given orderchannel and order id : OrderChannel : " + orderchannel
						+ "Order ID  :" + orderid);
				response.put("Error", "No order found for respective order id");
				return new JSONObject(response).getJSONObject("response");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			log.error("GetMappedOrderCount Exception : " + e.getLocalizedMessage());
			return new JSONObject(response).getJSONObject("response");
		}
	}

	public boolean orderMapping(String meids, String mapped_date, long devicestateid, String l, String devicemodel,
			String subscriptioncreated, String isuserregistered) {
		log.info("Entered into orderMapping : meids : " + meids);
		boolean res = true;
		// Check if given meids are valid and are not already mapped
		String validStatus = niomDbservice.checkmeidsvalid(meids, devicemodel);

		if (!validStatus.equalsIgnoreCase("valid")) {
			log.info("orderMapping:: One or more meids are not valid for mapping." + validStatus);
			return false;
		}
		// Update the device status and order_id for given meid in inventory
		boolean inventory_result = niomDbservice.updateInventoryNewMeid("6", meids, l);
		String meid_list[] = meids.split(",");
		ArrayList<Long> locationids = new ArrayList<Long>();

		if (inventory_result) {
			if (locationids != null && !locationids.isEmpty()) {
				for (int i = 0; i < meid_list.length; i++) {
					Device_history device_history = new Device_history(devicestateid, meid_list[i].replaceAll("\'", ""),
							locationids.get(i), mapped_date);

					boolean device_res = niomDbservice.saveDeviceHistory(device_history);
					if (device_res == false) {
						return res;
					}
				}

				boolean map_date_res = niomDbservice.updateOrderMappedDate(mapped_date, l);

				if (map_date_res) {
					log.info("vieworder : order_id = " + l);
					List<Jorder> jorders = niomDbservice.getJorder(l);

					if (jorders.size() > 0) {

						String name = null;
						String username = null;

						if (jorders.get(0).getBilling_last_name() != "NA")
							name = jorders.get(0).getBilling_first_name() + " " + jorders.get(0).getBilling_last_name();
						else
							name = jorders.get(0).getBilling_first_name();

						if ((jorders.get(0).getBilling_email().length()) > 30) {
							int ind = (jorders.get(0).getBilling_email()).indexOf("@");
							username = (jorders.get(0).getBilling_email()).substring(0, ind);
						} else {
							username = jorders.get(0).getBilling_email();
						}

						for (int i = 0; i < meid_list.length; i++) {
							int orderid = Integer.valueOf(l);
							log.info("Input order id :" + l);

							Ordermap ordermap = new Ordermap(orderid, jorders.get(0).getDatetime(), name,
									jorders.get(0).getBilling_email(), jorders.get(0).getBilling_phone(),
									meid_list[i].replaceAll("\'", ""), username, "NA", jorders.get(0).getDevicemodel(),
									"1", jorders.get(0).getCustomer_note(), "Activated", "1111-11-11 11:11:11",
									_helper.getCurrentTimeinUTC(), subscriptioncreated, isuserregistered);

							boolean result = niomDbservice.saveORupdateMappedOrder(ordermap);

							if (result == false) {
								return res;
							}
						}
						res = true;
					}
				}
			} else {
				log.info("Location ids are empty");
			}
		}
		return res;
	}

	public boolean updateOrdersData(String orderchannel, Orders order, Inventory inventory) {

		boolean status = false;
		Gson gson = new Gson();

		JSONObject jorderIdCheckResponse = new JSONObject();

		jorderIdCheckResponse = getNiomGetOrderCount(orderchannel, order.getId() + "");

		if (jorderIdCheckResponse != null) {

			int orderIdCheckStatus;
			try {
				orderIdCheckStatus = jorderIdCheckResponse.getInt("Status");
			} catch (JSONException e) {
				// TODO Auto-generated catch block
				log.error(e.getLocalizedMessage());
				return false;
			}

			if (orderIdCheckStatus > 0 ? true : false) {

				try {
					order = gson.fromJson(jorderIdCheckResponse.getJSONObject("Order").toString(), Orders.class);
				} catch (JsonSyntaxException e) {
					// TODO Auto-generated catch block
					log.error(e.getLocalizedMessage());
					return false;
				} catch (JSONException e) {
					// TODO Auto-generated catch block
					log.error(e.getLocalizedMessage());
					return false;
				}
				order.setProvision_status(1);
				order.setEmail_status("111");
				order.setTracking_summary("NA");
				order.setStatus("completed");
				order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
				order.setWelcome_status(order.getWelcome_status());
				if (inventory.getDevicestate().getName().toLowerCase().contains("amazon")) {
					order.setFulfillmentchannel("amazon");
				} else {
					order.setFulfillmentchannel("rv");
				}

				boolean updateExternalOrderData = updateExternalOrders(order);
				log.info("updateExternalOrderData Status : " + updateExternalOrderData);

				status = updateExternalOrderData;
			} else {
				return false;
			}
		}

		return status;
	}

	public JResponse activateEseyeSim(String niomActivateEseyeSimURL, String iccids, String tariff, String groupname) {
		JResponse response = new JResponse();

		try {
			niomActivateEseyeSimURL = niomActivateEseyeSimURL + "?iccids=" + URLEncoder.encode(iccids.trim(), "UTF-8")
					+ "&tariff=" + tariff + "&groupname=" + groupname;
			HttpClient httpClient = HttpClientBuilder.create().build();
			HttpPost post = new HttpPost(niomActivateEseyeSimURL);

			post.setHeader("Content-type", "application/json");
			try {
				HttpResponse niomresponse = httpClient.execute(post);
				String niomRes = EntityUtils.toString(niomresponse.getEntity());
				JSONObject _response = new JSONObject();
				Gson _gson = new Gson();
				try {
					JSONObject _res = new JSONObject(niomRes);
					_response = _res.getJSONObject("response");
					int _status = _response.getInt("Status");
					String msg = _response.getString("Msg");
					response.put("Status", _status);
					response.put("Msg", msg);
					response.put("ErrorCode", "0");
				} catch (JSONException e) {
					response.put("Status", 0);
					response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
					response.put("ErrorCode", "ER026");
					log.error(e.getLocalizedMessage());
				}

			} catch (ClientProtocolException e) {
				response.put("Status", 0);
				response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
				response.put("ErrorCode", "ER027");
			} catch (IOException e) {
				response.put("Status", 0);
				response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
				response.put("ErrorCode", "ER028");
				log.error(e.getLocalizedMessage());
			}

		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Error while getting response from NIOM during activation of sim card .");
			response.put("ErrorCode", "ER029");
			log.error("activateEseyeSim : " + ex.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	public JCreateGateway createOrUpdateGateway(ActivateUser activateUser, Inventory inventory, int model_id,
			long group_id, String sensor_available, String passwordType, User user) {

		boolean updateCreaditStatus = false;
		boolean saveGatewayReportStatus = false;
		boolean saveLastGatewayReport = false;
		boolean isGatewayCreated = false;

		JCreateGateway jcreateGateway = new JCreateGateway();

		JGateway jgateway = new JGateway();
		jgateway.setName(activateUser.getGatewayName());
		jgateway.setMeid(inventory.getMeid());
		jgateway.setMdn(inventory.getMdn());
		jgateway.setCarrier("NA");
		jgateway.setModelid(model_id);
		jgateway.setGroupid(group_id);
		jgateway.setEnable(true);
		jgateway.setAlive(false);
		jgateway.setSensorEnable(sensor_available);
		jgateway.setPasswordtype(Long.parseLong(passwordType));
		jgateway.setDescription("");
		jgateway.setQrcode(activateUser.getQrcCode().trim());

		jgateway = gatewayService.gatewayExitsinDB(jgateway, user.giveCompany().getId());

		Gateway gateway = new Gateway();
		try {
			gateway = gatewayService.saveORupdateGateway(jgateway, user.giveCompany().getId());
			isGatewayCreated = true;
		} catch (Exception e1) {
			log.error("createOrUpdateGateway : " + e1.getLocalizedMessage());
			return null;
		}

		if (jgateway.isUserGatDis()) {
			user.getGateways().add(gateway);
			userService.updateUser(user);
		}

		try {
			gatewayService.updateGatewayCredit(gateway.getId(), user.giveCompany().getId());
			updateCreaditStatus = true;

		} catch (Exception e) {
			log.error("Gateway credits not updated for Gateway: " + gateway.getId() + "  : " + e.getMessage());
			log.error("Gateway credits not updated for Gateway: " + gateway.getId() + "  : " + e.getLocalizedMessage());
		}

		if (jgateway.getId() == 0) {
			try {
				reportService.saveGatewayReport(gateway, activateUser.getLat(), activateUser.getLon());
				saveGatewayReportStatus = true;
				reportService.saveLastGatewayReport(gateway, activateUser.getLat(), activateUser.getLon());
				saveLastGatewayReport = true;
			} catch (Exception e) {
				log.error("5saveORupdateGateway-Default Reports Cannot be Generated::::" + e.getLocalizedMessage());
			}
		}

		jcreateGateway.setUpdateCreaditStatus(updateCreaditStatus);
		jcreateGateway.setSaveGatewayReportStatus(saveGatewayReportStatus);
		jcreateGateway.setSaveLastGatewayReport(saveLastGatewayReport);
		jcreateGateway.setGatewayCreated(isGatewayCreated);
		jcreateGateway.setJgateway(jgateway);
		jcreateGateway.setGateway(gateway);

		return jcreateGateway;
	}

	@RequestMapping(value = "v4.0/updatepageviewcount/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updatepageviewcount(@PathVariable String autho,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		log.info("Entered into updatepageviewcount : autho : " + autho);
		JResponse response = new JResponse();

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
			} else {
				boolean result = userServiceV4.updateViewCount(usr.getId());

				if (result == true) {
					response.put("Status", 1);
					response.put("Msg", "Count updated");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error while updating count");
				}
			}
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while updating count");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception : updatepageviewcount : " + e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v4.0/getamazonreviewstatus/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getAmazonReviewStatus(@PathVariable String autho,
			@RequestParam("gatewayid") long gatewayid) {
		log.info("Entered into updatepageviewcount : autho : " + autho);
		JResponse response = new JResponse();

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
			} else {
				if (showamazonrateus) {
					AmazonUserReview amazonReviewObj = userServiceV4.getAmazonUserReview(usr.getId(),1);
					AmazonReviewStatus reviewStatus = new AmazonReviewStatus();
					int viewCount = 0;
					int rptCount = 0;
					String lastRptDate = "";

					if (amazonReviewObj == null || (!amazonReviewObj.isAmazon_rateus())) {
						viewCount = userServiceV4.getViewCount(usr.getId());
						rptCount = rptServicev4.getGatewayReportCount(gatewayid);
						lastRptDate = rptServicev4.getLastGatewayReporttime(gatewayid);

						String currenttime = IrisservicesUtil.getCurrentTimeUTC();
						SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
						formatter.setTimeZone(TimeZone.getTimeZone("UTC"));

						Date pre = formatter.parse(lastRptDate);
						Date curr = formatter.parse(currenttime);
						log.info(pre + " : " + curr);

						long diffInMins = ((curr.getTime() - pre.getTime()) / 60000);
						log.info(" diffInMins : " + diffInMins);

						int amazon_redirect_cnt = 3;
						if ((viewCount % amazonrateuscount == 0) && (rptCount > 1000) && (diffInMins <= 60)) {
							boolean show_dialog = true;
							String redirecturl = amazonredirecturl;

							reviewStatus = new AmazonReviewStatus(redirecturl, show_dialog, amazon_redirect_cnt, "");

							response.put("reviewstatus", reviewStatus);
							response.put("Status", 1);
							response.put("Msg", "Success");
						} else {
							response.put("Status", 0);
							response.put("Msg", "Amazon rating not applicable");
						}

					} else {
						response.put("Status", 0);
						response.put("Msg", "Already amazon rating completed");
					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "Amazon Rating not enabled");
				}
			}
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while updating count");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception : updatepageviewcount : " + e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v4.0/updateamazonreview/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateAmazonReview(@PathVariable String autho,
			@RequestBody AmazonUserReview auReview,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver,
			@RequestParam(value = "monitorType", defaultValue = "1", required = false) long monitorType) {
		log.info("Entered into updatepageviewcount : autho : " + autho);
		JResponse response = new JResponse();

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
			} else {
				boolean result = userServiceV4.updateAmazonReview(auReview,monitorType);

				if (result == true) {
					response.put("Status", 1);
					response.put("Msg", "Review updated");
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error while updating count");
				}
			}
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while updating count");
			response.put("Error", e.getLocalizedMessage());

			log.error("Exception : updatepageviewcount : " + e.getLocalizedMessage());
		}
		return response;
	}

	// Used in web
	// ========get user by id -By Savitha ================
	@RequestMapping(value = "v4.0/getuserbyidweb/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserByIdWebV4(@PathVariable String autho, @RequestParam("userid") Long userid) {
		JResponse response = new JResponse();
		log.info("Entering getUserByIdWebV4 : " + userid);
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("id", userid.toString());

			} catch (InvalidAuthoException e) {
				log.error("Invalid UserId :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid User!");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}

			if (!(user.getAuthKey().equalsIgnoreCase(autho))) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
				return response;
			}
			
			String password = user.getPassword();
			if( user.getPassword_ver().equalsIgnoreCase("V2") ) {
				password = userServiceV4.getEValidationPassword(user.getId());
				user.setPassword(password);
			}
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("users", user);

		} catch (Exception e) {
			log.error("Exception : getUserByIdWebV4 :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	// Used in web
	// userUpdateV4 - By Savitha
	@RequestMapping(value = "v4.0/userupdateweb/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse userUpdateWebV4(@PathVariable String autho, @RequestBody UserV4 user) {
		JResponse response = new JResponse();
		log.info("Entering userUpdateWebV4 : " + autho);
		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.error("Invalid UserId :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey!");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}

			long user_id = userServiceV4.getUserByUNameOrEmailV4(user.getEmail());
			if (user_id > 0 && (usr.getId() != user_id)) {
				response.put("Status", 0);
				response.put("Msg", "Email  already exist. Please enter alternate Email!");
				return response;
			}
			
			String password = user.getPassword();
//			UserV4 existingUser = userServiceV4.verifyAuthV3("authkey", user.getAuthKey());
//			if( !password.equals( existingUser.getPassword() ) ) {
//				
//				JValidateString validatePassword = userServiceV4.validatePassword(password);
//				if( !validatePassword.isValid() ) {
//					response.put("Status", 0);
//					response.put("Msg", validatePassword.getMsg());
//					return response;
//				}
				
				user.setPassword( _helper.bCryptEncoder(password) );
				user.setPassword_ver("V2");
//			}

			user.setUpdatedOn(_helper.getCurrentTimeinUTC());
			String mobileNo = user.getMobileno();

			if (!mobileNo.contains("-") && mobileNo.length() > 0) {
				mobileNo = new CountryCode().getCountryCode(user.getCountry().toUpperCase()) + user.getMobileno();
				user.setMobileno(mobileNo);
			}

			if (user.getEmail() == null || user.getEmail().equalsIgnoreCase("NA"))
				user.setEmail(usr.getEmail());
			
			boolean Status = userServiceV4.updateUserv4byuseridWeb(user);
			if(Status)
				async.updateEvalidation(user.getId(), password);
			
			if (Status) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "UnExcepted Error in User Update WebV4");
			}

		} catch (Exception e) {
			log.error("Exception : userUpdateWebV4 : " + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in User Update WebV4");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}

	// Used in web
	// ========get user by id -By Savitha ================
	@RequestMapping(value = "v4.0/getamazonreviewlist/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAmazonReviewList(@PathVariable String autho) {
		JResponse response = new JResponse();
		log.info("Entering getAmazonReviewList : ");

		try {
			List<AmazonReviewList> amazonReviewList = userServiceV4.getAmazonReviewList();
			if (amazonReviewList != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("amazonreviewlist", amazonReviewList);
				log.info("Amazon Review list found!");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Exception Occurred/No reviews found!");
				log.info("No Amazon Review List found!");
			}
			log.info("Exit :: AmazonReviewList ::");

		} catch (Exception e) {
			log.error("Exception : AmazonReviewList :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured !" + e.getLocalizedMessage());
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	// Used in web
	// v4.0/userV2/ - Savitha
	@RequestMapping(value = "v4.0/userV2web/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserV2_V4Web(@PathVariable String autho, @RequestParam("userid") String userid,
			@RequestParam("cmpid") String cmpid,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		log.info("Entering getUserV2_V4_web : " + autho);
		JResponse response = new JResponse();
		UserV4 user = null;
		List<UserV4> usersList = new ArrayList<UserV4>();
		List<UserV4> responseList = new ArrayList<UserV4>();
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);
		} catch (InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + autho);
			return response;
		}
		try {
			long cmp_id = 0;
			if (user != null) {
				cmp_id = user.getCmpId();
				if (cmpid.trim().isEmpty())
					cmpid = String.valueOf(user.getCmpId());

				usersList = userServiceV4.getUsersByUserId_CmpId(userid, Long.parseLong(cmpid));

				if (usersList != null && usersList.size() > 0) {
					for (UserV4 thisUser : usersList) {
						if (userid.trim().isEmpty())
							userid = String.valueOf(thisUser.getId());
						String mobile = "";
						UserV4 newUser = thisUser;
						if (thisUser != null) {
							String mobileNumber = thisUser.getMobileno();
							String[] phoneNumber = null;

							if (!mobileNumber.contains("-")) {
								mobileNumber = "-" + mobileNumber;
							}
							phoneNumber = mobileNumber.split("-");
							String cont = phoneNumber[0];
							String mob = phoneNumber[1];

							if (thisUser.getCountry().equalsIgnoreCase("NA") || thisUser.getCountry() == null) {
								if (cont.equalsIgnoreCase("+91") || cont.equalsIgnoreCase("91")) {
									thisUser.setCountry("IN");
								} else if (cont.equalsIgnoreCase("+44") || cont.equalsIgnoreCase("44")) {
									thisUser.setCountry("GB");
								} else {
									thisUser.setCountry("US");
								}
							}
							mobile = mob.replaceAll("\\W", "");
							thisUser.setMobileno(mobile);
							responseList.add(newUser);
						}
					}
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("users", responseList);

				} else {
					response.put("Status", 0);
					response.put("Msg", "User not found!");
					response.put("users", responseList);
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "User not found!");
				response.put("users", responseList);
			}
		} catch (Exception e) {
			log.error("Error : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v4.0/forgetpassword", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse oneTimePassword(@RequestHeader HttpHeaders header,
			@RequestParam(value = "username") String userName,
			@RequestParam(value = "via", defaultValue = "mobileno") String via, @RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver) {
		log.info(" Entered into forgetpassword v4 :: username :" + userName);
		JResponse response = new JResponse();

		String auth = header.getFirst("auth");
		int localOtpTimer=0;
		localOtpTimer=validMinutesForOTP;
		log.info("generateaddonpurchaselink :" + auth);

		try {
			
			if (!validation_authkey.equals(auth)) {
				userServiceV4.verifyAuthV4("authkey", auth);
			}
			
		} catch (InvalidAuthoException ex) {

			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			log.error("Exception while getting user for auth : " + auth);
			return response;

		}

		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("username", userName);
			} catch (InvalidAuthoException e) {
				log.error("Invalid Authkey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				return response;
			}

			if (!user.isEnable()) {
				response.put("Status", 0);
				response.put("Msg", "Account Disabled. Please contact support!");
				return response;
			}

			log.info(" user id : " + user.getId());

			int getOtp = userServiceV4.checkWithInOtpTimeLimit("one_time_password","userid",String.valueOf(user.getId()));

			long max = 9876;
			long min = 1234;
			int otp = (int) (Math.random() * (max - min + 1) + min);

			//System.out.println(otp);

			OneTimePassword otpObj = new OneTimePassword();

			otp=(getOtp==(-1))?otp:getOtp;
			otpObj.setOtp(otp);
			otpObj.setCreatedOn(_helper.getCurrentTimeinUTC());
			otpObj.setUserId(user.getId());

			boolean isOTPSaved =getOtp==(-1)? userServiceV4.saveOTP(otpObj):false;
			log.info(" OTP Generated : " + isOTPSaved + " OTP : " + otp);

			String smsMsg = otp + " is your OTP to reset your Waggle Pet App password.";
			String otpInfo = "";

			String email = user.getEmail();
			String phoneNo = user.getMobileno();

			if(getOtp!=-1)
			{
				int remainingTimer=0;
				remainingTimer=userServiceV4.getOtpTimeLimit("one_time_password","userid",String.valueOf(user.getId()));
				localOtpTimer=	remainingTimer==-1 ? localOtpTimer : remainingTimer;
			}

			StringBuilder hiddenEmail = new StringBuilder();

			if (via.equalsIgnoreCase("mobileno")) {

				String companyName = companyService.getCompanyById(user.getCmpId()).getName();
				
				boolean smsSent = messagingService.savePlivoData(user.getMobileno(), smsMsg, String.valueOf(user.getCmpId()), companyName, "waggle","sms");

				phoneNo = _helper.encodeString(phoneNo, 4, '*'); // params ( String, no_of_String_to_visible, encodeChar )
				otpInfo = "<div style='color:#fff;'><p><center><h3>Verify your sms</h3>" + "Please enter the 4 digit code sent to " + phoneNo
						+ " </center></p></div>";

			} else {
				Template oneTimePassword = (Template) templates.getTemplate("OneTimePassword.ftl");
				Map<String, String> OTPEmailParams = new HashMap<>();
				String minutes = localOtpTimer==1?" minute ":" minutes ";
				OTPEmailParams.put("OTP", String.valueOf(otp));
				OTPEmailParams.put("valid_minutes",  localOtpTimer +minutes);
				ResponseEntity<String> newEmailContent = ResponseEntity
						.ok(FreeMarkerTemplateUtils.processTemplateIntoString(oneTimePassword, OTPEmailParams));
				String emailContent = newEmailContent.getBody();
				email_helper.SendEmail_SES(user.getEmail(), null, "<EMAIL>", "Reset Password",
						emailContent);

				for (int i = 0; i < email.length(); i++)
					hiddenEmail.append((i >= 2 && i < email.indexOf('@')) ? '*' : email.charAt(i));

				otpInfo = "<div style='color:#fff;'><p><center><h3>Verify your email</h3>" + "Please enter the 4 digit code sent to "
						+ hiddenEmail + " </center></p></div>";
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("otp_info", otpInfo);
			response.put("valid_otp_time", localOtpTimer * 60);

		} catch (Exception e) {
			log.error(" Error in forgetpassword v4 " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		return response;
	}

	@RequestMapping(value = "v4.0/validateotp", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse validateOneTimePassword(@RequestHeader HttpHeaders header, @RequestParam long otp,
			@RequestParam(value = "username") String userName, @RequestParam(value = "os") String os,
			@RequestParam(value = "app_ver") String app_ver) {

		log.info(" Entered into validateOneTimePassword :: username :" + userName);
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");

		log.info("generateaddonpurchaselink :" + auth);

		try {
			
			if (!validation_authkey.equals(auth)) {
				userServiceV4.verifyAuthV4("authkey", auth);
			}
			
		} catch (InvalidAuthoException ex) {

			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			log.error("Exception while getting user for auth : " + auth);
			return response;

		}
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("username", userName);
			} catch (InvalidAuthoException e) {
				log.error("Invalid Authkey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				return response;
			}

			log.info(" user id : " + user.getId());

			OneTimePassword otpObj = new OneTimePassword();

			otpObj.setOtp((int)otp);
			otpObj.setUserId(user.getId());

			boolean validOTP = userServiceV4.validateOTP(user.getId(), otpObj, validMinutesForOTP);

			log.info(" is valid OTP : " + validOTP);

			if (validOTP) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "OTP not valid");
			}

		} catch (Exception e) {
			log.error(" Error in validateOneTimePassword " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		return response;
	}

	@RequestMapping(value = "v4.0/getuserbyusername", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserByUsername(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestParam("name") String name) {

		JResponse response = new JResponse();
		String auth = header.getFirst("auth");

		log.info("Entering getuserbyusername : " + name + "auth key : " + auth);
		name = name.toLowerCase().trim();
		name = name.replaceAll("\\s", "");
		UserV4 user = null;
		try {
			if (!auth.equalsIgnoreCase(validation_authkey)) {
				response.put("Status", 0);
				response.put("Msg", "Authentication Error");
				return response;
			}

			try {
				user = userServiceV4.verifyAuthV3("username", name);
			} catch (InvalidAuthoException e) {
				log.error("Invalid Username :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}

			if (user == null || !(user.getUsername().equals(name))) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Username");
				return response;
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("users", user);

		} catch (Exception e) {
			log.error("Exception : getUserByUsernameV4 :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured!");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/pwdupdatev2", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse passwordUpdateV4(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver, @RequestBody UpdatePassword udatePassword) {

		log.info(" Entered into Password Update v2 ");

		JResponse response = new JResponse();

		String autho = header.getFirst("auth");

		byte[] passwordByte = {};
		try {
			passwordByte = Base64.getDecoder().decode(udatePassword.getPassword().getBytes("UTF-8"));
		} catch (UnsupportedEncodingException e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Password not updated. Please try again later.");
			return response;
		}
		
		String password = "";
		try {
			password = new String(passwordByte, "UTF-8");
			if(os.equalsIgnoreCase("ios")) {
				password = _helper.urlDecoder(password);	
			}
			
			JValidateString validatePassword = userServiceV4.validatePassword(password);	
			if( !validatePassword.isValid() ) {
				response.put("Status", 0);
				response.put("Msg", validatePassword.getMsg());
				return response;
			}
			
		} catch (UnsupportedEncodingException e1) {
			response.put("Status", 0);
			response.put("Msg", "Password not updated. Please try again later.");
			return response;
		}

		String userName = udatePassword.getUsername();

		UserV4 user = null;

		try {
			if (!validation_authkey.equals(autho)) {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			}

		} catch (InvalidAuthoException ex) {

			response.put("Status", 0);
			response.put("Msg", "Password not updated. Please try again later.");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + autho);
			return response;

		}

		try {

			String bCryptPassword = _helper.bCryptEncoder(password);
			
			int status = userServiceV4.updateUserPasswordV2(userName, bCryptPassword);
			if(status>0) {
				user = userServiceV4.verifyAuthV4("username", userName);
				async.updateEvalidation(user.getId(), password);
			}
				

			if (status <= 0) {
				log.error("Invalid Username : ");
				response.put("Status", 0);
				response.put("Msg", "Password not updated. Please try again later.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			response.put("Status", 1);
			response.put("Msg", "Password updated successfully");
		} catch (Exception e) {
			log.error("Exception : passwordUpdateV4 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Password not updated. Please try again later.");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}
	
	@RequestMapping(value = "v4.0/validateCurrentPassword", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse validateCurrentPassword(@RequestHeader HttpHeaders header, @RequestParam("os") String os,
			@RequestParam("app_ver") String app_ver,@RequestParam("currentPassword") String currentPassword) {

		log.info(" Entered into Password Update v2 ");

		JResponse response = new JResponse();

		String autho = header.getFirst("auth");

		byte[] passwordByte = {};
		try {
			passwordByte = Base64.getDecoder().decode(currentPassword.getBytes("UTF-8"));
		} catch (UnsupportedEncodingException e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in Base64 decoding");
			return response;
		}

		String password = "";
		try {
			password = new String(passwordByte, "UTF-8");
		} catch (UnsupportedEncodingException e1) {
			response.put("Status", 0);
			response.put("Msg", "Password format error");
			return response;
		}

		UserV4 user = null;

		try {
			
			user = userServiceV4.verifyAuthV4("authkey", autho);
			boolean passwordMatch = _helper.checkUserCredencial(password, user.getPassword(), user.getPassword_ver());
				
			if( passwordMatch ) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				return response;
			}else {
				response.put("Status", 0);
				response.put("Msg", "Current Password is Incorrect.");
				return response;
			}

		} catch (InvalidAuthoException ex) {

			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : " + autho);
			return response;

		}
	}
	
	//Used in web - Savitha
	@RequestMapping(value = "v4.0/getevalidationbyuser", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getEValidationPassword(@RequestHeader HttpHeaders header) {
		JResponse response = new JResponse();
		String auth = header.getFirst("auth");
		String user_id = header.getFirst("user_id");

		log.info("Entering getEValidation by user : auth key : " + auth);
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("authkey", auth);
			} catch (InvalidAuthoException e) {
				log.error("Invalid AuthKey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid AuthKey");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}

			if (user != null && user.getRole().getId() != 8) {
				response.put("Status", 0);
				response.put("Msg", "Access denied!");
				return response;
			}

			String evalidPwd = userServiceV4.getEValidationPassword(Long.parseLong(user_id));
			if (evalidPwd != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("EValid", evalidPwd);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Exception occurred!");
				response.put("EValid", evalidPwd);
			}
		} catch (Exception e) {
			log.error("Exception : getEValidationPassword :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occurred!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

}
