package com.nimble.irisservices.controller;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.Advertisement;
import com.nimble.irisservices.entity.AppImage;
import com.nimble.irisservices.service.IAdvertisementService;
import com.nimble.irisservices.service.IReferAndEarnServiceV4;

@Controller
public class AdvertisementControllerV4 {


	private static final Logger log = LogManager.getLogger(AdvertisementControllerV4.class);

	@Autowired
	@Lazy
	IAdvertisementService advService;
	
	@Autowired
	IReferAndEarnServiceV4 referAndEarnServiceV4;

	// No changes compared to V3
	@RequestMapping(value = "v4.0/getAdvertisements", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getadvertisementinfoV4(@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "ver", defaultValue = "v1", required = false) String ver,
			@RequestParam(value = "type", defaultValue = "", required = false) String type,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
			) {

		JResponse response = new JResponse();
		try {

			List<Advertisement> advList = advService.getAdvertismentUrl(ver);
			
			// This is only for events
			if(ver.equalsIgnoreCase("v2")) {
				String imgname = "events";
				String imgpath = "";
				
				if (!imgname.trim().isEmpty() && !type.trim().isEmpty()) {
					AppImage appimage = referAndEarnServiceV4.getAppImages(type, imgname);
					imgpath = appimage.getImg_path();
				}
				
				advList.get(0).setImgpath(imgpath);
			}
			response.put("Status", 1);
			response.put("Msg", "success");
			response.put("advList", advList);
			log.error("getadvertisementinfo Created");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "failure");
			log.error("getadvertisementinfo : " + e.getLocalizedMessage());
		}

		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

}
