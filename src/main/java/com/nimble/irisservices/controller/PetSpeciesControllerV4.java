package com.nimble.irisservices.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.PetBreeds;
import com.nimble.irisservices.entity.PetSpecies;
import com.nimble.irisservices.service.IPetSpeciesServices;
import com.nimble.irisservices.service.IPetSpeciesServicesV4;

@Controller
public class PetSpeciesControllerV4 {

	private static final Logger log = LogManager.getLogger(PetSpeciesControllerV4.class);


	@Autowired
	@Lazy
	IPetSpeciesServices ipetSpeciesServices;
	
	@Autowired
	@Lazy
	IPetSpeciesServicesV4 ipetSpeciesServicesv4;

	
	
	// No changes compared to V3
	@RequestMapping(value = "v4.0/getspecies", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSpeciesV4(@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		try {

			List<PetSpecies> petSpecies = new ArrayList<PetSpecies>();

			petSpecies = ipetSpeciesServices.getPetSpecies();

			if (petSpecies != null) {
				if (petSpecies.size() > 0) {

					List<String> species = new ArrayList<String>();

					for (PetSpecies specie : petSpecies) {
						species.add(specie.getSpeciesName());
					}

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("petSpecies", species);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "No Species found");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "No Species found");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		} catch (Exception e) {
			log.error("Excepitoin while getting pet species:"+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting pet species.");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}

	// getBreedsV4 - by anand
	@RequestMapping(value = "v4.0/getbreeds", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getBreedsV4(
			@RequestParam(value = "speciename", defaultValue = "", required = false) String speciename,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver) {
		JResponse response = new JResponse();
		log.info("Entering getBreedsV4 : ");
		try {

			List<PetBreeds> petBreeds = new ArrayList<PetBreeds>();

			petBreeds = ipetSpeciesServicesv4.getPetBreedsV4(speciename);

			if (petBreeds != null) {
				if (petBreeds.size() > 0) {
					if (speciename.isEmpty()) {
						response.put("petBreeds", petBreeds);
					} else {
						List<String> breedName = new ArrayList<String>();

						for (PetBreeds breed : petBreeds) {
							breedName.add(breed.getBreedName());
						}

						Collections.sort(breedName);

						response.put("petBreeds", breedName);
						response.put("Msg", "Success");
					}

					response.put("Status", 1);

					response.put("Return Time", System.currentTimeMillis());
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "No pet breeds found");
					log.error("No pet breeds found");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "No pet breeds found");
				log.error("No pet breeds found");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting pet breeds.");
			response.put("Error", e.getLocalizedMessage());
			log.error("Excepitoin : getBreedsV4 : " + e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
	}

}
