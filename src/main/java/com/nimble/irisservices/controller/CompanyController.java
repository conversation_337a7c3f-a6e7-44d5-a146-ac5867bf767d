package com.nimble.irisservices.controller;

import java.lang.reflect.Type;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.Ordermap;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class CompanyController {

	private static final Logger log = LogManager.getLogger(CompanyController.class);

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;
	
	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	Helper _helper = new Helper();

	@Value("${verificationtime}")
	private long verifytime;

	@Value("${buynowfurbit}")
	private String buynowfurbit;

	@Value("#{${buynowpetsafety}}")
	private Map<String,String> buynowpetsafety;
	
	@Autowired 
	@Lazy
	IUserServiceV4 userServiceV4;
	
	@Value("${niomip}")
	private String niomIP;

	@Value("${niomauthkey}")
	private String niomAuthKey;
	// Used in web
	// ========get company ================
	@RequestMapping(value = "v3.0/company/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCompanyById(@PathVariable String autho) {
		JResponse response = new JResponse();

		User user;
		try {
			user = userService.verifyAuthKey(autho);
			Company cmp = companyService.getCompany(user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("company", cmp);

		} catch (InvalidAuthoException e) {

			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}
	
	// ==========update company========

	@RequestMapping(value = "v3.0/company/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse saveCompany(@PathVariable String autho, @ModelAttribute Company cmp,
			BindingResult result) {
		System.out.println("save user");
		JResponse response = new JResponse();
		try {
			companyService.updateCompany(cmp);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("Company", cmp);
		} catch (Exception e) {
			log.error("saveCompany:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Company");

		}

		return response;

	}

	// ==========delete company========
	@RequestMapping(value = "v3.0/company/{cmp_id}/{autho}", method = RequestMethod.DELETE, headers = "Accept=application/json")
	public @ResponseBody JResponse deleteCompany(@PathVariable String autho, @PathVariable long cmp_id)

	{

		JResponse response = new JResponse();

		companyService.deleteCompany(cmp_id);

		response.put("Status", 1);
		response.put("Msg", "Success");
		return response;
	}

	//Used in web
	// ==========get companyconfig========
	@RequestMapping(value = "v3.0/companyconfig/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getCompanyConfig(@PathVariable String autho) {

		JResponse response = new JResponse();
		Gson _gson = new Gson();
		

		User user;
		try {

			user = userService.verifyAuthKey(autho);

			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());

			response.put("Status", 1);
			response.put("Msg", "Success");

			response.put("company config", cmp_cfg);
			response.put("companyconfig", cmp_cfg);
			if (user.giveCompany().getCompanytype().getId() == 3) {

				boolean registerProduct = true;				
				
				if ((user.getUsername().matches("[0-9]+") && user.getUsername().length() == 6)) {
					
					//String niomIP = _helper.getExternalConfigValue("niomip", externalConfigService);
					//String niomAuthKey = _helper.getExternalConfigValue("niomauthkey", externalConfigService);
					String niomCheckMEIDURL = _helper.getExternalConfigValue("niomcheckmappedorderurl", externalConfigService);

					Set<Gateway> gatewayList = user.getGateways();

					if (gatewayList.size() > 1 || gatewayList.size() < 1) {
						registerProduct = true;
					} else if (gatewayList.size() == 1) {

						Gateway gat = gatewayList.iterator().next();

						List<Ordermap> orderMapList = new ArrayList<Ordermap>();

						JSONObject _response = new JSONObject();

						String IsCheckMEIDMappedResponse = null;
						try {
							String url = niomIP+niomCheckMEIDURL +niomAuthKey+ "/"+gat.getMeid();
							
							IsCheckMEIDMappedResponse = _helper.getURL(url);

							JSONObject meidDetailsResponse = new JSONObject(IsCheckMEIDMappedResponse);
							_response = meidDetailsResponse.getJSONObject("response");

							int _status = _response.getInt("Status");

							if (_status > 0 ? true : false) {

								Type orderListType = new TypeToken<ArrayList<Ordermap>>() {
								}.getType();
								orderMapList = _gson.fromJson(_response.getJSONArray("OrderMap").toString(),
										orderListType);
								if (orderMapList.size() > 0) {
									registerProduct = true;
								}else{
									registerProduct = false;
								}
							} else {

								registerProduct = false;
							}

						} catch (Exception e) {
							log.error("getcompanyconfig"+e.getLocalizedMessage());
							registerProduct = true;
						}
					}
				}
				
				/*user token verification
				 2 - user token verification Status pending and with in x[ config param] hrs
				 1 - user token verification Status verified 
				 0 - user token verification Status pending and more than x[ config param] hrs
				 
				 */
				
				String userStatus = "0";
				try {
					Calendar curCal = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
					System.out.println(curCal);
					DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
					Date date = sdf.parse(user.getCreatedOn());
					Calendar createdCal = Calendar.getInstance();
					createdCal.setTime(date);
					
					long timeMills = curCal.getTimeInMillis()-createdCal.getTimeInMillis();
					long verifyMills = verifytime * 60 * 60 * 1000;
					//user verification time - config parameter from properties file
					
					if( user.isVerified()==true) {
						userStatus = "1";
					}
					else if(timeMills<verifyMills && (user.isVerified() == false)) {
						userStatus = "1";	
					}				 
					else if(timeMills>verifyMills &&( user.isVerified() == false)) {
						userStatus = "1";
					}
					
				}catch(Exception e) {
					log.error("getCompanyConfig : " + e.getMessage());
					
				}
				
				response.put("notificationStatus", user.isNotification());
				response.put("isProductRegistered", registerProduct);

				response.put("upgradeSubscriptionUrl",
						_helper.getExternalConfigValue("upgradesubscriptionurl", externalConfigService));

				response.put("activateSubscriptionUrl",
						_helper.getExternalConfigValue("activatesubscriptionurl", externalConfigService));

				response.put("registerProductUrl",
						_helper.getExternalConfigValue("registerproducturl", externalConfigService));
				
				response.put("buynowfurbit",buynowfurbit);
				
				String country = user.getCountry().toUpperCase();
				if(country.equalsIgnoreCase("US")|| country.equalsIgnoreCase("NA")
						|| country.toLowerCase().contains("india") || country.equalsIgnoreCase("in")
						|| country.isEmpty() || country == null) {
					country = "US";
				}
				
				response.put("buynowpetsafety",buynowpetsafety.get(country));
				
				response.put("userverificationStatus", userStatus);

			}

		} catch (InvalidAuthoException e) {

			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");

			return response;
		}
		return response;
	}
	
	// ==========update companyconfig========
	@RequestMapping(value = "v3.0/companyconfig/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse saveCompanyConfig(@PathVariable String autho, @ModelAttribute CompanyConfig cmpcfg,
			BindingResult result, @RequestParam("cmpid") String cmpid) {
		System.out.println("save user");
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			cmpcfg.setCompany(user.giveCompany());
//			if (!cmpid.isEmpty())
//				cmpcfg.setCompany(companyService.getCompany(Long.valueOf(cmpid)));
			boolean prevRealTimeMonitor = cmpcfg.isRealtimemonitor();
			
			if(cmpcfg.getId() >0 ) {
				CompanyConfig prevcmp_cfg = companyService.getCompanyConfig(cmpcfg.giveCompany().getId());
				prevRealTimeMonitor = prevcmp_cfg.isRealtimemonitor();
			}
			companyService.updateCompanyCfg(cmpcfg, prevRealTimeMonitor);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("CompanyConfig", cmpcfg);

		} catch (InvalidAuthoException e) {

			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");

			return response;
		} catch (Exception e) {
			log.error("saveCompanyConfig:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Companyconfig");
		}
		return response;
	}

	//Used in web
	@RequestMapping(value = "v3.0/companyconfigv2/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse saveCompanyConfigV2(@PathVariable String autho, @ModelAttribute CompanyConfig cmpcfg,
			BindingResult result, @RequestParam("cmpid") String cmpid) {
		System.out.println("save user");
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			cmpcfg.setCompany(user.giveCompany());
			if (!cmpid.isEmpty())
				cmpcfg.setCompany(companyService.getCompany(Long.valueOf(cmpid)));

			boolean prevRealTimeMonitor = cmpcfg.isRealtimemonitor();
			
			if(cmpcfg.getId() >0 ) {
				CompanyConfig prevcmp_cfg = companyService.getCompanyConfig(cmpcfg.giveCompany().getId());
				prevRealTimeMonitor = prevcmp_cfg.isRealtimemonitor();
			}
			
			companyService.updateCompanyCfg(cmpcfg,prevRealTimeMonitor);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("CompanyConfig", cmpcfg);

		} catch (InvalidAuthoException e) {

			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");

			return response;
		} catch (Exception e) {
			log.error("saveCompanyConfig:::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in Companyconfig");
		}
		return response;
	}
	
	//Used in web
	// ========get company List================
	@RequestMapping(value = "v3.0/companylist/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getCompanyList(@PathVariable String autho) {
		JResponse response = new JResponse();

		User user;
		try {
			user = userService.verifyAuthKey(autho);
			List<Company> cmplist = companyService.getCompanyList(user.getRole().getId(), user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("companylist", cmplist);

		} catch (InvalidAuthoException e) {

			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}

	//Used in web
	// ==========get companyconfig version 3.1========
	@RequestMapping(value = "v3.1/companyconfig/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getCompanyConfigList(@PathVariable String autho,
			@RequestParam("cmpid") String cmpid) {
		JResponse response = new JResponse();

		User user;
		try {
			user = userService.verifyAuthKey(autho);
			long cmp_id = user.giveCompany().getId();
			if (!cmpid.isEmpty())
				cmp_id = Long.valueOf(cmpid);

			List<CompanyConfig> cmp_cfglist = companyService.getCompanyConfigList(user.getRole().getId(), cmp_id);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("companyconfig", cmp_cfglist);

		} catch (InvalidAuthoException e) {

			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");

			return response;
		}
		return response;
	}
	
	// ========get vetplus details ================
	@RequestMapping(value = "v3.0/getvpmdetails/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getvetplusdetails(@PathVariable String autho) {
		JResponse response = new JResponse();

		UserV4 user = null;
		try {
			user = userServiceV4.verifyAuthV4("authkey", autho);
			if(user != null) {
				boolean vetplus_enable = companyService.getVetCallStaus(user.getCmpId());
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("vetplus", vetplus_enable);
			}else {
				response.put("Status", 0);
				response.put("Msg", "Invalid user");
			}

		} catch (InvalidAuthoException e) {
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}
}
