package com.nimble.irisservices.controller;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.nimble.irisservices.Util.SecretManagerService;
import org.apache.http.entity.StringEntity;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.ocpsoft.prettytime.PrettyTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.amazonaws.services.sqs.model.SendMessageResult;
import com.google.gson.Gson;
import com.nimble.irisservices.dto.JFcmNotification;
import com.nimble.irisservices.dto.JPushNotifications;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSendNotification;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.NotificationType;
import com.nimble.irisservices.entity.PushNotificationStatus;
import com.nimble.irisservices.entity.PushNotifications;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserMetaData;
import com.nimble.irisservices.entity.UserToken;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.pojo.CloudNotifications;
import com.nimble.irisservices.pojo.FCMNofitication;
import com.nimble.irisservices.pojo.JSendNotifications;
import com.nimble.irisservices.pojo.SendNotifications;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IFirebaseService;
import com.nimble.irisservices.service.IPushNotificationService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class PushNotificatonController {

	@Value("${aws_sqs_secret_name}")
	private String SQS_SECRET_NAME;

	@Autowired
	private SecretManagerService secretManagerService;

	private String AWS_SQS_ACCESS_KEY;

	private String AWS_SQS_SECRET_KEY;

	@Value("${iris.services.amazonSQS.microserviceQueue.url}")
	private String amazonSqs_microserviceUrl;

	@Autowired
	@Lazy
	IPushNotificationService iNotificationService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	Helper _helper = new Helper();

	PrettyTime prettyTime = new PrettyTime();

	@Autowired
	@Lazy
	IUserService userService;
	
	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;
	
	@Autowired
	IFirebaseService iFirebaseService;

	private static final Logger log = LogManager.getLogger(PushNotificatonController.class);
	
	@RequestMapping(value = "v5.0/deleteusernotification/{autho}/{notificationId}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deleteUserNotificaitonsV5(@PathVariable String autho, @PathVariable String notificationId) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			boolean status = iNotificationService.deleteUserNotification(Long.toString(user.getId()), notificationId);
			if (status) {
				response.put("Status", 1);
				response.put("Msg", "Notification is deleted successfully.");
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Error in deleting the user notifications.");
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "Invalid authentication key");
			return response;
		} catch (Exception ex) {
			log.error(ex.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in deleting the user pushnotifications.");
			return response;
		}
	}

	@RequestMapping(value = "v3.0/createnotification/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createPushNotificaitons(@PathVariable String autho,
			@RequestBody JPushNotifications jpushNotificaitons) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			boolean status = iNotificationService.savePushNotificaitons(jpushNotificaitons);
			if (status) {
				response.put("Status", 1);
				response.put("Msg", "Notifications are created");
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Error in creating the notifications.");
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception ex) {
			log.error(ex.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in creating the pushnotifications.");
			return response;
		}
	}

	@RequestMapping(value = "v3.0/updatenotification/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updatePushNotificaitons(@PathVariable String autho,
			@RequestBody PushNotifications pushNotificaitons) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			PushNotifications notifications = iNotificationService
					.getNotification(Long.toString(pushNotificaitons.getId()));

			if (notifications != null) {
				Set<User> users = notifications.getUsers();

				pushNotificaitons.setUsers(users);
				;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No notification found to update.");
				return response;
			}

			NotificationType type = iNotificationService
					.getNotificationType(Long.parseLong(pushNotificaitons.getNotificationTypeID()));

			pushNotificaitons.setNotificationtype(type);

			boolean status = iNotificationService.updatePushNotifications(pushNotificaitons);
			if (status) {
				response.put("Status", 1);
				response.put("Msg", "Notifications are updated");
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Error in updating the notifications.");
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception ex) {
			log.error(ex.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in updating the pushnotifications.");
			return response;
		}
	}

	@RequestMapping(value = "v3.0/deletenotification/{autho}/{notificationid}", method = RequestMethod.DELETE, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deletePushNotificaitons(@PathVariable String autho, @PathVariable long notificationid) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			boolean status = iNotificationService.deleteNotification(notificationid);
			if (status) {
				response.put("Status", 1);
				response.put("Msg", "Notifications are deleted");
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Error in deleting the notifications.");
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception ex) {
			log.error(ex.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in deleting the pushnotifications.");
			return response;
		}
	}

	@RequestMapping(value = "v3.0/deleteusernotification/{autho}/{notificationId}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deleteUserNotificaitons(@PathVariable String autho, @PathVariable String notificationId) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			boolean status = iNotificationService.deleteUserNotification(Long.toString(user.getId()), notificationId);
			if (status) {
				response.put("Status", 1);
				response.put("Msg", "Notification is deleted successfully.");
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Error in deleting the user notifications.");
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "Invalid authentication key");
			return response;
		} catch (Exception ex) {
			log.error(ex.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in deleting the user pushnotifications.");
			return response;
		}
	}

	@RequestMapping(value = "v3.0/getnotifications/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getNotificaiton(@PathVariable String autho, @RequestParam("id") String id) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			PushNotifications notifications = iNotificationService.getNotification(id);
			if (notifications != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("PushNotification", notifications);
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Error in getting the notifications.");
				return response;
			}

		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception ex) {
			log.error(ex.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in getting the notifications.");
			return response;
		}
	}

	/*
	 * 
	 * Status would be either all or active or expired
	 */
	@RequestMapping(value = "v3.0/usernotifications/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getNotificaitonByUserID(@PathVariable String autho, @RequestParam("userid") String userId,
			@RequestParam("status") String status) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			List<SendNotifications> sendNotificationList = new ArrayList<SendNotifications>();

			List<PushNotifications> notifications = new ArrayList<PushNotifications>();

			notifications = iNotificationService.userNotifications(userId, status);
			if (notifications != null) {

				DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

				Calendar c = Calendar.getInstance();

				c.add(Calendar.MONTH, -1);

				Date lastMonth = _helper.formatDate((df.format(c.getTime())));

				for (PushNotifications _notification : notifications) {

					SendNotifications sendNotification = new SendNotifications();

					String createOn = null;

					PushNotificationStatus pushNotificationStatusPrev = iNotificationService
							.getUserNotificationStatus(userId, Long.toString(_notification.getId()));

					if (pushNotificationStatusPrev != null) {

						Date sendDate = _helper.formatDate(pushNotificationStatusPrev.getSendDate());

						if (sendDate.before(lastMonth)) {
							continue;
						}

						createOn = prettyTime.format(_helper.getDate(pushNotificationStatusPrev.getSendDate()));

						sendNotification.setSendDate(pushNotificationStatusPrev.getSendDate());

					} else {
						createOn = prettyTime.format(_helper.getDate(_notification.getCreatedOn()));

						sendNotification.setSendDate(_notification.getCreatedOn());
					}

					sendNotification.setNotificationType(Long.toString(_notification.getNotificationtype().getId()));

					sendNotification.setNotificationId(_notification.getId());

					sendNotification.setTitle(_notification.getTitle());

					sendNotification.setShortDescription(_notification.getShortDescription());

					sendNotification.setMessage(_notification.getMessage());

					sendNotification.setImageUrl(_notification.getImageUrl());

					sendNotification.setBannerImageUrl(_notification.getBannerImageUrl());

					sendNotification.setExpiryOn(_notification.getExpiryOn());

					// sendNotification.setSendDate(sendDate);

					sendNotification.setCreatedOn(createOn);

					sendNotification.setHyperLink(_notification.getHyperLink());

					sendNotificationList.add(sendNotification);

				}

				/* To reverse the collection of latest reported gateway */
				Collections.sort(sendNotificationList, new Comparator<SendNotifications>() {
					@Override
					public int compare(SendNotifications a, SendNotifications b) {
						DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						try {
							return format.parse(b.getSendDate()).compareTo(format.parse(a.getSendDate()));
						} catch (ParseException e) {
						}
						return 0;
					}
				});

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("userPushNotification", sendNotificationList);
				userService.updateUserNotification(userId, "false");
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No notifications found for user.");
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception ex) {
			log.error(ex.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in getting the notifications.");
			return response;
		}
	}

	/*
	 * 
	 * Status would be either all or active or expired
	 */
	@RequestMapping(value = "v3.0/listnotifications/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse listNotifications(@PathVariable String autho, @RequestParam("status") String status) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			List<PushNotifications> pushNotifications = iNotificationService.getNotifications(status);

			if (pushNotifications != null) {
				if (pushNotifications.size() > 0) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("pushNotification", pushNotifications);
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "No notifications found.");
					return response;
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "No notifications found.");
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception ex) {
			log.error(ex.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in getting the pushnotifications.");
			return response;
		}
	}

	@RequestMapping(value = "v3.0/sendnotifications/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendNotifications(@PathVariable String autho, @RequestBody JSendNotification jSendNotification) {

		JResponse response = new JResponse();

		try {
			User user = userService.verifyAuthKey(autho);

			/*
			 * Notification Type will be as follows,
			 * 
			 * Type 1 - Only Text Message
			 * 
			 * It has only short description and message body. It does not
			 * contain the image url
			 * 
			 * Type 2 :
			 * 
			 * It has short description and message body and it has image url
			 * 
			 * Type 3. It has only short description and bannerurl
			 * 
			 */
			
			PushNotifications notifications = iNotificationService
					.getNotification(Integer.toString(jSendNotification.getPushNotificationId()));

			if (notifications != null) {

				JSendNotifications sendNotification = new JSendNotifications();

				sendNotification.setSource("pushNotifications");

				sendNotification.setTitle(notifications.getTitle());

				sendNotification.setShortDescription(notifications.getShortDescription());

				// sendNotification.setMessage(notifications.getMessage());

				if (notifications.getNotificationtype().getId() == 2) {
					sendNotification.setImageUrl(notifications.getBannerImageUrl());
				} else {
					sendNotification.setImageUrl("NA");
				}

				Set<User> users = new HashSet<User>();

				for (int i = 0; i < jSendNotification.getUserID().length; i++) {
					
					UserMetaData userMetaData = userServiceV4.getUserMetaData( jSendNotification.getUserID()[i]);
					if( userMetaData != null && !userMetaData.isShow_marketing_notif()) {
						log.info("user disabled marletting notification");
						continue;
					}
					else {
					
					List<User> nUserList = new ArrayList<User>();

					nUserList = userService.getUser(Long.toString(jSendNotification.getUserID()[i]), 3);

					if (nUserList.size() == 0) {
						continue;
					}

					users.add(nUserList.get(0));

					List<UserToken> userToken = new ArrayList<UserToken>();

					userToken = userService.getUserToken(Long.toString(jSendNotification.getUserID()[i]));

					boolean notificationSent = false;

					if (null != userToken && userToken.size() > 0) {
						
						ArrayList<String> tokenList = new ArrayList<String>();

						String title = notifications.getTitle();
						String body = notifications.getShortDescription();
						String redirectUrl = "NA";
						String imageUrl = sendNotification.getImageUrl();
						String source = "pushNotifications";
						String shortDescription = notifications.getShortDescription();
						int monitor_id = (int) notifications.getMonitortype();
						if (notifications.getNotificationtype().getId() == 2) {
							imageUrl = notifications.getBannerImageUrl();
						}
						
						for (UserToken token : userToken) {
							tokenList.add(token.getToken());
						}
						JFcmNotification fcmNotification = new JFcmNotification(title, body, redirectUrl, imageUrl, tokenList,
								source, shortDescription, monitor_id);
						int message = iFirebaseService.sendNotification(fcmNotification);
						log.info(message + " message(s) were sent : userid:"+jSendNotification.getUserID()[i]);
						
						if(message>0)
							notificationSent = true;	
					} else {
						notificationSent = false;
					}
					PushNotificationStatus notificationStatus = new PushNotificationStatus();

					notificationStatus.setPushNotificationId(Long.toString(notifications.getId()));
					notificationStatus.setUserId(Long.toString(jSendNotification.getUserID()[i]));
					notificationStatus.setSendDate(_helper.getCurrentTimeinUTC());
					notificationStatus.setShort_description(notifications.getShortDescription());

					if (notificationSent) {
						notificationStatus.setStatus("Succeed");

					} else {
						notificationStatus.setStatus("Failed");
					}
					iNotificationService.updatePushNotificationStatus(notificationStatus);
				}
				//				notifications.setUsers(users);
				boolean status = iNotificationService.updatePushNotificationsForUsers(notifications,users);
				if(status) {
					log.info("userpushnotifications table updated Success!");
				}else {
					log.info("userpushnotifications table updated Failed!");
				}

				for (User _user : users) {
					boolean result = userService.updateUserNotification(Long.toString(_user.getId()), "true");
					log.info("updateUserNotification updated : "+result);
				}

				if (status) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error in sending the notifications.");
					return response;
				}
				}
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Notifications not found for given notification id.");
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception ex) {
			log.error(ex.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in getting the pushnotifications.");
			return response;
		}

	}

	// Send pushnotification by email 1st approach (dao impl : getUsers().addAll(users) and merge)
	@RequestMapping(value = "v3.0/sendnotificationsbyemail/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendNotificationsUsingEmail(@PathVariable String autho,
			@RequestBody JSendNotification jSendNotification) {
		long startTime = System.nanoTime();		
		String validateTokenUrl = _helper.getExternalConfigValue("validatetokenurl", externalConfigService);
		String fcmsendUrl = _helper.getExternalConfigValue("fcmsendurl", externalConfigService);
		String authKey = _helper.getExternalConfigValue("fcmauthorization", externalConfigService);
		JResponse response = new JResponse();
		System.out.println(jSendNotification.getEmaiID()[0]);
		try {
			User user = userService.verifyAuthKey(autho);
			PushNotifications notifications = iNotificationService
					.getNotification(Integer.toString(jSendNotification.getPushNotificationId()));
			if (notifications != null) {
				JSendNotifications sendNotification = new JSendNotifications();
				sendNotification.setSource("pushNotifications");
				sendNotification.setTitle(notifications.getTitle());
				sendNotification.setShortDescription(notifications.getShortDescription());
				if (notifications.getNotificationtype().getId() == 2) {
					sendNotification.setImageUrl(notifications.getBannerImageUrl());
				} else {
					sendNotification.setImageUrl("NA");
				}

				Set<User> users = new HashSet<User>();
				ArrayList<String> invalidEmails = new ArrayList<String>();

				for (int i = 0; i < jSendNotification.getEmaiID().length; i++) {
					User newUser = null;
					try {
						newUser = userService.getUserByEmail(jSendNotification.getEmaiID()[i]);
						if (newUser.giveCompany().getCompanytype().getId() != 3) {
							log.info(newUser.getEmail()+" : User's company id is not 3 (rv petsafety)");
							continue;
						}
					} catch (Exception e) {
						log.error("User not found for the email : " + jSendNotification.getEmaiID()[i]);
						invalidEmails.add(jSendNotification.getEmaiID()[i]);
						continue;
					}
					users.add(newUser);
					long userId = newUser.getId();
					List<UserToken> userToken = new ArrayList<UserToken>();
					userToken = userService.getUserToken(userId+"");
					boolean notificationSent = false;
					if (null != userToken && userToken.size() > 0) {
						ArrayList<String> tokenList = new ArrayList<String>();

						String title = notifications.getTitle();
						String body = notifications.getShortDescription();
						String redirectUrl = "NA";
						String imageUrl = sendNotification.getImageUrl();
						String source = "pushNotifications";
						String shortDescription = notifications.getShortDescription();
						int monitor_id = (int) notifications.getMonitortype();
						if (notifications.getNotificationtype().getId() == 2) {
							imageUrl = notifications.getBannerImageUrl();
						}
						
						for (UserToken token : userToken) {
							tokenList.add(token.getToken());
						}
						JFcmNotification fcmNotification = new JFcmNotification(title, body, redirectUrl, imageUrl, tokenList,
								source, shortDescription, monitor_id);
						int message = iFirebaseService.sendNotification(fcmNotification);
						log.info(message + " message(s) were sent : userid:"+userId);
						
						if(message>0)
							notificationSent = true;
					} else {
						notificationSent = false;
					}
					PushNotificationStatus notificationStatus = new PushNotificationStatus();

					notificationStatus.setPushNotificationId(Long.toString(notifications.getId()));
					notificationStatus.setUserId(userId + "");
					notificationStatus.setSendDate(_helper.getCurrentTimeinUTC());
					notificationStatus.setShort_description(notifications.getShortDescription());

					if (notificationSent) {
						notificationStatus.setStatus("Succeed");

					} else {
						notificationStatus.setStatus("Failed");
					}
					iNotificationService.updatePushNotificationStatus(notificationStatus);
				}
				boolean status = iNotificationService.updatePushNotificationsForUsers(notifications,users);

				for (User _user : users) {
					boolean result = userService.updateUserNotification(Long.toString(_user.getId()), "true");
					log.info("update UserNotification : "+result);
				}
				if (status) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("InvalidEmails", invalidEmails);
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error in sending the notifications.");
					return response;
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "Notifications not found for given notification id.");
				return response;
			}

		}  catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}catch (Exception ex) {
			log.error(ex.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in getting the pushnotifications.");
			return response;
		}

	}

	//Send push notification by email 2nd approach (dao impl : Insert ignore query for mapping table)
	@RequestMapping(value = "v4.0/sendnotificationsbyemail/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendNotificationsUsingEmail_V2(@PathVariable String autho,
			@RequestBody JSendNotification jSendNotification) {
		long startTime = System.nanoTime();		
		String validateTokenUrl = _helper.getExternalConfigValue("validatetokenurl", externalConfigService);
		String fcmsendUrl = _helper.getExternalConfigValue("fcmsendurl", externalConfigService);
		String authKey = _helper.getExternalConfigValue("fcmauthorization", externalConfigService);
		JResponse response = new JResponse();
		System.out.println(jSendNotification.getEmaiID().length);
		try {
			
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			PushNotifications notifications = iNotificationService.
					getPushNotificationById(Integer.toString(jSendNotification.getPushNotificationId()));
			if (notifications != null) {
				JSendNotifications sendNotification = new JSendNotifications();
				sendNotification.setSource("pushNotifications");
				sendNotification.setTitle(notifications.getTitle());
				sendNotification.setShortDescription(notifications.getShortDescription());
				if (notifications.getNotificationtype().getId() == 2) {
					sendNotification.setImageUrl(notifications.getBannerImageUrl());
				} else {
					sendNotification.setImageUrl("NA");
				}
				String createOn = prettyTime.format(_helper.getDate(notifications.getCreatedOn()));
				CloudNotifications cloudNotifications = new CloudNotifications();
				cloudNotifications.setTitle(notifications.getTitle());
				cloudNotifications.setBody(notifications.getShortDescription());
				cloudNotifications.setSound("default");
				ArrayList<String> invalidEmails = new ArrayList<String>();

				Set<Long> users = new HashSet<Long>();
				System.out.println("before");
				for (int i = 0; i < jSendNotification.getEmaiID().length; i++) {
					long newUserId = 0;
					try {						
						newUserId = userService.getUserIdByEmail(jSendNotification.getEmaiID()[i]);
						if(newUserId == 0) {
							invalidEmails.add(jSendNotification.getEmaiID()[i]);
							continue;
						}
					} catch (Exception e) {
						log.error("Error while getting user by email : " + e.getLocalizedMessage());
						invalidEmails.add(jSendNotification.getEmaiID()[i]);
						continue;
					}
					users.add(newUserId);
					List<UserToken> userToken = new ArrayList<UserToken>();
					userToken = userService.getUserToken(newUserId+ "");
					boolean notificationSent = false;
					if (null != userToken && userToken.size() > 0) {
						for (UserToken token : userToken) {
							try {
								String validationResponse = null;
								validationResponse = _helper.validateToken(validateTokenUrl + token.getToken(),
										authKey);
								JSONObject _res = new JSONObject(validationResponse);
								if (_res.has("error")) {
									continue;
								} else {
									Gson gson = new Gson();
									StringEntity postingString = null;
									String jsonString = null;
									String platForm;
									try {
										platForm = _res.getString("platform");
									} catch (Exception ex) {
										log.error("No Platform object found in token validation");
										continue;
									}
									if (platForm.equalsIgnoreCase("ANDROID")) {
										FCMNofitication fcmNotification = new FCMNofitication(token.getToken(), "high",
												sendNotification, true, true);
										jsonString = gson.toJson(fcmNotification);
									} else if (platForm.equalsIgnoreCase("IOS")) {
										FCMNofitication fcmNotification = new FCMNofitication(token.getToken(), "high",
												sendNotification, cloudNotifications, true, true);
										jsonString = gson.toJson(fcmNotification);
									} else {
										FCMNofitication fcmNotification = new FCMNofitication(token.getToken(), "high",
												sendNotification, cloudNotifications, true, true);
										jsonString = gson.toJson(fcmNotification);
									}
									System.out.println(jsonString);
									postingString = new StringEntity(jsonString);
									String postResponse = _helper.postFCMRequest(fcmsendUrl, "application/json",
											authKey, postingString);
									JSONObject sendFCMResponse = new JSONObject(postResponse);
									int success = sendFCMResponse.getInt("success");
									if (success == 1) {
										notificationSent = true;
									}
								}
							} catch (Exception ex) {
								log.error("Issue  found in token validation");
								continue;
							}
						}
					} else {
						notificationSent = false;
					}
					PushNotificationStatus notificationStatus = new PushNotificationStatus();

					notificationStatus.setPushNotificationId(Long.toString(notifications.getId()));
					notificationStatus.setUserId(newUserId + "");
					notificationStatus.setSendDate(_helper.getCurrentTimeinUTC());

					if (notificationSent) {
						notificationStatus.setStatus("Succeed");

					} else {
						notificationStatus.setStatus("Failed");
					}
					iNotificationService.updatePushNotificationStatus(notificationStatus);

				}

				boolean status = iNotificationService.updateUserPushNotifications(notifications, users);

				long timeElapsed = System.nanoTime() - startTime;
				System.out.println("Execution time in milliseconds : " + timeElapsed / 1000000);
				if (status) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("InvalidEmails", invalidEmails);
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error in sending the notifications.");
					return response;
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "Notifications not found for given notification id.");
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}  catch (Exception ex) {
			log.error(ex.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in getting the pushnotifications.");
			return response;
		}

	}
	
	//Send push notification by user id 2nd approach (dao impl : Insert ignore query for mapping table)
	@RequestMapping(value = "v4.0/sendnotificationsbyuserid/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendNotificationsUsingUserId_V2(@PathVariable String autho,
			@RequestBody JSendNotification jSendNotification) {
		long startTime = System.nanoTime();		
		String validateTokenUrl = _helper.getExternalConfigValue("validatetokenurl", externalConfigService);
		String fcmsendUrl = _helper.getExternalConfigValue("fcmsendurl", externalConfigService);
		String authKey = _helper.getExternalConfigValue("fcmauthorization", externalConfigService);
		JResponse response = new JResponse();
		System.out.println(jSendNotification.getUserID().length);
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
			
			PushNotifications notifications = iNotificationService
					.getPushNotificationById(Integer.toString(jSendNotification.getPushNotificationId()));
			if (notifications != null) {
				JSendNotifications sendNotification = new JSendNotifications();
				sendNotification.setSource("pushNotifications");
				sendNotification.setTitle(notifications.getTitle());
				sendNotification.setShortDescription(notifications.getShortDescription());
				if (notifications.getNotificationtype().getId() == 2) {
					sendNotification.setImageUrl(notifications.getBannerImageUrl());
				} else {
					sendNotification.setImageUrl("NA");
				}
				String createOn = prettyTime.format(_helper.getDate(notifications.getCreatedOn()));
				CloudNotifications cloudNotifications = new CloudNotifications();
				cloudNotifications.setTitle(notifications.getTitle());
				cloudNotifications.setBody(notifications.getShortDescription());
				cloudNotifications.setSound("default");
				ArrayList<String> invalidUserIds = new ArrayList<String>();

				Set<Long> users = new HashSet<Long>();
				System.out.println("before");
				for (int i = 0; i < jSendNotification.getUserID().length; i++) {
					long newUserId = jSendNotification.getUserID()[i];
					UserV4 thisUser = null;
					try {						
						thisUser = userServiceV4.verifyAuthV4("id", newUserId+"");
						if(thisUser.getEmail().equalsIgnoreCase("NA")) {
							invalidUserIds.add(newUserId+"");
							continue;
						}
					} catch (InvalidAuthoException e) {
						log.error("Error while getting user by email : " + e.getLocalizedMessage());
						invalidUserIds.add(newUserId+"");
						continue;
					}
					users.add(newUserId);
					List<UserToken> userToken = new ArrayList<UserToken>();
					userToken = userService.getUserToken(newUserId+ "");
					boolean notificationSent = false;
					if (null != userToken && userToken.size() > 0) {
						for (UserToken token : userToken) {
							try {
								String validationResponse = null;
								validationResponse = _helper.validateToken(validateTokenUrl + token.getToken(),
										authKey);
								JSONObject _res = new JSONObject(validationResponse);
								if (_res.has("error")) {
									continue;
								} else {
									Gson gson = new Gson();
									StringEntity postingString = null;
									String jsonString = null;
									String platForm;
									try {
										platForm = _res.getString("platform");
									} catch (Exception ex) {
										log.error("No Platform object found in token validation");
										continue;
									}
									if (platForm.equalsIgnoreCase("ANDROID")) {
										FCMNofitication fcmNotification = new FCMNofitication(token.getToken(), "high",
												sendNotification, true, true);
										jsonString = gson.toJson(fcmNotification);
									} else if (platForm.equalsIgnoreCase("IOS")) {
										FCMNofitication fcmNotification = new FCMNofitication(token.getToken(), "high",
												sendNotification, cloudNotifications, true, true);
										jsonString = gson.toJson(fcmNotification);
									} else {
										FCMNofitication fcmNotification = new FCMNofitication(token.getToken(), "high",
												sendNotification, cloudNotifications, true, true);
										jsonString = gson.toJson(fcmNotification);
									}
									System.out.println(jsonString);
									postingString = new StringEntity(jsonString);
									String postResponse = _helper.postFCMRequest(fcmsendUrl, "application/json",
											authKey, postingString);
									JSONObject sendFCMResponse = new JSONObject(postResponse);
									int success = sendFCMResponse.getInt("success");
									if (success == 1) {
										notificationSent = true;
									}
								}
							} catch (Exception ex) {
								log.error("Issue  found in token validation");
								continue;
							}
						}
					} else {
						notificationSent = false;
					}
					PushNotificationStatus notificationStatus = new PushNotificationStatus();

					notificationStatus.setPushNotificationId(Long.toString(notifications.getId()));
					notificationStatus.setUserId(newUserId + "");
					notificationStatus.setSendDate(_helper.getCurrentTimeinUTC());
					notificationStatus.setShort_description(notifications.getShortDescription());

					if (notificationSent) {
						notificationStatus.setStatus("Succeed");

					} else {
						notificationStatus.setStatus("Failed");
					}
					iNotificationService.updatePushNotificationStatus(notificationStatus);

				}

				boolean status = iNotificationService.updateUserPushNotifications(notifications, users);

				long timeElapsed = System.nanoTime() - startTime;
				System.out.println("Execution time in milliseconds : " + timeElapsed / 1000000);
				if (status) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("InvalidUserIds", invalidUserIds);
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error in sending the notifications.");
					return response;
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "Notifications not found for given notification id.");
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}  catch (Exception ex) {
			log.error(ex.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in getting the pushnotifications.");
			return response;
		}

	}


	//	####################################################################################################
	//Send push notification by email to Microservice (packet formate)  (dao impl : insert ignore)
	@RequestMapping(value = "v5.0/sendnotificationsbyemail/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendNotificationsUsingEmail_Microservice(@PathVariable String autho,
			@RequestBody JSendNotification jSendNotification) {
		JResponse response = new JResponse();
		log.info("Email Count : "+jSendNotification.getEmaiID().length);

		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
		}catch(InvalidAuthoException e) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			log.error("Invalid Authkey");
			return response;
		}

		String statusMonitorPersonsEmail[] = jSendNotification.getStatusmonitorpersonsemail();
		String statusMonitorPersonsEmail_ = "NA";
		if(statusMonitorPersonsEmail.length > 0) {
			statusMonitorPersonsEmail_="";
			for(int i=0; i< statusMonitorPersonsEmail.length;i++) {
				statusMonitorPersonsEmail_ += statusMonitorPersonsEmail[i] +",";
			}
		}	
		if(!statusMonitorPersonsEmail_.equals("NA"))
			statusMonitorPersonsEmail_ = statusMonitorPersonsEmail_.substring(0, statusMonitorPersonsEmail_.length() - 1);

		String basepacket = "nimble|petmonitor|pushnotifications|email|"+statusMonitorPersonsEmail_+"|";
		String finalPacket = "";
		try {

			PushNotifications notifications = iNotificationService
					.getPushNotificationById(Integer.toString(jSendNotification.getPushNotificationId()));

			AWS_SQS_ACCESS_KEY = secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_access_key");
			AWS_SQS_SECRET_KEY = secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_secret_key");
			AWSCredentials credentials = new BasicAWSCredentials(AWS_SQS_ACCESS_KEY,AWS_SQS_SECRET_KEY);
			AWSStaticCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(credentials);
			AmazonSQS sqs = AmazonSQSClientBuilder.standard()
					.withCredentials(credentialsProvider)
					.withRegion(Regions.US_WEST_2)
					.build();

			if (notifications != null) {

				String notificationsId = notifications.getId()+"";
				String notificationsTitle = notifications.getTitle();
				String notificationShortDescription = notifications.getShortDescription();
				String notificationsImageUrl = "NA";
				if (notifications.getNotificationtype().getId() == 2) {
					notificationsImageUrl = notifications.getBannerImageUrl();
				}
				basepacket += notificationsId+"|"+notificationsTitle+"|"+notificationShortDescription+"|"+notificationsImageUrl+"|";
				String allUserIds = "";				

				ArrayList<String> invalidEmails = new ArrayList<String>();
				Set<Long> users = new HashSet<Long>();

				int emailCount = 0;
				for (int i = 0; i < jSendNotification.getEmaiID().length; i++) {
					long newUserId = 0;
					try {						
						newUserId = userService.getUserIdByEmail(jSendNotification.getEmaiID()[i]);
						if(newUserId == 0) {
							invalidEmails.add(jSendNotification.getEmaiID()[i]);
							continue;
						}
						emailCount++ ;
					} catch (Exception e) {
						log.error("Error while getting user by email : " + e.getLocalizedMessage());
						continue;
					}

					users.add(newUserId);
					allUserIds += newUserId +",";
					if(emailCount == 1000) {
						allUserIds = allUserIds.substring(0, allUserIds.length()-1);						
						finalPacket = basepacket+allUserIds+"#";
						log.info("Final Packet of 1000 User : "+finalPacket);
						emailCount = 0;
						SendMessageRequest sendMessageRequest = new SendMessageRequest(amazonSqs_microserviceUrl,finalPacket);
						SendMessageResult sendMessageResult = sqs.sendMessage(sendMessageRequest);
						String sequenceNumber = sendMessageResult.getSequenceNumber();
						String messageId = sendMessageResult.getMessageId();
						log.info("SendMessage succeed with messageId " + messageId + ",sequence number " + sequenceNumber);
						finalPacket = "";

					}
				}

				if(emailCount > 0 ) {
					allUserIds = allUserIds.substring(0, allUserIds.length()-1);
					finalPacket = basepacket+allUserIds+"#";
					log.info("Final Packet of "+emailCount+" users : "+finalPacket);
					SendMessageRequest sendMessageRequest = new SendMessageRequest(amazonSqs_microserviceUrl,finalPacket);
					SendMessageResult sendMessageResult = sqs.sendMessage(sendMessageRequest);
					String sequenceNumber = sendMessageResult.getSequenceNumber();
					String messageId = sendMessageResult.getMessageId();
					log.info("SendMessage succeed with messageId " + messageId + ",sequence number " + sequenceNumber);

				}

				//				boolean status = iNotificationService.updateUserPushNotifications(notifications, users);
				//
				//				//check with viswa : is only for valid user emails 
				//				for (Long _userId : users) {
				//					boolean result = userService.updateUserNotification(Long.toString(_userId), "true");
				//					log.info("updateUserNotification updated : "+result);
				//				}
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("InvalidEmails", invalidEmails);
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Notifications not found for given notification id.");
				log.error("Notifications not found for given notification id.");
				return response;
			}
		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Error in getting the pushnotifications.");
			log.error("Error in getting the pushnotifications.");
			return response;
		}

	}

	//Send push notification by user id to Microservice (packet formate) (dao impl : insert ignore)
	@RequestMapping(value = "v5.0/sendnotificationsbyuserid/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendNotificationsUsingUserId_Microservice(@PathVariable String autho,
			@RequestBody JSendNotification jSendNotification) {
		JResponse response = new JResponse();
		log.info("User id Count : "+jSendNotification.getUserID().length);

		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
		}catch(InvalidAuthoException e) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey");
			log.error("Invalid Authkey");
			return response;
		}

		String statusMonitorPersonsEmail[] = jSendNotification.getStatusmonitorpersonsemail();
		String statusMonitorPersonsEmail_ = "NA";
		if(statusMonitorPersonsEmail.length > 0) {
			statusMonitorPersonsEmail_="";
			for(int i=0; i< statusMonitorPersonsEmail.length;i++) {
				statusMonitorPersonsEmail_ += statusMonitorPersonsEmail[i] +",";
			}
		}		
		if(!statusMonitorPersonsEmail_.equals("NA"))
			statusMonitorPersonsEmail_ = statusMonitorPersonsEmail_.substring(0, statusMonitorPersonsEmail_.length() - 1);

		String basepacket = "nimble|petmonitor|pushnotifications|userid|"+statusMonitorPersonsEmail_+"|";
		String finalPacket = "";
		try {

			PushNotifications notifications = iNotificationService
					.getPushNotificationById(Integer.toString(jSendNotification.getPushNotificationId()));

			AWS_SQS_ACCESS_KEY = secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_access_key");
			AWS_SQS_SECRET_KEY = secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_secret_key");
			AWSCredentials credentials = new BasicAWSCredentials(AWS_SQS_ACCESS_KEY,AWS_SQS_SECRET_KEY);
			AWSStaticCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(credentials);
			AmazonSQS sqs = AmazonSQSClientBuilder.standard()
					.withCredentials(credentialsProvider)
					.withRegion(Regions.US_WEST_2)
					.build();

			if (notifications != null) {

				String notificationsId = notifications.getId()+"";
				String notificationsTitle = notifications.getTitle();
				String notificationShortDescription = notifications.getShortDescription();
				String notificationsImageUrl = "NA";
				if (notifications.getNotificationtype().getId() == 2) {
					notificationsImageUrl = notifications.getBannerImageUrl();
				}
				basepacket += notificationsId+"|"+notificationsTitle+"|"+notificationShortDescription+"|"+notificationsImageUrl+"|";
				String allUserIds = "";				

				ArrayList<String> invalidUserIds = new ArrayList<String>();
				Set<Long> users = new HashSet<Long>();

				int emailCount = 0;
				for (int i = 0; i < jSendNotification.getUserID().length; i++) {
					long newUserId = jSendNotification.getUserID()[i];
					List<User> _user = new ArrayList<User>();
					try {								
						_user = userService.getUser(Long.toString(newUserId), 3);

						if (_user.size() == 0) {
							invalidUserIds.add(jSendNotification.getUserID()[i]+"");
							continue;
						}
						emailCount++ ;
					} catch (Exception e) {
						log.error("Error while getting user by user id : " + e.getLocalizedMessage());
						continue;
					}

					users.add(newUserId);
					allUserIds += newUserId +",";
					if(emailCount == 1000) {
						allUserIds = allUserIds.substring(0, allUserIds.length()-1);						
						finalPacket = basepacket+allUserIds+"#";
						log.info("Final Packet of 1000 User : "+finalPacket);
						emailCount = 0;
						SendMessageRequest sendMessageRequest = new SendMessageRequest(amazonSqs_microserviceUrl,finalPacket);
						SendMessageResult sendMessageResult = sqs.sendMessage(sendMessageRequest);
						String sequenceNumber = sendMessageResult.getSequenceNumber();
						String messageId = sendMessageResult.getMessageId();
						log.info("SendMessage succeed with messageId " + messageId + ",sequence number " + sequenceNumber);
						finalPacket = "";

					}
				}

				if(emailCount > 0 ) {
					allUserIds = allUserIds.substring(0, allUserIds.length()-1);
					finalPacket = basepacket+allUserIds+"#";
					log.info("Final Packet of "+emailCount+" users : "+finalPacket);
					SendMessageRequest sendMessageRequest = new SendMessageRequest(amazonSqs_microserviceUrl,finalPacket);
					SendMessageResult sendMessageResult = sqs.sendMessage(sendMessageRequest);
					String sequenceNumber = sendMessageResult.getSequenceNumber();
					String messageId = sendMessageResult.getMessageId();
					log.info("SendMessage succeed with messageId " + messageId + ",sequence number " + sequenceNumber);

				}

				//				boolean status = iNotificationService.updateUserPushNotifications(notifications, users);
				//
				//				//check with viswa : is only for valid user emails 
				//				for (Long _userId : users) {
				//					boolean result = userService.updateUserNotification(Long.toString(_userId), "true");
				//					log.info("updateUserNotification updated : "+result);
				//				}

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("InvalidUserIds", invalidUserIds);
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Notifications not found for given notification id.");
				log.error("Notifications not found for given notification id.");
				return response;
			}
		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "Error in getting the pushnotifications.");
			log.error("Error in getting the pushnotifications.");
			return response;
		}

	}

	
	@RequestMapping(value = "v5.0/sendNotificationswithDynamicContent/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendNotificationswithDynamicContent(@PathVariable String autho, @RequestBody JSendNotification jSendNotification,
			@RequestParam("feededPer") String feededPer) {

		JResponse response = new JResponse();

		try {
			User user = userService.verifyAuthKey(autho);
			
			PushNotifications notifications = iNotificationService
					.getNotification(Integer.toString(jSendNotification.getPushNotificationId()));

			if (notifications != null) {

				JSendNotifications sendNotification = new JSendNotifications();

				sendNotification.setSource("pushNotifications");

				sendNotification.setTitle(notifications.getTitle());
				
				sendNotification.setShortDescription(
					    notifications.getShortDescription()
					        .replace("$mealval$", String.valueOf(feededPer)).replace("+", " ")
					);
				
				if (notifications.getNotificationtype().getId() == 2) {
					sendNotification.setImageUrl(notifications.getBannerImageUrl());
				} else {
					sendNotification.setImageUrl("NA");
				}

				Set<User> users = new HashSet<User>();

				for (int i = 0; i < jSendNotification.getUserID().length; i++) {
					
					UserMetaData userMetaData = userServiceV4.getUserMetaData( jSendNotification.getUserID()[i]);
					if( userMetaData != null && !userMetaData.isShow_marketing_notif()) {
						log.info("user disabled marletting notification");
						continue;
					}
					else {
					
					List<User> nUserList = new ArrayList<User>();

					nUserList = userService.getUser(Long.toString(jSendNotification.getUserID()[i]), 3);

					if (nUserList.size() == 0) {
						continue;
					}

					users.add(nUserList.get(0));

					List<UserToken> userToken = new ArrayList<UserToken>();

					userToken = userService.getUserToken(Long.toString(jSendNotification.getUserID()[i]));

					boolean notificationSent = false;

					if (null != userToken && userToken.size() > 0) {
						
						ArrayList<String> tokenList = new ArrayList<String>();

						String title = notifications.getTitle();
						String body = notifications.getShortDescription().replace("$mealval$", String.valueOf(feededPer)).replace("+", " ");
						String redirectUrl = "NA";
						String imageUrl = sendNotification.getImageUrl();
						String source = "pushNotifications";
						String shortDescription = notifications.getShortDescription().replace("$mealval$", String.valueOf(feededPer)).replace("+", " ");
						int monitor_id = (int) notifications.getMonitortype();
						if (notifications.getNotificationtype().getId() == 2) {
							imageUrl = notifications.getBannerImageUrl();
						}
						
						for (UserToken token : userToken) {
							tokenList.add(token.getToken());
						}
						JFcmNotification fcmNotification = new JFcmNotification(title, body, redirectUrl, imageUrl, tokenList,
								source, shortDescription, monitor_id);
						int message = iFirebaseService.sendNotification(fcmNotification);
						log.info(message + " message(s) were sent : userid:"+jSendNotification.getUserID()[i]);
						
						if(message>0)
							notificationSent = true;	
					} else {
						notificationSent = false;
					}
					PushNotificationStatus notificationStatus = new PushNotificationStatus();

					notificationStatus.setPushNotificationId(Long.toString(notifications.getId()));
					notificationStatus.setUserId(Long.toString(jSendNotification.getUserID()[i]));
					notificationStatus.setSendDate(_helper.getCurrentTimeinUTC());
					notificationStatus.setShort_description(notifications.getShortDescription().replace("$mealval$", String.valueOf(feededPer)).replace("+", " "));
					notificationStatus.setGateway_id(jSendNotification.getGatewayId()[i]);
					if (notificationSent) {
						notificationStatus.setStatus("Succeed");

					} else {
						notificationStatus.setStatus("Failed");
					}
					iNotificationService.updatePushNotificationStatus(notificationStatus);
				}
				//				notifications.setUsers(users);
				boolean status = iNotificationService.updatePushNotificationsForUsers(notifications,users);
				if(status) {
					log.info("userpushnotifications table updated Success!");
				}else {
					log.info("userpushnotifications table updated Failed!");
				}

				for (User _user : users) {
					boolean result = userService.updateUserNotification(Long.toString(_user.getId()), "true");
					log.info("sendNotificationswithDynamicContent updateUserNotification updated : "+result);
				}

				if (status) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error in sending the notifications.");
					return response;
				}
				}
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Notifications not found for given notification id.");
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception ex) {
			log.error(ex.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in getting the pushnotifications.");
			return response;
		}

	}
	
	@RequestMapping(value = "v3.0/sendNotificationstoplanpage/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse sendNotificationstoplanpage(@PathVariable String autho, @RequestBody JSendNotification jSendNotification) {

		JResponse response = new JResponse();

		try {
			User user = userService.verifyAuthKey(autho);

			/*
			 * Notification Type will be as follows,
			 * 
			 * Type 1 - Only Text Message
			 * 
			 * It has only short description and message body. It does not
			 * contain the image url
			 * 
			 * Type 2 :
			 * 
			 * It has short description and message body and it has image url
			 * 
			 * Type 3. It has only short description and bannerurl
			 * 
			 */
			
			PushNotifications notifications = iNotificationService
					.getNotification(Integer.toString(jSendNotification.getPushNotificationId()));

			if (notifications != null) {

				JSendNotifications sendNotification = new JSendNotifications();

				sendNotification.setSource("pushNotifications");

				sendNotification.setTitle(notifications.getTitle());

				sendNotification.setShortDescription(notifications.getShortDescription());

				// sendNotification.setMessage(notifications.getMessage());

				if (notifications.getNotificationtype().getId() == 2) {
					sendNotification.setImageUrl(notifications.getBannerImageUrl());
				} else {
					sendNotification.setImageUrl("NA");
				}

				Set<User> users = new HashSet<User>();

				for (int i = 0; i < jSendNotification.getUserID().length; i++) {
					
					UserMetaData userMetaData = userServiceV4.getUserMetaData( jSendNotification.getUserID()[i]);
					if( userMetaData != null && !userMetaData.isShow_marketing_notif()) {
						log.info("user disabled marletting notification");
						continue;
					}
					else {
					
					List<User> nUserList = new ArrayList<User>();

					nUserList = userService.getUser(Long.toString(jSendNotification.getUserID()[i]), 3);

					if (nUserList.size() == 0) {
						continue;
					}

					users.add(nUserList.get(0));

					List<UserToken> userToken = new ArrayList<UserToken>();

					userToken = userService.getUserToken(Long.toString(jSendNotification.getUserID()[i]));

					boolean notificationSent = false;

					if (null != userToken && userToken.size() > 0) {
						
						ArrayList<String> tokenList = new ArrayList<String>();

						String title = notifications.getTitle();
						String body = notifications.getShortDescription();
						String redirectUrl = "planpage";
						String imageUrl = sendNotification.getImageUrl();
						String source = "pushNotifications";
						String shortDescription = notifications.getShortDescription();
						int monitor_id = (int) notifications.getMonitortype();
						if (notifications.getNotificationtype().getId() == 2) {
							imageUrl = notifications.getBannerImageUrl();
						}
						
						for (UserToken token : userToken) {
							tokenList.add(token.getToken());
						}
						JFcmNotification fcmNotification = new JFcmNotification(title, body, redirectUrl, imageUrl, tokenList,
								source, shortDescription, monitor_id);
						int message = iFirebaseService.sendNotification(fcmNotification);
						log.info(message + " message(s) were sent : userid:"+jSendNotification.getUserID()[i]);
						
						if(message>0)
							notificationSent = true;	
					} else {
						notificationSent = false;
					}
					PushNotificationStatus notificationStatus = new PushNotificationStatus();

					notificationStatus.setPushNotificationId(Long.toString(notifications.getId()));
					notificationStatus.setUserId(Long.toString(jSendNotification.getUserID()[i]));
					notificationStatus.setSendDate(_helper.getCurrentTimeinUTC());
					notificationStatus.setShort_description(notifications.getShortDescription());
					notificationStatus.setGateway_id(jSendNotification.getGatewayId()[i]);

					if (notificationSent) {
						notificationStatus.setStatus("Succeed");

					} else {
						notificationStatus.setStatus("Failed");
					}
					iNotificationService.updatePushNotificationStatus(notificationStatus);
				}
				//				notifications.setUsers(users);
				boolean status = iNotificationService.updatePushNotificationsForUsers(notifications,users);
				if(status) {
					log.info("userpushnotifications table updated Success!");
				}else {
					log.info("userpushnotifications table updated Failed!");
				}

				for (User _user : users) {
					boolean result = userService.updateUserNotification(Long.toString(_user.getId()), "true");
					log.info("updateUserNotification updated : "+result);
				}

				if (status) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "Error in sending the notifications.");
					return response;
				}
				}
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Notifications not found for given notification id.");
				return response;
			}
		} catch (InvalidAuthoException ex) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception ex) {
			log.error(ex.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in getting the pushnotifications.");
			return response;
		}

	}
}
