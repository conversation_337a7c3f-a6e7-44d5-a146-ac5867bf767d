package com.nimble.irisservices.controller;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.DeviceSubscriptions;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IDeviceSubscriptionService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGeneralConfigService;
import com.nimble.irisservices.service.IUserService;

@Controller
public class DeviceSubscriptionController {

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IGeneralConfigService generalConfigService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

//	@Value("${supportcontactnumber}")
//	private String supportContactNumber;
//
//	@Value("${supportemail}")
//	private String supportContactEmail;

	@Value("${petguideurl}")
	private String petGuideURL;

	@Autowired
	@Lazy
	IDeviceSubscriptionService deviceSubscriptionService;

	private static final Logger log = LogManager.getLogger(DeviceSubscriptionController.class);

	@RequestMapping(value = "v3.0/devicesubscription/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveOrUpdate(@PathVariable String autho,
			@RequestBody List<DeviceSubscriptions> deviceSubscription) {
		JResponse response = new JResponse();
		String responseMessage = "Status :" + "\n";
		try {

			
			User user = userService.verifyAuthKey(autho);

			if (true) {

				boolean isSuccess = false;

				if (deviceSubscription.size() > 0) {

					for (DeviceSubscriptions sub : deviceSubscription) {

						responseMessage = responseMessage + "Niom Sub ID : "+sub.getNiomSubId();
						
						if(sub.getBillingPeriod().toLowerCase().equals("year")) {
							sub.setBillingPeriod("Yearly");
                        }
                        if (sub.getBillingPeriod().toLowerCase().contains("month")) {
                        	sub.setBillingPeriod("Monthly");
                        }
                        if (sub.getBillingPeriod().toLowerCase().contains("half")) {
                        	sub.setBillingPeriod("Half-Yearly");
                        }
                        if (sub.getBillingPeriod().toLowerCase().contains("quarter")) {
                        	sub.setBillingPeriod("Quarterly");
                        }
                        
						try {
							isSuccess = deviceSubscriptionService.saveOrUpdateDeviceSubscription(sub);

							if (isSuccess) {

								responseMessage = responseMessage + " : Success" + "\n";

							} else {
								responseMessage = responseMessage + " : Failed" + "\n";
							}
						} catch (Exception ex) {
							responseMessage = responseMessage + " : Failed" + "|Exception : " + ex.getMessage() + "\n";
						}
					}

				} else {
					responseMessage = "No data to insert";
				}

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("ResponseMessage", responseMessage);
			} 
//			else {
//				response.put("Status", 0);
//				response.put("Msg", "User Does not have permission to import subscriptions");
//				return response;
//			}

		} 
		catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} 
		catch (Exception e) {
			log.error("Excepitoin while importing data .");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while importing data .");
			response.put("ResponseMessage", responseMessage);
			return response;
		}
		return response;
	}

	
}
