package com.nimble.irisservices.controller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JLocationBasedTemperature;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IUserService;

@Controller
public class WeatherController {

	@Autowired
	@Lazy
	IUserService userService;

	@Value("${openWeatherAPI}")
	private String openWeatherAPI;

	@Value("${weathericonUrl}")
	private String Weathericonurl;

	Helper _helper = new Helper();

	private static final Logger log = LogManager.getLogger(WeatherController.class);

	@RequestMapping(value = "v3.0/gettemperature/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse GetTemperature(@PathVariable String autho, @RequestParam("lat") String lat,
			@RequestParam("lon") String lon, @RequestParam("units") String units) {
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);
			String url = openWeatherAPI;

			String originalUnits = units;

			url =url.replaceAll("LATITUDE", lat);
			url=url.replaceAll("LONGTITUDE", lon);

			if(units.equalsIgnoreCase("c")){
				units="Metric";
			}else{
				units="Imperial";
			}
			url = url + units;

			String openWeatherReponse = _helper.getURL(url);

			JSONObject jopenWeatherReponse = new JSONObject();
			JSONObject nresponse = new JSONObject(openWeatherReponse);
			jopenWeatherReponse = nresponse.getJSONObject("main");
			JSONObject weather=(JSONObject) nresponse.getJSONArray("weather").get(0);

			String Description = weather.getString("description");
			String icon = weather.getString("icon");
			String temp =  Double.toString(jopenWeatherReponse.getDouble("temp"));

			JLocationBasedTemperature jLocationBasedTemperature = new JLocationBasedTemperature(originalUnits,temp);

			jLocationBasedTemperature.setDescription(Description);
			jLocationBasedTemperature.setIcon(icon);
			jLocationBasedTemperature.setIconUrl(Weathericonurl+icon+".png");

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("jLocationBasedTemperature",jLocationBasedTemperature);
			return response;

			//			if (jopenWeatherReponse == null) {
			//				response.put("Status", 0);
			//				response.put("Msg", "Error in getting temperature. Try after sometime.");
			//				// response.put("SpecificException", RegisterUserError.ER035);
			//				log.info("Error - GetTemperature. Error in getting temperature. Try after sometime.");
			//
			//				return response;
			//			}
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error("Error - GetTemperature. Error in getting temperature. Try after sometime.");
			response.put("Status", 0);
			response.put("Msg", "Error in getting temperature. Try after sometime.");
			return response;
		}

	}

}
