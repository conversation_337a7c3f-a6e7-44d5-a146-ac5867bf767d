package com.nimble.irisservices.controller;

import java.io.File;
import java.io.FileOutputStream;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IExternalConfigService;

@Controller
public class S3BucketController {
	private static final Logger log = LogManager.getLogger(S3BucketController.class);
	Helper helper = new Helper();
	
	private static String s3_bucket_details = null;
	
	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;
	
	@Value("${profileimages_url}")
	private String s3Url;	
	
	@RequestMapping(value = "v4.0/saveins3bucket", method = RequestMethod.POST/* , consumes = {"multipart/form-data"} */)
	@ResponseBody
	public JResponse saveInS3Bucket(@RequestParam(value = "file", required = true) MultipartFile myFile,
			@RequestParam(value = "bucketname", required = true) String bucketname) {

		JResponse response = new JResponse();
		String bucket_name, folder_name, access_key, secret_key, file_path;

		try {
			if (myFile.isEmpty()) {
				response.put("Status", 0);
				response.put("Msg", "File is empty!!!");
				return response;
			}

			s3_bucket_details = helper.getExternalConfigValue(bucketname, externalConfigService);
			if (!s3_bucket_details.equals(null)) {
				bucket_name = bucketname.split("_")[1];
				access_key = s3_bucket_details.split(",")[0];
				secret_key = s3_bucket_details.split(",")[1];
			} else {
				response.put("Status", 0);
				response.put("Msg", "S3 bucket name not found!!!");
				return response;
			}
			file_path = bucket_name.split("/")[1] + "/" + myFile.getOriginalFilename();

			String originalFileName = StringUtils.cleanPath(myFile.getOriginalFilename());
			AWSCredentials credentials = new BasicAWSCredentials(access_key, secret_key);
			AmazonS3 s3client = AmazonS3Client.builder().withRegion("us-west-2").withCredentials(new AWSStaticCredentialsProvider(credentials)).build();
			File convFile = new File(myFile.getOriginalFilename());
			FileOutputStream fos = new FileOutputStream(convFile);
			fos.write(myFile.getBytes());
			fos.close();
			try {
				PutObjectResult saved = s3client
						.putObject(new PutObjectRequest(bucket_name.split("/")[0], file_path, convFile)
								.withCannedAcl(CannedAccessControlList.PublicRead));
				if (saved != null) {
					log.info("uploadImageToS3Bucket : File saved in S3 Bucket!");
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("image_url", s3Url + bucket_name + "/" + originalFileName);
					return response;
				}
			} catch (Exception ex) {
				log.error("uploadImageToS3Bucket : " + ex.getMessage());
				response.put("Status", 0);
				response.put("Msg", "S3 bucket name not found!!!");
				response.put("error", ex.getLocalizedMessage());
			} finally {
				convFile.delete();
			}
		} catch (Exception e) {
			log.error("uploadImageToS3Bucket : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "S3 bucket name not found!!!");
			response.put("error", e.getLocalizedMessage());
		}
		return response;
	}

}
