package com.nimble.irisservices.controller;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.MonitorType;
import com.nimble.irisservices.service.IMonitorTypeService;

@RestController
public class MonitorTypeController {
	private static final Logger log = LogManager.getLogger(MonitorTypeController.class);

	@Autowired
	IMonitorTypeService MonitorTypeService;

	@PostMapping(value = "v3.0/updatemonitortype", headers = "Accept=application/json")
	private JResponse saveOrUpdateMonitorType(@RequestBody MonitorType monitorType) {

		JResponse response = new JResponse();
		try {
			boolean isUpdated = MonitorTypeService.updateMonitorType(monitorType);
			if (isUpdated) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed");
			}

		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		return response;
	}

	// Used in web
	@GetMapping("v3.0/getallmonitortypes")
	private JResponse getAllMonitorTypes() {
		JResponse jres = new JResponse();

		List<MonitorType> monitorTypeLists = MonitorTypeService.getAllMonitorTypes();

		if (monitorTypeLists.size() > 0 && monitorTypeLists != null) {
			jres.put("status", 1);
			jres.put("msg", "success");
			jres.put("monitorTypes", monitorTypeLists);
		} else {
			jres.put("status", 0);
			jres.put("msg", "failed");
		}
		return jres;
	}

	@GetMapping(value = "v3.0/getmonitortypebyid", headers = "Accept=application/json")
	private JResponse getMonitorTypeById(@RequestParam("id") long id) {
		JResponse response = new JResponse();

		try {
			MonitorType monitorType = MonitorTypeService.getMonitorTypeById(id);
			if (monitorType != null) {
				response.put("status", 1);
				response.put("msg", "success");
				response.put("monitortype", monitorType);
			} else {
				response.put("status", 0);
				response.put("msg", "failed");
			}
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			response.put("status", 0);
			response.put("msg", "exception occured");
		}

		return response;
	}

	@DeleteMapping(value = "v3.0/deleteMonitortype", headers = "Accept=application/json")
	private JResponse deleteMonitorType(@RequestParam int id) {
		JResponse response = new JResponse();

		try {
			boolean isDeleted = MonitorTypeService.deleteMonitorType(id);

			if (isDeleted) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed");
			}

		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}

		return response;
	}

}
