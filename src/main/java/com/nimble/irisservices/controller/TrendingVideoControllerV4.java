package com.nimble.irisservices.controller;

import java.util.ArrayList;
import java.util.List;

import javax.validation.ConstraintViolationException;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JTrendingVideo;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.service.ITrendingvideoService;
import com.nimble.irisservices.service.ITrendingvideoServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class TrendingVideoControllerV4 {

	private static final Logger log = LogManager.getLogger(TrendingVideoControllerV4.class);

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	ITrendingvideoServiceV4 trendingServiceV4;

	@Autowired
	ITrendingvideoService trendingService;


	@RequestMapping(value = "v4.0/trendingvideoslist/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getTrendingVideosListv4(@PathVariable String autho) {

		JResponse response = new JResponse();
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
			
			if (user != null) {
				List<JTrendingVideo> trendvideoList = trendingService.getTrendingvideos();

				trendvideoList = trendingService.getTrendingvideoInfo(trendvideoList, user.getId());

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("TrendingVideoList", trendvideoList);
				log.info("getTrendingVideo completed");

			} else {
				response.put("Status", 0);
				response.put("Msg", "User not exists");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "failure");
			log.error("getTrendingVideo : " + e.getLocalizedMessage());
		}

		return response;
	}

	// updateVideoStatusV4 -by Anand
	@RequestMapping(value = "v4.0/updatevideostatus/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateVideoStatusV4(@PathVariable String autho, @RequestParam("videoid") long videoid,
			@RequestParam("like") int like, @RequestParam("viewcount") int viewcount,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		log.info("Entering updateVideoStatusV4 : " + autho);
		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);
			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			boolean Status = trendingServiceV4.UpdateVideoInfoTransaction(usr.getId(), videoid, like, viewcount);
			if (!Status)
				Status = trendingServiceV4.CreateVideoInfoTransaction(usr.getId(), videoid, like, viewcount);

			if (Status) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				log.info("getTrendingVideostatus completed");
			} else {
				log.error("videoid not found..");
				response.put("Status", 0);
				response.put("Msg", "Video not exists");
			}

		} catch (ConstraintViolationException ex) {
			response.put("Status", 0);
			response.put("Msg", "Videostatus already exist");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : updateVideoStatusV4 : " + ex.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Videostatus creation failed");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : updateVideoStatusV4 : " + e.getLocalizedMessage());
		}

		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// getTrendingVideosListV4 - function by anand
	@RequestMapping(value = "v4.1/trendingvideoslist/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getTrendingVideosListV41(@PathVariable String autho,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {

		JResponse response = new JResponse();
		log.info("Entering getTrendingVideosListV4 : " + autho);
		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);

			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			List<JTrendingVideo> trendvideoList = new ArrayList<JTrendingVideo>();

			trendvideoList = trendingServiceV4.getTrendingvideoInfoV4(trendvideoList, usr.getId());

			// trendvideoList = _helper.updateYoutubeStatistics(trendvideoList,youtubeAPI);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("TrendingVideoList", trendvideoList);
			log.info("getTrendingVideo completed");
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception occured while getting Trending VideosListV4");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception getTrendingVideosListV4 : " + e.getLocalizedMessage());
		}

		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

}
