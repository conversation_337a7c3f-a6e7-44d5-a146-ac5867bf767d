package com.nimble.irisservices.controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.google.gson.JsonObject;
import com.nimble.irisservices.dto.AlexaTokenSet;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAlexaService;
import com.nimble.irisservices.service.IUserServiceV4;

@RestController
public class AlexaController {

	private static final Logger log = LogManager.getLogger(AlexaController.class);

	@Autowired
	IUserServiceV4 userServiceV4;
	
	@Value("${waggleauth_url}")
	private String waggleAuthUrl;
	
	@Value("${amazon.alexa.skillid}")
	private String waggleSkillId;
	
	@Autowired
	Helper _helper = new Helper();
	
	@Value("${amazon.alexa.state}")
	private String amazonAlexaState;
	
	@Value("${waggle.universal_url}")
	private String waggleUniversalUrl;
	
	@Value("${waggle.redirect_uri}")
	private String waggleRedirectUri;	
	
	@Autowired
	IAlexaService alexaService;
	
	@RequestMapping(value = "v4.0/enablealexaskill/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public JResponse enableAlexaSkillAndLinkAccount(@PathVariable String autho, @RequestParam String amazonauthcode,
			@RequestParam String deviceos,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
			) throws Exception {
		log.info("Entered into enableAlexaSkillAndLinkAccount : authkey : "+autho);
		JResponse response = new JResponse();	
		try {
			UserV4 usr = null;
			try {
				usr = userServiceV4.verifyAuthV3("authkey", autho);				
				String username = usr.getUsername();             //AES.encrypt(usr.getUsername());
				String password = usr.getPassword();             //AES.encrypt(usr.getPassword());		
				String url = waggleAuthUrl+"/getauthcode?username="+username+"&password="+password;
				
				String waggleAuthCode = _helper.httpPOSTRequest(url,null,null);
				log.info("waggle user's auth code : "+waggleAuthCode);
				
				if(waggleAuthCode == null) {
					log.info("Could not get waggle user authcode : "+waggleAuthCode);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					return response;
				}
				
				org.json.JSONObject authCodeObj = new org.json.JSONObject(waggleAuthCode);
				org.json.JSONObject authCodeRes = authCodeObj.getJSONObject("response");

				int status = authCodeRes.getInt("Status");
				if(status == 1 && !authCodeRes.getString("authcode").equalsIgnoreCase("NA")) {
					waggleAuthCode = authCodeRes.getString("authcode");
				}else {
					log.info("Could not get waggle user authcode : "+waggleAuthCode);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					return response;
				}
				
				String accessToken = _helper.getAwsAccessToken(amazonauthcode,usr.getId());	
				log.info("Amazon access token : "+accessToken);				
				if(accessToken == null) {
					log.info("Could not get amazon access token : "+accessToken);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					return response;
				}				
				String alexaUserEndpointResponse = _helper.getAmazonUserEndpointArray(accessToken);
				if(alexaUserEndpointResponse == null) {
					log.info("Could not get user endpoint based on location : "+alexaUserEndpointResponse);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					return response;
				}
				org.json.JSONObject alexaUserEndpointObj = new org.json.JSONObject(alexaUserEndpointResponse);
				if(!alexaUserEndpointObj.has("endpoints")) {
					log.info("Could not get user endpoint based on location : "+alexaUserEndpointResponse);
					response.put("Status", 0);
					response.put("Msg", "Alexa linking failed, please try again");
					response.put("is_linked", false);
					return response;
				}
				JSONArray alexaUserEndpointArray = alexaUserEndpointObj.getJSONArray("endpoints");
				for(Object baseEndPoint : alexaUserEndpointArray) {
					try {
						String skillActivationApi = "https://"+baseEndPoint+"/v1/users/~current/skills/"+waggleSkillId+"/enablement";
						
						JsonObject postBody = new JsonObject();
						postBody.addProperty("stage", amazonAlexaState);
						JsonObject accountLinkRequest = new JsonObject();
						accountLinkRequest.addProperty("redirectUri", waggleRedirectUri);			
						accountLinkRequest.addProperty("authCode", waggleAuthCode);
						accountLinkRequest.addProperty("type", "AUTH_CODE");
						postBody.add("accountLinkRequest", accountLinkRequest);	
						StringEntity postingString = new StringEntity(postBody.toString());
						HttpClient httpclient = HttpClients.createDefault();
						HttpPost httppost = new HttpPost(skillActivationApi);
						httppost.setEntity(postingString);
						httppost.setHeader("Content-type", "application/json");
						httppost.setHeader("Authorization", "Bearer "+accessToken);
						HttpResponse response1 = httpclient.execute(httppost);
						String serviceResponse = EntityUtils.toString(response1.getEntity());
						String enablementResponse = serviceResponse.toString();		
						log.info("Skill enablement service response : "+enablementResponse);
						if(enablementResponse.contains("Invalid")  || enablementResponse.contains("Could not contact provider of account linking credentials")) {
							response.put("Status", 0);
							response.put("Msg", "Alexa linking failed, please try again");
							response.put("is_linked", false);
							return response;
						}
						else if(enablementResponse.contains("Incorrect endpoint for customer")) {
							response.put("Status", 0);
							response.put("Msg", "Alexa linking failed, please try again");
							response.put("is_linked", false);
							continue;
						}

						org.json.JSONObject enablementResponseObj = new org.json.JSONObject(enablementResponse);
						if(enablementResponseObj.has("skill")) {
							org.json.JSONObject skillObj = (org.json.JSONObject) enablementResponseObj.get("skill");
							String stage = skillObj.getString("stage"); log.info("skillStage : "+stage);
							String id = skillObj.getString("id");       log.info("skillId : "+id);
							org.json.JSONObject userObj = (org.json.JSONObject) enablementResponseObj.get("user");
							String userId = userObj.getString("id");    log.info("userId : "+userId);
							org.json.JSONObject accountLinkObj = (org.json.JSONObject) enablementResponseObj.get("accountLink");
							String accountLinkStatus = accountLinkObj.getString("status");  log.info("accountLinkStatus : "+accountLinkStatus);

							response.put("Status", 1);
							if(accountLinkStatus.equalsIgnoreCase("linked")) {
								response.put("Msg", "Alexa linked successfully");
								response.put("is_linked", true);
							}else {
								response.put("Msg", "Alexa linking failed, please try again");
								response.put("is_linked", false);

							}
										
							String enableStatus = enablementResponseObj.getString("status"); 
							log.info("enableStatus : "+enableStatus);								
							break;
						}else {
							response.put("Status", 1);
							response.put("Msg", "Alexa linking failed, please try again");
							response.put("is_linked", false);
							log.error("Enablement response doesn't have expected data");
						}
					}catch (Exception e) {
						log.error("Exception occured at enableAlexaSkillAndLinkAccount : "+e.getLocalizedMessage());
						response.put("Status", 0);
						response.put("Msg", "Alexa linking failed, please try again");
						response.put("is_linked", false);
						response.put("Error", e.getLocalizedMessage());
					}
				}
			} catch (InvalidAuthoException e) {
				log.error("InvalidAuthoException :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Alexa linking failed, please try again");
				response.put("is_linked", false);
				response.put("Error", e.getLocalizedMessage());
			}						
		}catch(Exception e) {			
			response.put("Status", 0);
			response.put("Msg", "Alexa linking failed, please try again");
			response.put("is_linked", false);
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception occured while authorizing user : "+e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v4.0/getaccountlinkingstatus", method = RequestMethod.GET, headers = "Accept=application/json")
	public JResponse getAccountLinkingStatus(@RequestParam long userid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
			) throws Exception {
		log.info("Entered into getAccountLinkingStatus : userId : "+userid);
		JResponse response = new JResponse();		
		try {
			AlexaTokenSet ats = alexaService.getAlexaTokens(userid);
			if(ats == null) {
				log.info("Could not find user's alexa credentials");
				response.put("Status", 1);
				response.put("Msg", "Alexa not linked, please link");
				response.put("is_linked", false);
				return response;
			}		
			log.info("Access token : "+ats.getAccessToken());
			
			String alexaApiEndpoint = "https://api.eu.amazonalexa.com/v1/alexaApiEndpoint";
			String alexaUserEndpointResponse = _helper.httpGETRequest(alexaApiEndpoint, "Bearer "+ats.getAccessToken());				
			if(alexaUserEndpointResponse == null) {
				log.info("Could not get user endpoint based on location : "+alexaUserEndpointResponse);
				response.put("Status", 0);
				response.put("Msg", "Alexa not linked, please link");
				response.put("is_linked", false);
				return response;
			}
			org.json.JSONObject alexaUserEndpointObj = new org.json.JSONObject(alexaUserEndpointResponse);

			if(!alexaUserEndpointObj.has("endpoints")) {
				log.info("Something is wrong while try to get amazon user's endpoint");
				response.put("Status", 0);
				response.put("Msg", "Alexa not linked, please link");
				response.put("is_linked", false);
				return response;
			}
			JSONArray alexaUserEndpointArray = alexaUserEndpointObj.getJSONArray("endpoints");

			for(Object baseEndPoint : alexaUserEndpointArray) {
				try {
				String accLinkingStatusUrl = "https://"+baseEndPoint+"/v1/users/~current/skills/"+waggleSkillId+"/enablement";				
				String endPointResponse = _helper.httpGETRequest(accLinkingStatusUrl, "Bearer "+ats.getAccessToken());				

//				{"skill":{"stage":"development","id":"amzn1.ask.skill.4a03c200-fa57-4976-a5d6-b0526c6fac3f"},"user":{"id":"amzn1.ask.account.AHV5WHOYTVVK7P3YG6EITK5VHZZU4GEBAC2TM5AOPRZJJYIIBYSTCRHI673LGW2PI3VLWCP2UIQHWDWFGXXFRIATJK33POHSHRPPD3LASIU2YDBISFUS445SKILHORN2QL76WOMH2ZDMOTQ4LLVQGZUTUZB4TP3YPXTI52D2RZVCGNI7FNJTRYYYORRAU5JNBLPC66GUHLB64AI"},"accountLink":{"status":"LINKED"},"status":"ENABLED"}
					org.json.JSONObject enablementResponseObj = new org.json.JSONObject(endPointResponse);
					
					if(enablementResponseObj.has("skill")) {
						org.json.JSONObject skillObj = (org.json.JSONObject) enablementResponseObj.get("skill");
						String stage = skillObj.getString("stage"); log.info("skillStage : "+stage);
						String id = skillObj.getString("id");       log.info("skillId : "+id);
						org.json.JSONObject userObj = (org.json.JSONObject) enablementResponseObj.get("user");
						String userId = userObj.getString("id");    log.info("userId : "+userId);
						org.json.JSONObject accountLinkObj = (org.json.JSONObject) enablementResponseObj.get("accountLink");
						String accountLinkStatus = accountLinkObj.getString("status");  log.info("accountLinkStatus : "+accountLinkStatus);
						response.put("Status", 1);
						if(accountLinkStatus.equalsIgnoreCase("linked")) {
							response.put("Msg", "Alexa linked successfully");
							response.put("is_linked", true);
						}else {
							response.put("Msg", "Alexa not linked, please link");
							response.put("is_linked", false);
						}									
						String enableStatus = enablementResponseObj.getString("status"); log.info("enableStatus : "+enableStatus);							
						return response;
					}else if(enablementResponseObj.has("message")) {
						String message = enablementResponseObj.getString("message");
						log.info("Response from alexa about skill status, message : "+message);
						response.put("Status", 1);
						response.put("Msg", "Alexa not linked, please link");
						response.put("is_linked", false);
						return response;
					}
				}catch (Exception e) {
					log.error("Error while getting Account Linking Status : "+e.getLocalizedMessage());
				}		
			}
			response.put("Status", 0);
			response.put("Msg", "Alexa not linked, please link");
			response.put("is_linked", false);
			log.error("Enablement response doesn't have expected data");
		}catch(Exception e) {
			log.error("Error occured at getAccountLinkingStatus : "+e.getLocalizedMessage());
		}
		return response;
	}

	@RequestMapping(value = "v4.0/disablealexaskill", method = RequestMethod.POST, headers = "Accept=application/json")
	public JResponse disableAlexaSkill(@RequestParam long userid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
			) throws Exception {
		log.info("Entered into disableAlexaSkill : userid : "+userid);
		JResponse response = new JResponse();

		AlexaTokenSet ats = alexaService.getAlexaTokens(userid);
		
		if(ats == null) {
			log.info("Could not find user's alexa credentials");
			response.put("Status", 1);
			response.put("Msg", "Alexa not linked, please link");
			response.put("is_linked", false);
			return response;
		}
		String alexaUserEndpointResponse = _helper.getAmazonUserEndpointArray(ats.getAccessToken());
		if(alexaUserEndpointResponse == null) {
			log.info("Could not get user endpoint based on location : "+alexaUserEndpointResponse);
			response.put("Status", 0);
			response.put("Msg", "Alexa still linked, please unlink");
			response.put("is_linked", true);
			return response;
		}
		org.json.JSONObject alexaUserEndpointObj = new org.json.JSONObject(alexaUserEndpointResponse);		
		if(!alexaUserEndpointObj.has("endpoints")) {
			log.info("Something is wrong while try to get amazon user's endpoint");
			response.put("Status", 0);
			response.put("Msg", "Alexa still linked, please unlink");
			response.put("is_linked", true);
			return response;
		}
		JSONArray alexaUserEndpointArray = alexaUserEndpointObj.getJSONArray("endpoints");

		for(Object baseEndPoint : alexaUserEndpointArray) {
			HttpURLConnection request = null;
			BufferedReader in = null;
			String disableAlexaSkillUrl = "https://"+baseEndPoint+"/v1/users/~current/skills/"+waggleSkillId+"/enablement";
			try {
				URL url = new URL(disableAlexaSkillUrl);
				request = (HttpURLConnection) url.openConnection();
				request.setRequestMethod("DELETE");
				request.setRequestProperty("Accept", "application/json");
				request.setRequestProperty("Authorization", "Bearer "+ats.getAccessToken());
				request.setDoOutput(true);
				int responseCode = request.getResponseCode();
				StringBuilder sb = new StringBuilder();
				String inputLine;
				if (responseCode >= 400) {
					in = new BufferedReader(new InputStreamReader(request.getErrorStream(), "UTF-8"));
					while ((inputLine = in.readLine()) != null) {
						sb.append(inputLine);
						sb.append("\n");
					}
					in.close();
				} else {
					in = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
					while ((inputLine = in.readLine()) != null) {
						sb.append(inputLine);
						sb.append("\n");
					}
					in.close();
				}
				log.info("HTTP Response received : responseCode : "+responseCode);
				request.disconnect();
				if(responseCode == 204) {
					response.put("Status", 1);
					response.put("Msg", "Alexa unlinked successfully");
					response.put("is_linked", false);
					log.info("Disable skill and unlink account successful.");
				}else {
					response.put("Status", 1);
					response.put("Msg", "Alexa still linked, please unlink");
					response.put("is_linked", true);
					log.info("Failed to disable skill and unlink account.");
				}
			} catch (IOException e) {
				log.error("Exception occured at disableAlexaSkill : "+e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Process failed, try again!");
				response.put("is_linked", true);
			}		
		}
		return response;
	}

}
