package com.nimble.irisservices.controller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.ocpsoft.prettytime.PrettyTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;

import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IFurBitReportService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IOptimizedV4Service;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IWifiInfoService;
import com.nimble.irisservices.service.IWifiInfoServiceV4;

@Controller
public class WifiInfoControllerV4 {

	private static final Logger log = LogManager.getLogger(WifiInfoControllerV4.class);
	
	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;
	
	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IWifiInfoService wifiService;
	
	@Autowired
	@Lazy
	IWifiInfoServiceV4 wifiServiceV4;

	@Autowired
	IFurBitReportService iFurBitReportService;

	@Value("${lastrpt_max}")
	private String lastrpt_max;

	@Value("${lastwifirpt_max}")
	private String lastWifirptMax;
	
	@Autowired 
	@Lazy
	IOptimizedV4Service optimizedService;

	Helper _helper = new Helper();

	PrettyTime prettyTime = new PrettyTime();
	
//	@RequestMapping(value = "v4.0/getgpsandwifistatus/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getgpsandwifistatusV4(@PathVariable String autho, @RequestParam("gatewayId") String gatewayId,
//			@RequestParam("timezone") String timezone) {
//		JResponse response = new JResponse();
//		log.info("Entering getgpsandwifistatus : "+autho);
//		//UserV4 user =null;
//		Map<String, String> map = new HashMap<String, String>();
//
//		try {
//			map = userServiceV4.getUserId_cmpIdByAuthV2(autho);	
//		}catch(InvalidAuthoException ex) {
//			response.put("Status", 0);
//			response.put("Msg", "invalid authkey");
//			response.put("Error", ex.getLocalizedMessage());
//			log.error("Exception while getting user for authkey : "+autho);
//			response.put("Return Time", System.currentTimeMillis());
//			return response;
//		}
//
//		long userId = Long.valueOf(map.get("user_id"));
//
//		timezone = timezone.replaceAll("\\s+","");
//		if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
//			timezone = "+" + timezone;
//
//		try {
//			JGateway jgateway = gatewayService.getJGateway("id",gatewayId);
//			AssetModel assetModel =null;
//			boolean gps = false;
//			boolean advmode = false;
//			boolean powermode = false;
//			boolean signalstatus=false;
//			String lastwifireporttime = "NA";
//			String furbitLastRptDt = "NA";
//			String lastgpsdatetime = "NA";
//			String SSID="No WiFi Details found!!";
//			String password="No WiFi Details found!!";
//			String timeago = "";
//			String gpstimeago = "";
//			String addr = "";
//			String signal = "w";
//			String wifistatus = "";
//			String zonename = "";
//			String zonedesc = "";
//			double lat =0;
//			double lon =0;
//			int zonelevel = -1;
//			try {
//				assetModel = gatewayService.getAssetModel(jgateway.getModelid());
//			}catch (Exception e) {
//			}
//
//			if(assetModel != null) {
//				if(assetModel.getIsgps().equalsIgnoreCase("1"))
//					gps = true;
//				else
//					gps = false;
//
//				advmode = assetModel.isAdvmode();
//				powermode = assetModel.isPowermode();
//			}
//
//			List<FurbitLastGatewayReport> getwayreport = iFurBitReportService.getFurbitLastGatewayReport(gatewayId);
//
//			SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
//			SimpleDateFormat formatter1 = new SimpleDateFormat("dd MMM yyyy, hh:mm aa");
//
//			String curUTC = IrisservicesUtil.getCurrentTimeUTC();
//			String currenttime = IrisservicesUtil.getDateime_Timezone(curUTC, IrisservicesConstants.UTCTIMEZONE,timezone);
//			Date curr = formatter.parse(currenttime);
//			WifiInfo wifiinfo = null;
//
//			if (getwayreport != null && !getwayreport.isEmpty()) {
//
//				Date ldate = getwayreport.get(0).getDate();
//				Time ltime = getwayreport.get(0).getTime();
//
//				String ldatetime = ldate.toString() + " " + ltime.toString();
//
//				String lastdatetime = IrisservicesUtil.getDateime_Timezone(ldatetime, IrisservicesConstants.UTCTIMEZONE,timezone);
//
//				Date pktDate = formatter.parse(lastdatetime);
//				furbitLastRptDt = formatter1.format(formatter.parse(lastdatetime));
//				timeago = prettyTime.format(new Date(getwayreport.get(0).getDatetime().getTime())).replace("minute", "min");
//
//				//last valid gps dattime
//				if(getwayreport.get(0).getLastgpsdatetime() != null){
//					Date gpsdate=new Date(getwayreport.get(0).getLastgpsdatetime().getTime());
//
//					lastgpsdatetime = IrisservicesUtil.getDateime_Timezone(formatter.format(gpsdate), IrisservicesConstants.UTCTIMEZONE,timezone);
//					lastgpsdatetime = formatter1.format(formatter.parse(lastgpsdatetime));
//					gpstimeago = prettyTime.format(new Date(getwayreport.get(0).getLastgpsdatetime().getTime())).replace("minute", "min");
//				}
//
//				long diff =curr.getTime() - pktDate.getTime();
//				long limit = (Integer.parseInt(lastrpt_max) * 3600000);
//
//				if (diff < limit && diff >= 0) {
//					signalstatus=true;
//				}
//
//				if(getwayreport.get(0).getTxnMode()==1) {
//					signal ="c" ;
//				}
//				else if(getwayreport.get(0).getTxnMode()==0){
//					signal = "w";
//				}else {
//					signal = "";
//				}
//
//				lat = getwayreport.get(0).getLastvalidlat();
//				lon = getwayreport.get(0).getLastvalidlon();
//				zonename = getwayreport.get(0).getZonename();
//				zonedesc = getwayreport.get(0).getZonedesc();
//				zonelevel= getwayreport.get(0).getZonelevel();
//				//TODO: needed oly for wifi - txnmode
//				String ssiscat = "SSID_1";
//
//				if(getwayreport.get(0).getWifissid() == 1)
//					ssiscat = "SSID_1";
//				else if(getwayreport.get(0).getWifissid() == 2)
//					ssiscat = "SSID_2";
//
//				//Getting last wifi info 
//				wifiinfo = wifiService.isAlreadycontain(Long.parseLong(gatewayId),ssiscat,userId);
//			}
//			else {
//				//Getting last wifi info 
//				wifiinfo = wifiServiceV4.isAlreadycontain(Long.parseLong(gatewayId));
//			}
//
//			if(wifiinfo !=null )
//			{
//				SSID=wifiinfo.getSsid();
//
//				password=wifiinfo.getPassword();
//				addr = wifiinfo.getAddress();
//
//				Date lastreportdate = new Date(wifiinfo.getUpdatedOn().getTime());
//				String lastdatetime = formatter.format(lastreportdate);
//				String outputreportdate = IrisservicesUtil.getDateime_Timezone(lastdatetime,IrisservicesConstants.UTCTIMEZONE, timezone);
//
//				Date finaldate = formatter.parse(outputreportdate);
//
//				lastwifireporttime = formatter1.format(finaldate);
//
//				Date pre = formatter.parse(outputreportdate);
//
//				if(!signalstatus) {
//					long diff =  (curr.getTime() - pre.getTime());
//					long limit = (Integer.parseInt(lastWifirptMax) * 60000);
//
//					if (diff < limit && diff >= 0 && wifiinfo.isStatus()) {
//						signalstatus = true;
//					}else
//						signalstatus = false;
//				}
//			}			
//
//			if (signalstatus)
//				wifistatus = "WiFi connected";
//			else if(wifiinfo !=null && wifiinfo.isStatus()) 
//				wifistatus = "WiFi connected"; 
//			else 
//				wifistatus = "WiFi not connected";
//
//			JWifiStatus jwifistatus = new JWifiStatus(gps, advmode, powermode, SSID, password, signal, wifistatus, zonename,
//					lat, lon, furbitLastRptDt, timezone, lastwifireporttime, timeago, signalstatus,addr,zonedesc,zonelevel,
//					lastgpsdatetime,gpstimeago);
//			response.put("Status", 1);
//			response.put("Msg", "Success");
//			response.put("wifiinfo", jwifistatus);
//
//			log.info("Exit : getgpsandwifistatus");
//		} catch (Exception e) {
//			log.error("Exception: getgpsandwifistatus :"+ e.getLocalizedMessage());
//			response.put("Status", 0);
//			response.put("Msg", "Unexpected Error. Please Contact Support ");
//			response.put("Error", e.getLocalizedMessage());
//			response.put("Return Time", System.currentTimeMillis());
//			return response;
//		}
//		response.put("Return Time", System.currentTimeMillis());
//		return response;
//	}


	
}
