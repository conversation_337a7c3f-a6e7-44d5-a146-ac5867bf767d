package com.nimble.irisservices.controller;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JOrderChannel;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AlertType;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.MessageType;
import com.nimble.irisservices.entity.MonitorType;
import com.nimble.irisservices.entity.NotificationType;
import com.nimble.irisservices.entity.OrderChannel;
import com.nimble.irisservices.entity.ReportType;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IFetchDropdownService;
import com.nimble.irisservices.service.IFotaService;
import com.nimble.irisservices.service.IMonitorTypeService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class DropdownController {

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IFetchDropdownService fetchDropdownService;
	
	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;
	
	@Autowired
	IMonitorTypeService MonitorTypeService;
	
	@Autowired
	@Lazy
	IFotaService fotaService;
	
	private static final Logger log = LogManager.getLogger(DropdownController.class);
	
	// Used in web
	// ========get assetmodels================
	@RequestMapping(value = "v3.0/assetmodel/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetModel(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			List<AssetModel> assetmodel = fetchDropdownService.getAssetModel();
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetmodel", assetmodel);
//			System.out.println("AsyncStarted");
//			asyncs.runAsync();

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

//		System.out.println("===========================================================");
		return response;

	}

	// ========get alerttypes================
	@RequestMapping(value = "v3.0/alerttype/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertType(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			List<AlertType> alerttypes = fetchDropdownService.getAlertTypes();
			for(AlertType atObj : alerttypes) {
				if(atObj.getId() == 1) {
					atObj.setMaxRange(80);
					atObj.setMinRange(-45);
					break;
				}
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("alerttype", alerttypes);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		

		return response;

	}

	// Used in web
	// ========get reporttype================
	@RequestMapping(value = "v3.0/reporttype/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getReportType(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			List<ReportType> reporttypes = fetchDropdownService.getReportTypes();
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("reporttype", reporttypes);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	// ========get messagetype================
	@RequestMapping(value = "v3.0/messagetype/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getMessageType(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			List<MessageType> messagetypes = fetchDropdownService.getMessageTypes();
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("messagetype", messagetypes);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	// Used in web
	@RequestMapping(value = "v3.0/cmpalerttype/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertTypeByCmp(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			List<AlertType> alerttypes = fetchDropdownService.getAlertTypesByCmp(user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("cmpalerttype", alerttypes);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	// Used in web
	// ========get reporttype================
	@RequestMapping(value = "v3.0/cmpreporttype/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getReportTypeByCmp(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			List<ReportType> reporttypes = fetchDropdownService.getReportTypesByCmp(user.giveCompany().getId());
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("cmpreporttype", reporttypes);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;

	}

	// ========get OrderChannel================
	@RequestMapping(value = "v3.0/orderchannel", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getOrderChannel() {
		JResponse response = new JResponse();
		try {
			// User user = userService.verifyAuthKey(autho);
			List<JOrderChannel> jOrderChannelsList = new ArrayList<JOrderChannel>();

			List<OrderChannel> orderChannels = fetchDropdownService.getOrderChannel();

			if (orderChannels != null && orderChannels.size() > 0) {

				for (OrderChannel orderChannel : orderChannels) {

					JOrderChannel jorderChannel = new JOrderChannel();

					jorderChannel.setOrderChannel(orderChannel.getOrderchannel());
					jorderChannel.setShortDescription(orderChannel.getShortdescription());
					jorderChannel.setImgpath(orderChannel.getImgpath());

					jOrderChannelsList.add(jorderChannel);
				}
				response.put("Status", 1);
				response.put("Msg", "Success");

				// response.put("orderChannel", jOrderChannelsList);
				response.put("Orderchannel", orderChannels);
				return response;

			} else {
				response.put("Status", 0);
				response.put("Msg", "No Order channel listed.");
				return response;
			}

		} catch (Exception e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "Exception when getting orderchannels.");
			return response;
		}

	}

	// ========get NotificationType================
	@RequestMapping(value = "v3.0/notificationtype", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getNotification() {
		JResponse response = new JResponse();
		try {
			// User user = userService.verifyAuthKey(autho);
			List<NotificationType> notificationTypes = new ArrayList<NotificationType>();

			notificationTypes = fetchDropdownService.getNotificaitonType();

			if (notificationTypes != null && notificationTypes.size() > 0) {

				response.put("Status", 1);
				response.put("Msg", "Success");

				// response.put("orderChannel", jOrderChannelsList);
				response.put("notificationtype", notificationTypes);
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No Notification Type listed.");
				return response;
			}

		} catch (Exception e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "Exception when getting notification type.");
			return response;
		}

	}

	@Async
	public void runFromAnotherThreadPool() {

		int i = 0;
		while (i < 100000) {
//			try {
//				Thread.sleep(5000);
//			} catch (InterruptedException e) {
//				// TODO Auto-generated catch block
//				e.printStackTrace();
//			}
			log.info("You function code here");
			i++;
		}
		log.info("AsyncEnd");
	}
	
	// Used in web
	// Save asset model --> Savitha
	@RequestMapping(value = "v4.0/saveassetmodel/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveAssetModel(@PathVariable String autho, @RequestBody AssetModel assetModel,
			@RequestParam long monitortype_id) {
		log.info("Entered save asset model - " + autho);
		JResponse response = new JResponse();
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			MonitorType monitorType = MonitorTypeService.getMonitorTypeById(monitortype_id);
			if (monitorType != null) {
				assetModel.setMonitor_type(monitorType);
				boolean isSuccess = fetchDropdownService.saveAssetModel(assetModel);
				if (isSuccess) {
					response.put("Status", 1);
					response.put("Msg", "Success");
				} else {
					log.error("Error occurred in save asset model");
					response.put("Status", 0);
					response.put("Msg", "Error occurred!!!");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Monitor type not found!!!");
			}
		} catch (Exception e) {
			log.error("InValid Authkey");
			response.put("Status", 0);
			response.put("Msg", "Exception occurred - " + e.getMessage());
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}
	
	// Used in web
	// Get gps valuse based on asset id --> Savitha
	@RequestMapping(value = "v4.0/checkgpsstatus/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse checkGpsStatus(@PathVariable String autho, @RequestParam long model_id) {
		log.info("Entered get asset model by id - " + autho);
		JResponse response = new JResponse();
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);

			AssetModel assetModel = fotaService.getAssetModelById(model_id);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("isgps", assetModel.getIsgps());
		} catch (Exception e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "Exception in get asset model by id!!!");
			return response;
		}
		return response;
	}

}
