package com.nimble.irisservices.controller;

import org.springframework.web.bind.annotation.RestController;

@RestController
public class FurBitController {
/*
	private static final Logger log = LogManager.getLogger(FurBitController.class);

	@Value("${lastrpt_max}")
	private String lastrpt_max;

	@Autowired
	IFurBitReportService iFurBitReportService;

	@Autowired
	IUserService userService;

	@Autowired
	IGatewayService gatewayService;
	
	@Autowired
	IGatewayServiceV4 gatewayServiceV4;	
		
	
	@Autowired
	@Lazy
	ICompanyServiceV4 companyServicev4;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	IAlertCfgService alertCfgService;

	@RequestMapping("/")
	public String greeting() {
		log.info("Starteds");
		return "Greetings from Waggle!";
	}

	// ========get petfitdailyreport================
	@RequestMapping(value = "v3.0/furbitdailyrpt/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFurBitDailyRpt(@PathVariable String autho, @RequestParam("date") String date) {
		JResponse response = new JResponse();
		try {
			log.info("Entered :: FurBitController::::getPetFitDailyReport::[{}]", date);
			List<FurBitDailyReport> furBitDailyReport = iFurBitReportService.getFurBitDailyRpt(date);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("furBitDailyReport", furBitDailyReport);
			log.info("Exit :: FurBitController::::getPetFitDailyReport::[{}]", date);
		} catch (Exception e) {
			log.error("PetFitController:::::getPetFitDailyReport [{}] ", e);
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}

	// ========get petfitdailyreport================
	@RequestMapping(value = "v3.0/checkwifistatus/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse checkWifiStatus(@PathVariable String autho, @RequestParam("gatewayId") String gatewayId,
			@RequestParam("timezone") String timezone) {
		JResponse response = new JResponse();
		log.info("Entered :: FurBitController::::getPetFitlastReport::[{}]");

		timezone = timezone.replaceAll("\\s+","");
		if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
			timezone = "+" + timezone;
		System.out.println(timezone);

		try {
			List<FurbitLastGatewayReport> getwayreport = iFurBitReportService.getFurbitLastGatewayReport(gatewayId);
			List<FurBitReport> furBitReprts = iFurBitReportService.getFurBitlastreport(gatewayId);

			SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
			SimpleDateFormat formatter1 = new SimpleDateFormat("dd MMM yyyy, hh:mm aa");

			String outputlastreportdate = "";

			if (furBitReprts != null && !furBitReprts.isEmpty()) {

				Timestamp ts = furBitReprts.get(0).getEnddatetime();
				Date lastreportdate = new Date(ts.getTime());

				String lastdatetime = formatter.format(lastreportdate);

				outputlastreportdate = IrisservicesUtil.getDateime_Timezone(lastdatetime,
						IrisservicesConstants.UTCFORMAT, timezone);

				Date finaldate = formatter.parse(outputlastreportdate);

				outputlastreportdate = formatter1.format(finaldate);
			}

			if (getwayreport != null && !getwayreport.isEmpty()) {
				Date ldate = getwayreport.get(0).getDate();
				Time ltime = getwayreport.get(0).getTime();

				String ldatetime = ldate.toString() + " " + ltime.toString();

				String ltimezone = getwayreport.get(0).getTimezone();

				String lastdatetime = IrisservicesUtil.getDateime_Timezone(ldatetime, ltimezone,
						IrisservicesConstants.UTCFORMAT);

				String currenttime = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT,
						IrisservicesConstants.UTCFORMAT);

				String outputwifidate = IrisservicesUtil.getDateime_Timezone(ldatetime, ltimezone, timezone);

				Date pre = formatter.parse(lastdatetime);
				Date curr = formatter.parse(currenttime);
				Date outputwifi = formatter.parse(outputwifidate);

				long diff =  (curr.getTime() - pre.getTime());
				long limit = (Integer.parseInt(lastrpt_max) * 3600000);

				if (diff < limit) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("Lastreportedtime", formatter1.format(outputwifi));
					response.put("Description", "Wifi connected");
					response.put("Furbitlastreporttime", outputlastreportdate);
				} else {
					response.put("Status", 2);
					response.put("Msg", "Success");
					response.put("Lastreportedtime", formatter1.format(outputwifi));
					response.put("Description", "No Data received since last " + limit / 3600000 + " hour");
					response.put("Furbitlastreporttime", outputlastreportdate);
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Wifi not connected");
				response.put("Lastreportedtime", "NA");
				response.put("Description", "No Data received from device");
				response.put("Furbitlastreporttime", outputlastreportdate);
			}
			log.info("Exit :: FurBitController::::getFurBitlastReport::[{}]");
		} catch (Exception e) {
			log.error("FurBitController:::::getFurBitlastReport [{}] ", e);
			response.put("Status", 0);
			response.put("Msg", "Unexpected error . ");
			return response;
		}
		return response;
	}

	// ========get furbit dailyreport================
	@RequestMapping(value = "v3.0/furbitdailyreport/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFurBitDailyReport(@PathVariable String autho, @RequestParam("date") String date,
			@RequestParam("gatewayId") String gatewayId, @RequestParam("hour") String hour,
			@RequestParam("timezone") String timezone) {
		JResponse response = new JResponse();

		timezone = timezone.replaceAll("\\s+","");
		if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
			timezone = "+" + timezone;
		System.out.println(timezone);
		try {
			log.info("Entered :: FurBitController::::getPetFitDailyReport::[{}]", date);
			User user = userService.verifyAuthKey(autho);

			// monitor type by default 2 for furbit
			String monitortype = "2,3";
			// hour =24 - daily summary for all devices of a particular user
			if (hour.equalsIgnoreCase("24")) {
				List<Gateway> userGateway = gatewayService.getGatewayByMonitorType(monitortype, user.getId());

				log.info("userGateway Size" + userGateway.size());
				List<JFurBitReportV1> furBitDailySummaryReport = new ArrayList<JFurBitReportV1>();
				List<JFurBitReportBest> furBitDailyBest = new ArrayList<JFurBitReportBest>();
				List<JFurBitReportAvg> furBitDailyAverage = new ArrayList<JFurBitReportAvg>();
				List<JFurBitReportTotal> furBitDailytotal = new ArrayList<JFurBitReportTotal>();

				if (userGateway != null) {
					if (userGateway.size() > 0) {


						for (Gateway gateway : userGateway) {

							log.info("Gateway ID : " + gateway.getId() + "Report Date : " + date + " , Time ZOne"
									+ timezone);


							JFurBitReportReportSummary furBitDailytotalReport = iFurBitReportService
									.getFurBitDailyReport(date, user.getId(), gateway.getId() + "", hour, timezone,
											gateway.getName(),gateway);

							List<JFurBitReport> furBitDailyReport = furBitDailytotalReport.getjFurBitDailyRptLis();
							List<JFurBitReportBest> furBitDailyBestLis = furBitDailytotalReport.getFurBitDailyBestLis();
							List<JFurBitReportAvg> furBitDailyAverageLis = furBitDailytotalReport
									.getFurBitDailyAverageLis();
							List<JFurBitReportTotal> furBitDailytotalLis = furBitDailytotalReport.getFurBitDailyTotal();

							furBitDailyBest.addAll(furBitDailyBestLis);
							furBitDailyAverage.addAll(furBitDailyAverageLis);
							furBitDailytotal.addAll(furBitDailytotalLis);

							if (furBitDailyReport.size() > 0) {
								for (JFurBitReport jFurBit : furBitDailyReport) {
									AddDailySummaryReport(furBitDailySummaryReport, jFurBit);
								}

							} else {

								JFurBitReport defaultFurBitReport = new JFurBitReport();
								defaultFurBitReport.setFromDatetime("1753-01-01 00:00:00");
								defaultFurBitReport.setToDatetime("1753-01-01 00:00:00");
								defaultFurBitReport.setTotalIdleSecs(0);
								defaultFurBitReport.setTotalRunSecs(0);
								defaultFurBitReport.setTotalStepCount(0);
								defaultFurBitReport.setTotalWalkSecs(0);
								defaultFurBitReport.setTotalActiveSecs(0);
								defaultFurBitReport.setGatewayId(gateway.getId());
								defaultFurBitReport.setGatewayName(gateway.getName());
								defaultFurBitReport.setDefaultStepGoal(gateway.getDefault_goal());
								defaultFurBitReport.setGoalAchievedPercentage("0");

								AddDailySummaryReport(furBitDailySummaryReport, defaultFurBitReport);

								JFurBitReportBest jfurbest = new JFurBitReportBest();

								jfurbest.setBestIdleSecs(0);
								jfurbest.setBestRunSecs(0);
								jfurbest.setBeststepcount(0);
								jfurbest.setBestWalkSecs(0);
								jfurbest.setBestActiveSecs(0);

								jfurbest.setGatewayId(gateway.getId());
								jfurbest.setGatewayName(gateway.getName());

								jfurbest.setBestIdlefromDatetime("1753-01-01 00:00:00");
								jfurbest.setBestIdletoDatetime("1753-01-01 00:00:00");

								jfurbest.setBestRunfromDatetime("1753-01-01 00:00:00");
								jfurbest.setBestRuntoDatetime("1753-01-01 00:00:00");

								jfurbest.setBeststepfromDatetime("1753-01-01 00:00:00");
								jfurbest.setBeststeptoDatetime("1753-01-01 00:00:00");

								jfurbest.setBestWalkfromDatetime("1753-01-01 00:00:00");
								jfurbest.setBestWalktoDatetime("1753-01-01 00:00:00");

								jfurbest.setBestActivefromDatetime("1753-01-01 00:00:00");
								jfurbest.setBestActivetoDatetime("1753-01-01 00:00:00");

								JFurBitReportAvg jfurAvg = new JFurBitReportAvg();
								jfurAvg.setAvgIdleSecs(0);
								jfurAvg.setAvgRunSecs(0);
								jfurAvg.setAvgstepcount(0);
								jfurAvg.setAvgWalkSecs(0);
								jfurAvg.setAvgActivesec(0);

								jfurAvg.setTotalpacket(0);

								jfurAvg.setGatewayId(gateway.getId());
								jfurAvg.setGatewayName(gateway.getName());

								furBitDailyBest.add(jfurbest);
								furBitDailyAverage.add(jfurAvg);

							}

						}

						response.put("furBitDailySummaryReport", furBitDailySummaryReport);

						response.put("furBitDailyBestReport", furBitDailyBest);

						response.put("furBitDailyAverageReport", furBitDailyAverage);

						response.put("furBitDailyTotalReport", furBitDailytotal);

					} else {
						response.put("furBitDailySummaryReport", furBitDailySummaryReport);

						response.put("furBitDailyBestReport", furBitDailyBest);

						response.put("furBitDailyAverageReport", furBitDailyAverage);

						response.put("furBitDailyTotalReport", furBitDailytotal);
					}
				}

			} else { 
				// defined hour interval report for a particular device 
				Gateway gateway = gatewayService.getGateway(Long.valueOf(gatewayId));
				String gatewayName = "";

				if (gateway != null) {
					gatewayName = gateway.getName();
				}

				JFurBitReportReportSummary furBitDailyReport = iFurBitReportService.getFurBitDailyReport(date,
						user.getId(), gatewayId, hour, timezone, gatewayName,gateway);
				response.put("furBitDailySummaryReport", furBitDailyReport.getjFurBitDailyRptLis());

				response.put("furBitDailyBestReport", furBitDailyReport.getFurBitDailyBestLis());

				response.put("furBitDailyAverageReport", furBitDailyReport.getFurBitDailyAverageLis());

				response.put("furBitDailyTotalReport", furBitDailyReport.getFurBitDailyTotal());

			}

			response.put("Status", 1);
			response.put("Msg", "Success");

			log.info("Exit :: FurBitController::::getFurBitDailyReport::[{}]", date);
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error("FurBitController:::::getFurBitDailyReport [{}] ", e);
			response.put("Status", 0);
			response.put("Msg", "Unexpected error . ");
			return response;
		}
		return response;
	}

	private void AddDailySummaryReport(List<JFurBitReportV1> furBitDailySummaryReport, JFurBitReport jFurBit) {

		JFurBitReportV1 FurBitReportV1 = new JFurBitReportV1();
		FurBitReportV1.setFromDatetime(jFurBit.getFromDatetime());
		FurBitReportV1.setToDatetime(jFurBit.getToDatetime());
		FurBitReportV1.setTotalIdleSecs(jFurBit.getTotalIdleSecs());
		FurBitReportV1.setTotalRunSecs(jFurBit.getTotalRunSecs());
		FurBitReportV1.setTotalStepCount(jFurBit.getTotalStepCount());
		FurBitReportV1.setTotalWalkSecs(jFurBit.getTotalWalkSecs());
		FurBitReportV1.setTotalActiveSecs(jFurBit.getTotalActiveSecs());
		FurBitReportV1.setGatewayId(jFurBit.getGatewayId());
		FurBitReportV1.setGatewayName(jFurBit.getGatewayName());
		FurBitReportV1.setDefaultStepGoal(jFurBit.getDefaultStepGoal());
		FurBitReportV1.setGoalAchievedPercentage(jFurBit.getGoalAchievedPercentage());

		List<FurbitLastGatewayReport> getwayreport = iFurBitReportService
				.getFurbitLastGatewayReport(jFurBit.getGatewayId() + "");

		if (getwayreport != null && !getwayreport.isEmpty()) {
			FurBitReportV1.setBatterylife(getwayreport.get(0).getBattery() + "");
		}

		//Calories burnt calculation
		PetProfile prof =gatewayService.getPetProfile(jFurBit.getGatewayId());

		if(prof != null)
		{
			double weight = 0;
			double caloriesBurnt = 0;

			if(prof.getWeight().matches("[0-9]*\\.?[0-9]*")) {
				weight =Float.parseFloat(prof.getWeight())* (0.453592f);
			}
			// Calories Burnt (kcal) = (Rest MET x Rest Duration x Weight ) + // Rest	1
			//(Walk MET x Walk Duration x Weight ) +						//Walk	2
			//(Run MET x Run Duration x Weight )							//Run	8
			double idleHr = jFurBit.getTotalIdleSecs()* (1.0/3600);
			double walkHr = jFurBit.getTotalWalkSecs()*(1.0/3600);
			double runHr = jFurBit.getTotalRunSecs()*(1.0/3600);

			//			System.out.println("idle :"+idleHr);
			//			System.out.println("walk:"+walkHr);
			//			System.out.println("run:"+runHr);
			//			System.out.println("weight:"+weight);

			caloriesBurnt = ( 1 * idleHr * weight )+ 
					( 2 * walkHr * weight ) +
					( 8 * runHr * weight );
			caloriesBurnt = Math.round(caloriesBurnt);

			log.info("caloriesburnt:"+caloriesBurnt);

			FurBitReportV1.setCaloriesburnt(caloriesBurnt);

			//calories burnt percentage

			//			Gateway gateway = gatewayService.getGateway(jFurBit.getGatewayId());
			//			double caloriesGoal = gateway.getCalories_goal();
			//			double caloriesPercent = Math.round((caloriesBurnt / caloriesGoal)*100);
			//			if (caloriesPercent > 100)
			//				caloriesPercent = 100;
			//			FurBitReportV1.setCaloriesAchievedPercentage(String.valueOf(caloriesPercent));
		}

		furBitDailySummaryReport.add(FurBitReportV1);

	}

	// For 7 days report - reportDays =1, days=7
	// For 30days report - reportDays=7,days=30
	@RequestMapping(value = "v3.0/furbitreport/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFurBitReport(@PathVariable String autho, @RequestParam("date") String date,
			@RequestParam("gatewayId") String gatewayId, @RequestParam("timezone") String timezone,
			@RequestParam("days") String days, @RequestParam("reportDays") String reportDays) {
		JResponse response = new JResponse();

		timezone = timezone.replaceAll("\\s+","");
		if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
			timezone = "+" + timezone;
		System.out.println(timezone);

		try {
			log.info("Entered :: FurBitController::::getPetFitReport::[{}]", date);
			User user = userService.verifyAuthKey(autho);

			Gateway gateway = gatewayService.getGateway(Long.valueOf(gatewayId));
			String gatewayName = "";

			if (gateway != null) {
				gatewayName = gateway.getName();
			}

			JFurBitReportReportSummary furBitDailyReport = iFurBitReportService.getFurBitReport(date, user.getId(),
					gatewayId, timezone, days, reportDays,gatewayName,gateway);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("furBitDailySummaryReport", furBitDailyReport.getjFurBitDailyRptLis());

			response.put("furBitDailyBestReport", furBitDailyReport.getFurBitDailyBestLis());

			response.put("furBitDailyAverageReport", furBitDailyReport.getFurBitDailyAverageLis());

			response.put("furBitDailyTotalReport", furBitDailyReport.getFurBitDailyTotal());

			log.info("Exit :: FurBitController::::getFurBitDailyReport::[{}]", date);
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error("FurBitController:::::getFurBitDailyReport [{}] ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to process the report at this time.");
			return response;
		}
		return response;
	}

	// Used in web
	@RequestMapping(value = "v3.0/furbitlastgatewayreport/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFurBitLastGatewayReport(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			//			User user = null;
			//			try {
			//				user = userService.verifyAuthKey(autho);
			//			}
			//			catch (Exception e) {
			//				user = null;
			//			}
			//			
			//			if(user != null) {
			List<FurbitLastGatewayReport> rpt = gatewayService.getFLastGatewayReportByUser(0l);
			List<JFurBitLastGatewayReport> jRptList = convertJFurBitLastGatewayReport(rpt);

			if(jRptList != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("rpt", jRptList);
			}else {
				response.put("Status", 1);
				response.put("Msg", "No Report found");					
			}
			//			}
			//			else
			//			{
			//				response.put("Status", 0);
			//				response.put("Msg", "User not found");
			//			}
		}
		catch (Exception e) {
			log.error("getFurBitLastGatewayReport : ",e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to process the report at this time.");
		}

		return response;
	}
	
	@RequestMapping(value = "v3.0/furbitlastgatewayreportby/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFurBitLastGatewayReport1(@PathVariable String autho) {
		JResponse response = new JResponse();
		try {
			//			User user = null;
			//			try {
			//				user = userService.verifyAuthKey(autho);
			//			}
			//			catch (Exception e) {
			//				user = null;
			//			}
			//			
			//			if(user != null) {
			List<FurbitLastGatewayReport> rpt = gatewayService.getFLastGatewayReportByUser(0l);
			List<JFurBitLastGatewayReport> jRptList = convertJFurBitLastGatewayReport(rpt);

			if(jRptList != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("rpt", jRptList);
			}else {
				response.put("Status", 1);
				response.put("Msg", "No Report found");					
			}
			//			}
			//			else
			//			{
			//				response.put("Status", 0);
			//				response.put("Msg", "User not found");
			//			}
		}
		catch (Exception e) {
			log.error("getFurBitLastGatewayReport : ",e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to process the report at this time.");
		}

		return response;
	}

	public List<JFurBitLastGatewayReport> convertJFurBitLastGatewayReport(List<FurbitLastGatewayReport> rptList){
		List<JFurBitLastGatewayReport> jRptList = new ArrayList<JFurBitLastGatewayReport>();
		try {
			for( FurbitLastGatewayReport fRpt : rptList) {			
				JFurBitLastGatewayReport lastgateway = new JFurBitLastGatewayReport(fRpt.getVersion(),fRpt.getDatetime(),fRpt.getDate(),
						fRpt.getTime(),fRpt.getTimezone(),fRpt.getLat(),fRpt.getLatdir(),fRpt.getLon(),fRpt.getLondir(),fRpt.getGpsstatus(),
						fRpt.getGpsinfo(),fRpt.getEventid1(),fRpt.getEventid2(),fRpt.getIostatus(),fRpt.getBattery(),fRpt.getRawrssi(),
						fRpt.getRssi(), fRpt.getGpsmode(),fRpt.getTxnMode(),fRpt.getLastpkt(),fRpt.getGateway().getId(),fRpt.getGateway().getMeid());

				jRptList.add(lastgateway);
			}
		}
		catch (Exception e) {
			log.error("convertJFurBitLastGatewayReport : ",e.getLocalizedMessage());
			jRptList = null;
		}
		return jRptList;
	}

	@RequestMapping(value = "v3.0/getleaderboard/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getLeaderboardDetails(@PathVariable String autho, @RequestParam("limit") int limit, @RequestParam("rpttype") String rpttype,@RequestParam("rptperiod") String rptperiod) {
		JResponse response = new JResponse();
		HashMap<Long, Long> usergaway = new HashMap<Long, Long>();
		try {
			try {
				User user = userService.verifyAuthKey(autho);
				if(user!= null) {
					List<Gateway> gateways = gatewayService.getGatewayByUser(null, null, null, null, user.getId(), null);

					for (Gateway gateway : gateways) {
						usergaway.put(gateway.getId(), gateway.getId());
					}
				}
			}catch (Exception e) {
				log.error("user authkey",e.getLocalizedMessage());
			}

			String startDate = "";
			String endDate = "";

			String date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT, IrisservicesConstants.UTCTIMEZONE,-1);

			//common for last 7/30 days
			endDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.ENDTIME);
			//			endDate = IrisservicesUtil.getDateime_Timezone(
			//					date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.ENDTIME), timezone,
			//					IrisservicesConstants.UTCTIMEZONE);	

			// Current day leader board data 
			if(rptperiod.equalsIgnoreCase("currentday")) {
				date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT, IrisservicesConstants.UTCTIMEZONE,0);

				startDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.STARTTIME);
				endDate = 	date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.ENDTIME);	

			}else if(rptperiod.equalsIgnoreCase("last7days")) {
				date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT, IrisservicesConstants.UTCTIMEZONE,-7);

				startDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.STARTTIME);

			}else if(rptperiod.equalsIgnoreCase("last30days")) {
				date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT, IrisservicesConstants.UTCTIMEZONE,-30);

				startDate =	date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.STARTTIME);
			}

			System.out.println("leaderboard: time : "+startDate+" : "+endDate);
			log.info("leaderboard: time : "+startDate+" : "+endDate);

			List<JLeaderBoard> rpt = iFurBitReportService.getLeaderBoardDetails(rpttype,startDate, endDate, limit,usergaway);

			if(rpt != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("rpt", rpt);
				response.put("rpttype", rpttype);
				response.put("rptperiod", rptperiod);
			}else {
				response.put("Status", 1);
				response.put("Msg", "No Report found");					
			}
		}
		catch (Exception e) {
			log.error("getFurBitLastGatewayReport : ",e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to process the report at this time.");
			response.put("error", e.getLocalizedMessage());
		}

		return response;
	}

	@RequestMapping(value = "v3.0/getuserleaderboard/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserLeaderboardDetails(@PathVariable String autho, @RequestParam("rptperiod") String rptperiod
			,@RequestParam("rpttype") String rpttype) {
		JResponse response = new JResponse();
		try {
			User user = null;
			try {
				user = userService.verifyAuthKey(autho);				
			}catch (Exception e) {
				log.error("user authkey",e.getLocalizedMessage());
			}

			String startDate = "";
			String endDate = "";

			String date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT, IrisservicesConstants.UTCTIMEZONE,-1);

			//common for last 7/30 days
			endDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.ENDTIME);

			// Current day leader board data 
			if(rptperiod.equalsIgnoreCase("currentday")) {
				date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT, IrisservicesConstants.UTCTIMEZONE,0);

				startDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.STARTTIME);
				endDate = 	date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.ENDTIME);	

			}else if(rptperiod.equalsIgnoreCase("last7days")) {
				date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT, IrisservicesConstants.UTCTIMEZONE,-7);

				startDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.STARTTIME);

			}else if(rptperiod.equalsIgnoreCase("last30days")) {
				date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT, IrisservicesConstants.UTCTIMEZONE,-30);

				startDate =	date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.STARTTIME);
			}

			System.out.println("leaderboard: time : "+startDate+" : "+endDate);
			log.info("leaderboard: time : "+startDate+" : "+endDate);

			List<JUserLeaderBoard> rpt = iFurBitReportService.getUserLeaderBoardDetails(user.getId(),startDate, endDate,rpttype);

			if(rpt != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("rpt", rpt);
				response.put("rptperiod", rptperiod);
			}else {
				response.put("Status", 1);
				response.put("Msg", "No Report found");					
			}
		}
		catch (Exception e) {
			log.error("getFurBitLastGatewayReport : ",e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to process the report at this time.");
			response.put("error", e.getLocalizedMessage());
		}

		return response;
	}	

	@RequestMapping(value = "v3.0/setdevicemode/{autho}" , method = RequestMethod.POST, headers="Accept=application/json")
	public JResponse setDeviceMode(@PathVariable String autho, @RequestParam("devicemode") String devicemode, 
			@RequestParam("gatewayId") long gatewayId) {
		log.info("Entered :: setPowerSaveMode :: "+autho);
		JResponse response = new JResponse();		

		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey",autho);
		}catch(InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid User");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : "+ex.getLocalizedMessage());
			return response;
		}

		String cmdValue = "";
		String mode = "";

		if(devicemode.equalsIgnoreCase("UPM")) {
			mode = "UPM";
			cmdValue = "%rthawki,,prptinterval=720#";
			log.info("Request received to set gateway : "+gatewayId +" to ultrapowersavemode");
		}else if(devicemode.equalsIgnoreCase("LPM")) {
			mode = "LPM";
			cmdValue = "%rthawki,,prptinterval=360#";
			log.info("Request received to set gateway : "+gatewayId +" to powersavemode");
		}else if(devicemode.equalsIgnoreCase("NPM")) {
			mode = "NPM";
			cmdValue = "%rthawki,,prptinterval=60#";
			log.info("Request received to set gateway : "+gatewayId +" to powersavemode");
		}
		try {			
			JGatewayDetails gateway = gatewayServiceV4.getJGatewayDetails("id",String.valueOf(gatewayId));

			if(gateway != null) {
				response = iFurBitReportService.insertPowerModeDynamicCmd(gateway, cmdValue, 1, "notsent",mode);				
			}else {
				response.put("Status", 0);
				response.put("Msg", "Gateway not found");
				log.info("Gateway not found for gateway id : "+gatewayId);
			}

		}catch(Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception occured ");
			response.put("Error", e.getLocalizedMessage());
			log.error("Gateway not found for gateway id : "+gatewayId);
		}
		return response ;
	}

//	@RequestMapping(value = "v3.0/getpowermodestatus/{autho}" , method = RequestMethod.GET, headers="Accept=application/json")
//	public JResponse getCurrentPowerMode(@PathVariable String autho, @RequestParam("gatewayId") long gatewayId) {
//		log.info("Entered :: getCurrentPowerMode : "+autho);
//		JResponse response = new JResponse();
//		try {
//			UserV4 user = optimizedService.verifyAuthV4("authkey",autho);
//		}catch(InvalidAuthoException ex) {
//			response.put("Status", 0);
//			response.put("Msg", "invalid authkey");
//			response.put("Error", ex.getLocalizedMessage());
//			log.error("Exception while getting user for auth : "+ex.getLocalizedMessage());
//			return response;
//		}
//		try {
//			response = iFurBitReportService.getDevicePowerMode(gatewayId+"");
//
//		}catch(Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "Exception occured");
//			response.put("Error", e.getLocalizedMessage());
//			log.error("Error while gatting device power save mode : "+e.getLocalizedMessage() );
//		}
//
//		return response;
//	}


	@RequestMapping(value = "v3.0/setadventuremode/{autho}" , method = RequestMethod.POST, headers="Accept=application/json")
	public JResponse setAdventureMode(@PathVariable String autho, @RequestParam("enable") String enable, 
			@RequestParam("gatewayId") long gatewayId) {
		log.info("Entered :: setAdventureMode :: "+autho);
		JResponse response = new JResponse();

		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey",autho);
		}catch(InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "Invalid User");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : "+ex.getLocalizedMessage());
			return response;
		}
		try {
			String cmdValue = "";
			JGatewayDetails gateway = gatewayServiceV4.getJGatewayDetails("id",gatewayId+"");
			if(gateway != null) {	
				if(enable.equalsIgnoreCase("on")) {				
					cmdValue = "%rthawki,,gpstrack=on#";
				}else if(enable.equalsIgnoreCase("off")) {
					cmdValue = "%rthawki,,gpstrack=off#";
				}

				response = iFurBitReportService.insertAdventureModeDynamicCmd(gateway, cmdValue, 1, "notsent",enable);

			}else {
				response.put("Status", 0);
				response.put("Msg", "Device not found");
				log.info("Gateway not found : "+gatewayId);
			}
		}catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception occured");
			log.error("Gateway not found : "+gatewayId);
		}
		return response;		
	}

	@RequestMapping(value = "v3.0/getdevicemodestatus/{autho}" , method = RequestMethod.GET, headers="Accept=application/json")
	public JResponse getDeviceModeStatus(@PathVariable String autho, @RequestParam(value="gatewayId", defaultValue = "", required = false) String gatewayId) {
		log.info("Entered :: getalldevicemodestatus : "+autho);
		JResponse response = new JResponse();
		ArrayList<DeviceModes> deviceModes = new ArrayList<DeviceModes>();
		UserV4 user =null;
		try {
			user = userServiceV4.verifyAuthV4("authkey",autho);
		}catch(InvalidAuthoException ex) {
			response.put("Status", 0);
			response.put("Msg", "invalid authkey");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception while getting user for auth : "+ex.getLocalizedMessage());
			return response;
		}

		try {
			List<JGateway> gatewaylist= new ArrayList<JGateway>();

			if(gatewayId.isEmpty())
				gatewaylist = gatewayService.getJGatewayByUser(user.getId(),"2,3"); // furbit monitortype
			else 
				gatewaylist.add(gatewayService.getJGateway("id",gatewayId));

//			ArrayList<Mode> allModes = iFurBitReportService.getAllDeviceModes();

			for(JGateway gateway: gatewaylist)			
			{
				Map<String,Object> resMap =new HashMap<String, Object>();
				DeviceModes devModeList =new DeviceModes();

//				JResponse response2 = iFurBitReportService.getDevicePowerMode(gateway.getId()+"");
//				resMap =response2.getResponse();

				ArrayList<Mode> Modes = getcloneAndwriteModes(resMap.get("current_power_mode"),allModes) ;
				devModeList.setPowerModes( Modes);

				devModeList.setCurMode(resMap.get("current_power_mode").toString());
				devModeList.setCurModeMsg(resMap.get("cur_modemsg").toString());

				Mode advantureMode =new Mode();
				advantureMode.setModename("Adventure Mode");
				advantureMode.setShortname("AM");
				advantureMode.setEnabled((boolean)resMap.get("adventuremode_flag"));
				advantureMode.setDescription1("");

				devModeList.setAdventureModes(advantureMode);

				AssetModel assetModel =null;
				try {
					assetModel = gatewayService.getAssetModel(gateway.getModelid());
				}catch (Exception e) {
				}
				if(assetModel != null) {
					if(assetModel.isAdvmode()==false )
						devModeList.setAdvNAmsg("This monitor does not have GPS location live tracking facility");
					if(assetModel.isPowermode()==false)
						devModeList.setPmNAmsg("This feature not applicable for this model");

					devModeList.setPowerModeAvailable(assetModel.isPowermode());
					devModeList.setAdventureModeAvailable(assetModel.isAdvmode());				
					devModeList.setBle(assetModel.isBle());
				}
				devModeList.setGatewayId(gateway.getId()+"");
				devModeList.setGatewayName(gateway.getName());

				deviceModes.add(devModeList);
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("devicemodes", deviceModes);

		}catch(Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception occured");
			response.put("Error", e.getLocalizedMessage());
			log.error("Error while getting device power save mode : "+e.getLocalizedMessage() );
		}

		return response;
	}

	private ArrayList<Mode> getcloneAndwriteModes(Object Currmode, ArrayList<Mode> allModes) {

		ArrayList<Mode> copy=new ArrayList<Mode>();

		for (Mode dev_mode : allModes) {
			if(dev_mode.getShortname().equalsIgnoreCase("AM"))
				continue;

			boolean isSame = Currmode.toString().equalsIgnoreCase(dev_mode.getShortname());

			Mode temp = new Mode();
			temp.setShortname(dev_mode.getShortname());
			temp.setModename(dev_mode.getModename());
			temp.setDescription1(dev_mode.getDescription1());
			temp.setDescription2(dev_mode.getDescription2());
			temp.setEnabled( isSame  );

			copy.add(temp);
		}
		return copy;

	}


//	@RequestMapping(value = "v3.0/getfurbitalertcfg/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse getFurbitAlertCfg(@PathVariable String autho, @RequestParam("assetid") String assetid ) {
//		JResponse response = new JResponse();
//		log.info("Entering getFurbitAlertCfg : "+ autho );
//		try {
//			UserV4 user;
//			try {
//				user  = userServiceV4.verifyAuthV4("authkey", autho);
//			}catch (InvalidAuthoException e) {
//				response.put("Status", 0);
//				response.put("Msg", "Invalid authentication key");
//				response.put("Error", e.getLocalizedMessage());
//				return response;
//			}
//
//			long userId = user.getId();
//			long asset_id = 0;
//			
//			if(!assetid.isEmpty()) {
//				asset_id = Long.parseLong(assetid);
//			}
//			
//			response = alertCfgService.getFurbitAlertCfg(userId, asset_id,response);
//
//		} catch (Exception e) {
//			log.error("Exception while get furbit alert cfg : " +e.getLocalizedMessage());
//			response.put("Status", 0);
//			response.put("Msg", "Exception while getting furbit alert cfg ");
//			response.put("Error", e.getLocalizedMessage());
//		}
//		return response;
//	}
//
//	@RequestMapping(value = "v3.0/updatefurbitalertcfg/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse updateFurbitAlertcfg(@PathVariable String autho, @RequestBody JFurbitAlertCfgRequest jfurbitAlertCfg){
//		log.info("Entering updateFurbitAlertcfg : "+autho);
//		JResponse response = new JResponse();
//
//		try {
//			UserV4 user = null;
//			try {
//				user = userServiceV4.verifyAuthV4("authkey",autho);
//			}catch (Exception e) {
//				log.error("verifyAuthKey : "+ e.getLocalizedMessage() );
//				response.put("Status", 0);
//				response.put("Msg", "Invalid Authkey");
//				response.put("Error", e.getLocalizedMessage());
//				return response;
//			}
//
//			if(user != null) {
//				
//				int result = 0;
//				String updatefor= jfurbitAlertCfg.getUpdatefor();
//				String[] assetCfgLst = jfurbitAlertCfg.getAlertcfgids().split(",");
//				
//				long userId = user.getId();
//				
//				FurbitAlertCfg furbitAlertCfg = alertCfgService.getFurbitAlertCfg(Long.parseLong(assetCfgLst[0]));				
//
//				boolean checkAsset = false;
//				if(furbitAlertCfg != null) {
//					Set<Asset> listassets = furbitAlertCfg.getAssets();
//					for (Asset ast : listassets ) {
//						if(ast.getId() == jfurbitAlertCfg.getAssetid())
//						{
//							checkAsset = true;
//							break;
//						}
//					}
//				}
//
//				if(checkAsset == false)
//				{
//					response.put("Status", 0);
//					response.put("Msg", "Asset configuration not matched");
//					return response;
//				}
//
//				if(!jfurbitAlertCfg.getAlerttypeids().contains(String.valueOf(furbitAlertCfg.getAlerttype().getId())))
//				{
//					response.put("Status", 0);
//					response.put("Msg", "Alert type not matched");
//					return response;
//				}
//
//				if(updatefor.equalsIgnoreCase("enabledisable")){
//					result = alertCfgService.enableOrDisableFurbitAlertcfg(jfurbitAlertCfg.getAlertcfgids(), jfurbitAlertCfg.isEnable());
//				}
//				else if(updatefor.equalsIgnoreCase("updateemailphone")){
//					CompanyConfigResponse  cmp_cfg = companyServicev4.getCompanyConfigAndCompany(user.getCmpId());
//
//					long mobile_count = cmp_cfg.getMobileNos();
//					long email_count = cmp_cfg.getEmailIds();
//
//					long entered_mobileNos = jfurbitAlertCfg.getMobilenos().split(",").length;
//					long entered_emailIds = jfurbitAlertCfg.getEmailids().split(",").length;
//
//					if ((mobile_count >= entered_mobileNos && email_count >= entered_emailIds) || cmp_cfg.getThrottsettings_id() == 5) {						
//						result = alertCfgService.updateEmailPhoneFurbitAlertCfg(jfurbitAlertCfg.getAlertcfgids(), jfurbitAlertCfg.getMobilenos(), jfurbitAlertCfg.getEmailids());
//												
//						if (result > 0) {
//							response.put("Status", 1);
//							response.put("Msg", "Success");
//						} else {
//							response.put("Status", 0);
//							response.put("Msg", "FurbitAlert cfgs not updated");
//						}
//					}else {
//						response.put("Status", 0);
//						response.put("Msg", "You are allowed to enter " + mobile_count + " mobile numbers and " + email_count+ " email address");
//					}
//				}
//				
//				else if(updatefor.equalsIgnoreCase("updatenotifyfreq")){
//					result = alertCfgService.updateFurbitNotify(jfurbitAlertCfg.getAlertcfgids(), jfurbitAlertCfg.getAlerttypeids(), jfurbitAlertCfg.getNotifyfreq());
//				}
//				else if(updatefor.equalsIgnoreCase("minmax")){
//					result = alertCfgService.updateFurbitAlertCfg(furbitAlertCfg,Long.parseLong(jfurbitAlertCfg.getAlerttypeids()),jfurbitAlertCfg.getMinval() , jfurbitAlertCfg.getMaxval(), jfurbitAlertCfg.getAssetid(),user.getCmpId());
//
//				}
//
//				if(result > 0) {
//					response.put("Status", 1);
//					response.put("Msg", "Success");
//				}
//				else {
//					response.put("Status", 0);
//					response.put("Msg", "Alert config updation failed");
//				}
//			}
//			else {
//				response.put("Status", 0);
//				response.put("Msg", "Invalid User");
//			}				
//		}catch (Exception e) {
//			log.error("Exception : updateAlertcfg : " + e.getLocalizedMessage());
//			response.put("Status", 0);
//			response.put("Msg", "Alert Config updation failed");
//			response.put("Error", e.getLocalizedMessage());
//		}
//		log.info("Exit updateAlertcfg");
//		return response;
//	}*/
}
