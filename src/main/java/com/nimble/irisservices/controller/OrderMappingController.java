package com.nimble.irisservices.controller;

import java.io.File;
import java.io.FileInputStream;
import java.text.SimpleDateFormat;
import java.util.*;

import com.nimble.irisservices.dto.Jorder;
import com.nimble.irisservices.entity.OrderSkuDetails;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;

import com.google.gson.Gson;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class OrderMappingController {

	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	@Lazy
	IChargebeeService cbService;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	Helper _helper;

	IrisservicesUtil irisUtil;

	@Value("${product_subs_enable}")
	private boolean product_subs_enable;
	
	@Value("${validation_authkey}")
	private String validation_authkey = "NA";
	
	@Value("${return_username}")
	private String return_username = "NA";

	private static final Logger log = LogManager.getLogger(OrderMappingController.class);

	// balaji
	@RequestMapping(value = "v4.0/ordermap/{auth}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse orderMap(@PathVariable String auth,
			@RequestParam(value = "orderid", required = true) String orderId,
			@RequestParam(value = "purchasedfrom", required = true) String orderChannel,
			@RequestParam(value = "gatewayid", required = true) long gatewayId,
			@RequestParam(value = "remark", required = false, defaultValue = "NA") String remark) {
		log.info("Entered into orderMap : authKey : " + auth);
		JResponse response = new JResponse();
		UserV4 user = null;
		String to_address = null;
		String cc_address = null;
		String bcc_address = null;
		String mailSub = "";
//		String contactMsg = "<br>Please contact us at <br><font color='blue'> <a href=\"tel: ************\"><b>************</b></a></font> or <font color='blue'><a href=\"mailto:<EMAIL>\"><b><EMAIL></b></a></font>";
		String mailContent = "<p>Hi Team,</p>" + "<p>Find the user order mapping details</p>";
		try {
			Properties prop = new Properties();
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			prop.load(new FileInputStream(file));

			to_address = prop.getProperty("to_address");
			cc_address = prop.getProperty("cc_address");
			bcc_address = prop.getProperty("bcc_address");

			try {
				user = userServiceV4.verifyAuthV3("authkey", auth);

			} catch (InvalidAuthoException e) {

				log.error("Invalid Authkey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "<center><p>User not found</p></center>");

				mailSub = "Failed : External Order Mapping Status-Email : ";
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ " <p> Error Msg : User not found </p>";
				return response;
			}
			mailContent = mailContent + "<p>Name : " + user.getFirstname() + " " + user.getLastname() + "</p>"
					+ "<p>Purchased from : " + orderChannel + " </p>" + "<p>Email : " + user.getEmail() + "  </p>"
					+ "<p>Phone : " + user.getMobileno() + "  </p>" + "<p>Order ID : " + orderId + "</p>";

			Gson gson = new Gson();
			Orders order = new Orders();
			boolean orderMappedtatus = false;
			String subscriptionStatus = "0";

			if (orderChannel.equalsIgnoreCase("rv")) {
				subscriptionStatus = "1";
			} else if (orderChannel.equalsIgnoreCase("others")) {
				response.put("Status", 0);
				response.put("Msg",
						"<center><p>" + RegisterUserError.contactMsg + " to claim your warranty</p></center>");

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p>Remark : " + remark + "  </p>" + "<p>Order Mapping Status : Failed </p>"
						+ " <p> Error Msg : Order purchased from other source : " + remark + "</p>";
				return response;
			}

			Gateway gateway = gatewayService.getGatewayByid(gatewayId);

			if (gateway == null) {
				log.error("Gateway not found :: gateway id : " + gatewayId);
				response.put("Status", 0);
				response.put("Msg", "<center><p>Unable to fetch your monitor details. " + RegisterUserError.contactMsg
						+ "</p></center>");

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ "<p> Error Msg : Gateway details not found </p>";
				return response;
			}

			mailContent = mailContent + "<p> Meid : " + gateway.getMeid() + "</p>";
			Inventory inventory = niomDbservice.getInventoryByMeid(gateway.getMeid());

			if (inventory == null) {
				log.error("Meid not found in inventory :: meid : " + gateway.getMeid());
				response.put("Status", 0);
				response.put("Msg", "<center><p>Unable to fetch your monitor details. " + RegisterUserError.contactMsg
						+ ".</p></center>");

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ "<p> Error Msg : Inventory not found </p>";
				return response;
			}

			mailContent = mailContent + "<p>QRC : " + inventory.getQrc() + " </p>";

			JSONObject jorderIdCheckResponse = new JSONObject();
			jorderIdCheckResponse = userService.getNiomGetOrderCount(orderChannel, orderId);

			int orderIdCheckStatus = jorderIdCheckResponse.getInt("Status");

			if (orderIdCheckStatus > 0) {
				order = gson.fromJson(jorderIdCheckResponse.getJSONObject("Order").toString(), Orders.class);
				int totalOrderCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalordered"));
				int totalMappedCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalmapped"));

				if (totalMappedCount >= totalOrderCount) {
					response.put("Status", 0);
					response.put("Msg",
							"<center><p>Invalid Order ID " + RegisterUserError.contactMsg + ".</p></center>");

					mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
					mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
							+ "<p> Error Msg : Order Id is already registered </p>";
					return response;
				}

				log.info("Order id found in niom. Order Channel : " + order.getOrder_acc_type().getAcc_type()
						+ "Order ID : " + order.getOrder_id() + "Out Order ID  :" + order.getExternal_order_id());

				if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("amazon")) {
					if (order.getBilling_email().toLowerCase().contains("na")
							|| order.getBilling_phone().toLowerCase().contains("na")) {
						order.setBilling_email(user.getEmail());
						order.setBilling_phone(user.getMobileno());
						order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
						order.setWelcome_status(order.getWelcome_status());
						boolean updateExternalOrderData = niomDbservice.updateExternalOrdersInNiom(order);
					}
				} else if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("walmart")) {
					if (order.getBilling_email().toLowerCase().contains("na")
							|| order.getBilling_phone().toLowerCase().contains("na")) {
						order.setBilling_email(user.getEmail());
						order.setBilling_phone(user.getMobileno());
						order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
						order.setWelcome_status(order.getWelcome_status());
						boolean updateExternalOrderData = niomDbservice.updateExternalOrdersInNiom(order);
					}
				}

				orderMappedtatus = userService.orderMapping(inventory.getMeid(), _helper.getCurrentTimeinUTC(),
						inventory.getDevicestate().getId(), order.getOrder_id() + "", order.getDevicemodel(),
						subscriptionStatus, "1", "manual", gatewayId);

				if (orderMappedtatus == false) {
					response.put("Status", 0);
					response.put("Msg", "<center><p>Unable to find your Order ID " + RegisterUserError.contactMsg
							+ ". </p></center>");

					mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
					mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
							+ "<p> Error Msg : Unable to map order </p>";
					return response;
				}else {
					// Update order details in device subscription table
					async.updateSalesChannel(user.getId()+"", order.getOrder_acc_type().getAcc_type(), gatewayId, inventory.getMeid(), 
							order.getOrder_id()+"" ,gateway.getModel().getMonitor_type().getId());
				}

			} else {
				response.put("Status", 0);
				response.put("Msg",
						"<center><p>Unable to find your Order ID " + RegisterUserError.contactMsg + ".</p></center>");

				mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Failed </p>"
						+ "<p> Error Msg : Order Id not found </p>";

				log.info("Order id not found in niom, Error Code : ER039 :" + RegisterUserError.ER039);

				return response;
			}

			niomDbservice.updateOrdermapUserDetails(order.getOrder_id(), user.getUsername(), user.getId() + "", "NA");

			boolean updateExternalOrderDataStatus = userService.updateOrdersDataV2(orderChannel, order, inventory);

			if (orderMappedtatus) {

				boolean isUpdateOrderMappingDetails = userServiceV4.updateOrderMappingDetails(user.getId(),
						inventory.getQrc(), orderId, orderChannel, "6", order.getOrder_sku(), order.getDatetime());

				gatewayService.changeGatewayOrderidStatus(gateway.getId(), false);
				int remainingdays = calculateWarrantyDays(order.getDatetime());
				String msg = "You have " + remainingdays + " days of warranty left";

				if (remainingdays <= 0) {
					msg = "Your warranty has expired";
				}

				response.put("Status", 1);
				response.put("Msg", msg);

				mailSub = "Success : External Order Mapping Status-Email : " + user.getEmail();
				mailContent = mailContent + "<p> Order Mapping Status : Success </p>";

				// Activate CB subscription
//				String cb_planid = crService.getCBPlan(String.valueOf(order.getOrder_id()));
//				if(!cb_planid.equalsIgnoreCase("NA") && !cb_planid.isEmpty() && product_subs_enable) {
//					boolean stat = cbService.createPaidSubscription(user.getChargebeeid(), cb_planid);
//					
//					if(stat) {
//						stat = crService.updateSubStatus(String.valueOf(order.getOrder_id()));
//						
//					}
//				}
			}
		} catch (Exception e) {
			log.error("Error occured in ordermap :: auth : " + auth);
			response.put("Status", 0);
			response.put("Msg", "<center><p>Error! " + RegisterUserError.contactMsg + ".</p></center>");

			mailSub = "Failed : External Order Mapping Status-Email : " + user.getEmail();
			mailContent = mailContent + "<p> Order Mapping Status : Failed </p>";
			mailContent = mailContent + "<p> Error Msg : " + e.getLocalizedMessage() + " </p>";
		} finally {
			mailContent = mailContent + "<br>Thanks,<br> Irisservice ";
			async.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);
		}
		return response;
	}

	private int calculateWarrantyDays(String orderDate) {
		String currentDate = irisUtil.getCurrentTimeUTC();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			Date orderdate = sdf.parse(orderDate);
			Date currentdate = sdf.parse(currentDate);

			long difference = currentdate.getTime() - orderdate.getTime();
			int daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
			daysBetween = 365 - daysBetween;
			return daysBetween;
		} catch (Exception e) {
			return 0;
		}
	}

//	@RequestMapping(value = "v4.0/updatereturndevices", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse updateReturnDevices(@RequestParam("order_id") long order_id,
//			@RequestParam("isChecked") Boolean isChecked, @RequestParam("meid") String meid, 
//			@RequestParam("user_id") long userid, @RequestHeader HttpHeaders header) {
//		JResponse response = new JResponse();
//		boolean updatedCmpInGateway = false;
//		boolean isAssignedToLogin=false;
//		boolean updateOrderMap=false;
//		try {
//			String backing = header.getFirst("backing");
//			JBacking jBacking = _helper.backingKeyValidationNiom(backing);
//
//			if (jBacking.getStatus() <= 0) {
//				response.put("Status", jBacking.getStatus());
//				response.put("Msg", jBacking.getMsg());
//				response.put("Return Time", System.currentTimeMillis());
//				return response;
//			}
//
//			if (!validation_authkey.equals(jBacking.getAuthKey())) {
//				response.put("Status", -2);
//				response.put("Msg", "Invalid Authkey");
//				response.put("Return Time", System.currentTimeMillis());
//				return response;
//			}
//			
//			log.info("Entered Main::updateReturnDevices");
//			Orders getOrder = niomDbservice.getOrderDetails(order_id);
//			
//			if(isChecked) {
//
//				boolean isUpdatedOrderTable = niomDbservice.updateOrdersTable(order_id, getOrder.getQuantity(),
//						getOrder.getReturn_units());
//				
//				if(isUpdatedOrderTable) {
//					
//					Gateway gateway = gatewayService.getGatewayDetails(meid);
//					
//					if(gateway != null) {
//
//						String gatewayId = String.valueOf(gateway.getId());
//
//						boolean removeUsergateway = niomDbservice.deleteUserGateway(userid, gateway.getId());
//
//						String cmp_id = companyService.getCompanyId(return_username);
//
//						updatedCmpInGateway = companyService.updateCmpIdInGateway(cmp_id, meid);
//
//						isAssignedToLogin = niomDbservice.assignGatewayToLogin(gatewayId, userid);
//
//						updateOrderMap = niomDbservice.updateOrderMap(order_id, meid, isChecked);
//
//						if (updateOrderMap) {
//							response.put("Status", 1);
//							response.put("Msg", "Successfuly Updated");
//							response.put("Return Time", System.currentTimeMillis());
//							log.info("Successfuly updated return");
//						} else {
//							response.put("Status", 0);
//							response.put("Msg", "Return Device not Updated in table");
//							response.put("Return Time", System.currentTimeMillis());
//						}
//					} else {
//						niomDbservice.updateOrdersTable(order_id, getOrder.getQuantity(),
//								getOrder.getReturn_units() - 1);
//						
//						response.put("Status", 0);
//						response.put("Msg", "No Gateway Found");
//						response.put("Return Time", System.currentTimeMillis());
//					}	
//				} else {
//					response.put("Status", 0);
//					response.put("Msg", "All Units have been already returned!!");
//					response.put("Return Time", System.currentTimeMillis());
//				}
//				
//			}		
//
//		} catch (Exception e) {
//			log.error("Exception occured while updateReturnDevices - " + e.getLocalizedMessage());
//			response.put("Status", 0);
//			response.put("Msg", "Exception occured : " + e.getLocalizedMessage());
//			response.put("Return Time", System.currentTimeMillis());
//		}
//		return response;
//	}

	@GetMapping(value = "v4.0/getorderdetails", headers = "Accept=application/json")
	@ResponseBody
	public JResponse getOrderDetails(
			@RequestParam(value = "order_id", defaultValue = "NA", required = false) String order_id,
			@RequestParam String channel) {
		log.info("Entered getOrderDetails :");
		JResponse response = new JResponse();
		try {
			Orders order =  niomDbservice.getOrderByOrderId(order_id, channel);
			if (order != null) {
				String[] order_skus = order.getOrder_sku().split("\\.");
				List<OrderSkuDetails> orderSkuList = niomDbservice.getSkuDetails();
				if (orderSkuList != null) {
					Map<String, String> skuMap = new HashMap<>();
					for (String orderSku : order_skus) {
						Optional<String> productType = orderSkuList.stream()
								.filter(o -> o.getSku().equalsIgnoreCase(orderSku))
								.map(OrderSkuDetails::getProduct_type).findFirst();
						productType.ifPresent(s -> skuMap.put(orderSku, s));
					}
					order.setOrderSkuList(skuMap);
				}

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("order", order);
			} else {
				response.put("Status", 1);
				response.put("Msg", "No order found");
				response.put("order", order);
			}
		} catch (Exception e) {
			log.error("Error in getOrderDetails : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error getting order");
			response.put("order", null);
		}
		return response;
	}
}

