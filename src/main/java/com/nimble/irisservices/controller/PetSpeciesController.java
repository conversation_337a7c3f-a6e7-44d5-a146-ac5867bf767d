package com.nimble.irisservices.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.PetBreeds;
import com.nimble.irisservices.entity.PetSpecies;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IPetBreedServices;
import com.nimble.irisservices.service.IPetSpeciesServices;
import com.nimble.irisservices.service.IUserService;

@Controller
public class PetSpeciesController {

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IPetSpeciesServices ipetSpeciesServices;

	@Autowired
	@Lazy
	IPetBreedServices iPetBreedServices;

	private static final Logger log = LogManager.getLogger(PetSpeciesController.class);

	// Update or Save Pet Species
	@RequestMapping(value = "v3.0/petspecies/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveOrUpdateSpecies(@PathVariable String autho, @RequestBody PetSpecies petSpecies) {
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);

			if (user.getRole().getId() == 1) {

				boolean isSuccess = false;

				isSuccess = ipetSpeciesServices.saveORupdateSpecies(petSpecies);

				if (isSuccess) {
					response.put("Status", 1);
					log.info("Pet Species inserted/updated successfully. ");
					response.put("Msg", "Pet Species inserted/updated successfully.");
				} else {
					response.put("Status", 0);
					log.info("Pet Species not inserted/updated successfully.");
					response.put("Msg", "Pet Species not inserted/updated successfully.");
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "User Does not have permission to create company Accounts");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error("Excepitoin while creating/updating Pet Species details.");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while creating/updating Pet Species details.");
			return response;
		}
		return response;
	}

	// Update or Save Pet Species
	@RequestMapping(value = "v3.0/petbreeds/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveOrUpdateBreeds(@PathVariable String autho, @RequestBody PetBreeds petBreeds) {
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);

			if (user.getRole().getId() == 1) {

				boolean isSuccess = false;

				PetSpecies specie = ipetSpeciesServices.getPetSpecies(petBreeds.getSpeciesId());

				if (specie == null) {
					specie = ipetSpeciesServices.getPetSpecies(1L);
				}
				petBreeds.setPetSpecies(specie);
				isSuccess = iPetBreedServices.saveORupdateBreed(petBreeds);

				if (isSuccess) {
					response.put("Status", 1);
					log.info("Pet Breeds inserted/updated successfully. ");
					response.put("Msg", "Pet Breeds inserted/updated successfully.");
				} else {
					response.put("Status", 0);
					log.info("Pet Breeds not inserted/updated successfully.");
					response.put("Msg", "Pet Breeds not inserted/updated successfully.");
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "User Does not have permission to create company Accounts");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error("Excepitoin while creating/updating Pet Breeds details.");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while creating/updating Pet Breeds details.");
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v3.0/getspecies", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getSpecies() {
		JResponse response = new JResponse();
		try {

			List<PetSpecies> petSpecies = new ArrayList<PetSpecies>();

			petSpecies = ipetSpeciesServices.getPetSpecies();

			if (petSpecies != null) {
				if (petSpecies.size() > 0) {

					List<String> species = new ArrayList<String>();

					for (PetSpecies specie : petSpecies) {
						species.add(specie.getSpeciesName());
					}

					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("petSpecies", species);
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "No Species found");
					return response;
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "No Species found");
				return response;
			}
		} catch (Exception e) {
			log.error("Excepitoin while getting pet species.");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting pet species.");
			return response;
		}
	}

	@RequestMapping(value = "v3.0/getbreeds", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getBreeds(
			@RequestParam(value = "speciename", defaultValue = "", required = false) String speciename) {
		JResponse response = new JResponse();
		try {

			List<PetBreeds> petBreeds = new ArrayList<PetBreeds>();

			if (speciename.isEmpty()) {
				petBreeds = iPetBreedServices.getAllPetBreeds();
			} else {
				PetSpecies specie = ipetSpeciesServices.getPetSpeciesByName(speciename);

				if (specie == null) {
					specie = ipetSpeciesServices.getPetSpecies(1L);
				}

				petBreeds = iPetBreedServices.getPetBreeds((int) specie.getId());
			}
			if (petBreeds != null) {
				if (petBreeds.size() > 0) {
					if (speciename.isEmpty()) {
						response.put("petBreeds", petBreeds);
					} else {
						List<String> breedName = new ArrayList<String>();

						for (PetBreeds breed : petBreeds) {
							breedName.add(breed.getBreedName());
						}

						Collections.sort(breedName);

						response.put("petBreeds", breedName);
						response.put("Msg", "Success");

					}

					response.put("Status", 1);

					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "No pet breeds found");
					return response;
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "No pet breeds found");
				return response;
			}
		} catch (Exception e) {
			log.error("Excepitoin while getting pet breeds.");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting pet breeds.");
			return response;
		}
	}

}
