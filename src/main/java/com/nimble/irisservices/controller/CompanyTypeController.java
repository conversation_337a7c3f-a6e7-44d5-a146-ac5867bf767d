 package com.nimble.irisservices.controller;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.CompanyType;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.ICompanyTypeService;
import com.nimble.irisservices.service.IUserService;

@Controller
public class CompanyTypeController {

	@Autowired
	@Lazy
	IUserService userService;
	
	@Autowired
	@Lazy
	ICompanyTypeService companytypeservice;
	
	private static final Logger log = LogManager.getLogger(CompanyTypeController.class);

	// Used in web
	//========get companyType================
	@RequestMapping(value="v3.0/companytype/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getCompanyType(@PathVariable String autho, @RequestParam("cmptypeid") String cmptypeid)
	{
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			List<CompanyType> companytypes = companytypeservice.getCompanyType(cmptypeid,user.getRole().getId());
			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("companytype", companytypes);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}

		return response;

	}

}
