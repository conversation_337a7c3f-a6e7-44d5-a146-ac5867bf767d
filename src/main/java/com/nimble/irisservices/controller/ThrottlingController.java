package com.nimble.irisservices.controller;

import java.util.List;

import javax.validation.Valid;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dao.IThrottlingDao;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IUserService;

@Controller
public class ThrottlingController {
	
	private static final Logger log = LogManager.getLogger(ThrottlingController.class);

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IThrottlingDao throttlingdao;

	@Autowired
	@Lazy
	IGatewayService gatewayService;
	
	@RequestMapping(value="v3.0/throttlingsettings/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getThrotSettingById(@PathVariable String autho, @RequestParam("throttleid") String throttleid)
	{
		JResponse response = new JResponse();

		try {
			User user = userService.verifyAuthKey(autho);
			List<ThrottlingSettings> throtsettings = throttlingdao.getThrotSettings(throttleid);
			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("throtsettings", throtsettings);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg","invalid throttleid");
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v3.0/throttlingsettings/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse updateThrottlingSettings(@PathVariable String autho,
			@ModelAttribute  @Valid ThrottlingSettings throtsettings,  BindingResult result)     
	{
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);
			throttlingdao.saveOrUpdateThrotSetting(throtsettings);
			if(throtsettings.getId() != 0) {
				gatewayService.updateGatewayCreditPoints(throtsettings);
			}
			response.put("Status", 1);
			response.put("Msg","success");

		} catch (InvalidAuthoException e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 1);
			response.put("Msg","success");

		}
		catch (DataIntegrityViolationException e) {
			log.error("updateThrottlingSettings:"+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg","name already exits");
		}
		catch (Exception e) {
			log.error("updateThrottlingSettings:::"+e.getMessage());
			response.put("Status", 0);
			response.put("Msg","UnExcepted Error in throttling settings");
		}
		return response; 
	}
}
