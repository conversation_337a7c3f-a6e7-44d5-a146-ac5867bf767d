package com.nimble.irisservices.controller;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.ExternalConfig;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IUserService;

@Controller
public class ExternalConfigController {

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	private static final Logger log = LogManager.getLogger(ExternalConfigController.class);

	@RequestMapping(value = "v3.0/externalConfig/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveOrUpdate(@PathVariable String autho, @RequestBody ExternalConfig configuration) {
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);

			if (user.getRole().getId() == 1) {

				ExternalConfig _configuration = new ExternalConfig();

				if (configuration.getParametername().isEmpty() || configuration.getParametername() == null
						|| configuration.getParametername() == "") {
					log.error("External Config parameter value is empty.");
					response.put("Status", 0);
					response.put("Msg", "Parameter name should not be empty.");
					return response;

				}
				ExternalConfig config = externalConfigService
						.getExternalConfig(configuration.getParametername().toLowerCase());

				boolean isSuccess = false;

				if (config != null) {
					ExternalConfig updateConfig = new ExternalConfig(config.getId(),
							configuration.getParametername().toLowerCase(), configuration.getValue());
					isSuccess = externalConfigService.saveOrUpdateExternalConfig(updateConfig);

				} else {
					ExternalConfig saveConfig = new ExternalConfig(configuration.getParametername().toLowerCase(),
							configuration.getValue());
					isSuccess = externalConfigService.saveOrUpdateExternalConfig(saveConfig);
				}

				if (isSuccess) {
					response.put("Status", 1);
					log.info("External configuration updated successfully. " + configuration.getParametername());
					response.put("Msg", "External Configuration updated successfully.");
				} else {
					response.put("Status", 0);
					log.info("External configuration not updated successfully. " + configuration.getParametername());
					response.put("Msg", "External Configuration not updated successfully.");
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "User Does not have permission to create company Accounts");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error("Excepitoin while creating/updating external configuration details");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while creating/updating external configuration details");
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v3.0/getallexternalconfig", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllExternalConfig() {
		JResponse response = new JResponse();
		try {

			List<ExternalConfig> externalConfig = new ArrayList<ExternalConfig>();

			externalConfig = externalConfigService.getAllExternalConfig();
			if (externalConfig.size() > 1) {
				response.put("Status", 1);
				response.put("externalconfig", externalConfig);
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No external configuration found");
				return response;
			}
		} catch (Exception e) {
			log.error("Excepitoin while getting external configuration details.");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting external configuration details.");
			return response;
		}
	}
	

	@RequestMapping(value = "v3.0/getexternalconfig/{parametername}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getExternalConfig(@PathVariable String parametername) {
		JResponse response = new JResponse();
		try {

			ExternalConfig externalConfig = new ExternalConfig();

			externalConfig = externalConfigService.getExternalConfig(parametername);
			if (externalConfig!=null) {
				response.put("Status", 1);
				response.put("ReturnValue", externalConfig.getValue());
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No external configuration found");
				return response;
			}
		} catch (Exception e) {
			log.error("Excepitoin while getting external configuration details.");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while getting external configuration details.");
			return response;
		}
	}

	@RequestMapping(value = "v3.0/deleteexternalconfig/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse deleteexternalConfig(@PathVariable String autho, @RequestParam("name") String name) {
		JResponse response = new JResponse();
		try {

			if (name == null || name == "" || name.isEmpty()) {
				response.put("Status", 0);
				response.put("Msg", "Name should not be empty configuration found");
				return response;
			}

			List<ExternalConfig> externalConfig = new ArrayList<ExternalConfig>();

			ExternalConfig config = externalConfigService.getExternalConfig(name.toLowerCase());

			boolean isSuccess = false;

			if (config != null) {

				isSuccess = externalConfigService.deleteExternalConfig(name.toLowerCase());

			} else {
				response.put("Status", 0);
				response.put("Msg", "No external configuration found to delete.");
				return response;
			}

			if (isSuccess) {
				response.put("Status", 1);
				log.info("External configuration deleted successfully. " + name);
				response.put("Msg", "External Configuration deleted successfully.");
			} else {
				response.put("Status", 0);
				log.info("External configuration not deleted successfully. " + name);
				response.put("Msg", "External Configuration not deleted successfully.");
			}
			
			return response;

		} catch (Exception e) {
			log.error("Excepitoin while deleting external configuration");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while deleting external configuration");
			return response;
		}
	}

}
