package com.nimble.irisservices.controller;

import org.springframework.stereotype.Controller;

@Controller
public class FurBitControllerV4 {
/*
	private static final Logger log = LogManager.getLogger(FurBitControllerV4.class);

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	IFurBitReportServiceV4 iFurBitReportServiceV4;

	@Autowired
	IFurBitReportService iFurBitReportService;

	@Autowired
	IGatewayService gatewayService;

	@Autowired
	IGatewayServiceV4 gatewayServiceV4;

	// getFurBitDailyReportv4 by- Anand
	@RequestMapping(value = "v4.0/furbitdailyreport/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFurBitDailyReportv4(@PathVariable String autho, @RequestParam("date") String date,
			@RequestParam("gatewayId") String gatewayId, @RequestParam("hour") String hour,
			@RequestParam("timezone") String timezone,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();

		timezone = timezone.replaceAll("\\s+","");
		if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
			timezone = "+" + timezone;
		//System.out.println(timezone);
		try {
			//System.out.println("Entered :: FurBitController::::getPetFitDailyReportV4::[{}]" + date);
			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuthV2(autho);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			long userId = Long.valueOf(map.get("user_id"));

			// monitor type by default 2 for furbit
			String monitortype = "2,3";
			// hour =24 - daily summary for all devices of a particular user
			if (hour.equalsIgnoreCase("24")) {
				List<Gateway> userGateway = gatewayService.getGatewayByMonitorType(monitortype, userId);

				//System.out.println("userGateway Size" + userGateway.size());
				List<JFurBitReportV1> furBitDailySummaryReport = new ArrayList<JFurBitReportV1>();
				List<JFurBitReportBest> furBitDailyBest = new ArrayList<JFurBitReportBest>();
				List<JFurBitReportAvg> furBitDailyAverage = new ArrayList<JFurBitReportAvg>();
				List<JFurBitReportTotal> furBitDailytotal = new ArrayList<JFurBitReportTotal>();

				if (userGateway != null) {
					if (userGateway.size() > 0) {

						for (Gateway gateway : userGateway) {

//							System.out.println("Gateway ID : " + gateway.getId() + "Report Date : " + date
//									+ " , Time ZOne" + timezone);

							JFurBitReportReportSummary furBitDailytotalReport = iFurBitReportServiceV4
									.getFurBitDailyReport(date, userId, gateway.getId() + "", hour, timezone,
											gateway.getName(), gateway);

							List<JFurBitReport> furBitDailyReport = furBitDailytotalReport.getjFurBitDailyRptLis();
							List<JFurBitReportBest> furBitDailyBestLis = furBitDailytotalReport.getFurBitDailyBestLis();
							List<JFurBitReportAvg> furBitDailyAverageLis = furBitDailytotalReport
									.getFurBitDailyAverageLis();
							List<JFurBitReportTotal> furBitDailytotalLis = furBitDailytotalReport.getFurBitDailyTotal();

							furBitDailyBest.addAll(furBitDailyBestLis);
							furBitDailyAverage.addAll(furBitDailyAverageLis);
							furBitDailytotal.addAll(furBitDailytotalLis);

							if (furBitDailyReport.size() > 0) {
								for (JFurBitReport jFurBit : furBitDailyReport) {
									AddDailySummaryReport(furBitDailySummaryReport, jFurBit);
								}

							} else {

								JFurBitReport defaultFurBitReport = new JFurBitReport();
								defaultFurBitReport.setFromDatetime("1753-01-01 00:00:00");
								defaultFurBitReport.setToDatetime("1753-01-01 00:00:00");
								defaultFurBitReport.setTotalIdleSecs(0);
								defaultFurBitReport.setTotalRunSecs(0);
								defaultFurBitReport.setTotalStepCount(0);
								defaultFurBitReport.setTotalWalkSecs(0);
								defaultFurBitReport.setTotalActiveSecs(0);
								defaultFurBitReport.setGatewayId(gateway.getId());
								defaultFurBitReport.setGatewayName(gateway.getName());
								defaultFurBitReport.setDefaultStepGoal(gateway.getDefault_goal());
								defaultFurBitReport.setGoalAchievedPercentage("0");

								AddDailySummaryReport(furBitDailySummaryReport, defaultFurBitReport);

								JFurBitReportBest jfurbest = new JFurBitReportBest();

								jfurbest.setBestIdleSecs(0);
								jfurbest.setBestRunSecs(0);
								jfurbest.setBeststepcount(0);
								jfurbest.setBestWalkSecs(0);
								jfurbest.setBestActiveSecs(0);

								jfurbest.setGatewayId(gateway.getId());
								jfurbest.setGatewayName(gateway.getName());

								jfurbest.setBestIdlefromDatetime("1753-01-01 00:00:00");
								jfurbest.setBestIdletoDatetime("1753-01-01 00:00:00");

								jfurbest.setBestRunfromDatetime("1753-01-01 00:00:00");
								jfurbest.setBestRuntoDatetime("1753-01-01 00:00:00");

								jfurbest.setBeststepfromDatetime("1753-01-01 00:00:00");
								jfurbest.setBeststeptoDatetime("1753-01-01 00:00:00");

								jfurbest.setBestWalkfromDatetime("1753-01-01 00:00:00");
								jfurbest.setBestWalktoDatetime("1753-01-01 00:00:00");

								jfurbest.setBestActivefromDatetime("1753-01-01 00:00:00");
								jfurbest.setBestActivetoDatetime("1753-01-01 00:00:00");

								JFurBitReportAvg jfurAvg = new JFurBitReportAvg();
								jfurAvg.setAvgIdleSecs(0);
								jfurAvg.setAvgRunSecs(0);
								jfurAvg.setAvgstepcount(0);
								jfurAvg.setAvgWalkSecs(0);
								jfurAvg.setAvgActivesec(0);

								jfurAvg.setTotalpacket(0);

								jfurAvg.setGatewayId(gateway.getId());
								jfurAvg.setGatewayName(gateway.getName());

								furBitDailyBest.add(jfurbest);
								furBitDailyAverage.add(jfurAvg);

							}

						}

						response.put("furBitDailySummaryReport", furBitDailySummaryReport);

						response.put("furBitDailyBestReport", furBitDailyBest);

						response.put("furBitDailyAverageReport", furBitDailyAverage);

						response.put("furBitDailyTotalReport", furBitDailytotal);

					} else {
						response.put("furBitDailySummaryReport", furBitDailySummaryReport);

						response.put("furBitDailyBestReport", furBitDailyBest);

						response.put("furBitDailyAverageReport", furBitDailyAverage);

						response.put("furBitDailyTotalReport", furBitDailytotal);
					}
				}

			} else {
				// defined hour interval report for a particular device
				Gateway gateway = gatewayService.getGateway(Long.valueOf(gatewayId));
				String gatewayName = "";

				if (gateway != null) {
					gatewayName = gateway.getName();
				}

				JFurBitReportReportSummary furBitDailyReport = iFurBitReportServiceV4.getFurBitDailyReport(date, userId,
						gatewayId, hour, timezone, gatewayName, gateway);
				response.put("furBitDailySummaryReport", furBitDailyReport.getjFurBitDailyRptLis());

				response.put("furBitDailyBestReport", furBitDailyReport.getFurBitDailyBestLis());

				response.put("furBitDailyAverageReport", furBitDailyReport.getFurBitDailyAverageLis());

				response.put("furBitDailyTotalReport", furBitDailyReport.getFurBitDailyTotal());

			}

			response.put("Status", 1);
			response.put("Msg", "Success");

			//System.out.println("Exit :: FurBitController::::getFurBitDailyReport::[{}]" + date);
		} catch (Exception e) {
			log.error("FurBitController:::::getFurBitDailyReport [{}] " + e);
			response.put("Status", 0);
			response.put("Msg", "Unexpected error . ");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	private void AddDailySummaryReport(List<JFurBitReportV1> furBitDailySummaryReport, JFurBitReport jFurBit) {

		JFurBitReportV1 FurBitReportV1 = new JFurBitReportV1();
		FurBitReportV1.setFromDatetime(jFurBit.getFromDatetime());
		FurBitReportV1.setToDatetime(jFurBit.getToDatetime());
		FurBitReportV1.setTotalIdleSecs(jFurBit.getTotalIdleSecs());
		FurBitReportV1.setTotalRunSecs(jFurBit.getTotalRunSecs());
		FurBitReportV1.setTotalStepCount(jFurBit.getTotalStepCount());
		FurBitReportV1.setTotalWalkSecs(jFurBit.getTotalWalkSecs());
		FurBitReportV1.setTotalActiveSecs(jFurBit.getTotalActiveSecs());
		FurBitReportV1.setGatewayId(jFurBit.getGatewayId());
		FurBitReportV1.setGatewayName(jFurBit.getGatewayName());
		FurBitReportV1.setDefaultStepGoal(jFurBit.getDefaultStepGoal());
		FurBitReportV1.setGoalAchievedPercentage(jFurBit.getGoalAchievedPercentage());
		FurBitReportV1.setCaloriesburnt(jFurBit.getCaloriesburnt());
		List<FurbitLastGatewayReport> getwayreport = iFurBitReportService
				.getFurbitLastGatewayReport(jFurBit.getGatewayId() + "");

		if (getwayreport != null && !getwayreport.isEmpty()) {
			FurBitReportV1.setBatterylife(getwayreport.get(0).getBattery() + "");
		}

		furBitDailySummaryReport.add(FurBitReportV1);

	}

	// getFurBitReportv4 by -Anand
	@RequestMapping(value = "v4.0/furbitreport/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFurBitReportv4(@PathVariable String autho, @RequestParam("date") String date,
			@RequestParam("gatewayId") String gatewayId, @RequestParam("timezone") String timezone,
			@RequestParam("days") String days, @RequestParam("reportDays") String reportDays,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();

		timezone = timezone.replaceAll("\\s+","");
		if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
			timezone = "+" + timezone;
		//System.out.println(timezone);

		try {
			//System.out.println("Entered :: FurBitController::::getPetFitReport::[{}]" + date);
			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuthV2(autho);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			long userId = Long.valueOf(map.get("user_id"));

			Gateway gateway = gatewayService.getGateway(Long.valueOf(gatewayId));
			String gatewayName = "";

			if (gateway != null) {
				gatewayName = gateway.getName();
			}

			JFurBitReportReportSummary furBitDailyReport = iFurBitReportServiceV4.getFurBitReport(date, userId,
					gatewayId, timezone, days, reportDays, gatewayName, gateway);
			response.put("Status", 1);

			response.put("Msg", "Success");

			response.put("furBitDailySummaryReport", furBitDailyReport.getjFurBitDailyRptLis());

			response.put("furBitDailyBestReport", furBitDailyReport.getFurBitDailyBestLis());

			response.put("furBitDailyAverageReport", furBitDailyReport.getFurBitDailyAverageLis());

			response.put("furBitDailyTotalReport", furBitDailyReport.getFurBitDailyTotal());

			log.info("Exit :: FurBitController::::getFurBitDailyReport::[{}]" + date);
		} catch (Exception e) {
			log.error("FurBitController:::::getFurBitDailyReport [{}] " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to process the report at this time.");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/getuserleaderboard/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getUserLeaderboardDetailsV4(@PathVariable String autho,
			@RequestParam("rptperiod") String rptperiod, @RequestParam("rpttype") String rpttype) {
		JResponse response = new JResponse();
		try {
			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuthV2(autho);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			long userId = Long.valueOf(map.get("user_id"));

			String startDate = "";
			String endDate = "";

			String date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT,
					IrisservicesConstants.UTCTIMEZONE, -1);

			// common for last 7/30 days
			endDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.ENDTIME);

			// Current day leader board data
			if (rptperiod.equalsIgnoreCase("currentday")) {
				date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT,
						IrisservicesConstants.UTCTIMEZONE, 0);

				startDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.STARTTIME);
				endDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.ENDTIME);

			} else if (rptperiod.equalsIgnoreCase("last7days")) {
				date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT,
						IrisservicesConstants.UTCTIMEZONE, -7);

				startDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.STARTTIME);

			} else if (rptperiod.equalsIgnoreCase("last30days")) {
				date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT,
						IrisservicesConstants.UTCTIMEZONE, -30);

				startDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.STARTTIME);
			}

			//System.out.println("leaderboard: time : " + startDate + " : " + endDate);
			log.info("leaderboard: time : " + startDate + " : " + endDate);

			List<JUserLeaderBoard> rpt = iFurBitReportService.getUserLeaderBoardDetails(userId, startDate, endDate,
					rpttype);

			if (rpt != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("rpt", rpt);
				response.put("rptperiod", rptperiod);
			} else {
				response.put("Status", 1);
				response.put("Msg", "No Report found");
			}
		} catch (Exception e) {
			log.error("getFurBitLastGatewayReport : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to process the report at this time.");
			response.put("error", e.getLocalizedMessage());
		}

		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/getleaderboard/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getLeaderboardDetailsV4(@PathVariable String autho, @RequestParam("limit") int limit,
			@RequestParam("rpttype") String rpttype, @RequestParam("rptperiod") String rptperiod) {
		JResponse response = new JResponse();
		HashMap<Long, Long> usergaway = new HashMap<Long, Long>();
		try {
			try {
				Map<String, String> map = new HashMap<String, String>();

				try {
					map = userServiceV4.getUserId_cmpIdByAuthV2(autho);
				} catch (InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for authkey : " + autho);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				long userId = Long.valueOf(map.get("user_id"));

				List<JGateway> gateways = gatewayServiceV4.getGatewayV4(null, null, null, null, userId, null);

				for (JGateway gateway : gateways) {
					usergaway.put(gateway.getId(), gateway.getId());
				}

			} catch (Exception e) {
				log.error("user authkey", e.getLocalizedMessage());
			}

			String startDate = "";
			String endDate = "";

			String date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT,
					IrisservicesConstants.UTCTIMEZONE, -1);

			// common for last 7/30 days
			endDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.ENDTIME);

			// Current day leader board data
			if (rptperiod.equalsIgnoreCase("currentday")) {
				date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT,
						IrisservicesConstants.UTCTIMEZONE, 0);

				startDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.STARTTIME);
				endDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.ENDTIME);

			} else if (rptperiod.equalsIgnoreCase("last7days")) {
				date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT,
						IrisservicesConstants.UTCTIMEZONE, -7);

				startDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.STARTTIME);

			} else if (rptperiod.equalsIgnoreCase("last30days")) {
				date = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATEFORMAT,
						IrisservicesConstants.UTCTIMEZONE, -30);

				startDate = date.concat(IrisservicesConstants.SPACE_STRING).concat(IrisservicesConstants.STARTTIME);
			}

			//System.out.println("leaderboard: time : " + startDate + " : " + endDate);
			log.info("leaderboard: time : " + startDate + " : " + endDate);

			List<JLeaderBoard> rpt = iFurBitReportService.getLeaderBoardDetails(rpttype, startDate, endDate, limit,
					usergaway);

			if (rpt != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("rpt", rpt);
				response.put("rpttype", rpttype);
				response.put("rptperiod", rptperiod);
			} else {
				response.put("Status", 1);
				response.put("Msg", "No Report found");
			}
		} catch (Exception e) {
			log.error("getFurBitLastGatewayReport : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unable to process the report at this time.");
			response.put("error", e.getLocalizedMessage());
		}

		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
*/
}
