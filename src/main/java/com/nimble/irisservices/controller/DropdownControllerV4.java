package com.nimble.irisservices.controller;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JOrderChannel;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AlertType;
import com.nimble.irisservices.entity.OrderChannel;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IDropdownServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class DropdownControllerV4 {

	private static final Logger log = LogManager.getLogger(DropdownControllerV4.class);

	@Autowired
	IUserServiceV4 userServiceV4;
	

	@Autowired
	@Lazy
	IDropdownServiceV4 dropdownServiceV4;

	// v4.0/alerttype/ - SIV
	@RequestMapping(value = "v4.0/alerttype/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertType_V4(@PathVariable String autho,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		log.info("Entering getAlertType_V4 : " + autho);
		try {
			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);
			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception while getting user for authkey : " + autho);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			if (user != null) {
				List<AlertType> alerttypes = dropdownServiceV4.getAlertTypesV4();

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("alerttype", alerttypes);
			} else {
				log.info("in valid auth");
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
			}
		} catch (Exception e) {
			log.error("getAlertType_V4 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occured");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@RequestMapping(value = "v4.0/orderchannel", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getOrderChannelV4( @RequestParam(value="sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		log.info("Entering getOrderChannelV4 : ");
		try {
			List<JOrderChannel> jOrderChannelsList = new ArrayList<JOrderChannel>();

			List<OrderChannel> orderChannels = dropdownServiceV4.getOrderChannelV4();

			if (orderChannels != null && orderChannels.size() > 0) {

				for (OrderChannel orderChannel : orderChannels) {

					JOrderChannel _orderChannel = new JOrderChannel();

					_orderChannel.setOrderChannel(orderChannel.getOrderchannel());
					_orderChannel.setShortDescription(orderChannel.getShortdescription());

					jOrderChannelsList.add(_orderChannel);
				}
				response.put("Status", 1);
				response.put("Msg", "Success");

				// response.put("orderChannel", jOrderChannelsList);
				response.put("Orderchannel", orderChannels);

				response.put("Return Time", System.currentTimeMillis());
				return response;

			} else {
				response.put("Status", 0);
				response.put("Msg", "No Order channel found.");
				log.error("No Order channel listed.");

				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting orderchannels.");
			response.put("Error", e.getLocalizedMessage());
			log.error("getOrderChannelV4 : Exception : " + e.getLocalizedMessage());

			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

	}

	
}
