package com.nimble.irisservices.controller;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.GZIPOutputStream;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Controller;
import org.springframework.util.ResourceUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chargebee.Environment;
import com.chargebee.ListResult;
import com.chargebee.models.Customer;
import com.chargebee.models.Plan;
import com.chargebee.models.Subscription.Status;
import com.google.gson.Gson;
import com.nimble.irisservices.dao.IReportDao;
import com.nimble.irisservices.dto.EnableOrDisablePetProfile;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JGatewayUserDetails;
import com.nimble.irisservices.dto.JPetprofile;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSGateway;
import com.nimble.irisservices.dto.JSubscritionDeviceReport;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.DeviceSubscriptions;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.Orders;
import com.nimble.irisservices.entity.ProbeCategory;
import com.nimble.irisservices.entity.Subscription;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.EmailContent;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.IDeviceSubscriptionService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.IRVCentricDetailsService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class GatewayController {

	private static final Logger log = LogManager.getLogger(GatewayController.class);

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;
	
	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;
	
	@Autowired
	@Lazy
	IMessagingService messagingService;

	@Autowired
	@Lazy
	IReportService reportService;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IDeviceSubscriptionService deviceSubscription;

	@Autowired
	@Lazy
	IReportDao reportDao;

	@Autowired
	@Lazy
	IAlertCfgService alertCfgService;
	
	@Autowired
	@Lazy
	private IAsyncService async;

	@Value("${niomip}")
	private String niomIP;

	@Value("${niomauthkey}")
	private String niomAuthKey;

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;
	
	@Value("${freeplan}")
	private String freeplan;
	
	@Value("${omitplan}")
	private String omitplan;
	
	@Value("${vpmplan}")
	private String vpmplan;
	
	@Value("${addonplan}")
	private String addonplan;

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;

	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;

	Helper _helper = new Helper();
	
	@Autowired
	IRVCentricDetailsService rvcentricServ;

	// Used in listener check tool
	// ========get gateway================
	@RequestMapping(value = "v3.0/gateway/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getGatewayById(@PathVariable String autho, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("groupid") String groupid, @RequestParam("subgroupid") String subgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("userid") String userid,
			@RequestParam(value = "meid", defaultValue = "", required = false) String meid) {

		JResponse response = new JResponse();
		//System.out.println("get gateway");
		try {
			User user = userService.verifyAuthKey(autho);
			//System.out.println("get gateways gatewayid: " + gatewayid);
			long userId = 0;

			if (!userid.trim().equals(""))
				userId = Long.parseLong(userid);
			else
				userId = user.getId();

			List<JGateway> gateways = gatewayService.getGateway(assetgroupid, groupid, subgroupid, gatewayid, userId,
					meid);
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("gateways", gateways);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		return response;
	}

	// Used in web
	// ========save or update gateway================
//	@RequestMapping(value = "v3.0/gateway/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse saveORupdateGatewayById(@PathVariable String autho, @ModelAttribute @Valid JGateway jgateway,
//			BindingResult result) {
//		JResponse response = new JResponse();
//
//		try {
//			/* Create or Update Validation */
//			if (result.hasErrors()) {
//				return gatewayValidation(result);
//			}
//
//			if (jgateway.isStopreport()) {
//				if (jgateway.getStarttime().isEmpty()) {
//					response.put("Status", 0);
//					response.put("Msg", "Starttime should not be empty");
//					return response;
//				} else if (jgateway.getStoptime().isEmpty()) {
//					response.put("Status", 0);
//					response.put("Msg", "Stoptime should not be empty");
//					return response;
//				}
//			}
//
//			User user = userService.verifyAuthKey(autho);
//			jgateway = gatewayService.gatewayExitsinDB(jgateway, user.giveCompany().getId());
//
//			// Check if gateway name contains special characters
//			String gateway_name = jgateway.getName();
//			Pattern special = Pattern.compile("[!@#$%&*()+=|<>?{}.,\\[\\]~'\"]");
//			Matcher hasSpecial = special.matcher(gateway_name);
//			if (hasSpecial.find()) {
//
//				log.info("Gateway name contains special chars");
//
//				response.put("Status", 0);
//				response.put("Msg", "Gateway name contains special chars other than hyphen(-) and underscore(_)");
//				return response;
//			}
//
//			if (jgateway.getQrcode().equals("NA")) {
//				Helper _helper = new Helper();
//
//				String niomResponse = _helper
//						.getURL(niomIP + "v1.0/getinventory/" + niomAuthKey + "/" + jgateway.getMeid());
//
//				JSONObject nresponse = new JSONObject(niomResponse.toString());
//				JSONObject jresponse = new JSONObject();
//
//				jresponse = nresponse.getJSONObject("response");
//				int status = jresponse.getInt("Status");
//				if (status > 0) {
//					jgateway.setQrcode(jresponse.getString("qrCode"));
//				} else {
//					jgateway.setQrcode("NA");
//				}
//
//			}
//
//			Gateway gateway = gatewayService.saveORupdateGateway(jgateway, user.giveCompany().getId());
//
//			if (jgateway.isUserGatDis()) {
//
//				Company usrCompany = user.giveCompany();
//				Company gatCop = gateway.getCompany();
//
//				user.getGateways().add(gateway);
//				// Set<Gateway> gateways =user.getGateways();
//				// gateways.add(gateway);
//				// user.setGateways(gateways);
//				userService.updateUser(user);
//			}
//			gatewayService.updateGatewayCredit(gateway.getId(), user.giveCompany().getId());
//			if (jgateway.getId() == 0) {
//				try {
//					// gatewayService.updateGatewayCredit(gateway.getId(),
//					// user.giveCompany().getId());
//					reportService.saveGatewayReport(gateway,270.0,270.0);
//					reportService.saveLastGatewayReport(gateway,270.0,270.0);
//
//				} catch (Exception e) {
//					response.put("Status", 0);
//					response.put("Msg", "Default Reports Cannot be Generated.");
//					log.error("2saveORupdateGateway-Default Reports Canot be Generated::::" + e.getLocalizedMessage());
//					return response;
//				}
//			}
//
//			String message1 = "gpsmode=standalone";
//			String message2 = "SFWSTORAGE=ON";
//			String message3="reportinterval=900";
//			String message4="maxsleeptime=900";
//			String message5="modemresetint=3600";
//			String n7Cmd = "batoffset=-0.7,chgoffset=1.9,tempoffset=-1.2,fullchgoffset=-2.5";
//
//			String messages ="";
//			if (gateway.getModel().getModel().toLowerCase().contains("nt3d")) {
//				messages = message3 +","+message4+","+message2+","+message5;
//			}else if (gateway.getModel().getModel().toLowerCase().contains("nt3f")) {
//				messages = message2 +","+message3+","+message4+","+message5;
//			}else if (gateway.getModel().getInventorymodelname().toLowerCase().contains("n1")) {
//				messages = message1 +","+message2;
//			}else if (gateway.getModel().getInventorymodelname().toLowerCase().contains("w5")) {
//				messages = message2;
//			}else if (gateway.getModel().getModel().toLowerCase().contains("n7")) {
//				messages = message2 +","+message3+","+message4+","+message5;
//				
//				if( !(gateway.getModel().getModel().equalsIgnoreCase("n7a-503 nt3k") || gateway.getModel().getModel().equalsIgnoreCase("n7-504 nt3k")) ) {
//					messages = messages + "," + n7Cmd;
//				}
//			}
//			
//			if(!jgateway.getQrcode().isEmpty() && !jgateway.getQrcode().equalsIgnoreCase("NA"))
//				messages = messages +",setqrc="+jgateway.getQrcode().trim();
//			
//			
//			messagingService.saveMessageV2(Long.toString(gateway.getId()), messages, 0L);
//			
////			UserRvDetails rvObj = userServiceV4.getUserRvDetails(user.getId());
////			boolean rvStatus = (rvObj != null) ? true : false;
//			
////			boolean stat1 = rvcentricServ.saveUserBadgeTxn(user.getId(),"NA",1,rvStatus,user.getChargebeeid());						
////			log.info("in add gateway:"+user.getId()+" Badge created:"+stat1);
//
//			
//			response.put("Status", 1);
//			response.put("Msg", "Success");
//
//		} catch (InvalidAuthoException e) {
//			log.error("in valid auth");
//			response.put("Status", 0);
//			response.put("Msg", "invalid authentication key");
//			return response;
//		} catch (InvalidAsseIdException be) {
//			log.error("in valid assetid");
//			response.put("Status", 0);
//			response.put("Msg", "invalid id");
//			return response;
//		} catch (ConstraintViolationException e) {
//			log.error("gateway : constraint violation ");
//			response.put("Status", 0);
//			response.put("Msg", "MEID already exist");
//			return response;
//		} catch (InvalidAssetGroupIdException e) {
//			response.put("Status", 0);
//			response.put("Msg", "invalid assetgroupid exception");
//			return response;
//		} catch (InvalidSubgroupIdException e) {
//			response.put("Status", 0);
//			response.put("Msg", "invalid subgroupid exception");
//			return response;
//		} catch (InvalidGroupIdException e) {
//			response.put("Status", 0);
//			response.put("Msg", "invalid groupid exception");
//			return response;
//		} catch (InvalidModelIdException e) {
//			response.put("Status", 0);
//			response.put("Msg", "invalid modelid exception");
//			return response;
//		} catch (DataIntegrityViolationException e) {
//			response.put("Status", 0);
//			response.put("Msg", "Asset name or MEID already exits");
//			return response;
//		} catch (Exception e) {
//			response.put("Status", 0);
//			log.info("saveORupdateGateway::::" + e.getMessage());
//			response.put("Msg", "gateway name cannot be empty , and unique for every group");
//			return response;
//		}
//		return response;
//	}

	// Used in web
	// ==========save / update gateway========
	@RequestMapping(value = "v3.0/usergateway/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse assignGatewaysToUser(@PathVariable String autho, @PathVariable Long userid,
			@RequestParam String[] gatewayids, BindingResult result) {

		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			// userService.assignGatewaysToUser(userid, gatewayids);
			response.put("Status", 1);
			response.put("Msg", "success");

		} catch (InvalidAuthoException e) {
			response.put("Status", 1);
			response.put("Msg", "Invalid user");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in assign asset to user");
			log.info("assignGatewaysToUser::::" + e.getMessage());
		}
		return response;
	}

	// Used in web
	// ==========delete gateway========
//	@RequestMapping(value = "v3.0/gateway/{asset_id}/{autho}", method = RequestMethod.DELETE, headers = "Accept=application/json")
//	public @ResponseBody JResponse deleteGateway(@PathVariable String autho, @PathVariable String asset_id) {
//		JResponse response = new JResponse();
//		try {
//			User user = userService.verifyAuthKey(autho);
//			gatewayService.delGateway(user, asset_id);
//			response.put("Status", 1);
//			response.put("Msg", "success");
//		} catch (InvalidAuthoException e) {
//			response.put("Status", 0);
//			response.put("Msg", "invalid user");
//		} catch (Exception e) {
//			response.put("Status", 0);
//			response.put("Msg", "UnExcepted Error in delete gateway");
//			log.error("deleteGateway::::" + e.getMessage());
//		}
//		return response;
//	}

	/* Create or Update Validation */
	public JResponse gatewayValidation(BindingResult result) {
		JResponse response = new JResponse();

		response.put("Status", 0);
		if (result.getFieldError("id") != null)
			response.put("Msg", "id should not be empty");
		else if (result.getFieldError("name") != null)
			response.put("Msg", result.getFieldError("name").getDefaultMessage());
		else if (result.getFieldError("meid") != null)
			response.put("Msg", result.getFieldError("meid").getDefaultMessage());
		else if (result.getFieldError("modelid") != null)
			response.put("Msg", "Model should not be empty");
		else if (result.getFieldError("groupid") != null)
			response.put("Msg", "Groupid should not be empty");
		else if (result.getFieldError("mdn") != null)
			response.put("Msg", result.getFieldError("mdn").getDefaultMessage());
		else if (result.getFieldError("enable") != null)
			response.put("Msg", "Enable must be true or false");
		else if (result.getFieldError("alive") != null)
			response.put("Msg", "Alive must be true or false");
		else if (result.getFieldError("assetgroupid") != null)
			response.put("Msg", "assetgroupid should not be empty");
		else if (result.getFieldError("sensorEnable") != null)
			response.put("Msg", result.getFieldError("sensorEnable").getDefaultMessage());
		else if (result.getFieldError("stopreport") != null)
			response.put("Msg", "stopreport must be true or false");
		return response;
	}

	// Used in web
	// ========get all gateways================
	@RequestMapping(value = "v3.0/allgateways/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllGateway(@PathVariable String autho, @RequestParam("cmptype_id") String cmptype_id,
			@RequestParam(value = "zip", defaultValue = "0", required = false) String zip) {

		JResponse response = new JResponse();
		log.info("get gateway");
		try {
			User user = userService.verifyAuthKey(autho);
			long roleId = user.getRole().getId();
			// long roleId=1;
			if (roleId == 1) {
				List<JSGateway> gateways = gatewayService.getGateways(cmptype_id);
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("gateways", gateways);

				/* If zip params is 1.Report is zipped */
				// if(zip.equalsIgnoreCase("1"))
				// response.put("gateways", zipContent(gateways));
				// else
				// response.put("gateways", gateways);

			} else {
				response.put("Status", 0);
				response.put("Msg", "invalid role");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		// catch (IOException e) {
		// e.printStackTrace();
		// response.put("Status", 0);
		// response.put("Msg","invalid content for compress");
		// return response;
		// }
		return response;
	}

	// ========save or update gateway================
	@RequestMapping(value = "v3.0/saveorupdatePetProfile/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveorupdatePetProfile(@PathVariable String autho, @RequestBody List<JPetprofile> jpetprofiles) {
		JResponse response = new JResponse();

		try {
			if (jpetprofiles != null) {
				User user = userService.verifyAuthKey(autho);
				if (user.giveCompany().getCompanytype().getId() == 3) {

					// RV Pet
					// safety
					/**
					 * Fix for not updating the gateway name when user have two pets and try to
					 * update the petname
					 * 
					 * same as another gateway name
					 * 
					 **/

					for (JPetprofile jpetprofile : jpetprofiles) {
						jpetprofile.setUser_id(user.getId());
						String gateway_name = jpetprofile.getName();
						Pattern special = Pattern.compile("[!@#$%&*()+=|<>?{}.,\\[\\]~'\"]");
						Matcher hasSpecial = special.matcher(gateway_name);
						if (hasSpecial.find()) {
							log.info("Gateway name contains special chars");
							response.put("Status", 0);
							response.put("Msg", RegisterUserError.ER009);
							return response;
						}

						List<JGateway> gateways = gatewayService.getGateway(null, null, null, null, user.getId(), null);
						for (JGateway jGateway : gateways) {

							if (!jpetprofile.getGateway_id().equals(Long.toString(jGateway.getId()))) {
								if (jpetprofile.getName().equalsIgnoreCase(jGateway.getName())) {
									response.put("Status", 0);
									response.put("Msg", RegisterUserError.petNameUserMessage);
									return response;

								}
							}
						}
					}

					int status = gatewayService.saveorupdatePetprofile(jpetprofiles, Long.valueOf(user.getId()));
					log.info("Enter Into Gateway");
					if (status == 1) {
						// Get the gateway details of given gateway id and
						// userid
						for (JPetprofile jpetprofile : jpetprofiles) {

							// Get the gateway details of given gateway id and
							// userid
							// Fix to update Gateway name along with petname
							log.info("Enter Into Gateway");
							boolean issuccess = gatewayService.updateGatewayName(jpetprofile.getName(),
									jpetprofile.getGateway_id());
							log.info("GatewayName updated with respect to petname");

						}
						response.put("Status", 1);
						response.put("Msg", "success");
						response.put("Msg", "All profiles are saved Successfully");
					} else if (status == -2) {
						response.put("Status", 0);
						response.put("Msg", "Enter all the mandatory fields like gatewayid,name,age,sex and breed");
					} else {
						response.put("Status", 0);
						response.put("Msg", "Error, Not saved");
					}

				} else {
					response.put("Status", 0);
					response.put("Msg", "Invalid company type");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Empty input profile list");
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");

		} catch (DataIntegrityViolationException e) {
			log.error("saveorupdatePetProfile::::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "DataIntegrityViolationException");
		} catch (Exception e) {
			response.put("Status", 0);
			log.error("saveorupdatePetProfile::::" + e.getMessage());
			response.put("Msg", "UnExcepted Error in pet profile");
		}
		return response;
	}

	// ========get all gateways================
	@RequestMapping(value = "v3.0/getPetProfiles/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getPetProfiles(@PathVariable String autho,
			@RequestParam(value = "userid", defaultValue = "", required = false) String userid,
			@RequestParam(value = "gatewayid", defaultValue = "", required = false) String gatewayid,
			@RequestParam(value = "id", defaultValue = "", required = false) String id,
			@RequestParam(value = "name", defaultValue = "", required = false) String name,
			@RequestParam(value = "age", defaultValue = "", required = false) String age,
			@RequestParam(value = "sex", defaultValue = "", required = false) String sex,
			@RequestParam(value = "breed", defaultValue = "", required = false) String breed) {

		JResponse response = new JResponse();
		long userId = 0;

		try {
			User user = userService.verifyAuthKey(autho);
			if (user.giveCompany().getCompanytype().getId() == 3) {// RV Pet
				// safety
				long profileId = 0;
				long gatewayId = 0;

				if (id.isEmpty())
					profileId = 0;
				else
					profileId = Long.valueOf(id);

				if (userid.isEmpty())
					userId = user.getId();
				else
					userId = Long.valueOf(userid);

				if (!gatewayid.isEmpty())
					gatewayId = Long.valueOf(gatewayid);
				else
					gatewayId = 0;
				List<JPetprofile> jPetprofiles = gatewayService.getJPetprofiles(userId, profileId, gatewayId, name, age,
						sex, breed);

				response.put("Status", 1);
				response.put("Msg", "success");
				response.put("jPetprofiles", jPetprofiles);

			} else {
				response.put("Status", 0);
				response.put("Msg", "Invalid company type");
				log.info("Invalid company type");
			}

		} catch (InvalidAuthoException e) {
			log.error("Invalid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;
	}

	// ========get all gateways pet profile ================
	@RequestMapping(value = "v3.0/getPetProfile/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getPetProfile(@PathVariable String autho,
			@RequestParam(value = "userid", defaultValue = "", required = false) String userid,
			@RequestParam(value = "gatewayid", defaultValue = "", required = false) String gatewayid,
			@RequestParam(value = "monitortype", defaultValue = "", required = false) String monitortype) {

		JResponse response = new JResponse();
		long userId = 0;
		long gatewayId = 0;
		int monitortypeid = 0;

		try {
			User user = userService.verifyAuthKey(autho);

			if (userid.isEmpty())
				userId = user.getId();
			else
				userId = Long.valueOf(userid);

			if (!gatewayid.isEmpty())
				gatewayId = Long.valueOf(gatewayid);
			else
				gatewayId = 0;

			if (!monitortype.isEmpty())
				monitortypeid = Integer.valueOf(monitortype);
			else
				monitortypeid = 0;

			response = gatewayService.getJPetprofilesByUser(userId, gatewayId, monitortypeid);

		} catch (InvalidAuthoException e) {
			// System.out.println("in valid auth");
			log.error("Invalid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;
	}

	public static byte[] zipContent(Object obj) throws IOException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		GZIPOutputStream gzipOut = new GZIPOutputStream(baos);

		Gson gson = new Gson();
		String json = gson.toJson(obj);
		gzipOut.write(json.getBytes());

		gzipOut.close();
		// objectOut.close();
		byte[] bytes = baos.toByteArray();

		return bytes;
	}

	// ========get gateway user details with given meid================
	@RequestMapping(value = "v3.0/getgateway", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getgateway(@RequestParam("meid") String meid) {

		JResponse response = new JResponse();
		if (meid == null || meid.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "getgateway Failed: meid must not be empty");
			return response;
		}
		log.info("Get gateway with meid = " + meid);
		try {

			JGatewayUserDetails gateway = gatewayService.getGateway(meid);
			if (gateway != null) {
				response.put("Status", 1);
				response.put("Msg", "Success: Gateway is mapped");
				response.put("usergateway", gateway);

			} else {
				response.put("Status", 2);
				response.put("Msg", "Success:This gateway is not mapped to any user");

			}

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "getgateway Exception: " + e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	/**
	 * This is test function to get only the device summary
	 * 
	 * @param autho
	 * @param groupname
	 * @return
	 */

	@RequestMapping(value = "v3.0/getdevicesummary/{groupname}/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetSummaryv3(@PathVariable String autho, @PathVariable("groupname") String groupname) {

		JResponse response = new JResponse();
		//System.out.println("gateway summary json response");

		Helper _helper = new Helper();
		boolean setupActivate = false;
//		String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
//
//		String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);

		String upgradeSubscriptionUrl = _helper.getExternalConfigValue("upgradesubscriptionurl", externalConfigService);

		String activateSubscriptionUrl = _helper.getExternalConfigValue("activatesubscriptionurl",externalConfigService);

		Environment.configure(chargebeeSiteName, chargebeeSiteKey);

		try {
			User user = userService.verifyAuthKey(autho);

			List<JGateway> userGateway = gatewayService.getGateway(null, null, null, null, user.getId(), null);

			List<JSubscritionDeviceReport> subDeviceReportList = new ArrayList<JSubscritionDeviceReport>();

			if (userGateway != null) {

				if (userGateway.size() > 0) {

					for (JGateway gateway : userGateway) {

						JSubscritionDeviceReport subDeviceReport = new JSubscritionDeviceReport();
						String deviceQrc = "";
						deviceQrc = gateway.getQrcode();
						subDeviceReport.setId(gateway.getId());
						subDeviceReport.setMeid(gateway.getMeid());
						subDeviceReport.setAssetid(gateway.getName());
						subDeviceReport.setReportdatetime("NA");
						subDeviceReport.setTimeoffset("NA");
						subDeviceReport.setGpsstatus("NA");
						subDeviceReport.setLat(0);
						subDeviceReport.setLatdir("NA");
						subDeviceReport.setLon(0);
						subDeviceReport.setLondir("NA");
						subDeviceReport.setHeading("NA");
						subDeviceReport.setBattery(0);
						subDeviceReport.setTemperature(0);
						subDeviceReport.setSpeed(0);
						subDeviceReport.setDistance(0);
						subDeviceReport.setTdisplayunit("NA");
						subDeviceReport.setEventid("NA");
						subDeviceReport.setSignalstrength("NA");
						subDeviceReport.setQrcCode(deviceQrc);

						// Get Probe Category
						ProbeCategory probeCat = gatewayService.getProbeCategory(gateway.getModelid());
						if (probeCat != null)
							subDeviceReport.setProbetype(probeCat.getType());
						else
							subDeviceReport.setProbetype("");

						// subDeviceReportList.add(subDeviceReport);
						boolean res = false;
						long defaultid = 10000000;
						Integer id = 10000000;

						if (user.getChargebeeid().equalsIgnoreCase("NA"))
							res = true;
						else {
							ListResult result = com.chargebee.models.Subscription.list().customerId()
									.is(user.getChargebeeid()).status().in(Status.ACTIVE, Status.NON_RENEWING,Status.IN_TRIAL)
									.request();

							if (result.size() > 0) {
								com.chargebee.models.Subscription subscrip = null;
								Customer customer = null;
								res = false;

								int ssize = result.size();
								for (ListResult.Entry subs : result) {

									if (ssize == 1 && !omitplan.contains(subs.subscription().planId())
											&& !vpmplan.contains(subs.subscription().planId())
											&& !addonplan.contains(subs.subscription().planId())) {
										subscrip = subs.subscription();
										customer = subs.customer();
									} else if (!freeplan.contains(subs.subscription().planId())
											&& !omitplan.contains(subs.subscription().planId())
											&& !vpmplan.contains(subs.subscription().planId())
											&& !addonplan.contains(subs.subscription().planId())) {
										subscrip = subs.subscription();
										customer = subs.customer();
										break;
									}
								}
								if(subscrip == null) {
									for (ListResult.Entry subs : result) {
										if (freeplan.contains(subs.subscription().planId())) {
											subscrip = subs.subscription();
										}
									}
								}
								
								if(subscrip == null)
								{
									res = true;
								}
								else {
									ListResult planRes = Plan.list().id().is(subscrip.planId()).request();
									String billingPeriod = "";
									String periodUnit = "";
									int period = 0;
									String sku = "";
									for (ListResult.Entry planR : planRes) {
										Plan plan = planR.plan();
										period = plan.period();
										periodUnit = plan.periodUnit().name();
										sku = plan.sku();
									}

									if (periodUnit.equalsIgnoreCase("YEAR")) {
										billingPeriod = "Yearly";
									} else if (periodUnit.equalsIgnoreCase("MONTH")) {
										if (period == 3)
											billingPeriod = "Quarterly";
										else if (period == 6)
											billingPeriod = "Half-Yearly";
										else
											billingPeriod = "Monthly";
									} else if (periodUnit.equalsIgnoreCase("DAY")) {
										billingPeriod = "Daily";
									} else if (periodUnit.equalsIgnoreCase("WEEK")) {
										billingPeriod = "Weekly";
									}

									subDeviceReport.setSubcriptionStatus("true");
									subDeviceReport.setSubscriptionMessage("Subscription details found");

									SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
									sdf.setTimeZone(TimeZone.getTimeZone("UTC"));

									String billingStartDate = "";
									String trialEndDate = "1753-01-01 00:00:00";
									String nextPaymentDate = "";
									String lastPaymentDate = "";
									String subscriptionCreated = sdf.format(subscrip.createdAt().getTime());
									String endDate1 = "";
									String status = subscrip.status().name();
									if(subscrip.status().name().equalsIgnoreCase("IN_TRIAL")) {
										billingStartDate = sdf.format(subscrip.startedAt().getTime());
										trialEndDate =sdf.format(subscrip.trialEnd().getTime());	
										endDate1 = trialEndDate;
										lastPaymentDate = billingStartDate;
									}
									else {
										billingStartDate = sdf.format(subscrip.currentTermStart().getTime());
										lastPaymentDate = billingStartDate;
										endDate1 = sdf.format(subscrip.currentTermEnd().getTime());
									}					
																		
									String addr = "NA";
									String fname = "NA";
									String lname = "NA";
									String phone = "NA";
									String email = "NA";

									if (customer.billingAddress() != null) {
										addr = customer.billingAddress().line1() + " " + customer.billingAddress().line2();
										fname = customer.billingAddress().firstName();
										lname = customer.billingAddress().lastName();
										phone = customer.billingAddress().phone();
										email = customer.billingAddress().email();
									}

									Subscription subDetails = new Subscription(id, defaultid, defaultid, user.getId(),
											gateway.getMeid(), defaultid, status, fname, lname, email,
											phone, "NA", 0, subscrip.planId(), sku, (long) subscrip.planQuantity(),
											(float) subscrip.planUnitPrice() / 100, billingPeriod, billingStartDate,
											trialEndDate, nextPaymentDate, "NA", lastPaymentDate, addr, subscriptionCreated,
											endDate1, "RV", 0, "NA");

									//									if (subDetails.getEndDate() != null) {
									//
									//										Date endDate = _helper.formatDate(subDetails.getEndDate());
									//
									//										if (endDate.after(_helper.formatDate(_helper.getCurrentTimeinUTC()))
									//												|| endDate.equals(_helper.formatDate(_helper.getCurrentTimeinUTC()))) {
									//											subDetails.setStatus("active");
									//											subDetails.setNextPaymentDate(subDetails.getEndDate());
									//										}
									//									}

									SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
									Date dateobj = new Date();

									if (subDetails.getStatus().equalsIgnoreCase("ACTIVE") ) {
										subDetails.setStatus("active");

										nextPaymentDate = sdf.format(subscrip.nextBillingAt().getTime());
										subDetails.setNextPaymentDate(nextPaymentDate);
										subDetails.setAutoRenewalStatus("Enabled");

										Date nextPaymentDate1 = df.parse(nextPaymentDate);
										Date todayDate = df.parse(df.format(dateobj));

										long difference = nextPaymentDate1.getTime() - todayDate.getTime();
										int daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
										subDeviceReport.setDaysRemaining(Integer.toString(daysBetween));

										if (daysBetween < 0) {
											subDeviceReport.setDaysRemaining("-1");
											subDetails.setStatus("expired");
										}
										subDeviceReport.setSetupActivate(false);
										
										if (freeplan.contains(subscrip.planId())) {
																					
											subDeviceReport.setDaysRemaining("-1");
											subDeviceReport.setSetupActivate(true);											
											subDetails.setStatus("in-active");
											
										}
										
									} else if (subDetails.getStatus().equalsIgnoreCase("NON_RENEWING")) {
										subDetails.setStatus("active");
										nextPaymentDate = sdf.format(subscrip.cancelledAt().getTime());
										subDetails.setNextPaymentDate(nextPaymentDate);

										Date cancelDate = sdf.parse(nextPaymentDate);
										Date todayDate = df.parse(df.format(dateobj));

										long difference = cancelDate.getTime() - todayDate.getTime();
										int daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
										subDeviceReport.setDaysRemaining(Integer.toString(daysBetween));

										if (daysBetween < 0) {
											subDeviceReport.setDaysRemaining("-1");
											subDetails.setStatus("expired");
										}

										subDetails.setAutoRenewalStatus("Disabled");
										subDeviceReport.setSetupActivate(false);
									}else if(subDetails.getStatus().equalsIgnoreCase("IN_TRIAL")) {
										subDetails.setStatus("active");

										nextPaymentDate = sdf.format(subscrip.nextBillingAt().getTime());
										subDetails.setNextPaymentDate(nextPaymentDate);
										subDetails.setAutoRenewalStatus("Enabled");

										Date nextPaymentDate1 = df.parse(nextPaymentDate);
										Date todayDate = df.parse(df.format(dateobj));

										long difference = nextPaymentDate1.getTime() - todayDate.getTime();
										int daysBetween = (int) (difference / (1000 * 60 * 60 * 24));
										subDeviceReport.setDaysRemaining(Integer.toString(daysBetween));

										if (daysBetween < 0) {
											subDeviceReport.setDaysRemaining("-1");
											subDetails.setStatus("expired");
										}
										subDeviceReport.setSetupActivate(false);
									}									
									else {
										subDetails.setAutoRenewalStatus("Disabled");
										subDeviceReport.setDaysRemaining("-1");
										subDeviceReport.setSetupActivate(true);
									}

									subDeviceReport.setSetupAutoRenewal(false);
									subDeviceReport.setAutoRenewalURL("NA");
									subDeviceReport.setUpgradeSubscriptionUrl(upgradeSubscriptionUrl + "?qrc=" + deviceQrc);
									subDeviceReport.setActivateSubscriptionUrl(activateSubscriptionUrl + "?qrc=" + deviceQrc);

									subDeviceReport.setSub(subDetails);

									// }
									subDeviceReportList.add(subDeviceReport);
								}
							} else {
								res = true;
							}
						}

						if (res == true) {
							DeviceSubscriptions devSub = deviceSubscription.getDeviceSubscription(gateway.getMeid());

							if (devSub != null) {
								subDeviceReport.setSubcriptionStatus("true");
								subDeviceReport.setSubscriptionMessage("Subscription details found");
								Subscription subDetails = new Subscription((int) devSub.getNiomSubId(),
										devSub.getWoocomSubId(), devSub.getOrderId(), devSub.getWoocomSubId(),
										devSub.getMeid(), devSub.getWoocomSubId(), devSub.getStatus(),
										devSub.getBillingFirstname(), devSub.getBillingLastname(),
										devSub.getBillingEmail(), "NA", "NA", 0, devSub.getPlanName(), devSub.getSku(),
										devSub.getQuantity(), devSub.getPrice(), devSub.getBillingPeriod(),
										devSub.getBillingStartDate(), devSub.getTrialEndDate(),
										devSub.getNextPaymentDate(), "NA", devSub.getLastPaymentDate(), "NA",
										devSub.getSubscriptionCreated(), devSub.getEndDate(), devSub.getAccountType(),
										0, "NA");

								String originalStatus = subDetails.getStatus();

								if (subDetails.getEndDate() != null) {

									Date endDate = _helper.formatDate(subDetails.getEndDate());

									if (endDate.after(_helper.formatDate(_helper.getCurrentTimeinUTC()))
											|| endDate.equals(_helper.formatDate(_helper.getCurrentTimeinUTC()))) {
										subDetails.setStatus("active");
										subDetails.setNextPaymentDate(subDetails.getEndDate());
									}
								}

								if (subDetails.getWoocomSubId() > ********) {
									subDetails.setAutoRenewalStatus("Disabled");
								} else {
									if (originalStatus.equalsIgnoreCase("active")) {
										subDetails.setAutoRenewalStatus("Enabled");
									} else {
										subDetails.setAutoRenewalStatus("Disabled");
									}
								}

								if (subDetails.getStatus().equalsIgnoreCase("active")) {
									SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
									Date dateobj = new Date();

									Date nextPaymentDate = df.parse(subDetails.getNextPaymentDate());
									Date todayDate = df.parse(df.format(dateobj));
									long difference = nextPaymentDate.getTime() - todayDate.getTime();
									int daysBetween = (int) (difference / (1000 * 60 * 60 * 24));

									subDeviceReport.setDaysRemaining(Integer.toString(daysBetween));
									if (daysBetween < 0) {
										subDeviceReport.setDaysRemaining("-1");
										subDetails.setStatus("expired");
									}

								} else {
									subDeviceReport.setDaysRemaining("-1");
								}

								// For Amazon
								// if
								// (!subDetails.getStatus().equalsIgnoreCase("active")
								// && (subDetails.getOrderId() > ******** ||
								// subDetails.getWoocomSubId() > ********))
								//
								// {
								// subDeviceReport.setSetupActivate(false);
								// } else {
								// subDeviceReport.setSetupActivate(false);
								// }

								// For Both Amazon and RV Customers
								if (!subDetails.getStatus().equalsIgnoreCase("active")) {
									subDeviceReport.setSetupActivate(true);
								} else {
									subDeviceReport.setSetupActivate(false);
								}

								if (subDetails.getStatus().equalsIgnoreCase("active")
										&& (subDetails.getOrderId() > ********
												|| subDetails.getWoocomSubId() > ********)) {

									String autoRenewalUrl = _helper.getExternalConfigValue("autorenewalurl",
											externalConfigService);

									subDeviceReport.setSetupAutoRenewal(true);

									String qrcCode = "";
									if (!qrcCode.equals("NA")) {
										qrcCode = gateway.getQrcode();
									}

									String nextPaymentDate = "";
									if (!nextPaymentDate.equals("NA")) {
										nextPaymentDate = devSub.getHassleEndDate();
									}

									String purchaseDate = devSub.getSubscriptionCreated();

									String orderChannel = devSub.getOrderChannel();

									String orderId = "";
									if (orderChannel.contains("rv")) {
										orderChannel = "RVPetSafety.com";
										orderId = Long.toString(devSub.getOrderId());
									} else if (orderChannel.contains("amazon")) {
										orderChannel = "Amazon";

										if (!devSub.getExternalOrderId().equalsIgnoreCase("NA")) {
											orderId = devSub.getExternalOrderId();
										}
									} else if (orderChannel.contains("facebook")) {
										orderChannel = "Facebook";
										if (!devSub.getExternalOrderId().equalsIgnoreCase("NA")) {
											orderId = devSub.getExternalOrderId();
										}
									} else {

										if (!devSub.getExternalOrderId().equalsIgnoreCase("NA")) {
											orderId = devSub.getExternalOrderId();
										}
									}

									String firstName = "";

									if (!devSub.getBillingFirstname().equalsIgnoreCase("NA")) {
										firstName = devSub.getBillingFirstname();
									}

									String lastName = "";

									if (!devSub.getBillingLastname().equalsIgnoreCase("NA")) {
										lastName = devSub.getBillingLastname();
									}

									String phone = "";
									String email = "";

									if (!devSub.getBillingEmail().equalsIgnoreCase("NA")) {
										email = devSub.getBillingEmail();
									}

									if (!devSub.getBillingPhone().equalsIgnoreCase("NA")) {
										phone = devSub.getBillingPhone();
									}
									String parameters = "?f_name=" + firstName + "&l_name=" + lastName + "&orderId="
											+ orderId + "&email=" + email + "&phone=" + phone + "&purchasedfrom="
											+ orderChannel + "&qrc=" + qrcCode + "&paymentdate=" + nextPaymentDate
											+ "&purchasedate=" + purchaseDate;

									subDeviceReport.setAutoRenewalURL(autoRenewalUrl + parameters);

								} else {
									subDeviceReport.setSetupAutoRenewal(false);
									subDeviceReport.setAutoRenewalURL("NA");
								}

								subDeviceReport.setUpgradeSubscriptionUrl(upgradeSubscriptionUrl + "?qrc=" + deviceQrc);
								subDeviceReport
								.setActivateSubscriptionUrl(activateSubscriptionUrl + "?qrc=" + deviceQrc);

								subDeviceReport.setSub(subDetails);

							} else {

								Subscription subDetails = new Subscription();
								try {
									/* load a properties file */
									// prop.load(new
									// FileInputStream("iris3.properties"));
									// niomGetSubURL =
									// prop.getProperty("niomGetSubscriptionURL");
									//// String niomResponse =
									// _helper.getURL(niomGetSubURL
									// + "?meid=" + deviceReport.getMeid());

									// String niomResponse =
									// _helper.getURL(niomIP+"v1.0/getdevicesubscription?meid="
									// + "359486065167999");

									String niomResponse = _helper
											.getURL(niomIP + "v1.0/getdevicesubscription?meid=" + gateway.getMeid());
									Gson gson = new Gson();
									JSONObject nresponse = new JSONObject(niomResponse.toString());
									JSONObject jresponse = new JSONObject();

									jresponse = nresponse.getJSONObject("response");
									int status = jresponse.getInt("Status");
									String msg = jresponse.getString("Msg");
									String qrcCode = null;
									if (!jresponse.isNull("Qrccode")) {
										qrcCode = jresponse.getString("Qrccode");
										deviceQrc = qrcCode;
									}

									subDeviceReport.setQrcCode(qrcCode);

									if (status > 0 ? true : false) {
										subDeviceReport.setSubcriptionStatus("true");
										subDeviceReport.setSubscriptionMessage("Subscription details found");

										subDetails = gson.fromJson(jresponse.getJSONObject("subscription").toString(),
												Subscription.class);

										if (subDetails.getBillingPeriod().toLowerCase().equals("year")) {
											subDetails.setBillingPeriod("Yearly");
										}

										if (subDetails.getBillingPeriod().toLowerCase().contains("month")) {
											subDetails.setBillingPeriod("Monthly");
										}

										if (subDetails.getBillingPeriod().toLowerCase().contains("half")) {
											subDetails.setBillingPeriod("Half-Yearly");
										}

										if (subDetails.getBillingPeriod().toLowerCase().contains("quarter")) {
											subDetails.setBillingPeriod("Quarterly");
										}

										if (subDetails.getStatus().equalsIgnoreCase("active")) {
											SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
											Date dateobj = new Date();

											Date nextPaymentDate = df.parse(subDetails.getNextPaymentDate());
											Date todayDate = df.parse(df.format(dateobj));
											long difference = nextPaymentDate.getTime() - todayDate.getTime();
											int daysBetween = (int) (difference / (1000 * 60 * 60 * 24));

											subDeviceReport.setDaysRemaining(Integer.toString(daysBetween));
											if (daysBetween < 0) {
												subDeviceReport.setDaysRemaining("-1");
												subDetails.setStatus("expired");
											}

										} else {
											subDeviceReport.setDaysRemaining("-1");
										}

										// For Amazon Only
										// if
										// (!subDetails.getStatus().equalsIgnoreCase("active")
										// && (subDetails.getOrderId() > ********
										// || subDetails.getWoocomSubId() >
										// ********))
										//
										// {
										// subDeviceReport.setSetupActivate(setupActivate);
										// } else {
										// subDeviceReport.setSetupActivate(false);
										// }

										if (!subDetails.getStatus().equalsIgnoreCase("active"))

										{
											subDeviceReport.setSetupActivate(true);
										} else {
											subDeviceReport.setSetupActivate(false);
										}

										if (subDetails.getStatus().equalsIgnoreCase("active")
												&& (subDetails.getOrderId() > ********
														|| subDetails.getWoocomSubId() > ********))

										{

											String autoRenewalUrl = _helper.getExternalConfigValue("autorenewalurl",
													externalConfigService);
											String autoRenewalDefaultUrl = _helper.getExternalConfigValue(
													"autorenewaldefaulturl", externalConfigService);

											subDeviceReport.setSetupAutoRenewal(true);

											if (!jresponse.isNull("Orders")) {

												if (qrcCode == null) {
													qrcCode = "";
												}

												String nextPaymentDate = null;
												if (!jresponse.isNull("nextPaymentDate")) {
													nextPaymentDate = jresponse.getString("nextPaymentDate");
												} else {
													nextPaymentDate = "";
												}

												String purchaseDate = null;
												if (!jresponse.isNull("purchaseDate")) {
													purchaseDate = jresponse.getString("purchaseDate");
												} else {
													purchaseDate = "";
												}

												Orders order = new Orders();
												String orderChannel = "";
												order = gson.fromJson(jresponse.getJSONObject("Orders").toString(),
														Orders.class);

												String orderId = "";
												if (order.getOrder_acc_type().getAcc_type().contains("rv")) {
													orderChannel = "RVPetSafety.com";
													orderId = Long.toString(order.getOrder_id());
												} else if (order.getOrder_acc_type().getAcc_type().contains("amazon")) {
													orderChannel = "Amazon";

													if (!order.getExternal_order_id().equalsIgnoreCase("NA")) {
														orderId = order.getExternal_order_id();
													}
												} else if (order.getOrder_acc_type().getAcc_type()
														.contains("facebook")) {
													orderChannel = "Facebook";
													if (!order.getExternal_order_id().equalsIgnoreCase("NA")) {
														orderId = order.getExternal_order_id();
													}
												} else {
													orderChannel = order.getOrder_acc_type().getAcc_type();
													if (!order.getExternal_order_id().equalsIgnoreCase("NA")) {
														orderId = order.getExternal_order_id();
													}
												}

												String firstName = "";

												if (!order.getBilling_first_name().equalsIgnoreCase("NA")) {
													firstName = order.getBilling_first_name();
												}

												String lastName = "";

												if (!order.getBilling_last_name().equalsIgnoreCase("NA")) {
													lastName = order.getBilling_last_name();
												}

												String phone = "";
												String email = "";

												if (!order.getBilling_email().equalsIgnoreCase("NA")) {
													email = order.getBilling_email();
												}

												if (!order.getBilling_phone().equalsIgnoreCase("NA")) {
													phone = order.getBilling_phone();
												}
												String parameters = "?f_name=" + firstName + "&l_name=" + lastName
														+ "&orderId=" + orderId + "&email=" + email + "&phone=" + phone
														+ "&purchasedfrom=" + orderChannel + "&qrc=" + qrcCode
														+ "&paymentdate=" + nextPaymentDate + "&purchasedate="
														+ purchaseDate;

												subDeviceReport.setAutoRenewalURL(autoRenewalUrl + parameters);

											} else {
												subDeviceReport
												.setAutoRenewalURL(autoRenewalDefaultUrl + "?qrc=" + deviceQrc);
											}

										} else {
											subDeviceReport.setSetupAutoRenewal(false);
										}

										subDeviceReport.setUpgradeSubscriptionUrl(
												upgradeSubscriptionUrl + "?qrc=" + deviceQrc);
										subDeviceReport.setActivateSubscriptionUrl(
												activateSubscriptionUrl + "?qrc=" + deviceQrc);
										subDeviceReport.setSub(subDetails);
									} else {
										subDeviceReport.setUpgradeSubscriptionUrl(
												upgradeSubscriptionUrl + "?qrc=" + deviceQrc);
										subDeviceReport.setActivateSubscriptionUrl(
												activateSubscriptionUrl + "?qrc=" + deviceQrc);

										// Have to check if need we need to change
										// it to false
										subDeviceReport.setSetupActivate(true);
										subDeviceReport.setSetupAutoRenewal(false);
										subDeviceReport.setSubcriptionStatus("false");
										subDeviceReport.setSubscriptionMessage(
												"Invalid MEID or Subscription Details not found for respective MEID");
										subDeviceReport.setDaysRemaining("-1");
										subDeviceReport.setSub(null);
									}

								} catch (IOException ex) {
									subDeviceReport
									.setUpgradeSubscriptionUrl(upgradeSubscriptionUrl + "?qrc=" + deviceQrc);
									subDeviceReport
									.setActivateSubscriptionUrl(activateSubscriptionUrl + "?qrc=" + deviceQrc);
									subDeviceReport.setSubcriptionStatus("false");
									subDeviceReport.setSetupActivate(false);
									subDeviceReport.setSetupAutoRenewal(false);
									subDeviceReport.setSubscriptionMessage("Exception while reading niom URL from iris3.propertiesfile");
									subDeviceReport.setDaysRemaining("-1");
									subDeviceReport.setSub(null);
									log.error("Exception while reading niom URL from iris3.propertiesfile" + ex.getMessage());

								} catch (Exception e) {
									subDeviceReport.setSubcriptionStatus("false");
									subDeviceReport.setSetupActivate(false);
									subDeviceReport.setSetupAutoRenewal(false);
									subDeviceReport
									.setUpgradeSubscriptionUrl(upgradeSubscriptionUrl + "?qrc=" + deviceQrc);
									subDeviceReport
									.setActivateSubscriptionUrl(activateSubscriptionUrl + "?qrc=" + deviceQrc);
									subDeviceReport
									.setSubscriptionMessage("Exception while getting response from niom");
									subDeviceReport.setDaysRemaining("-1");
									subDeviceReport.setSub(null);
									log.error("Exception while getting response from niom :" + e.getMessage());
								}

							}
							subDeviceReportList.add(subDeviceReport);
						}
					}
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("DeviceSummary", subDeviceReportList);
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg", "No Devices are configured for users");
					return response;
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "No Devices are configured for users");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error(e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid group name/invalid authentication key");
			return response;
		}
	}

	// On-Off Feature

	@RequestMapping(value = "v3.0/onoff/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse assignGatewaysToUser(@PathVariable String autho, @RequestParam String gatewayid,
			@RequestParam boolean enable) {

		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);

			String message = null;

			Gateway gateway = gatewayService.getGateway(Long.valueOf(gatewayid));
			if (enable) {
				message = "lpm=iomode,reportinterval=" + gateway.getOnSleepTime() + ",maxsleeptime="
						+ gateway.getOnSleepTime();
			} else {
				message = "lpm=hibernate,reportinterval=" + gateway.getOffSleepTime() + ",maxsleeptime="
						+ gateway.getOffSleepTime();
			}

			boolean updateGatewayOnOffStatus = gatewayService.gatewayOnOff(gatewayid, enable, message);
			// userService.assignGatewaysToUser(userid, gatewayids);

			if (updateGatewayOnOffStatus) {

				String[] msg = message.split(",");

				for (String _msg : msg) {
					messagingService.saveMessageV2(gatewayid, _msg, 0L);
				}

				response.put("Status", 1);
				response.put("Msg", "success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed to On/Off the device");
			}

		} catch (InvalidAuthoException e) {
			response.put("Status", 0);
			response.put("Msg", "Invalid Authentication");
			log.error("Invalid Authentication:" + e.getMessage());

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "UnExcepted Error in ON OFF the device");
			log.error("On/OFF Device:" + e.getMessage());
		}
		return response;
	}

	@RequestMapping(value = "v3.0/enableordisableGateway/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse enableOrDisableGateway(@PathVariable String autho,
			@RequestParam(value = "gatewayid", required = true) String gatewayid,
			@RequestParam(value = "userid", required = true) String userid,
			@RequestParam(value = "isenable", required = true) boolean enable) {
		JResponse response = new JResponse();

		try {
			User user = userService.verifyAuthKey(autho);

			if (gatewayid != null && gatewayid != "" && !gatewayid.isEmpty() && userid != null && userid != ""
					&& !userid.isEmpty()) {

				List<JGateway> userGateway = gatewayService.getGateway(null, null, null, null, Long.parseLong(userid),
						null);

				if (userGateway != null && userGateway.size() > 0) {

					boolean gatewayFound = false;

					for (JGateway gateway : userGateway) {

						if ((gateway.getId() + "").equals(gatewayid)) {

							gatewayFound = true;

							boolean result = gatewayService.enableOrDisableGateway(gatewayid, userid, enable);

							if (!result) {
								response.put("Status", 0);
								response.put("Msg", "Could not enable or disabled the gateway for id : " + gatewayid);
								// response.put("Msg", "Could not enable or
								// disabled the gateway and alerts for id :
								// "+gatewayid);
								log.info("Could not enable or disabled the gateway for id : " + gatewayid);
								return response;
							}

							if (result && enable) {
								response.put("Status", 1);
								response.put("Msg", "Successfully enabled the gateway for Gateway id :" + gatewayid);
								log.info("Successfully enabled the gateway with alerts for Gateway id :" + gatewayid);
							}

							if (result && !enable) {
								response.put("Status", 1);
								response.put("Msg", "Successfully disabled the gateway for Gateway id :" + gatewayid);
								log.info("Successfully disabled the gateway with alerts for Gateway id :" + gatewayid);
							}
						}
					} // for end
					// gateway enable/disable success and if enable gateway
					// then enable alerts
					// if(result && enable){

					/*
					 * List<JAlertCfg> alertCfgList = alertCfgService.getAlertCfg("", "", gatewayid,
					 * Long.parseLong(userid), ""); String alertCfgIds = "";
					 * 
					 * for(JAlertCfg j : alertCfgList) { alertCfgIds += j.getId() + ","; }
					 * 
					 * if(alertCfgIds.length() > 2) alertCfgIds = alertCfgIds.substring(0,
					 * alertCfgIds.length()-1);
					 * 
					 * int alertResult = alertCfgService.enabledisablealertcfg(alertCfgIds, 1);
					 * 
					 * if(alertResult > 0) { response.put("Status", 1); response.put("Msg",
					 * "Successfully enabled the gateway with alerts for Gateway id :" +gatewayid);
					 * log. info("Successfully enabled the gateway with alerts for Gateway id :"
					 * +gatewayid); } else { response.put("Status", 1); response.put("Msg",
					 * "Successfully enabled the gateway. Could not enable the alerts for Gateway id :"
					 * +gatewayid); log.
					 * info("Successfully enabled the gateway. Could not enable the alerts for Gateway id :"
					 * +gatewayid); }
					 */

					// }
					// else if(result && !enable){
					//
					// List<JAlertCfg> alertCfgList =
					// alertCfgService.getAlertCfg("", "", gatewayid,
					// Long.parseLong(userid), "");
					//
					// String alertCfgIds = "";
					//
					// for(JAlertCfg j : alertCfgList)
					// {
					// alertCfgIds += j.getId() + ",";
					// }
					//
					// if(alertCfgIds.length() > 2)
					// alertCfgIds = alertCfgIds.substring(0,
					// alertCfgIds.length()-1);
					//
					// int alertResult =
					// alertCfgService.enabledisablealertcfg(alertCfgIds, 0);
					//
					// if(alertResult > 0)
					// {
					// response.put("Status", 1);
					// response.put("Msg", "Successfully disabled the gateway
					// with alerts for Gateway id :"+gatewayid);
					// log.info("Successfully disabled the gateway with alerts
					// for Gateway id :"+gatewayid);
					// }
					// else
					// {
					// response.put("Status", 1);
					// response.put("Msg", "Successfully disabled the gateway.
					// Could not disable the alerts for Gateway id
					// :"+gatewayid);
					// log.info("Successfully disabled the gateway. Could not
					// disable the alerts for Gateway id :"+gatewayid);
					// }
					//
					// }
					// }
					// }

					if (!gatewayFound) {
						response.put("Status", 0);
						response.put("Msg",
								"This Gateway id : " + gatewayid + " is not found for this user : " + userid);
						log.info("This Gateway id : " + gatewayid + " is not found for this user : " + userid);
					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "There is no gateway found for this user id : " + userid);
					log.info("There is no gateway found for this user id : " + userid);
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "The userId or GatewayId is invalid ");
				log.info("The userId or GatewayId is invalid ");
			}
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			log.error("Invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Unexpected error while enabling or disabling the gateway");
			log.error("Unexpected error while enabling or disabling the gateway");
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v3.0/updategoalsettings/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateGoalSetting(@PathVariable String autho,
			@RequestParam(value = "gatewayid", required = true) String gatewayid,
			@RequestParam(value = "goalSetting", required = true) String goalSetting) {
		JResponse response = new JResponse();

		try {
			User user = userService.verifyAuthKey(autho);
		} catch (Exception e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			log.error("Invalid authentication key");
		}

		int status = gatewayService.updateGoalSetting(gatewayid, goalSetting);

		if (status > 0) {
			response.put("Status", 1);
			response.put("Msg", "Goal updated successfully");
		} else {
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}

		return response;
	}

	@RequestMapping(value = "v3.0/updatecaloriesgoalsettings/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateCaloriesGoalSetting(@PathVariable String autho,
			@RequestParam(value = "gatewayid", required = true) String gatewayid,
			@RequestParam(value = "caloriesgoal", required = true) String caloriesgoal) {
		JResponse response = new JResponse();

		try {
			User user = userService.verifyAuthKey(autho);

			int status = gatewayService.updateCaloriesGoalSetting(gatewayid, caloriesgoal);
	
			if (status > 0) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed");
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Unexpected Error, Please contact support team");
			log.error("updatecaloriesgoalsettings : "+e.getLocalizedMessage());
		}

		return response;
	}
	
	@RequestMapping(value = "v3.0/checkqrcexist/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse checkQrcExist(@PathVariable String autho, @RequestParam(value="qrcode") String qrcode
			, @RequestParam(value="monitortypeid") String monitortypeid,
			@RequestParam(value = "os", defaultValue = "", required = false) String os,
			@RequestParam(value = "app_ver", defaultValue = "", required = false) String app_ver
			) {
		log.info("Entered checkQrcExist : autho : "+autho);
		JResponse response = new JResponse();
		UserV4 user = null;
		try {				
			Properties prop = new Properties();
			File file = ResourceUtils.getFile("classpath:iris3.properties");
			prop.load(new FileInputStream(file));
			String errorResponse = "No Error";
			String errorCode = "ER000";
			boolean isExist = false;

			response.put("qrcode", qrcode != null ? qrcode : "");
			if(!autho.equalsIgnoreCase("NA")) {
				try {
					user = userServiceV4.verifyAuthV3("authkey",autho);
				}catch(InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for auth : "+ex.getLocalizedMessage());
					return response;
				}
				
				if (!(qrcode.matches("[0-9]+")) || !(qrcode.length() == 6 || (qrcode.length() == 18 && qrcode.substring(0, 4).equalsIgnoreCase("BSCN"))) || qrcode.startsWith("8")) {
					log.info("Invalid QRCode . " + qrcode);
					response.put("Status", 0);
					response.put("Msg", RegisterUserError.ER048);
					errorCode = "ER048";
					errorResponse = RegisterUserError.ER048;
					String subject = "External User QRC Activation Status";
					String to_address = prop.getProperty("to_address");
					String cc_address = ""; //prop.getProperty("cc_address");
					String bcc_address = prop.getProperty("bcc_address");
					String statusEmailContent = new EmailContent().externalUserQrcActivationEmailContent(user, qrcode, errorResponse);
					async.externalQrcActivationStatus(qrcode,errorCode,to_address,cc_address,bcc_address,subject,statusEmailContent);				
					return response;
				}
				isExist = gatewayService.checkQrcExist(user.getId(), qrcode);
				boolean isQRCUser = false;
				
				if( user.getUsername().equalsIgnoreCase(qrcode) ) {
					isQRCUser = true;
				}
				
				if(isExist) {
					response.put("Status", 1);
					
					if( isQRCUser ) {
						response.put("Msg", "Valid QRCode");
					} else {
						response.put("Msg","A monitor with this QR code already exists in your account");
					}
					
				}else {
					response.put("Status", 1);
					response.put("Msg","Qrcode not exist!");
				}
				response.put("qrcavail", isExist);
				
			}else {
				log.info("invalid authkey received so do qrcode validation only and send if qrcode is valid");
				if (!(qrcode.matches("[0-9]+")) || !(qrcode.length() == 6 || (qrcode.length() == 18 && qrcode.substring(0, 4).equalsIgnoreCase("BSCN"))) || qrcode.startsWith("8")) {
					log.info("Valid QRCode . " + qrcode);
					response.put("Status", 0);
					response.put("Msg", RegisterUserError.ER048);
					return response;
				}else {
					response.put("Status", 1);
					response.put("Msg", "Valid QRCode");
					response.put("qrcavail", isExist);
				}
			}
		}catch(Exception e) {
			response.put("Status", 0);
			response.put("Msg", "invalid authkey");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception checkQrcExist : "+ e.getLocalizedMessage());
		}
		return response;
	}
	
	@RequestMapping(value = "v3.0/validateorderid/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse validateOrderId(@PathVariable String autho, @RequestParam("orderid") String orderId,
			@RequestParam("orderchannel") String orderChannel, @RequestParam(value ="os",defaultValue = "", required = false) String os,
			@RequestParam(value = "device_country", defaultValue = "US", required = false) String device_country) {
		log.info("Entered validateOrderId, orderId : "+orderId+" orderChannel : "+orderChannel);
		JResponse response = new JResponse();
		try {
			int orderIdLength = orderId.length();
			if(!autho.equalsIgnoreCase("NA")) {
				try {
					UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
				}catch(InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for username : "+autho);
					return response;
				}
			}
			if(orderChannel.equalsIgnoreCase("rv")) {
				Pattern numbers = Pattern.compile("^[0-9]+$");
				Matcher hasNumbers = numbers.matcher(orderId);
				if (hasNumbers.find() && orderIdLength >= 5 && orderIdLength <= 10) {
					response.put("Status", 1);
					response.put("Msg", "Success");
					log.info(orderChannel+" Order ID validated "+orderId);
					return response;
				}else {
					response.put("Status", 0);
					response.put("Msg", "Order ID should be 5 to 10 digits and should contains only numerics.");
					log.info(orderChannel+" Order ID should be 5 to 10 digits and should contains only numerics."+orderId);
				}
			}
			else if(orderChannel.equalsIgnoreCase("amazon")) {
				
				Pattern numbersWithHypen= Pattern.compile("^[0-9,-]+$");
				Matcher hasNumbersWithHypen= numbersWithHypen.matcher(orderId);
			
				Pattern numbers  = Pattern.compile("^[0-9]+$");
				Matcher hasNumbers  = numbers.matcher(orderId);
				if((hasNumbers.find() || hasNumbersWithHypen.find()) &&  orderIdLength >= 7 ){
					response.put("Status", 1);
					response.put("Msg", "Success");
					log.info(orderChannel+" Order ID validation success : "+orderId);
				}
				else{
					response.put("Status", 0);
					response.put("Msg", "Order ID should be min 7 characters and should not contains any alphabets or special characters except '-' (hypen)");
					log.info(orderChannel+" Order ID should contains only numerics : "+orderId);
				}
				return response;					
			}
			else if(orderChannel.equalsIgnoreCase("walmart") || orderChannel.equalsIgnoreCase("others")) {
				Pattern numbers  = Pattern.compile("^[0-9]+$");
				Matcher hasNumbers  = numbers.matcher(orderId);
				
				if(hasNumbers.find() ) {
					
					if(orderId.length() >= 10 ) {
						response.put("Status", 1);
						response.put("Msg", "Success");
						log.info(orderChannel+" Order id validated "+orderId);					
					}else {
						response.put("Status", 0);
						response.put("Msg", "Order ID should be min 10 characters and should not contains any alphabets or special characters for walmart orders");
						log.info(orderChannel+" Order ID should be min 10 characters and should not contains any alphabets or special characters for walmart orders");
					}
					
				}else {
					String msg = "Please contact us at "+supportContactNumber.get(device_country)+"  or email to "+supportContactEmail.get(device_country)+" to register your product";

					response.put("Status", 1);
					response.put("Msg",msg);
					log.info(orderChannel+ msg);				
				}
				return response;
			}
//			else if(orderChannel.equalsIgnoreCase("others"))  {
////				Pattern numbers  = Pattern.compile("^[0-9]+$");
////				Matcher hasNumbers  = numbers.matcher(orderId);
////				
////				if(hasNumbers.find() && orderId.length() >= 10 ) {
////					response.put("Status", 1);
////					response.put("Msg", "Success");
////					log.info(orderChannel+" Order id validated "+orderId);					
////				}else {
//					response.put("Status", 0);
//					response.put("Msg", "Please contact us at +1 (855)983-5566  or <NAME_EMAIL> to register your product");
//					log.info(orderChannel+" Please contact us at +1 (855)983-5566  or <NAME_EMAIL> to register your product");
//				//}
//				return response;
//			}
		}catch(Exception e) {
			log.error("Exception occured while validating order id : "+e.getLocalizedMessage());			
			response.put("Status", 0);
			response.put("Msg", "Unknown error");
			response.put("Error", e.getLocalizedMessage());
		}
		return response;
	}
	
	// ========get all gateways pet profile ================
	@RequestMapping(value = "v3.0/gatewaylistbymonitor/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse gatewayListByMonitortype(@PathVariable String autho,
			@RequestParam ("monitortype") String monitortype) {

		JResponse response = new JResponse();
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
			List<JGateway> gatewayList = gatewayService.getJGatewayByUser(user.getId(),monitortype );

			response.put("Status", 0);
			response.put("Msg", "Success");
			response.put("gatewayList", gatewayList);
			
		} catch (InvalidAuthoException e) {
			log.error("Invalid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}

		return response;
	}

	@RequestMapping(value = "v4.0/enableordisablepetprofile/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")	
	public @ResponseBody JResponse enableOrDisablePetprofile(@PathVariable String autho, 
			@RequestBody List<EnableOrDisablePetProfile> enableOrDisablePetPrfList) {

		log.info("Entered into enableOrDisablePetprofile");
		JResponse response = new JResponse();
			
		try {
			UserV4 user = userServiceV4.verifyAuthV4("authkey", autho);
			if(enableOrDisablePetPrfList.size() > 0) {
				boolean updatedStatus = gatewayService.enableOrDisablePetProfile(user.getId(), enableOrDisablePetPrfList);
				log.info("updated status : "+updatedStatus);

				if(updatedStatus) {
					response.put("Status", 1);
					response.put("Msg", "success");
				}else {
					response.put("Status", 1);
					response.put("Msg", "Failed to update pet profile");
				}
			}else {
				response.put("Status", 1);
				response.put("Msg", "No pet profile selected");
				return response;
			}			

		} catch (InvalidAuthoException e) {
			log.error("Invalid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}		
		return response;
	}
}
