package com.nimble.irisservices.controller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JApidetails;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.Apidetails;
import com.nimble.irisservices.entity.CompanyAccountSettings;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IAccountService;
import com.nimble.irisservices.service.IUserService;

@Controller
public class AccountController {

	@Autowired
	@Lazy
	IAccountService accService;

	@Autowired
	@Lazy
	IUserService userService;

	private static final Logger log = LogManager.getLogger(AccountController.class);

	@RequestMapping(value = "v3.0/createapi/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createapi(@PathVariable String autho, @RequestBody JApidetails japiDetail) {
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);

			if (user.getRole().getId() == 1) {
				
				Apidetails apiDetail = new Apidetails();
				
				apiDetail.setId(japiDetail.getId());
				apiDetail.setServiceType(japiDetail.getServiceType());
				apiDetail.setApi(japiDetail.getApiType());
				apiDetail.setDescription(japiDetail.getDescription());

				boolean status = accService.saveOrUpdateApiDetails(apiDetail);
				if (status) {
					response.put("Status", 1);
					response.put("Msg", "Api's are created");
					return response;
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "User Does not have permission to create company Accounts");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		catch (Exception e) {
			log.error("Excepitoin while creating/updating Company details");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while creating/updating Company details");
			return response;
		}
		return response;
	}
	
	
	@RequestMapping(value = "v3.0/createapiaccount/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createApiAccount(@PathVariable String autho, @RequestBody CompanyAccountSettings cmpAccDetail) {
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);

			if (user.getRole().getId() == 1) {

				boolean status = accService.saveOrUpdateCmpAccount(cmpAccDetail);
				if (status) {
					response.put("Status", 1);
					response.put("Msg", "Api's are created");
					return response;
				}

			} else {
				response.put("Status", 0);
				response.put("Msg", "User Does not have permission to create api's");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
		catch (Exception e) {
			log.error("Excepitoin while creating/updating API details");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while creating/updating API details");
			return response;
		}
		return response;
	}
	
	
}
