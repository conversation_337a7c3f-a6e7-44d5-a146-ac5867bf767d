package com.nimble.irisservices.controller;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.zip.GZIPOutputStream;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.gson.Gson;
import com.nimble.irisservices.dto.JAssetDescription;
import com.nimble.irisservices.dto.JAssetLastLiveTrackReport;
import com.nimble.irisservices.dto.JAssetLastReport;
import com.nimble.irisservices.dto.JAssetReport;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.ExternalConfig;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.Gatewayconfig;
import com.nimble.irisservices.entity.Livetracking;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGatewayConfigService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.ILiveTrackService;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IUserService;

@Controller
public class LiveTrackController {

	private static final Logger log = LogManager.getLogger(LiveTrackController.class);

	public LiveTrackController() {
		// TODO Auto-generated constructor stub

	}

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IGatewayConfigService gatewayConfigService;

	@Autowired
	@Lazy
	ILiveTrackService iLiveTrackService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IMessagingService messagingService;

	@Autowired
	@Lazy
	IReportService reportService;

	// Enable Live Tracking for white label user and also send OTA call to
	// device
	@RequestMapping(value = "v3.0/enablelivetrack/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse enableLiveTrack(@PathVariable String autho, @RequestParam("gatewayid") String gatewayID,
			@RequestParam("minutes") String minutes) {
		JResponse response = new JResponse();
		try {
			log.info("Verifying auth key . " + autho);
			User user = userService.verifyAuthKey(autho);
			Company cmp = companyService.getCompany(user.giveCompany().getId());

			String throtName = cmp.getThrotsettings().getName().toLowerCase();
			if (!throtName.contains("premium")) {
				response.put("Status", 0);
				response.put("Msg",
						"User should be premium user to enable live tracking. Please contact our support team.");
				return response;
			}

			log.info("Premium User . User Name" + user.getUsername());
			String companyType = cmp.getCompanytype().getName().toLowerCase();
			if (companyType.contains("white")) {
				log.info("WhiteLabel User . User Name" + user.getUsername());

				Gateway gatewayDetails = gatewayService.getGateway(Long.parseLong(gatewayID));

				Gatewayconfig gatewayConfig = new Gatewayconfig();
				gatewayConfig = gatewayConfigService.getGatewayConfig(Long.parseLong(gatewayID));

				if (gatewayConfig != null) {
					if (gatewayConfig.getLivetrackEnable()) {
						response.put("Status", 0);
						response.put("Msg", "Live Tracking already enabled for this gateway.");
						return response;
					}
				}

				List<JGateway> gateways = gatewayService.getGateway(null, null, null, null, user.getId(), null);

				boolean gatewayexists = false;

				for (JGateway gateway : gateways) {

					if (gateway.getId() == Long.parseLong(gatewayID)) {
						gatewayexists = true;
					}

				}

				if (!gatewayexists) {
					response.put("Status", 0);
					response.put("Msg",
							"Device is not corresponding to current user. Please contact our support team.");
					return response;
				}

				if (gateways.size() > 0) {

					for (JGateway gateway : gateways) {

						Gatewayconfig _gatewayConfig = new Gatewayconfig();
						_gatewayConfig = gatewayConfigService.getGatewayConfig(gateway.getId());

						if (_gatewayConfig != null) {
							if (_gatewayConfig.getLivetrackEnable()) {
								response.put("Status", 0);
								response.put("Msg",
										"Live Tracking already enabled for other device of this user. Live Tracking will be available for only one device at a time.");
								return response;
							}
						}

					}

				} else {
					response.put("Status", 0);
					response.put("Msg", "No device found for this user");
					return response;
				}

				ExternalConfig configuration = new ExternalConfig();
				configuration = externalConfigService.getExternalConfig("livetrackreportinterval");
				boolean messageStatus = false;
				boolean liveTrackEnableStatus = false;

				String reportInverval = "reportinterval=" + configuration.getValue();

				configuration = externalConfigService.getExternalConfig("enablelivetrackwithota");

				if (configuration.getValue().equalsIgnoreCase("false")) {
					messageStatus = messagingService.sendLiveTrackMessage(gatewayID, reportInverval, user.getId());
				} else {
					messageStatus = messagingService.saveMessage(gatewayID, reportInverval, user.getId());
				}

				if (messageStatus) {

					// Save or Update Gateway config
					boolean gateConfigStatus = false;
					Gatewayconfig storeGatewayConfig = new Gatewayconfig();

					if (gatewayConfig != null) {
						storeGatewayConfig.setId(gatewayConfig.getId());
						storeGatewayConfig.setLivetrackEnable(true);
						storeGatewayConfig.setGateway(gatewayConfig.getGateway());
						gateConfigStatus = gatewayConfigService.saveOrUpdateGatewayConfig(storeGatewayConfig);

					} else {
						storeGatewayConfig = new Gatewayconfig(gatewayDetails, true);
						gateConfigStatus = gatewayConfigService.saveOrUpdateGatewayConfig(storeGatewayConfig);
					}

					// Update or Save Live Track
					boolean liveTrackStatus = false;
					Livetracking liveTrack = new Livetracking();
					liveTrack = iLiveTrackService.getLiveTrackDetails(Long.parseLong(gatewayID));

					String enabledtime = getCurrentTimeinUTC();

					if (liveTrack != null) {
						liveTrack.setEnabledtime(enabledtime);
						liveTrack.setDisabledtime(null);
						liveTrack.setMinutes(Long.parseLong(minutes));
						liveTrackStatus = iLiveTrackService.saveOrUpdateLiveTrack(liveTrack);
					} else {
						liveTrack = new Livetracking(gatewayDetails, enabledtime, null, Long.parseLong(minutes));
						liveTrackStatus = iLiveTrackService.saveOrUpdateLiveTrack(liveTrack);
					}

					if (gateConfigStatus && liveTrackStatus) {
						liveTrackEnableStatus = true;
					}
				}

				if (!liveTrackEnableStatus) {
					configuration = externalConfigService.getExternalConfig("defaultreportinterval");
					reportInverval = "reportinterval=" + configuration.getValue();
					configuration = externalConfigService.getExternalConfig("enablelivetrackwithota");

					if (configuration.getValue().equalsIgnoreCase("false")) {
						messageStatus = messagingService.sendLiveTrackMessage(gatewayID, reportInverval, user.getId());
					} else {
						messageStatus = messagingService.saveMessage(gatewayID, reportInverval, user.getId());
					}
				}
				if (messageStatus && liveTrackEnableStatus) {
					response.put("Status", 1);
					response.put("Msg", "Live Tracking successfully enabled for the respective device.");
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg",
							"Unable to activate live tracking for the respective device . Please check with support team.");
					return response;
				}
				// CompanyConfig cmp_cfg =
				// companyService.getCompanyConfig(user.giveCompany().getId());
				// if(cmp_cfg.isLivetrack_enable()){}

			} else {
				response.put("Status", 0);
				response.put("Msg",
						"User should be White Label to enable live tracking. Please contact our support team.");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error("Excepitoin while enabling live tracking");
			response.put("Status", 0);
			log.error("Exception while enabling live tracking : " + e.getMessage());
			response.put("Msg", "Unable to enable live tracking for this device.");
			return response;
		}

	}

	// Disable Live Tracking for white label user and also send OTA call to
	// device
	@RequestMapping(value = "v3.0/disablelivetrack/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse disableLiveTrack(@PathVariable String autho, @RequestParam("gatewayid") String gatewayID) {
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);
			Company cmp = companyService.getCompany(user.giveCompany().getId());
			ExternalConfig configuration = new ExternalConfig();

			String throtName = cmp.getThrotsettings().getName().toLowerCase();
			if (!throtName.contains("premium")) {
				response.put("Status", 0);
				response.put("Msg",
						"User should be premium user to disable live tracking. Please contact our support team.");
				return response;
			}

			String companyType = cmp.getCompanytype().getName().toLowerCase();
			if (companyType.contains("white")) {

				Gateway gatewayDetails = gatewayService.getGateway(Long.parseLong(gatewayID));

				if (gatewayDetails == null) {
					response.put("Status", 0);
					response.put("Msg", "No device found to disable the live tracking.");
					return response;
				}

				List<JGateway> gateways = gatewayService.getGateway(null, null, null, null, user.getId(), null);

				boolean gatewayexists = false;

				for (JGateway gateway : gateways) {

					if (gateway.getId() == Long.parseLong(gatewayID)) {
						gatewayexists = true;
					}

				}

				if (!gatewayexists) {
					response.put("Status", 0);
					response.put("Msg",
							"Device is not corresponding to current user. Please contact our support team.");
					return response;
				}

				configuration = externalConfigService.getExternalConfig("defaultreportinterval");
				String reportInverval = "reportinterval=" + configuration.getValue();

				boolean liveTrackDisableStatus = false;
				boolean messageStatus = false;

				configuration = externalConfigService.getExternalConfig("disablelivetrackwithota");

				if (configuration.getValue().equalsIgnoreCase("true")) {
					messageStatus = messagingService.saveMessage(gatewayID, reportInverval, user.getId());
				} else {
					messageStatus = messagingService.sendLiveTrackMessage(gatewayID, reportInverval, user.getId());
				}

				if (messageStatus) {

					// Save or Update Gateway config
					boolean gateConfigStatus = false;
					Gatewayconfig gatewayConfig = new Gatewayconfig();
					gatewayConfig = gatewayConfigService.getGatewayConfig(Long.parseLong(gatewayID));
					Gatewayconfig storeGatewayConfig = new Gatewayconfig();

					if (gatewayConfig != null) {
						storeGatewayConfig.setId(gatewayConfig.getId());
						storeGatewayConfig.setLivetrackEnable(false);
						storeGatewayConfig.setGateway(gatewayConfig.getGateway());
						gateConfigStatus = gatewayConfigService.saveOrUpdateGatewayConfig(storeGatewayConfig);

					}

					// Update or Save Live Track
					boolean liveTrackStatus = false;
					Livetracking liveTrack = new Livetracking();
					liveTrack = iLiveTrackService.getLiveTrackDetails(Long.parseLong(gatewayID));

					String disabledTime = getCurrentTimeinUTC();

					// get current TimeZone using

					if (liveTrack != null) {
						liveTrack.setEnabledtime(liveTrack.getEnabledtime());
						liveTrack.setDisabledtime(disabledTime);
						liveTrack.setMinutes(Long.parseLong("0"));
						liveTrackStatus = iLiveTrackService.saveOrUpdateLiveTrack(liveTrack);
					}

					if (gateConfigStatus && liveTrackStatus) {
						liveTrackDisableStatus = true;
					}
				}

				if (!liveTrackDisableStatus) {
					configuration = externalConfigService.getExternalConfig("livetrackreportinterval");
					reportInverval = "reportinterval=" + configuration.getValue();
					configuration = externalConfigService.getExternalConfig("disablelivetrackwithota");

					if (configuration.getValue().equalsIgnoreCase("true")) {
						messageStatus = messagingService.saveMessage(gatewayID, reportInverval, user.getId());
					} else {
						messageStatus = messagingService.sendLiveTrackMessage(gatewayID, reportInverval, user.getId());
					}
				}
				if (messageStatus && liveTrackDisableStatus) {
					response.put("Status", 1);
					response.put("Msg", "Live Tracking successfully disabled for the respective device.");
					return response;
				} else {
					response.put("Status", 0);
					response.put("Msg",
							"Unable to disable live tracking for the respective device . Please check with support team.");
					return response;
				}

			} else {
				response.put("Status", 0);
				response.put("Msg",
						"User should be White Label to disable live tracking. Please contact our support team.");
				return response;
			}

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (Exception e) {
			log.error("Excepitoin while disabling Live tracking.");
			response.put("Status", 0);
			response.put("Msg", "Excepitoin while disabling Live tracking.");
			return response;
		}

	}

	
	
	// Disable Live Tracking for white label user and also send OTA call to
		// device
		@RequestMapping(value = "v3.0/disablelivetrackv2/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
		@ResponseBody
		public JResponse disableLiveTrackV2(@PathVariable String autho, @RequestParam("gatewayid") String gatewayID) {
			JResponse response = new JResponse();
			try {

				User user = userService.verifyAuthKey(autho);
				Company cmp = companyService.getCompany(user.giveCompany().getId());
				ExternalConfig configuration = new ExternalConfig();

//				String throtName = cmp.getThrotsettings().getName().toLowerCase();
//				if (!throtName.contains("premium")) {
//					response.put("Status", 0);
//					response.put("Msg",
//							"User should be premium user to disable live tracking. Please contact our support team.");
//					return response;
//				}

//				String companyType = cmp.getCompanytype().getName().toLowerCase();
				boolean companyType = true;
				if (companyType) {

					Gateway gatewayDetails = gatewayService.getGateway(Long.parseLong(gatewayID));

					if (gatewayDetails == null) {
						response.put("Status", 0);
						response.put("Msg", "No device found to disable the live tracking.");
						return response;
					}
					
					

//					List<JGateway> gateways = gatewayService.getGateway(null, null, null, null, gatewayDetails.getUsers().iterator().next().getId(), null);

//					boolean gatewayexists = false;
//
//					for (JGateway gateway : gateways) {
//
//						if (gateway.getId() == Long.parseLong(gatewayID)) {
//							gatewayexists = true;
//						}
//
//					}
//
//					if (!gatewayexists) {
//						response.put("Status", 0);
//						response.put("Msg",
//								"Device is not corresponding to current user. Please contact our support team.");
//						return response;
//					}

					configuration = externalConfigService.getExternalConfig("defaultreportinterval");
					String reportInverval = "reportinterval=" + configuration.getValue();

					boolean liveTrackDisableStatus = false;
					boolean messageStatus = false;

					configuration = externalConfigService.getExternalConfig("disablelivetrackwithota");

					if (configuration.getValue().equalsIgnoreCase("true")) {
						messageStatus = messagingService.saveMessage(gatewayID, reportInverval, gatewayDetails.getUsers().iterator().next().getId());
					} else {
						messageStatus = messagingService.sendLiveTrackMessage(gatewayID, reportInverval,gatewayDetails.getUsers().iterator().next().getId());
					}

					if (messageStatus) {

						// Save or Update Gateway config
						boolean gateConfigStatus = false;
						Gatewayconfig gatewayConfig = new Gatewayconfig();
						gatewayConfig = gatewayConfigService.getGatewayConfig(Long.parseLong(gatewayID));
						Gatewayconfig storeGatewayConfig = new Gatewayconfig();

						if (gatewayConfig != null) {
							storeGatewayConfig.setId(gatewayConfig.getId());
							storeGatewayConfig.setLivetrackEnable(false);
							storeGatewayConfig.setGateway(gatewayConfig.getGateway());
							gateConfigStatus = gatewayConfigService.saveOrUpdateGatewayConfig(storeGatewayConfig);

						}

						// Update or Save Live Track
						boolean liveTrackStatus = false;
						Livetracking liveTrack = new Livetracking();
						liveTrack = iLiveTrackService.getLiveTrackDetails(Long.parseLong(gatewayID));

						String disabledTime = getCurrentTimeinUTC();

						// get current TimeZone using

						if (liveTrack != null) {
							liveTrack.setEnabledtime(liveTrack.getEnabledtime());
							liveTrack.setDisabledtime(disabledTime);
							liveTrack.setMinutes(Long.parseLong("0"));
							liveTrackStatus = iLiveTrackService.saveOrUpdateLiveTrack(liveTrack);
						}

						if (gateConfigStatus && liveTrackStatus) {
							liveTrackDisableStatus = true;
						}
					}

					if (!liveTrackDisableStatus) {
						configuration = externalConfigService.getExternalConfig("livetrackreportinterval");
						reportInverval = "reportinterval=" + configuration.getValue();
						configuration = externalConfigService.getExternalConfig("disablelivetrackwithota");

						if (configuration.getValue().equalsIgnoreCase("true")) {
							messageStatus = messagingService.saveMessage(gatewayID, reportInverval, gatewayDetails.getUsers().iterator().next().getId());
						} else {
							messageStatus = messagingService.sendLiveTrackMessage(gatewayID, reportInverval, gatewayDetails.getUsers().iterator().next().getId());
						}
					}
					if (messageStatus && liveTrackDisableStatus) {
						response.put("Status", 1);
						response.put("Msg", "Live Tracking successfully disabled for the respective device.");
						return response;
					} else {
						response.put("Status", 0);
						response.put("Msg",
								"Unable to disable live tracking for the respective device . Please check with support team.");
						return response;
					}

				} else {
					response.put("Status", 0);
					response.put("Msg",
							"User should be White Label to disable live tracking. Please contact our support team.");
					return response;
				}

			} catch (InvalidAuthoException e) {
				log.error("in valid auth");
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				return response;
			} catch (Exception e) {
				log.error("Excepitoin while disabling Live tracking.");
				response.put("Status", 0);
				response.put("Msg", "Excepitoin while disabling Live tracking.");
				return response;
			}

		}
	
	// ========get lastgatewayreport ================
	@RequestMapping(value = "v3.0/livetracksummary/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetSummary(@PathVariable String autho, @RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("assetgroupid") String assetgroupid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("offset") String offset,
			@RequestParam("limit") String limit,
			@RequestParam(value = "zip", defaultValue = "1", required = false) String zip) throws ParseException {

		System.out.println("gateway summary");
		log.info("called gateway summary services" + "groupid: " + groupid + ", subgroupid: " + subgroupid
				+ ", assetgroupid: " + assetgroupid + " , gatewayid: " + gatewayid);
		JResponse response = new JResponse();
		try {

			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());
			List<JAssetLastReport> reportsummmary = reportService.getLastgatewayreport(groupid, subgroupid,
					assetgroupid, gatewayid, user.getId(), offset, limit, cmp_cfg.getTemperatureunit(),"");
			log.info("received gateway summary , report length: " + reportsummmary.size());

			List<JAssetLastLiveTrackReport> jTrackLiveSummaryList = new ArrayList<JAssetLastLiveTrackReport>();

			for (JAssetLastReport lastReport : reportsummmary) {

				JAssetLastLiveTrackReport jTrackLiveReport = new JAssetLastLiveTrackReport(lastReport.getAssetid(),
						lastReport.getAssetname(), lastReport.getAssetgroupname(), lastReport.getGroupname(),
						lastReport.getDatetime(), lastReport.getLat(), lastReport.getLatdir(), lastReport.getLon(),
						lastReport.getLondir(), lastReport.getSpeed(), lastReport.getGpsstatus(),
						lastReport.getGpsinfo(), lastReport.getGpsmode(), lastReport.getBatt(),
						lastReport.getDistance(), lastReport.getHeading(), lastReport.getEventid(),
						lastReport.getNmeventid(), lastReport.getIostatus(), lastReport.getExtsensor(),
						lastReport.getExtsensortype(), lastReport.getHumidity(), lastReport.getTemperature(),
						lastReport.getTempseverity(), lastReport.getLight(), lastReport.getPressure(),
						lastReport.getMotion(), lastReport.getRssi(), lastReport.getAddress(), lastReport.getAlive(),
						lastReport.getLastvalidlat(), lastReport.getLastvalidlatdir(), lastReport.getLastvalidlon(),
						lastReport.getLastvalidlondir(), lastReport.getLastvaliddatetime(), lastReport.getAssetInfo(),
						lastReport.getGroupid(), lastReport.getCellidlat(), lastReport.getCellidlon(),
						lastReport.getCellidacc(), lastReport.getEventname(), lastReport.getLastvalidaddress(),
						lastReport.getRawrssi(), lastReport.getLat_d(), lastReport.getLon_d(),
						lastReport.getLastvalidlat_d(), lastReport.getLastvalidlon_d(), lastReport.getLastvalidtemp(),
						lastReport.getRpt_timezone(), lastReport.getHeat_index());

				Gatewayconfig gatewayConfig = new Gatewayconfig();
				gatewayConfig = gatewayConfigService.getGatewayConfig(lastReport.getAssetid());

				if (gatewayConfig != null) {
					if (gatewayConfig.getLivetrackEnable()) {
						Date liveTrackEnableDate = null;
						Date reportTime = null;
						String timeZoneOffset = null;
						Livetracking liveTrack = new Livetracking();

						liveTrack = iLiveTrackService.getLiveTrackDetails(lastReport.getAssetid());

						liveTrackEnableDate = formatDate(liveTrack.getEnabledtime());

						timeZoneOffset = lastReport.getRpt_timezone();

						String gatewayreportTime = "";

						gatewayreportTime = getUTCTime(lastReport.getDatetime(), timeZoneOffset);

						reportTime = formatDate(gatewayreportTime);

						System.out.println("Report time zone :" + timeZoneOffset);

						log.info("Report time zone :" + timeZoneOffset);

						System.out.println("Live Track Enabled : gatewayreportTime : -->" + gatewayreportTime
								+ " liveTrackEnableDate " + liveTrack.getEnabledtime());

						log.info("Live Track Enabled : gatewayreportTime : -->" + gatewayreportTime
								+ " liveTrackEnableDate " + liveTrack.getEnabledtime());

						if (liveTrackEnableDate.before(reportTime)) {
							log.info("Live Track Enabled : gatewayreportTime : -->" + gatewayreportTime
									+ " liveTrackEnableDate " + liveTrack.getEnabledtime());
							jTrackLiveReport.setIsLiveTrackEnable("1");

							jTrackLiveReport
									.setEnabledOn(getTimeBasedOffet(liveTrack.getEnabledtime(), timeZoneOffset));
						}

						if (liveTrackEnableDate.after(reportTime)) {
							log.info("Yet to track : gatewayreportTime : -->" + gatewayreportTime
									+ " liveTrackEnableDate " + liveTrack.getEnabledtime());
							jTrackLiveReport.setIsLiveTrackEnable("0");
							jTrackLiveReport
									.setEnabledOn(getTimeBasedOffet(liveTrack.getEnabledtime(), timeZoneOffset));

						}

						if (liveTrackEnableDate.equals(reportTime)) {
							log.info("Live Track Enabled : gatewayreportTime : -->" + gatewayreportTime
									+ " liveTrackEnableDate " + liveTrack.getEnabledtime());
							jTrackLiveReport.setIsLiveTrackEnable("1");
							jTrackLiveReport
									.setEnabledOn(getTimeBasedOffet(liveTrack.getEnabledtime(), timeZoneOffset));

						}

					} else {
						jTrackLiveReport.setIsLiveTrackEnable("-1");
						jTrackLiveReport.setEnabledOn("NA");

					}
				} else {
					jTrackLiveReport.setIsLiveTrackEnable("-1");
					jTrackLiveReport.setEnabledOn("NA");
				}

				jTrackLiveSummaryList.add(jTrackLiveReport);
				jTrackLiveReport = new JAssetLastLiveTrackReport();

			}

			JAssetDescription assetDescrip = reportService.getAssetDescription(user.giveCompany().getId());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetdescription", assetDescrip);

			/* To reverse the collection of latest reported gateway */
			Collections.sort(jTrackLiveSummaryList, new Comparator<JAssetLastLiveTrackReport>() {
				@Override
				public int compare(JAssetLastLiveTrackReport a, JAssetLastLiveTrackReport b) {
					DateFormat format = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
					try {
						return format.parse(b.getDatetime()).compareTo(format.parse(a.getDatetime()));
					} catch (ParseException e) {
					}
					return 0;
				}
			});

			/* If zip params is 1.Report is zipped */
			if (zip.equalsIgnoreCase("1"))
				response.put("lastgatewayreport", zipContent(jTrackLiveSummaryList));
			else
				response.put("lastgatewayreport", jTrackLiveSummaryList);
		} catch (InvalidAuthoException e) {
			log.error("livetracksummary: "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (IOException e) {
			log.error("livetracksummary:"+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;
		}
		return response;
	}

	// ========get gatewayreport ================
	@RequestMapping(value = "v3.0/livetrackgatewayreport/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetReport(@PathVariable String autho, @RequestParam("fromtime") String fromtime,
			@RequestParam("timezone") String timezone, @RequestParam("gatewayid") String gatewayid,
			@RequestParam(value = "zip", defaultValue = "1", required = false) String zip) {
		JResponse response = new JResponse();
		try {

			if (fromtime.isEmpty() || fromtime == null || fromtime == "") {
				response.put("Status", 0);
				response.put("Msg", "Report fromtime should not be empty");
				return response;
			}

			if (timezone.isEmpty() || timezone == null || timezone == "") {
				response.put("Status", 0);
				response.put("Msg", "TimeZone should not be empty");
				return response;
			}

			if (gatewayid.isEmpty() || gatewayid == null || gatewayid == "") {
				response.put("Status", 0);
				response.put("Msg", "GatewayID should not be empty");
				return response;
			}

			User user = userService.verifyAuthKey(autho);

			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());

			// Commented below it since live tracking is not based on company
			// config
			//
			// if (!cmp_cfg.isLivetrack_enable()) {
			// response.put("Status", 0);
			// response.put("Msg", "Live tracking is not enabled for this
			// user.");
			// return response;
			// }

			Gatewayconfig gatewayConfig = new Gatewayconfig();
			gatewayConfig = gatewayConfigService.getGatewayConfig(Long.parseLong(gatewayid));

			if (gatewayConfig == null) {

				response.put("Status", 0);
				response.put("Msg", "Live tracking is not enabled for this device.");
				return response;

			}

			if (gatewayConfig != null) {
				if (!gatewayConfig.getLivetrackEnable()) {
					response.put("Status", 0);
					response.put("Msg", "Live tracking is not enabled for this device.");
					return response;
				}
			}

			Calendar calendar = Calendar.getInstance();
			Date d = calendar.getTime();

			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			df.setTimeZone(TimeZone.getTimeZone("GMT" + timezone));
			String totime = df.format(d);

			ExternalConfig configuration = new ExternalConfig();
			String offset = "0";
			configuration = externalConfigService.getExternalConfig("livetrackingreportlimit");
			String limit = configuration.getValue();

			List<JAssetReport> reportsummmary = reportService.getgatewayreport(fromtime, totime, "", gatewayid,
					user.giveCompany().getId(), offset, limit, cmp_cfg.getTemperatureunit());
			log.info("received gateway summary , report length: " + reportsummmary.size());

			response.put("Status", 1);
			response.put("Msg", "Success");

			/* If zip params is 1.Report is zipped */
			if (zip.equalsIgnoreCase("1"))
				response.put("gatewayreports", zipContent(reportsummmary));
			else
				response.put("gatewayreports", reportsummmary);

		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		} catch (IOException e) {
			// TODO Auto-generated catch block
			response.put("Status", 0);
			response.put("Msg", "invalid content for compress");
			return response;
		}

		return response;

	}

	public static byte[] zipContent(Object obj) throws IOException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		GZIPOutputStream gzipOut = new GZIPOutputStream(baos);

		Gson gson = new Gson();
		String json = gson.toJson(obj);
		gzipOut.write(json.getBytes());

		gzipOut.close();
		// objectOut.close();
		byte[] bytes = baos.toByteArray();

		return bytes;
	}

	public Date formatDate(String date) throws ParseException {

		Date formatedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(date);

		return formatedDate;
	}

	public String getUTCTime(String dateString, String timeZoneOffset) {

		SimpleDateFormat formatter = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");

		formatter.setTimeZone(TimeZone.getTimeZone("GMT" + timeZoneOffset));

		Date date = new Date();
		try {
			date = formatter.parse(dateString);
			log.info("TimeZoneOffset :" + timeZoneOffset);
			log.info("Date and Time in String :" + formatter.format(date));
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

			log.info("Converted to UTC Date and Time in String :" + formatter.format(date));

			return dateFormat.format(date);
		} catch (ParseException e) {
			log.error("Error while converting device time to utc " + dateString + "TimeZoneOffset" + timeZoneOffset);
			return null;
		}

	}

	public String getTimeBasedOffet(String dateString, String timeZoneOffset) {

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		formatter.setTimeZone(TimeZone.getTimeZone("UTC"));

		Date date = null;
		try {
			date = formatter.parse(dateString);
			log.info("TimeZoneOffset :" + timeZoneOffset);
			log.info("Date and Time in String :" + formatter.format(date));

			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			dateFormat.setTimeZone(TimeZone.getTimeZone("GMT" + timeZoneOffset));

			log.info("Converted to UTC Date and Time in String :" + formatter.format(date));

			return dateFormat.format(date);
		} catch (ParseException e) {
			log.error("Error while converting device time to utc " + dateString + "TimeZoneOffset" + timeZoneOffset);
			return null;
		}

	}

	public String getCurrentTimeinUTC() {

		Calendar calendar = Calendar.getInstance();
		Date d = calendar.getTime();

		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		df.setTimeZone(TimeZone.getTimeZone("UTC"));
		System.out.println(df.format(d));
		return df.format(d);
	}

}
