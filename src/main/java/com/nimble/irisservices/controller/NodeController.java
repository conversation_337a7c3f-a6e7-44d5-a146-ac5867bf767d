package com.nimble.irisservices.controller;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JNode;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.Node;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAsseIdException;
import com.nimble.irisservices.exception.InvalidAssetGroupIdException;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.exception.InvalidGatewayIdException;
import com.nimble.irisservices.exception.InvalidModelIdException;
import com.nimble.irisservices.service.INodeService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IUserService;

@Controller
public class NodeController {
	private static final Logger log = LogManager.getLogger(NodeController.class);
	
	@Autowired
	@Lazy
	IUserService userService;
	@Autowired
	@Lazy
	INodeService nodeService;
	@Autowired
	@Lazy
	IReportService reportService;

	// Used in web
	//========get nodes===============
	@RequestMapping(value="v3.0/node/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getNodeById(@PathVariable String autho,  @RequestParam("assetgroupid") String assetgroupid,
			 @RequestParam("groupid") String groupid, @RequestParam("subgroupid") String subgroupid,
			 @RequestParam("gatewayid") String gatewayid,@RequestParam("nodeid") String nodeid)	{
		
		JResponse response = new JResponse();
		try {
		
			User user = userService.verifyAuthKey(autho);			
			List<JNode> nodes = nodeService.getNode(assetgroupid,groupid,subgroupid,gatewayid,nodeid,user.getId());
			response.put("Status", 1);
			response.put("Msg","Success");
			response.put("nodes", nodes);
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}		
		return response;
	}
	
	// Used in web
	//========save or update node================
	@RequestMapping(value="v3.0/node/{autho}",method = RequestMethod.POST, headers="Accept=application/json")
	@ResponseBody
	public JResponse saveOrUpdateNodeById(@PathVariable String autho, @ModelAttribute JNode jnode)
	{
		JResponse response = new JResponse();
		
		try {
			
			User user = userService.verifyAuthKey(autho);			
			Node node = nodeService.saveORupdateNode(jnode, user.giveCompany().getId(), user.getId());
			
			if(jnode.getId() == 0) {
				try {
					reportService.saveNodeReport(node);
					reportService.saveLastNodeReport(node);
				}
				catch(Exception e)
				{
					response.put("Status", 1);
					response.put("Msg","Node Default Reports Cannot be Generated.");
					log.error("saveORupdateGateway-Node Default Reports Cannot be Generated::::"+e.getMessage());
					log.error("saveORupdateGateway-Node Default Reports Canot be Generated::::"+e.getLocalizedMessage());
					return response;
				}
			}
			
			response.put("Status", 1);
			response.put("Msg","Success");
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}catch (ConstraintViolationException e) {
			log.error("constraint violation exception");
			response.put("Status", 0);
			response.put("Msg","macaddrs already exist");
			return response;
		} catch (InvalidAssetGroupIdException e) {
			log.error("constraint violation exception");
			response.put("Status", 0);
			response.put("Msg","invalid assetgroup id");
			return response;
		} catch (InvalidGatewayIdException e) {
			log.error("constraint violation exception");
			response.put("Status", 0);
			response.put("Msg","invalid gateway id");
			return response;
		} catch (InvalidModelIdException e) {
			log.error("constraint violation exception");
			response.put("Status", 0);
			response.put("Msg","invalid model id");
			return response;
		} catch (InvalidAsseIdException e) {
			log.error("batch updateexception");
			log.error("invalid asset id");
			response.put("Status", 0);
			response.put("Msg","invalid id");
			return response;
		} catch (DataIntegrityViolationException e) {
			response.put("Status", 0);
			
			if(e.getMostSpecificCause().getLocalizedMessage().contains("uq_alertnameBycmp"))
				response.put("Msg","Node name already exist");
			else if(e.getMostSpecificCause().getLocalizedMessage().contains("assetaddr"))
				response.put("Msg","Node meid already exist");
			else 
				response.put("Msg","Node name or meid already exist");			
			
			return response;
		} 
		catch(Exception e) {
			log.error("saveORupdateNode::::"+e.getMessage());
			response.put("Status", 0);
			response.put("Msg","node name cannot be empty , and unique for every gateway");
			return response;
		}
		return response;	
	}
}
