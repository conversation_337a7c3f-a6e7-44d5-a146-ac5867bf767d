package com.nimble.irisservices.controller;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.zip.GZIPOutputStream;

import javax.validation.Valid;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.gson.Gson;
import com.nimble.irisservices.dto.JCCP;
import com.nimble.irisservices.dto.JCCPReport;
import com.nimble.irisservices.dto.JCCPResponseList;
import com.nimble.irisservices.dto.JCCPSummary;
import com.nimble.irisservices.dto.JCCPTemplateConfig;
import com.nimble.irisservices.dto.JCalibration;
import com.nimble.irisservices.dto.JF5Monitor;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JTemplate;
import com.nimble.irisservices.entity.CCP;
import com.nimble.irisservices.entity.CCPType;
import com.nimble.irisservices.entity.CCP_Checklist;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.exception.InvalidTemplateIdException;
import com.nimble.irisservices.exception.InvalidUsernameException;
import com.nimble.irisservices.service.ICcplistService;
import com.nimble.irisservices.service.IUserService;

@Controller
public class CcpController {
	private static final Logger log = LogManager.getLogger(CcpController.class);
	
	@Autowired
	@Lazy
	IUserService userService;
	
	@Autowired
	@Lazy
	ICcplistService ccpservice;
	
	//========get ccp_list================
	@RequestMapping(value="v3.0/ccpchecklist/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getCcpList(@PathVariable String autho,
			 @RequestParam(value = "typeid", defaultValue = "", required = false) String type_id) {
		
		JResponse response = new JResponse();
		
		try {			
			User user = userService.verifyAuthKey(autho);
			if(user.giveCompany().getCompanytype().getId() == 5 ) {
				List<CCP_Checklist> ccplist = ccpservice.getCcpChecklist(type_id);
				
				response.put("Status", 1);
				response.put("Msg","Success");
				response.put("ccpchecklist", ccplist);
			} else {
				response.put("Status", 0);
				response.put("Msg","Invalid company type");
			}
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		return response;	
	}
	
	//========get ccp type================
	@RequestMapping(value="v3.0/ccptype/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getCCPType(@PathVariable String autho,
			 @RequestParam(value = "typeid", defaultValue = "", required = false) String type_id) {
		
		JResponse response = new JResponse();
		
		try {			
			User user = userService.verifyAuthKey(autho);
			if(user.giveCompany().getCompanytype().getId() == 5 ) {
				List<CCPType> ccptype = ccpservice.getCCPType(type_id);
				
				response.put("Status", 1);
				response.put("Msg","Success");
				response.put("ccptype", ccptype);
			} else {
				response.put("Status", 0);
				response.put("Msg","Invalid company type");
			}
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		return response;	
	}	
	
	
	//========get ccp_list_user based================
	@RequestMapping(value="v3.0/ccp/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getCcpListByUser(@PathVariable String autho,@RequestParam("userid") String user_id,
							@RequestParam(value = "typeid", defaultValue = "", required = false) String type_id) {
		
		JResponse response = new JResponse();
		
		try {			
			User user = userService.verifyAuthKey(autho);
			if(user.giveCompany().getCompanytype().getId() == 5 ) {
				List<JCCP> ccplist = ccpservice.getCcpListByUser(user.getId(),user_id,type_id);
				//List<JCCP> ccplist = ccpservice.getEnabledCcpListByUser(user.getId(),user_id,type_id);
				
				response.put("Status", 1);
				response.put("Msg","Success");
				response.put("ccp", ccplist);
			} else {
				response.put("Status", 0);
				response.put("Msg","Invalid company type");
			}
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		return response;	
	}	

	//========save or update ccp================
	@RequestMapping(value="v3.0/saveOrUpdateccp/{autho}",method = RequestMethod.POST,  headers="Accept=application/json")
	@ResponseBody
	public JResponse saveORupdateCCP(@PathVariable String autho,  @ModelAttribute @Valid 
			JCCP ccp, BindingResult result) {
		JResponse response = new JResponse();		
		try {	
			if(result.hasErrors()) {
				response.put("Status",0);
				
				if(result.getFieldError("id") != null)
					response.put("Msg","id should not be empty");
				else if(result.getFieldError("user_id") != null)
					response.put("Msg",result.getFieldError("user_id").getDefaultMessage());
				else if(result.getFieldError("description") != null)
					response.put("Msg",result.getFieldError("description").getDefaultMessage());
				else if(result.getFieldError("type_id") != null)
					response.put("Msg",result.getFieldError("type_id").getDefaultMessage());
				return response;
			}			
			
			User user 	= userService.verifyAuthKey(autho);
			if(user.giveCompany().getCompanytype().getId() == 5 ) {
				ccpservice.saveOrUpdateCCP(ccp);			
				response.put("Status", 1);
				response.put("Msg","Success");
			} else {
				response.put("Status", 0);
				response.put("Msg","Invalid company type");
			}
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}  catch (InvalidUsernameException e) {
			log.error("invalid username");
			response.put("Status", 0);
			response.put("Msg","invalid username");
			return response;
		} catch (InvalidTemplateIdException e) {
			// TODO Auto-generated catch block
			response.put("Status", 0);
			response.put("Msg","InvalidTemplateIdException");
			e.printStackTrace();
		} catch(Exception e) {
			response.put("Status", 0);
			response.put("Msg","Exception occured");
			e.printStackTrace();
			log.info("saveORupdateCCP::::"+e.getMessage());
		}
		return response;
	}
	
	//========save or update ccplist================
	@RequestMapping(value="v3.0/saveOrUpdateccpList/{autho}",method = RequestMethod.POST,  headers="Accept=application/json")
	@ResponseBody
	public JResponse saveOrUpdateccpList(@PathVariable String autho, @RequestParam("userid") String userId,
			@RequestBody List<JCCP> jccplist, BindingResult result) {
		JResponse response = new JResponse();		
		try {	
			if(result.hasErrors()) {
				response.put("Status",0);
				
				if(result.getFieldError("id") != null)
					response.put("Msg","id should not be empty");
				else if(result.getFieldError("user_id") != null)
					response.put("Msg",result.getFieldError("user_id").getDefaultMessage());
				else if(result.getFieldError("description") != null)
					response.put("Msg",result.getFieldError("description").getDefaultMessage());
				else if(result.getFieldError("type_id") != null)
					response.put("Msg",result.getFieldError("type_id").getDefaultMessage());
				else if(result.getFieldError("template_id") != null)
					response.put("Msg",result.getFieldError("template_id").getDefaultMessage());
				return response;
			}			
			
			User user 	= userService.verifyAuthKey(autho);
			if(user.giveCompany().getCompanytype().getId() == 5 ) {
				//Long userid=(Long) (userId!= null?userId:user.getId());
				Long userid= Long.parseLong(userId);
				List<JCCPResponseList> ccpresponse =  ccpservice.saveOrUpdateCCPList(userid, jccplist);
				response.put("responselist", ccpresponse);					
				
			} else {
				response.put("Status", 0);
				response.put("Msg","Invalid company type");
			}
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}  catch (InvalidUsernameException e) {
			log.error("invalid username");
			response.put("Status", 0);
			response.put("Msg","invalid username");
			return response;
		} catch (InvalidTemplateIdException e) {
			// TODO Auto-generated catch block
			response.put("Status", 0);
			response.put("Msg","InvalidTemplateIdException");
			e.printStackTrace();
		} catch(Exception e) {
			response.put("Status", 0);
			response.put("Msg","Exception occured in saveOrUpdateccpList");
			e.printStackTrace();
			log.info("saveOrUpdateccpList::::"+e.getMessage());
		}
		return response;
	}
	
	//========update ccp================
		@RequestMapping(value="v3.0/saveorupdateccpsummary/{autho}",method = RequestMethod.POST,  headers="Accept=application/json")
		@ResponseBody
		public JResponse saveOrUpdateCCPSummary(@PathVariable String autho, 
				@RequestParam(value = "freqenable") String freqEnable,
				@ModelAttribute @Valid 
				JCCPSummary jccpsummary, BindingResult result) {
			/*
			 * This method is used to save the status of execution, of a template's particular time period.
			 * When the CCP inspection starts.
			 */
			
			JResponse response = new JResponse();		
			try {	
				if(result.hasErrors()) {
					response.put("Status",0);					
					if((result.getFieldError("template_id") != null))
						response.put("Msg",result.getFieldError("template_id").getDefaultMessage());
					else if(result.getFieldError("start_time") != null)
						response.put("Msg",result.getFieldError("start_time").getDefaultMessage());
					else if(freqEnable.equals("true") && result.getFieldError("end_time") != null )
						response.put("Msg",result.getFieldError("end_time").getDefaultMessage());
					else if(result.getFieldError("actual_start_datetime") != null)
						response.put("Msg",result.getFieldError("actual_start_datetime").getDefaultMessage());
					else if(result.getFieldError("status") != null)
						response.put("Msg",result.getFieldError("status").getDefaultMessage());					
					System.out.println("Error: "+result.getAllErrors());
					return response;
				}			
				
				User user 	= userService.verifyAuthKey(autho);
				
				if(user.giveCompany().getCompanytype().getId() == 5 ) {
					int status = ccpservice.saveOrUpdateCCPSummary(jccpsummary,freqEnable);	
					if(status == -1) {
						response.put("Status", 0);
						response.put("Msg","HACCP Record is already in completed/Skipped state.Cannot save");
					}else if(status ==-2){
						response.put("Status", 0);
						response.put("Msg","Actual start/end datetime is null");
					}else if(status ==-3){
						response.put("Status", 0);
						response.put("Msg","Actual start/end datetime is wrong");
					}else if(status ==-4){
						response.put("Status", 0);
						response.put("Msg","Given date/time slot does not exists");
					}else if(status ==-5){
						response.put("Status", 0);
						response.put("Msg","Given date/time slot already exists. Cannot update status again, for disabled frequency");
					}
					else{
						response.put("Status", 1);
						response.put("Msg","Success");
					}
				} else {
					response.put("Status", 0);
					response.put("Msg","Invalid company type");
				}
				
			} catch (InvalidAuthoException e) {
				System.out.println("in valid auth");
				response.put("Status", 0);
				response.put("Msg","invalid authentication key");
				return response;
			
			} catch(InvalidTemplateIdException e){
				log.error("in Template id");
				response.put("Status", 0);
				response.put("Msg","Invalid Template id");
				return response;
			}catch(Exception e) {
				response.put("Status", 0);
				response.put("Msg","Exception occured in saveOrUpdateCCPSummary");
				e.printStackTrace();
				log.info("saveOrUpdateCCPSummary::::"+e.getMessage());
			}
			return response;
		}
	
		

	//==========delete ccp========
	@RequestMapping(value = "v3.0/deleteccp/{ccp_id}/{autho}", method = RequestMethod.DELETE, headers = "Accept=application/json")
	public @ResponseBody JResponse deleteCCP(@PathVariable String autho, @PathVariable String ccp_id)       
	{
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			
			if(user.giveCompany().getCompanytype().getId() == 5 ) {
				List<CCP> ccplist = ccpservice.getCCPById(ccp_id);
				
				if(ccplist != null) {
					ccpservice.deleteCCP(ccplist);
					response.put("Status", 1);
					response.put("Msg","success");
					
				} else {
					response.put("Status", 0);
					response.put("Msg","No CCP Found");
				}
				
			} else {
				response.put("Status", 0);
				response.put("Msg","Invalid company type");
			}
			
		} catch (InvalidAuthoException e) {
			e.printStackTrace();
			response.put("Status", 1);
			response.put("Msg","success");

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg","UnExcepted Error in CCP");
			e.printStackTrace();
			log.info("deleteGateway::::"+e.getMessage());
		}
		return response; 
	}
		
	//========get ccp report================
	@RequestMapping(value="v3.0/ccpreport/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getCcpReport(@PathVariable String autho,
								  @RequestParam(value="userid", defaultValue = "", required = false) String user_id,
								  @RequestParam(value = "typeid", defaultValue = "", required = false) String type_id,
								  @RequestParam("fromtime") String fromtime,@RequestParam("totime") String totime,
								  @RequestParam("offset") String offset, 
								  @RequestParam("limit") String limit,
								  @RequestParam(value = "zip", defaultValue = "1", required = false) String zip,
								  @RequestParam(value = "templateid", defaultValue = "", required = false) String template_id,
	                              @RequestParam(value = "ccpsummaryid", defaultValue = "", required = false) String ccpsummary_id) {
		
		JResponse response = new JResponse();
		
		try {			
			User user = userService.verifyAuthKey(autho);
			if(user.giveCompany().getCompanytype().getId() == 5 ) {
				String userId=null;
				String authUserId = String.valueOf(user.getId());
				if(user_id!= null && !user_id.isEmpty())
					userId = user_id;
				else
					userId = authUserId;	
				List<JCCPReport> ccpreport = ccpservice.getCCPReport(userId,type_id,fromtime,totime,offset,limit,template_id,ccpsummary_id);
				
				response.put("Status", 1);
				response.put("Msg","Success");
				
				if(zip.equalsIgnoreCase("1"))
					response.put("ccpreports", zipContent(ccpreport));
				else
					response.put("ccpreports", ccpreport);
			} else {
				response.put("Status", 0);
				response.put("Msg","Invalid company type");
			}
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			response.put("Status", 0);
			response.put("Msg","invalid content for compress"); 
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg","Exception occured in getCcpReport");
			e.printStackTrace();
			log.info("getCcpReport::::"+e.getMessage());
		}
		return response;	
	}

	//========save or update ccp report================
	@RequestMapping(value="v3.0/ccpreport/{autho}/{starttime}/{endtime}/{startdate}/{enddatetime}/{freqenable}",method = RequestMethod.POST,  headers="Accept=application/json")
	@ResponseBody
	public JResponse saveORupdateCCPReport(@PathVariable String autho,@PathVariable String starttime,
									@PathVariable String endtime,@PathVariable String startdate,@PathVariable String enddatetime,@PathVariable String freqenable,																	
									@RequestBody List<JCCPReport> jccpreport,BindingResult result) {
		JResponse response = new JResponse();
		String startTime = null;
		String endTime   = null;
		
		
		try {	
			if(result.hasErrors()) {
				response.put("Status",0);				
				if(result.getFieldError("template_id") != null)
					response.put("Msg","template_id should not be empty");
				else if(result.getFieldError("description") != null)
					response.put("Msg",result.getFieldError("description").getDefaultMessage());
				else if(result.getFieldError("type_id") != null)
					response.put("Msg",result.getFieldError("type_id").getDefaultMessage());
				else if(result.getFieldError("datetime") != null)
					response.put("Msg",result.getFieldError("datetime").getDefaultMessage());
				else if(result.getFieldError("value") != null)
					response.put("Msg",result.getFieldError("value").getDefaultMessage());
				else if(result.getFieldError("monitor_id") != null)
					response.put("Msg",result.getFieldError("monitor_id").getDefaultMessage());
				return response;
			}	
		
			
			User user 	= userService.verifyAuthKey(autho);
			if(user.giveCompany().getCompanytype().getId() == 5 ) {
				
				List<JCCPResponseList> ccpresponse =  ccpservice.saveOrUpdateCCPReport(user.getId(),jccpreport,starttime,
						endtime,enddatetime,freqenable,startdate);	
				response.put("responselist", ccpresponse);				
				
			} else {
				response.put("Status", 0);
				response.put("Msg","Invalid company type");
			}
			
		} catch (InvalidAuthoException e) {
			log.error("invalid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}  catch (InvalidUsernameException e) {
			log.error("invalid auth");
			response.put("Status", 0);
			response.put("Msg","invalid username");
			return response;
		} catch(InvalidTemplateIdException e){
			log.error("invalid template id");
			response.put("Status", 0);
			response.put("Msg","invalid template id");
			return response;
		}catch(Exception e) {
			response.put("Status", 0);
			response.put("Msg","Exception occured in saveORupdateCCPReport");
			e.printStackTrace();
			log.info("saveORupdateCCPReport::::"+e.getMessage());
		}
		return response;
	}
	
	//========get ccp last report================
	@RequestMapping(value="v3.0/ccplastreport/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getCcpLastReport(@PathVariable String autho,@RequestParam("userid") String user_id,
							@RequestParam(value = "typeid", defaultValue = "", required = false) String type_id,
							@RequestParam(value = "templateid", defaultValue = "", required = false) String template_id) {
		
		JResponse response = new JResponse();
		
		try {			
			User user = userService.verifyAuthKey(autho);
			if(user.giveCompany().getCompanytype().getId() == 5 ) {
				List<JCCPReport> ccpreport = ccpservice.getCCPLastReport(user.getId(),user_id,type_id,template_id);
				
				response.put("Status", 1);
				response.put("Msg","Success");
				response.put("ccplastreport", ccpreport);
			} else {
				response.put("Status", 0);
				response.put("Msg","Invalid company type");
			}
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}
		return response;	
	}
	
	//========save or update ccp last report================
	@RequestMapping(value="v3.0/ccplastreport/{autho}",method = RequestMethod.POST,  headers="Accept=application/json")
	@ResponseBody
	public JResponse saveORupdateCCPLastReport(@PathVariable String autho, @RequestBody List<JCCPReport> jccpreport) {
		JResponse response = new JResponse();		
		try {	
			User user 	= userService.verifyAuthKey(autho);
			if(user.giveCompany().getCompanytype().getId() == 5 ) {
				List<JCCPResponseList> ccpresponse  = ccpservice.saveOrUpdateCCPLastReport(user.getId(), jccpreport);

				response.put("responselist", ccpresponse);
			} else {
				response.put("Status", 0);
				response.put("Msg","Invalid company type");
			}
		} catch (InvalidAuthoException e) {
			log.error("invalid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}  catch (InvalidUsernameException e) {
			log.error("invalid user name");
			response.put("Status", 0);
			response.put("Msg","invalid user name");
			return response;
		} catch(InvalidTemplateIdException e){
			log.error("invalid template id");
			response.put("Status", 0);
			response.put("Msg","invalid template id");
			return response;			
		}
		return response;
	}	

	//========save or update f5 monitor================
	@RequestMapping(value="v3.0/f5monitor/{autho}",method = RequestMethod.POST,  headers="Accept=application/json")
	@ResponseBody
	public JResponse saveORupdateF5Monitor(@PathVariable String autho, @ModelAttribute @Valid 
			JF5Monitor monitor, BindingResult result) {
		JResponse response = new JResponse();		
		
		try {	
			
			if(result.hasErrors()) {
				response.put("Status",0);
				
				if(result.getFieldError("id") != null)
					response.put("Msg","id should not be empty");
				else if(result.getFieldError("user_id") != null)
					response.put("Msg",result.getFieldError("user_id").getDefaultMessage());
				else if(result.getFieldError("name") != null)
					response.put("Msg",result.getFieldError("name").getDefaultMessage());
				else if(result.getFieldError("meid") != null)
					response.put("Msg",result.getFieldError("meid").getDefaultMessage());
				return response;
			}	
			
			User user 	= userService.verifyAuthKey(autho);
			List<User> userlist = userService.getUser(monitor.getUser_id(),-1);
			
			if(user.giveCompany().getCompanytype().getId() == 5 ) {
				if(userlist.size() > 0){
					ccpservice.saveOrUpdateF5Monitor(monitor);
					response.put("Status", 1);
					response.put("Msg","Success");
				}else {
					response.put("Status", 0);
					response.put("Msg","Invalid User Id");
				}
	
			} else {
				response.put("Status", 0);
				response.put("Msg","Invalid company type");
			}
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		}   catch (InvalidUsernameException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid username");
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg","Exception occured in saveORupdateF5Monitor");
			e.printStackTrace();
			log.info("saveORupdateF5Monitor::::"+e.getMessage());
		}
		return response;
	}
	
	
	//========get f5monitor================
	@RequestMapping(value="v3.0/f5monitor/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getF5Monitor(@PathVariable String autho,@RequestParam("userid") String user_id,
			@RequestParam("id") String id) {
		
		JResponse response = new JResponse();
		
		try {			
			User user = userService.verifyAuthKey(autho);
			if(user.giveCompany().getCompanytype().getId() == 5 ) {
				List<JF5Monitor> f5monitor = ccpservice.getF5Monitor(id,user_id);
				
				response.put("Status", 1);
				response.put("Msg","Success");
				response.put("f5monitor", f5monitor);
			} else {
				response.put("Status", 0);
				response.put("Msg","Invalid company type");
			}
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg","Exception occured in getF5Monitor");
			e.printStackTrace();
			log.info("getF5Monitor::::"+e.getMessage());
		}
		return response;	
	}
	
	//========get f5monitor================
	@RequestMapping(value="v3.0/f5monitorByName/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
	@ResponseBody
	public JResponse getF5MonitorByName(@PathVariable String autho, @RequestParam("name") String name, 
			@RequestParam(value = "meid", defaultValue = "", required = false) String meid) {
		
		JResponse response = new JResponse();
		
		try {			
			User user = userService.verifyAuthKey(autho);
			if(user.giveCompany().getCompanytype().getId() == 5 ) {
				List<JF5Monitor> f5monitor = ccpservice.getF5MonitorByName(name, meid, user);
				
				response.put("Status", 1);
				response.put("Msg","Success");
				response.put("f5monitor", f5monitor);
			} else {
				response.put("Status", 0);
				response.put("Msg","Invalid company type");
			}
			
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg","invalid authentication key");
			return response;
		} catch (ConstraintViolationException e) {
			e.printStackTrace();
			log.error("constraint violation ");
			response.put("Status", 0);
			response.put("Msg","F5 Monitor name already exist");
			return response;
		}catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg","Exception occured in getF5MonitorByName");
			e.printStackTrace();
			log.error("getF5MonitorByName::::"+e.getMessage());
		}
		return response;	
	}
	
	//========send EMAIL================
	@RequestMapping(value="v3.0/nimbleemailCCPreport",method = RequestMethod.POST,  headers="Accept=application/json")
	@ResponseBody
	public JResponse sendEmailCCPReport(@RequestParam("emailids") String emailids,@RequestParam("subject") String subject,
			@RequestParam("content") String content,@RequestParam("month") String month,@RequestParam("year") String year) {

		JResponse response = new JResponse();
		try{
			ccpservice.sendEmail(emailids,subject,content,month,year);
			response.put("Status", 1);
			response.put("Msg", "success");

			return response;
		}
		catch(Exception e)
		{
			log.error("sendEmailCCPReport::" +e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", e.getLocalizedMessage());
			return response;
		}
	}
	
	public static byte[] zipContent(Object obj) throws IOException
	{
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		GZIPOutputStream gzipOut = new GZIPOutputStream(baos);

		Gson gson = new Gson();
		String json = gson.toJson(obj);
		gzipOut.write(json.getBytes());

		gzipOut.close();
		//objectOut.close();
		byte[] bytes = baos.toByteArray();

		return bytes;
	}
	
	//========save user CCP template================
		@RequestMapping(value="v3.0/editCCPTemplate/{autho}",method = RequestMethod.POST,  headers="Accept=application/json")
		@ResponseBody
		public JResponse editCCPTemplate(@PathVariable String autho,  @ModelAttribute @Valid  
				JTemplate jTemplate, BindingResult result) {
			//TODO Need to implement
			JResponse response = new JResponse();
			
			try{
				if(result.hasErrors()) {
					
					response.put("Status",0);
						
					if(result.getFieldError("userId") != null)
						response.put("Msg",result.getFieldError("userId").getDefaultMessage());
					
					if(result.getFieldError("templateId") != null)
						response.put("Msg",result.getFieldError("templateId").getDefaultMessage());
					
					if(result.getFieldError("templateEnable") != null)
						response.put("Msg",result.getFieldError("templateEnable").getDefaultMessage());
					
					if(result.getFieldError("freqEnable") != null)
						response.put("Msg",result.getFieldError("freqEnable").getDefaultMessage());
					return response;
				}	
				
				User user 	= userService.verifyAuthKey(autho);
				
				
				if(user.giveCompany().getCompanytype().getId() == 5 ) {
										
						
						ccpservice.editTemplate(jTemplate);	
						
						response.put("Status", 1);
						response.put("Msg","Success");
					
					
				} else {
					response.put("Status", 0);
					response.put("Msg","Invalid company type");
				}
			}catch (InvalidAuthoException e) {
				log.error("invalid auth");
				response.put("Status", 0);
				response.put("Msg","invalid authentication key");
				return response;
			}  catch (InvalidUsernameException e) {
				log.error("invalid Username");
				response.put("Status", 0);
				response.put("Msg","invalid Username");
				return response;
			} catch (InvalidTemplateIdException e) {
				log.error("invalid TemplateId");
				response.put("Status", 0);
				response.put("Msg","invalid TemplateId");
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg","Exception occured in editCCPTemplate");
				e.printStackTrace();
				log.info("editCCPTemplate::::"+e.getMessage());
			}
			
			return response;
		}
		
		//========get ccp_list_user based================
		@RequestMapping(value="v3.0/ccpTemplateConfigs/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
		@ResponseBody
		public JResponse getCcpTemplateConfigs(@PathVariable String autho,@RequestParam("userid") String user_id,								
								@RequestParam(value = "template_enable", defaultValue = "", required = false) String template_enable) {
			
			JResponse response = new JResponse();
			
			try {			
				User user = userService.verifyAuthKey(autho);
				if(user.giveCompany().getCompanytype().getId() == 5 ) {
					List<JCCPTemplateConfig> ccpTemplateslist = ccpservice.getCcpTemplatesListByUser(user.getId(),user_id,template_enable);
					
					response.put("Status", 1);
					response.put("Msg","Success");
					response.put("ccpTemplates", ccpTemplateslist);
				} else {
					response.put("Status", 0);
					response.put("Msg","Invalid company type");
				}
				
			} catch (InvalidAuthoException e) {
				log.error("in valid auth");
				response.put("Status", 0);
				response.put("Msg","invalid authentication key");
				return response;
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg","Exception occured in getCcpTemplateConfigs");
				e.printStackTrace();
				log.info("getCcpTemplateConfigs::::"+e.getMessage());
			}
			return response;	
		}	
		
		//========get ccp_list_user based================
		@RequestMapping(value="v3.0/enabledccptemplate/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
		@ResponseBody
		public JResponse getEnabledCcpListByUser(@PathVariable String autho,@RequestParam("userid") String user_id,
								@RequestParam(value = "typeid", defaultValue = "", required = false) String type_id) {
			
			JResponse response = new JResponse();
			
			try {			
				User user = userService.verifyAuthKey(autho);
				if(user.giveCompany().getCompanytype().getId() == 5 ) {
					//List<JCCP> ccplist = ccpservice.getCcpListByUser(user.getId(),user_id,type_id);
					List<JCCP> ccplist = ccpservice.getEnabledCcpListByUser(user.getId(),user_id,type_id);
					response.put("Status", 1);
					response.put("Msg","Success");
					response.put("ccp", ccplist);
				} else {
					response.put("Status", 0);
					response.put("Msg","Invalid company type");
				}
				
			} catch (InvalidAuthoException e) {
				log.error("in valid auth");
				response.put("Status", 0);
				response.put("Msg","invalid authentication key");
				return response;
			}
			return response;	
		}	
		
		//========get ccp_list_user based================
		@RequestMapping(value="v3.0/ccpsummary/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
		@ResponseBody
		public JResponse getCcpSummary(@PathVariable String autho,@RequestParam(value ="userid", defaultValue = "", required = false) String user_id,
										@RequestParam(value = "startdate") String startdate,
										@RequestParam(value = "starttime") String starttime,
										@RequestParam(value = "enddate") String enddate,
										@RequestParam(value = "endtime") String endtime) {
			
			JResponse response = new JResponse();
			
			try {			
				User user = userService.verifyAuthKey(autho);
				
				if(user.giveCompany().getCompanytype().getId() == 5 ) {
					List<JCCPSummary> ccpSummarylist = ccpservice.getCCPSummary(user.getId(),user_id,startdate,
							starttime,enddate,endtime);
					if(ccpSummarylist!= null){
						response.put("Status", 1);
						response.put("Msg","Success");
						response.put("ccpSummary", ccpSummarylist);
					}else{
						response.put("Status", 0);
						response.put("Msg","Error:Empty list");
						
					}
					
				} else {
					response.put("Status", 0);
					response.put("Msg","Invalid company type");
				}
				
			} catch (InvalidAuthoException e) {
				log.error("in valid auth");
				response.put("Status", 0);
				response.put("Msg","invalid authentication key");
				return response;
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg","Exception occured in getCcpSummary");
				e.printStackTrace();
				log.info("getCcpSummary::::"+e.getMessage());
			}
			return response;	
		}	
		
		//========get ccp_list_user based================
		@RequestMapping(value="v3.0/ccpsummarystatus/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
		@ResponseBody
		public JResponse getCcpSummaryStatus(@PathVariable String autho,@RequestParam(value ="userid", defaultValue = "", required = false) String user_id,
										@RequestParam(value = "date") String date,
										@RequestParam(value = "template_id") String templateId,
										@RequestParam(value = "start_time") String startTime,
										@RequestParam(value = "end_time") String endTime) {
			
			JResponse response = new JResponse();
			
			try {			
				User user = userService.verifyAuthKey(autho);
			
				if(user.giveCompany().getCompanytype().getId() == 5 ) {
					// Check if status is completed
					Long rowsExists = ccpservice.getCCPSummaryStatus(templateId,date,startTime,endTime,3);//3->completed status
					
					boolean completedStatus = false;
					if(rowsExists>0)
						completedStatus = true;
					
					log.info("CCPSummary completed Status = "+ completedStatus+" for template_id= "+templateId+" start_date= "+date+" start_time= "+startTime+" end_time= "+endTime+" as on "+ new Date());
					response.put("Status", 1);
					response.put("Msg","Success");
					response.put("completedStatus", completedStatus);
				} else {
					response.put("Status", 0);
					response.put("Msg","Invalid company type");
				}
				
			} catch (InvalidAuthoException e) {
				
				response.put("Status", 0);
				response.put("Msg","invalid authentication key");
				return response;
			}catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg","Exception occured in getCcpSummaryStatus");
				e.printStackTrace();
				log.error("getCcpSummaryStatus::::"+e.getMessage());
			}
			return response;	
		}			

		//========Save or update Calibration details================
				@RequestMapping(value="v3.0/saveorupdatecalibration/{autho}",method = RequestMethod.POST,  headers="Accept=application/json")
				@ResponseBody
				public JResponse SaveorUpdateCalibration(@PathVariable String autho,  @ModelAttribute @Valid  
						JCalibration jcalibration, BindingResult result) {
					//TODO Need to implement
					JResponse response = new JResponse();
					
					try{
						if(result.hasErrors()) {
							
							response.put("Status",0);
								
							/*if(result.getFieldError("user_id") != null)
								response.put("Msg",result.getFieldError("user_id").getDefaultMessage());*/
							
							if(result.getFieldError("monitor_id") != null)
								response.put("Msg",result.getFieldError("monitor_id").getDefaultMessage());
							
							if(result.getFieldError("date_time") != null)
								response.put("Msg",result.getFieldError("date_time").getDefaultMessage());
							
							if(result.getFieldError("status") != null)
								response.put("Msg",result.getFieldError("status").getDefaultMessage());
							return response;
						}	
						
						User user 	= userService.verifyAuthKey(autho);
						
						
						if(user.giveCompany().getCompanytype().getId() == 5 ) {
														
								if(jcalibration.getUser_id() == null || jcalibration.getUser_id().isEmpty())
									jcalibration.setUser_id(String.valueOf(user.getId()));
								
								ccpservice.saveOrUpdateCalibration(jcalibration);	
								
								response.put("Status", 1);
								response.put("Msg","Success");
							
							
						} else {
							response.put("Status", 0);
							response.put("Msg","Invalid company type");
						}
					}catch (InvalidAuthoException e) {
						log.error("invalid auth");
						response.put("Status", 0);
						response.put("Msg","invalid authentication key");
						return response;
					}  catch (InvalidUsernameException e) {
						log.error("invalid Username");
						response.put("Status", 0);
						response.put("Msg","invalid Username");
						return response;
					} catch (Exception e) {
						response.put("Status", 0);
						response.put("Msg","Exception occured in SaveorUpdateCalibration");
						e.printStackTrace();
						log.error("SaveorUpdateCalibration::::"+e.getMessage());
					}
					
					return response;
				}
				//========get calibration details================
				@RequestMapping(value="v3.0/getcalibrationdetails/{autho}",method = RequestMethod.GET, headers="Accept=application/json")
				@ResponseBody
				public JResponse getcalibrationdetails(@PathVariable String autho,@RequestParam(value ="userid", defaultValue = "", required = false) String user_id,
												@RequestParam(value = "date_time", defaultValue = "", required = false) String datetime,
												@RequestParam(value = "monitor_id", defaultValue = "", required = false) String monitor_id,
												@RequestParam(value = "status", defaultValue = "", required = false) String status,
												@RequestParam(value = "id", defaultValue = "", required = false) String id) {
					
					JResponse response = new JResponse();
					
					try {			
						User user = userService.verifyAuthKey(autho);
						
						if(user.giveCompany().getCompanytype().getId() == 5 ) {
							
							JCalibration jcalib  =new JCalibration(user_id,monitor_id,null,datetime,status);
							List<JCalibration> calib_details = ccpservice.getCalibration(jcalib);
							
							if(calib_details != null){
								response.put("Status", 1);
								response.put("Msg","Success");
								response.put("JCalibration",calib_details);
							}else{
								response.put("Status", 0);
								response.put("Msg","Calibration details returned null");
							}							
							
						} else {
							response.put("Status", 0);
							response.put("Msg","Invalid company type");
						}
						
					} catch (InvalidAuthoException e) {
						
						response.put("Status", 0);
						response.put("Msg","invalid authentication key");
						return response;
					} catch (InvalidUsernameException e) {
						response.put("Status", 0);
						response.put("Msg","invalid username");
						return response;
					} catch (Exception e) {
						response.put("Status", 0);
						response.put("Msg","Exception occured in getcalibrationdetails");
						e.printStackTrace();
						log.error("getcalibrationdetails::::"+e.getMessage());
					}
					return response;	
	}		
	
	//========save or update ccp================
	@RequestMapping(value="v3.0/initializTemplateSlots",method = RequestMethod.POST,  headers="Accept=application/json")
	@ResponseBody
	public JResponse initializTemplateSlots() {
					JResponse response = new JResponse();		
					try {	
						System.out.println("initializTemplateSlots........");	
						ccpservice.initializeTemplateSlots();
						
						response.put("Status", 1);
						response.put("Msg","Success");
						return response;
						
						
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
						response.put("Status", 0);
						response.put("Msg","Exception in initializTemplateSlots");
						log.error("initializTemplateSlots::Exception: "+e.getLocalizedMessage());
					} 
					return response;
				}
}

