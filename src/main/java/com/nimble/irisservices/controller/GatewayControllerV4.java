package com.nimble.irisservices.controller;

import com.nimble.irisservices.dao.impl.GatewayDaoImplV4;
import com.nimble.irisservices.dto.GatewayV4Web;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JPacketReport;
import com.nimble.irisservices.dto.JPetprofile;
import com.nimble.irisservices.dto.JQrcDetails;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JUser;
import com.nimble.irisservices.dto.JWarrantyInfo;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.DeviceReplaced;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.PetSpecies;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Ordermap;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IPetSpeciesServices;
import com.nimble.irisservices.service.IUserServiceV4;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Controller
public class GatewayControllerV4 {

	private static final Logger log = LogManager.getLogger(GatewayControllerV4.class);

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IPetSpeciesServices petSpeciesServices;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;

	@Autowired
	GatewayDaoImplV4 GatewayDaoimplv4;

	@Autowired
	@Lazy
	INiomDataBaseService niomServiceV4;

	@Value("${nimbleservices.url}")
	private String nimbleservicesUrl;

	Helper _helper = new Helper();

	// v4.0/updategoalsettings/ - SIV
	@RequestMapping(value = "v4.0/updategoalsettings/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateGoalSettingsV4(@PathVariable String autho,
			@RequestParam(value = "gatewayid", required = true) String gatewayid,
			@RequestParam(value = "goalSetting", required = true) String goalSetting,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		log.info("Entering updateGoalSettingsV4 : " + autho);
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV4("authkey", autho);

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("Exception : updateGoalSettingsV4 :" + ex.getLocalizedMessage());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			int status = gatewayService.updateGoalSetting(gatewayid, goalSetting);

			if (status > 0) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "Failed");
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception occured while  updating GoalSettingsV4 ");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : updateGoalSettingsV4 :" + e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// getPetProfile V4 - currently not used
	@RequestMapping(value = "v4.0/getPetProfile/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getPetProfileV4(@PathVariable String autho,
			@RequestParam(value = "userid", defaultValue = "", required = false) String userid,
			@RequestParam(value = "gatewayid", defaultValue = "", required = false) String gatewayid,
			@RequestParam(value = "monitortype", defaultValue = "", required = false) String monitortype) {

		JResponse response = new JResponse();
		log.info("Entering getPetProfileV4 : " + autho);
		long userId = 0;
		long gatewayId = 0;
		int monitortypeid = 0;

		try {
			UserV4 usr = userServiceV4.verifyAuthV4("authkey", autho);
			if (usr == null) {
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				return response;
			}

			if (userid.isEmpty())
				userId = usr.getId();
			else
				userId = Long.valueOf(userid);

			if (!gatewayid.isEmpty())
				gatewayId = Long.valueOf(gatewayid);
			else
				gatewayId = 0;

			if (!monitortype.isEmpty())
				monitortypeid = Integer.valueOf(monitortype);
			else
				monitortypeid = 0;

			response = gatewayServiceV4.getJPetprofilesByUserV4(userId, gatewayId, monitortypeid);

		} catch (Exception ex) {
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			response.put("Error", ex.getLocalizedMessage());
			log.error("Exception : getPetProfileV4 : " + ex.getLocalizedMessage());
		}

		return response;
	}

	
	// ========save or update gateway================
	@RequestMapping(value = "v4.0/saveorupdatePetProfile/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveorupdatePetProfileV4(@PathVariable String autho, @RequestBody List<JPetprofile> jpetprofiles,
			@RequestParam(value = "sttime", defaultValue = "", required = false) String stTime) {
		JResponse response = new JResponse();
		log.info("Entering saveorupdatePetProfile : " + autho);
		try {
			if (jpetprofiles != null) {

				Map<String, String> map = new HashMap<String, String>();

				try {
					map = userServiceV4.getUserId_cmpIdByAuthV2(autho);
				} catch (InvalidAuthoException ex) {
					response.put("Status", 0);
					response.put("Msg", "invalid authkey");
					response.put("Error", ex.getLocalizedMessage());
					log.error("Exception while getting user for authkey : " + autho);
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				long userId = Long.valueOf(map.get("user_id"));
				long cmpType_id = Long.valueOf(map.get("cmpType_id"));

				if (cmpType_id == 3) {

					int status = 0;
					for (JPetprofile jpetprofile : jpetprofiles) {
						PetProfile petProfile = null;
						boolean GatewayAva = false;

						String gateway_name = jpetprofile.getName();
						Pattern special = Pattern.compile("[!@#$%&*()+=|<>?{}.,\\[\\]~'\"]");
						Matcher hasSpecial = special.matcher(gateway_name);
						if (hasSpecial.find()) {
							log.info("Gateway name contains special chars");
							response.put("Status", 0);
							response.put("Msg", RegisterUserError.ER009);
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}

						List<JGateway> gateways = gatewayServiceV4.getGatewayV4(null, null, null, null, userId, null);
						for (JGateway jGateway : gateways) {

							if (!jpetprofile.getGateway_id().equals(Long.toString(jGateway.getId()))) {
								GatewayAva = true;
								if (jpetprofile.getName().equalsIgnoreCase(jGateway.getName())) {
									response.put("Status", 0);
									response.put("Msg", RegisterUserError.petNameUserMessage);
									response.put("Return Time", System.currentTimeMillis());
									return response;

								}
							}
						}

						if (jpetprofile != null) {
							if (!jpetprofile.getGateway_id().isEmpty() && !jpetprofile.getAge().isEmpty()
									&& !jpetprofile.getBreed().isEmpty() && !jpetprofile.getName().isEmpty()
									&& !jpetprofile.getSex().isEmpty()) {

								Date birth_date = gatewayService.getBirthDate("YEAR", jpetprofile.getAge());
								SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
								String date = sf.format(birth_date);

								PetSpecies species = petSpeciesServices
										.getPetSpeciesByName(jpetprofile.getSpecieName());

								if (species == null) {
									species = petSpeciesServices.getPetSpecies(1L);
								}

								status = gatewayServiceV4.saveorupdatePetprofileV4(jpetprofile, userId, GatewayAva,
										date, species.getId());

							} else {
								log.info("saveORupdatePetprofile: Error:mandatory fields are missing");

								response.put("Status", 0);
								response.put("Msg",
										"Enter all the mandatory fields like gatewayid,name,age,sex and breed");
								response.put("Return Time", System.currentTimeMillis());
								return response;
							}

						}
//						else {
//							log.info("saveORupdatePetprofile: Error: Input JPetprofile is null");
//							response.put("Status", 0);
//							response.put("Msg", "Enter all the mandatory fields like gatewayid,name,age,sex and breed");
//							response.put("Return Time", System.currentTimeMillis());
//							return response;
//						}
					}

					log.info("Enter Into Gateway");
					if (status == 1) {
						for (JPetprofile jpetprofile : jpetprofiles) {

							log.info("Enter Into Gateway");
							boolean issuccess = gatewayService.updateGatewayName(jpetprofile.getName(),
									jpetprofile.getGateway_id());
							log.info("GatewayName updated with respect to petname");

						}
						response.put("Status", 1);
						response.put("Msg", "success");
						response.put("Msg", "All profiles are saved Successfully");
					} else {
						response.put("Status", 0);
						response.put("Msg", "Error, Not saved");
					}

				} else {
					response.put("Status", 0);
					response.put("Msg", "Invalid company type");
				}
			} else {
				response.put("Status", 0);
				response.put("Msg", "Empty input profile list");
			}

		} catch (DataIntegrityViolationException e) {
			log.error("saveorupdatePetProfile::::" + e.getMessage());
			response.put("Status", 0);
			response.put("Msg", "DataIntegrityViolationException");
			response.put("Error", e.getLocalizedMessage());
		} catch (Exception e) {
			response.put("Status", 0);
			log.error("saveorupdatePetProfile::::" + e.getLocalizedMessage());
			response.put("Msg", "UnExcepted Error in pet profile");
			response.put("Error", e.getLocalizedMessage());

		}
		log.info("Exit saveorupdatePetProfile");
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	// Used in web
	// allGatewaysListByFilter for web - Savitha
	@RequestMapping(value = "v4.0/allgatewayslistbyfilter/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse allGatewaysListByFilter(@PathVariable String autho,
			@RequestParam(value = "skey", required = false) String sKey,
			@RequestParam(value = "svalue", required = false) String sValue,
			@RequestParam(value = "ftype", defaultValue = "equals", required = false) String fType,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "desc", required = false) String otype,
			@RequestParam(value = "okey", required = false) String oKey) {
		log.info("Entered into allGatewaysListByFilter : " + sKey + " - " + sValue);
		JResponse response = new JResponse();
		try {

			response = gatewayServiceV4.getGatewayListByFilter(sKey, sValue, fType, otype, offset, limit, oKey,
					response);

			if ((Integer) response.getResponse().get("Status") > 0) {
				List<GatewayV4Web> gatewayV4WebList = (List<GatewayV4Web>) response.getResponse().get("gatewaylist");
				gatewayV4WebList = gatewayServiceV4.setReplacedDevice(gatewayV4WebList);
				response.put("gatewaylist", gatewayV4WebList);
			}
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting allGatewaysListByFilter");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : allGatewaysListByFilter : " + e.getLocalizedMessage());
		}
		return response;
	}

	// Used in web
	// getGateways by gateway id for web - Savitha
	@RequestMapping(value = "v4.0/getgatewaybyidweb/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getGatewayByIdWeb(@PathVariable String autho,
			@RequestParam(value = "gatewayid", required = true) String gatewayId) {
		log.info("Entered into getGatewaysWithGatewayId : " + gatewayId);
		JResponse response = new JResponse();
		try {
			response = gatewayServiceV4.getGatewayByIdWeb(gatewayId, response);
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting getGatewayByIdWeb");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : getGatewayByIdWeb : " + e.getLocalizedMessage());
		}
		return response;
	}

	// Used in web
	// Get user pet profile for web - Savitha
	@RequestMapping(value = "v4.0/listuserpetprofileweb/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse listuserpetprofileweb(@PathVariable String autho,
			@RequestParam(value = "userid", required = false) String userId) {

		log.info("Entered into listUserPetProfileWeb : " + userId);
		JResponse response = new JResponse();
		UserV4 usr = null;
		try {
			usr = userServiceV4.verifyAuthV4("authkey", autho);
		} catch (InvalidAuthoException e) {
			log.error("Invalid Authkey :" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Invalid Authkey!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}

		try {
			response = gatewayServiceV4.listUserPetProfileWeb(usr.getId(), response);
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting listUserPetProfileWeb");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : listUserPetProfileWeb : " + e.getLocalizedMessage());
		}
		return response;
	}

	// Update current fota version in niom inventory table --> Savitha
	@RequestMapping(value = "v4.0/updateinventoryfotaversion", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse updateInventoryFotaVersion(@RequestParam String meid, @RequestParam String fota_version) {
		log.info("Update inventory fota version - " + meid);
		JResponse response = new JResponse();
		try {
			boolean isSuccess = niomServiceV4.updateInventoryFotaVersion(meid, fota_version);
			if (isSuccess) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				log.error("Error occurred in update inventory fota version!!!");
				response.put("Status", 0);
				response.put("Msg", "Error occurred!!!");
			}
		} catch (Exception e) {
			log.error("Error occurred in update inventory fota version!!!");
			response.put("Status", 0);
			response.put("Msg", "Exception occurred!!!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	// Currently not in use
	// Used in web
	// Update current meid in niom ordermap table --> Savitha
//	@RequestMapping(value = "v4.0/updatenewmeidinordermap/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
//	@ResponseBody
//	public JResponse updateNewMeidInOrderMap(@PathVariable String autho, @RequestParam long user_id,
//			@RequestParam String new_meid, @RequestParam String old_meid) {
//		log.info("Update new meid in niom ordermap table - " + new_meid);
//
//		JResponse response = new JResponse();
//		UserV4 usr = null;
//		boolean updateMeid = false;
//		try {
//			usr = userServiceV4.verifyAuthV4("authkey", autho);
//			if (user_id == usr.getId())
//				updateMeid = true;
//			else
//				updateMeid = false;
//		} catch (InvalidAuthoException e) {
//			log.error("Invalid Authkey :" + e.getLocalizedMessage());
//			response.put("Status", 0);
//			response.put("Msg", "Invalid Authkey!");
//			response.put("Error", e.getLocalizedMessage());
//			return response;
//		}
//
//		if (updateMeid) {
//			try {
//				boolean isSuccess = gatewayServiceV4.updateNewMeidInOrderMap(user_id, new_meid, old_meid);
//				if (isSuccess) {
//					response.put("Status", 1);
//					response.put("Msg", "Success");
//				} else {
//					log.error("Failed update new meid in niom ordermap table!!!");
//					response.put("Status", 0);
//					response.put("Msg", "Failed!!!");
//				}
//			} catch (Exception e) {
//				log.error("Error occurred in update new meid in niom ordermap table!!!");
//				response.put("Status", 0);
//				response.put("Msg", "Exception occurred!!!");
//				response.put("Error", e.getLocalizedMessage());
//				return response;
//			}
//		} else {
//			log.error("UserId mismatch to given authkey while updating new meid in niom ordermap table!!!");
//			response.put("Status", 0);
//			response.put("Msg", "UserId mismatch!!!");
//			return response;
//		}
//		return response;
//	}

	// Get user id by username and validate meid give is mapped to any user -->
	// Savitha
	@RequestMapping(value = "v4.0/getuseridvalidatemeid", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getuseridforniom(@RequestParam String meid, @RequestParam String username) {
		log.info("Get userid and validate meid for niom - " + meid);
		JResponse response = new JResponse();
		long userId = 0;
		try {
			userId = gatewayServiceV4.getUserIdForGateway(meid, username);
			if (userId != 0) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("UserId", userId);
			} else {
				response.put("Status", 0);
				response.put("Msg",
						"Gateway and user mapping is incorrect/ Gateway is not mapped to any user - " + meid);
				response.put("UserId", 0);
			}
		} catch (Exception e) {
			log.error("Error occurred in userid and validate meid for niom - " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception occurred!!!");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	// To add user and gateway details to Recall device table - Balaji
	@RequestMapping(value = "v4.0/recallreplacegateway/{auth}", method = RequestMethod.POST, headers = "Accept=application/json")
	public @ResponseBody JResponse addReplaceGateway(@PathVariable String auth,
			@RequestParam(value = "action") String action, @RequestParam(value = "gatewayid") long gatewayId,
			@RequestParam(value = "enable") boolean enable) {
		log.info(" Entered into addReplaceGateway :: gateway ID : " + gatewayId);
		JResponse jResponse = new JResponse();

		/*
		 * action : 0 default 1 Replace 2 Recall 3 Replacement Success 4 Recall Success
		 */

		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);

			} catch (Exception e) {
				jResponse.put("Status", 0);
				jResponse.put("Msg", "Invalid Authkey");
				return jResponse;
			}

			long userId = 0;
			String meid = "NA";
			try {
				JUser userGateway = gatewayServiceV4.getUsergatewaydetails(gatewayId);
				if (userGateway != null) {
					Set<Gateway> gateways = userGateway.giveGateways();
					userId = userGateway.getId();
					Gateway[] setValue = gateways.toArray(new Gateway[gateways.size()]);
					meid = setValue[0].getMeid();
				}
			} catch (Exception e) {
				jResponse.put("Status", 0);
				jResponse.put("Msg", "Invalid GatewayId");
				return jResponse;
			}

			if (enable) {

				DeviceReplaced deviceReplaced = new DeviceReplaced();

				deviceReplaced.setUserId(userId);
				deviceReplaced.setMeid(meid);
				deviceReplaced.setInsertedDate(_helper.getCurrentTimeinUTC());

				boolean enableReplace = false;
				if (action.equalsIgnoreCase("recall")) {
					deviceReplaced.setIsReplaced(2);
				} else {
					deviceReplaced.setIsReplaced(1);
					enableReplace = niomServiceV4.enableReplaceInOrderMapByMeid(meid, userId);
				}

				boolean savedDeviceReplaced = gatewayServiceV4.saveDeviceReplaced(deviceReplaced);

				log.info(" OrderMap isReplaced status Changed : " + enableReplace);
				log.info(" Saved device data : " + deviceReplaced);

				if (savedDeviceReplaced) {
					jResponse.put("Status", 1);
					jResponse.put("Msg", "Success");
				} else {
					jResponse.put("Status", 0);
					jResponse.put("Msg", "Not updated");
				}
			} else {

				boolean isGatewayRemoved = gatewayServiceV4.removeDeviceReplaced(userId, meid);
				boolean disableReplace = niomServiceV4.disableReplaceInOrderMapByMeid(meid, userId);
				if (isGatewayRemoved) {
					jResponse.put("Status", 1);
					jResponse.put("Msg", "Success");
				} else {
					jResponse.put("Status", 0);
					jResponse.put("Msg", "Not updated");
				}

			}

			return jResponse;
		} catch (Exception e) {
			log.error(" Error in addReplaceGateway : " + e.getLocalizedMessage());
			jResponse.put("Status", 0);
			jResponse.put("Msg", "Failed");
			jResponse.put("Error", e.getLocalizedMessage());
			return jResponse;
		}
	}

	// -Balaji
	@RequestMapping(value = "v4.0/warrantyinfo/{auth}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse getWarrantyInfo(@PathVariable String auth,
			@RequestParam(value = "os", required = false) String os,
			@RequestParam(value = "app_ver", required = false) String appVer) {

		log.info(" Entered into getWarrantyInfo :: auth : " + auth);
		JResponse jResponse = new JResponse();
		try {

			UserV4 user = null;
			try {
				user = userServiceV4.verifyAuthV4("authkey", auth);
			} catch (Exception e) {
				jResponse.put("Status", 0);
				jResponse.put("Msg", "Invalid Auth");
				return jResponse;
			}
			String gatewayImg = "NA";
			boolean allWarrantyClaimed = true;
			try {
				List<JGateway> jGatewayList = gatewayService.getJGatewayByUser(user.getId(), "1");

				if (jGatewayList.isEmpty()) {
					jResponse.put("Status", 1);
					jResponse.put("Msg", "Register your device to claim the Warranty");
					jResponse.put("all_warranty_claimed", allWarrantyClaimed);
				} else {

					List<JWarrantyInfo> jWarrantyInfoList = new ArrayList<JWarrantyInfo>();

					for (JGateway gateway : jGatewayList) {

						JWarrantyInfo jWarrantyInfo = new JWarrantyInfo();

						if (!gateway.isShowOrderId()) {
							gatewayImg = gatewayServiceV4.getGatewayImg(gateway.getId());

							jWarrantyInfo.setGateway_id(gateway.getId());
							jWarrantyInfo.setGateway_name(gateway.getName());
							jWarrantyInfo.setGateway_img(gatewayImg);
							jWarrantyInfoList.add(jWarrantyInfo);
						}
					}

					if (jWarrantyInfoList.isEmpty()) {
						jResponse.put("Status", 1);
						jResponse.put("Msg", "You had claimed all the Warranties successfully.");
						jResponse.put("all_warranty_claimed", allWarrantyClaimed);
					} else {
						jResponse.put("Status", 1);
						jResponse.put("Msg", "Success");
						jResponse.put("gateway_list", jWarrantyInfoList);
						jResponse.put("all_warranty_claimed", false);
					}

				}

			} catch (Exception e) {
				jResponse.put("Status", 0);
				jResponse.put("Msg", "Error");
				return jResponse;
			}
			return jResponse;
		} catch (Exception e) {
			log.error(" Error in addReplaceGateway : " + e.getLocalizedMessage());
			jResponse.put("Status", 0);
			jResponse.put("Msg", "Error");
			return jResponse;
		}
	}

	// Used in web
	// Unmapped gateways - Savitha
	@RequestMapping(value = "v4.0/unmappedgatewayslistbyfilter/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse allUnMappedGateways(@PathVariable String autho,
			@RequestParam(value = "skey", required = false) String sKey,
			@RequestParam(value = "svalue", required = false) String sValue,
			@RequestParam(value = "ftype", defaultValue = "equals", required = false) String fType,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "desc", required = false) String otype,
			@RequestParam(value = "okey", required = false) String oKey) {
		log.info("Entered into allUnMappedGatewaysListByFilter : " + sKey + " - " + sValue);
		JResponse response = new JResponse();

		try {
			response = gatewayServiceV4.getUnmappedGatewayListByFilter(sKey, sValue, fType, otype, offset, limit, oKey,
					response);
			return response;
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting allUnMappedGatewaysListByFilter");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : allUnMappedGatewaysListByFilter : " + e.getLocalizedMessage());
		}
		return response;
	}

	// Get Meid exist in DB for BLT --> Savitha
	@RequestMapping(value = "v4.0/isdeviceexist", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse checkDeviceExist(@RequestParam("meid") String meid) {
		log.info("Entered Is Device Exist with meid = " + meid);
		JResponse response = new JResponse();
		if (meid == null || meid.isEmpty()) {
			response.put("Status", 0);
			response.put("Msg", "Meid must not be empty!");
			return response;
		}

		try {
			List<JGateway> gateway = gatewayService.checkDeviceExist(meid);
			if (gateway != null) {
				response.put("Status", 0);
				response.put("Msg", "Device Exist in DB!");
				response.put("Device_Details", gateway);
			} else {
				response.put("Status", 1);
				response.put("Msg", "Device not Exist!");
			}
		} catch (Exception e) {
			log.error("Exception in checkDeviceExist : ", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occurred!" + e.getLocalizedMessage());
			return response;
		}
		return response;
	}

	@RequestMapping(value = "v4.0/getQrcDetails", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getQrcDetails(@RequestParam("qrc") String qrc,
			@RequestParam(value = "order_id", defaultValue = "NA", required = false) String order_id) {
		log.info("Entered getQrcDetails: ");

		JResponse response = new JResponse();
		try {
			boolean isUserRegistered = false;
			JQrcDetails qrcDetails = niomServiceV4.getFirmwareAndFotaVersionDeviceModelNumber(qrc); // inventory

			if (!qrcDetails.getProduct_name().toLowerCase().contains("tempcube")) {
				isUserRegistered = gatewayService.isUserRegistered(qrc);

				String deviceModelNo = qrcDetails.getDevicemodelnumber();
				JQrcDetails modelDetails = gatewayService.getOldModelNameandModelId(deviceModelNo);

				qrcDetails.setOld_model_name(modelDetails.getOld_model_name());
				qrcDetails.setOld_model_id(modelDetails.getOld_model_id());

				if (isUserRegistered) {
					JQrcDetails additionalDetailsFromGateway = gatewayService.getQrcDetails(qrc);
					
					Ordermap mappedOrder = niomServiceV4.getMappedOrderByMeidSql(additionalDetailsFromGateway.getMeid());
					if (mappedOrder != null) {
						qrcDetails.setOrder_id(mappedOrder.getOrder_id());
						qrcDetails.setOrder_sku(mappedOrder.getOrderSku());
						qrcDetails.setExternal_order_id(mappedOrder.getExternal_order_id());
						qrcDetails.setQuantity(Integer.parseInt(mappedOrder.getQuantity()));
						qrcDetails.setOrder_total(mappedOrder.getOrder_total());
						qrcDetails.setOrder_date(mappedOrder.getOrder_date());
						qrcDetails.setRegistered_date(mappedOrder.getRegistered_date());
						qrcDetails.setOrder_account_type(mappedOrder.getOrder_account_type());
					}

					qrcDetails.setNew_model_name(additionalDetailsFromGateway.getNew_model_name());
					qrcDetails.setNew_model_id(additionalDetailsFromGateway.getNew_model_id());
					qrcDetails.setUser_name(additionalDetailsFromGateway.getUser_name());
					qrcDetails.setEmail(additionalDetailsFromGateway.getEmail());
					qrcDetails.setPhone_no(additionalDetailsFromGateway.getPhone_no());
					qrcDetails.setCx_name(additionalDetailsFromGateway.getCx_name());
					qrcDetails.setMeid(additionalDetailsFromGateway.getMeid());
					qrcDetails.setDevice_name(additionalDetailsFromGateway.getDevice_name());

				} else {
					// User not Registered
					qrcDetails.setNew_model_name(modelDetails.getOld_model_name());
					qrcDetails.setNew_model_id(modelDetails.getOld_model_id());
				}
			} else {
				String url = nimbleservicesUrl + "/v4.0/getqrcdetails?qrc=" + qrc + "&niom_device_model="
						+ qrcDetails.getDevicemodelnumber();
				String endPointResponse = _helper.httpGETRequest(url, null);
				org.json.JSONObject overallResponseObj = new org.json.JSONObject(endPointResponse);
				org.json.JSONObject responseObj = (org.json.JSONObject) overallResponseObj.get("response");
				org.json.JSONObject deviceDetailsObj = (org.json.JSONObject) responseObj.get("deviceDetails");
				qrcDetails.setOld_model_id(Long.parseLong(deviceDetailsObj.getString("old_model_id")));
				qrcDetails.setOld_model_name(deviceDetailsObj.getString("old_model_name"));
				qrcDetails.setNew_model_id(Long.parseLong(deviceDetailsObj.getString("new_model_id")));
				qrcDetails.setNew_model_name(deviceDetailsObj.getString("new_model_name"));
				qrcDetails.setFota_version(deviceDetailsObj.getString("fota_version"));
				qrcDetails.setUser_name(deviceDetailsObj.getString("user_name"));
				qrcDetails.setEmail(deviceDetailsObj.getString("email"));
				qrcDetails.setPhone_no(deviceDetailsObj.getString("phone_no"));
				qrcDetails.setCx_name(deviceDetailsObj.getString("cx_name"));
			}

			qrcDetails.setRegistration_status(isUserRegistered);
			
			String orderDetails = (order_id.equals("NA")) ? "NA" : niomServiceV4.getDateTimeByOrderId(order_id);
			qrcDetails.setPurchase_date("NA");
			
			if( !order_id.equalsIgnoreCase("NA") ) {
				
				JQrcDetails qrcInfo = niomServiceV4.getOrderAndUserInfo( order_id );
				if( qrcInfo != null ) {
					qrcDetails.setFirst_name( qrcInfo.getFirst_name() );
		        	qrcDetails.setLast_name( qrcInfo.getLast_name() );
		        	qrcDetails.setRegistered_date( qrcInfo.getRegistered_date() );
				}
				
			}
			
//			List<Ordermap> orderMap = niomServiceV4.getMappedOrderByOrderId(order_id);
			
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("DeviceDetails", qrcDetails);

		} catch (Exception e) {
			log.error("Exception in getQrcDetails: " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failure");
		}

		return response;
	}

	/**
	 * Retrieves reports based on the provided MEID and timestamp.
	 *
	 * @param meid      The MEID of the device for which reports are requested.
	 * @param startTime The timestamp indicating the starting point for retrieving reports.
	 * @param seconds   The number of seconds from the timestamp to consider for report retrieval.
	 * @param packetId  The packet ID to filter the reports, default is 0 if not provided.
	 * @return A JResponse object containing the status and details of the requested reports.
	 */
	@GetMapping(value = "v5.0/getreportsbymeid", headers = "Accept=application/json")
	@ResponseBody
	public JResponse getReportsByMeId(@RequestParam("meid") String meid,
									  @RequestParam(value = "timestamp") String startTime, @RequestParam(value = "seconds") int seconds,
									  @RequestParam(value = "packet_id", defaultValue = "0", required = false) String packetId) {

		log.info("Entered getReportsByMeId: meid");
		JResponse response = new JResponse();

		try {
			List<JPacketReport> packetReportList = gatewayService.getPacketReport(meid, startTime, seconds);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("report_list", packetReportList);
		}
		catch (Exception e) {
			log.error("Exception in getReportsByMeId: {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failure");
			response.put("report_list", new ArrayList<>());
		}

		return response;
	}

	/**
	 * Retrieves newqrc based on the provided oldqrc.
	 *
	 * @param oldQrc The oldqrc of the device.
	 * @return A JResponse object containing the newqrc.
	 */
	@GetMapping(value = "v5.0/getnewqrc", headers = "Accept=application/json")
	public @ResponseBody JResponse getNewQrc(@RequestParam("oldqrc") String oldQrc) {

		JResponse response = new JResponse();

		try {
			String newQrc = "NA";
			newQrc = gatewayService.getNewQrc(oldQrc);

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("newqrc", newQrc);
		} catch (Exception e) {
			log.error("Exception occurred in getNewQrc : {}", e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Error", e.getLocalizedMessage());
		}

		return response;
	}
}
