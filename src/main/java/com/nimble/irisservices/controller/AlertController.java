package com.nimble.irisservices.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JAlert;
import com.nimble.irisservices.dto.JAlertV4;
import com.nimble.irisservices.dto.JAssetDescription;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.service.IAlertService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class AlertController {
	private static final Logger log = LogManager.getLogger(AlertController.class);

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	IUserService userService;

	@Autowired
	IAlertService alertService;

	@Autowired
	IReportService reportService;

	@Autowired
	ICompanyService companyService;

	// ========get alerts ================
	@RequestMapping(value = "v3.0/alert/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetSummary(@PathVariable String autho, @RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("alerttypeid") String alerttypeid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("deliquencystatus") String deliquencystatus,
			@RequestParam(value = "nodeid", defaultValue = "", required = false) String nodeid,
			@RequestParam(value = "id", defaultValue = "", required = false) String id) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());

			List<JAlert> alerts = alertService.getalerts(groupid, subgroupid, gatewayid, alerttypeid, user.getId(),
					deliquencystatus, cmp_cfg.getTemperatureunit(), "", "", nodeid, id);
			JAssetDescription assetDescrip = reportService.getAssetDescription(user.giveCompany().getId());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetdescription", assetDescrip);
			response.put("Alters", alerts);
			return response;
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
	}

	// Used in web
	// ========get alerts Version 2================
	@RequestMapping(value = "v3.0/alertV2/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAssetSummaryV2(@PathVariable String autho, @RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("alerttypeid") String alerttypeid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("deliquencystatus") String deliquencystatus,
			@RequestParam("fromtime") String fromtime, @RequestParam("totime") String totime,
			@RequestParam(value = "nodeid", defaultValue = "", required = false) String nodeid,
			@RequestParam(value = "id", defaultValue = "", required = false) String id) {
		JResponse response = new JResponse();
		try {
			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());

			List<JAlert> alerts = alertService.getalerts(groupid, subgroupid, gatewayid, alerttypeid, user.getId(),
					deliquencystatus, cmp_cfg.getTemperatureunit(), fromtime, totime, nodeid, id);
			JAssetDescription assetDescrip = reportService.getAssetDescription(user.giveCompany().getId());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetdescription", assetDescrip);
			response.put("Alters", alerts);
			return response;
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
	}

	// Used in web
	// ========get alerts Version 2================
		@RequestMapping(value = "v3.0/furbitalertV2/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
		@ResponseBody
		public JResponse getFurbitalertV2(@PathVariable String autho, @RequestParam("groupid") String groupid,
				@RequestParam("subgroupid") String subgroupid, @RequestParam("alerttypeid") String alerttypeid,
				@RequestParam("gatewayid") String gatewayid, @RequestParam("deliquencystatus") String deliquencystatus,
				@RequestParam("fromtime") String fromtime, @RequestParam("totime") String totime,
				@RequestParam(value = "nodeid", defaultValue = "", required = false) String nodeid,
				@RequestParam(value = "id", defaultValue = "", required = false) String id) {
			JResponse response = new JResponse();
			try {
				Map<String, String> resMaps = userServiceV4.getUserId_cmpIdByAuth(autho);
				long cmp_id = Long.valueOf(resMaps.get("cmp_id"));
				long userId = Long.valueOf(resMaps.get("user_id"));
				String tempUnit = resMaps.get("tempunit");
				

				List<JAlert> alerts = alertService.getFurbitalerts(groupid, subgroupid, gatewayid, alerttypeid, userId,
						deliquencystatus,tempUnit, fromtime, totime, nodeid, id);
				JAssetDescription assetDescrip = reportService.getAssetDescription(cmp_id);
				
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("assetdescription", assetDescrip);
				response.put("Alters", alerts);
				return response;
			} catch (InvalidAuthoException e) {
				log.error("in valid auth");
				response.put("Status", 0);
				response.put("Msg", "invalid authentication key");
				return response;
			}
		}

		
	// ========get alerts ================
	@RequestMapping(value = "v3.0/alertV3/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAlertSummary(@PathVariable String autho, @RequestParam("groupid") String groupid,
			@RequestParam("subgroupid") String subgroupid, @RequestParam("alerttypeid") String alerttypeid,
			@RequestParam("gatewayid") String gatewayid, @RequestParam("deliquencystatus") String deliquencystatus,
			@RequestParam(value = "nodeid", defaultValue = "", required = false) String nodeid,
			@RequestParam(value = "id", defaultValue = "", required = false) String id) {
		JResponse response = new JResponse();
		try {

//				SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//				Date dateobj = new Date();
//
//				Calendar cal = Calendar.getInstance();
//				
//				String currentDate = df.format(dateobj);
////				Date today = cal.getTime();
//				cal.add(Calendar.DATE, -1); // to get previous year add -1
//				Date yesterday = cal.getTime();

			User user = userService.verifyAuthKey(autho);
			CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());

			List<JAlert> alerts = alertService.getackalerts(groupid, subgroupid, gatewayid, alerttypeid, user.getId(),
					deliquencystatus, cmp_cfg.getTemperatureunit(), "", "", nodeid, id);
			JAssetDescription assetDescrip = reportService.getAssetDescription(user.giveCompany().getId());

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("assetdescription", assetDescrip);
			response.put("Alters", alerts);
			return response;
		} catch (InvalidAuthoException e) {
			log.error("in valid auth");
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return response;
		}
	}

	@RequestMapping(value = "v3.0/furbitalertV3/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getFurbitAlertV3(@PathVariable String autho, @RequestParam("monitortype") String monitortype,
			@RequestParam(value = "timezone", defaultValue = "+00:00", required = false) String timezone) {
		JResponse response = new JResponse();
		log.info("Entering getFurbitAlertV3 : " + autho);
		//timezone = timezone.trim();
		timezone = timezone.replaceAll("\\s+","");
		if (timezone.charAt(0) != '-' && timezone.charAt(0) != '+')
			timezone = "+" + timezone;
		try {
			Map<String, String> resMaps = new HashMap<String, String>();

			try {
				resMaps = userServiceV4.getUserId_cmpIdByAuth(autho);

			} catch (InvalidAuthoException ex) {
				response.put("Status", 0);
				response.put("Msg", "invalid authkey");
				response.put("Error", ex.getLocalizedMessage());
				log.error("getFurbitAlertV3 : Exception : " + autho);
				return response;
			}

			if (resMaps != null) {
				long userId = Long.valueOf(resMaps.get("user_id"));
				String tempUnit = resMaps.get("tempunit");

				List<JAlertV4> alerts = alertService.getUnAckFurbitAlerts(userId, tempUnit, monitortype,timezone);

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("furbitalerts", alerts);
				return response;
			}
		} catch (Exception e) {
			log.error("getFurbitAlertV3 : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", " Exception occured ");
			response.put("Error", e.getLocalizedMessage());
			return response;
		}
		return response;
	}
	

}
