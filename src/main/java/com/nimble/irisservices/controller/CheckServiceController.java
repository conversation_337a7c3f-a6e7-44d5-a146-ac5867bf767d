package com.nimble.irisservices.controller;

import java.net.InetAddress;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.irisservices.dto.pingdom_http_custom_check;
import com.nimble.irisservices.entity.Testtable;
import com.nimble.irisservices.service.ICheckserviceService;

@RestController
public class CheckServiceController {
	private static final Logger log = LogManager.getLogger(CheckServiceController.class);

	@Autowired
	ICheckserviceService checkService;

	@RequestMapping("/")
	public String greeting() {
		log.info("Starteds");
		return "Greetings from Waggle!";
	}
	
	@GetMapping(value = "/checkirisservice",  produces = MediaType.APPLICATION_XML_VALUE)
	@ResponseBody
	public ResponseEntity<pingdom_http_custom_check> checkDatabase() {

		System.out.println("Entered :: DBcheckcontroller::::checkDatabse::[{}] : ");
		String result = "";

		try {
			long milliseconds = new Date().getTime();
			String hashKey = InetAddress.getLocalHost().getHostAddress() + "_" + milliseconds;

			Testtable tt = new Testtable();
			Testtable tt1 = new Testtable();

			log.info("Server Ip With Hash : " + hashKey);

			tt.setServerIp(hashKey);
			tt.setCheckServer("irisservice_Create");

			log.info("irisservice test Create Query ");
			Testtable ttable = checkService.testSaveOrUpdatequery(tt);

			if (ttable == null)
				result = "i";

			log.info("irisservice test Select Query ");
			ttable = checkService.testselectquery(tt);

			if (ttable != null || ttable.equals(tt))
				result = "s";

			log.info("irisservice test Update Query ");
			log.info(InetAddress.getLocalHost().getHostAddress());

			ttable.setCheckServer("irisservice_Close");
			ttable.setServerIp(hashKey);
			ttable = checkService.testSaveOrUpdatequery(ttable);

			if (ttable == null)
				result = "u";

			log.info("irisservice test Delete Query ");
			ttable = checkService.testDeletequery(ttable);

			if (ttable == null)
				result = "d";

			pingdom_http_custom_check response = new pingdom_http_custom_check();
			response.setStatus("OK");
			response.setResponse_time(96.777);
			System.out.println("Exit :: DBcheckcontroller::::checkDatabse::[{}] : ");
			return new ResponseEntity<>(response, HttpStatus.OK);

		} catch (Exception e) {
			System.out.println("Exception Occured");
			pingdom_http_custom_check response = new pingdom_http_custom_check();
			response.setStatus("FAIL");
			response.setResponse_time(96.777);
			System.out.println("Exit :: DBcheckcontroller::::checkDatabse::[{}] : ");
			return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
		}

	}
}
