package com.nimble.irisservices.controller;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.BatteryLookup;
import com.nimble.irisservices.entity.ProbeCategory;
import com.nimble.irisservices.entity.ProbeLookup;
import com.nimble.irisservices.service.IProbeCategoryService;
import com.nimble.irisservices.service.IUserServiceV4;

@Controller
public class ProbeCategoryController {

	private static final Logger log = LogManager.getLogger(ProbeCategoryController.class);

	@Autowired
	@Lazy
	IProbeCategoryService probService;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	// Used in web --> Savitha
	@RequestMapping(value = "v4.0/createprobecategory/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse createProbeCategory(@PathVariable String autho,
			@RequestParam(name = "os", required = false) String os,
			@RequestParam(name = "model_id", required = true) long model_id,
			@RequestBody List<ProbeCategory> probeList) {
		JResponse response = new JResponse();
		String responseMessage = "Status :" + "\n";
		log.info("Entering createProbeCategory - " + autho);
		int i = 0;

		try {
			if (probeList.size() > 0) {
				for (ProbeCategory probe : probeList) {
					probe.setModel_id(model_id);
					if (i == 0) {
						responseMessage += probe.getType() + " - ";
						i = 1;
					} else
						responseMessage += ", \n" + probe.getType() + " - ";
					try {
						boolean status = probService.createProbeCategory(probe);
						if (status)
							responseMessage += "Success";
						else
							responseMessage += "Failed";
					} catch (Exception e) {
						responseMessage += "Failed";
						log.error("createProbeCategory : " + e.getLocalizedMessage());
					}
				}
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("ResponseMessage", responseMessage);
			} else {
				responseMessage += "No probe category found to create!!!";
				response.put("Status", 0);
				response.put("Msg", "No probe category found to create!!!");
				response.put("ResponseMessage", responseMessage);
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Probe category creation failed!!!");
			response.put("ResponseMessage", e.getLocalizedMessage());
			log.error("createProbeCategory : " + e.getLocalizedMessage());
		}
		return response;
	}

	// Used in web --> Savitha
	@RequestMapping(value = "v4.0/getallprobecategory/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllProbeCategory(@PathVariable String autho,
			@RequestParam(name = "os", required = false) String os) {
		JResponse response = new JResponse();
		log.info("Entering getAllProbeCategory : " + autho);
		try {
			List<ProbeCategory> getAllProbeCategory = probService.getAllProbeCategory();

			if (getAllProbeCategory != null && getAllProbeCategory.size() > 0) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("probecategory", getAllProbeCategory);
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No probe category found.");
				log.error("No probe category found.");
				return response;
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting getAllProbeCategory.");
			response.put("Error", e.getLocalizedMessage());
			log.error("getAllProbeCategory : Exception : " + e.getLocalizedMessage());
			return response;
		}
	}

	// Used in web --> Savitha
		@RequestMapping(value = "v4.0/saveprobelookup/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
		@ResponseBody
		public JResponse saveProbeLookup(@PathVariable String autho, @RequestBody List<ProbeLookup> probeLookup,
				@RequestParam String probecategory, @RequestParam(name = "os", required = false) String os) {
			log.info("Entered save probe lookup - " + autho);
			JResponse response = new JResponse();
			String responseMessage = "Status :" + "\n";
			int i = 0;
			try {
				if (probeLookup.size() > 0) {
					for (ProbeLookup probe : probeLookup) {
						ProbeCategory probeCategory_type = probService.getProbeCategoryById(probecategory);
						if (probeCategory_type != null) {
							probe.setProbecategory(probeCategory_type);
							if (i == 0) {
								responseMessage += probe.getProbecategory().getType() + " - ";
								i = 1;
							} else
								responseMessage += ", \n" + probe.getProbecategory().getType() + " - ";
							try {
								boolean status = probService.createProbeLookup(probe);
								if (status)
									responseMessage += "Success";
								else
									responseMessage += "Failed";
							} catch (Exception e) {
								responseMessage += "Failed";
								log.error("createProbeLookup : " + e.getLocalizedMessage());
							}
						} else {
							responseMessage += "No probe category found to create probe lookup!!!";
						}
					}
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("ResponseMessage", responseMessage);
				} else {
					responseMessage += "No probe lookup found to create!!!";
					response.put("Status", 0);
					response.put("Msg", "No probe lookup found to create!!!");
					response.put("ResponseMessage", responseMessage);
				}
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Probe lookup creation failed!!!");
				response.put("ResponseMessage", e.getLocalizedMessage());
				log.error("saveprobelookup : " + e.getLocalizedMessage());
			}
			return response;
		}

	// Used in web --> Savitha
	@RequestMapping(value = "v4.0/getallprobelookup/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllProbeLookup(@PathVariable String autho,
			@RequestParam(name = "os", required = false) String os) {
		JResponse response = new JResponse();
		log.info("Entering getAllProbeLookup : " + autho);
		try {
			List<ProbeLookup> getAllProbeCategory = probService.getAllProbeLookup();
			if (getAllProbeCategory != null && getAllProbeCategory.size() > 0) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("probelookup", getAllProbeCategory);
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No probe lookup found.");
				log.error("No probe lookup found.");
				return response;
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting getAllProbeLookup.");
			response.put("Error", e.getLocalizedMessage());
			log.error("getAllProbeLookup : Exception : " + e.getLocalizedMessage());
			return response;
		}
	}

	// Used in web --> Savitha
		@RequestMapping(value = "v4.0/savebatterylookup/{autho}", method = RequestMethod.POST, headers = "Accept=application/json")
		@ResponseBody
		public JResponse saveBatteryLookup(@PathVariable String autho, @RequestBody List<BatteryLookup> batteryLookup,
				@RequestParam String probecategory, @RequestParam(name = "os", required = false) String os) {
			log.info("Entered save battery lookup - " + autho);
			JResponse response = new JResponse();
			String responseMessage = "Status :" + "\n";
			int i = 0;
			try {
				if (batteryLookup.size() > 0) {
					for (BatteryLookup battery : batteryLookup) {
						ProbeCategory probeCategory_type = probService.getProbeCategoryById(probecategory);
						if (probeCategory_type != null) {
							battery.setProbecategory(probeCategory_type);
							if (i == 0) {
								responseMessage += battery.getProbecategory().getType() + " - ";
								i = 1;
							} else
								responseMessage += ", \n" + battery.getProbecategory().getType() + " - ";
							try {
								boolean status = probService.createBatteryLookup(battery);
								if (status)
									responseMessage += "Success";
								else
									responseMessage += "Failed";
							} catch (Exception e) {
								responseMessage += "Failed";
								log.error("saveBatteryLookup : " + e.getLocalizedMessage());
							}
						} else {
							responseMessage += "No probe category found to create battery lookup!!!";
						}
					}
					response.put("Status", 1);
					response.put("Msg", "Success");
					response.put("ResponseMessage", responseMessage);
				} else {
					responseMessage += "No battery lookup found to create!!!";
					response.put("Status", 0);
					response.put("Msg", "No battery lookup found to create!!!");
					response.put("ResponseMessage", responseMessage);
				}
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Battery lookup creation failed!!!");
				response.put("ResponseMessage", e.getLocalizedMessage());
				log.error("savebatterylookup : " + e.getLocalizedMessage());
			}
			return response;
		}

	// Used in web --> Savitha
	@RequestMapping(value = "v4.0/getallbatterylookup/{autho}", method = RequestMethod.GET, headers = "Accept=application/json")
	@ResponseBody
	public JResponse getAllBatteryLookup(@PathVariable String autho,
			@RequestParam(name = "os", required = false) String os) {
		JResponse response = new JResponse();
		log.info("Entering getAllProbeLookup : " + autho);
		try {
			List<BatteryLookup> getAllBatteryCategory = probService.getAllBatteryLookup();
			if (getAllBatteryCategory != null && getAllBatteryCategory.size() > 0) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("batterylookup", getAllBatteryCategory);
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "No battery lookup found.");
				log.error("No battery lookup found.");
				return response;
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting getAllBatteryLookup.");
			response.put("Error", e.getLocalizedMessage());
			log.error("getAllBatteryLookup : Exception : " + e.getLocalizedMessage());
			return response;
		}
	}

}
