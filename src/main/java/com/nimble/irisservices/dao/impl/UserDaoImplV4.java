package com.nimble.irisservices.dao.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.Temporal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.*;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Repository;

import com.chargebee.Environment;
import com.chargebee.ListResult;
import com.chargebee.Result;
import com.chargebee.filters.enums.SortOrder;
import com.chargebee.models.Customer;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dao.IUserDaoV4;
import com.nimble.irisservices.entity.AskFeature;
import com.nimble.irisservices.entity.CountryCodeV4;
import com.nimble.irisservices.entity.EValidation;
import com.nimble.irisservices.entity.EmailOtp;
import com.nimble.irisservices.entity.FeedbackForm;
import com.nimble.irisservices.entity.FreshChat;
import com.nimble.irisservices.entity.OneTimePassword;
import com.nimble.irisservices.entity.OrderMappingDetails;
import com.nimble.irisservices.entity.Role;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserDltInfo;
import com.nimble.irisservices.entity.UserFeedbackTransaction;
import com.nimble.irisservices.entity.UserLocation;
import com.nimble.irisservices.entity.UserMetaData;
import com.nimble.irisservices.entity.UserRvDetails;
import com.nimble.irisservices.entity.UserTimeZone;
import com.nimble.irisservices.entity.UserToken;

import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.ICbWebhook;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IRechargeService;
import com.nimble.irisservices.service.IUserService;

@Repository
public class UserDaoImplV4 implements IUserDaoV4 {

	@Autowired
	private SessionFactory sessionFactory;

	@Autowired
	private SessionFactory slave2SessionFactory;
	
	@Autowired
	private SessionFactory slave3SessionFactory;

	@Autowired
	private SessionFactory slave4SessionFactory;
	
	@Autowired
	private SessionFactory slave5SessionFactory;

	@Value("${vpm_freedays}")
	private String freeVPM_days;
	
	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Value("${chargebee.customerid}")
	private String cb_customerid;

	@Value("${chargebee.restrictdomain}")
	private String restrictdomain;

	@Value("${vetchat_free_activation_days}")
	private int vetchat_free_activation_days;

	@Value("${valid_minutes_for_OTP}")
	private int validMinutesForOTP;
	
	@Autowired
	@Lazy
	IUserService userService;
	
	@Autowired
	@Lazy
	IChargebeeService cbService;
	
	@Autowired
	@Lazy
	ICreditSystemService crService;
	
	@Autowired
	@Lazy
	ICbWebhook cbw;
	
	@Autowired
	@Lazy
	IRechargeService reService;
	
	Helper _helper = new Helper();

	private static final Logger log = LogManager.getLogger(UserDaoImplV4.class);

	public Map<String, String> getUserId_cmpIdByAuthV2(String auth) throws InvalidAuthoException {

		log.info("Entered :: OptimizedDoaImpl :: getUserId_cmpIdByAuthV2 :: auth : "+auth);
		Map<String, String> map = new HashMap<String, String>();
		try {

			Query qry = slave3SessionFactory.getCurrentSession().createSQLQuery(
					"SELECT U.id, U.cmp_id, C.cmptype_id, U.username FROM user U JOIN company C ON U.cmp_id = C.id WHERE  U.authkey=:auth ;");
			qry.setParameter("auth", auth);

			List res = qry.list();
			Object[] tuple = (Object[]) res.get(0);

			BigInteger userId = (BigInteger) tuple[0];
			BigInteger cmp_id = (BigInteger) tuple[1];
			BigInteger cmpType_id = (BigInteger) tuple[2];
			String username = (String) tuple[3];

			map.put("user_id", userId + "");
			map.put("cmp_id", cmp_id + "");
			map.put("cmpType_id", cmpType_id + "");
			map.put("username", username + "");
			
		} catch (IndexOutOfBoundsException e) {
			log.error("Error in getUserId_cmpIdByAuthV2 :: Session Name : slave3SessionFactory :: error : " + e.getLocalizedMessage());
			throw new InvalidAuthoException();
		}

		return map;
	}

	@Override
	public UserV4 verifyAuthV4(String key, String value) throws InvalidAuthoException {

		log.info("Entered :: UserDaoImplV4 :: verifyAuthV4 :: key : "+key+" :: value : "+value);
		UserV4 usr = null;
		try {
			
			// Enhancement
//			Session session = sessionFactory.getCurrentSession();
//			Criteria cr = session.createCriteria(User.class)
//					.setProjection(Projections.projectionList()
//					.add(Projections.property("id"),"id")
//					.add(Projections.property("password"),"password")
//					.add(Projections.property("username"),"username")
//					.add(Projections.property("company.id"),"cmpId")
//					.add(Projections.property("email"),"email")
//					.add(Projections.property("password_ver"),"password_ver")
//					.add(Projections.property("chargebeeid"),"chargebeeid"))
//					.add(Restrictions.eq(key, value));
//					
//			cr.setResultTransformer(new AliasToBeanResultTransformer(UserV4.class));
//			List<UserV4> userV4List = cr.list();
//			if( userV4List.isEmpty() ) {
//				throw new InvalidAuthoException();
//			} else {
//				usr = userV4List.get(0);
//			}

			String query = "SELECT U.id, U.cmp_id,U.chargebeeid , U.email, U.password, U.username, U.password_ver, U.delete_user, U.delete_time, U.firstname, U.mobileno, U.lastname,U.country,U.recharge_custid,U.authkey,U.createdon  FROM `user` U  WHERE U."
					+ key + " = :value";

			Query qry = this.sessionFactory.getCurrentSession().createSQLQuery(query);

			qry.setParameter("value", value);

			List res = qry.list();

			if (!res.isEmpty() && res.size() > 0) {

				Object[] tuple = (Object[]) res.get(0);

				usr = new UserV4();

				if (tuple[0] != null) {
					BigInteger userId = (BigInteger) tuple[0];
					usr.setId(userId.longValue());
				}
				if (tuple[1] != null) {
					BigInteger cmp_id = (BigInteger) tuple[1];
					usr.setCmpId(cmp_id.longValue());
				}
				if (tuple[2] != null)
					usr.setChargebeeid((String) tuple[2]);

				if (tuple[3] != null)
					usr.setEmail((String) tuple[3]);
				
				if (tuple[4] != null)
					usr.setPassword((String) tuple[4]);
				
				if (tuple[5] != null)
					usr.setUsername((String) tuple[5]);
				
				if (tuple[6] != null)
					usr.setPassword_ver((String) tuple[6]);
				
				if (tuple[7] != null)
					usr.setDelete_user((boolean) tuple[7]);
				
				if (tuple[8] != null)
					usr.setDelete_time((String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(tuple[8]));
				
				if (tuple[9] != null)
					usr.setFirstname((String) tuple[9]);
				
				if (tuple[10] != null)
					usr.setMobileno((String) tuple[10]);
				
				if (tuple[11] != null)
					usr.setLastname((String) tuple[11]);
				
				if (tuple[12] != null)
					usr.setCountry((String) tuple[12]);

				
				if (tuple[13] != null)
					usr.setRecharge_custid((String) tuple[13]);
				
				if(tuple[14] != null)
					usr.setAuthKey((String) tuple[14]);
				
				if (tuple[15] != null)
					usr.setCreatedOn((String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(tuple[15]));
				
				return usr;

			} else {
				throw new InvalidAuthoException();
			}
		} catch (Exception e) {
			log.error("Error in verifyAuthV4 :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			throw new InvalidAuthoException();
		}
	}

	@Override
	public long getUserByUNameOrEmail(String email) {
		log.info("Entered :: UserDaoImplV4 :: getUserByUNameOrEmail :: email : "+email);
		try {
			Session session = slave2SessionFactory.getCurrentSession();

			SQLQuery qry = session
					.createSQLQuery("SELECT U.id, U.email FROM user U WHERE U.email= :email OR U.username = :email");
			qry.setParameter("email", email);

			List<Object[]> res = qry.list();

			if (res.size() > 0) {
				Object[] tuple = (Object[]) res.get(0);
				BigInteger userId = (BigInteger) tuple[0];
				return userId.longValue();
			} else {
				log.error("NO user found...");
				return -1;
			}

		} catch (IndexOutOfBoundsException e) {
			log.error("Error in getUserByUNameOrEmail :: Session Name : slave2SessionFactory :: error : " + e.getLocalizedMessage());
			return 0;
		} catch (Exception e) {
			log.error("Error in getUserByUNameOrEmail :: Session Name : slave2SessionFactory :: error : " + e.getLocalizedMessage());
			return 0;
		}
	}

	@Override
	public boolean updateUserv4byuserid(User user, long usr_id) {
		log.info("Entered :: UserDaoImplV4 :: updateUserv4byuserid :: user id : "+usr_id );
		try {
			String qry = "UPDATE user SET firstname=:firstname, lastname=:lastname, zipcode=:zipcode, city=:city, state=:state, country=:country, updatedon=:updatedOn ";

			if (user.getMobileno() != null)
				qry += ", mobileno=:mobileNo ";
			if (user.getEmail() != null)
				qry += ", email=:email ";

			qry += " WHERE id = :userId";

			Session ses = sessionFactory.getCurrentSession();

			Query qry1 = ses.createSQLQuery(qry);
			qry1.setParameter("firstname", user.getFirstname());
			qry1.setParameter("lastname", user.getLastname());
			qry1.setParameter("zipcode", user.getZipcode());
			qry1.setParameter("city", user.getCity());
			qry1.setParameter("state", user.getState());
			qry1.setParameter("country", user.getCountry());
			qry1.setParameter("updatedOn", user.getUpdatedOn());

			if (user.getMobileno() != null)
				qry1.setParameter("mobileNo", user.getMobileno());
			if (user.getEmail() != null)
				qry1.setParameter("email", user.getEmail());

			qry1.setParameter("userId", usr_id);

			int status = qry1.executeUpdate();

			if (status > 0) {
				log.info("User Updated");
				return true;
			} else {
				log.error("User not Updated");
				return false;
			}

		} catch (Exception e) {
			log.error("Error in updateUserv4byuserid :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
		}

		return false;
	}

	public Map<String, String> getUserId_cmpIdByAuth(String auth) throws InvalidAuthoException {

		log.info("Entered :: OptimizedDoaImpl :: getUserId_cmpIdByAuth:: auth : "+auth);
		Map<String, String> map = new HashMap<String, String>();
		try {

			Query qry = slave3SessionFactory.getCurrentSession()
					.createSQLQuery("SELECT U.id, U.cmp_id, CC.temperatureunit, CC.geofence_enable, U.username, U.delete_user, U.country, U.chargebeeid FROM user U ,companyconfig CC "
							+ "WHERE U.cmp_id = CC.cmp_id AND U.authkey='" + auth + "';");

			List res = qry.list();

			Object[] tuple = (Object[]) res.get(0);

			BigInteger userId = (BigInteger) tuple[0];
			BigInteger cmp_id = (BigInteger) tuple[1];
			String geofence_enable ="0";
			
			if((boolean)tuple[3])
				geofence_enable ="1";
			
			map.put("user_id", userId + "");
			map.put("cmp_id", cmp_id + "");
			map.put("tempunit", (String) tuple[2]);
			map.put("geofence_enable", geofence_enable);
			map.put("username", (String) tuple[4]);
			map.put("delete_user", tuple[5] + "");
			map.put("country", tuple[6] + "");
			map.put("chargebee_id", (String) tuple[7]);

		} catch (IndexOutOfBoundsException e) {
			log.error("Error in getUserId_cmpIdByAuth :: Session Name : slave3SessionFactory :: error : " + e.getLocalizedMessage());
			throw new InvalidAuthoException();
		}

		return map;
	}
	
	@Override
	public List<JAlertV4> getUnackAlerts(long userid, String tempunit, String monitortype) {
		log.info("Entered into getUnackAlerts :: user : "+userid);
		List<JAlertV4> alertList = new ArrayList<JAlertV4>();

		String curDt = IrisservicesUtil.getCurrentTimeUTC();
		
		String qry = "SELECT A.startdatetime,A.enddatetime,A.ack,A.timezone,A.alerttype_id ,AT.name AS alerttype,A.gateway_id,"
				+ " G.name AS gatewayname,A.lat,A.lon, A.gpsstatus, A.temperature,A.battery,A.alertvalue  FROM alert A "
				+ " JOIN alerttype AT ON AT.id = A.alerttype_id JOIN gateway G ON G.id = A.gateway_id"
				+ " JOIN assetmodel AM ON AM.id=G.model_id "
				+ " WHERE AM.monitor_type_id=1 AND  A.gateway_id IN(SELECT gatewayid FROM usergateway WHERE userid = " + userid
				+ ") AND CONVERT_TZ(`enddatetime`,`timezone`,'+00:00') >= DATE_SUB('"+curDt+"', INTERVAL 24 HOUR) ORDER BY enddatetime DESC limit 50;";

		Query query = this.slave5SessionFactory.getCurrentSession().createSQLQuery(qry);

		JAlertV4 alertv4;
		List<Object> res = query.list();
		try {
			if (!res.isEmpty()) {
				for (int i = 0; i < res.size(); i++) {

					Object[] result = (Object[]) res.get(i);
					String OLD_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";
					final String NEW_FORMAT = "MM/dd/yyyy HH:mm:ss";
					String oldDateString = "";
					String newDateString = "";

					alertv4 = new JAlertV4();

//					if (result[0] != null) {
//						oldDateString = result[0].toString();
//						try {
//							DateFormat formatter = new SimpleDateFormat(OLD_FORMAT);
//							Date d = formatter.parse(oldDateString);
//							((SimpleDateFormat) formatter).applyPattern(NEW_FORMAT);
//							newDateString = formatter.format(d);
//						} catch (Exception e) {
//							log.error("getUnackAlerts : " + e.getLocalizedMessage());
//						}
//
//						alertv4.setStartdatetime(newDateString);
//					}
					if (result[1] != null) {
						oldDateString = result[1].toString();
						try {
							DateFormat formatter = new SimpleDateFormat(OLD_FORMAT);
							Date d = formatter.parse(oldDateString);
							((SimpleDateFormat) formatter).applyPattern(NEW_FORMAT);
							newDateString = formatter.format(d);
						} catch (Exception e) {
							log.error("getUnackAlerts : " + e.getLocalizedMessage());
						}

						alertv4.setLastnotifiedtime(newDateString);
						alertv4.setStartdatetime(newDateString);
					}
					if (result[2] != null)
						alertv4.setAcknowledged(false);//(boolean) result[2]);
					if (result[3] != null)
						alertv4.setTimezone(result[3].toString());
					if (result[4] != null)
						alertv4.setAlerttypeid(Long.parseLong(result[4].toString()));
					if (result[5] != null)
						alertv4.setAlerttype(result[5].toString());
					if (result[6] != null)
						alertv4.setGatewayid(Long.parseLong(result[6].toString()));
					if (result[7] != null)
						alertv4.setGateway(result[7].toString());
					if (result[8] != null)
						alertv4.setLat(Double.parseDouble(result[8].toString()));
					if (result[9] != null)
						alertv4.setLon(Double.parseDouble(result[9].toString()));
					if (result[10] != null)
						alertv4.setGpsstatus(result[10].toString());
					if (result[11] != null) {
						float tempval = Float.parseFloat(result[11].toString());

						if (tempunit.equalsIgnoreCase("F") && tempval > -200
								&& (alertv4.getAlerttypeid() == 1 || alertv4.getAlerttypeid() == 4)) {
							tempval = CelsiusToFahrenheit(tempval);
						}

						alertv4.setTemperature(tempval);
					}
					if (result[12] != null)
						alertv4.setBattery(Integer.parseInt(result[12].toString()));
					if (result[13] != null)
						alertv4.setAlertvalue(Float.parseFloat(result[13].toString()));

					alertList.add(alertv4);
				}
			}
		} catch (Exception e) {
			log.error("Error in getUnackAlerts :: Session Name : slave5SessionFactory :: error : " + e.getLocalizedMessage());
		}
		return alertList;
	}


	private float CelsiusToFahrenheit(float tempmvalIndegreeCelsius) {

		double degreeFahrenheit = Double.valueOf(tempmvalIndegreeCelsius).floatValue() * 9 / 5 + 32;
		double roundvalues = Math.round(degreeFahrenheit * 100.0) / 100.0;
		return (float) roundvalues;
	}

	private float FahrenheitToCelsius(float tempmvalIndegreeFahrenheit) {
		double degreeCelcius = (Double.valueOf(tempmvalIndegreeFahrenheit).floatValue() - 32) * 5 / 9;
		double roundvalues = Math.round(degreeCelcius * 100.0) / 100.0;
		return (float) roundvalues;
	}

	@Override
	public UserV4 verifyAuthV3(String key, String value) throws InvalidAuthoException {
		log.info("Entered into verifyAuthV3 :: key : "+key+" :: value : "+value);
		UserV4 user = new UserV4();
		try {
			// Enhacement
//			Session session = sessionFactory.getCurrentSession();
//			
//			Criteria cr = session.createCriteria(User.class)
//					.setProjection(Projections.projectionList()
//					.add(Projections.property("id"),"id")
//					.add(Projections.property("authKey"),"authKey")
//					.add(Projections.property("password"),"password")
//					.add(Projections.property("username"),"username")
//					.add(Projections.property("company.id"),"cmpId")
//					.add(Projections.property("role"),"role")
//					.add(Projections.property("email"),"email")
//					.add(Projections.property("mobileno"),"mobileno")
//					.add(Projections.property("webappid"),"webappid")
//					.add(Projections.property("mobileappid"),"mobileappid")
//					.add(Projections.property("enable"),"enable")
//					.add(Projections.property("resetpassword"),"resetpassword")
//					.add(Projections.property("notification"),"notification")
//					.add(Projections.property("alternateemail"),"alternateemail")
//					.add(Projections.property("alternatephone"),"alternatephone")
//					.add(Projections.property("updatedOn"),"updatedOn")
//					.add(Projections.property("isVerified"),"isVerified")
//					.add(Projections.property("firstname"),"firstname")
//					.add(Projections.property("lastname"),"lastname")
//					.add(Projections.property("zipcode"),"zipcode")
//					.add(Projections.property("signuptoken"),"signupToken")
//					.add(Projections.property("signupType.id"),"signupTypeId")
//					.add(Projections.property("city"),"city")
//					.add(Projections.property("state"),"state")
//					.add(Projections.property("country"),"country")
//					.add(Projections.property("createdOn"),"createdOn")
//					.add(Projections.property("imageUrl"),"imageUrl")
//					.add(Projections.property("chargebeeid"),"chargebeeid")
//					.add(Projections.property("in_app"),"in_app")
//					.add(Projections.property("lastlogintype"),"lastlogintype")
//					.add(Projections.property("completesetup"),"completesetup")
//					.add(Projections.property("lastlogintime"),"lastlogintime")
//					.add(Projections.property("inapp_purchase"),"inapp_purchase")
//					.add(Projections.property("plan_ver"),"plan_ver")
//					.add(Projections.property("password_ver"),"password_ver"))		
//					.add(Restrictions.eq(key, value));
//
//			cr.setResultTransformer(new AliasToBeanResultTransformer(UserV4.class));
//			List<UserV4> userV4List = cr.list();
//			
//			if( userV4List.isEmpty() ) {
//				throw new InvalidAuthoException();
//			} else {
//				user = userV4List.get(0);
//			}
			
			String qry = "SELECT U.id, U.authkey, U.password, U.username, U.cmp_id, U.role_id,U.email,U.mobileno,U.webappid,U.mobileappid,U.enable,U.resetpassword,U.notification,"
					+ " U.alternate_email,U.alternate_phone,U.updatedon,U.isverified,U.firstname,U.lastname,U.zipcode,U.signuptoken,U.signuptype_id,U.city,U.state,"
					+ " U.country,U.createdon,U.imageurl,U.chargebeeid,U.in_app,U.lastlogintype,U.completesetup,U.lastlogintime,R.name,U.inapp_purchase,U.plan_ver,U.password_ver,"
					+ "U.delete_user,U.delete_time,U.recharge_custid FROM user U "
					+ " JOIN role R ON  U.role_id = R.id  WHERE U." + key + "= :value ;";

			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("value", value);

			List resultset = query.list();
			if (resultset.size() > 0) {
				Object[] result = (Object[]) resultset.get(0);

				if (result[0] != null) {
					user.setId(((BigInteger) result[0]).longValue());
				}
				if (result[1] != null) {
					user.setAuthKey((String) result[1]);
				}
				if (result[2] != null) {
					user.setPassword((String) result[2]);
				}
				if (result[3] != null) {
					user.setUsername((String) result[3]);
				}
				if (result[4] != null) {
					user.setCmpId(((BigInteger) result[4]).longValue());
				}
				Role role = new Role();
				if (result[5] != null) {
					role.setId(((BigInteger) result[5]).longValue());
				}
				if (result[6] != null) {
					user.setEmail((String) result[6]);
				}
				if (result[7] != null) {
					user.setMobileno((String) result[7]);
				}
				if (result[8] != null) {
					user.setWebappid((int) result[8]);
				}
				if (result[9] != null) {
					user.setMobileappid((int) result[9]);
				}
				if (result[10] != null) {
					user.setEnable((boolean) result[10]);
				}
				if (result[11] != null) {
					user.setResetpassword((int) result[11]);
				}
				if (result[12] != null) {
					user.setNotification((boolean) result[12]);
				}
				if (result[13] != null) {
					user.setAlternateemail((String) result[13]);
				}
				if (result[14] != null) {
					user.setAlternatephone((String) result[14]);
				}
				if (result[15] != null) {
					user.setUpdatedOn(((Timestamp) result[15]).toString());
				}
				if (result[16] != null) {
					user.setVerified((boolean) result[16]);
				}
				if (result[17] != null) {
					user.setFirstname((String) result[17]);
				}
				if (result[18] != null) {
					user.setLastname((String) result[18]);
				}
				if (result[19] != null) {
					user.setZipcode((String) result[19]);
				}
				if (result[20] != null) {
					user.setSignupToken((String) result[20]);
				}
				if (result[21] != null) {
					user.setSignupTypeId(((BigInteger) result[21]).longValue());
				}
				if (result[22] != null) {
					user.setCity((String) result[22]);
				}
				if (result[23] != null) {
					user.setState((String) result[23]);
				}
				if (result[24] != null) {
					user.setCountry((String) result[24]);
				}
				if (result[25] != null) {
					user.setCreatedOn(((Timestamp) result[25]).toString());
				}
				if (result[26] != null) {
					user.setImageUrl((String) result[26]);
				}
				if (result[27] != null) {
					user.setChargebeeid((String) result[27]);
				}
				if (result[28] != null) {
					user.setIn_app((boolean) result[28]);
				}
				if (result[29] != null && result[29].toString().matches("[-+]?\\d*\\.?\\d+")) {
					user.setLastlogintype(Integer.parseInt(result[29].toString()));
				}
				if (result[30] != null) {
					user.setCompletesetup((boolean) result[30]);
				}
				if (result[31] != null) {
					user.setLastlogintime(((Timestamp) result[31]).toString());
				}
				if (result[32] != null) {
					role.setName((String) result[32]);
				}
				if (result[33] != null) {
					user.setInapp_purchase((int) result[33]);
				}
				if (result[34] != null) {
					user.setPlan_ver((String) result[34]);
				}
				if (result[35] != null) {
					user.setPassword_ver((String) result[35]);
				}
				if (result[36] != null) {
					user.setDelete_user((boolean) result[36]);
				}
				if (result[37] != null) {
					user.setDelete_time((String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(result[37]));
				}
				if (result[38] != null) {
					user.setRecharge_custid((String)result[38]);
				}
				user.setRole(role);
			} else {
				throw new InvalidAuthoException();
			}
		} catch (Exception e) {
			log.error("Error in verifyAuthV3 :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			throw new InvalidAuthoException();
		}
		return user;
	}

	@Override
	public List<UserV4> getUsersByUserId_CmpId(String userId, long cmpId) {
		log.info("Entered :: Slave3OptimizedDaoImpl :: getUserById_CmpId :: user id : "+userId+" cmp id : "+cmpId);
		List<UserV4> userList = new ArrayList<UserV4>();
		try {
String qry = "SELECT  U.id, U.authkey, U.password, U.username, U.cmp_id, U.role_id,U.email,U.mobileno,U.webappid,U.mobileappid,U.enable,U.resetpassword,U.notification,"
					+ "U.alternate_email,U.alternate_phone,U.updatedon,U.isverified,U.firstname,U.lastname,U.zipcode,U.signuptoken,U.signuptype_id,U.city,U.state,"
					+ "U.country,U.createdon,U.imageurl,U.chargebeeid,U.in_app,U.lastlogintype,U.completesetup,U.lastlogintime,R.name, U.inapp_purchase, U.delete_user FROM user U JOIN role R ON  U.role_id = R.id WHERE ";
			if (!userId.isEmpty()) {
				qry += "U.id =:userId ;";
			} else {
				qry += "U.cmp_id :cmpId ;";
			}
			Query selectQuery = slave5SessionFactory.getCurrentSession().createSQLQuery(qry);
			if (!userId.isEmpty())
				selectQuery.setParameter("userId", userId);
			else
				selectQuery.setParameter("cmpId", cmpId);

			List<Object[]> res = selectQuery.list();

			if (res.size() > 0) {
				for (Object[] Obj : res) {
					UserV4 user = new UserV4();
					Object[] result = Obj;
					if (result[0] != null) {
						user.setId(((BigInteger) result[0]).longValue());
					}
					if (result[1] != null) {
						user.setAuthKey((String) result[1]);
					}
					if (result[2] != null) {
						user.setPassword((String) result[2]);
					}
					if (result[3] != null) {
						user.setUsername((String) result[3]);
					}
					if (result[4] != null) {
						user.setCmpId(((BigInteger) result[4]).longValue());
					}
					Role role = new Role();
					if (result[5] != null) {
						role.setId(((BigInteger) result[5]).longValue());
					}
					if (result[6] != null) {
						user.setEmail((String) result[6]);
					}
					if (result[7] != null) {
						user.setMobileno((String) result[7]);
					}
					if (result[8] != null) {
						user.setWebappid((int) result[8]);
					}
					if (result[9] != null) {
						user.setMobileappid((int) result[9]);
					}
					if (result[10] != null) {
						user.setEnable((boolean) result[10]);
					}
					if (result[11] != null) {
						user.setResetpassword((int) result[11]);
					}
					if (result[12] != null) {
						user.setNotification((boolean) result[12]);
					}
					if (result[13] != null) {
						user.setAlternateemail((String) result[13]);
					}
					if (result[14] != null) {
						user.setAlternatephone((String) result[14]);
					}
					if (result[15] != null) {
						user.setUpdatedOn(((Timestamp) result[15]).toString());
					}
					if (result[16] != null) {
						user.setVerified((boolean) result[16]);
					}
					if (result[17] != null) {
						user.setFirstname((String) result[17]);
					}
					if (result[18] != null) {
						user.setLastname((String) result[18]);
					}
					if (result[19] != null) {
						user.setZipcode((String) result[19]);
					}
					if (result[20] != null) {
						user.setSignupToken((String) result[20]);
					}
					if (result[21] != null) {
						user.setSignupTypeId(((BigInteger) result[21]).longValue());
					}
					if (result[22] != null) {
						user.setCity((String) result[22]);
					}
					if (result[23] != null) {
						user.setState((String) result[23]);
					}
					if (result[24] != null) {
						user.setCountry((String) result[24]);
					}
					if (result[25] != null) {
						user.setCreatedOn(((Timestamp) result[25]).toString());
					}
					if (result[26] != null) {
						user.setImageUrl((String) result[26]);
					}
					if (result[27] != null) {
						user.setChargebeeid((String) result[27]);
					}
					if (result[28] != null) {
						user.setIn_app((boolean) result[28]);
					}
					if (result[29] != null) {
						user.setLastlogintype(Integer.parseInt(result[29] + ""));
					}
					if (result[30] != null) {
						user.setCompletesetup((boolean) result[30]);
					}
					if (result[31] != null) {
						user.setLastlogintime(((Timestamp) result[31]).toString());
					}
					if (result[32] != null) {
						role.setName((String) result[32]);
					}
					if (result[33] != null) {
						user.setInapp_purchase((int) result[33]);
					}
					if(result[34] != null) {
						user.setDelete_user((boolean) result[34]);
					}
					user.setRole(role);

					userList.add(user);
				}
			} else {
				log.info("No users found !");
			}
		} catch (Exception e) {
			log.error("Error in getUsersByUserId_CmpId :: Session Name : slave5SessionFactory :: error : " + e.getLocalizedMessage());
		}
		return userList;
	}

	@Override
	public int updateUserPassword(String authkey, String password) {
		log.info("Entered :: OptimizedDoaImpl :: updateUserV4:: ");
		try {

			Session ses = sessionFactory.getCurrentSession();

			String hql = "UPDATE user set " +
			          "password = :Password, password_ver='V2'" +  
			          " where authKey = :AuthKey";

			Query qr = ses.createSQLQuery(hql);

			qr.setParameter("Password",password);

			qr.setParameter("AuthKey",authkey);
			
			int status = qr.executeUpdate();

			if (status > 0)
				log.info("User Updated");

			return status;

		} catch (Exception e) {
			log.error("updateUserV4: " + e.getLocalizedMessage());
		}

		return -1;

	}

	public boolean saveOrUpdateUserTokenV4(String userid, UserToken usertoken) throws DataIntegrityViolationException {

		log.info("Entered :: OptimizedDoaImpl :: saveOrUpdateUserTokenV4 :: user id : "+userid);
		boolean isSuccess = false;

		try {
			long userId = Long.valueOf(userid.trim());
			UserToken usertokenPrev = getUserTokenV4(userId, usertoken.getToken());
			Session ses = sessionFactory.getCurrentSession();
			String curUtc = IrisservicesUtil.getCurrentTimeUTC();
			int enable = usertoken.isEnable() ? 1 : 0;
			if (usertokenPrev != null) {

				SQLQuery qry = ses.createSQLQuery(
						"UPDATE usertoken UT SET UT.token = '" + usertoken.getToken() + "', UT.enable = '" + enable
								+ "' , UT.userId = '" + userId + "',UT.ostype='" + usertoken.getOstype()
								+ "',UT.updatedon='" + curUtc + "', UT.ver='"+usertoken.getVer()+"'  WHERE UT.id = '" + usertokenPrev.getId() + "'");
				if (qry.executeUpdate() > 0) {
					log.info("Updated successfully!");
				}
				isSuccess = true;
			} else {
				SQLQuery qry = ses.createSQLQuery("INSERT INTO usertoken(token,enable,userId,ostype,updatedon,ver) VALUES('"
						+ usertoken.getToken() + "','" + enable + "','" + userId + "','" + usertoken.getOstype() + "','"
						+ curUtc + "','"+usertoken.getVer()+"')");
				if (qry.executeUpdate() > 0) {
					log.info("Inserted successfully!");
				}
				isSuccess = false;
			}
		} catch (Exception e) {
			log.error("Error in saveOrUpdateUserTokenV4 :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
		}
		return isSuccess;
	}

	public UserToken getUserTokenV4(long userid, String token) {
		UserToken userTok = new UserToken();
		log.info("Entered :: OptimizedDoaImpl :: getUserTokenV4:: ");
		try {
			Session session = sessionFactory.getCurrentSession();

			SQLQuery qry = session.createSQLQuery("SELECT U.id, U.enable FROM usertoken U WHERE U.token='" + token
					+ "' AND U.userId='" + userid + "'");
			List<Object[]> res = qry.list();
			if (res.size() > 0) {
				Object[] tuple = (Object[]) res.get(0);
				userTok.setId(Long.valueOf(((BigInteger) tuple[0]) + ""));
				userTok.setEnable((boolean) tuple[1]);
			} else {
				log.info("UserToken Not Found!");
				return null;
			}
			return userTok;
		} catch (IndexOutOfBoundsException e) {
			log.error(e.getMessage());
			return null;
		} catch (Exception e) {
			log.error(e.getMessage());
			return null;
		}
	}

	@Override
	public int executeQuery(String qry) {


		log.info("Entered ::  UserDaoImplV4:: executeQuery:: ");
		try {
			Session create_ses = sessionFactory.getCurrentSession();
			SQLQuery createQuery = create_ses.createSQLQuery(qry);
			int Cstatus = createQuery.executeUpdate();
			log.info("Query :" + qry);
			log.info("executeStatus :" + Cstatus);
			return Cstatus;
		} catch (IndexOutOfBoundsException e) {
			log.error("Query : " + qry);
			log.error("Error in executeQuery :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return 0;
		} catch (Exception e) {
			log.error("Query : " + qry);
			log.error("Error in executeQuery :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return 0;
		}

	}
	
	@Override
	public int executePetProfileQuery(String qry) {
		log.info("Entered ::  UserDaoImplV4:: executePetProfileQuery:: ");
		try {
			Session create_ses = sessionFactory.getCurrentSession();
			SQLQuery createQuery = create_ses.createSQLQuery(qry);
			int Cstatus = createQuery.executeUpdate();
			
			// Retrieve the last inserted ID
			SQLQuery generatedObj = create_ses.createSQLQuery("SELECT LAST_INSERT_ID()");
			int generatedId = ((BigInteger) generatedObj.list().get(0)).intValue();
			log.info("Query :" + qry);
			log.info("executeStatus :" + Cstatus);
			return generatedId;
		} catch (IndexOutOfBoundsException e) {
			log.error("Query : " + qry);
			log.error("Error in executeQuery :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return 0;
		} catch (Exception e) {
			log.error("Query : " + qry);
			log.error("Error in executeQuery :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return 0;
		}

	}

	@Override
	public String saveOrUpdateVPM_id(long user_id, String vpm_id) {

		String vpmId = "";
		try {
			Session ses = sessionFactory.getCurrentSession();
			SQLQuery qry = ses.createSQLQuery("SELECT * FROM user_metadata WHERE user_id=" + user_id + ";");

			List<BigInteger> ids = qry.list();
			if (!ids.isEmpty()) {
				String updateQry = "UPDATE user_metadata SET vpm_id='" + vpm_id + "' WHERE user_id=" + user_id;

				int insertCount = ses.createSQLQuery(updateQry).executeUpdate();
				log.info("updateQry : " + insertCount);
				log.info("User not found for : " + user_id);
			} else {
				String insertQry = "INSERT INTO  user_metadata( user_id,vpm_id) VALUES(" + user_id + ",'" + vpm_id
						+ "');";

				int insertCount = ses.createSQLQuery(insertQry).executeUpdate();
				log.info("insertCount : " + insertCount);
				log.info("User not found for : " + user_id);
			}
		} catch (Exception e) {
			log.error("Exception in saveOrUpdateVPM_id: " + e.getLocalizedMessage());
			log.info("vpm details not found for : " + user_id);
		}
		return vpmId;
	}

	@Override
	public Map<String, String> getVPMData(long user_id) {

		log.info("Entered :: userDoaImpl :: getVPMData:: ");
		Map<String, String> map = new HashMap<String, String>();
		try {

			Query qry = sessionFactory.getCurrentSession()
					.createSQLQuery(" SELECT vpm_id,channel_sid FROM user_metadata WHERE user_id=" + user_id + ";");

			List res = qry.list();

			Object[] tuple = (Object[]) res.get(0);

			String vpm_id = (String) tuple[0];
			String channel_sid = (String) tuple[1];

			log.info(channel_sid);

			map.put("vpm_id", vpm_id);
			map.put("channel_sid", channel_sid);

		} catch (Exception e) {
			log.error("Invalid auth exception : " + e.getLocalizedMessage());
			return map;
		}

		return map;
	}

	@Override
	public JResponse getuserlistbyfilter(String uKey, String uValue, String fType, String oType, long offset,
			long limit, String Okey, JResponse response) {
		ArrayList<UserV4> user = new ArrayList<UserV4>();
		log.info("Entered :: OptimizedDoaImpl :: getuserlistbyfilter:: ");
		try {
			Session session = sessionFactory.getCurrentSession();

			// Get Total count From Database
			String query = "";
			String selectQuery = "SELECT "
					+ "U.id,"			//0
					+ "U.username,"		//1
					+ "U.password,"		//2
					+ "U.email,"		//3
					+ "U.mobileno,"		//4
					+ "U.enable,"		//5
					+ "U.chargebeeid,"	//6
					+ "U.authkey,"		//7
					+ "U.delete_user,"	//8
					+ "U.city,"			//9
					+ "U.state,"		//10
					+ "U.country, "		//11
					+ "U.password_ver "		//12
					+ "FROM `user` U ";

			if (uKey != null && uKey != "" && !uKey.equalsIgnoreCase("all")) {
				if (fType.equalsIgnoreCase("equal"))
					query += "WHERE U." + uKey + " ='" + uValue + "' ";
				else
					query += "WHERE U." + uKey + " like ('%" + uValue + "%') ";
			} else if (uKey != null && uKey != "" && uKey.equalsIgnoreCase("all")) {
				query += selectQuery + "where U.username like ('%" + uValue + "%') union " + selectQuery
						+ " where U.email like ('%" + uValue + "%') union " + selectQuery + " where U.mobileno like ('%"
						+ uValue + "%') union " + selectQuery + " where U.firstname like ('%" + uValue + "%') union "
						+ selectQuery + " where U.lastname like ('%" + uValue + "%') union " + selectQuery
						+ " where U.chargebeeid like ('%" + uValue + "%') union " + selectQuery + " where U.id like ('%"
						+ uValue + "%') union " + selectQuery + " where U.alternate_email like ('%" + uValue
						+ "%') union " + selectQuery + " where U.alternate_phone like ('%" + uValue + "%') ";
			}

			try {
				SQLQuery countQry = null;
				if (uKey != null) {
					if (uKey.equalsIgnoreCase("all"))
						countQry = session.createSQLQuery("SELECT count(*) FROM (" + query + ") as U ");
					else
						countQry = session.createSQLQuery("SELECT count(*) FROM `user` U " + query);
				} else
					countQry = session.createSQLQuery("SELECT count(*) FROM `user` U " + query);

				List<BigInteger> countRes = countQry.list();
				long totalUser = Long.valueOf(((BigInteger) countRes.get(0)) + "");

				response.put("Totaluser", totalUser);
				response.put("offset", offset);
			} catch (Exception e) {
				response.put("Status", 0);
				response.put("Msg", "Error occur While Get usercount .");
				response.put("Error", e.getLocalizedMessage());
				log.error(e.getMessage());
			}

			// Get data From Database
			if (uKey != null) {
				if (Okey != null && !uKey.equalsIgnoreCase("all"))
					query += " order by U." + Okey + " " + oType;
				else if (Okey != null && uKey.equalsIgnoreCase("all"))
					query += " order by " + Okey + " " + oType;
			} else
				query += " order by U." + Okey + " " + oType;
			query += " limit " + limit * (offset - 1) + "," + limit;

			SQLQuery listQry = null;
			if (uKey != null) {
				if (!uKey.equalsIgnoreCase("all"))
					listQry = session.createSQLQuery(selectQuery + query);
				else
					listQry = session.createSQLQuery(query);
			} else
				listQry = session.createSQLQuery(selectQuery + query);

			List<Object[]> listRes = (List<Object[]>) listQry.list();

			for (int i = 0; i < listRes.size(); i++) {
				UserV4 usr = new UserV4();
				Object[] tuple1 = (Object[]) listRes.get(i);

				usr.setId(Long.valueOf(((BigInteger) tuple1[0]) + ""));
				usr.setUsername((String) tuple1[1]);
				usr.setPassword((String) tuple1[2]);
				usr.setEmail((String) tuple1[3]);
				usr.setMobileno((String) tuple1[4]);
				if (tuple1[5].toString().equalsIgnoreCase("1") || tuple1[5].toString().equalsIgnoreCase("true"))
					usr.setEnable(true);
				else
					usr.setEnable(false);
				usr.setChargebeeid((String) tuple1[6]);
				usr.setAuthKey((String) tuple1[7]);
				if (tuple1[8].toString().equalsIgnoreCase("1") || tuple1[8].toString().equalsIgnoreCase("true"))
					usr.setDelete_user(true);
				else
					usr.setDelete_user(false);
				usr.setCity( (String) tuple1[9] );
				usr.setState( (String) tuple1[10] );
				usr.setCountry( (String) tuple1[11] );
				usr.setPassword_ver((String) tuple1[12]);

				user.add(usr);
			}
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("userlist", user);

		} catch (IndexOutOfBoundsException e) {
			response.put("Status", 0);
			response.put("Msg", "Error occur while getting userlist.");
			response.put("Error", e.getLocalizedMessage());
			log.error(e.getMessage());
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error occur while getting userlist.");
			response.put("Error", e.getLocalizedMessage());
			log.error(e.getMessage());
		}
		return response;
	}

	public boolean updateViewCount(long user_id) {
		
		Session ses = sessionFactory.getCurrentSession();
		try {
			SQLQuery qry = ses.createSQLQuery("SELECT user_id FROM user_viewcount WHERE user_id=" + user_id + ";");
			
			List<BigInteger> ids = qry.list();
			if (!ids.isEmpty()) {
				String updateQry = "UPDATE user_viewcount SET viewcount=viewcount+1 WHERE user_id=" + user_id;

				int updateCount = ses.createSQLQuery(updateQry).executeUpdate();
				log.info("User : " + user_id + "updateQry : " + updateCount);
			} else {
				String insertQry = "INSERT INTO  user_viewcount( user_id,viewcount) VALUES(" + user_id + ",1);";

				int insertCount = ses.createSQLQuery(insertQry).executeUpdate();
				log.info("User : " + user_id + " : insertCount : " + insertCount);
			}
			return true;
			
		} catch (Exception e) {
			log.error("updateViewCount : " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public boolean updateAmazonReview(AmazonUserReview auReview,long monitorType) {
		log.info("Entered :: updateAmazonReview :: ");
		Session ses = sessionFactory.getCurrentSession();
		try {
			SQLQuery qry = ses.createSQLQuery(
					"SELECT user_id FROM amazon_userreview WHERE user_id=" + auReview.getUser_id() + " AND  monitor_type=" +monitorType+";");
			String CurrentTime = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT,
					IrisservicesConstants.UTCFORMAT);

			List<BigInteger> ids = qry.list();
			if (!ids.isEmpty()) {
				
				String updateQry = "NA";
				
				if( auReview.isSkip() ) {
					updateQry = " UPDATE amazon_userreview SET `skip_count` = `skip_count`+1,updatedon='" + CurrentTime + "',monitor_type="+monitorType+" where `skip_count` < 3 AND user_id=" + auReview.getUser_id() + " AND  monitor_type=" +monitorType;
				} else {
					updateQry = "UPDATE amazon_userreview SET rateus_count=" + auReview.getRateus_count()
					+ ", comments='" + auReview.getComments() + "' , app_rateus=" + auReview.isApp_rateus()
					+ ", amazon_rateus=" + auReview.isAmazon_rateus() + ", pagename='" + auReview.getPagename()
					+ "',updatedon='" + CurrentTime + "',`skip_count`=3,monitor_type="+monitorType+" WHERE user_id=" + auReview.getUser_id() + " AND  monitor_type=" +monitorType;	
				}
				
				
				int updateCount = ses.createSQLQuery(updateQry).executeUpdate();
				log.info("User : " + auReview.getUser_id() + "updateQry : " + updateCount);
			} else {
				
				int skip_count = 3;
				if( auReview.isSkip() ) 
					skip_count = 0;
				
				String insertQry = "INSERT INTO `amazon_userreview` (`user_id`, `rateus_count`, `comments`, `app_rateus`, `amazon_rateus`, `pagename`,createdon,updatedon, `skip_count`,monitor_type)"
						+ "VALUES(" + auReview.getUser_id() + "," + auReview.getRateus_count() + ",'"
						+ auReview.getComments() + "'," + auReview.isApp_rateus() + "," + auReview.isAmazon_rateus()
						+ ",'" + auReview.getPagename() + "','" + CurrentTime + "','" + CurrentTime + "', "+ skip_count +" , "+monitorType+" );";

				int insertCount = ses.createSQLQuery(insertQry).executeUpdate();
				log.info("User : " + auReview.getUser_id() + " : insertQry : " + insertQry);
			}
			return true;
		} catch (Exception e) {
			log.error("Error in updateAmazonReview :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return false;
		}
	}

	public int getViewCount(long user_id) {
		log.info("Entered into getViewCount :: user id : "+user_id);
		Session ses = slave5SessionFactory.getCurrentSession();
		try {
			SQLQuery qry = ses.createSQLQuery("SELECT viewcount FROM user_viewcount WHERE user_id=" + user_id + ";");
			int viewcount = 0;
			List<Integer> ids = qry.list();

			if (!ids.isEmpty()) {
				viewcount = ((int) ids.get(0));
			}
			return viewcount;
		} catch (Exception e) {
			log.error("Error in getViewCount :: Session Name : slave5SessionFactory :: error : " + e.getLocalizedMessage());
			return 0;
		}
	}

	public AmazonUserReview getAmazonUserReview(long userid, long monitorType) {
		log.info("Entered getAmazonUserReview ");

		AmazonUserReview amazonReview = null;
		String curDt = IrisservicesUtil.getCurrentTimeUTC();
		String qry = "SELECT `rateus_count`, `comments`, `app_rateus`, `amazon_rateus`, `skip_count`,`updatedon` from amazon_userreview "
				+ " WHERE user_id=" + userid + " AND monitor_type="+monitorType+" ;";

		try {
			Query query = this.slave4SessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
			if (!res.isEmpty()) {
				Object[] amazonObj = (Object[]) res.get(0);
				int rateus_count = (Integer) amazonObj[0];
				String comments = (String) amazonObj[1];
				boolean app_rateus = (boolean) amazonObj[2];
				boolean amazon_rateus = (boolean) amazonObj[3];
				int skip_count = (Integer) amazonObj[4];
				String updateDt = ((Timestamp) amazonObj[5]).toString();

				amazonReview = new AmazonUserReview(userid, rateus_count, comments, app_rateus, amazon_rateus,
						"rvhome", skip_count,updateDt);

			}
		} catch (Exception e) {
			log.error("get GatewayReportCount : ", e.getLocalizedMessage());
		}
		return amazonReview;
	}
	
	public GeneralReview getGeneralReview(long userid, long monitorType) {
	    log.info("Entered :: UserDaoImplV4 :: getGeneralReview :: user id : " + userid);
	    GeneralReview reviewData = new GeneralReview();

	    try {
	        
	        String baseQuery = "SELECT ff.category, ff.param1, ff.param2, ff.monitor_type FROM iris.feedback_form ff LEFT JOIN iris.user_feedback_transaction uft  "
	        					+ " ON uft.userid =:userid AND uft.monitor_type =:monitorType AND uft.updatedon BETWEEN ff.createdon AND ff.expiredon AND (uft.skip = FALSE OR uft.updatedon >= NOW() - INTERVAL 24 HOUR) "
	        					+ " AND ff.category = uft.reviewtype WHERE ff.enable = 1 AND ff.link = 'generalreview' AND ff.expiredon >= NOW() AND uft.id IS NULL ";
	        
	        Query query = this.sessionFactory.getCurrentSession().createSQLQuery(baseQuery);
	        query.setParameter("userid", userid);
	        query.setParameter("monitorType", monitorType);
	        List<Object[]> resultData = query.list();

	        if (!resultData.isEmpty()) {
	            Object[] data = resultData.get(0);
	            reviewData.setShow_review(true);
	            reviewData.setOption_content((String) data[1]);
	            reviewData.setReview_content((String) data[2]);
	            reviewData.setMonitor_type((int) data[3]);
	            reviewData.setReview_type(determineReviewType((String) data[0]));
	            reviewData.setCategory((String) data[0]);
	            return reviewData;
	        }

	        return null;

	    } catch (Exception e) {
	        log.error("Error in getGeneralReview :: userId: " + userid + " :: " + e.getMessage(), e);
	        return null;
	    }
	}

	
	
//	public GeneralReview getGeneralReview(long userid) {
//	    log.info("Entered :: UserDaoImplV4 :: getGeneralReview :: user id : " + userid);
//
//	    try {
//	    	String qry1 = "SELECT * FROM iris.user_feedback_transaction uft WHERE userid = :userid";
//	    	Query query1 = this.sessionFactory.getCurrentSession().createSQLQuery(qry1);
//	        query1.setParameter("userid", userid);
//	        List<Object[]> transactionData = query1.list();
//	        GeneralReview reviewData = new GeneralReview();
//	        
//	        if(!transactionData.isEmpty()) {
//	        	String qry2 = "SELECT ff.category , ff.param1 , ff.param2, ff.monitor_type "
//		        		+ "FROM iris.feedback_form ff JOIN iris.user_feedback_transaction uft ON uft.updatedon < ff.createdon AND ff.expiredon > uft.updatedon "
//		        		+ "WHERE uft.userid =:userid AND ff.enable = '1' AND ff.link = 'generalreview'";
//		        Query query2 = this.sessionFactory.getCurrentSession().createSQLQuery(qry2);
//		        query2.setParameter("userid", userid);
//		        List<Object[]> reviewResults = query2.list();
//		        if (!reviewResults.isEmpty()) {
//		            Object[] data = reviewResults.get(0);
//		            reviewData.setShow_review(true);
//		            reviewData.setOption_content((String) data[1]);
//		            reviewData.setReview_content((String) data[2]);
//		            reviewData.setMonitor_type((int) data[3]);
//
//		            // Set review type based on data[5] value
//		            reviewData.setReview_type(determineReviewType((String) data[0]));
//
//		            return reviewData;
//		        }
//		        return null;
//	        }else {
//	        	
//	        	String qry3 = "SELECT ff.category , ff.param1 , ff.param2 , ff.monitor_type  FROM iris.feedback_form ff WHERE ff.enable = '1' AND ff.link = 'generalreview'";
//		    	Query query3 = this.sessionFactory.getCurrentSession().createSQLQuery(qry3);
//		        List<Object[]> formData = query3.list();
//		        if (!formData.isEmpty()) {
//		        	Object[] data = formData.get(0);
//		        	reviewData.setShow_review(true);
//		            reviewData.setOption_content((String) data[1]);
//		            reviewData.setReview_content((String) data[2]);
//		            reviewData.setMonitor_type((int) data[3]);
//		            // Set review type based on data[5] value
//		            reviewData.setReview_type(determineReviewType((String) data[0]));
//		            return reviewData;
//		        }
//		        
//	        }
//	        return null;
//
//	    } catch (Exception e) {
//	        log.error("Error in getGeneralReview :: userId: " + userid + " :: " + e.getMessage(), e);
//	        return null;
//	    }
//	}


	private int determineReviewType(String reviewTypeValue) {
	    if ("type1".equals(reviewTypeValue)) {
	        return 1;
	    } else if ("type2".equals(reviewTypeValue)) {
	        return 2;
	    } else if ("type3".equals(reviewTypeValue)) {
	        return 3;
	    } else {
	        return 4; // Default case
	    }
	}

	

	@Override
	public boolean updateUserv4byuseridWeb(UserV4 user) {
		log.info("Entered :: UserDaoImplV4 :: updateUserv4byuseridWeb:: ");
		try {
			String qry = "UPDATE user SET firstname='" + user.getFirstname() + "" + "', lastname='" + user.getLastname()
					+ "'," + " zipcode='" + user.getZipcode() + "'" + ", city='" + user.getCity() + "' " + ", state='"
					+ user.getState() + "'" + ", country='" + user.getCountry() + "'" + ", username='"
					+ user.getUsername() + "'" + ", password='" + user.getPassword() + "'" + ", role_id="
					+ user.getRole_id() + "" + ", chargebeeid='" + user.getChargebeeid() + "'" + ", isverified="
					+ user.isVerified() + "" + ", enable=" + user.isEnable() + "" + ", webappid='" + user.getWebappid()
					+ "'" + ", mobileappid='" + user.getMobileappid() + "'" + ", alternate_email='"
					+ user.getAlternateemail() + "'" + ", alternate_phone='" + user.getAlternatephone() + "'"
					+ ", updatedon='" + user.getUpdatedOn() + "'" + ",mobileno='" + user.getMobileno() + "'"
					+ ", email='" + user.getEmail() + "'" + ",password_ver='"+user.getPassword_ver()+"' WHERE id = " + user.getId() + ";";

			int status = sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if (status > 0) {
				log.info("User Updated");
				return true;
			} else {
				log.info("User not Updated");
				return false;
			}

		} catch (Exception e) {
			log.error("updateUserV4: " + e.getLocalizedMessage());
		}
		return false;
	}

	public List<AmazonReviewList> getAmazonReviewList() {
		log.info("Entered :: getAmazonReviewList");
		List<AmazonReviewList> amazonReviewList = new ArrayList<AmazonReviewList>();

		try {
			Session ses = sessionFactory.getCurrentSession();
			String sqlQry = "SELECT U.id AS userid,U.username,U.email,A.comments,A.rateus_count,A.app_rateus,A.amazon_rateus,A.createdon FROM `amazon_userreview`  A JOIN `user` U ON A.user_id=U.id and U.is_test=0 ORDER BY createdon DESC;";
			SQLQuery reviewQry = ses.createSQLQuery(sqlQry);
			List<Object[]> reviewRes = reviewQry.list();

			for (Object[] result : reviewRes) {

				AmazonReviewList amazonReviewdata = new AmazonReviewList();

				if (result[0] != null)
					amazonReviewdata.setUser_id(((BigInteger) result[0]).longValue());
				if (result[1] != null)
					amazonReviewdata.setUsername((String) result[1]);
				if (result[2] != null)
					amazonReviewdata.setEmail((String) result[2]);
				if (result[3] != null)
					amazonReviewdata.setComments((String) result[3]);
				if (result[4] != null)
					amazonReviewdata.setRateus_count((int) result[4]);
				if (result[5] != null)
					amazonReviewdata.setApp_rateus((boolean) result[5]);
				if (result[6] != null)
					amazonReviewdata.setAmazon_rateus((boolean) result[6]);
				if (result[7] != null)
					amazonReviewdata.setCreatedOn(((Timestamp) result[7]).toString());

				amazonReviewList.add(amazonReviewdata);
			}
		} catch (Exception e) {
			log.error("Exception occured while getting amazon review list : " + e.getLocalizedMessage());
			return null;
		}
		return amazonReviewList;
	}

	@Override
	public JUserFeedback getFeedbackLink(long userid, String category) {
		log.info("Entered :: UserDaoImplV4 :: getFeedbackLink :: user id : "+userid+" :: category : "+category);
		try {
			String curDt = IrisservicesUtil.getCurrentTimeUTC();

			String qry = "SELECT * FROM ((SELECT UT.id,F.link,UT.show_form,UT.close_form,UT.retry_count,F.next_show_interval,UT.updatedon,F.id AS formid ,"
					+ " F.createdon FROM user_feedback_transaction  UT  RIGHT JOIN user_feedback UF ON UT.userid=UF.userid  AND UT.formid=UF.formid JOIN feedback_form F ON "
					+ " F.id=UF.formid WHERE UF.userid=" + userid + " AND  DATE(F.expiredon)>=DATE('" + curDt
					+ "') AND F.enable=1 AND F.category='" + category
					+ "' ORDER BY F.createdon DESC LIMIT 1) UNION (SELECT UT.id,F.link,UT.show_form,UT.close_form,UT.retry_count,F.next_show_interval,UT.updatedon,F.id AS formid , F.createdon "
					+ " FROM  feedback_form F LEFT OUTER JOIN user_feedback_transaction UT ON (UT.formid = F.id AND UT.userid="
					+ userid + ") WHERE" + " DATE(F.expiredon)>=DATE('" + curDt + "') AND F.enable=1 AND F.category='"
					+ category + "'  AND F.show_alluser=1 ORDER BY createdon DESC ) )"
					+ " AS tmp ORDER BY createdon DESC LIMIT 1;";
			
//			String qry = "SELECT * FROM iris.v_feedback_rating WHERE userid="+ userid +" AND id IS NOT NULL "
//					+ "AND expiredon>=DATE('"+ curDt +"') AND `enable`=1 "
//					+ "AND category='"+ category +"' ORDER BY createdon DESC LIMIT 1;";
			
			// System.out.println(qry);
			List<Object[]> listRes = slave2SessionFactory.getCurrentSession().createSQLQuery(qry).list();
			JUserFeedback juserfeedback = null;

			if (!listRes.isEmpty()) {
				for (int i = 0; i < listRes.size(); i++) {
					Object[] feedbackData = (Object[]) listRes.get(i);

					long ut_id = 0;
					long formid = ((BigInteger) feedbackData[7]).longValueExact();
					String link = (String) feedbackData[1];

					if (feedbackData[0] != null) {
						ut_id = ((BigInteger) feedbackData[0]).longValueExact();

						boolean show = false;
						if (feedbackData[2].toString().equalsIgnoreCase("1"))
							show = true;

						boolean close = false;
						if (feedbackData[3].toString().equalsIgnoreCase("1"))
							close = true;

						int retry = (int) feedbackData[4];
						int nextShowInterval = (int) feedbackData[5];

						String updateDt = ((Timestamp) feedbackData[6]).toString();
						String currenttime = IrisservicesUtil.getCurrentTimeUTC();
						final SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
						Date curr = formatter.parse(currenttime);

						Date pre = formatter.parse(updateDt);

						long diffInHrs = ((curr.getTime() - pre.getTime()) / 3600000);

						if (diffInHrs >= (nextShowInterval * 24) && !show && !close && retry > 0) {
							juserfeedback = new JUserFeedback();

							juserfeedback.setLink(link);
							juserfeedback.setFormid(formid);
						}
					} else {
						juserfeedback = new JUserFeedback();

						juserfeedback.setLink(link);
						juserfeedback.setFormid(formid);
					}
				}
			}
			return juserfeedback;
		} catch (Exception e) {
			log.error("Error in getFeedbackLink :: Session Name : slave2SessionFactory :: error : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public UserFeedbackTransaction getUserFeedbackTransaction(long userid, long formid) {
		log.info("Entered into getUserFeedbackTransaction :: user id : "+userid+" :: form id : "+formid);
		try {

			String qry = "SELECT * FROM `user_feedback_transaction` WHERE formid=" + formid + " AND userid=" + userid;
			Query query = slave5SessionFactory.getCurrentSession().createSQLQuery(qry)
					.addEntity(UserFeedbackTransaction.class);
			UserFeedbackTransaction feedbackObj = (UserFeedbackTransaction) query.list().get(0);
			return feedbackObj;
		} catch (Exception e) {
			log.error("Error in getUserFeedbackTransaction :: Session Name : slave5SessionFactory :: error : " + e.getLocalizedMessage());
			return null;
		}

	}

	@Override
	public boolean saveOrUpdateUserFeedback(UserFeedbackTransaction feedbackObj) {
		log.info("Entered into saveOrUpdateUserFeedback");
		boolean isSuccess = false;
		try {
			sessionFactory.getCurrentSession().merge(feedbackObj);
			isSuccess = true;
		} catch (Exception e) {
			log.error("Error in saveOrUpdateUserFeedback :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			isSuccess = false;
		}
		return isSuccess;
	}

	@Override
	public ArrayList<OrderMappingDetails> getOrderMappingListByUser(long userid) {
		log.info("Entered into getOrderMappingListByUser :: user id : "+userid);
		try {
			Criteria criteria = this.slave3SessionFactory.getCurrentSession().createCriteria(OrderMappingDetails.class)
					.add(Restrictions.eq("user_id", userid));
			List order = criteria.list();
			return (ArrayList<OrderMappingDetails>) order;
		} catch (Exception e) {
			log.error("Error in getOrderMappingListByUser :: Session Name : slave3SessionFactory :: error : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public String getLastFeedbackDate(long userid,long monitorType) {
		log.info("Entered :: getLastFeedbackDate :: user id : "+userid);
		String lastFeedbackDt = null;
		try {
			Session ses = slave2SessionFactory.getCurrentSession();
			String sqlQry = "SELECT * FROM (SELECT updatedon FROM `user_feedback_transaction` WHERE monitor_type="+monitorType+" AND userid=" + userid
					+ " UNION " + "SELECT updatedon FROM  `amazon_userreview`  WHERE monitor_type="+monitorType+" AND user_id=" + userid
					+ ") t ORDER BY  updatedon DESC LIMIT 1;";
			SQLQuery qry = ses.createSQLQuery(sqlQry);
			List<Object> dt = qry.list();

			if (!dt.isEmpty()) {
				lastFeedbackDt = ((Timestamp) dt.get(0)).toString();
			}

			return lastFeedbackDt;

		} catch (Exception e) {
			log.error("Error in getLastFeedbackDate :: Session Name : slave2SessionFactory :: error : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public void addFreeVPM(String featureName, long userId) {
		log.info("Entered into addFreeVPM : user id : " + userId);
		int limit = 1;
		try {
			String curUTC = IrisservicesUtil.getCurrentTimeUTC();

			String featureQry = "SELECT F.id from feature F WHERE F.feature_name='" + featureName + "'";
			Query featureQuery = this.sessionFactory.getCurrentSession().createSQLQuery(featureQry);
			List featureQueryRes = featureQuery.list();
			long featureid = 0;

			if (!featureQueryRes.isEmpty()) {
				featureid = ((BigInteger) featureQueryRes.get(0)).longValue();
			}

			String qry = "SELECT * FROM user_feature WHERE user_id=" + userId
					+ " AND feature_id = (SELECT id FROM feature WHERE feature_name='" + featureName + "');";
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();

			if (res.isEmpty()) {
				qry = " INSERT INTO user_feature (`user_id`, `feature_id` , `enable` ,  `txn_limit` ,  `extra_txn_limit`, `resettype_id`)"
						+ " VALUES(" + userId + "," + featureid + ",1," + limit + "," + limit + ",1);";

				int resultVal1 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
				log.info("Inserted INTO user_feature : " + resultVal1);

				qry = " INSERT INTO user_txn (`user_id`, `feature_id` , `no_txn` , `last_reset` )" + " VALUES(" + userId
						+ "," + featureid + ",0,'" + curUTC + "');";

				int resultVal2 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
				log.info("Inserted INTO INTO user_txn : " + resultVal2);

				qry = " INSERT INTO user_txn_history (`user_id`, `feature_id` , `no_txn` ,`txn_date` )" + " VALUES("
						+ userId + "," + featureid + ",0,'" + curUTC + "');";

				int resultVal3 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
				log.info("Inserted INTO user_txn_history : " + resultVal3);
			} else {
				qry = " SELECT IF(IFNULL(UT.no_txn,'')='',(UF.txn_limit+UF.extra_txn_limit),"
						+ "((UF.txn_limit+UF.extra_txn_limit) - UT.no_txn)) AS available,UF.txn_limit+UF.extra_txn_limit as tot,UF.feature_id "
						+ " FROM  user_feature UF left JOIN user_txn UT ON UF.feature_id = UT.feature_id AND UF.user_id=UT.user_id JOIN feature F ON "
						+ " UF.feature_id = F.id WHERE F.feature_name='" + featureName + "' AND UF.user_id=" + userId
						+ " AND UF.ENABLE=1";
				query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
				res = query.list();
				log.info(qry);

				if (!res.isEmpty()) {
					Object[] tuple = (Object[]) res.get(0);
					qry = "UPDATE  `user_feature` SET `txn_limit`=" + limit + ",`extra_txn_limit` = "
							+ ((BigInteger) tuple[0]).intValue() + " WHERE feature_id = "
							+ ((BigInteger) tuple[2]).intValue() + " AND user_id=" + userId + " ";

					int resultVal2 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();

					if (resultVal2 != 0) {
						qry = "UPDATE  `user_txn` SET `no_txn`=" + 0 + " WHERE feature_id = "
								+ ((BigInteger) tuple[2]).intValue() + " AND user_id=" + userId + " ";

						int resultVal3 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
						log.info("Updated user_txn : " + resultVal3);
					}
				}
			}

		} catch (Exception e) {
			log.error("Exception occured at addFreeVPM : " + e.getLocalizedMessage());
		}
	}

	@Override
	public int getInappRedirect(String osversion, String ostype) {

		Session ses = sessionFactory.getCurrentSession();
		int inapp_redirect = 1; // 1 - Redirect to CB

		try {
			SQLQuery qry = ses.createSQLQuery("SELECT inapp_redirect FROM `inapp_mapping` WHERE os_type='" + ostype
					+ "' AND os_version='" + osversion + "' limit 1;");
			List<Integer> rediect = qry.list();

			if (!rediect.isEmpty()) {
				inapp_redirect = rediect.get(0).intValue();
			}
			return inapp_redirect;
		} catch (Exception e) {
			log.error("getInappRedirect : " + e.getLocalizedMessage());
			return inapp_redirect;
		}
	}

	@Override
	public long getUserbyVPMid(String vpmid) {

		Session ses = sessionFactory.getCurrentSession();
		try {
			SQLQuery qry = ses.createSQLQuery("SELECT user_id FROM user_metadata WHERE vpm_id='" + vpmid + "';");
			long userid = 0;
			List<BigInteger> userids = qry.list();

			if (!userids.isEmpty()) {
				userid = userids.get(0).longValue();
			}
			return userid;
		} catch (Exception e) {
			log.error("getUserbyVPMid : " + e.getLocalizedMessage());
			return 0;
		}
	}

	@Override
	public boolean updateInAppPurchaseInfo(int inapp_redirect, long userid) {
		log.info("Entered :: UserDaoImplV4 :: updateInAppPurchaseInfo:: ");
		try {
			String curUtc = IrisservicesUtil.getCurrentTimeUTC();
			String qry = "UPDATE `user` SET updatedon='" + curUtc + "' ,`inapp_purchase`='" + inapp_redirect
					+ "' WHERE id = " + userid + ";";

			int status = sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if (status > 0) {
				log.info("User Updated");
				return true;
			} else {
				log.info("User not Updated");
				return false;
			}

		} catch (Exception e) {
			log.error("updateInAppPurchaseInfo : " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean updateUserRVDetails(UserRvDetails auReview, long userid) {
		log.info("Entered :: UserDaoImplV4 :: updateUserRVDetails :: userid : "+userid);
		Session ses = sessionFactory.getCurrentSession();
		try {
			String curUTC = IrisservicesUtil.getCurrentTimeUTC();
			try {
				if(auReview.getOwn_rv() == 0l)
					auReview.setOwn_rv(1);
				
				if(auReview.getRvtype() == 0l)
					auReview.setRvtype(3l);
				
				if(auReview.getWithPet() == 0l)
					auReview.setWithPet(10l);
				
				if(auReview.getHow_often()==0l)
					auReview.setHow_often(12);
				
				String insertQry = "INSERT INTO `user_rvdetails` (`user_id`, own_rv, `rvtype`,with_pet, `how_often`,"
						+ " `enable`,`createdon`,`updatedon`,`others_type`)"
						+ "VALUES('" + userid + "'," + auReview.getOwn_rv() + ",'" + auReview.getRvtype() + "',"
						+ auReview.getWithPet() + ",'" + auReview.getHow_often()
						+ "','1','"+curUTC+"','"+curUTC+"','"+auReview.getOthers_type()+"');";

				int insertCount = ses.createSQLQuery(insertQry).executeUpdate();
				log.info("User : " + userid + " : insertQry : " + insertQry);

			} catch (Exception e) {
				String updateQry = "UPDATE user_rvdetails SET  own_rv=" + auReview.getOwn_rv() + " , rvtype='"
						+ auReview.getRvtype() + "', with_pet=" + auReview.getWithPet() + ", how_often='"
						+ auReview.getHow_often() + "',`enable`= '1',updatedon= '"+curUTC+"',others_type='"
						+ auReview.getOthers_type() + "' WHERE user_id=" + userid;
				int updateCount = ses.createSQLQuery(updateQry).executeUpdate();
				log.info("User : " + userid + "updateQry : " + updateCount);
			}
			return true;
		} catch (Exception e) {
			log.error("Error in updateUserRVDetails :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return false;
		}
	}

//	@Override
//	public UserRvDetails getUserRvDetails(long user_id){
//		log.info("Entered into getUserRvDetails :: user id : "+user_id);
//
//		try {
//			Session session = slave2SessionFactory.getCurrentSession();
//			Criteria criteria = session.createCriteria(UserRvDetails.class).add(Restrictions.eq("user_id", user_id));
//			UserRvDetails rvObj = (UserRvDetails) criteria.list().get(0);
//
//			return rvObj;
//		} catch (Exception e) {
//			log.error("Error in getUserRvDetails :: Session Name : slave2SessionFactory :: error : " + e.getLocalizedMessage());
//			return null;
//			
//		}
//	}

	@Override
	public boolean createFeedbackFormWeb(FeedbackForm feedback) {
		boolean isSuccess = false;
		try {
			sessionFactory.getCurrentSession().merge(feedback);
			isSuccess = true;
		} catch (Exception e) {
			log.error("saveOrUpdate FeedbackForm : ", e.getLocalizedMessage());
			isSuccess = false;
		}
		return isSuccess;
	}

	@Override
	public List<FeedbackForm> listFeedbackWeb() {
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(FeedbackForm.class);
			List<FeedbackForm> feedbackForm = (List<FeedbackForm>) criteria.list();
			return feedbackForm;
		} catch (Exception e) {
			log.error("list FeedbackForm: ", e.getLocalizedMessage());
			return null;
		}
	}
	

	@Override
	public String createUserInChargebee(String fName, String lName, String email, String phoneNo, String userName,
			int amount, String orderID) {
		// Creating user in chargebee
		String chargebeeCusId = "NA";
		String eMail = email.trim();

		eMail = eMail.toLowerCase();
		eMail = eMail.replaceAll("\\s+", "");
		String planId ="NA";
//		String reCustId="NA";
		
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		
		if (_helper.isValidEmail(eMail)) {
			try {
				ListResult resultSet = Customer.list().email().is(eMail).sortByCreatedAt(SortOrder.ASC).request();
				if (resultSet.size() > 0) {
					for (ListResult.Entry entry : resultSet) {
						Customer customer = entry.customer();
						chargebeeCusId = customer.id();

						// Check user having subscription, if not creating chum subscription
//						ListResult result = com.chargebee.models.Subscription.list().customerId().is(chargebeeCusId)
//								.status().in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL)
//								.sortByUpdatedAt(SortOrder.DESC).request();
//
//						if (result.isEmpty())
//							cbService.createDefaultSubsPlan(chargebeeCusId);
						break;
					}
				} else {
//					ListResult rs = null;
//					int i =1;
//					Loop: while ( rs == null || i<=3) {
//						i=i+1;
//						Thread.sleep(2000);
//						rs = Customer.list().email().is(eMail).sortByCreatedAt(SortOrder.ASC).request();
//						
//						if(!rs.isEmpty()) {
//							for (ListResult.Entry entry : rs) {
//								Customer customer = entry.customer();
//								chargebeeCusId = customer.id();
//							}
//							break Loop;
//						}
//					}
					
					if(chargebeeCusId.equalsIgnoreCase("NA") && !eMail.contains(restrictdomain)) {
						Result customerCreateResult = Customer.create().firstName(fName).lastName(lName).email(eMail)
								.phone(phoneNo).request();
	
						Customer customer = customerCreateResult.customer();
						chargebeeCusId = customer.id();  
						
//						if(!chargebeeCusId.equalsIgnoreCase("NA"))
//							cbService.createDefaultSubsPlan(chargebeeCusId);
					}else if(chargebeeCusId.equalsIgnoreCase("NA") && eMail.contains(restrictdomain)) {
						chargebeeCusId =cb_customerid;
					}
					
				}
				
//				ArrayList<String> subDetail = reService.getReSubscription(email,orderID);
//				
//				if(!subDetail.isEmpty()) {
//					reCustId = subDetail.get(0);
//					planId = subDetail.get(1);
//					cbService.createCBSubsForRecharge("NA", chargebeeCusId, planId);
//				}

//			if(!chargebeeCusId.equalsIgnoreCase("NA") && !chargebeeCusId.isEmpty()&&amount>0) {
//				Result result = PromotionalCredit.add()
//						.customerId(chargebeeCusId)
//						.amount(amount) // 100 cent = 1 $
//						.currencyCode("USD")
//						.description(desc)
//						.request();
//				PromotionalCredit promotionalCredit = result.promotionalCredit();
//			}

			} catch (Exception ex) {
				log.error("createUserInChargebee : " + ex.getMessage());
			}

			try {
				User user = userService.getUserByName(userName);

				if (user.getChargebeeid().equalsIgnoreCase("NA")
						&& chargebeeCusId != null
						&& !chargebeeCusId.equalsIgnoreCase("NA"))
					user.setChargebeeid(chargebeeCusId);

//				user.setFirstname(fName);
//				user.setLastname(lName);
//				user.setRecharge_custid(reCustId);
				user.setUpdatedOn(_helper.getCurrentTimeinUTC());
				userService.updateUser(user);

			} catch (Exception e) {
				log.error("Invalid Username while create updating chargeBee id for user : " + userName);
			}
		}
		return chargebeeCusId;
	}
	
	public boolean recreateUserInChargebee(String fName, String lName, String email, String phoneNo, String cbId) {
		// Creating user in chargebee
		String eMail = email.trim();
		eMail = eMail.toLowerCase();
		eMail = eMail.replaceAll("\\s+", "");
		boolean status = false;
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		
		if (_helper.isValidEmail(eMail) && !cbId.equalsIgnoreCase("NA") ) {
			try {
				Result customerCreateResult = Customer.create().firstName(fName).lastName(lName).email(eMail)
						.phone(phoneNo).id(cbId).request();

				Customer customer = customerCreateResult.customer();
				status = true;
				log.info("recreateUserInChargebee:" + status);
			} catch (Exception ex) {
				log.error("recreateUserInChargebee : " + ex.getMessage());
				status = false;
			}
		}
		return status;
	}

	@Override
	public boolean saveOTP(OneTimePassword otpObj) {
		log.info(" Entered into saveOTP :: userId : "+otpObj.getUserId());
		try {
			this.sessionFactory.getCurrentSession().saveOrUpdate(otpObj);
			return true;
		} catch (Exception e) {
			log.error("Error in saveOTP :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return false;
		}
	}


	@Override
	public boolean validateOTP(long userId,OneTimePassword otpObj, int validMinutes) {
		log.info(" Entered into validateOTP :: userId : "+otpObj.getUserId());
		try {
			
			String qry = "UPDATE one_time_password SET status=1, updated_on='"+ _helper.getCurrentTimeinUTC() +"' WHERE userId="+userId+" AND status=0 AND otp = "+otpObj.getOtp()+" AND ( created_on > DATE_SUB(NOW(), INTERVAL "+validMinutes+" MINUTE) ) ";
			int updated = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
	
			if( updated >= 1 ) {
				return true;
			} else {
				return false;
			}
			
		} catch (Exception e) {
			log.error(" Error in validateOTP : "+ e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public boolean updateOrderMappingDetails(long userId, String qrc, String orderId, String orderChannel,
			String deviceStateId, String orderSku, String orderedDate) {
		log.info("Entered in updateOrderMappingDetails :: userId "+userId+" orderId : "+orderId);
		try {
			if(orderedDate.isEmpty() || orderedDate.equalsIgnoreCase("NA"))
				orderedDate = "1753-01-01 00:00:01";
			
			String updateQry = "UPDATE ordermappingdetails SET devicestate_id='"+deviceStateId+"',STATUS='Success',orderid='"+orderId+"',orderchannel='"+orderChannel+"',externalsku='"+orderSku+"',orderdate='"+orderedDate+"' WHERE user_id="+userId+" and qrccode='"+qrc+"'";

			int update = this.sessionFactory.getCurrentSession().createSQLQuery(updateQry).executeUpdate();
		
			if( update >= 1 ) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			log.error("Error in updateOrderMappingDetails  : "+e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public int updateUserPasswordV2(String username, String password) {
		log.info("Entered :: OptimizedDoaImpl :: updateUserV4 :: username : "+username+" :: password : "+password);
		try {

			Session ses = sessionFactory.getCurrentSession();
			
			String hql = "UPDATE user set " +
			          "password = :Password, password_ver='V2'" +  
			          " where username = :Username";

			Query qr = ses.createSQLQuery(hql);

			qr.setParameter("Password",password);

			qr.setParameter("Username",username);

			int status =  qr.executeUpdate();

			if (status > 0)
				log.info("User Updated");

			return status;

		} catch (Exception e) {
			log.error("Error in updateUserV4 :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
		}

		return -1;

	}

	@Override
	public int updateUserPasswordV3(String username, String password, String password_ver) {
		log.info("Entered :: updateUserPasswordV3 :: username : "+username+" :: password : "+password+" :: password_ver : "+password_ver);
		try {

			Session ses = sessionFactory.getCurrentSession();

			String hql = "UPDATE user set " +
					"password = :Password, password_ver= :password_ver" +
					" where username = :Username";

			Query qr = ses.createSQLQuery(hql);

			qr.setParameter("Password",password);

			qr.setParameter("Username",username);

			qr.setParameter("password_ver",password_ver);

			int status =  qr.executeUpdate();

			if (status > 0)
				log.info("User Updated");

			return status;

		} catch (Exception e) {
			log.error("Error in updateUserPasswordV3 :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
		}

		return -1;

	}
	
	@Override
	public boolean updateEvalidation(String encryptId, String encryptPwd) {
		log.info(" Entered into updateEvalidation :: userId : " + encryptId);
		String query = "update evalidation set entity_password = '" + encryptPwd + "',updated_date=UTC_TIMESTAMP() where entity_id = '" + encryptId
				+ "'; ";
		try {
			int update = this.sessionFactory.getCurrentSession().createSQLQuery(query).executeUpdate();
			if (update >= 1) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			log.error("Error in updateEvalidation :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public boolean insertEvalidation(String encryptId, String encryptPwd) {
		log.info(" Entered into insertEvalidation :: userId : " + encryptId);
		String query = "insert into evalidation (`entity_password` , `entity_id`, `updated_date` ) values ('" + encryptPwd + "','" + encryptId
				+ "',UTC_TIMESTAMP()); ";
		try {
			int update = this.sessionFactory.getCurrentSession().createSQLQuery(query).executeUpdate();
			if (update >= 1) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			log.error("Error in insertEvalidation :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public boolean getEvalidation(String encryptId) {
		log.info("Entered in getEvalidation :: userId : " + encryptId);
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(EValidation.class).add(Restrictions.eq("entity_id", encryptId));
			List<EValidation> eValid = (List<EValidation>) criteria.list();

			if (eValid.isEmpty())
				return false;
			else
				return true;
		} catch (Exception e) {
			log.error("Error in getEvalidation :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public String getEValidationPassword(String encryptId) {
		log.info("Entered in getEvalidation :: userId : " + encryptId);
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(EValidation.class).add(Restrictions.eq("entity_id", encryptId));
			List<EValidation> eValid = (List<EValidation>) criteria.list();

			if (!eValid.isEmpty())
				return eValid.get(0).getEntity_password();
			else
				return null;
		} catch (Exception e) {
			log.error("Exception in getEvalidation : " + e.getLocalizedMessage());
			return null;
		}
	}
	
	public boolean getFreeTrialDays(String chargebeeid) {
		log.info("Entered UserDaoImpl :: getFreeTrialDays ");
		boolean free_trial_applied = true;
		try	{
			String qry = "SELECT free_trial_applied FROM `user_subscription` WHERE chargebeeid='"+chargebeeid+"';" ;				

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);	
			List res = query.list();

			if (!res.isEmpty())
				free_trial_applied = (boolean) res.get(0);
		}catch (Exception e) {
			log.error("get FreeTrialDays : ",e.getLocalizedMessage());
		}
		return free_trial_applied;
	}
	
	@Override
	public boolean saveFreshChatId(FreshChat freshChat) {
		log.info(" Entered into saveFreshChatId :: userId : "+freshChat.getUser_id());
		try {
			this.sessionFactory.getCurrentSession().saveOrUpdate(freshChat);
			return true;
		} catch (Exception e) {
			log.error(" Error in saveFreshChatId : "+ e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public FreshChat getFreshChatByUserId(long user_id) {
		log.info(" Entered into getFreshChatByUserId :: userId : "+user_id);
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(FreshChat.class).add(Restrictions.eq("user_id", user_id));
			List<FreshChat> freshChatList = (List<FreshChat>) criteria.list();

			if (!freshChatList.isEmpty())
				return freshChatList.get(0);
			else
				return null;
		} catch (Exception e) {
			log.error(" Error in getFreshChatByUserId : "+ e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public long getUserGateway(String key, long value) {
		log.info("Entered into getUserGateway :: key : "+key+" :: value : "+value);
		try {
			String qry = "";
			if( key.contains("userId") ) {
				qry = "SELECT gatewayId FROM `usergateway` WHERE "+key+"='"+value+"';" ;
			} else {
				qry = "SELECT userId FROM `usergateway` WHERE "+key+"='"+value+"';" ;
			}				

			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);	
			List res = query.list();

			if (!res.isEmpty())
				return ( (BigInteger) res.get(0)).longValue();
			return 0;
		} catch (Exception e) {
			log.error("Error in getUserGateway :: error : "+e.getLocalizedMessage());
			return 0;
		}
	}

	@Override
	public boolean deleteUserById(long userid) {
		log.info("Entered into deleteUserById :: ");
		try {
			String qry = "UPDATE user set delete_user=1,delete_time=UTC_TIMESTAMP() where id=" + userid;
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			int result = query.executeUpdate();
			if (result > 0)
				return true;
		} catch (Exception e) {
			log.error("Error in deleteUserById : " + e.getMessage());
		}
		return false;
	}

	@Override
	public boolean saveOrUpdateUserDelete(UserDelete user) {
		log.info("Entered into saveOrUpdateUserDelete...");
		try {
			String qry = "";
			if(user.getId()>0)
				qry = "update user_dlt set dlt_reason='"+user.getDlt_reason()+"',dlt_description='"+user.getDlt_description()+"',updated_date='"+user.getUpdated_date()+"' where id="+user.getId();
			else
				qry = "insert into user_dlt(user_id,dlt_reason,dlt_description,updated_date) values("+user.getUser_id()+",'"+user.getDlt_reason()+"','"+user.getDlt_description()+"','"+user.getUpdated_date()+"')";
			
			int saveOrUpdate = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if(saveOrUpdate>0)
				return true;
			else
				return false;
		} catch (Exception e) {
			log.error("Error in saveOrUpdateUserDelete : " + e.getMessage());
		}
		return false;
	}

	@Override
	public long getUserDlt(long user_id) {
		log.info("Entered into getUserDlt...");
		try {
			String qry = "select id from user_dlt where user_id="+user_id;
			List<BigInteger> idList = (List<BigInteger>) this.sessionFactory.getCurrentSession().createSQLQuery(qry).list();
			if(!idList.isEmpty())
				return idList.get(0).longValue();
		} catch (Exception e) {
			log.error("Error in getUserDlt : " + e.getMessage());
		}
		return 0;
	}
	
	@Override
	public long getUserDltId(String meid) {
		log.info("Entered into getUserDltId...");
		try {
			String qry = "select id from user_dlt_info where meid='"+meid+"'";
			BigInteger id = (BigInteger) this.sessionFactory.getCurrentSession().createSQLQuery(qry).list().get(0);
			return id.longValue();
		} catch (Exception e) {
			log.error("Error in getUserDltDetails : "+e.getMessage());
		}
		return 0;
	}

	@Override
	public boolean saveOrUpdateUserDltDetails(UserDltInfo userDlt) {
		log.info("Entered into saveOrUpdateUserDltDetails...");
		try {
			this.sessionFactory.getCurrentSession().saveOrUpdate(userDlt);
			return true;
		} catch (Exception e) {
			log.error("Error in saveOrUpdateUserDelete : " + e.getMessage());
		}
		return false;
	}
	
	@Override
	public List<CountryCodeV4> getCountryCodeList(String req_from) {
		log.info("Entered getCountryCodeList : OptimizedDoaImpl");
		List<CountryCodeV4> rvList = null;
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT * FROM `country_code`";
			if(req_from.equalsIgnoreCase("settings")) {
				qry += " where page_select IN(0,2) order by page_select DESC;";
			} else {
				qry += " where page_select IN(0,1) order by page_select DESC;";
			}
			Query query = session.createSQLQuery(qry).addEntity(CountryCodeV4.class);
			rvList = query.list();
		} catch (Exception e) {
			log.error("Exception in getCountryCodeList : " + e.getLocalizedMessage());
			return null;
		}
		return rvList;
	}
	
	@Override
	public String getUserCountryCode(String country) {
		log.info("Entered getUserCountryCode : OptimizedDoaImpl");
		String countryCode = "NA";
		try {
			Session session = sessionFactory.getCurrentSession();
			SQLQuery qry = session
					.createSQLQuery("SELECT code FROM `country_code` where short_name = '" + country + "';");
			countryCode = (String) qry.list().get(0);
		} catch (Exception e) {
			log.error("Exception in getUserCountryCode : " + e.getLocalizedMessage());
		}
		return countryCode;
	}

	@Override
	public UserMetaData getUserMetaData(long user_id) {
		log.info("Entered getUserMetaData : OptimizedDoaImpl");
		try {
			Criteria criteria = this.sessionFactory.getCurrentSession().createCriteria(UserMetaData.class)
					.add(Restrictions.eq("user_id", user_id));
			List<UserMetaData> userMetaData = (List<UserMetaData>) criteria.list();

			if (!userMetaData.isEmpty())
				return (UserMetaData) userMetaData.get(0);
			else
				return null;
		} catch (Exception e) {
			log.error("Exception in getUserMetaData : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public boolean enableDisableMarketingNotification(UserMetaData userMetaData) {
		log.info("Entered enableDisableMarketingNotification : OptimizedDoaImpl");
		boolean status = false;

		try {
			sessionFactory.getCurrentSession().merge(userMetaData);
			status = true;
		} catch (Exception e) {
			log.error("Exception in enableDisableMarketingNotification : " + e.getLocalizedMessage());
		}

		return status;
	}

	@Override
	public boolean removeDeleteRequest(long user_id, boolean isdelete) {
		log.info("Entered into removeDeleteRequest...");
		long delete = 0;
		if (isdelete)
			delete = 1;
		try {
			String qry = "update `user` set delete_user='" + delete + "' where id=" + user_id;
			int saveOrUpdate = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if (saveOrUpdate > 0)
				return true;
			else
				return false;
		} catch (Exception e) {
			log.error("Error in saveOrUpdateUserDelete : " + e.getMessage());
		}
		return false;
	}

	@Override
	public boolean saveEmailOTP(EmailOtp emailOtpObj) {
		log.info(" Entered into saveEmailOTP :: email : "+emailOtpObj.getEmail());
		try {
			this.sessionFactory.getCurrentSession().saveOrUpdate(emailOtpObj);
			return true;
		} catch (Exception e) {
			log.error("Error in saveEmailOTP :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public boolean validateEmailOTP(String userName, EmailOtp otpObj, int validMinutes) {
		log.info(" Entered into validateEmailOTP :: email : "+otpObj.getEmail());
		try {
			
			String qry = "UPDATE email_verify_otp SET status=1, updated_on='"+ _helper.getCurrentTimeinUTC() +"' WHERE email='"+userName+"' AND status=0 AND otp = "+otpObj.getOtp()+" AND ( created_on > DATE_SUB(NOW(), INTERVAL "+validMinutes+" MINUTE) ) ";
			int updated = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
	
			if( updated >= 1 ) {
				return true;
			} else {
				return false;
			}
			
		} catch (Exception e) {
			log.error(" Error in validateEmailOTP : "+ e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public boolean saveOrUpdateUserMini(long userId, String deviceId, String deviceName,boolean isPetCam) {

		try {
			Session ses = sessionFactory.getCurrentSession();

			SQLQuery qry = ses.createSQLQuery("SELECT device_id FROM user_minicam WHERE device_id='" + deviceId + "'  AND user_id=" + userId + ";");

			List<BigInteger> ids = qry.list();

			if (!ids.isEmpty()) {
				String updateQry = "UPDATE user_minicam SET is_petcam="+isPetCam+",user_id='" + userId + "', device_name='"+deviceName+"' WHERE device_id='" + deviceId +"';";

				int insertCount = ses.createSQLQuery(updateQry).executeUpdate();
			} else {
				String curUtc = IrisservicesUtil.getCurrentTimeUTC();
				String insertQry = "INSERT INTO  user_minicam( user_id,device_name,device_id,createdon,is_petcam) VALUES(" + userId + ",'"
						+ deviceName + "', '" + deviceId + "','"+ curUtc +"'," + isPetCam+");";

				int insertCount = ses.createSQLQuery(insertQry).executeUpdate();
				log.info("insertCount:userId : " + userId + ": " + insertCount);
			}
		} catch (Exception e) {
			log.error("Exception in saveOrUpdateUserMini: " + e.getLocalizedMessage());
		}
		return true;
	}

	@Override
	public List<Object> listUserMiniCam(long userId) {
		List<Object> minicamList = new ArrayList<Object>();
		
		try {
			Session ses = sessionFactory.getCurrentSession();

			SQLQuery qry = ses.createSQLQuery("SELECT device_id,device_name,is_petcam FROM user_minicam "
					+ "WHERE is_deleted = 0 AND is_petcam = 0 AND user_id=:userid ;");
			qry.setParameter("userid", userId);
			List<Object[]> devices = qry.list();

			if (!devices.isEmpty()) {
				for (int i = 0; i < devices.size(); i++) {
					Object[] deviceData = (Object[]) devices.get(i);

					String device_id =(String) deviceData[0];
					String device_name = (String) deviceData[1];
					boolean is_petcam = (boolean) deviceData[2];
					
					DeviceList dev = new DeviceList(device_id, device_name, is_petcam);
					
					minicamList.add(dev);
					
				}
			} 
		} catch (Exception e) {
			log.error("Exception in listUserMiniCam: " + e.getLocalizedMessage());
		}
		
		return minicamList;
	}
	
	@Override
	public boolean updateDeleteDeviceStatus(long userId, String deviceId) {

		try {
			Session ses = sessionFactory.getCurrentSession();

			if (!deviceId.isEmpty()) {
				String curUtc = IrisservicesUtil.getCurrentTimeUTC();
				String updateQry = "UPDATE user_minicam SET is_deleted=1, updatedon='"+curUtc+"' WHERE user_id = "+userId+" AND device_id NOT IN (" + deviceId +");";

				int insertCount = ses.createSQLQuery(updateQry).executeUpdate();
			} 
		} catch (Exception e) {
			log.error("Exception in updateDeleteDeviceStatus: " + e.getLocalizedMessage());
		}
		return true;
	}

	@Override
	public ArrayList<String> getUserFirebaseEvents(long userid)  {
		log.info("Entered UserDaoImpl ::  userid :"+userid);
		ArrayList<String> events = new ArrayList<String>();
		try {

			Query qry = sessionFactory.getCurrentSession().createSQLQuery("SELECT event_id FROM user_firebase_event WHERE view_count=0 AND user_id=:user_id AND to_dt >=CURRENT_TIMESTAMP ");
			qry.setParameter("user_id", userid);
			List<String> res = qry.list();
			if(res.isEmpty()) {
				qry = sessionFactory.getCurrentSession().createSQLQuery("SELECT event_id FROM user_firebase_event WHERE is_common_event=1 AND to_dt >=CURRENT_TIMESTAMP ");
				res = qry.list();
				if(!res.isEmpty() && res.size() > 0 ) {
					for(String evt : res) {
						events.add(evt);
					}
				}
			}else {
				for(String evt : res) {
					events.add(evt);
				}
			}
		}
		catch(Exception e) {
			log.error("getUserFirebaseEvents exception : "+ e.getLocalizedMessage());
		}

		return events;
	}
	
	
	@Override
	public ArrayList<String> checkReferralEligible(long userid)  {
		log.info("Entered UserDaoImpl ::  userid :"+userid);
		ArrayList<String> events = new ArrayList<String>();
		try {

			Query qry = sessionFactory.getCurrentSession().createSQLQuery("SELECT id FROM `device_subscription` WHERE  user_id=:user_id"
					+" AND sub_status='ACTIVE' AND `paid_subscription_date`   BETWEEN DATE_ADD(NOW(), INTERVAL -60 DAY)   AND "
					+ " DATE_ADD(NOW(), INTERVAL -30 DAY)"
					+ " AND user_id NOT IN (SELECT user_id FROM user_firebase_event WHERE user_id = :user_id AND event_id = 'refer_event')");

			qry.setParameter("user_id", userid);
			List<String> res = qry.list();

			if(!res.isEmpty() && res.size() > 0 ) {
				events.add("refer_event");
			}
		}
		catch(Exception e) {
			log.error("getUserFirebaseEvents exception : "+ e.getLocalizedMessage());
		}

		return events;
	}
	
	
	@Override
	public ArrayList<String> checkActivateEligible(long userid)  {
		log.info("Entered UserDaoImpl ::  userid :"+userid);
		ArrayList<String> events = new ArrayList<String>();
		try {

			Query qry = sessionFactory.getCurrentSession().createSQLQuery("SELECT ds.instal_date FROM device_subscription ds "
					+ " JOIN usergateway UG ON ds.user_id= UG.userid JOIN gateway g ON g.id = ds.gateway_id JOIN assetmodel a ON a.id = g.model_id AND is_deleted=0 "
					+ " WHERE ds.user_id=:user_id AND ds.paid_subscription_date LIKE '1753%'  AND ds.instal_date NOT LIKE '1753%'  AND monitor_type_id = 1 "
					+ " AND ds.user_id NOT IN (SELECT user_id FROM user_firebase_event WHERE user_id = :user_id AND event_id = 'activate_event') "
					+ " ORDER BY ds.instal_date DESC LIMIT 1");

			qry.setParameter("user_id", userid);
			List<Object> res = qry.list();

			if(!res.isEmpty() && res.size() > 0 ) {

					Timestamp date = (Timestamp) res.get(0);
					Calendar cal = Calendar.getInstance();
					cal.setTime(date);
					cal.add(Calendar.HOUR, 48); // shows  2 days from day of installed
					Date curDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse( _helper.getCurrentTimeinUTC() );

					if( curDate.after(cal.getTime()) ) 
						events.add("activate_event");		
			}
		}
		catch(Exception e) {
			log.error("getUserFirebaseEvents exception : "+ e.getLocalizedMessage());
		}

		return events;
	}
	
	@Override
	public void updateUserTimezone(long userid, String timezone) {
		try {
			String qry = "SELECT * FROM user_timezone WHERE user_id="+userid+";";
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
			int resultVal1 =0;
			if(res.isEmpty())
			{				
				qry = " INSERT INTO user_timezone (`user_id`, `timezone`)" + 
						" VALUES("+userid+",'"+timezone+"');";
				resultVal1 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			}
			else {
				qry = " update user_timezone set `timezone`='"+timezone+"' where `user_id`='"+userid+"';";
				resultVal1 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			}
			
		}catch (Exception e) {
			log.error("updateUserTimezone : ",e.getLocalizedMessage());
		}
	}
	
	@Override
	public void updateAddonTomorrowStatus(long gatewayid, boolean status) {
		try {
			
			String qry = " update gateway set `add_on`="+status+" where `id`='"+gatewayid+"';";
			int resultVal1 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			
		}catch (Exception e) {
			log.error("updateAddonTomorrowStatus : ",e.getLocalizedMessage());
		}
	}
	
	@Override
	public void updateDeviceNotoficationStatus(long gatewayid, boolean status,long userId,boolean habit_notification) {
		
		try {
			String qry = "SELECT id FROM restrict_push_notification WHERE user_id=" + userId + " AND gateway_id='"
					+ gatewayid + "';";
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();
			int resultVal1 = 0;

			if (res.isEmpty()) {

				qry = " INSERT INTO restrict_push_notification (`user_id`, `gateway_id`, `device_notification` , `habit_alert_notification`) VALUES("
						+ userId + ",'" + gatewayid + "'," + status + "," + habit_notification + ");";

				resultVal1 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			} else {

				qry = " update restrict_push_notification set device_notification=" + status + ", habit_alert_notification = "+ habit_notification +" where gateway_id='"
						+ gatewayid + "' and user_id = " + userId + ";";
				resultVal1 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			}

		}catch (Exception e) {
			log.error("updateDeviceNotificationStatus : ",e.getLocalizedMessage());
		}
	}

	@Override
	public boolean checkDeviceNotify(long userId,long gatewayid) {
		log.info("Entered into checkDeviceNotify :: user_id : "+ userId+" :: gateway_id : "+ gatewayid);
		boolean enable = false; 
		try {
			String qry = "SELECT device_notification FROM restrict_push_notification WHERE user_id=" +userId+" AND gateway_id='"+gatewayid+"';";
			Query query =this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res =query.list(); 
			int resultVal1 =0;
			
			if(!res.isEmpty()) {
				enable = (boolean) res.get(0);
			} else {
				qry = " INSERT INTO restrict_push_notification (`user_id`, `gateway_id`, `device_notification`) VALUES("
						+ userId + ",'" + gatewayid + "',1);";

				resultVal1 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
				enable =true;
			}
		}catch (Exception e) {
			log.error("Error in checkDeviceNotify :: Error : "+e.getLocalizedMessage());
		}
		return enable;
	}

	@Override
	public boolean delUserGatewayV2(long user_id, long gateway_id) {
		log.info("Entered into delUserGatewayV2 :: user_id : "+user_id+" :: gateway_id : "+ gateway_id);
		try {
			String qry = "delete from usergateway where userId = " + user_id + " and gatewayId = " + gateway_id
					+ ";";
			int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			
			return (resultVal>0);
		} catch (Exception e) {
			log.error("Error in delUserGatewayV2 :: Error : "+e.getLocalizedMessage());
		}
		return false;
	}
	
	@Override
	public List<Object> listUserPetCam(long userId) {
		List<Object> minicamList = new ArrayList<Object>();
		
		try {
			Session ses = sessionFactory.getCurrentSession();

			SQLQuery qry = ses.createSQLQuery("SELECT device_id,device_name,is_petcam FROM user_minicam WHERE is_deleted = 0 AND is_petcam = 1 AND user_id='" + userId + "';");

			List<Object[]> devices = qry.list();

			if (!devices.isEmpty()) {
				for (int i = 0; i < devices.size(); i++) {
					Object[] deviceData = (Object[]) devices.get(i);

					String device_id =(String) deviceData[0];
					String device_name = (String) deviceData[1];
					boolean is_petcam = (boolean) deviceData[2];
					
					DeviceList dev = new DeviceList(device_id, device_name, is_petcam);
					
					minicamList.add(dev);
					
				}
			} 
		} catch (Exception e) {
			log.error("Exception in listUserPetCam: " + e.getLocalizedMessage());
		}
		
		return minicamList;
	}

	@Override
	public boolean checkActiveSubsForAmazonReview(long user_id) {
		log.info("Entered into checkActiveSubsForAmazonReview :: user_id : "+ user_id);
		try {
			
			String query = "SELECT id FROM `device_subscription` "
					+ " WHERE sub_status='ACTIVE' AND user_id = :user_id AND UTC_TIMESTAMP() > DATE_ADD(paid_subscription_date, INTERVAL 5 DAY);";
			
			log.info("select query : "+ query);
			Query qry = sessionFactory.getCurrentSession().createSQLQuery( query );
			qry.setParameter("user_id", user_id);

			 if( qry.list().size() > 0 ) 
				 return true;
			 
		} catch (Exception e) {
			log.error("Error in checkActiveSubsForAmazonReview :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}
	
	@Override
	public ArrayList<String> checkAmazonRatingEligible(long userid)  {
		log.info("Entered UserDaoImpl ::  userid :"+userid);
		ArrayList<String> events = new ArrayList<String>();
		try {

			String qry = "SELECT OM.id FROM `ordermappingdetails` OM JOIN`device_subscription`  DS ON OM.user_id=DS.user_id "
					+ "WHERE sub_status='active' AND orderchannel='amazon' AND OM.user_id =:user_id"
					+ " AND OM.user_id NOT IN (SELECT user_id FROM user_firebase_event WHERE user_id = :user_id AND event_id = 'amz_review')";
			
			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("user_id", userid);
			List<String> res = query.list();

			if(!res.isEmpty() && res.size() > 0 ) {
				events.add("amz_review");
			}
		}
		catch(Exception e) {
			log.error("getUserFirebaseEvents exception : "+ e.getLocalizedMessage());
		}

		return events;
	}
	
	@Override
	public boolean updateFirebaseCnt(long userid,int view, int click,String eventname) {
		String strqry ="";
		long inapp_id =0;
		boolean stat= false;
		try {
			try {
				String qry = "SELECT event_id FROM `firebase_event` WHERE event_name =:evname ";

				Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
				query.setParameter("evname", eventname);
				List<BigInteger> res = (List<BigInteger>) query.list();
				
				if (!res.isEmpty())
					inapp_id = res.get(0).longValue();
				
			} catch (Exception e) {
				log.error("updateDeviceNotificationStatus : ",e.getLocalizedMessage());
			}
			
			String from_dt = IrisservicesUtil.getCurrentTimeUTC();
			String to_dt = IrisservicesUtil.getCurrentDateTime("yyyy-MM-dd HH:mm:ss", "00:00", 30);

			strqry = " INSERT INTO `user_firebase_event` (`user_id`, `event_id`, `from_dt`, `to_dt`, `updated_on`, `view_count`,click_count, `inapp_id`)"
						+ " VALUES (:userid, :eventid, :from_dt, :to_dt, :upt_dt, :view_count, :click_count, :inapp_id)";

						SQLQuery qry = this.sessionFactory.getCurrentSession().createSQLQuery(strqry);
						qry.setParameter("userid", userid);
						qry.setParameter("eventid", eventname);
						qry.setParameter("from_dt", from_dt);
						qry.setParameter("upt_dt", from_dt);
						qry.setParameter("to_dt", to_dt);
						qry.setParameter("click_count", click);
						qry.setParameter("view_count", view);
						qry.setParameter("inapp_id", inapp_id);
						
			int	resultVal1 = qry.executeUpdate();
			if(resultVal1>0)
				stat = true;

		}catch (Exception e) {
			
			try {
				strqry = "UPDATE `user_firebase_event` SET `view_count` =view_count+ :view_count , `click_count` =click_count + :click_count WHERE `user_id` =:userid AND `inapp_id` =:inappid";
				
				SQLQuery qry = this.sessionFactory.getCurrentSession().createSQLQuery(strqry);
				qry.setParameter("view_count", view);
				qry.setParameter("click_count", click);
				qry.setParameter("userid", userid);
				qry.setParameter("inappid", inapp_id);				
				int status = qry.executeUpdate();			
				if(status>0)
					stat = true;
			}
			catch (Exception ex) {
				log.error("updateDeviceNotificationStatus : ",e.getLocalizedMessage());
			}
			
		}
		return stat;
	}

	@Override
	public UserTimeZone getUserTimeZone(long user_id) {
		log.info("Entered into getUserTimeZone :: user_id : "+ user_id);
		try {
			List userTimeZoneList = sessionFactory.getCurrentSession().createCriteria(UserTimeZone.class).add(Restrictions.eq("user_id", user_id)).list();
			
			if( userTimeZoneList.isEmpty() ) {
				log.info("No user time zone found for user_id : "+ user_id);
				return null;
			}
			return (UserTimeZone) userTimeZoneList.get(0);
		} catch (Exception e) {
			log.error("Error in getUserTimeZone :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public boolean checkHabitalertNotify(long userId,long gatewayid) {
		log.info("Entered into checkHabitalertNotify :: user_id : "+ userId+" :: gateway_id : "+ gatewayid);
		boolean enable = false; 
		try {
			String qry = "SELECT habit_alert_notification FROM restrict_push_notification WHERE user_id=" +userId+" AND gateway_id='"+gatewayid+"';";
			Query query =this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res =query.list(); 
			int resultVal1 =0;
			
			if(!res.isEmpty()) {
				enable = (boolean) res.get(0);
			} else {
				qry = " INSERT INTO restrict_push_notification (`user_id`, `gateway_id`, `device_notification`) VALUES("
						+ userId + ",'" + gatewayid + "',1);";

				resultVal1 = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
				enable =true;
			}
		}catch (Exception e) {
			log.error("Error in checkHabitalertNotify :: Error : "+e.getLocalizedMessage());
		}
		return enable;
	}
	
	@Override
	public PetCaloriesCalculation checkFoodFeededDetails(long userId,long gatewayid) {
		log.info("Entered into checkFoodFeededDetails :: user_id : "+ userId+" :: gateway_id : "+ gatewayid);
		PetCaloriesCalculation enable = new PetCaloriesCalculation(); 
		try {
			String qry = "SELECT pfd.meal_times,pfd.req_calories,pfd.req_grams,ut.timezone,pf.calories FROM pet_feed_details pfd JOIN user_timezone ut ON ut.user_id = pfd.user_id "
					+ "JOIN pet_food pf ON pf.id = pfd.pet_food_id "
					+ "WHERE pfd.gateway_id = '"+gatewayid+"' AND pfd.user_id = " +userId+";";
			Query query =this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			
			List res =query.list();
			
			if(!res.isEmpty()) {
				Object[] tuple = (Object[]) res.get(0);
				String mealTime = (String) tuple[0];
				double req_cal = (double) tuple[1];
				double req_grams = (double) tuple[2];
				String userTimezone = (String) tuple[3];
				
				String[] timeArray = mealTime.split(",");
		        
				DateFormat timeFormat = new SimpleDateFormat("HH:mm");
				timeFormat.setTimeZone(TimeZone.getTimeZone("GMT"+userTimezone));
				
				Date currentTime = timeFormat.parse(timeFormat.format(new Date()));
				int count = 0;
		        if(timeArray.length > 0) {
		        	for (int i = 0; i < timeArray.length; i++) {
		        		 Date mealsTime = timeFormat.parse(timeArray[i]);
		        		 if (currentTime.after(mealsTime)) {
		        			 count++;
		        		 }
		            }
		        	
		        	double caloriesPerMeal = (int) req_cal / timeArray.length;
					double caloriesPerGram = (double) tuple[4] / 1000;

					double weight_grams = (int) caloriesPerMeal / (int) caloriesPerGram;
					
			        double curgram = (int) weight_grams * count;
			        
			        enable.setCurrentMealCnt(count);
			        enable.setCurrRegGrams(curgram);
			        enable.setMealTime(mealTime);
			        enable.setReq_cal(req_cal);
			        enable.setReq_grams(req_grams);
			        enable.setTotMealCnt(timeArray.length);
		        }
		        
				DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
				
				DateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				
				Date convertedDateForAft = _helper.timeZoneConverter( "yyyy-MM-dd", "+00:00", userTimezone, dateFormat1.format(new Date())); 
				
				String qry1 = "SELECT IFNULL(SUM(si.total_intake),0),IFNULL(SUM(si.last_weight),0) FROM sensor_intake_report si WHERE si.gateway_id = '"+gatewayid+"' AND si.date = '"+dateFormat.format(convertedDateForAft)+"';";
				
				Query query1 =this.sessionFactory.getCurrentSession().createSQLQuery(qry1);
				
				List res1 = query1.list();
				
				if(!res1.isEmpty()) {
					Object[] senResult = (Object[]) res1.get(0);
					BigDecimal intake = (BigDecimal) senResult[0];
					BigDecimal curWht = (BigDecimal) senResult[1];
					long totalWeight = intake.longValue() + curWht.longValue();
					enable.setFeededWeight(totalWeight);
				}
			}
			
		}catch (Exception e) {
			log.error("Error in checkFoodFeededDetails :: Error : "+e.getLocalizedMessage());
		}
		return enable;
	}
	
	@Override
	public boolean checkFoodFeededorNot(long userId,long gatewayid) {
		log.info("Entered into checkFoodFeededorNot :: user_id : "+ userId+" :: gateway_id : "+ gatewayid);
		boolean enable = false; 
		try {
			String qry = "SELECT pfd.meal_times,ut.timezone FROM pet_feed_details pfd JOIN user_timezone ut ON ut.user_id = pfd.user_id "
					+ "WHERE pfd.gateway_id = '"+gatewayid+"' AND pfd.user_id = " +userId+";";
			Query query =this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			
			List res =query.list();
			
			if(!res.isEmpty()) {
				Object[] tuple = (Object[]) res.get(0);
				String mealTime = (String) tuple[0];
				String userTimezone = (String) tuple[1];
				
				String[] timeArray = mealTime.split(",");
		        
				DateFormat timeFormat = new SimpleDateFormat("HH:mm");
				timeFormat.setTimeZone(TimeZone.getTimeZone("GMT"+userTimezone));
				
				Date currentTime = timeFormat.parse(timeFormat.format(new Date()));
				BigInteger intake = null;
		        if(timeArray.length > 0) {
		        	for (int i = 0; i < timeArray.length; i++) {
		        		
		        		 Date mealsTime = timeFormat.parse(timeArray[i]);
		        	        
							if (mealsTime.before(currentTime)) {
								intake = BigInteger.ZERO;
								DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

								DateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

								Date convertedDateForAft = _helper.timeZoneConverter("yyyy-MM-dd", "+00:00",
										userTimezone, dateFormat1.format(new Date()));
								
								String qry1  = "SELECT IFNULL(si.weight,0) FROM sensorreport si WHERE si.gateway_id = '"
											+ gatewayid + "' AND si.datetime > '" + dateFormat.format(convertedDateForAft)
											+ " " + timeArray[i] + ":00"
											+ "' AND (si.eventid LIKE '%100' OR si.eventid = '20000001');";
								
								Query query1 = this.sessionFactory.getCurrentSession().createSQLQuery(qry1);

								List res1 = query1.list();

								if (!res1.isEmpty()) {
									intake = (BigInteger) res1.get(0);
								}
								
							}else {
								break;
							}
		            }
		        	if(intake != null && intake.floatValue() > 0) {
		        		return true;
		        	}
		        	
		        }
		        
			}
			
		}catch (Exception e) {
			log.error("Error in checkFoodFeededorNot :: Error : "+e.getLocalizedMessage());
		}
		return enable;
	}
	
	@Override
	public boolean updateSensorlocation(long gatewayid,long sensorloc,String gname) {
		log.info("Entered into updateSensorlocation &gatewayname : "+ gatewayid+":"+ sensorloc+":"+gname);
		boolean isSuccess = false; 
		try {
			log.info("Update Gateway function");
			Session session = sessionFactory.getCurrentSession();
			String hql = "UPDATE Gateway set sensor_location_type_id =:sensorloc,name =:gname where id =:gatewayid";
			Query query = session.createQuery(hql);
			query.setParameter("gname", gname);
			query.setParameter("gatewayid",gatewayid);
			query.setParameter("sensorloc",sensorloc);

			int rs = query.executeUpdate();
			isSuccess = true;
			return isSuccess;
			
		}catch (Exception e) {
			log.error("Error in updateSensorlocation :: Error : "+e.getLocalizedMessage());
		}
		return isSuccess;
	}
	
	@Override
	public AskFeature saveOrUpdateAskFeature(AskFeature askFeature) {
		log.info("Entered into saveOrUpdateAskFeature :: user_id : "+ askFeature.getUser_id());
		try {
			return (AskFeature) sessionFactory.getCurrentSession().merge(askFeature);
		} catch (Exception e) {
			log.error("Error in saveOrUpdateAskFeature :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}
	
	@Override
	public boolean updateUserInChargebee(String fromemail, String toemail, String chargebeeId) {
		String freMail = fromemail.trim();
		freMail = freMail.toLowerCase();
		freMail = freMail.replaceAll("\\s+", "");
		
		String toEMail = toemail.trim();
		toEMail = toEMail.toLowerCase();
		toEMail = toEMail.replaceAll("\\s+", "");
		boolean status = false;
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		
		if (_helper.isValidEmail(freMail) && _helper.isValidEmail(toEMail)) {
			try {
				Result customerUpdateResult = Customer.update(chargebeeId).email(toEMail).request();

				Customer customer = customerUpdateResult.customer();
				status = true;
				log.info("updateUserInChargebee:" + status);
			} catch (Exception ex) {
				log.error("updateUserInChargebee : " + ex.getMessage());
				status = false;
			}
		}
		return status;
	}
	
	@Override
	public List<JSensorAlert> getSensorAlerts(long userid) {
		log.info("Entered into getSensorAlerts :: user : "+userid);
		List<JSensorAlert> alertList = new ArrayList<JSensorAlert>();

		String curDt = IrisservicesUtil.getCurrentTimeUTC();
		
		String qry = " SELECT A.startdatetime,A.enddatetime,A.timezone,A.alerttype_id ,A.gateway_id, G.name AS gatewayname,A.alertvalue FROM alert A "
				+ " JOIN alerttype AT ON AT.id = A.alerttype_id "
				+ " JOIN gateway G ON G.id = A.gateway_id "
				+ " JOIN assetmodel AM ON AM.id=G.model_id "
				+ " WHERE AM.monitor_type_id=9 AND A.gateway_id IN(SELECT gatewayid FROM usergateway WHERE userid =:userId ) "
				+ " AND CONVERT_TZ(`enddatetime`,`timezone`,'+00:00') >= DATE_SUB( :curr_date , INTERVAL 24 HOUR) ORDER BY enddatetime DESC LIMIT 50;";

		Query query = this.slave5SessionFactory.getCurrentSession().createSQLQuery(qry)
		.setParameter("userId", userid).setParameter("curr_date", curDt);

		JSensorAlert senalert;
		List<Object> res = query.list();
		try {
			if (!res.isEmpty()) {
				for (int i = 0; i < res.size(); i++) {

					Object[] result = (Object[]) res.get(i);
					String OLD_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";
					final String NEW_FORMAT = "MM/dd/yyyy HH:mm:ss";
					String oldDateString = "";
					String newDateString = "";

					senalert = new JSensorAlert();

					if (result[1] != null) {
						oldDateString = result[1].toString();
						try {
							DateFormat formatter = new SimpleDateFormat(OLD_FORMAT);
							Date d = formatter.parse(oldDateString);
							((SimpleDateFormat) formatter).applyPattern(NEW_FORMAT);
							newDateString = formatter.format(d);
						} catch (Exception e) {
							log.error("getUnackAlerts : " + e.getLocalizedMessage());
						}

						senalert.setLastnotifiedtime(newDateString);
					}
					if (result[3] != null)
						senalert.setAlerttypeid(Long.parseLong(result[3].toString()));
					if (result[4] != null)
						senalert.setGatewayid(Long.parseLong(result[4].toString()));
					if (result[5] != null)
						senalert.setGatewayName(result[5].toString());
					if (result[6] != null)
					{
						float alertVal = Float.parseFloat(result[6].toString());
						senalert.setEventId((int) alertVal);
						if(senalert.getAlerttypeid() == 30 && alertVal == 400 ) {
							senalert.setTitle("Door Open");
							senalert.setDesc("Alert: Door is open! Close it for comfort and security.");
						}else if(senalert.getAlerttypeid() == 30 && alertVal == 800 ) {
							senalert.setTitle("Door Close");
							senalert.setDesc("Door closed tight! Nothing gets in without your say.");
						}
					}
					if(senalert.getAlerttypeid() == 2) {
						senalert.setTitle("Battery Alert");
						senalert.setDesc("Low Battery! Recharge your sensor to keep everything running smoothly!");
					}else if(senalert.getAlerttypeid() == 11) {
						senalert.setTitle("Network unavailable");
						senalert.setDesc("Network signal lost or weak. Please check your connection!");
					}else if(senalert.getAlerttypeid() == 29) {
						senalert.setTitle("Water Leakage");
						senalert.setDesc("Water Detected! Examine your tank to maintain appropriate water levels");
					}else if(senalert.getAlerttypeid() == 32) {
						senalert.setTitle("Door Reminder");
						senalert.setDesc("Alert: Your door is Still open! Make sure it's securely closed to keep everyone safe and sound.");
					}
					if (result[6] != null)
					{
						float alertVal = Float.parseFloat(result[6].toString());
						senalert.setEventId((int) alertVal);
						if(senalert.getAlerttypeid() == 30 && alertVal == 1 ) {
							senalert.setTitle("Door Open");
							senalert.setDesc("Alert: Door is open! Close it for comfort and security.");
						}else if(senalert.getAlerttypeid() == 30 && alertVal == 0 ) {
							senalert.setTitle("Door Close");
							senalert.setDesc("Door closed tight! Nothing gets in without your say.");
						}if(senalert.getAlerttypeid() == 29 && alertVal == 1 ) {
							senalert.setTitle("Water Detected!");
							senalert.setDesc("Check for overflow or leaks before they become an issue.");
						}else if(senalert.getAlerttypeid() == 29 && alertVal == 0 ) {
							senalert.setTitle("Water Leak Fixed!");
							senalert.setDesc("All set, no more water trouble.");
						}
					}				

					alertList.add(senalert);
				}
			}
		} catch (Exception e) {
			log.error("Error in getSensorAlerts :: Session Name : slave5SessionFactory :: error : " + e.getLocalizedMessage());
		}
		return alertList;
	}
	
	public JResponse getAskFeature(long offset,long limit) {
		log.info("Entered into getAskFeature: ");
		List<AskFeature> askFeatureList = new ArrayList<AskFeature>();
		JResponse askFeatureResponse= new JResponse();
		Session session = this.sessionFactory.getCurrentSession();
		try {
//			String qry = "SELECT  id,user_id,feature_request,updated_on,username,email,createdon,plan_id,subscription_status FROM ("
//					+ "(SELECT a.id,a.user_id,a.feature_request,a.updated_on,CONCAT(u.firstname,' ',u.lastname) AS username,email ,u.createdon ,plan_id,`subscription_status`, u.is_test "
//					+ " FROM ask_feature a "
//					+ " JOIN `user` u ON a.user_id =u.id "
//					+ " JOIN `all_chargebee_subscription` A ON  A.`chargebee_id` = u.chargebeeid) "
//					+ "UNION "
//					+ "(SELECT a.id,a.user_id,a.feature_request,a.updated_on,CONCAT(u.firstname,' ',u.lastname) AS username,email ,u.createdon,plan_id,`subscription_status`, u.is_test "
//					+ "FROM ask_feature a "
//					+ "JOIN `user` u ON a.user_id =u.id "
//					+ "JOIN `all_product_subscription` A ON  A.`chargebee_id` = u.chargebeeid "
//					+ ")"
//					+ "UNION "
//					+ "(SELECT a.id,a.user_id,a.feature_request,a.updated_on,CONCAT(u.firstname,' ',u.lastname) AS username,email ,u.createdon,\"NA\" AS plan_id,\"NA\" AS `subscription_status`, u.is_test "
//					+ "FROM ask_feature a "
//					+ "JOIN `user` u ON a.user_id =u.id WHERE a.user_id NOT IN "
//					+ "	(SELECT U.id FROM `user` U   JOIN `ask_feature` AK ON AK.user_id =U.id "
//					+ "	JOIN all_chargebee_subscription AC ON AC.`chargebee_id` = U.chargebeeid		  "
//					+ "	UNION 		 "
//					+ "	SELECT U.id FROM `user` U "
//					+ "	JOIN `ask_feature` AK ON AK.user_id =U.id "
//					+ "	JOIN all_product_subscription AP ON AP.`chargebee_id` = U.chargebeeid GROUP BY U.id)"
//					+ ")			"
//					+ ") AS temp WHERE is_test = 0 GROUP BY id,user_id	 ORDER BY updated_on DESC ";
			String qry = "SELECT a.id,a.user_id,a.feature_request,a.updated_on,CONCAT(u.firstname,' ',u.lastname) AS username,email,createdon "
					+ " FROM ask_feature a "
					+ " JOIN `user` u ON a.user_id =u.id  WHERE is_test = 0"
					+ " ORDER BY updated_on DESC ";
			
			String count_qry ="SELECT COUNT(A.id) FROM ask_feature A JOIN `user` U ON U.id=A.user_id WHERE U.is_test=0";
			
			if (limit != 0 && !(offset <= 0))
				qry += "limit "+ limit * (offset - 1) + "," + limit;
			
			Query query =this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			//askFeatureList =query.list();
			
			List res = query.list();
			if (!res.isEmpty()) {
				for (int i = 0; i < res.size(); i++) {
					AskFeature askFe = new AskFeature();
					Object[] checklistObj = (Object[]) res.get(i);
					askFe.setId(((BigInteger) checklistObj[0]).intValue());
					askFe.setUser_id(((BigInteger) checklistObj[1]).longValue());
					askFe.setFeature_request(checklistObj[2].toString() != null && !checklistObj[2].toString().trim().equals("") ? checklistObj[2].toString() : "NA");
					askFe.setUpdated_on((String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(checklistObj[3]));
					askFe.setUsername(((String) checklistObj[4]).toString());
					askFe.setEmail(((String) checklistObj[5]).toString());
					askFe.setRegister_dt((String) new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(checklistObj[6]));
//					askFe.setPlan_id(((String) checklistObj[7]).toString());
//					askFe.setStatus(((String) checklistObj[8]).toString());
					askFeatureList.add(askFe);
				}
			}
			
			long total_count =((BigInteger) this.sessionFactory.getCurrentSession().createSQLQuery(count_qry).list().get(0)).longValue();
			
			if(askFeatureList.size()>0) {
				askFeatureResponse.put("Status", 1);
				askFeatureResponse.put("Msg", "Success");
				askFeatureResponse.put("offset", offset);
				askFeatureResponse.put("limit", limit);
				askFeatureResponse.put("total_cnt", total_count);
				askFeatureResponse.put("ask_feature_list", askFeatureList);
			}else {
				askFeatureResponse.put("Status", 1);
				askFeatureResponse.put("Msg", "No Data Available");
				askFeatureResponse.put("ask_feature_list", askFeatureList);
			}
		} catch (Exception e) {
			log.error("Exception occurred in getAskFeature - " + e.getLocalizedMessage());
			askFeatureResponse.put("Status", 0);
			askFeatureResponse.put("Msg", "No Data Available");
			askFeatureResponse.put("ask_feature_list", askFeatureList);
		}
		return 	askFeatureResponse;
	}

	public boolean checkActiveSubsForMeariDevice(long user_id, long monitorType) {
		log.info("Entered into checkActiveSubsForMeariDevice :: user_id : "+ user_id);
		try {
			
			String query = "SELECT APS.id FROM usergateway UG JOIN all_product_subscription APS ON APS.gateway_id = UG.gatewayId WHERE UTC_TIMESTAMP() > DATE_ADD(APS.subscription_started_at, INTERVAL 5 DAY) AND "
					+ " UG.userId =:user_id AND APS.monitor_type =:monitorType AND APS.is_deleted = 0 ORDER BY APS.updated_indb DESC LIMIT 1";
			
			log.info("select query : "+ query);
			Query qry = sessionFactory.getCurrentSession().createSQLQuery( query );
			qry.setParameter("user_id", user_id);
			qry.setParameter("monitorType", monitorType);

			 if( qry.list().size() > 0 ) 
				 return true;
			 
		} catch (Exception e) {
			log.error("Error in checkActiveSubsForMeariDevice :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}
	
	@Override
	public UserLocation getUserLatLon(long id, long gateway_id) {
		log.info("Entered into getUserLatLon :: user_id : "+ id);
		try {
			Criteria criteria = sessionFactory.getCurrentSession().createCriteria(UserLocation.class)
					.add(Restrictions.eq("user_id", id)).add(Restrictions.eq("gateway_id", gateway_id));
			
			List<UserLocation> userLocList = criteria.list();
			 if( userLocList.size() > 0 )
				 return userLocList.get(0);
			 
		} catch (Exception e) {
			log.error("Error in checkActiveSubsForMeariDevice :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}
	
	@Override
	public void updateUserLocation(UserLocation userLocation) {
		log.info("Entered into updateUserLocation :: ");
		try {
			this.sessionFactory.getCurrentSession().merge(userLocation);			 
		} catch (Exception e) {
			log.error("Error in updateUserLocation :: Error : "+ e.getLocalizedMessage());
		}
	}
	
	@Override
	public ArrayList<String> checkProductBasedEligible(long userid) {
		log.info("Entered UserDaoImpl ::  userid :"+userid);
		ArrayList<String> events = new ArrayList<String>();
		try {

			String sql = "select aM.monitor_type_id from usergateway uG, asset ass, assetmodel aM where uG.userId = :user_id"
					+ " and ass.id = uG.gatewayId and ass.model_id = aM.id"
					+ " AND uG.userId NOT IN (SELECT user_id FROM user_firebase_event WHERE user_id = :user_id AND (event_id = 'buyWaggleCam' OR event_id = 'buyPetMonitor'))";
			Query qry = sessionFactory.getCurrentSession().createSQLQuery(sql);
			qry.setParameter("user_id", userid);

			Boolean waggleCamEvent = false;
			Boolean petMonitorEvent =  false;
			List<BigInteger> monitorTypes = qry.list();
		
			if (!monitorTypes.isEmpty()) {
				for (int i = 0; i < monitorTypes.size(); i++) {

					int monitorType =monitorTypes.get(i).intValue();
					
					if(monitorType==1) {
						waggleCamEvent = true;
						if(petMonitorEvent) {
							waggleCamEvent = false;
							petMonitorEvent = false;
						}
					}else if(monitorType==6) {
						petMonitorEvent = true;
						if(waggleCamEvent) {
							waggleCamEvent = false;
							petMonitorEvent = false;
						}
					}
					
					
				}
			}
			if(waggleCamEvent) {
				events.add("buyWaggleCam");
			}else if(petMonitorEvent) {
				events.add("buyPetMonitor");
			}
			
		}
		catch(Exception e) {
			log.error("getUserFirebaseEvents exception : "+ e.getLocalizedMessage());
		}

		return events;
		
	}
	
	@Override
	public UserFeedbackTransaction getUserFeedbackTransactionWithMonitor(long userid, long formid, long monitorType) {
		log.info("Entered into getUserFeedbackTransaction :: user id : "+userid+" :: form id : "+formid);
		try {

			String qry = "SELECT * FROM `user_feedback_transaction` WHERE formid=" + formid + " AND userid=" + userid + " AND monitor_type=" + monitorType;
			Query query = slave5SessionFactory.getCurrentSession().createSQLQuery(qry)
					.addEntity(UserFeedbackTransaction.class);
			UserFeedbackTransaction feedbackObj = (UserFeedbackTransaction) query.list().get(0);
			return feedbackObj;
		} catch (Exception e) {
			log.error("Error in getUserFeedbackTransaction :: Session Name : slave5SessionFactory :: error : " + e.getLocalizedMessage());
			return null;
		}

	}
	
	@Override
	public AskFeature getPlanStatusByUser(String cbid) {
		log.info("Entered into getPlanStatusByUser: ");
		AskFeature askFe = new AskFeature();
		JResponse askFeatureResponse= new JResponse();
		Session session = this.sessionFactory.getCurrentSession();
		try {
			String qry = " SELECT plan_id,`subscription_status` FROM all_chargebee_subscription A WHERE A.`chargebee_id`='"+cbid+"' "
					+ "  UNION "
					+ "  SELECT plan_id,`subscription_status` FROM all_product_subscription A WHERE A.`chargebee_id`='"+cbid+"'"
					+ "  ORDER BY subscription_status ASC LIMIT 1 ";
			Query query =this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			
			List res = query.list();
			if (!res.isEmpty()) {
				for (int i = 0; i < res.size(); i++) {
					Object[] checklistObj = (Object[]) res.get(i);			
				}
			}
			
		} catch (Exception e) {
			log.error("Exception occurred in getPlanStatusByUser - " + e.getLocalizedMessage());
			
		}
		return 	askFe;
	}

	@Override
		public String checkInappNotify(long gatewayid) {
			log.info("Entered into checkInappNotify :: gateway_id : "+ gatewayid);
			String notify = "0000"; 
			try {
				String qry = "SELECT AC.notificationtype FROM alertcfg_to_asset AA JOIN alertcfg AC ON AC.id = AA.alertcfg_id WHERE AA.asset_id =:gatewayid ORDER BY AC.updated_on DESC LIMIT 1;";
				Query query =this.sessionFactory.getCurrentSession().createSQLQuery(qry);
				query.setParameter("gatewayid", gatewayid);
				List res =query.list();
			
				if(!res.isEmpty())
					notify = (String) res.get(0);
				
			}catch (Exception e) {
				log.error("Error in checkInappNotify :: Error : "+e.getLocalizedMessage());
			}
			return notify;
		}
	
	@Override
	public List<JAlertV4> getUnackAlertsByGateway(long userid, String tempunit, String monitortype, long gatewayid) {
		log.info("Entered into getUnackAlertsByGateway :: user : "+userid);
		List<JAlertV4> alertList = new ArrayList<JAlertV4>();

		String curDt = IrisservicesUtil.getCurrentTimeUTC();
		
		String qry = "SELECT A.startdatetime,A.enddatetime,A.ack,A.timezone,A.alerttype_id ,AT.name AS alerttype,A.gateway_id,"
				+ " G.name AS gatewayname,A.lat,A.lon, A.gpsstatus, A.temperature,A.battery,A.alertvalue  FROM alert A "
				+ " JOIN alerttype AT ON AT.id = A.alerttype_id JOIN gateway G ON G.id = A.gateway_id"
				+ " JOIN assetmodel AM ON AM.id=G.model_id "
				+ " WHERE AM.monitor_type_id=1 AND  A.gateway_id  = " + gatewayid
				+ " AND CONVERT_TZ(`enddatetime`,`timezone`,'+00:00') >= DATE_SUB('"+curDt+"', INTERVAL 24 HOUR) ORDER BY enddatetime DESC limit 50;";

		Query query = this.slave5SessionFactory.getCurrentSession().createSQLQuery(qry);

		JAlertV4 alertv4;
		List<Object> res = query.list();
		try {
			if (!res.isEmpty()) {
				for (int i = 0; i < res.size(); i++) {

					Object[] result = (Object[]) res.get(i);
					String OLD_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";
					final String NEW_FORMAT = "MM/dd/yyyy HH:mm:ss";
					String oldDateString = "";
					String newDateString = "";

					alertv4 = new JAlertV4();

//					if (result[0] != null) {
//						oldDateString = result[0].toString();
//						try {
//							DateFormat formatter = new SimpleDateFormat(OLD_FORMAT);
//							Date d = formatter.parse(oldDateString);
//							((SimpleDateFormat) formatter).applyPattern(NEW_FORMAT);
//							newDateString = formatter.format(d);
//						} catch (Exception e) {
//							log.error("getUnackAlerts : " + e.getLocalizedMessage());
//						}
//
//						alertv4.setStartdatetime(newDateString);
//					}
					if (result[1] != null) {
						oldDateString = result[1].toString();
						try {
							DateFormat formatter = new SimpleDateFormat(OLD_FORMAT);
							Date d = formatter.parse(oldDateString);
							((SimpleDateFormat) formatter).applyPattern(NEW_FORMAT);
							newDateString = formatter.format(d);
						} catch (Exception e) {
							log.error("getUnackAlertsByGateway : " + e.getLocalizedMessage());
						}

						alertv4.setLastnotifiedtime(newDateString);
						alertv4.setStartdatetime(newDateString);
					}
					if (result[2] != null)
						alertv4.setAcknowledged(false);//(boolean) result[2]);
					if (result[3] != null)
						alertv4.setTimezone(result[3].toString());
					if (result[4] != null)
						alertv4.setAlerttypeid(Long.parseLong(result[4].toString()));
					if (result[5] != null)
						alertv4.setAlerttype(result[5].toString());
					if (result[6] != null)
						alertv4.setGatewayid(Long.parseLong(result[6].toString()));
					if (result[7] != null)
						alertv4.setGateway(result[7].toString());
					if (result[8] != null)
						alertv4.setLat(Double.parseDouble(result[8].toString()));
					if (result[9] != null)
						alertv4.setLon(Double.parseDouble(result[9].toString()));
					if (result[10] != null)
						alertv4.setGpsstatus(result[10].toString());
					if (result[11] != null) {
						float tempval = Float.parseFloat(result[11].toString());

						if (tempunit.equalsIgnoreCase("F") && tempval > -200
								&& (alertv4.getAlerttypeid() == 1 || alertv4.getAlerttypeid() == 4)) {
							tempval = CelsiusToFahrenheit(tempval);
						}

						alertv4.setTemperature(tempval);
					}
					if (result[12] != null)
						alertv4.setBattery(Integer.parseInt(result[12].toString()));
					if (result[13] != null)
						alertv4.setAlertvalue(Float.parseFloat(result[13].toString()));

					alertList.add(alertv4);
				}
			}
		} catch (Exception e) {
			log.error("Error in getUnackAlertsByGateway :: Session Name : slave5SessionFactory :: error : " + e.getLocalizedMessage());
		}
		return alertList;
	}

	@Override
	public void updateEvent(InAppEvent inAppEvent) {

		log.info("Entered into updateEvent : ");

		try {
			Session session = sessionFactory.getCurrentSession();
			String hql = "insert into user_inapp_event(user_id, reasontype, reason_id) values(:user_id, :reasontype, :reason_id)";
			Query query = session.createSQLQuery(hql);

			query.setParameter("user_id", inAppEvent.getUserId());
			query.setParameter("reasontype", inAppEvent.getReasonType());
			query.setParameter("reason_id", inAppEvent.getReasonId());

			query.executeUpdate();
		}
		catch (Exception e) {
			log.error("Error in updateEvent - {}", e.getLocalizedMessage());
		}
	}

	@Override
	public User verifyUser(String key, String value) {
		Session ses = sessionFactory.getCurrentSession();
		try {
			Criteria criteria = ses.createCriteria(User.class).add(Restrictions.eq(key, value));
			List<User> userList = criteria.list();
			if (!userList.isEmpty()) {
				return userList.get(0);
			}
		} catch (Exception e) {
			log.error("verifyUser : " + e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public boolean saveUserOTPVerify(UserOtpVerification otpVerify) {
		try {
			Session ses = sessionFactory.getCurrentSession();
			ses.merge(otpVerify);
			return true;
		} catch (Exception e) {
			log.error("saveUserOTPVerify : " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean validateUserOTP(UserOtpVerification otpVerification, int validMinutesForOTP) {
		log.info(" Entered into validateUserOTP ::");
		try {
			String qry = "UPDATE user_otp_verification SET status=1, " +
					"updated_on='"+ _helper.getCurrentTimeinUTC() +"' " +
					"WHERE status=0 AND otp = "+otpVerification.getOtp() +
					" AND verification_value='"+otpVerification.getVerification_value()+"' " +
					"AND ( updated_on > DATE_SUB(NOW(), INTERVAL "+validMinutesForOTP+" MINUTE))";
			int updated = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			if( updated >= 1 ) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			log.error(" Error in validateUserOTP : "+ e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean isVetChatUser(long userId) {

		log.info(" Entered into isVetChatUser ::");
		try {
			String sql = "SELECT " +
							"CASE " +
							"  WHEN DATEDIFF(CURDATE(), U.createdon) <= 15 THEN TRUE " +
							"  WHEN DATEDIFF(CURDATE(), U.createdon) <= :days " +
							"       AND DATEDIFF(CURDATE(), U.createdon) > 15 " +
							"       AND EXISTS ( " +
							"           SELECT 1 " +
							"           FROM usergateway UG " +
							"           WHERE UG.userid = U.id " +
							"       ) THEN TRUE " +
							"  ELSE FALSE " +
							"END AS result " +
							"FROM user U " +
							"WHERE U.id = :userId";
			Query query = this.sessionFactory.getCurrentSession()
								.createSQLQuery(sql)
								.setParameter("userId", userId)
								.setParameter("days", vetchat_free_activation_days);

			return ((BigInteger) query.uniqueResult()).longValue() > 0;
		} catch (Exception e) {
			log.error(" Error in isVetChatUser : {}", e.getLocalizedMessage());
			return false;
		}
	}

	@Override
	public ArrayList<String> checkPetMonitorPopupEligible(long userId) {

		log.info(" Entered into checkPetMonitorPopupEligible ::");
		ArrayList<String> events = new ArrayList<>();

		try {
			String sql = "SELECT A.* FROM ("
					+ "SELECT G.id FROM `user` U "
					+ "JOIN usergateway UG ON U.id = UG.userid "
					+ "JOIN gateway G ON UG.gatewayid = G.id "
					+ "JOIN assetmodel AM ON G.model_id = AM.id "
					+ "JOIN all_product_subscription APS ON G.id = APS.gateway_id "
					+ "WHERE U.id = :userId AND AM.monitor_type_id = 1 "
					+ "AND subscription_status NOT IN ('active', 'non_renewing') "
					+ "AND APS.is_test = 0 AND APS.duplicate_subs = 0 "
					+ "UNION "
					+ "SELECT G.id FROM `user` U "
					+ "JOIN usergateway UG ON U.id = UG.userid "
					+ "JOIN gateway G ON UG.gatewayid = G.id "
					+ "JOIN assetmodel AM ON G.model_id = AM.id "
					+ "JOIN all_chargebee_subscription APS ON U.chargebeeid = APS.chargebee_id "
					+ "WHERE U.id = :userId AND AM.monitor_type_id = 1 "
					+ "AND subscription_status NOT IN ('active', 'non_renewing') "
					+ "AND APS.is_test = 0 AND APS.duplicate_subs = 0"
					+ ") AS A";

			Query query = this.sessionFactory.getCurrentSession()
								.createSQLQuery(sql)
								.setParameter("userId", userId);

			if(!query.list().isEmpty()) {
				String sql2 = "SELECT event_name FROM firebase_event WHERE event_id = :eventId";
				Query query2 = this.sessionFactory.getCurrentSession()
						.createSQLQuery(sql2)
						.setParameter("eventId", 3);

				String eventName = (String) query2.uniqueResult();
				events.add(eventName);
			}
		}
		catch (Exception e) {
			log.error(" Error in checkPetMonitorPopupEligible : {}", e.getLocalizedMessage());
		}
		return events;
	}

	@Override
	public ArrayList<String> isN7PopupEligible(long userId) {

		log.info(" Entered into isN7PopupEligible ::");
		ArrayList<String> events = new ArrayList<>();

		try {
			String sql =
					"SELECT G.id " +
							"FROM `user` U " +
							"JOIN usergateway UG ON U.id = UG.userid " +
							"JOIN gateway G ON UG.gatewayid = G.id " +
							"JOIN assetmodel AM ON G.model_id = AM.id " +
							"WHERE U.id = :userId " +
							"  AND AM.model LIKE 'N7%' " +
							"  AND AM.monitor_type_id = 1 " +
							"  AND NOT EXISTS ( " +
							"      SELECT 1 " +
							"      FROM all_product_subscription APS " +
							"      WHERE APS.gateway_id = G.id " +
							"        AND APS.subscription_status IN ('active', 'non_renewing') " +
							"        AND APS.is_test = 0 " +
							"        AND APS.duplicate_subs = 0 " +
							"  ) " +
							"  AND NOT EXISTS ( " +
							"      SELECT 1 " +
							"      FROM all_chargebee_subscription ACS " +
							"      WHERE ACS.chargebee_id = U.chargebeeid " +
							"        AND ACS.subscription_status IN ('active', 'non_renewing') " +
							"        AND ACS.is_test = 0 " +
							"        AND ACS.duplicate_subs = 0 " +
							"  )";


			Query query = this.sessionFactory.getCurrentSession()
								.createSQLQuery(sql)
								.setParameter("userId", userId);

			if(!query.list().isEmpty()) {
				String sql2 = "SELECT event_name FROM firebase_event WHERE event_id = :eventId";
				Query query2 = this.sessionFactory.getCurrentSession()
								.createSQLQuery(sql2)
								.setParameter("eventId", 4);

				String eventName = (String) query2.uniqueResult();
				events.add(eventName);
			}
		}
		catch (Exception e) {
			log.error(" Error in isN7PopupEligible : {}", e.getLocalizedMessage());
		}
		return events;
	}


	@Override
	public int checkWithInOtpTimeLimit(String tableName,String userWhereKey,String userValue){
		log.info("Entered into checkWithInOtpTimeLimit : "+ userValue);

		try {
			String qry = "SELECT otp FROM "+tableName+" WHERE "+userWhereKey+" = :userValue AND status=0 AND NOW() < DATE_ADD(updated_on , INTERVAL 10 MINUTE); ";
			Query query =this.sessionFactory.getCurrentSession().createSQLQuery(qry);

			query.setParameter("userValue", userValue);
			Integer resultOtp = (Integer) query.uniqueResult();

			if(resultOtp != null )
				return resultOtp ;


		}catch (Exception e) {
			log.error("Error in checkWithInOtpTimeLimit :: Error : "+e.getLocalizedMessage());
			return -1;
		}



		return -1;
	}

	@Override
	public int getOtpTimeLimit(String tableName,String userWhereKey,String userValue){
		log.info("Entered into getOtpTimeLimit : "+ userValue);

		try {

			String qry = "SELECT updated_on FROM "+tableName+" WHERE "+userWhereKey+" = :userValue";
			Query query =this.sessionFactory.getCurrentSession().createSQLQuery(qry);
			query.setParameter("userValue", userValue);
			Timestamp dbTime = (Timestamp) query.uniqueResult();
			LocalDateTime updatedTime = dbTime.toLocalDateTime();
			String now = _helper.getCurrentTimeinUTC();

			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			LocalDateTime nowLocal = LocalDateTime.parse(now, formatter);

			long remainingMinutes =validMinutesForOTP- Duration.between(updatedTime, nowLocal).toMinutes();

			Integer mins=(int) remainingMinutes;


			if(mins != null  && mins<10)
				return mins ;


		}catch (Exception e) {
			log.error("Error in getOtpTimeLimit :: Error : "+e.getLocalizedMessage());
			return -1;
		}



		return -1;
	}


	@Override
	public   void saveFirstHitTimeDiff(String tableName, String whereKey, String userValue){
		log.info("Entered into saveFirstHitTimeDiff : "+ userValue);

		try {

			String updateQuery="UPDATE "+tableName +" SET last_updated_on = NOW() WHERE verification_value='"+userValue+"'";
			Query query =this.sessionFactory.getCurrentSession().createSQLQuery(updateQuery);
			int rs = query.executeUpdate();

			if(rs>0){
				log.info(" Hit Updated in Otp table ");
			}
		}catch (Exception e) {
			log.error("Error in saveFirstHitTimeDiff :: Error : "+e.getLocalizedMessage());

		}

	}


	@Override
	public    boolean getFirstHitTimeDiff(String tableName, String whereKey, String userValue){
		log.info("Entered into getFirstHitTimeDiff : "+ userValue);

		try {

			String qry = "SELECT last_updated_on FROM "+tableName+" WHERE "+whereKey+" = :userValue";


			log.info("Query "+qry);
			Query query =this.sessionFactory.getCurrentSession().createSQLQuery(qry);

			query.setParameter("userValue", userValue);
			Timestamp dbTime = (Timestamp) query.uniqueResult();
			LocalDateTime updatedTime = dbTime.toLocalDateTime();

			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			Temporal firstHitTime = updatedTime;
			log.info("First Hit Time "+firstHitTime);
			String now = _helper.getCurrentTimeinUTC();



			// Parse the string to LocalDateTime
			LocalDateTime secondHitTime = LocalDateTime.parse(now, formatter);
			log.info("Second Hit Time "+secondHitTime);
			// Calculate duration
			long secondsDiff = Duration.between(firstHitTime, secondHitTime).getSeconds();
			log.info("secondsDiff "+secondsDiff);
			if(secondsDiff >= 0 && secondsDiff <5)
			{
				return false ;
			}

		}catch (Exception e) {
			log.error("Error in getFirstHitTimeDiff :: Error : "+e.getLocalizedMessage());
			return true;
		}
		return true;

	}


	@Override
	public boolean updateFullUserName(User user, long userId) {

        try {
            int status = getUpdateFullUserNameStatus(user, userId);

            if(status > 0) {
                log.info("Successfully updated");
                return true;
            } else {
                log.info("Error in updating");
                return false;
            }
        } catch (Exception e) {
           log.info("Error in updateFullUserName : SessionName : SessionFactory : error : {}", e.getLocalizedMessage());
        }

        return false;
	}

	private int getUpdateFullUserNameStatus(User user, long userId) {
		String qry = "UPDATE user SET firstname=:firstName, lastname=:lastName, updated_on=:updatedOn where id=:userId";

		Session ses = sessionFactory.getCurrentSession();

		Query query = ses.createSQLQuery(qry);

		query.setParameter("firstName", user.getFirstname());
		query.setParameter("lastName", user.getLastname());
		query.setParameter("updatedOn", user.getUpdatedOn());
		query.setParameter("userId", userId);

		return query.executeUpdate();
	}

	@Override
	public boolean updateUserPlanPreference(UserPlanPreference userPlanPreference) {

		try {

			boolean exists = false;

			String checkQuery =
					"SELECT id FROM user_plan_preference where user_id =:userId and gateway_id =:gatewayId";

			Query query = sessionFactory.getCurrentSession().createSQLQuery(checkQuery);

			query.setParameter("userId", userPlanPreference.getUserId());
			query.setParameter("gatewayId", userPlanPreference.getGatewayId());

			Object result = query.uniqueResult();

			exists = result != null;

			int status = 0;

			if(exists) {

				log.info("Already exists an user for the given gateway updating the existing record userId : {}, gatewayId : {}",
						userPlanPreference.getUserId(), userPlanPreference.getGatewayId());

				String updateQuery =
						"UPDATE user_plan_preference SET usage_type =:usageType where user_id =:userId and gateway_id =:gatewayId";

				Query queryUpdate =  sessionFactory.getCurrentSession().createSQLQuery(updateQuery);

				queryUpdate.setParameter("usageType", userPlanPreference.getPlanPref());
				queryUpdate.setParameter("userId", userPlanPreference.getUserId());
				queryUpdate.setParameter("gatewayId", userPlanPreference.getGatewayId());

				status = queryUpdate.executeUpdate();
			} else {

				log.info("Inserting a new record for the user and gateway userId: {}, gatewayId: {}",
						userPlanPreference.getUserId(), userPlanPreference.getGatewayId());

				String insertQuery =
						"INSERT INTO `user_plan_preference` (`user_id`, `gateway_id`, `usage_type`, `updated_on`) " +
								"VALUES(:userId, :gatewayId, :usageType, :updatedOn)";

				Query queryInsert = sessionFactory.getCurrentSession().createSQLQuery(insertQuery);
				queryInsert.setParameter("userId", userPlanPreference.getUserId());
				queryInsert.setParameter("gatewayId", userPlanPreference.getGatewayId());
				queryInsert.setParameter("usageType", userPlanPreference.getPlanPref());
				queryInsert.setParameter("updatedOn", userPlanPreference.getUpdatedOn());

				status = queryInsert.executeUpdate();
			}

			if(status > 0) {
				log.info("Successfully updated the plan preference");
				return true;
			} else {
				log.info("Error in updating the plan preference");
				return false;
			}

		} catch (Exception e) {
			log.info("Error occurred in updateUSerPlanPreference : SessionName : SessionFactory : error : {}",
					e.getLocalizedMessage());
		}

		return false;
	}

    @Override
    public String getChargebeeid(long userId) {
        String chargebeeid = "";
        try {
            log.info("Entered into getChargebeeid :: userId : "+ userId);
            String qry = "SELECT chargebeeid FROM user WHERE id = :userId";
            Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
            query.setParameter("userId", userId);
             chargebeeid = (String) query.uniqueResult();
        } catch (Exception e) {
            log.error("Error in getChargebeeid :: Error : "+ e.getLocalizedMessage());
        }
        return chargebeeid;
    }
}
