package com.nimble.irisservices.dao;

import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.*;
import com.nimble.irisservices.exception.InvalidAuthoException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface IUserDaoV4 {
	public AskFeature getPlanStatusByUser(String cbid);

	public Map<String, String> getUserId_cmpIdByAuthV2(String autho) throws InvalidAuthoException;;

	public UserV4 verifyAuthV4(String key, String value)throws InvalidAuthoException;
	
	public long getUserByUNameOrEmail(String email);

	public boolean updateUserv4byuserid(User user, long userid);

	public Map<String, String> getUserId_cmpIdByAuth(String auth) throws InvalidAuthoException;

	public List<JAlertV4> getUnackAlerts(long userid, String tempunit,String monitortype);

	public UserV4 verifyAuthV3(String key, String value) throws InvalidAuthoException;
	
	public List<UserV4> getUsersByUserId_CmpId(String userId, long cmpId);
	
	public int updateUserPassword(String authKey, String password);
	
	public boolean saveOrUpdateUserTokenV4(String userid, UserToken usertoken);

	public int executeQuery(String query);
	
	public String saveOrUpdateVPM_id(long user_id,String vpm_id);
	
	public Map<String, String> getVPMData(long user_id);
	
	public JResponse getuserlistbyfilter(String uKey, String uValue, String fType, String oType, long offset, long limit, String okey, JResponse response);

	public boolean updateViewCount(long user_id);
	
	public boolean updateAmazonReview(AmazonUserReview auReview, long monitorType);
	
	public int getViewCount(long user_id);
	
	public AmazonUserReview getAmazonUserReview(long userid, long monitorType);

	public boolean updateUserv4byuseridWeb(UserV4 user);

	public List<AmazonReviewList> getAmazonReviewList();
	
	public JUserFeedback getFeedbackLink(long userid,String category) ;
	
	public UserFeedbackTransaction getUserFeedbackTransaction(long userid, long formid);
	
	public boolean saveOrUpdateUserFeedback(UserFeedbackTransaction feedbackObj);

	public ArrayList<OrderMappingDetails> getOrderMappingListByUser(long userid);
	
	public String getLastFeedbackDate(long userid,long monitorType);
	
	public void addFreeVPM(String featureName, long userId);

	public long getUserbyVPMid(String vpmid);


	public int getInappRedirect(String osversion, String ostype);

	boolean updateInAppPurchaseInfo(int inapp_redirect, long userid);

	public boolean updateUserRVDetails(UserRvDetails userRvDetails, long userid);
	
	//public UserRvDetails getUserRvDetails(long user_id);

	public boolean createFeedbackFormWeb(FeedbackForm feedback);

	public List<FeedbackForm> listFeedbackWeb();
	
	public String createUserInChargebee(String fName, String lName, String email, String phoneNo, String userName,
			int amount, String desc);

	public boolean saveOTP(OneTimePassword otpObj);

	public boolean validateOTP(long userId,OneTimePassword otpObj, int validMinutes);

	public boolean updateOrderMappingDetails(long userId, String qrc, String orderId, String orderChannel,
			String deviceStatus, String orderSku, String orderedDate);
	
	public int updateUserPasswordV2(String username, String password);

	public int updateUserPasswordV3(String username, String password, String password_ver);

	public boolean updateEvalidation(String encryptId, String encryptPwd);

	public boolean insertEvalidation(String encryptId, String encryptPwd);

	public boolean getEvalidation(String encryptId);

	public String getEValidationPassword(String encryptId);
	
	public boolean getFreeTrialDays(String chargebeeid);

	public boolean saveFreshChatId(FreshChat freshChat);

	public FreshChat getFreshChatByUserId(long user_id);
	
	public boolean recreateUserInChargebee(String fName, String lName, String email, String phoneNo, String cbId);

	public long getUserGateway(String key, long value);

	public boolean deleteUserById(long userid);

	public boolean saveOrUpdateUserDelete(UserDelete user);

	public long getUserDlt(long user_id);

	public boolean saveOrUpdateUserDltDetails(UserDltInfo userDlt);

	public long getUserDltId(String meid);
	
	public List<CountryCodeV4> getCountryCodeList(String req_from);

	public String getUserCountryCode(String country);

	public UserMetaData getUserMetaData(long user_id);

	public boolean enableDisableMarketingNotification(UserMetaData userMetaData);

	public boolean removeDeleteRequest(long user_id, boolean isdelete);

	public boolean saveEmailOTP(EmailOtp emailOtpObj);
	
	public ArrayList<String> getUserFirebaseEvents(long userid);

	public boolean validateEmailOTP(String userName, EmailOtp otpObj, int validMinutesForOTP);
	
	public boolean saveOrUpdateUserMini(long userId,String deviceId,String deviceName, boolean isPetCam);
	
	public List<Object> listUserMiniCam(long userId);

	public boolean updateDeleteDeviceStatus(long userId, String deviceId);
	
	public ArrayList<String> checkReferralEligible(long userid);
	
	public ArrayList<String> checkActivateEligible(long userid);
	
	public void updateUserTimezone(long userid, String timezone);

	public void updateAddonTomorrowStatus(long gatewayid, boolean status);

	public void updateDeviceNotoficationStatus(long gatewayid, boolean status, long userId, boolean habit_notification);
	
	public boolean checkDeviceNotify(long userId,long gatewayid);

	public boolean delUserGatewayV2(long user_id, long gateway_id);

	public List<Object> listUserPetCam(long userId);

	public boolean checkActiveSubsForAmazonReview(long user_id);
	public ArrayList<String> checkAmazonRatingEligible(long userid);
	
	public boolean updateFirebaseCnt(long userid,int view, int click,String event_id);

	public UserTimeZone getUserTimeZone(long user_id);

	public boolean checkHabitalertNotify(long userId, long gatewayid);

	public PetCaloriesCalculation checkFoodFeededDetails(long userId, long gatewayid);

	public boolean checkFoodFeededorNot(long userId, long gatewayid);

	public boolean updateSensorlocation(long gatewayid, long sensorloc,String gname);

	public AskFeature saveOrUpdateAskFeature(AskFeature askFeature);

	public GeneralReview getGeneralReview(long userid, long monitorType);

	public boolean updateUserInChargebee(String fromemail, String toemail, String userName);

	public List<JSensorAlert> getSensorAlerts(long userid);

	public boolean checkActiveSubsForMeariDevice(long user_id, long monitorType);
	
	public JResponse getAskFeature(long offset, long limit);

	public ArrayList<String> checkProductBasedEligible(long userid);
	
	public UserLocation getUserLatLon(long id, long gateway_id);

	public void updateUserLocation(UserLocation userLocation);

	public UserFeedbackTransaction getUserFeedbackTransactionWithMonitor(long userid, long formid, long monitorType);

	public String checkInappNotify(long gatewayid);

	public List<JAlertV4> getUnackAlertsByGateway(long userid, String tempUnit, String monitortype, long gatewayid);

	int executePetProfileQuery(String qry);

	void updateEvent(InAppEvent event);

	User verifyUser(String key, String value);

	boolean saveUserOTPVerify(UserOtpVerification otpVerify);

	boolean validateUserOTP(UserOtpVerification otpVerification, int validMinutesForOTP);

	boolean isVetChatUser(long userId);

	ArrayList<String> checkPetMonitorPopupEligible(long userId);

	ArrayList<String> isN7PopupEligible(long userId);

	int checkWithInOtpTimeLimit(String tableName,String whereKey ,String userValue);

	int getOtpTimeLimit(String tableName, String whereKey, String userValue);

    boolean getFirstHitTimeDiff(String tableName, String whereKey, String userValueo);

	void saveFirstHitTimeDiff(String tableName, String whereKey, String userValue);

	boolean updateFullUserName(User user, long userId);

	boolean updateUserPlanPreference(UserPlanPreference userPlanPreference);

    String getChargebeeid(long userId);
}
