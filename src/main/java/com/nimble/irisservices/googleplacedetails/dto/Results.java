package com.nimble.irisservices.googleplacedetails.dto;

import java.util.ArrayList;

public class Results {
	
	  private Geometry geometry;
	  private String icon;
	  private String id;
	  private String name;
	  private Opening_hours opening_hours;
	  private ArrayList<Photos> photos = new ArrayList<Photos>();
	  private String place_id;
	  private Plus_code plus_code;
	  private float rating;
	  private String reference;
	  private String scope;
	  private ArrayList<Object> types = new ArrayList<Object>();
	  private String vicinity;
	  
	 private Long distance;

	public Geometry getGeometry() {
		return geometry;
	}

	public void setGeometry(Geometry geometry) {
		this.geometry = geometry;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Opening_hours getOpening_hours() {
		return opening_hours;
	}

	public void setOpening_hours(Opening_hours opening_hours) {
		this.opening_hours = opening_hours;
	}

	public ArrayList<Photos> getPhotos() {
		return photos;
	}

	public void setPhotos(ArrayList<Photos> photos) {
		this.photos = photos;
	}

	public String getPlace_id() {
		return place_id;
	}

	public void setPlace_id(String place_id) {
		this.place_id = place_id;
	}

	public Plus_code getPlus_code() {
		return plus_code;
	}

	public void setPlus_code(Plus_code plus_code) {
		this.plus_code = plus_code;
	}

	public float getRating() {
		return rating;
	}

	public void setRating(float rating) {
		this.rating = rating;
	}

	public String getReference() {
		return reference;
	}

	public void setReference(String reference) {
		this.reference = reference;
	}

	public String getScope() {
		return scope;
	}

	public void setScope(String scope) {
		this.scope = scope;
	}

	public ArrayList<Object> getTypes() {
		return types;
	}

	public void setTypes(ArrayList<Object> types) {
		this.types = types;
	}

	public String getVicinity() {
		return vicinity;
	}

	public void setVicinity(String vicinity) {
		this.vicinity = vicinity;
	}

	public Long getDistance() {
		return distance;
	}

	public void setDistance(Long distance) {
		this.distance = distance;
	}
	 
	 


	}
