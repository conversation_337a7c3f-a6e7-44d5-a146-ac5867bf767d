package com.nimble.irisservices.googleplacedetails.dto;

import java.util.ArrayList;

public class GooglePlaceDetailResponse {

	ArrayList <Object> html_attributions = new ArrayList <Object> ();

	private String status;

	private ArrayList <Results> results = new ArrayList <Results> ();

	
	public GooglePlaceDetailResponse(ArrayList<Object> html_attributions, String status, ArrayList<Results> results) {
		super();
		this.html_attributions = html_attributions;
		this.status = status;
		this.results = results;
	}
	
	public ArrayList<Object> getHtml_attributions() {
		return html_attributions;
	}

	public void setHtml_attributions(ArrayList<Object> html_attributions) {
		this.html_attributions = html_attributions;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public ArrayList<Results> getResults() {
		return results;
	}

	public void setResults(ArrayList<Results> results) {
		this.results = results;
	}

	
	
	
	}
