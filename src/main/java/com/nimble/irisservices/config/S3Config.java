package com.nimble.irisservices.config;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.nimble.irisservices.Util.SecretManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class S3Config {

    @Value("${aws_s3_secret_name}")
    private String S3_SECRET_NAME;

    @Autowired
    SecretManagerService secretManagerService;

    @Bean
    public AmazonS3 s3Client() {

        String secretKey = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_secret_key");
        String accessKey = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_access_key");

        BasicAWSCredentials awsCredentials = new BasicAWSCredentials(accessKey, secretKey);

        return AmazonS3Client.builder()
                .withRegion(Regions.US_WEST_2)
                .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                .build();
    }
}

