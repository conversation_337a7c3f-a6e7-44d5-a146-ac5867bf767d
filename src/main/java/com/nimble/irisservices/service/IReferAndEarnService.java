package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.entity.CreditType;
import com.nimble.irisservices.entity.ReferralCredits;
import com.nimble.irisservices.entity.ReferralDetail;

public interface IReferAndEarnService {
	public boolean deleteReferralCredits(long id) throws Exception;	
	public List<ReferralCredits> listReferralCredits();	
	public boolean saveOrUpdateReferralCredits(ReferralCredits refCredit) throws Exception;
	public ReferralCredits getLatestReferralCredits();
	
	public boolean saveOrUpdateReferralDetail(ReferralDetail ref) throws Exception;
	
	public CreditType getCreditTypeById(long id);
	public List<CreditType> listCreditType();	
}
