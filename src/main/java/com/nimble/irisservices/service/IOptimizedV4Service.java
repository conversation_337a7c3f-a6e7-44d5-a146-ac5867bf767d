package com.nimble.irisservices.service;
//
//import java.util.List;
//import java.util.Map;
//
//import org.springframework.dao.DataIntegrityViolationException;
//
//import com.nimble.irisservices.dto.AlertCfgV4;
//import com.nimble.irisservices.dto.CompanyConfigResponse;
//import com.nimble.irisservices.dto.JAlertV4;
//import com.nimble.irisservices.dto.JAssetLastReportV4;
//import com.nimble.irisservices.dto.JGateway;
//import com.nimble.irisservices.dto.JGatewayDetails;
//import com.nimble.irisservices.dto.JPetprofile;
//import com.nimble.irisservices.dto.JResponse;
//import com.nimble.irisservices.dto.JTrendingVideo;
//import com.nimble.irisservices.dto.JUserDeviceInfo;
//import com.nimble.irisservices.dto.UserV4;
//import com.nimble.irisservices.entity.AlertCfg;
//import com.nimble.irisservices.entity.AlertType;
//import com.nimble.irisservices.entity.CompanyConfig;
//import com.nimble.irisservices.entity.Gateway;
//import com.nimble.irisservices.entity.OrderChannel;
//import com.nimble.irisservices.entity.PetBreeds;
//import com.nimble.irisservices.entity.User;
//import com.nimble.irisservices.entity.UserDeviceInfo;
//import com.nimble.irisservices.entity.UserToken;
//import com.nimble.irisservices.exception.InvalidAuthoException;
//import com.nimble.irisservices.pojo.SendNotifications;
//
public interface IOptimizedV4Service {
//
//	public List<OrderChannel> getOrderChannelV4();
//
//	public List<PetBreeds> getPetBreedsV4(String speciename);
//
//	public int updateUserV4(String string, String password);
//
//	public boolean saveOrUpdateUserTokenV4(String string, UserToken usertoken);
//	
//	public String getUserNotificationStatusV4(String userid, String notificationID); 
//	
//	public long getUserByUNameOrEmailV4(String email);
//
//	public boolean updateUserv4byuserid(User user, long userid);
//
//	public Map<String, String> getUserId_cmpIdByAuth(String autho) throws InvalidAuthoException;
//
//	public boolean CreateVideoInfoTransaction(long userid, long videoid2, int like, int viewcount);
//
//	public JResponse getJPetprofilesByUserV4(long userId, long gatewayId, int monitortypeid);
//	
//	public List<JTrendingVideo> getTrendingvideoInfoV4(List<JTrendingVideo> trendvideoList, long parseLong) throws Exception;
//
//	public List<AlertCfgV4> getAlertCfgV4(long user_id, String tempunit, long asset_id);
//	
//	public CompanyConfig getCompanyConfigsForCmpCfgResponse(long cmp_id);
//	
//	public List<JAssetLastReportV4> getLastgatewayreportV4(String groupid, String subgroupid, String assetgroupid,
//			String gatewayid, long userId, String offset, String limit, String string);
//	
//	public List<AlertType> getAlertTypesV4();
//	
//	public List<UserV4> getUsersByUserId_CmpId(String userId, long cmpId);
//	
//	public UserV4 verifyAuthV3(String key, String value) throws InvalidAuthoException;
//	
//	public UserDeviceInfo getDeviceInfoV4(long userid, String deviceId);
//
//	boolean saveOrUpdateUserDeviceInfoV4(long userid, JUserDeviceInfo deviceInfo, String ipAddress, String Currdate)throws DataIntegrityViolationException;
//
//	boolean updateCompanyCfg(String cmpid, String cmpcfgid, String temperatureunit);
//	
//	public CompanyConfigResponse getCompanyConfigAndCompany(long cmpid);
//
//	public UserV4 verifyAuthV4(String key,String value) throws InvalidAuthoException;
//	
//	public int updateAlertCfg(long alertcfgid, long alerttypeid, float minval , float maxval, long gatewayid, long cmp_id);
//
//	public int updateEmailPhone(String alertcfgids, String phonenos, String emails);
//
//	public int updateNotify(String alertcfgids, String alerttype, int notifyfreq);
//
//	public int enabledisablealertcfg(String alertcfgids, boolean enable);
//	
//	public List<SendNotifications> userNotificationsV4(String userId, String status);
//
//	public AlertCfg getAlertCfg(long id);
//
//	public List<JAlertV4> getUnackAlerts(long userid, String tempUnit,String monitortype);
//
//	public int saveorupdatePetprofileV4(JPetprofile jpetprofiles, Long userid, boolean gatewayAva,String birth_date, long speciesid);
//
//	public Map<String, String> getUserId_cmpIdByAuthV2(String autho) throws InvalidAuthoException;
//	
//	public List<JGateway> getGatewayV4(String assetgroupid, String groupid, String subgroupid, String gatewayid,
//			long userid, String meid);
//	
//	public boolean UpdateVideoInfoTransaction(long id, long videoid, int like, int viewcount);
//	
//	public JGatewayDetails getJGatewayDetails(String key,String value);
//	
//	public int executeQuery(String qry);
//
//	public List<JAlertV4> getUnAckFurbitAlerts(long userid, String tempUnit,String monitortype);
//
}
