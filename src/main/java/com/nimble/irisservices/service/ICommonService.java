package com.nimble.irisservices.service;

import java.util.ArrayList;

import javax.servlet.http.HttpServletRequest;

import com.nimble.irisservices.dto.*;
import org.springframework.http.HttpHeaders;

public interface ICommonService {

	JResponse validateOneTimePassword(String auth, long otp, String userName, String request_from);

	JResponse validateCurrentPassword(UserV4 user, String currentPassword);

	JResponse passwordUpdateV4(UserV4 user, UpdatePassword udatePassword);

	JResponse oneTimePassword(String userName, String via, String request_from,String mobileno);

	JResponse findCountry(HttpServletRequest httpRequest, HttpHeaders header);

	ArrayList<JCategory> getProductCategory();

	ArrayList<JProductWithSubCategory> getProductList();
	
}
