package com.nimble.irisservices.service;

public interface IFurBitReportService {
	/*
	 * 
	 * 
	 * public List<FurBitDailyReport> getFurBitDailyRpt(String date);
	 * 
	 * public JFurBitReportReportSummary getFurBitDailyReport(String date, long
	 * userId, String gatewayId, String hour, String timezone,String gatewayname,
	 * Gateway gateway);
	 * 
	 * public JFurBitReportReportSummary getFurBitReport(String date, long userId,
	 * String gatewayId, String timezone, String days, String reportDays,String
	 * gatewayname, Gateway gateway);
	 * 
	 * public List<FurbitLastGatewayReport> getFurbitLastGatewayReport(String
	 * gateway) ;
	 * 
	 * public List<FurBitReport> getFurBitlastreport(String gatewayId);
	 * 
	 * public List<JLeaderBoard> getLeaderBoardDetails(String rpttype,String
	 * startDate, String endDate, int limit,HashMap<Long, Long> usergaway);
	 * 
	 * public List<JUserLeaderBoard> getUserLeaderBoardDetails( long userId,String
	 * startDate, String endDate,String rpttype);
	 * 
	 * JResponse insertPowerModeDynamicCmd(JGatewayDetails gateway, String cmdValue,
	 * int transportType, String powerModeStatus, String currentMode);
	 * 
	 * JResponse getDevicePowerMode(String gatewayid);
	 * 
	 * boolean deletePowerSaveModeDetailsForGateway(String gatewayId);
	 * 
	 * public JResponse insertAdventureModeDynamicCmd(JGatewayDetails gateway,
	 * String msg, int transportType, String status, String previousMode);
	 * 
	 * PowerModeConfig getPowerModeConfig(String key, String value);
	 * 
	 * public List<AdventureReport> getAdventureReport(long gatewayid) ;
	 * 
	 * public ArrayList<Mode> getAllDeviceModes();
	 * 
	 * public AdventureModeStatus getAdventureModeStatus(long gateway_id);
	 */}
