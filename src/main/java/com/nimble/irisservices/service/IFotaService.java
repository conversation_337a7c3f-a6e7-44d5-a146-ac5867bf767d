package com.nimble.irisservices.service;

import java.util.List;

import org.hibernate.exception.ConstraintViolationException;
import org.springframework.dao.DataIntegrityViolationException;

import com.nimble.irisservices.dto.FotaUpgrade;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.FotaModel;
import com.nimble.irisservices.entity.FotaVersion;
import com.nimble.irisservices.entity.LastFotaRpt;

public interface IFotaService {

	public List<FotaModel> getFotaModelList();
	
	public List<FotaVersion> getFotaVersionList();
	
	public boolean createOrUpdateModel(FotaModel fota) throws DataIntegrityViolationException,ConstraintViolationException;
	
	public boolean createOrUpdateVersion(FotaVersion fota) throws DataIntegrityViolationException,ConstraintViolationException;
	
	public AssetModel getAssetModelById(long modelId);

	public JResponse getFotaDetails(String sKey, String sValue, String fType, String otype, long offset, long limit, String oKey, long model_id, JResponse response, FotaUpgrade meid);

	public LastFotaRpt getLastfotareport(String meid);
}