package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.JGroups;
import com.nimble.irisservices.entity.AssetGroup;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.Group;
import com.nimble.irisservices.entity.Groups;
import com.nimble.irisservices.entity.GroupsC;
import com.nimble.irisservices.entity.GroupsD;
import com.nimble.irisservices.entity.GroupsE;
import com.nimble.irisservices.entity.GroupsF;
import com.nimble.irisservices.entity.SubGroup;
import com.nimble.irisservices.exception.InvalidGroupIdException;
import com.nimble.irisservices.exception.InvalidGroupNameException;

public interface IGroupServices {

	public List<SubGroup> getSubGroup(String groupid, String subgroupid, long id);

	public List<AssetGroup> getAssetGroup(String assetgroupid, long id);

	public List<Groups> getGroup(String groupid, long id);

	boolean saveOrUpdateAssetGroup(AssetGroup assetGroup, long cmpid);

	boolean saveOrUpdateGroup(Group group, long cmpid);

	boolean saveOrUpdateSubGroup(SubGroup subGroup, long cmpid) throws InvalidGroupIdException;
	
	boolean saveOrUpdateGroups(Groups groups, long cmpid);
	
	public List<JGroups> getGroups(String groupid, String topgroupid, String levelid, long id);
	
	public List<JGroups> getUserJGroup(String groupid, String topgroupid, String levelid,long userId);
	
	public Groups getGroupByName(String groupName, long cmpId)throws InvalidGroupNameException;
	
	public boolean saveORupdateGroups(Groups groupsA ,long cmpid);

	public Object getGroupsLevelObject(String valueOf, String string, String string2, long id);

	public List<JGroups> getgroups(String groupid, String topgroupid, String levelid, long userId);

	public List<JGroups> getgroupsB(String groupid, String topgroupid, String levelid, long userId);

	public List<JGroups> getgroupsC(String groupid, String topgroupid, String levelid, long userId);

	public List<JGroups> getgroupsD(String groupid, String topgroupid, String levelid, long userId);

	public List<JGroups> getgroupsE(String groupid, String topgroupid, String levelid, long userId);

	public List<JGroups> getgroupsF(String groupid, String topgroupid, String levelid, long userId);

	public void saveORupdateGroupsF(Company cmp, String groupsAname);

	public void saveORupdateGroupsE(Company cmp, GroupsF groupf, String groupsAname);

	public void saveORupdateGroupsD(Company cmp, GroupsE groupe, String groupsAname);

	public void saveORupdateGroupsC(Company cmp, GroupsD groupd, String groupsAname);

	public void saveORupdateGroupsB(Company cmp, GroupsC groupc, String groupsAname);

	public boolean deleteGroups(long id);

	public boolean deleteGroupsB(long cmp_id);

	public boolean deleteGroupsC(long cmp_id);

	public boolean deleteGroupsD(long cmp_id);

	public boolean deleteGroupsE(long cmp_id);

	public boolean deleteGroupsF(long cmp_id);

}
