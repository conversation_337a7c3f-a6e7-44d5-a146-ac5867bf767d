package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.JAddBenefits;
import com.nimble.irisservices.dto.JCouponData;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.RewardCredit;
import com.nimble.irisservices.entity.eBook;

public interface IAvatarService {

	public RewardCredit checkCredit(String emailId);

	public Boolean updateCredit(String emailId);

	public Boolean addCredit(long userId, String emailId, String periodName);

	public JAddBenefits getCouponContent(long userId, String emailId, String periodName);

	public List<eBook> getEbooks(Long userId);

	public Boolean buyEbook(Long userId, Long bookId);

	public Boolean addCoupon(long userId, String emailId, String code, String mugcode, String dueDate, String camcode,
			String refcode, String createdFrom);

	public JResponse generateCoupon(JCouponData couponObj,String createdFrom);

	public boolean checkBenefitsExpired(long userId);

	public boolean checkBenefitsAvail(String periodname);

	public RewardCredit checkCreditByPeriod(String emailId, int period_id);

	public JResponse generateSubCoupon(JCouponData couponObj, String period);

}
