package com.nimble.irisservices.service;

public interface IFurBitReportServiceV4 {
	/*
	 * 
	 * public JFurBitReportReportSummary getFurBitDailyReport(String date, long id,
	 * String string, String hour, String timezone, String name, Gateway gateway);
	 * 
	 * public JFurBitReportReportSummary getFurBitReport(String date, long id,
	 * String gatewayId, String timezone, String days, String reportDays, String
	 * gatewayName, Gateway gateway);
	 * 
	 */}
