package com.nimble.irisservices.service;

import com.nimble.irisservices.entity.SimReactivationHistory;

public interface IVerizonService {

	public boolean saveVerizonStatus(SimReactivationHistory simReactObj);
	
	boolean updateVerizonStatus(SimReactivationHistory simReactObj);

	public SimReactivationHistory getSimReactivationStatus(String key,String value);

	public SimReactivationHistory getSimReactivationStatusByMeid(String meid, String reqId);

	public boolean updateRequestId(String meid, String oldReqId, String reqId, String updateDate);

	public boolean updateRetryScheduledDate(String imei, String reqId);

	
}
