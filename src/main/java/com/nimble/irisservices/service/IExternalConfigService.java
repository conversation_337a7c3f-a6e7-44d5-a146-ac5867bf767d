package com.nimble.irisservices.service;

import java.util.List;

import org.springframework.dao.DataIntegrityViolationException;

import com.nimble.irisservices.entity.ExternalConfig;


public interface IExternalConfigService {

	public boolean saveOrUpdateExternalConfig(ExternalConfig externalConfig);

	public boolean updateExternalConfig(ExternalConfig externalConfig) throws DataIntegrityViolationException;

	public List<ExternalConfig> getAllExternalConfig();

	public ExternalConfig getExternalConfig(String name) throws DataIntegrityViolationException;

	public boolean deleteExternalConfig(String name);
	
}
