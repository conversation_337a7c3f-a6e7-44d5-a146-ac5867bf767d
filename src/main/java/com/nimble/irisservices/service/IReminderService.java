package com.nimble.irisservices.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.nimble.irisservices.dto.JReminderDetails;
import com.nimble.irisservices.entity.ReminderRepeat;

public interface IReminderService {

	boolean saveReminderDetails(long userid, String timezone, String remainderName, String displayname,
			String remainderDate, long repeatedId, String repeatedType, String remainderMsg, String jobstate,
			int enable);

	boolean disableReminderDetails(String userid, String remainderName, String state, int i);

	boolean updateReminderDetails(long reminderId, long userid, String timezone, String remainderName,String displayname, String utcDate, long repeatedId,
			String repeatedType, String remainderMsg, String string, int i);

	String saveReminderViewDetails(long id, String remainderName, Date triggerDate, int status, int onTime);

	boolean updateReminderViewDetails(long id, boolean isdeleted);

	Map<String, List<JReminderDetails>> getremainderOverDuelist(long id, int limitDays, int viewcomplete);

	String getRemainderViewDetails(long userid, long remainderid,  Date triggerDate);

	ArrayList<ReminderRepeat> getReminderRepeatTypeList(long repeatId);

	ArrayList<JReminderDetails> getReminderDetails(long id, long reminderid,String remainderName ,String timezone);

	int getremainderOverDueCount(long userId);



}
