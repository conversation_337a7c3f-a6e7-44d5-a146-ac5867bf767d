package com.nimble.irisservices.service;

import java.util.ArrayList;

import com.nimble.irisservices.dto.JAlertCount;
import com.nimble.irisservices.entity.AlertWC;

public interface IAlertServiceV4 {
	
	public JAlertCount getAlertCnt();
	
	public int getUnAckAlertCount(long cmp_id);

	public ArrayList<AlertWC> getAlertsWC(long gateway_id, long user_id);

	public AlertWC getAlertsWC(long alert_id);
}
