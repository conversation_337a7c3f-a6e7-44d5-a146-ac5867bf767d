package com.nimble.irisservices.service;

import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.User;

public interface IMailService {

	 public boolean sendVerificationMail(User user, String verificationCode);
	 
	 public boolean sendMail(String toAddr, String ccAddr, String bccAddr, String sub, String mailmsg);
	 
	 public boolean sendForgotPasswordMail(UserV4 user, String token );

	public boolean sendVerificationMailV2(UserV4 userV4, String token);
}
