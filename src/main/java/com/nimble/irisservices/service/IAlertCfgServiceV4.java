package com.nimble.irisservices.service;

import java.util.ArrayList;
import java.util.List;

import com.nimble.irisservices.dto.AlertCfgV4;
import com.nimble.irisservices.dto.JAlertCfgV4;
import com.nimble.irisservices.dto.JAlertSensor;
import com.nimble.irisservices.dto.JAlertsWC;
import com.nimble.irisservices.dto.JGeofence;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.AlertCfg;
import com.nimble.irisservices.entity.AlertCfgWC;
import com.nimble.irisservices.entity.AlertWC;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.User;

public interface IAlertCfgServiceV4 {
	
	public List<AlertCfgV4> getAlertCfgV4(long user_id, String tempunit, long asset_id,boolean  geofence_enable);

	public AlertCfg getAlertCfg(long id);

	public int enabledisablealertcfg(String alertcfgids, boolean enable);

	public int updateEmailPhone(String alertcfgids, String phonenos, String emails);
	
	public int updateAlertCfg(long alertcfgid, long alerttypeid, float minval , float maxval, long gatewayid, long cmp_id, String reqFrom);

	public int updateNotify(String alertcfgids, String alerttype, int notifyfreq);
	
	public int updateNotificationType(String alertcfgids, String notifytype);
	
	public JGeofence getGeofenceDetails(long gateway_id, String reqVer);
	
	public JResponse updateGeofenceDetails(JAlertCfgV4 jalertCfg,boolean isParkSafe);

	public boolean updateGeofenceState(long alertcfgId,int fenceState);

	public JResponse createTemperatureAlert(User user, Gateway gateway, String reqFrom) ;

	public JResponse createBatteryAlert(User user, Gateway gateway, String reqFrom) ;

	public JResponse createNetworkAlert(User user, Gateway gateway, String reqFrom);

	public JResponse createOnBatteryAlert(User user, Gateway gateway, boolean enableDelayFreq, int delayFreqSecs, String reqFrom) ;

	public JResponse createHumidityAlert(User user, Gateway gateway, String reqFrom);

	public JResponse createPowerRecoveryAlert(User user, Gateway gateway, boolean enableDelayFreq, int delayFreqSecs, String reqFrom);

	public JResponse createGeofenceAlert(User user, Gateway gateway, double lat, double lon, String reqFrom);

	public String createPMAlerts(User user, Gateway gateway, boolean enableDelayFreq, int delayFreqSecs, double lat,
			double lon, String reqFrom, boolean isAqi);

	public int updatealertrangeDetails(long alertid, long alerttypeid, int alert_range_id, float min, float max);

	public String createWCAlerts(User user, Gateway gateway);

	public AlertCfgWC saveOrUpdateAlertCfgWC(AlertCfgWC alertCfgWC);

	public JAlertsWC getWCAlertStatus(long gatewayid, long user_id);

	public List<AlertCfgWC> getAlertCfgWC(long gateway_id, String alert_type_id);

	public boolean updateAlertCfgRange(String min, String max, long gateway_id, String alert_type_id);

	public boolean updateAlertCfgWCEmailOrSmsOrPushNotification(long gateway_id, String value, String key);

	public ArrayList<AlertWC> getCurrentDateWCAlerts(long gateway_id);

	public float FahrenheitToCelsius(float temp_val_degree_Fahrenheit);
	
	public float CelsiusToFahrenheit(float temp_val_degree_celsius);

	public boolean updateNotifyFrequency(long notify_frequency, long gateway_id, String alert_type_id);
	
	public JAlertSensor getSensorAlertStatus(long gatewayid, long user_id);

	public String createSensorAlerts(User user, Gateway gateway, boolean enableDelayFreq, int delayFreqSecs, double lat,
			double lon, String string, int sensor_type_id);
	
	public boolean deleteAlertCfgById(ArrayList<Long> alertCfgIds);

	public boolean deleteAlertByAssetId(long id);
	
	public int enableAlldisablealertcfg(long assetid, boolean enable);

	public float getGeofenceDetails(int alertcfgId);

}
