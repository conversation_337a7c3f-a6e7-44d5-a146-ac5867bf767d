package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.JTrendingVideo;
import com.nimble.irisservices.entity.Trendingvideo;
import com.nimble.irisservices.entity.Trendingvideotransaction;
import com.nimble.irisservices.entity.User;

public interface ITrendingvideoService {
	
	public List<JTrendingVideo> getTrendingvideos();

	public boolean createTrendingvideoInfo( Trendingvideo videoInfo);

	public List<JTrendingVideo> getTrendingvideoInfo(List<JTrendingVideo> trendvideoList,long user_id) throws Exception;

	public Trendingvideotransaction getTrendingVideosByuser(User user, Trendingvideo videoInfo);

	public boolean CreateVideoInfoTransaction(Trendingvideotransaction trendVideoStaus);

	public Trendingvideo getvideoinfoByvideoid(long videoid);

	public Trendingvideo getvideoinfoByurl(String url);




}
