package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.CompanyConfigResponse;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.CompanyConfig;

public interface ICompanyServiceV4 {

	boolean updateCompanyCfg(String cmpid, String cmpcfgid, String temperatureunit);
	
	public CompanyConfigResponse getCompanyConfigAndCompany(long cmpid);

	public CompanyConfig getCompanyConfigsForCmpCfgResponse(long cmp_id);

	JResponse getCompanyListByFilter(String sKey, String sValue, String fType, String otype, long offset, long limit,
			String oKey, String cmpid, JResponse response);

	List<CompanyConfig> getCompanyConfigListWeb(long cmp_id);
}
