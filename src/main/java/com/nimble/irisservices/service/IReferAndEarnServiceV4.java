package com.nimble.irisservices.service;

import com.nimble.irisservices.dto.InAppReferReason;
import com.nimble.irisservices.entity.AppImage;
import com.nimble.irisservices.entity.ReferralCredits;

import java.util.List;

public interface IReferAndEarnServiceV4 {

	public ReferralCredits getLatestReferralCredits();
	
	public AppImage getAppImages(String type, String img_name);

	List<InAppReferReason> getNotInterestedReasons();

	boolean userIsEligible(long userId);
}
