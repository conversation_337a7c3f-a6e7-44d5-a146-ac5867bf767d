package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.JBreed;
import com.nimble.irisservices.entity.PetBreeds;

public interface IPetBreedServices {

	boolean saveORupdateBreed(PetBreeds petBreeds);
	
	List<PetBreeds> getAllPetBreeds();
	
	List<PetBreeds> getPetBreeds(int speciesId);
	
	List<PetBreeds> getPetBreedsByName(String speciesname);
	
	PetBreeds getPetBreedsByBName(String breedname);
	
	public List<JBreed> listBreeds(int speciesId);

}
