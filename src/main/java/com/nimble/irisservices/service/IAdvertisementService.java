package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.entity.Advertisement;
import com.nimble.irisservices.entity.AppImage;

public interface IAdvertisementService {
	
	public List<Advertisement> getAdvertismentUrl(String ver);

	public boolean createAdvertisement(Advertisement adinfo);
	
	public List<Advertisement> getAllAdvertismentUrl();
	
	public AppImage getAppImages(String type, String img_name);

}
