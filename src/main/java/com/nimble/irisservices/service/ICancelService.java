package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.entity.*;

public interface ICancelService {

	public List<CancelFeedback> getAllCancelFeedback();

	public List<CancelFeedbackContent> getAllCancelFeedbackContent();

	public UserCancelFeedBack getUserCancelFeedBackByUserId(long user_id);

	public UserCancelFeedBack saveOrUpdateUserCancelFeedBack(UserCancelFeedBack userCancelFeedBack);

	public CancelFeedback getCancelFeedbackById(long cancel_feedback_id);

	public AdditionBenefitsCancelReward getAdditionBenefitsCancelReward(long user_id);

	public AdditionBenefitsCancelReward saveOrUpdateAdditionBenefitsCancelReward(
			AdditionBenefitsCancelReward additionBenefitsCancelReward);

	public UserCancelFeedBackHistory saveOrUpdateUserCancelFeedBackHistory(UserCancelFeedBackHistory userCancelFeedBackHistory);

	public boolean deleteUserCancelFeedBack(long user_id);

	public UserCancelFeedBack getUserCancelFeedBackByShowCancel(long user_id, boolean show_cancel_sub);
	
	public List<NotTravelContent> getAllNotTravelContent();

	public List<NotHappyServiceContent> getAllNotHappyServiceContent();

	public List<CancelFeedbackContentList> getAllCancelFeedbackContentList(boolean is_flexi, long periodid);

}
