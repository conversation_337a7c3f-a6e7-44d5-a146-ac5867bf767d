package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.entity.BatteryLookup;
import com.nimble.irisservices.entity.ProbeCategory;
import com.nimble.irisservices.entity.ProbeLookup;

public interface IProbeCategoryService {

	public boolean createProbeCategory(ProbeCategory probe);

	public List<ProbeCategory> getAllProbeCategory();

	public ProbeCategory getProbeCategoryById(String probecategory);

	public boolean createProbeLookup(ProbeLookup probe);

	public List<ProbeLookup> getAllProbeLookup();

	public boolean createBatteryLookup(BatteryLookup probe);

	public List<BatteryLookup> getAllBatteryLookup();

}
