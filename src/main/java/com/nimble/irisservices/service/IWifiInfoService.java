package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.BluetoothDeviceList;
import com.nimble.irisservices.entity.WifiInfo;

public interface IWifiInfoService {

	public boolean saveOrUpdateWifiInfo(WifiInfo wifi) throws Exception;

	public WifiInfo isAlreadycontain(long gatewayid);

	public WifiInfo isAlreadycontain(long gatewayid,String ssidCategory, long userId);
	
	public List<BluetoothDeviceList> getWifiList(long gatewayID, long userID);
	
	boolean deleteWifiInfoForGateway(String userId, String gatewayId);
	
	boolean updateWifiInfoAddress(long gatewayid, String ssidCatergory, long userId , String address);


}
