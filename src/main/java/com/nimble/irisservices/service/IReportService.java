package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.JAssetDescription;
import com.nimble.irisservices.dto.JAssetLastReport;
import com.nimble.irisservices.dto.JAssetLastReportV4;
import com.nimble.irisservices.dto.JAssetReport;
import com.nimble.irisservices.dto.JDeviceReport;
import com.nimble.irisservices.dto.JNodeReport;
import com.nimble.irisservices.dto.JSensorReport;
import com.nimble.irisservices.dto.JSreiAssetLastReport;
import com.nimble.irisservices.dto.JTempReport;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.Node;
import com.nimble.irisservices.entity.PetFitReport;
import com.nimble.irisservices.exception.InvalidGroupNameException;

public interface IReportService {

	List<JAssetLastReport> getLastgatewayreport(String groupid, String subgroupid,
			String assetgroupid, String assetid,long userid, String offset, String limit,
			String tempunit,String monitortype);

	List<JNodeReport> getLastNodereport(String groupid, String subgroupid,
			String assetgroupid, String gatewayid,String nodeid,long cmpid, 
			String offset, String limit,String tempunit);

	List<JAssetReport> getgatewayreport(String fromtime, String totime,
			String assetgroupid,  String assetid,long cmpid,
			String offset, String limit,String tempunit);

	List<JNodeReport> geNodereport(String fromtime, String totime,
			String assetgroupid, String gatewayid, String nodeid, long cmpid,
			String offset, String limit,String tempunit);
	
	JAssetDescription getAssetDescription(long cmpid);

	List<JSreiAssetLastReport> getSreiLastgatewayreport(String groupid, String subgroupid,
			String assetgroupid, String assetid,long userid,
			String deliquencyStatus,String levelid, String offset, String limit);
	
	List<JSreiAssetLastReport> getSreiLastgatewayreportByCmp(String groupid, String subgroupid,
			String assetgroupid, String assetid,long cmpid, String deliquencyStatus,
			String levelid);
	
	boolean saveGatewayReport(Gateway gateway, double lat1 , double lon1);
	
	boolean saveLastGatewayReport(Gateway gateway, double lat1 , double lon1);
	
	long getgatewayreportCount(String fromtime, String totime,
			String assetgroupid,  String assetid,long cmpid);
	
	long getNodereportCount(String fromtime, String totime,
			String assetgroupid, String gatewayid, String nodeid, long cmpid);
	
	List<JDeviceReport> getDeviceSummary(String groupname,String subgroupid, String assetgroupid, String assetid,
			long userid, String offset,String limit,String tempunit, long cmp_id, boolean isMobileApp) throws InvalidGroupNameException;
	
	List<JDeviceReport> getDeviceSummaryV2(String groupname,String subgroupid, String assetgroupid, String assetid,
			long userid, String offset,String limit,String tempunit, long cmp_id, boolean isMobileApp) throws InvalidGroupNameException;
	
	
	boolean saveNodeReport(Node node);
	boolean saveLastNodeReport(Node node);
	
	List<PetFitReport> getPetFitReport();

	public List<JAssetLastReportV4> getLastgatewayreportV4(String groupid, String subgroupid, String assetgroupid,
			String gatewayid, long userId, String offset, String limit, String string);
	
	public List<JTempReport> getGatewayTempReports(String fromtime, String assetid,	String tempunit, String offset, String limit);

	public boolean updateLastGatewayReport(Gateway gateway);

	public boolean checkRecordInLastGatewayReport(Gateway gateway);
	
	public List<JSensorReport> getGatewaySmartBowlReport(long userid,long gatewayId, String tblName,String fromtime,String totime);
}
