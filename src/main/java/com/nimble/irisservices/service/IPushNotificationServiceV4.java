package com.nimble.irisservices.service;

import java.util.ArrayList;
import java.util.List;

import com.nimble.irisservices.entity.PushNotificationStatus;
import com.nimble.irisservices.pojo.SendNotifications;

public interface IPushNotificationServiceV4 {
	
	public List<SendNotifications> userNotificationsV4(String userId, String status, int monitor_type_id, boolean is_device_based);
	
	public int getNotViewedNotificationCount(long userid);
	
	public boolean updateNotificationStatus(long userId, ArrayList<Long> notificationIds);

	public PushNotificationStatus getPushNotificationStatus(long push_notification_status_id);
}
