package com.nimble.irisservices.service;

import java.util.ArrayList;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.ReCBWebhookStatus;

public interface IRechargeService {
	
	public boolean saveRechargeCBSubStatus(ReCBWebhookStatus reSub);
	
	public ArrayList<String> getReSubscription(String email,String order_id);

	public JResponse createRechargeSubscription(String userName, String chargebeeId, long user_id, String given_order_id, long gatewayId, long monitorID);

	public ArrayList<String> getReSubscriptionByEmail(String userName);

	public ArrayList<String> getRechargeSubscriptinInfo(String orderId);

}
