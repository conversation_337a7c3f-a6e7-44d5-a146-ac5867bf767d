package com.nimble.irisservices.service;

/*
 * This interface encapsulates the sms gateway that we are using.
 * So in future, we can use more than one sms gateway.
 */
public interface SMSInterface {

	public boolean SendMessage(String phoneNo, String message,String cmpid,
			String cmpname, String appname,String srcPhNo,boolean enablePowerBack, String powerBackUUid);
    public boolean SendVoiceMessage(String phoneNo, String message, String cmpid,
			String cmpname, String appname, String ip,String srcphno);
    
    public boolean SendEmail(String emailds,String subject,String content);
    public boolean SendEmail_SES(String alert_email,String emailds,String subject,String content,String filename,boolean isRVMailId);
    
    public boolean SendEmail_SESV2(String alert_email,String emailds,String subject,String content,String filename,boolean isRVMailId);
}
