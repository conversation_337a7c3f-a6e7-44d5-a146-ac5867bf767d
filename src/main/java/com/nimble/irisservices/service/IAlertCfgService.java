package com.nimble.irisservices.service;

import java.util.ArrayList;
import java.util.List;

import com.nimble.irisservices.dto.JAlertCfg;
import com.nimble.irisservices.exception.InvalidAlertTypeException;
import com.nimble.irisservices.exception.InvalidAsseIdException;

public interface IAlertCfgService {

	
	public List<JAlertCfg> getAlertCfg(String alertcfgid, String alerttypeid, String assetid, long userid,String tempunit);

	public boolean saveORupdateAlertCfg(JAlertCfg jalertcfg, long cmpid, String reqFrom) throws InvalidAlertTypeException, InvalidAsseIdException;
	public boolean delAlertcfg(long userid,String assetid);
	public int enabledisablealertcfg(String alertcfgids,int enable);
	
	public int getAdcLookUp (float tempValue,String probeCategory);
	
//	public JResponse getFurbitAlertCfg(long userId,long asset_id, JResponse response);
//	
//	public FurbitAlertCfg getFurbitAlertCfg(long id);
//	
//	int enableOrDisableFurbitAlertcfg(String alertcfgids, boolean enable);
//	
//	public int updateEmailPhoneFurbitAlertCfg(String alertcfgids, String phonenos, String emails);
	
	int updateNotify(String alertcfgids, String alerttype, int notifyfreq);
	
	public ArrayList<Long> getAlertCfgIds(long asset_id);
	
//	int updateFurbitAlertCfg(FurbitAlertCfg furbitAlertCfg, long alerttypeid, float minval, float maxval, long gatewayid, long cmp_id);
//
//	public boolean saveOrUpdateFurbitAlertCfg(JFurbitAlertCfg jfurbitalertcfg, Company company) throws InvalidAlertTypeException, InvalidAsseIdException;
//	
//	int updateFurbitNotify(String alertcfgids, String alerttype, int notifyfreq);
//	
//	public List<JFurbitAlertCfg> getFurbitAlertCfg(String alertcfgid, String alerttypeid, String assetid, long userid,
//			String tempunit);

}
