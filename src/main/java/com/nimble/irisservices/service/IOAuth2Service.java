package com.nimble.irisservices.service;

import com.nimble.irisservices.dto.JwtTokenData;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.provider.NoSuchClientException;
import org.springframework.security.oauth2.provider.OAuth2Authentication;

import com.nimble.irisservices.entity.UserEntity;

public interface IOAuth2Service {
	
	public byte[] generateOauth2Token(String username, String password, String clientId, String clientSecret );

	public ResponseEntity<OAuth2AccessToken> getOAuth2TokenByRefreshToken(String refreshToken, String clientId, String clientSecret) throws NoSuchClientException,InvalidTokenException;
	
	public UserEntity getUserDetails(String emailId);

	public OAuth2Authentication getOAuth2(String username, String password);

	public String saveOauth2Code(OAuth2Authentication authentication);

	String generateJWTToken(JwtTokenData jwtTokenData);

	JwtTokenData validateJWTToken(String token);
}
