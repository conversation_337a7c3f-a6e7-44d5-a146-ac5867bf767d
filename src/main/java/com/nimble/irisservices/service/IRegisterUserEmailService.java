package com.nimble.irisservices.service;

import com.nimble.irisservices.entity.RegisterUserEmailStatus;

public interface IRegisterUserEmailService {

	void updateRegisterUserEmailStatus(String toAddr, String ccAddr, String bccAddr, String sub, String mailmsg,String qrCode,String errorCode,
			boolean status, String meid, String iccid,String vendor, int isRecallDevice, String skuNumber, String userEmail, String deviceId, String tariffId, String qrc);
	
	public void externalQrcActivationStatus(String qrcode,String errorCode,String toAddr,String ccAddr,String bccAddr,String sub,String mailmsg);

	public RegisterUserEmailStatus getEmailStatus(String qrCode, String errorCode);
	
}
