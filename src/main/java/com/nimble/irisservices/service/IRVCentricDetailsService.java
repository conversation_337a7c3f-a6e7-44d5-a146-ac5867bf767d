package com.nimble.irisservices.service;

public interface IRVCentricDetailsService {
//
//	public List<RVCentricDetails> listRVCentricDetailsList();
//	
//	public List<RVCentricDetails> listAllRVCentricDetailsList();
//
//	public boolean saveOrUpdateRVCentricDetails(RVCentricDetails rvObj);
//
//	public RVCentricDetails getRVCentricDetailsById(long id);
//
//	public List<JRVChecklist> listRVCheckList(long userid);
//	
//	public List<RVChecklist> listAllRVCheckList();
//
//	public int saveOrUpdateRVChecklist(RVChecklist rvObj) throws SQLIntegrityConstraintViolationException;
//	
//	public boolean updateRVCheckList(long userId,ArrayList<Long> checklistIds);
//	
//	public boolean deleteUserCheckList(long userId,ArrayList<Long> checklistIds);
//	
//	public List<RVChecklistType> listRVChecklistType();
//	
//	public List<Badges> listAllBadges();
//
//	public List<JBadges> listBadges(long userid);
//	
//	public ArrayList<JUserBadges> getUserBadges(long userid,String api_type);
//	
//	public ArrayList<RVAnswer> listRVAnswer();
//	
//	public boolean saveUserBadgeTxn(long userId,String plan_id,	int device_cnt, boolean updatedRVProfile, String cbId) ;
//	
//	public HashSet<Long> checkUserBadgeExist(long userid);
//
//	public JUserLifetimeTrip getUserLifetimeTrip(long userid);
//
//	public RvTripHistory saveUserTripHistory(RvTripHistory rvTripHistory);
//
//	public boolean endUserTrip(long userid, String trip_id, String plan_id);
//	
//	public ArrayList<RVAnswer> listRVAnswerByQuest(long questId);
//	
//	public JSONObject toParseJson(String url);
//	
//	public int getTripCount(long userid);
//	
//	public boolean saveUserBadgeTxn(long userId,long badge_id,long stage_id);
}
