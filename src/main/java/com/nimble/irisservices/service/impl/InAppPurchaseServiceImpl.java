package com.nimble.irisservices.service.impl;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.dao.IInAppPurchaseDao;
import com.nimble.irisservices.entity.InAppPurchase;
import com.nimble.irisservices.entity.InAppSubscription;
import com.nimble.irisservices.service.IInAppPurchseService;

@Transactional
@Service
public class InAppPurchaseServiceImpl implements IInAppPurchseService {

	@Autowired
	IInAppPurchaseDao inAppPurchaseDao;

	@Override
	public int updateInAppPurchseInfo(InAppPurchase inAppPurchase) {
		return inAppPurchaseDao.updateInAppPurchseInfo(inAppPurchase);
	}

	@Override
	public InAppPurchase getInAppPurchseInfo(String transactionId) {
		return inAppPurchaseDao.getInAppPurchseInfo(transactionId);
	}

	@Override
	public boolean saveOrUpdateSubscriptionInfo(InAppSubscription inappSub) {
		return inAppPurchaseDao.saveOrUpdateSubscriptionInfo(inappSub);
	}

}
