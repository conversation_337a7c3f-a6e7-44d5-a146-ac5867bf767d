package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IExternalConfigDao;
import com.nimble.irisservices.entity.ExternalConfig;
import com.nimble.irisservices.service.IExternalConfigService;

@Service
public class ExternalConfigServiceImpl implements IExternalConfigService {

	
	@Autowired
	IExternalConfigDao extenalConfigDao;
	
	@Override
	@Transactional
	public boolean saveOrUpdateExternalConfig(ExternalConfig externalConfig) {
		// TODO Auto-generated method stub
		return extenalConfigDao.saveOrUpdateExternalConfig(externalConfig);
	}

	@Override
	@Transactional
	public boolean updateExternalConfig(ExternalConfig externalConfig) throws DataIntegrityViolationException {
		// TODO Auto-generated method stub
		return extenalConfigDao.updateExternalConfig(externalConfig);
	}

	@Override
	@Transactional
	public List<ExternalConfig> getAllExternalConfig() {
		// TODO Auto-generated method stub
		return extenalConfigDao.getAllExternalConfig();
	}

	@Override
	@Transactional
	public ExternalConfig getExternalConfig(String name) throws DataIntegrityViolationException {
		// TODO Auto-generated method stub
		return extenalConfigDao.getExternalConfig(name);
	}

	@Override
	@Transactional
	public boolean deleteExternalConfig(String name) {
		// TODO Auto-generated method stub
		return extenalConfigDao.deleteExternalConfig(name);
	}

	
}
