package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.INodeDao;
import com.nimble.irisservices.dto.JNode;
import com.nimble.irisservices.dto.JNodeOverview;
import com.nimble.irisservices.entity.Node;
import com.nimble.irisservices.exception.InvalidAsseIdException;
import com.nimble.irisservices.exception.InvalidAssetGroupIdException;
import com.nimble.irisservices.exception.InvalidGatewayIdException;
import com.nimble.irisservices.exception.InvalidModelIdException;
import com.nimble.irisservices.service.INodeService;
@Service
public class NodeServiceimpl implements INodeService {
	
	@Autowired
	INodeDao nodeDao;
	
	@Transactional
	public List<JNode> getNode(String assetgroupid, String groupid,
			String subgroupid, String gatewayid, String nodeid,long cmpid) {
		// TODO Auto-generated method stub
		return nodeDao.getNode(assetgroupid,groupid,subgroupid,gatewayid,nodeid,cmpid);
	}

	@Transactional
	public JNodeOverview getNodeoverview(String groupid,
			String subgroupid, long userid) {
		
		JNodeOverview nodeOverview = nodeDao.getNodeCount(groupid, subgroupid, userid);
		return nodeOverview;
	}
	
	@Transactional
	public Node saveORupdateNode(JNode node, long cmpid, long userId) throws InvalidAssetGroupIdException, InvalidGatewayIdException, InvalidModelIdException, InvalidAsseIdException {
		// TODO Auto-generated method stub
		return nodeDao.saveORupdateNode(node, cmpid, userId);
	}

}
