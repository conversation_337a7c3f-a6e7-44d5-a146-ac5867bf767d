package com.nimble.irisservices.service.impl;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IGatewayConfigDao;
import com.nimble.irisservices.entity.Gatewayconfig;
import com.nimble.irisservices.service.IGatewayConfigService;
@Repository
public class GatewayConfigsServiceImpl implements IGatewayConfigService {

	private static final Logger log = LogManager.getLogger(GatewayConfigsServiceImpl.class);
	@Autowired
	private SessionFactory sessionFactory;

	@Autowired
	IGatewayConfigDao gatewayConfigDao;

	@Override
	@Transactional
	public boolean saveOrUpdateGatewayConfig(Gatewayconfig gatewayconfig) {
		// TODO Auto-generated method stub

		return gatewayConfigDao.saveOrUpdateGatewayConfig(gatewayconfig);

	}

	@Override
	@Transactional
	public boolean update(Gatewayconfig gatewayconfig) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	@Transactional
	public Gatewayconfig getGatewayConfig(Long gatewayID) {

		return gatewayConfigDao.getGatewayConfig(gatewayID);

	}

	@Override
	@Transactional
	public List<Gatewayconfig> getAllGatewayConfig() {
		return gatewayConfigDao.getAllGatewayConfig();
	}

}
