package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IAccountDao;
import com.nimble.irisservices.entity.Apidetails;
import com.nimble.irisservices.entity.CompanyAccountSettings;
import com.nimble.irisservices.service.IAccountService;


@Service
public class AccountServiceImpl  implements IAccountService{

		
	@Autowired
	@Lazy
	IAccountDao accountDao;
		
	
	@Transactional
	public boolean saveOrUpdateCmpAccount(CompanyAccountSettings cmpAccDetails) throws DataIntegrityViolationException {
		// TODO Auto-generated method stub
		return accountDao.saveOrUpdateCmpAccount(cmpAccDetails);
	}

	@Transactional
	public boolean update(CompanyAccountSettings cmpAccDetails) throws DataIntegrityViolationException {
		// TODO Auto-generated method stub
		return accountDao.update(cmpAccDetails);
	}
	
	@Transactional
	public List<CompanyAccountSettings> getCmpAccount(CompanyAccountSettings cmpAccDetails) {
		// TODO Auto-generated method stub
		return accountDao.getCmpAccount(cmpAccDetails);
	}

	@Transactional
	public boolean deleteCmpAccount(long apiId, String serviceType, String api, String description) {
		// TODO Auto-generated method stub
		return false;
	}

	@Transactional
	public boolean saveOrUpdateApiDetails(Apidetails apiDetails) throws DataIntegrityViolationException {
		// TODO Auto-generated method stub
		return accountDao.saveOrUpdateApiDetails(apiDetails);
	}

	@Transactional
	public List<Apidetails> getApi(Apidetails apiDetails) {
		// TODO Auto-generated method stub
		return accountDao.getApi(apiDetails);
	}




	@Override
	public boolean deleteApi(Apidetails apiDetails) {
		// TODO Auto-generated method stub
		return false;
	}
}
