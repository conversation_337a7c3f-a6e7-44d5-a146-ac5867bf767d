package com.nimble.irisservices.service.impl;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IGroupDaos;
import com.nimble.irisservices.dto.JGroups;
import com.nimble.irisservices.entity.AssetGroup;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.Group;
import com.nimble.irisservices.entity.Groups;
import com.nimble.irisservices.entity.GroupsC;
import com.nimble.irisservices.entity.GroupsD;
import com.nimble.irisservices.entity.GroupsE;
import com.nimble.irisservices.entity.GroupsF;
import com.nimble.irisservices.entity.SubGroup;
import com.nimble.irisservices.exception.InvalidGroupIdException;
import com.nimble.irisservices.exception.InvalidGroupNameException;
import com.nimble.irisservices.service.IGroupServices;
@Service
@Transactional
public class GroupServiceImpl implements IGroupServices{
	
	private static final Logger log = LogManager.getLogger(GroupServiceImpl.class);
	
	@Autowired
	IGroupDaos groupDaos;
	
	@Autowired
	IGroupServices groupServices; 
	
	@Transactional
	public List<SubGroup> getSubGroup(String groupid, String subgroupid, long cmpid) {
		return groupDaos.getSubGroup(groupid,subgroupid,cmpid);
	}

	@Transactional
	public List<AssetGroup> getAssetGroup(String assetgroupid, long cmpid) {
		return groupDaos.getAssetGroup(assetgroupid,cmpid);
	}
	
	@Transactional
	public List<Groups> getGroup(String groupid, long cmpid) {
		return groupDaos.getGroup(groupid,cmpid);
	}

	@Transactional
	public boolean saveOrUpdateAssetGroup(AssetGroup assetGroup, long cmpid) {
		return groupDaos.saveORupdateAssetGroup(assetGroup, cmpid);
	}

	@Transactional
	public boolean saveOrUpdateGroup(Group group, long cmpid) {
		return groupDaos.saveORupdateGroup(group, cmpid);
	}

	@Transactional
	public boolean saveOrUpdateSubGroup(SubGroup subGroup, long cmpid) throws InvalidGroupIdException {
		return groupDaos.saveORupdateSubGroup(subGroup, cmpid);
	}

	@Transactional
	public boolean saveOrUpdateGroups(Groups groups, long cmpid) {
		return groupDaos.saveORupdateGroups(groups, cmpid);
	}

	@Transactional
	public List<JGroups> getGroups(String groupid, String topgroupid, String levelid, long id) {
		return groupDaos.getGroups(groupid, topgroupid, levelid, id);
	}

	@Transactional
	public List<JGroups> getUserJGroup(String groupid, String topgroupid, String levelid, long userId) {
		return groupDaos.getUserJGroup(groupid, topgroupid, levelid, userId);
	}

	@Transactional
	public Groups getGroupByName(String groupName, long cmpId)
			throws InvalidGroupNameException {
		return groupDaos.getGroupByName(groupName, cmpId);
	}

	@Override
	public boolean saveORupdateGroups(Groups groupsA, long cmpid) {
		return groupDaos.saveORupdateGroups(groupsA, cmpid);
	}

	@Override
	public Object getGroupsLevelObject(String valueOf, String string, String string2, long id) {
		return groupDaos.getGroupsLevelObject(valueOf, string, string2, id);
	}

	@Override
	public List<JGroups> getgroups(String groupid, String topgroupid, String levelid, long userId) {
		return groupDaos.getgroups(groupid, topgroupid, levelid, userId);
	}

	@Override
	public List<JGroups> getgroupsB(String groupid, String topgroupid, String levelid, long userId) {
		return groupDaos.getgroupsB(groupid, topgroupid, levelid, userId);
	}

	@Override
	public List<JGroups> getgroupsC(String groupid, String topgroupid, String levelid, long userId) {
		return groupDaos.getgroupsC(groupid, topgroupid, levelid, userId);
	}

	@Override
	public List<JGroups> getgroupsD(String groupid, String topgroupid, String levelid, long userId) {
		return groupDaos.getgroupsD(groupid, topgroupid, levelid, userId);
	}

	@Override
	public List<JGroups> getgroupsE(String groupid, String topgroupid, String levelid, long userId) {
		return groupDaos.getgroupsE(groupid, topgroupid, levelid, userId);
	}

	@Override
	public List<JGroups> getgroupsF(String groupid, String topgroupid, String levelid, long userId) {
		return groupDaos.getgroupsF(groupid, topgroupid, levelid, userId);
	}

	@Override
	public void saveORupdateGroupsF(Company cmp, String groupsAname) {
		groupDaos.saveORupdateGroupsF(cmp, groupsAname);
	}

	@Override
	public void saveORupdateGroupsE(Company cmp, GroupsF groupf, String groupsAname) {
		groupDaos.saveORupdateGroupsE(cmp, groupf, groupsAname);
	}

	@Override
	public void saveORupdateGroupsD(Company cmp, GroupsE groupe, String groupsAname) {
		groupDaos.saveORupdateGroupsD(cmp, groupe, groupsAname);
	}

	@Override
	public void saveORupdateGroupsC(Company cmp, GroupsD groupd, String groupsAname) {
		groupDaos.saveORupdateGroupsC(cmp, groupd, groupsAname);
	}

	@Override
	public void saveORupdateGroupsB(Company cmp, GroupsC groupc, String groupsAname) {
		groupDaos.saveORupdateGroupsB(cmp, groupc, groupsAname);
	}

	@Override
	public boolean deleteGroups(long cmp_id) {
		log.info("Entered into deleteGroups :: company id : "+cmp_id);
		try {
		
			boolean deleteGroupsStatus = groupDaos.deleteGroups(cmp_id);
			log.info("delete Groups Status : "+deleteGroupsStatus);
			
			boolean deleteGroupBStatus = groupServices.deleteGroupsB(cmp_id);
			log.info("delete Group B Status : "+deleteGroupBStatus);
			
			boolean deleteGroupCStatus = groupServices.deleteGroupsC(cmp_id);
			log.info("delete Group C Status : "+deleteGroupCStatus);
			
			boolean deleteGroupDStatus = groupServices.deleteGroupsD(cmp_id);
			log.info("delete Group D Status : "+deleteGroupDStatus);
			
			boolean deleteGroupEStatus = groupServices.deleteGroupsE(cmp_id);
			log.info("delete Group E Status : "+deleteGroupEStatus);
			
			boolean deleteGroupFStatus = groupServices.deleteGroupsF(cmp_id);
			log.info("delete Group F Status : "+deleteGroupFStatus);
			
			return true;
		} catch (Exception e) {
			log.error("Error in deleteGroups :: Error : "+cmp_id);
			return false;
		}
		
	}

	@Override
	public boolean deleteGroupsB(long cmp_id) {
		return groupDaos.deleteGroupsB(cmp_id);
	}

	@Override
	public boolean deleteGroupsC(long cmp_id) {
		return groupDaos.deleteGroupsC(cmp_id);
	}

	@Override
	public boolean deleteGroupsD(long cmp_id) {
		return groupDaos.deleteGroupsD(cmp_id);
	}

	@Override
	public boolean deleteGroupsE(long cmp_id) {
		return groupDaos.deleteGroupsE(cmp_id);
	}

	@Override
	public boolean deleteGroupsF(long cmp_id) {
		return groupDaos.deleteGroupsF(cmp_id);
	}
	
}
