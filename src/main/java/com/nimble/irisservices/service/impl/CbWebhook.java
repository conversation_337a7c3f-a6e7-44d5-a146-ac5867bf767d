package com.nimble.irisservices.service.impl;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.dao.ICbWebhookDao;
import com.nimble.irisservices.dao.IUserDaoV4;
import com.nimble.irisservices.service.ICbWebhook;

@Service
@Transactional
public class CbWebhook implements ICbWebhook {

	@Autowired
	IUserDaoV4 userDaov4;
	
	@Autowired
	ICbWebhookDao cbWebhookDao;

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;
	
	@Value("${chargebee.addonid}")
	private String activationAddonId;

	@Value("${chargebee.updateaddonid}")
	private String updateAddonId;

	@Value("${chargebee.reactivationid}")
	private String reactivateAddonId;
	
	@Value("${freeplan}")
	private String freeplan;

	@Value("${omitplan}")
	private String omitplan;
	
	@Value("${chargebee.coupon}")
	private String couponId;

	@Override
	public boolean saveWebHookStatus(String eventid, String eventType) {
		String Createqry = "INSERT INTO `cbwebhookstatus`(`eventid`,`eventtype`) VALUES ( '" + eventid + "','"
				+ eventType + "');";
		if (userDaov4.executeQuery(Createqry) == 0)
			return false;
		return true;
	}

	@Override
	public boolean webHookStatusIsAvailable(String event_id, String event_type) {
		// TODO Auto-generated method stub
		return cbWebhookDao.webHookStatusIsAvailable( event_id,  event_type);
	}

}
