package com.nimble.irisservices.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IAlertCfgDao;
import com.nimble.irisservices.dto.JAlertCfg;
import com.nimble.irisservices.exception.InvalidAlertTypeException;
import com.nimble.irisservices.exception.InvalidAsseIdException;
import com.nimble.irisservices.service.IAlertCfgService;

@Service
public class AlertCfgServiceImpl implements IAlertCfgService {
	
	@Autowired
	IAlertCfgDao alertCfgDao;

	@Transactional
	public List<JAlertCfg> getAlertCfg(String alertcfgid, String alerttypeid,
			 String assetid,long userid,String tempunit) {
		return alertCfgDao.getAlertCfg(alertcfgid,alerttypeid,assetid,userid,tempunit);
	}
	
	@Transactional
	public boolean saveORupdateAlertCfg(JAlertCfg jalertcfg, long cmpid, String reqFrom) throws InvalidAlertTypeException, InvalidAsseIdException {
		return alertCfgDao.saveORupdateAlertCfg(jalertcfg, cmpid, reqFrom);
	}

	@Transactional
	public boolean delAlertcfg(long userid, String assetid) {
		return alertCfgDao.delAlertcfg(userid, assetid);
	}
	
	@Transactional
	public int enabledisablealertcfg(String alertcfgids,int enable) {
		return alertCfgDao.enabledisablealertcfg(alertcfgids, enable);
	}

	@Override
	@Transactional
	public int getAdcLookUp(float tempValue, String probeCategory) {
		return alertCfgDao.getAdcLookUp(tempValue, probeCategory);
	}

//	@Override
//	@Transactional
//	public JResponse getFurbitAlertCfg(long userId, long asset_id, JResponse response) {
//		return alertCfgDao.getFurbitAlertCfg(userId, asset_id,response);
//	}
//
//	@Override
//	@Transactional
//	public FurbitAlertCfg getFurbitAlertCfg(long id) {
//		return alertCfgDao.getFurbitAlertCfg(id);
//	}
//
//	@Override
//	@Transactional
//	public int enableOrDisableFurbitAlertcfg(String alertcfgids, boolean enable) {
//		return alertCfgDao.enableOrDisableFurbitAlertcfg(alertcfgids, enable);
//	}
//
//	@Override
//	@Transactional
//	public int updateEmailPhoneFurbitAlertCfg(String alertcfgids, String phonenos, String emails) {
//		return alertCfgDao.updateEmailPhoneFurbitAlertCfg(alertcfgids, phonenos, emails);
//	}

	@Override
	@Transactional
	public int updateNotify(String alertcfgids, String alerttype, int notifyfreq) {
		return alertCfgDao.updateNotify(alertcfgids, alerttype, notifyfreq);
	}
	
	@Override
	@Transactional
	public ArrayList<Long> getAlertCfgIds(long asset_id) {
		return alertCfgDao.getAlertCfgIds(asset_id);
	}

//	@Override
//	@Transactional
//	public int updateFurbitAlertCfg(FurbitAlertCfg furbitAlertCfg, long alerttypeid, float minval, float maxval, long gatewayid,
//			long cmp_id) {
//		return alertCfgDao.updateFurbitAlertCfg(furbitAlertCfg, alerttypeid, minval, maxval, gatewayid, cmp_id);
//	}
//	
//	@Transactional
//	public boolean saveOrUpdateFurbitAlertCfg(JFurbitAlertCfg jfurbitalertcfg, Company company) throws InvalidAlertTypeException, InvalidAsseIdException {
//		return alertCfgDao.saveOrUpdateFurbitAlertCfg(jfurbitalertcfg, company);
//	}
//
//	@Override
//	@Transactional
//	public int updateFurbitNotify(String alertcfgids, String alerttype, int notifyfreq) {
//		return alertCfgDao.updateFurbitNotify(alertcfgids, alerttype, notifyfreq);
//	}
//	
//	@Override
//	@Transactional
//	public List<JFurbitAlertCfg> getFurbitAlertCfg(String alertcfgid, String alerttypeid, String assetid, long userid,
//			String tempunit){
//		return alertCfgDao.getFurbitAlertCfg(alertcfgid, alerttypeid, assetid, userid, tempunit);
//	}
	
}
