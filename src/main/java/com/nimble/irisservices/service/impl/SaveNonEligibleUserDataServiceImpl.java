package com.nimble.irisservices.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.impl.SaveNonEligibleUserDataDaoImpl;
import com.nimble.irisservices.entity.VetChatNonEligibleUsers;

@Service
@Transactional
public class SaveNonEligibleUserDataServiceImpl {
	
	@Autowired
	private SaveNonEligibleUserDataDaoImpl saveNonEligibleUserDataDaoImpl;

	public void saveNonEligibleUser(VetChatNonEligibleUsers saveNonEligibleUserData) {
		saveNonEligibleUserDataDaoImpl.saveNonEligibleUser(saveNonEligibleUserData);
	}
	
	
}
