package com.nimble.irisservices.service.impl;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.dao.ISignTypeDao;
import com.nimble.irisservices.entity.SignupType;
import com.nimble.irisservices.service.ISignTypeService;

@Service
@Transactional
public class SignTypeServiceImpl implements ISignTypeService {

	@Autowired
	ISignTypeDao signtypeDao;

	@Override
	public SignupType getSignType(String id) {
		return signtypeDao.getSignType(id);
	}

}
