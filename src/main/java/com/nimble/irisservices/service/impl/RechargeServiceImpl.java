package com.nimble.irisservices.service.impl;

import java.util.ArrayList;

import javax.transaction.Transactional;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.chargebee.models.HostedPage;
import com.nimble.irisservices.dao.IRechargeDao;
import com.nimble.irisservices.dao.IUserDaoV4;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.ReCBWebhookStatus;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IRechargeService;
import com.nimble.irisservices.service.IUserServiceV4;

@Service
@Transactional
public class RechargeServiceImpl implements IRechargeService {

	@Autowired
	IUserDaoV4 userDaov4;
	
	@Autowired
	IRechargeDao reDao;
	
	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;
	
	@Autowired
	@Lazy
	IChargebeeService cbService;
	
	@Autowired
	@Lazy
	ICreditSystemService crService;
	
	@Autowired
	@Lazy
	IRechargeService rechargeService;

	private static final Logger log = LogManager.getLogger(RechargeServiceImpl.class);

	@Override
	public ArrayList<String> getReSubscription(String email,String order_id) {
		return reDao.getReSubscription(email,order_id);
	}

	public boolean saveRechargeCBSubStatus(ReCBWebhookStatus reSub) {
		return reDao.saveRechargeCBSubStatus(reSub);
	}

	@Override
	public JResponse createRechargeSubscription(String userName, String chargebeeId, long user_id, String given_order_id, long gatewayId, long monitorID) {
		log.info("Entered into createRechargeSubscription :: email : "+userName);
		JResponse response = new JResponse();
		try {
			response.put("is_recharge", false);
			ArrayList<String> subDetails = new ArrayList<String>();
			String cbPlan = "NA";
			String recharge_cusid = "NA";
			String reSubId ="NA";
			String email_match ="NA";
			String nextrenewal_at = null;
			String price ="0.0";
			boolean is_email_match = false;
			boolean is_recharge = false;
			String order_id = "0";
			String mailContent = "";
			
			if( given_order_id.equalsIgnoreCase("NA") ) {
				subDetails = rechargeService.getReSubscriptionByEmail(userName);	
			} else {
				subDetails = rechargeService.getReSubscription(userName, given_order_id);	
			}
			
			if(!subDetails.isEmpty()) {
				recharge_cusid = subDetails.get(0);
				cbPlan = subDetails.get(1);
				reSubId = subDetails.get(2);

				if( !userName.equalsIgnoreCase("NA") ) {
					is_email_match= subDetails.get(3).equalsIgnoreCase(userName) ? true : false;
				} else {
					is_email_match=true;
				}
				
				nextrenewal_at= subDetails.get(4);
				price =subDetails.get(5);
				is_recharge = true;
				mailContent = mailContent + "<p> Recharage customer : true </p>";
				response.put("mail_content", mailContent);
				order_id = subDetails.get(6);
				if(!is_email_match) {
					mailContent = mailContent + "<p>Subscription Status :  Recharage Purchase email and signup email are different.</p>";
				}
			}else {
				mailContent = mailContent + "<p> Recharage customer : false </p>";
				is_recharge = false;
				response.put("mail_content", mailContent);
			}
			response.put("is_recharge", is_recharge);
			String subs_status = "user ver:v2 / product subscription disabled";
			response.put("subs_status", subs_status);
			String cb_status = "";
			response.put("mail_content", mailContent);
			
			if(!cbPlan.equalsIgnoreCase("NA")) {
				if(!is_email_match) {
					subs_status = "Recharge purchase order email and signup email not matched. so subscription not created for orderid : "+order_id+". Activate subscription manually. ";
					log.info(subs_status);
				} else {
					
					String cb_subs_status = "NA";
					
					String planVer = cbService.getPlanVersionbyplanname(cbPlan);
					
					if(planVer != null && planVer.equalsIgnoreCase("V3")) {
						
						if(monitorID > 0 && monitorID == 1)
							cbService.saveTemp_sub_purchase(user_id, gatewayId, 1, reSubId);
							
						 cb_subs_status = cbService.createCBSubsForRechargePlanv3("NA", chargebeeId, cbPlan, reSubId,nextrenewal_at,price);
					}else {
						 cb_subs_status = cbService.createCBSubsForRecharge("NA", chargebeeId, cbPlan, reSubId,nextrenewal_at,price);
					}

					if (!cb_subs_status.equalsIgnoreCase("NA") && !cb_subs_status.equalsIgnoreCase("Failed")) {

						if (cb_subs_status.equalsIgnoreCase("Success")) {
							mailContent = mailContent + "<p> Recharge subscription Created in CB</p>";
							cb_status = "Sub_Created";
							subs_status = "Recharge subscription created in CB";
						}
						else {
							cb_status = "Sub_Available";
							subs_status = "Already paid subscription available in CB, in order to update subscription plan,do it manually in CB portal";
							mailContent = mailContent + "<p> Paid Subcription already available, So recharge bundle subscription"
									+ " not created. In order to activate bundle subscription need to activate manually in CB</p>";
						}
							
						boolean stat1 = crService.updateSubStatus(String.valueOf(order_id), String.valueOf(user_id));
						log.info("update product_subscription:" + stat1);

						stat1 = crService.updateReSubStatus(String.valueOf(order_id),cb_subs_status);
						log.info("update updateReSubStatus:" + stat1);

						String qry = "update user set recharge_custid='" + recharge_cusid + "' where id=" + user_id + " ;";
						int stat2 = userServiceV4.executeQuery(qry);

						log.info("update user:" + user_id + " recustid:" + recharge_cusid + " :" + stat2);
					}else {
						subs_status = "Recharge order email matched. But subscription creation failed due to internal error.";
					}
				}
				response.put("subs_status", subs_status);
				response.put("show_payment_page", false);
				response.put("sub_details", subDetails);
				response.put("mail_content", mailContent);
				response.put("cb_status", cb_status);
				return response;
			}
			
		} catch (Exception e) {
			log.error("Error  in createRechargeSubscription :: Error : "+e.getLocalizedMessage());
		}
		return response;
	}

	@Override
	public ArrayList<String> getReSubscriptionByEmail(String userName) {
		return reDao.getReSubscriptionByEmail(userName);
	}

	@Override
	public ArrayList<String> getRechargeSubscriptinInfo(String orderId) {
		return reDao.getRechargeSubscriptinInfo(orderId);
	}
	
}
