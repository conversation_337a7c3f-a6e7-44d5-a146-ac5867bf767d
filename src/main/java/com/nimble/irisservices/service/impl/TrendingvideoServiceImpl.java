package com.nimble.irisservices.service.impl;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.dao.ITrendingvideoDao;
import com.nimble.irisservices.dto.JTrendingVideo;
import com.nimble.irisservices.entity.Trendingvideo;
import com.nimble.irisservices.entity.Trendingvideotransaction;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.service.ITrendingvideoService;

@Service
public class TrendingvideoServiceImpl implements ITrendingvideoService{
	
	@Autowired
	ITrendingvideoDao ITrendingvidDao;
	
	@Override
	@Transactional
	public List<JTrendingVideo> getTrendingvideos() {
		return ITrendingvidDao.getTrendingvideos();
	}

	@Override
	@Transactional
	public boolean createTrendingvideoInfo(Trendingvideo videoInfo) {
		return ITrendingvidDao.SaveOrUpdateTrendingvidInfo(videoInfo);
	}

	@Override
	@Transactional
	public List<JTrendingVideo> getTrendingvideoInfo(List<JTrendingVideo> TVUserInfo,long user_id) throws Exception {
		return ITrendingvidDao.getTrendingvideosUserInfo(TVUserInfo,user_id);
	}

	@Override
	@Transactional
	public Trendingvideotransaction getTrendingVideosByuser(User user,Trendingvideo videourl) {
		return ITrendingvidDao.SaveOrUpdateTrendingvidStatus(user,videourl);
	}


	@Override
	@Transactional
	public boolean CreateVideoInfoTransaction(Trendingvideotransaction trendVideoStaus) {
		return ITrendingvidDao.CreateOrUpdateVideoStatus(trendVideoStaus);
	}

	@Override
	@Transactional
	public Trendingvideo getvideoinfoByvideoid(long videoid) {
		return ITrendingvidDao.Getingvideoinfo(videoid);
	}

	@Override
	@Transactional
	public Trendingvideo getvideoinfoByurl(String url) {
		return ITrendingvidDao.Getingvideoinfobyurl(url);
	}


	
}
