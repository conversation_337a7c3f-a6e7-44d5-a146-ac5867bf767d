package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IAlertDao;
import com.nimble.irisservices.dto.JAlert;
import com.nimble.irisservices.dto.JAlertOverview;
import com.nimble.irisservices.dto.JAlertRange;
import com.nimble.irisservices.dto.JAlertV4;
import com.nimble.irisservices.dto.JSreiAlertOverview;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.service.IAlertService;

@Service
@Transactional
public class AlertServiceImpl implements IAlertService {

	@Autowired
	IAlertDao alertdao;

	@Transactional
	public List<JAlert> getalerts(String groupid, String subgroupid, String gatewayid, String alerttype, long userid,
			String deliquencyStatus, String tempunit, String fromtime, String totime, String nodeid, String id) {
		return alertdao.getalerts(groupid, subgroupid, gatewayid, alerttype, userid, deliquencyStatus, tempunit,
				fromtime, totime, nodeid, id);
	}

	@Transactional
	public JAlertOverview getalertoverview(String groupid, String subgroupid, long userid) {
		JAlertOverview alertoverview = alertdao.getAlertCount(groupid, subgroupid, userid);
		return alertoverview;
	}

	@Transactional
	public JSreiAlertOverview getsreialertoverview(String groupid, String subgroupid, long userid) {

		JSreiAlertOverview sAlertOverview = alertdao.getSreiAlertCount(groupid, subgroupid, userid);
		return sAlertOverview;
	}

	@Transactional
	public boolean delAlert(long userid, String assetid) {
		return alertdao.delAlert(userid, assetid);
	}

	@Transactional
	public List<JAlertRange> getAlertRange(String assetid, User user) {
		return alertdao.getAlertRange(assetid, user);
	}

	@Override
	@Transactional
	public List<JAlert> getackalerts(String groupid, String subgroupid, String gatewayid, String alerttype, long userid,
			String deliquencyStatus, String tempunit, String fromtime, String totime, String nodeid, String id) {
		return alertdao.getackalerts(groupid, subgroupid, gatewayid, alerttype, userid, deliquencyStatus, tempunit,
				fromtime, totime, nodeid, id);
	}

	@Override
	@Transactional
	public List<JAlertV4> getUnAckFurbitAlerts(long userid, String tempUnit, String monitortype, String timezone) {
		return alertdao.getUnAckFurbitAlerts(userid, tempUnit, monitortype,timezone);
	}

	@Override
	@Transactional	
	public List<JAlert> getFurbitalerts(String groupid, String subgroupid,String gatewayid, String alerttypeid, 
			long userid ,String deliquencyStatus,String tempunit,String fromtime,String totime, String nodeid, String id){
		return alertdao.getFurbitalerts(groupid, subgroupid, gatewayid, alerttypeid, userid, deliquencyStatus,
				tempunit, fromtime, totime, nodeid, id);
	}
}
