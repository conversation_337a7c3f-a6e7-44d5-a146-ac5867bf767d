package com.nimble.irisservices.service.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.InternetHeaders;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import com.nimble.irisservices.Util.SecretManagerService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.service.IMailService;

import freemarker.template.Configuration;
import freemarker.template.Template;

@Service
@Transactional
public class MailServiceImpl implements IMailService {

	@Value("${verificationAPIURL}")
	private String verificationAPIURL ;
	
	@Value("${verificationtime}")
	private long verifytime;
	
	@Value("${resetpasswordweblink}")
	private String resetPasswordWebLink;

	@Autowired
	private SecretManagerService secretManagerService;

	@Value("${aws_ses_secret_name}")
	private String SES_SECRET_NAME;

	@Autowired
	Configuration templates;
	
	private static final Logger log = LogManager.getLogger(MailServiceImpl.class);

	 //private final Configuration templates;
	@Override
	public boolean sendVerificationMail(User user, String verificationCode) {
		   String subject = "Waggle - Account Verification";
	        String body = "";
	        try {
	        	
	            Template t = templates.getTemplate("verifyemailcontent.ftl");
	            Map<String, String> map = new HashMap<>();
	            map.put("FNAME", user.getFirstname() );
	            map.put("LNAME", user.getLastname());
	            map.put("USERNAME", user.getUsername());
	           // map.put("PASSWORD", user.getPassword());
	            map.put("VERIFICATION_URL", verificationAPIURL + verificationCode);
	            map.put("verifytime", String.valueOf(verifytime));
	            body = FreeMarkerTemplateUtils.processTemplateIntoString(t, map);
	            log.info("sendVerificationMail : "+verificationAPIURL + verificationCode);
	            
	        } catch (Exception ex) {
	        	log.error("sendVerificationMail : "+ex.getLocalizedMessage());
	        }
	       // user.setEmail("<EMAIL>");
	        return this.sendMail(user.getEmail(), "","<EMAIL>",subject, body);
	}

	@Override
	public boolean sendMail(String toAddr, String ccAddr, String bccAddr, String sub, String mailmsg) {
	
		String EMAIL_HOST = "email-smtp.us-west-2.amazonaws.com";
		String EMAIL_HOST_USER = secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_access_key");
		String EMAIL_HOST_PASSWORD = secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_secret_key");

		String login = "<EMAIL>";
		Transport transport = null;

		try {
			Properties props = new Properties();
			props.put("mail.transport.protocol", "smtps");
			props.put("mail.smtp.auth", "true");
			props.setProperty("mail.smtp.port", "587");
	           
			props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");

			// Create a Session object to represent a mail session with the
			// specified properties.
			Session session = Session.getDefaultInstance(props);

			// Create a message with the specified information.
			MimeMessage msg = new MimeMessage(session);
			msg.setFrom(new InternetAddress(login));

			if (toAddr != null) {
				msg.setRecipients(Message.RecipientType.TO, toAddr);
			}
			
			if (ccAddr != null) {
				msg.setRecipients(Message.RecipientType.CC, ccAddr);
			}

			if (bccAddr != null) {
				msg.setRecipients(Message.RecipientType.BCC, bccAddr);
			}
			msg.setSubject(sub);
			// msg.setContent(mailmsg,"text/plain");

			InternetHeaders headers = new InternetHeaders();
			headers.addHeader("Content-type", "text/html; charset=UTF-8");
			MimeBodyPart body = new MimeBodyPart(headers, mailmsg.getBytes("UTF-8"));
			Multipart multipart = new MimeMultipart();
			multipart.addBodyPart(body);

			// Send the complete message parts
			msg.setContent(multipart);

			// Create a transport.
			transport = session.getTransport();
			log.info("Attempting to send an email through the Amazon SES SMTP interface...");

			// Connect to Amazon SES using the SMTP username and password you
			// specified above.
			transport.connect(EMAIL_HOST, EMAIL_HOST_USER, EMAIL_HOST_PASSWORD);

			// Send the email.

			transport.sendMessage(msg, msg.getAllRecipients());
			log.info("Email sent!");
			return true;
		} catch (AddressException ex) {
			log.error("Address Exception occured :" + ex.getMessage());
		} catch (MessagingException ex) {
			log.error("Message Exception occured :" + ex.getMessage());
		} catch (Exception ex) {
			log.error("SendEmail Exception: " + ex.getMessage());
		}

		finally {
			try {
				if (null != transport)
					transport.close();
			} catch (Exception e) {
				log.error("SendEmail Exception: " + e.getMessage());
			}
		}
		return false;
	}

//	@Override
	public boolean sendForgotPasswordMailOld(UserV4 user) {
		String subject = "Forgot your password?";
        String body = "";
        try {
        	
            Template t = templates.getTemplate("Forgotpasswordpage.ftl");
            Map<String, String> map = new HashMap<>();
            String firstName = user.getFirstname();
            map.put("FNAME", firstName.substring(0, 1).toUpperCase() + firstName.substring(1));
            map.put("USERNAME", user.getUsername());
            map.put("PASSWORD", user.getPassword());
            body = FreeMarkerTemplateUtils.processTemplateIntoString(t, map);
        }catch(Exception e) {
        	log.error("Error : "+e.getLocalizedMessage());
        }
        return this.sendMail(user.getEmail(),"", "<EMAIL>",subject, body);
	}
		
	@Override
	public boolean sendForgotPasswordMail(UserV4 user, String token ) {
		String subject = "Forgot your password?";
        String body = "";
                
        String changepasswordlink = resetPasswordWebLink+"/forgetpassword?code="+token+ 
        		"&email="+user.getEmail();
       
        try {        	
            Template t = templates.getTemplate("Forgotpasswordpage.ftl");
            Map<String, String> map = new HashMap<>();
            String firstName = user.getFirstname();
            map.put("FNAME", firstName.substring(0, 1).toUpperCase() + firstName.substring(1));
            map.put("USERNAME", user.getUsername());
            map.put("PASSWORD", user.getPassword());
            map.put("NEWPASSWORDLINK", changepasswordlink);
            body = FreeMarkerTemplateUtils.processTemplateIntoString(t, map);
        }catch(Exception e) {
        	log.error("Error sendForgotPasswordMail : "+e.getLocalizedMessage());
        }
        return this.sendMail(user.getEmail(),"", "<EMAIL>",subject, body);
	}

	@Override
	public boolean sendVerificationMailV2(UserV4 user, String verificationCode) {

		   String subject = "Waggle - Account Verification";
	        String body = "";
	        try {
	        	
	            Template t = templates.getTemplate("verifyemailcontent.ftl");
	            Map<String, String> map = new HashMap<>();
	            map.put("FNAME", user.getFirstname() );
	            map.put("LNAME", user.getLastname());
	            map.put("USERNAME", user.getUsername());
	           // map.put("PASSWORD", user.getPassword());
	            map.put("VERIFICATION_URL", verificationAPIURL + verificationCode);
	            map.put("verifytime", String.valueOf(verifytime));
	            body = FreeMarkerTemplateUtils.processTemplateIntoString(t, map);
	            log.info("sendVerificationMail : "+verificationAPIURL + verificationCode);
	            
	        } catch (Exception ex) {
	        	log.error("sendVerificationMail : "+ex.getLocalizedMessage());
	        }
	       // user.setEmail("<EMAIL>");
	        return this.sendMail(user.getEmail(), "","<EMAIL>",subject, body);
	
	}
}
