package com.nimble.irisservices.service.impl;

import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Random;
import java.util.TimeZone;

import org.apache.http.entity.StringEntity;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IAvatarDao;
import com.nimble.irisservices.dao.impl.AccountDaoImpl;
import com.nimble.irisservices.dto.JAddBenefits;
import com.nimble.irisservices.dto.JCouponData;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.RewardCredit;
import com.nimble.irisservices.entity.eBook;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAvatarService;

@Service
@Transactional
public class AvatarServiceImpl implements IAvatarService {

	private static final Logger log = LogManager.getLogger(AccountDaoImpl.class);
	Helper _helper = new Helper();

	@Autowired
	IAvatarDao iAvatarDao;
	
	@Value("${cancelCouponPercent}")
	private int cancelCouponPercent;

	@Override
	@Transactional
	public RewardCredit checkCredit(String emailId) {
		return iAvatarDao.checkCredit(emailId);
	}

	@Override
	public Boolean updateCredit(String emailId) {
		return iAvatarDao.updateCredit(emailId);
	}

	@Override
	public Boolean addCredit(long userId, String emailId, String periodName) {
		return iAvatarDao.addCredit(userId, emailId, periodName);
	}

	@Override
	public Boolean addCoupon(long userId, String emailId, String Coupon, String MugCoupon, String CamCoupon,
			String RefCoupon, String dueDate, String createdFrom) {
		return iAvatarDao.addCoupon(userId, emailId, Coupon, MugCoupon, CamCoupon, RefCoupon, dueDate, createdFrom);
	}

	@Override
	public JAddBenefits getCouponContent(long userId, String emailId, String periodName) {
		return iAvatarDao.getCouponContent(userId, emailId, periodName);
	}

	@Override
	public List<eBook> getEbooks(Long userId) {
		return iAvatarDao.getEbooks(userId);
	}

	@Override
	public Boolean buyEbook(Long userId, Long eBookId) {
		return iAvatarDao.buyEbook(userId, eBookId);
	}

	@Override
	public JResponse generateCoupon(JCouponData couponObj,String createdFrom) {
		JResponse response = new JResponse();
		String currentTime = "";
		String dueDateConverted = "";

		String emailId = couponObj.getEmailId();
		String periodid = couponObj.getPeriod();
		long userId = couponObj.getUserid();
		String firstName = couponObj.getFirstName();
		String lastName = couponObj.getLastName();
		String dueDate = couponObj.getDueDate();

		try {
			HashMap<String, String> periodlist = new HashMap<String, String>();
			periodlist.put("1", "Monthly");
			periodlist.put("2", "Quarterly");
			periodlist.put("3", "Half-Yearly");
			periodlist.put("4", "Yearly");
			periodlist.put("5", "2-Year");
			periodlist.put("6", "5-Year");
			String period = periodlist.get(periodid);

			JAddBenefits benefitsObj = getCouponContent(userId, emailId, period);

			RewardCredit rewardObj = checkCreditByPeriod(emailId, Integer.parseInt(periodid));

			if (rewardObj != null) {
				log.info("Benefits already created:" + emailId);
				boolean avatar = false;
				boolean ebook = false;
				boolean merch = false;
				boolean mug = false;
				boolean waggleCam = false;
				boolean referral = false;

				if (!rewardObj.getMerchCoupon().equalsIgnoreCase("NA"))
					merch = true;
				if (!rewardObj.getMugCoupon().equalsIgnoreCase("NA"))
					mug = true;
				if (!rewardObj.getwaggleCamCoupon().equalsIgnoreCase("NA"))
					waggleCam = true;
				if (!rewardObj.getReferralCoupon().equalsIgnoreCase("NA"))
					referral = true;
				if (rewardObj.getEbookCredit() > 0)
					ebook = true;
				if (rewardObj.getCredit() > 0)
					avatar = true;

				Calendar calendar = Calendar.getInstance();
				Date d = calendar.getTime();
				SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
				df.setTimeZone(TimeZone.getTimeZone("UTC"));
				currentTime = df.format(d) + "+00:00";

				response.put("Status", 1);
				response.put("mugcode", rewardObj.getMugCoupon());
				response.put("couponCode", rewardObj.getMerchCoupon());
				response.put("wagglecamcouponcode", rewardObj.getwaggleCamCoupon());
				response.put("referralcoupon", rewardObj.getReferralCoupon());
				response.put("startDate", currentTime);
				response.put("endDate", currentTime);
				response.put("showEbook", ebook);
				response.put("showAvatar", avatar);
				response.put("showMerchCoupon", merch);
				response.put("showMugCoupon", mug);
				response.put("showWaggleCamCoupon", waggleCam);
				response.put("showReferralCoupon", referral);
				response.put("mugUrl", "https://www.wagglemerch.com/products/white-glossy-mug");
				response.put("merchUrl", "https://www.wagglemerch.com/");
				response.put("ebookurl", "https://www.wagglemerch.com/collections/waggfluence-pet-ebooks");
				response.put("waggleCamUrl", "https://mywaggle.com/pages/wagglecam");
				response.put("referralUrl", "");

				response.put("couponContent1", benefitsObj != null ? benefitsObj.getContent1() : "NA");
				response.put("couponContent2", benefitsObj != null ? benefitsObj.getContent2() : "NA");
				response.put("couponContent3", benefitsObj != null ? benefitsObj.getContent3() : "NA");
				response.put("couponContent4", benefitsObj != null ? benefitsObj.getContent4() : "NA");
			} else {
				Boolean creditSuccess = addCredit(userId, emailId, period);

				if (creditSuccess && benefitsObj != null) {
					String customerId = "";

					if (benefitsObj.getCoupon_prcnt() > 0 || benefitsObj.getMug_prcnt() > 0
							|| benefitsObj.getCam_prcnt() > 0 || benefitsObj.getRef_prcnt() > 0) {
						// Get customerid from shopify using mailid
						String apiResponse = _helper.getShopifyUrl(
								"https://wagglesupplies.myshopify.com/admin/api/2023-04/customers/search.json?query=email:"
										+ emailId);
						JSONObject apiResJson = new JSONObject(apiResponse);
						if (apiResJson.has("customers")) {
							JSONArray resultlist = apiResJson.getJSONArray("customers");

							if (resultlist.length() > 0) {
								customerId = resultlist.getJSONObject(0).get("id").toString();
							} else {
								String requestparam = "{\n" + "    \"customer\": {\n" + "        \"first_name\": \""
										+ firstName + "\",\n" + "        \"last_name\": \"" + lastName + "\",\n"
										+ "        \"email\": \"" + emailId + "\",\n"
										+ "        \"verified_email\": true,\n" + "      \n"
										+ "        \"send_email_invite\": true\n" + "    }\n" + "}";
								StringEntity params;
								try {
									params = new StringEntity(requestparam);
								} catch (UnsupportedEncodingException e) {
									throw new RuntimeException(e);
								}
								String createCustomerResponse = _helper.postShopifyRequest(
										"https://wagglesupplies.myshopify.com/admin/api/2023-04/customers.json",
										"application/json", params);
								JSONObject createCustomerResJson = new JSONObject(createCustomerResponse);
								if (createCustomerResJson.has("customer")) {
									JSONObject customerdata = createCustomerResJson.getJSONObject("customer");
									Long id = customerdata.getLong("id");
									customerId = id.toString();
								} else {
									log.info("2-create Customer Resp: " + emailId + " :"
											+ createCustomerResJson.toString());
//							 	Get customerid from shopify using mailid
									Thread.sleep(1000);
									apiResponse = _helper.getShopifyUrl(
											"https://wagglesupplies.myshopify.com/admin/api/2023-04/customers/search.json?query=email:"
													+ emailId);
									apiResJson = new JSONObject(apiResponse);
									log.info("3-Get Customer Resp: " + emailId + " :" + apiResJson.toString());
									if (apiResJson.has("customers")) {
										resultlist = apiResJson.getJSONArray("customers");

										if (resultlist.length() > 0) {
											customerId = resultlist.getJSONObject(0).get("id").toString();
											log.info("2-got Customer id: " + customerId);
										}
									}
								}
							}
						}

					}
					Random rand = new Random();
					int rand_int = rand.nextInt(1000);
					Boolean success = false;
					String mugcode = "NA";
					String code = "NA";
					String referralCode = "NA";
					String waggleCamCode = "NA";
//					String createdFrom="cancel";

					if (benefitsObj.getCoupon_prcnt() > 0 && !customerId.isEmpty()) {
						// Creating Price Rule
						Calendar calendar = Calendar.getInstance();
						Date d = calendar.getTime();
						SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
						DateTimeFormatter dueDateFormat = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss",
								Locale.ENGLISH);
						LocalDate duedateLoc = LocalDate.parse(dueDate, dueDateFormat);
						Date DueDateFromLocalDate = java.sql.Date.valueOf(duedateLoc);
						df.setTimeZone(TimeZone.getTimeZone("UTC"));
						currentTime = df.format(d);
						currentTime = currentTime + "-00:00";
						dueDateConverted = df.format(DueDateFromLocalDate);
						dueDateConverted = dueDateConverted + "+00:00";
						String DiscountCode = emailId.substring(0, 4).toUpperCase() + customerId.substring(0, 4)
								+ rand_int;
						String priceName = "[AdditionalBenefits-Merch] " + DiscountCode;

						String requestparam = "{\n" + "    \"price_rule\": {\n"
								+ "        \"value_type\": \"percentage\",\n" + "        \"value\": \"-"
								+ benefitsObj.getCoupon_prcnt() + "\",\n"
								+ "        \"customer_selection\": \"prerequisite\",\n"
								+ "        \"target_type\": \"line_item\",\n"
								+ "        \"target_selection\": \"all\",\n"
								+ "        \"allocation_method\": \"across\",\n"
								+ "        \"allocation_limit\": null,\n" + "        \"once_per_customer\": false,\n"
								+ "        \"usage_limit\": null,\n" + "        \"starts_at\": \"" + currentTime
								+ "\",\n" + "        \"ends_at\": \"" + dueDateConverted + "\",\n"
								+ "        \"created_at\": \"" + currentTime + "\",\n" + "        \"updated_at\": \""
								+ currentTime + "\",\n" + "        \"entitled_product_ids\": [],\n"
								+ "        \"entitled_variant_ids\": [],\n"
								+ "        \"entitled_collection_ids\": [],\n"
								+ "        \"entitled_country_ids\": [],\n"
								+ "        \"prerequisite_product_ids\": [],\n"
								+ "        \"prerequisite_variant_ids\": [],\n"
								+ "        \"prerequisite_collection_ids\": [],\n"
								+ "        \"customer_segment_prerequisite_ids\": [],\n"
								+ "        \"prerequisite_customer_ids\": [\n" + customerId + "            \n"
								+ "        ],\n" + "        \"prerequisite_subtotal_range\": null,\n"
								+ "        \"prerequisite_quantity_range\": null,\n"
								+ "        \"prerequisite_shipping_price_range\": null,\n"
								+ "        \"prerequisite_to_entitlement_quantity_ratio\": {\n"
								+ "            \"prerequisite_quantity\": null,\n"
								+ "            \"entitled_quantity\": null\n" + "        },\n"
								+ "        \"prerequisite_to_entitlement_purchase\": {\n"
								+ "            \"prerequisite_amount\": null\n" + "        },\n"
								+ "        \"title\": \"" + priceName + "\"\n" + "    }\n" + "}";
						StringEntity params;
						try {
							params = new StringEntity(requestparam);
						} catch (UnsupportedEncodingException e) {
							throw new RuntimeException(e);
						}
						String createPriceModelRes = _helper.postShopifyRequest(
								"https://wagglesupplies.myshopify.com/admin/api/2023-04/price_rules.json",
								"application/json", params);
						JSONObject priceModelJson = new JSONObject(createPriceModelRes);
						JSONObject priceRuleArray = priceModelJson.getJSONObject("price_rule");
						Long priceruleId = priceRuleArray.getLong("id");

						// Creating Coupon Code
						String discountRequest = "{\"discount_code\":{\"code\":\"" + DiscountCode + "\"}}";
						StringEntity discountRequestparams;
						try {
							discountRequestparams = new StringEntity(discountRequest);
						} catch (UnsupportedEncodingException e) {
							throw new RuntimeException(e);
						}
						String saveDiscountRes = _helper
								.postShopifyRequest(
										"https://wagglesupplies.myshopify.com/admin/api/2023-04/price_rules/"
												+ priceruleId + "/discount_codes.json",
										"application/json", discountRequestparams);
						JSONObject saveDiscountJson = new JSONObject(saveDiscountRes);		
						JSONObject saveDiscountArray = saveDiscountJson.getJSONObject("discount_code");
						code = saveDiscountArray.getString("code");
					}

					if (benefitsObj.getMug_prcnt() > 0 && !customerId.isEmpty()) {
						// generating coupon for mug
						String MugDiscountCode = emailId.substring(0, 4).toUpperCase() + customerId.substring(0, 4)
								+ "FREE" + rand_int;
						String MugpriceName = "[AdditionalBenefits-Mug] " + MugDiscountCode;
						// String starttime ="";
						String mugProductId = "7123170132020";
						String Mugrequestparam = "{\n" + "    \"price_rule\": {\n"
								+ "        \"value_type\": \"fixed_amount\",\n" + "        \"value\": \"-"
								+ benefitsObj.getMug_prcnt() + "\",\n"
								+ "        \"customer_selection\": \"prerequisite\",\n"
								+ "        \"target_type\": \"line_item\",\n"
								+ "        \"target_selection\": \"entitled\",\n"
								+ "        \"allocation_method\": \"across\",\n" + "        \"allocation_limit\": 1,\n"
								+ "        \"once_per_customer\": true,\n" + "        \"usage_limit\": null,\n"
								+ "        \"starts_at\": \"" + currentTime + "\",\n" + "        \"ends_at\": \""
								+ dueDateConverted + "\",\n" + "        \"created_at\": \"" + currentTime + "\",\n"
								+ "        \"updated_at\": \"" + currentTime + "\",\n"
								+ "        \"entitled_product_ids\": [" + mugProductId + "],\n"
								+ "        \"entitled_variant_ids\": [],\n"
								+ "        \"entitled_collection_ids\": [],\n"
								+ "        \"entitled_country_ids\": [],\n"
								+ "        \"prerequisite_product_ids\": [],\n" + "        \"entitled_quantity\": 1,\n"
								+ "        \"prerequisite_variant_ids\": [],\n"
								+ "        \"prerequisite_collection_ids\": [],\n"
								+ "        \"customer_segment_prerequisite_ids\": [],\n"
								+ "        \"prerequisite_customer_ids\": [\n" + customerId + "            \n"
								+ "        ],\n" + "        \"prerequisite_subtotal_range\": null,\n"
								+ "        \"prerequisite_quantity_range\": null,\n"
								+ "        \"prerequisite_shipping_price_range\": null,\n"
								+ "        \"prerequisite_to_entitlement_quantity_ratio\": null,\n"
								+ "        \"prerequisite_to_entitlement_purchase\": {\n"
								+ "        \"prerequisite_amount\": null\n" + "  },\n" + "        \"title\": \""
								+ MugpriceName + "\"\n" + "    }\n" + "}";
						StringEntity Mugparams;
						try {
							Mugparams = new StringEntity(Mugrequestparam);
						} catch (UnsupportedEncodingException e) {
							throw new RuntimeException(e);
						}
						String createPriceModelMugRes = _helper.postShopifyRequest(
								"https://wagglesupplies.myshopify.com/admin/api/2023-04/price_rules.json",
								"application/json", Mugparams);
						JSONObject MugpriceModelJson = new JSONObject(createPriceModelMugRes);
						JSONObject MugpriceRuleArray = MugpriceModelJson.getJSONObject("price_rule");
						Long MugpriceruleId = MugpriceRuleArray.getLong("id");

						// Creating Coupon Code
						String MugdiscountRequest = "{\"discount_code\":{\"code\":\"" + MugDiscountCode + "\"}}";
						StringEntity MugdiscountRequestparams;
						try {
							MugdiscountRequestparams = new StringEntity(MugdiscountRequest);
						} catch (UnsupportedEncodingException e) {
							throw new RuntimeException(e);
						}
						String saveMugDiscountRes = _helper
								.postShopifyRequest(
										"https://wagglesupplies.myshopify.com/admin/api/2023-04/price_rules/"
												+ MugpriceruleId + "/discount_codes.json",
										"application/json", MugdiscountRequestparams);
						JSONObject saveMugDiscountJson = new JSONObject(saveMugDiscountRes);
						JSONObject saveMugDiscountArray = saveMugDiscountJson.getJSONObject("discount_code");
						mugcode = saveMugDiscountArray.getString("code");
					}

					if (benefitsObj.getCam_prcnt() > 0 && !customerId.isEmpty()) {
						Calendar calendar = Calendar.getInstance();
						Date d = calendar.getTime();
						SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
						DateTimeFormatter dueDateFormat = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss",
								Locale.ENGLISH);
						LocalDate duedateLoc = LocalDate.parse(dueDate, dueDateFormat);
						Date DueDateFromLocalDate = java.sql.Date.valueOf(duedateLoc);
						df.setTimeZone(TimeZone.getTimeZone("UTC"));
						currentTime = df.format(d);
						currentTime = currentTime + "-00:00";
						dueDateConverted = df.format(DueDateFromLocalDate);
						dueDateConverted = dueDateConverted + "+00:00";
						String DiscountCode = emailId.substring(0, 4).toUpperCase() + customerId.substring(0, 4) + "CAM"
								+ rand_int;
						String priceName = "[AdditionalBenefits-CAM] " + DiscountCode;
						String requestparam = "{\n" + "    \"price_rule\": {\n"
								+ "        \"value_type\": \"percentage\",\n" + "        \"value\": \"-"
								+ benefitsObj.getCam_prcnt() + "\",\n"
								+ "        \"customer_selection\": \"prerequisite\",\n"
								+ "        \"target_type\": \"line_item\",\n"
								+ "        \"target_selection\": \"all\",\n"
								+ "        \"allocation_method\": \"across\",\n"
								+ "        \"allocation_limit\": null,\n" + "        \"once_per_customer\": false,\n"
								+ "        \"usage_limit\": null,\n" + "        \"starts_at\": \"" + currentTime
								+ "\",\n" + "        \"ends_at\": \"" + dueDateConverted + "\",\n"
								+ "        \"created_at\": \"" + currentTime + "\",\n" + "        \"updated_at\": \""
								+ currentTime + "\",\n" + "        \"entitled_product_ids\": [],\n"
								+ "        \"entitled_variant_ids\": [],\n"
								+ "        \"entitled_collection_ids\": [],\n"
								+ "        \"entitled_country_ids\": [],\n"
								+ "        \"prerequisite_product_ids\": [],\n"
								+ "        \"prerequisite_variant_ids\": [],\n"
								+ "        \"prerequisite_collection_ids\": [],\n"
								+ "        \"customer_segment_prerequisite_ids\": [],\n"
								+ "        \"prerequisite_customer_ids\": [\n" + customerId + "            \n"
								+ "        ],\n" + "        \"prerequisite_subtotal_range\": null,\n"
								+ "        \"prerequisite_quantity_range\": null,\n"
								+ "        \"prerequisite_shipping_price_range\": null,\n"
								+ "        \"prerequisite_to_entitlement_quantity_ratio\": {\n"
								+ "            \"prerequisite_quantity\": null,\n"
								+ "            \"entitled_quantity\": null\n" + "        },\n"
								+ "        \"prerequisite_to_entitlement_purchase\": {\n"
								+ "            \"prerequisite_amount\": null\n" + "        },\n"
								+ "        \"title\": \"" + priceName + "\"\n" + "    }\n" + "}";
						StringEntity params;

						try {
							params = new StringEntity(requestparam);
						} catch (UnsupportedEncodingException e) {
							throw new RuntimeException(e);
						}
						String createPriceModelRes = _helper.postShopifyRequest(
								"https://wagglesupplies.myshopify.com/admin/api/2023-04/price_rules.json",
								"application/json", params);
						JSONObject priceModelJson = new JSONObject(createPriceModelRes);
						JSONObject priceRuleArray = priceModelJson.getJSONObject("price_rule");
						Long priceruleId = priceRuleArray.getLong("id");

						// Creating CamCoupon Code....

						String discountRequest = "{\"discount_code\":{\"code\":\"" + DiscountCode + "\"}}";
						StringEntity discountRequestparams;
						try {
							discountRequestparams = new StringEntity(discountRequest);
						} catch (UnsupportedEncodingException e) {
							throw new RuntimeException(e);
						}
						String saveDiscountRes = _helper
								.postShopifyRequest(
										"https://wagglesupplies.myshopify.com/admin/api/2023-04/price_rules/"
												+ priceruleId + "/discount_codes.json",
										"application/json", discountRequestparams);
						JSONObject saveDiscountJson = new JSONObject(saveDiscountRes);
						JSONObject saveDiscountArray = saveDiscountJson.getJSONObject("discount_code");
						waggleCamCode = saveDiscountArray.getString("code");

					}

					log.info("customerId:" + customerId + " C1:" + code + " C2:" + mugcode);
					if (!code.equalsIgnoreCase("NA") || !mugcode.equalsIgnoreCase("NA"))

						success = addCoupon(userId, emailId, code, mugcode, waggleCamCode, "NA", dueDate,createdFrom);

					if (success) {
						boolean avatar = false;
						boolean ebook = false;
						boolean merch = false;
						boolean mug = false;
						boolean cam = false;
						boolean ref = false;

						if (!code.equalsIgnoreCase("NA"))
							merch = true;
						if (!mugcode.equalsIgnoreCase("NA"))
							mug = true;
						if (creditSuccess && benefitsObj.getEbook_count() > 0)
							ebook = true;
						if (creditSuccess && benefitsObj.getCredits() > 0)
							avatar = true;
						if (creditSuccess && benefitsObj.getCam_prcnt() > 0)
							cam = true;
						if (creditSuccess && benefitsObj.getRef_prcnt() > 0)
							ref = true;

						response.put("Status", 1);
						response.put("mugcode", mugcode);
						response.put("couponCode", code);
						response.put("cam_code", waggleCamCode);
						response.put("create_from", createdFrom);
						response.put("ref_code", "NA");
						response.put("startDate", currentTime);
						response.put("endDate", dueDateConverted);

						response.put("showEbook", ebook);
						response.put("showAvatar", avatar);
						response.put("showMerchCoupon", merch);
						response.put("showMugCoupon", mug);
						response.put("showCamCoupon", cam);
						response.put("showRefCoupon", ref);
						response.put("mugUrl", "https://www.wagglemerch.com/products/white-glossy-mug");
						response.put("merchUrl", "https://www.wagglemerch.com/");
						response.put("ebookurl", "https://www.wagglemerch.com/collections/waggfluence-pet-ebooks");
						response.put("waggleCamUrl", "https://mywaggle.com/products/wagglecam?variant=44013801799921");
						response.put("referralUrl", "");

						response.put("couponContent1", benefitsObj != null ? benefitsObj.getContent1() : "NA");
						response.put("couponContent2", benefitsObj != null ? benefitsObj.getContent2() : "NA");
						response.put("couponContent3", benefitsObj != null ? benefitsObj.getContent3() : "NA");
						response.put("couponContent4", benefitsObj != null ? benefitsObj.getContent4() : "NA");
					} else {
						response.put("Status", 0);
						response.put("Msg", "Can't add coupon");
						response.put("Error", "Coupon has been created, but unable to store it in the database.");
					}
				} else {
					response.put("Status", 0);
					response.put("Msg", "Can't add credit");
				}
			}

		} catch (Exception e) {
			// e.printStackTrace();
			log.error("Error while parsing coupon content : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Can't add credit");
		}
		return response;
	}
	
	@Override
	public JResponse generateSubCoupon(JCouponData couponObj, String periodName) {
		JResponse response = new JResponse();
		String currentTime = "";
		String dueDateConverted = "";

		String emailId = couponObj.getEmailId();
		String periodid = couponObj.getPeriod();
		long userId = couponObj.getUserid();
		String firstName = couponObj.getFirstName();
		String lastName = couponObj.getLastName();
		String dueDate = couponObj.getDueDate();

		try {
//			HashMap<String, String> periodlist = new HashMap<String, String>();
//			periodlist.put("1", "Monthly");
//			periodlist.put("2", "Quarterly");
//			periodlist.put("3", "Half-Yearly");
//			periodlist.put("4", "Yearly");
//			periodlist.put("5", "2-Year");
//			periodlist.put("6", "5-Year");

//			JAddBenefits benefitsObj = getCouponContent(userId, emailId, periodName);
//
//			RewardCredit rewardObj = checkCreditByPeriod(emailId, Integer.parseInt(periodid));

//				Boolean creditSuccess = addCredit(userId, emailId, period);

//				if (creditSuccess && benefitsObj != null) {
					String customerId = "";

//					if (benefitsObj.getCoupon_prcnt() > 0 || benefitsObj.getMug_prcnt() > 0
//							|| benefitsObj.getCam_prcnt() > 0 || benefitsObj.getRef_prcnt() > 0) {
						// Get customerid from shopify using mailid
						String apiResponse = _helper.getShopifyUrl(
								"https://wagglesupplies.myshopify.com/admin/api/2023-04/customers/search.json?query=email:"
										+ emailId);
						JSONObject apiResJson = new JSONObject(apiResponse);
						if (apiResJson.has("customers")) {
							JSONArray resultlist = apiResJson.getJSONArray("customers");

							if (resultlist.length() > 0) {
								customerId = resultlist.getJSONObject(0).get("id").toString();
							} else {
								String requestparam = "{\n" + "    \"customer\": {\n" + "        \"first_name\": \""
										+ firstName + "\",\n" + "        \"last_name\": \"" + lastName + "\",\n"
										+ "        \"email\": \"" + emailId + "\",\n"
										+ "        \"verified_email\": true,\n" + "      \n"
										+ "        \"send_email_invite\": true\n" + "    }\n" + "}";
								StringEntity params;
								try {
									params = new StringEntity(requestparam);
								} catch (UnsupportedEncodingException e) {
									throw new RuntimeException(e);
								}
								String createCustomerResponse = _helper.postShopifyRequest(
										"https://wagglesupplies.myshopify.com/admin/api/2023-04/customers.json",
										"application/json", params);
								JSONObject createCustomerResJson = new JSONObject(createCustomerResponse);
								if (createCustomerResJson.has("customer")) {
									JSONObject customerdata = createCustomerResJson.getJSONObject("customer");
									Long id = customerdata.getLong("id");
									customerId = id.toString();
								} else {
									log.info("2-create Customer Resp: " + emailId + " :"
											+ createCustomerResJson.toString());
//							 	Get customerid from shopify using mailid
									Thread.sleep(1000);
									apiResponse = _helper.getShopifyUrl(
											"https://wagglesupplies.myshopify.com/admin/api/2023-04/customers/search.json?query=email:"
													+ emailId);
									apiResJson = new JSONObject(apiResponse);
									log.info("3-Get Customer Resp: " + emailId + " :" + apiResJson.toString());
									if (apiResJson.has("customers")) {
										resultlist = apiResJson.getJSONArray("customers");

										if (resultlist.length() > 0) {
											customerId = resultlist.getJSONObject(0).get("id").toString();
											log.info("2-got Customer id: " + customerId);
										}
									}
								}
							}
						}

//					}
					Random rand = new Random();
					int rand_int = rand.nextInt(1000);
					Boolean success = true;
					String mugcode = "NA";
					String code = "NA";

					if (!customerId.isEmpty()) {
						// Creating Price Rule
						Calendar calendar = Calendar.getInstance();
						Date d = calendar.getTime();
						SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
						DateTimeFormatter dueDateFormat = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss",
								Locale.ENGLISH);
						LocalDate duedateLoc = LocalDate.parse(dueDate, dueDateFormat);
						Date DueDateFromLocalDate = java.sql.Date.valueOf(duedateLoc);
						df.setTimeZone(TimeZone.getTimeZone("UTC"));
						currentTime = df.format(d);
						currentTime = currentTime + "-00:00";
						dueDateConverted = df.format(DueDateFromLocalDate);
						dueDateConverted = dueDateConverted + "+00:00";
						String DiscountCode = emailId.substring(0, 4).toUpperCase() + customerId.substring(0, 4)
								+ rand_int;
						String priceName = "[SubscriptionCoupon] " + DiscountCode;

						String requestparam = "{\n" + "    \"price_rule\": {\n"
								+ "        \"value_type\": \"percentage\",\n" + "        \"value\": \"-"
								+ cancelCouponPercent + "\",\n"
								+ "        \"customer_selection\": \"prerequisite\",\n"
								+ "        \"target_type\": \"line_item\",\n"
								+ "        \"target_selection\": \"all\",\n"
								+ "        \"allocation_method\": \"across\",\n"
								+ "        \"allocation_limit\": null,\n" + "        \"once_per_customer\": false,\n"
								+ "        \"usage_limit\": null,\n" + "        \"starts_at\": \"" + currentTime
								+ "\",\n" + "        \"ends_at\": \"" + dueDateConverted + "\",\n"
								+ "        \"created_at\": \"" + currentTime + "\",\n" + "        \"updated_at\": \""
								+ currentTime + "\",\n" + "        \"entitled_product_ids\": [],\n"
								+ "        \"entitled_variant_ids\": [],\n"
								+ "        \"entitled_collection_ids\": [],\n"
								+ "        \"entitled_country_ids\": [],\n"
								+ "        \"prerequisite_product_ids\": [],\n"
								+ "        \"prerequisite_variant_ids\": [],\n"
								+ "        \"prerequisite_collection_ids\": [],\n"
								+ "        \"customer_segment_prerequisite_ids\": [],\n"
								+ "        \"prerequisite_customer_ids\": [\n" + customerId + "            \n"
								+ "        ],\n" + "        \"prerequisite_subtotal_range\": null,\n"
								+ "        \"prerequisite_quantity_range\": null,\n"
								+ "        \"prerequisite_shipping_price_range\": null,\n"
								+ "        \"prerequisite_to_entitlement_quantity_ratio\": {\n"
								+ "            \"prerequisite_quantity\": null,\n"
								+ "            \"entitled_quantity\": null\n" + "        },\n"
								+ "        \"prerequisite_to_entitlement_purchase\": {\n"
								+ "            \"prerequisite_amount\": null\n" + "        },\n"
								+ "        \"title\": \"" + priceName + "\"\n" + "    }\n" + "}";
						StringEntity params;
						try {
							params = new StringEntity(requestparam);
						} catch (UnsupportedEncodingException e) {
							throw new RuntimeException(e);
						}
						String createPriceModelRes = _helper.postShopifyRequest(
								"https://wagglesupplies.myshopify.com/admin/api/2023-04/price_rules.json",
								"application/json", params);
						JSONObject priceModelJson = new JSONObject(createPriceModelRes);
						JSONObject priceRuleArray = priceModelJson.getJSONObject("price_rule");
						Long priceruleId = priceRuleArray.getLong("id");

						// Creating Coupon Code
						String discountRequest = "{\"discount_code\":{\"code\":\"" + DiscountCode + "\"}}";
						StringEntity discountRequestparams;
						try {
							discountRequestparams = new StringEntity(discountRequest);
						} catch (UnsupportedEncodingException e) {
							throw new RuntimeException(e);
						}
						String saveDiscountRes = _helper
								.postShopifyRequest(
										"https://wagglesupplies.myshopify.com/admin/api/2023-04/price_rules/"
												+ priceruleId + "/discount_codes.json",
										"application/json", discountRequestparams);
						JSONObject saveDiscountJson = new JSONObject(saveDiscountRes);
						JSONObject saveDiscountArray = saveDiscountJson.getJSONObject("discount_code");
						code = saveDiscountArray.getString("code");
					}

					log.info("customerId:" + customerId + " couponcode :" + code);
					if (!code.equalsIgnoreCase("NA") || !mugcode.equalsIgnoreCase("NA"))

//						success = addCoupon(userId, emailId, code, mugcode, waggleCamCode, "NA", dueDate,createdFrom);

					if (success) {
						response.put("Status", 1);
						response.put("couponCode", code);
						response.put("startDate", currentTime);
						response.put("endDate", dueDateConverted);
					} else {
						response.put("Status", 0);
						response.put("Msg", "Can't add coupon");
						response.put("Error", "Coupon has been created, but unable to store it in the database.");
					}
//				} else {
//					response.put("Status", 0);
//					response.put("Msg", "Can't add credit");
//				}

		} catch (Exception e) {
			log.error("Error while parsing coupon content : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Can't add credit");
		}
		return response;
	}

	public boolean checkBenefitsExpired(long userId) {
		return iAvatarDao.checkBenefitsExpired(userId);
	}

	@Override
	public boolean checkBenefitsAvail(String periodname) {
		return iAvatarDao.checkBenefitsAvail(periodname);
	}

	@Override
	public RewardCredit checkCreditByPeriod(String emailId, int period_id) {
		return iAvatarDao.checkCreditByPeriod(emailId, period_id);
	}
}
