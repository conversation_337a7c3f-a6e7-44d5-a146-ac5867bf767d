package com.nimble.irisservices.service.impl;

import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IAlertDaoV4;
import com.nimble.irisservices.dto.JAlertCount;
import com.nimble.irisservices.entity.AlertWC;
import com.nimble.irisservices.service.IAlertServiceV4;

@Service
@Transactional
public class AlertServiceImplV4 implements IAlertServiceV4 {

	@Autowired
	IAlertDaoV4 alertdao;

	@Override
	public JAlertCount getAlertCnt(){
		return alertdao.getAlertCnt();
	}
	
	@Override
	public int getUnAckAlertCount(long cmp_id) {
		return alertdao.getUnAckAlertCount(cmp_id);
	}

	@Override
	public ArrayList<AlertWC> getAlertsWC(long gateway_id, long user_id) {
		return alertdao.getAlertsWC(gateway_id, user_id);
	}

	@Override
	public AlertWC getAlertsWC(long alert_id) {
		return alertdao.getAlertsWC(alert_id);
	}


}
