package com.nimble.irisservices.service.impl;




import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IUserDao;
import com.nimble.irisservices.dao.IZipCodeDetailsDao;
import com.nimble.irisservices.entity.ZipCodeDetails;
import com.nimble.irisservices.service.IZipCodeDetailsService;



@Service
@Transactional
public class ZipCodeDetailsServiceImpl implements IZipCodeDetailsService{

	@Autowired
	IUserDao userDao;
	
	@Autowired
	IZipCodeDetailsDao iZipCodeDetailsDao;
	
	@Override
	public List<ZipCodeDetails> getZipCodeDetails(String zipCode, String countryCode) {
		// TODO Auto-generated method stub
		return iZipCodeDetailsDao.getZipCodeDetails(zipCode, countryCode);
	}

	@Override
	public boolean saveZipCode(ZipCodeDetails zipCodeDetails) {
		// TODO Auto-generated method stub
		return iZipCodeDetailsDao.saveZipCode(zipCodeDetails);
	}

	
	
}
