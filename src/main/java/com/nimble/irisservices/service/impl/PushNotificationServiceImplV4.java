package com.nimble.irisservices.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.dao.IPushNotificationDaoV4;
import com.nimble.irisservices.entity.PushNotificationStatus;
import com.nimble.irisservices.pojo.SendNotifications;
import com.nimble.irisservices.service.IPushNotificationServiceV4;

@Service
@Transactional
public class PushNotificationServiceImplV4 implements IPushNotificationServiceV4{
	

	@Autowired
	IPushNotificationDaoV4 pushNotificationDaoV4;
	
	
	@Override
	public List<SendNotifications> userNotificationsV4(String userId, String status, int monitor_type_id, boolean is_device_based) {
		return pushNotificationDaoV4.userNotificationsV4(userId, status, monitor_type_id, is_device_based);
	}
	
	@Override
	public int getNotViewedNotificationCount(long userid) {
		return pushNotificationDaoV4.getNotViewedNotificationCount(userid);
	}

	@Override
	public boolean updateNotificationStatus(long userId, ArrayList<Long> notificationIds) {
		return pushNotificationDaoV4.updateNotificationStatus(userId, notificationIds);
	}
	
	@Override
	public PushNotificationStatus getPushNotificationStatus(long push_notification_status_id) {
		return pushNotificationDaoV4.getPushNotificationStatus(push_notification_status_id);
	}
}
