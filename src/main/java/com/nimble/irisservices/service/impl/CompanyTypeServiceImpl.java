package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.ICompanyTypeDao;
import com.nimble.irisservices.entity.CompanyType;
import com.nimble.irisservices.service.ICompanyTypeService;
@Service
public class CompanyTypeServiceImpl implements ICompanyTypeService{

	@Autowired
	ICompanyTypeDao companyTypeDao;

	@Transactional
	public List<CompanyType> getCompanyType(String cmptypeid, long roleid) {
		// TODO Auto-generated method stub
		return companyTypeDao.getCompanyType(cmptypeid, roleid);
	}
	
}
