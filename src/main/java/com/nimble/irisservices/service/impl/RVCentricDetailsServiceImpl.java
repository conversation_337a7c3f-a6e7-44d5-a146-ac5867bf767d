package com.nimble.irisservices.service.impl;

import javax.transaction.Transactional;

import org.springframework.stereotype.Service;

import com.nimble.irisservices.service.IRVCentricDetailsService;

@Service
@Transactional
public class RVCentricDetailsServiceImpl implements IRVCentricDetailsService{
//	private static final Logger log = LogManager.getLogger(RVCentricDetailsServiceImpl.class);
//
//	@Autowired
//	IRVCentricDetailsDao rvDao;
//
//	@Autowired
//	IExternalConfigService externalConfigService;
//
//	Helper _helper = new Helper();
//	
//	@Autowired
//	@Lazy
//	private IAsyncService async;
//
//	@Value("${chargebee.site.name}")
//	private String chargebeeSiteName;
//
//	@Value("${chargebee.site.key}")
//	private String chargebeeSiteKey;
//	
//	@Value("${freeplan}")
//	private String freeplan;
//	
//	@Value("${omitplan}")
//	private String omitplan;
//	
//	@Value("${vpmplan}")
//	private String vpmplan;
//	
//	@Value("${mapboxtoken}")
//	private String mapboxtoken;
//
//	@Autowired
//	@Lazy
//	ICreditSystemService crService;
//	
//	@Override
//	public List<RVCentricDetails> listRVCentricDetailsList(){
//		return rvDao.listRVCentricDetailsList();	
//	}
//	
//	@Override
//	public List<RVCentricDetails> listAllRVCentricDetailsList(){
//		return rvDao.listAllRVCentricDetailsList();	
//	}
//	
//	@Override
//	public boolean saveOrUpdateRVCentricDetails(RVCentricDetails rvObj) {
//		return rvDao.saveOrUpdateRVCentricDetails(rvObj);
//	}
//
//	@Override
//	public RVCentricDetails getRVCentricDetailsById(long id) {
//		return rvDao.getRVCentricDetailsById(id);
//	}
//
//	@Override
//	public List<JRVChecklist> listRVCheckList(long userid) {
//		return rvDao.listRVCheckList(userid);
//	}
//
//	@Override
//	public List<RVChecklist> listAllRVCheckList() {
//		return rvDao.listAllRVCheckList();
//	}
//
//	@Override
//	public int saveOrUpdateRVChecklist(RVChecklist rvObj) throws SQLIntegrityConstraintViolationException {
//		return rvDao.saveOrUpdateRVChecklist(rvObj);
//	}
//	
//	@Override
//	public boolean updateRVCheckList(long userId,ArrayList<Long> checklistIds) {
//		return rvDao.updateRVCheckList(userId, checklistIds);
//	}
//
//	@Override
//	public boolean deleteUserCheckList(long userId, ArrayList<Long> checklistIds) {
//		return rvDao.deleteUserCheckList(userId, checklistIds);
//	}
//	
//	@Override
//	public List<RVChecklistType> listRVChecklistType(){
//		return rvDao.listRVChecklistType();
//	}
//
//	@Override
//	public List<Badges> listAllBadges(){
//		return rvDao.listAllBadges();
//	}
//	
//	@Override
//	public List<JBadges> listBadges(long userid){
//		return rvDao.listBadges(userid);
//	}
//	
//	@Override
//	public ArrayList<JUserBadges> getUserBadges(long userid,String api_ver){
//		return rvDao.getUserBadges(userid,api_ver);
//	}
//	
//	@Override
//	public ArrayList<RVAnswer> listRVAnswer(){
//		return rvDao.listRVAnswer();
//	}
//	
//	@Override
//	public ArrayList<RVAnswer> listRVAnswerByQuest(long questId){
//		return rvDao.listRVAnswerByQuest(questId);
//	}
//	
//	@Override
//	public boolean saveUserBadgeTxn(long userId,String plan_id,int device_cnt, boolean updatedRVProfile,String cbId) {
//		boolean stat1 = false;
//		boolean stat2 = false;				
//		boolean stat3 = false;
//		boolean stat4 = false;
//		boolean stat5 = false;
//		boolean stat6 = false;
//		boolean stat7 = false;
//		boolean stat8 = false;
//		boolean stat9 = false;
//		boolean stat10 = false;
//		boolean stat11 = false;
//		boolean stat12 = false;
//		int trip_count = 0;
//		try {
//			HashSet<Long> stageList =rvDao.checkUserBadgeExist(userId);
//			
//			stat1 = stageList.contains(1L);
//			stat2 = stageList.contains(2L);				
//			stat3 = stageList.contains(3L);
//			stat4 = stageList.contains(4L);
//			stat5 = stageList.contains(5L);
//			stat6 = stageList.contains(6L);
//			stat7 = stageList.contains(7L);
//			stat8 = stageList.contains(8L);
//			stat9 =  stageList.contains(9L);
//			stat10 = stageList.contains(10L);
//			stat11 = stageList.contains(11L);
//			stat12 = stageList.contains(12L);
//			
//			if(!stat1) {
//				stat1 = rvDao.saveUserBadgeTxn(userId, 1, 1);
//			}
//			
//			if(stat1 && updatedRVProfile) {
//				
//				if(!stat2 && !stat3) {
//					stat2 = rvDao.saveUserBadgeTxn(userId, 1, 2);
//					stat3 = rvDao.saveUserBadgeTxn(userId,1, 3);
//					
//					if (stat3) {
//						String notificationMsg = "You've achieved the badge. You're an RVer now!";
//						String notificationType = "Badges";
//						String notificationTitle = "Badges: RVer";
//						async.sendPushNotification(userId + "", notificationTitle, notificationMsg, 0l,
//								notificationType);
//	
//						log.info("Sending Badge1 Notification:" + userId+ ": "+notificationMsg);
//					}
//
//				}
//				
//				if(stat2 && stat3 && device_cnt>0) {
//					
//					if(!stat4 && device_cnt >0) {
//						stat4 = rvDao.saveUserBadgeTxn(userId,2, 4);
//						
//						if(stat4 && !cbId.equalsIgnoreCase("NA")&& ( plan_id.equalsIgnoreCase("NA") || plan_id.isEmpty()) ) {
//							Environment.configure(chargebeeSiteName, chargebeeSiteKey);
//
//							ListResult result = com.chargebee.models.Subscription.list().customerId().is(cbId).status()
//									.in(Status.ACTIVE,Status.NON_RENEWING,Status.IN_TRIAL).sortByUpdatedAt(SortOrder.DESC).request();
//							
//							com.chargebee.models.Subscription subscrip = null;
//							boolean isPaidPlan=false;
//							
//							if(!result.isEmpty()) {
////								String freeplan = _helper.getExternalConfigValue("freeplan", externalConfigService);
////								String omitplan = _helper.getExternalConfigValue("omitplan", externalConfigService);
////								String vpmplan = _helper.getExternalConfigValue("vpmplan", externalConfigService);
//								
//								for (ListResult.Entry subs : result) {
//									if (!freeplan.contains(subs.subscription().planId())
//											&& !omitplan.contains(subs.subscription().planId())
//											&& !vpmplan.contains(subs.subscription().planId())) {
//										subscrip = subs.subscription();
//										plan_id = subscrip.planId();
//										isPaidPlan = true;
//										break;
//									}
//								}
//								
//								if(isPaidPlan) {
//									if(!stat5)
//										stat5 = rvDao.saveUserBadgeTxn(userId, 2, 5);
//									
//									if(!stat6) {
//										stat6 = rvDao.saveUserBadgeTxn(userId, 2, 6);
//									
//										if (stat6) {
//											String notificationMsg = "You've achieved the badge. You're a Explorer now!";
//											String notificationType = "Badges";
//											String notificationTitle = "Badges: Explorer";
//											
//											async.sendPushNotification(userId + "", notificationTitle, notificationMsg, 0l,
//													notificationType);
//						
//											log.info("Sending Badge2 Notification:" + userId+ " : "+notificationMsg);
//										}
//									}
//									
//									
//									log.info("user:"+userId+" RVerBadges1:"+stat1+"Badges2:"+stat2+"Badges3:"+stat3
//											+"Badges4:"+stat4+"Badges5:"+stat5+"Badges6:"+stat6);
//
//								}								
//							}
//						}
//					}
//						
//					
//					if(stat4 && (! plan_id.equalsIgnoreCase("chum")
//							&& ! plan_id.equalsIgnoreCase("NA") 	&& ! plan_id.isEmpty()) ) {
//						if(!stat5)
//							stat5 = rvDao.saveUserBadgeTxn(userId, 2, 5);
//						
//						if(!stat6) {
//							stat6 = rvDao.saveUserBadgeTxn(userId, 2, 6);
//							if (stat6) {
//								String notificationMsg = "You've achieved the badge. You're a Explorer now!";
//								String notificationType = "Badges";
//								String notificationTitle = "Badges: Explorer";
//								
//								async.sendPushNotification(userId + "", notificationTitle, notificationMsg, 0l,
//										notificationType);
//			
//								log.info("Sending Badge2 Notification:" + userId+ " : "+notificationMsg);
//							}
//						}
//						log.info("user:"+userId+" RVerBadges1:"+stat1+"Badges2:"+stat2+"Badges3:"+stat3
//								+"Badges4:"+stat4+"Badges5:"+stat5+"Badges6:"+stat6);
//
//					}									
//					int period_id = 1;
//					
//					if (stat6 && !stat7) {
//						ArrayList<Integer> ids = crService.getPlanAndPeriod(plan_id);
//						if (!ids.isEmpty()) {
//							period_id = ids.get(1);
//						}
//					}
//					
//					if(stat6 && !stat7 && (period_id==4 || period_id==5 || period_id==6)) {
//						stat7 = rvDao.saveUserBadgeTxn(userId, 3, 7);
//						if(stat7)
//							trip_count = rvDao.getTripCount(userId);
//					}
//					
//					if(stat7 && !stat8 && (trip_count>=5)) {
//						stat8 = rvDao.saveUserBadgeTxn(userId, 3, 8);
//						stat9 = rvDao.saveUserBadgeTxn(userId, 3, 9);
//						if (stat9) {
//							String notificationMsg = "You've achieved the badge. You're a Adventurer now!";
//							String notificationType = "Badges";
//							String notificationTitle = "Badges: Adventurer";
//							async.sendPushNotification(userId + "", notificationTitle, notificationMsg, 0l,
//									notificationType);
//		
//							log.info("Sending Badge3 Notification:" + userId+ " : "+notificationMsg);
//						}
//					}					
//					
//					if ((stat9 && !stat10 && period_id==1)) {
//						ArrayList<Integer> ids = crService.getPlanAndPeriod(plan_id);
//						if (!ids.isEmpty()) {
//							period_id = ids.get(1);
//						}
//					}
//					if(stat9 && !stat10 && (period_id==5|| period_id==6)) {
//						stat10 = rvDao.saveUserBadgeTxn(userId, 4, 10);	
//						
//						if(stat10 && trip_count==0)
//							trip_count = rvDao.getTripCount(userId);
//						
//						if(stat10 && !stat11 && (trip_count>=10)) {
//							stat11 = rvDao.saveUserBadgeTxn(userId, 4, 11);
//							stat12 = rvDao.saveUserBadgeTxn(userId, 4, 12);
//							
//							if (stat12) {
//								String notificationMsg = "You've achieved all the badges. You're a legendary Nomad!";
//								String notificationType = "Badges";
//								String notificationTitle = "Badges: Nomad";
//								async.sendPushNotification(userId + "", notificationTitle, notificationMsg, 0l,
//										notificationType);
//			
//								log.info("Sending Badge4 Notification:" + userId+ " : "+notificationMsg);
//							}
//						}					
//					}
//					else {
//						log.info("user:"+userId+" RVerBadges1:"+stat1+"Badges2:"+stat2+"Badges3:"+stat3
//								+"Badges4:"+stat4);
//					}
//				}else {
//					log.info("user:"+userId+" RVerBadges1:"+stat1+"Badges2:"+stat2+"Badges3:"+stat3);
//				}
//			}
//			else {
//				log.info("user:"+userId+" RVerBadges1:"+stat1+" updatedRVProfile:"+updatedRVProfile);
//			}
//			
//			return true;
//
//		}catch (Exception e) {
//			log.error("Exception in saveUserBadgeTxn : "+e.getLocalizedMessage());
//			return false;
//		}
//	}
//	
//	@Override
//	public HashSet<Long> checkUserBadgeExist(long userid) {
//		return rvDao.checkUserBadgeExist(userid);
//	}
//
//	@Override
//	public JUserLifetimeTrip getUserLifetimeTrip(long userid) {
//		return rvDao.getUserLifetimeTrip(userid);
//	}
//
//	@Override
//	public RvTripHistory saveUserTripHistory(RvTripHistory rvTripHistory) {
//		return rvDao.saveUserTripHistory(rvTripHistory);
//	}	
//
//
//	@Override
//	public boolean endUserTrip(long userid, String trip_id, String plan_id) {
//		return rvDao.endUserTrip(userid,trip_id, plan_id);
//	}
//
//	@Override
//	public JSONObject toParseJson(String lonlat) {
//
//		InputStream is = null;
//		JSONObject jObj = null;
//		String json = "";
//
//	    try {
//	        // defaultHttpClient
//	    	String url = "https://api.mapbox.com/geocoding/v5/mapbox.places/"+lonlat+".json?access_token="+mapboxtoken; 
//	        DefaultHttpClient httpClient = new DefaultHttpClient();
//	        HttpGet httpPost = new HttpGet(url);
//
//	        HttpResponse httpResponse = httpClient.execute(httpPost);
//	        HttpEntity httpEntity = httpResponse.getEntity();
//	        is = httpEntity.getContent();
//
//	    } catch (UnsupportedEncodingException e) {
//	        e.printStackTrace();
//	    } catch (ClientProtocolException e) {
//	        e.printStackTrace();
//	    } catch (IOException e) {
//	        e.printStackTrace();
//	    }
//
//	    try {
//	        BufferedReader reader = new BufferedReader(new InputStreamReader(
//	                is, "iso-8859-1"), 8);
//	        StringBuilder sb = new StringBuilder();
//	        String line = null;
//	        while ((line = reader.readLine()) != null) {
//	            sb.append(line + "\n");
//	            //System.out.println(line);
//	        }
//	        is.close();
//	        json = sb.toString();
//
//	    } catch (Exception e) {
//	        System.out.println("Buffer Error Error converting result " + e.toString());
//	    }
//
//	    // try parse the string to a JSON object
//	    try {
//	        jObj = new JSONObject(json);
//	    } catch (JSONException e) {
//	        System.out.println("error on parse data in jsonparser.java");
//	    }
//
//	    // return JSON String
//	    return jObj;
//	}
//	
//	@Override
//	public int getTripCount(long userid) {
//		return rvDao.getTripCount(userid);
//	}
//	
//	@Override
//	public boolean saveUserBadgeTxn(long userId,long badge_id,long stage_id) {
//		return rvDao.saveUserBadgeTxn(userId, badge_id, stage_id);
//	}
}
