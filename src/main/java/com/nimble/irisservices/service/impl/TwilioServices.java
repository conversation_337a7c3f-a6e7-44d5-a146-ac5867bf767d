package com.nimble.irisservices.service.impl;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nimble.irisservices.service.SMSInterface;
import com.twilio.Twilio;
import com.twilio.exception.ApiException;
import com.twilio.http.HttpMethod;
import com.twilio.rest.api.v2010.account.Call;
import com.twilio.rest.api.v2010.account.Call.Status;
import com.twilio.rest.api.v2010.account.Message;
import com.twilio.type.PhoneNumber;
import org.springframework.stereotype.Service;

@Service("twilio")
public class TwilioServices implements SMSInterface{

	public static final String ACCOUNT_SID =
            "**********************************";
    public static final String AUTH_TOKEN =
            "ff0a565f683c145f5bf93d19566424d6";
    
	private static final Logger log = LogManager.getLogger(Twilio.class);
	private String twilioPhNo;
	
	public TwilioServices(){
		Twilio.init(ACCOUNT_SID, AUTH_TOKEN);
		twilioPhNo = "+***********";
	}
	
	@Override
	public boolean SendMessage(String phoneNo, String msg, String cmpid, String cmpname, String appname,
			String srcPhNo,boolean enablePowerBack,String powerBackUUID) {
		
		
		boolean status = false;
		String[] phList = phoneNo.split(",");		
		
//		if(!phoneNo.contains("+"))
//			phoneNo="+"+phoneNo;
		try{
//			Message message = Message.creator(new PhoneNumber(phoneNo), // to
//		            new PhoneNumber(twilioPhNo), // from
//		         //  "MGd7bf2ce6a28698c875dcf71d537bea2e",
//					msg).create();
//			if(message.getErrorCode() != null)
//				log.info("sendSMS: Message delivery status = "+message.getStatus()+"Error msg= "+
//						message.getErrorMessage());		
//			else
//				status=true;
			
			for(String ph : phList) {
				if(!ph.contains("+"))
					ph="+"+ph;
				Message message = Message.creator(new PhoneNumber(ph), // to
			            new PhoneNumber(twilioPhNo), // from
			         //  "MGd7bf2ce6a28698c875dcf71d537bea2e",
						msg).create();
				if(message.getErrorCode() != null)
					log.info("sendSMS: Message delivery status = "+message.getStatus()+"Error msg= "+
							message.getErrorMessage());		
				else
					status=true;
			}	
		}catch(ApiException e){
			
			System.out.println("SendMessage: Exception: "+e.getLocalizedMessage());
		}
		
		return status;
	}

	@Override
	public boolean SendVoiceMessage(String phoneNo, String message, String cmpid, String cmpname, String appname,
			String ip,String srcphno) {
		boolean status = false;
		if(!phoneNo.contains("+"))
			phoneNo="+"+phoneNo;
		try {
			//String message = "Hello I am Twilio!";
			String msg_enc  = URLEncoder.encode(message.trim(),"UTF-8");
			String URL = "http://"+ip+":8080/irisservices/v3.0/twilio_texttoSpeech?msg="+msg_enc;
			/*Call call = Call.creator(new PhoneNumber("+919489835157"), new PhoneNumber("+***********"),
			        new URI("http://demo.twilio.com/docs/voice.xml")).create();*/
						
			Call call = Call.creator(new PhoneNumber(phoneNo), new PhoneNumber(twilioPhNo),
			        new URI(URL)).setMethod(HttpMethod.GET).create();
			if(call.getStatus().equals(Status.FAILED))
				log.info("SendVoiceMessage: Call failed .May be due to wrong number");				
			else
				status = true;
		} catch (URISyntaxException e) {
			
			log.info("SendVoiceMessage:URISyntaxException: "+e.getLocalizedMessage());
		} catch (UnsupportedEncodingException e) {
			
			log.info("SendVoiceMessage:UnsupportedEncodingException: "+e.getLocalizedMessage());
		}catch(ApiException e){
			
			System.out.println("SendMessage: ApiException: "+e.getLocalizedMessage());
		}
		return status;
	}

	@Override
	public boolean SendEmail(String emailds, String subject, String content) {
		log.info("SendEmail is not implemented");
		return false;
	}

	@Override
	public boolean SendEmail_SES(String from_email, String emailds, String subject, String content, String filename, boolean isRVMailId) {
		log.info("SendEmail_SES is not implemented");
		return false;
	}

	@Override
	public boolean SendEmail_SESV2(String alert_email, String emailds, String subject, String content, String filename,
			boolean isRVMailId) {
		return false;
	}
}


