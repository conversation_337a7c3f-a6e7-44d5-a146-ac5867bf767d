package com.nimble.irisservices.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.nimble.irisservices.service.IS3Service;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;

@Service
public class S3ServiceImpl implements IS3Service {

    private static final Logger log = LogManager.getLogger(S3ServiceImpl.class);

    private final AmazonS3 s3Client;

    @Value("${ble_fota_debug_s3_path_prefix}")
    private String bleFotaDebugS3PathPrefix;

    @Value("${ble_fota_debug_s3_bucket_name}")
    private String bleFotaDebugS3BucketName;

    public S3ServiceImpl(AmazonS3 s3Client) {
        this.s3Client = s3Client;
    }

    @Override
    public boolean uploadFileToS3Bucket(String content, String fileName) {

        log.info("Entered into uploadFileToS3Bucket :: fileName : {}", fileName);
        File tempFile = null;

        try {
            tempFile = File.createTempFile("upload-", ".txt");

            try (FileWriter writer = new FileWriter(tempFile)) {
                writer.write(content);
            }

            String s3Key = bleFotaDebugS3PathPrefix.endsWith("/")
                    ? bleFotaDebugS3PathPrefix + fileName
                    : bleFotaDebugS3PathPrefix + "/" + fileName;

            PutObjectRequest request = new PutObjectRequest(
                    bleFotaDebugS3BucketName,
                    s3Key,
                    tempFile
            ).withCannedAcl(CannedAccessControlList.PublicRead);

            s3Client.putObject(request);

            return true;
        } catch (Exception e) {
            log.error("Exception occured at uploadFileToS3Bucket : {}", e.getLocalizedMessage());
            return false;
        } finally {
            if (tempFile != null && tempFile.exists()) {
                boolean deleteStatus = tempFile.delete();
                if (!deleteStatus) {
                    log.error("Failed to delete temp file : {}", tempFile.getAbsolutePath());
                }
            }
        }
    }
}
