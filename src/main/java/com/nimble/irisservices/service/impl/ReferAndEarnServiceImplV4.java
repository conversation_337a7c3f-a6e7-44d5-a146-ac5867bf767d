package com.nimble.irisservices.service.impl;

import com.nimble.irisservices.dto.InAppReferReason;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IReferAndEarnDaoV4;
import com.nimble.irisservices.entity.AppImage;
import com.nimble.irisservices.entity.ReferralCredits;
import com.nimble.irisservices.service.IReferAndEarnServiceV4;

import java.util.List;

@Service
@Transactional
public class ReferAndEarnServiceImplV4 implements IReferAndEarnServiceV4
{
	
	@Autowired
	IReferAndEarnDaoV4 referAndEarnDaoV4;

	@Override
	public ReferralCredits getLatestReferralCredits() {
		return referAndEarnDaoV4.getLatestReferralCredits();
	}

	@Override
	public AppImage getAppImages(String type, String img_name){
		return referAndEarnDaoV4.getAppImages(type, img_name);
	}

	@Override
	public List<InAppReferReason> getNotInterestedReasons() {

		return referAndEarnDaoV4.getNotInterestedReasons();
	}

	@Override
	public boolean userIsEligible(long userId) {

		return referAndEarnDaoV4.userIsEligible(userId);
	}
}
