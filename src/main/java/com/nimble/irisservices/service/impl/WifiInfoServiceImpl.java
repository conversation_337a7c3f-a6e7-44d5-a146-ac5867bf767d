package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IWifiInfoDao;
import com.nimble.irisservices.dto.BluetoothDeviceList;
import com.nimble.irisservices.entity.WifiInfo;
import com.nimble.irisservices.service.IWifiInfoService;

@Service
public class WifiInfoServiceImpl implements IWifiInfoService{
	
	@Autowired
	@Lazy
	IWifiInfoDao wifiDao;
	
	@Transactional
	public boolean saveOrUpdateWifiInfo(WifiInfo wifi) throws Exception{
		
		return wifiDao.saveOrUpdateWifiInfo(wifi);
	}

	@Override
	@Transactional
	public WifiInfo isAlreadycontain(long gatewayid) {

		return wifiDao.isAlreadycontain(gatewayid);
	}
	
	@Override
	@Transactional
	public WifiInfo isAlreadycontain(long gatewayid,String ssidCategory, long userId) {

		return wifiDao.isAlreadycontain(gatewayid,ssidCategory, userId);
	}


	@Override
	@Transactional
	public List<BluetoothDeviceList> getWifiList(long gatewayID, long userID) {
		return wifiDao.getWifiList(gatewayID, userID);
	}

	
	@Override
	@Transactional
	public boolean deleteWifiInfoForGateway(String userId, String gatewayId) {
		// TODO Auto-generated method stub
		return wifiDao.deleteWifiInfoForGateway(userId, gatewayId);
	}
	
	@Override
	@Transactional
	public boolean updateWifiInfoAddress(long gatewayid, String ssidCatergory, long userId , String address) {
		return wifiDao.updateWifiInfoAddress(gatewayid, ssidCatergory, userId, address);
	}

	
}
