package com.nimble.irisservices.service.impl;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.util.List;
import java.util.Properties;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import com.nimble.irisservices.dao.ICompanyDao;
import com.nimble.irisservices.dao.IDynamicCmdDao;
import com.nimble.irisservices.dao.IGatewayDao;
import com.nimble.irisservices.dto.JSentMessage;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.exception.InvalidGatewayIdException;
import com.nimble.irisservices.service.IDynamicCmdService;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.SMSInterface;

@Service
public class MessagingServiceImpl implements IMessagingService {

	private static final Logger log = LogManager.getLogger(MessagingServiceImpl.class);

	@Autowired
	IDynamicCmdDao dynamicCmdDao;
	@Autowired
	ICompanyDao companyDao;
	@Autowired
	IGatewayDao gatewayDao;
	@Autowired
	IDynamicCmdService dynamicCmdService;

	@Autowired
	private SMSInterface smsInterface;
	
	@Autowired
	@Qualifier("amazonPinPoint")
	private SMSInterface smsInterfaceAmazon;

	@Autowired
	@Qualifier("twilio")
	private	SMSInterface twilio;

	@Value("${plivono}")
	private String plivono;
	
	@Value("${enablePowerBack}")
	private boolean enablePowerBack;
	
	@Value("${powerBackUUID}")
	private String powerBackUUID;

	@Transactional
	public boolean sendMessage(String gatewayid, String msg, long cmpId) throws InvalidGatewayIdException {
		try {
			Gateway gateway = gatewayDao.getGateway("", "", "", gatewayid, cmpId, "").get(0);

			String message1 = "%" + gateway.giveDatap().getName().trim() + ",," + msg + "#";
			String phonenumber = gateway.getMdn();
			String carrier = gateway.getCarrier();
			if (carrier.equalsIgnoreCase("INDIA")) {
				System.out.println("in india");
				phonenumber = "91" + phonenumber;
			} else if (carrier.equalsIgnoreCase("US")) {
				System.out.println("in us");
				phonenumber = "1" + phonenumber;
			} else {
				System.out.println("other countries");
				phonenumber = phonenumber.replace("-", "");
			}
			dynamicCmdService.saveDynamicCmd(gateway, msg, 0, "sent");

			callPlivoAPI(phonenumber, message1, String.valueOf(gateway.giveCompany().getId()),
					gateway.giveCompany().getName(), "Iris3.0Service", "sms");
		} catch (Exception e) {
			log.info("sendMessage " + e.getLocalizedMessage());
			throw new InvalidGatewayIdException();

		}
		return true;
	}

	@Transactional
	public boolean sendLiveTrackMessage(String gatewayid, String msg, long cmpId) throws InvalidGatewayIdException {
		try {
			Gateway gateway = gatewayDao.getGateway("", "", "", gatewayid, cmpId, "").get(0);

			String message1 = "%" + gateway.giveDatap().getName().trim() + ",," + msg + "#";
			String phonenumber = gateway.getMdn();
			String carrier = gateway.getCarrier();
			if (carrier.equalsIgnoreCase("INDIA")) {
				System.out.println("in india");
				phonenumber = "91" + phonenumber;
			} else if (carrier.equalsIgnoreCase("US")) {
				System.out.println("in us");
				phonenumber = "1" + phonenumber;
			} else {
				System.out.println("other countries");
				phonenumber = phonenumber.replace("-", "");
			}
			dynamicCmdService.saveDynamicCmd(gateway, msg, 0, "sent");

			boolean status = callPlivoAPILiveTrack(phonenumber, message1, String.valueOf(gateway.giveCompany().getId()),
					gateway.giveCompany().getName(), "Iris3.0Service", "sms");
			return status;
		} catch (Exception e) {
			log.info("sendMessage " + e.getLocalizedMessage());
			return false;

		}
	}

	@Transactional
	public boolean saveMessage(String gatewayid, String msg, long cmpId) {
		Gateway gateway = gatewayDao.getGateway("", "", "", gatewayid, cmpId, "").get(0);
		dynamicCmdService.saveDynamicCmd(gateway, msg, 1, "notsent");

		return true;
	}
	
	@Transactional
	public boolean saveFotaMessage(long gatewayid, String msg) {
		Gateway gateway = gatewayDao.getGatewayByid(gatewayid);
		dynamicCmdService.saveDynamicCmd(gateway, msg, 1, "notsent");

		return true;
	}

	@Override
	public void sendEmail(String alert_email,String emailids, String subject, String content, boolean isRVMailId) {
		// TODO Auto-generated method stub

		String[] emails = emailids.split(",");
		String fileName = null;
		for (String email : emails) {
			smsInterface.SendEmail_SES(alert_email,email, subject, content, fileName, isRVMailId);
		}
	}
	
	@Override
	public void sendEmailV2(String alert_email,String emailids, String subject, String content, boolean isRVMailId) {
		// TODO Auto-generated method stub

		String[] emails = emailids.split(",");
		String fileName = null;
		for (String email : emails) {
			smsInterface.SendEmail_SESV2(alert_email,email, subject, content, fileName, isRVMailId);
		}
	}

	@Transactional
	public String formMessage(String gatewayid, String message, long cmpId) throws InvalidGatewayIdException {

		CompanyConfig cmpCfg = companyDao.getCompanyConfig(cmpId);
		String[] msg = message.split("#");
		String[] msg1s = msg[0].split(",");
		message = "";
		int count = 0;
		for (String msg1 : msg1s) {
			if (count > 0)
				message = message + ",";

			String[] vals = msg1.split("=");
			for (int i = 0; i < vals.length; i++) {

				System.out.println("val " + i + " , " + vals[i]);
			}
			String val1 = vals[1];
			if (cmpCfg.getTemperatureunit().equals("F")) {
				val1 = this.FahrenheitToCelsius(val1);
			}
			int adc1 = this.convertTempIntoADCVal(Float.parseFloat(val1));
			message = message + vals[0] + "=" + adc1;
			count++;

			System.out.println(message);
		}
		message = message + "#";

		return message;
	}

	private String FahrenheitToCelsius(String tempmvalIndegreeFahrenheit) {
		double degreeCelsius = (Double.valueOf(tempmvalIndegreeFahrenheit.trim()).floatValue() - 32) * 5 / 9d;
		double roundvalues = Math.round(degreeCelsius);
		return Double.toString(roundvalues);
	}

	@Transactional
	public int convertTempIntoADCVal(float T) {
		// temp val 70.5 to 85
		if (T >= 70.5 && T <= 85) {
			int ADC_count = (int) (-1 * (((T - (70.5)) * (266 - 418)) - 418 * (70.5 - (85))) / (70.5 - (85)));
			return ADC_count;

		}
		// temp val 70.5 to 85
		if (T >= 60.5 && T <= 70) {
			int ADC_count = (int) (-1 * (((T - (60.5)) * (425 - 574)) - 574 * (60.5 - (70))) / (60.5 - (70)));
			return ADC_count;

		}
		// temp val 50.5 to 60
		if (T >= 50.5 && T <= 60) {
			int ADC_count = (int) (-1 * (((T - (50.5)) * (583 - 788)) - 788 * (50.5 - (60))) / (50.5 - (60)));

			return ADC_count;

		}
		// temp val 40.5 to 50
		if (T >= 40.5 && T <= 50) {
			int ADC_count = (int) (-1 * (((T - (40.5)) * (801 - 1075)) - 1075 * (40.5 - (50))) / (40.5 - (50)));

			return ADC_count;

		}
		// temp val 30.5 to 40
		if (T >= 30.5 && T <= 40) {
			int ADC_count = (int) (-1 * (((T - (30.5)) * (1091 - 1440)) - 1440 * (30.5 - (40))) / (30.5 - (40)));
			return ADC_count;

		}
		// temp val -4.5 to 30
		if (T >= -4.5 && T <= 30) {
			int ADC_count = (int) (-1 * (((T - (-4.5)) * (1461 - 3044)) - 3044 * (-4.5 - (30))) / (-4.5 - (30)));
			return ADC_count;

		}
		// temp val -19.5 to -5
		if (T >= -19.5 && T <= -5) {
			int ADC_count = (int) (-1 * (((T - (-19.5)) * (3065 - 3558)) - 3558 * (-19.5 - (-5))) / (-19.5 - (-5)));
			return ADC_count;

		}
		// temp val -29.5 to -20
		if (T >= -29.5 && T <= -20) {
			int ADC_count = (int) (-1 * (((T - (-29.5)) * (3572 - 3780)) - 3780 * (-29.5 - (-20))) / (-29.5 - (-20)));

			return ADC_count;

		}
		// temp val -40 to -30
		if (T >= -40 && T <= -30) {
			int ADC_count = (int) (-1 * (((T - (-40)) * (3789 - 3927)) - 3927 * (-40 - (-30))) / (-40 - (-30)));
			return ADC_count;

		} else
			return -300;
	}

	@Transactional
	public List<JSentMessage> sentMessages(String groupid, String subgroupid, String gatewayid, long cmpId) {
		// TODO Auto-generated method stub
		return dynamicCmdService.sentMessages(groupid, subgroupid, gatewayid, cmpId);
	}

	private String getPassword(long passwordtype) {
		/*
		 * Do not discuss with others 0 - %nimblexl (Old Password) 1 - %rthawki
		 * (New Password)
		 */
		String password = "%nimblexl";
		if (passwordtype == 1)
			password = "%rthawki";
		return password;
	}

	@Transactional
	public boolean savePlivoData(String phoneno, String msg, String cmpid, String cmpname, String appname,
			String type) {

		boolean status = false;
		int sms_transport_type = 0;
			try {
			// Check if Plivo/TwilioServices has to be used-July 2018
			sms_transport_type = dynamicCmdDao.getEnabledTransportType();
			//System.out.println("plivono : " + plivono);
			
			if (sms_transport_type == 1)// Plivo
			{
				log.info("Send SMS through Plivo");
				
				status = smsInterface.SendMessage(phoneno, msg, cmpid, cmpname, "Iris3.0Service",plivono,enablePowerBack,powerBackUUID);
				
				if (status)
					dynamicCmdService.savePlivoData(phoneno, msg, cmpid, cmpname, appname, type, sms_transport_type);
				else
					log.info("savePlivoData: SMS not sent successfully through Plivo");
	
			} else if (sms_transport_type == 2) {// TwilioServices
				log.info("Send SMS through Twilio");
				status = twilio.SendMessage(phoneno, msg, cmpid, cmpname, "Iris3.0Service","",false,"");
				
				if (status)
					dynamicCmdService.savePlivoData(phoneno, msg, cmpid, cmpname, appname, type, sms_transport_type);
				else
					log.info("savePlivoData: SMS not sent successfully through TwilioServices");
			} else if( sms_transport_type == 3 ) { // Amazon Pin Point Service
				
				log.info("Send SMS through Amazon Pin Point Services");
				
				status = smsInterfaceAmazon.SendMessage(phoneno, msg, cmpid, cmpname, "Iris3.0Service","",false,"");
				
				if (status)
					dynamicCmdService.savePlivoData(phoneno, msg, cmpid, cmpname, appname, type, sms_transport_type);
				else
					log.info("savePlivoData: SMS not sent successfully through Amazon Pin Point Service");
				
			} else {
				log.info("No messaging interfaces (Plivo/TwilioServices)are enabled to send data");
				//System.out.println("(Plivo/TwilioServices)are not enabled");
	
			}
		}catch (Exception e) {
			log.error("savePlivoData : "+e.getLocalizedMessage());
			//e.printStackTrace();
		}

		return status;
	}

	@Transactional
	public boolean savePlivoVoiceData(String phoneno, String msg, String cmpid, String cmpname, String appname,
			String type, String ip) {

		boolean status = false;
		int voice_transport_type = 0;
		// Check if Plivo/TwilioServices has to be used-July 2018
		voice_transport_type = dynamicCmdDao.getEnabledTransportType();

		if (voice_transport_type == 1)// Plivo
		{
			//System.out.println("Voice call through Plivo");
			status = smsInterface.SendVoiceMessage(phoneno, msg, cmpid, cmpname, appname, ip,plivono);
			if (status)
				dynamicCmdService.savePlivoData(phoneno, msg, cmpid, cmpname, appname, type, voice_transport_type);
			else
				log.info("savePlivoData: Voice msg not sent successfully through Plivo");

		} else if (voice_transport_type == 2) {// TwilioServices
			//System.out.println("Voice call through Twilio");
			status = twilio.SendVoiceMessage(phoneno, msg, cmpid, cmpname, appname, ip,"");
			if (status)
				dynamicCmdService.savePlivoData(phoneno, msg, cmpid, cmpname, appname, type, voice_transport_type);
			else
				log.info("savePlivoData: Voice msg not sent successfully through TwilioServices");

		} else {
			log.info("No messaging interfaces(Plivo/TwilioServices) are enabled to send data");

		}

		return status;
	}

	private void callPlivoAPI(String phoneno, String msg, String cmpid, String cmpname, String appname, String type) {
		int timeout = 5 * 1000; // 5 seconds
		HttpClient client = new DefaultHttpClient();
		// String ipaddress = getMessageIp()+":8080";

		try {

			String msg_enc = URLEncoder.encode(msg.trim(), "UTF-8");
			String cmpid_enc = URLEncoder.encode(cmpid.trim(), "UTF-8");
			String cmpname_enc = URLEncoder.encode(cmpname.trim(), "UTF-8");
			String appname_enc = URLEncoder.encode(appname.trim(), "UTF-8");

			String url1 = getMessageIp() + "/irisservices/v3.0/nimblesms?" + "phoneno=" + phoneno + "&msg=" + msg_enc
					+ "&cmpid=" + cmpid_enc + "&cmpname=" + cmpname_enc + "&appname=" + appname_enc + "&type=" + type;

			log.info("Message Url " + url1);
			HttpPost post = new HttpPost(url1);

			HttpResponse response = client.execute(post);

			//System.out.println("\nSending 'POST' request to URL : " + url1);
			//System.out.println("Response Code : " + response.getStatusLine().getStatusCode());

			// client.getParams().setParameter("http.socket.timeout", new
			// Integer(10*1000));

			HttpParams httpParams = client.getParams();
			HttpConnectionParams.setConnectionTimeout(httpParams, timeout); // http.connection.timeout
			HttpConnectionParams.setSoTimeout(httpParams, timeout); // http.socket.timeout

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}

			rd.close();

		} catch (IOException e) {
			log.info("savePlivoVoiceData " + e.getLocalizedMessage());
			System.err.println("callPlivoAPI Exception:" + e);
		} catch (Exception e) {
			log.info("savePlivoVoiceData " + e.getLocalizedMessage());
		} finally {
			try {
				client.getConnectionManager().shutdown();
			} catch (Exception e) {
				log.info("savePlivoVoiceData " + e.getLocalizedMessage());
			}
		}
	}

	private boolean callPlivoAPILiveTrack(String phoneno, String msg, String cmpid, String cmpname, String appname,
			String type) {

		//System.out.println("calling plivo api");

		int timeout = 5 * 1000; // 5 seconds
		HttpClient client = new DefaultHttpClient();
		// String ipaddress = getMessageIp()+":8080";

		try {

			String msg_enc = URLEncoder.encode(msg.trim(), "UTF-8");
			String cmpid_enc = URLEncoder.encode(cmpid.trim(), "UTF-8");
			String cmpname_enc = URLEncoder.encode(cmpname.trim(), "UTF-8");
			String appname_enc = URLEncoder.encode(appname.trim(), "UTF-8");

			String url1 = getMessageIp() + "/irisservices/v3.0/nimblesms?" + "phoneno=" + phoneno + "&msg=" + msg_enc
					+ "&cmpid=" + cmpid_enc + "&cmpname=" + cmpname_enc + "&appname=" + appname_enc + "&type=" + type;

			log.info("Message Url " + url1);
			HttpPost post = new HttpPost(url1);

			HttpResponse response = client.execute(post);

			//System.out.println("\nSending 'POST' request to URL : " + url1);
			//System.out.println("Response Code : " + response.getStatusLine().getStatusCode());

			// client.getParams().setParameter("http.socket.timeout", new
			// Integer(10*1000));

			HttpParams httpParams = client.getParams();
			HttpConnectionParams.setConnectionTimeout(httpParams, timeout); // http.connection.timeout
			HttpConnectionParams.setSoTimeout(httpParams, timeout); // http.socket.timeout

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}

			rd.close();

			JSONObject apiResponse = new JSONObject(result.toString());
			JSONObject _response = new JSONObject();
			_response = apiResponse.getJSONObject("response");
			int _status = _response.getInt("Status");
			if (_status > 0) {
				return true;
			} else {

				return false;
			}

		} catch (IOException e) {
			log.info("savePlivoVoiceData " + e.getLocalizedMessage());
			//System.err.println("callPlivoAPI Exception:" + e);
			return false;
		} catch (Exception e) {
			log.info("savePlivoVoiceData " + e.getLocalizedMessage());
			return false;
		} finally {
			try {
				client.getConnectionManager().shutdown();
			} catch (Exception e) {
				log.info("savePlivoVoiceData " + e.getLocalizedMessage());
			}
		}
	}

	@Transactional
	public String getMessageIp() {
		String ip = "http://Iris-1353414126.us-west-2.elb.amazonaws.com";
		try {
			Properties prop = new Properties();

			try {
				// load a properties file
				//prop.load(new FileInputStream("iris3.properties"));
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));
				ip = prop.getProperty("ip");

			} catch (IOException ex) {
				log.info("getMessageIp " + ex.getLocalizedMessage());
				return ip;
			}
		} catch (Exception e) {
			log.info("getMessageIp " + e.getLocalizedMessage());
			return ip;
		}
		return ip;

	}

	@Transactional
	public boolean saveMessageV2(String gatewayid, String msg, long seqno) {
		Gateway gateway = gatewayDao.getGateway(Long.valueOf(gatewayid));
		dynamicCmdService.saveDynamicCmdV2(gateway, msg, 1, "notsent", seqno);
		return true;
	}
	@Override
	public boolean isDynamicCmdInserted(Long gatewayId, String fotacommand){

		return dynamicCmdService.isDynamicCmdInserted(gatewayId,fotacommand);
	}

}
