package com.nimble.irisservices.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IAlexaDao;
import com.nimble.irisservices.dto.AlexaTokenSet;
import com.nimble.irisservices.service.IAlexaService;

@Service
@Transactional
public class AlexaServiceImpl implements IAlexaService{

	@Autowired
	@Lazy
	IAlexaDao alexaDao;

	@Override
	public long getUserIdByAlexaId(String alexaId) {
		return alexaDao.getUserIdByAlexaId(alexaId);
	}

	@Override
	public void updateAlexaId(String userid ,String alexaId) {
		 alexaDao.updateAlexaId(userid, alexaId);
	}

	@Override
	public AlexaTokenSet getAlexaTokens(long userId) {
		return alexaDao.getAlexaTokens(userId);
	}

	@Override
	public boolean insertAlexaTokens(AlexaTokenSet alexaTokenSet) {
		return alexaDao.insertAlexaTokens(alexaTokenSet);
	}

	@Override
	public boolean updateAlexaTokens(AlexaTokenSet alexaTokenSet) {
		return alexaDao.updateAlexaTokens(alexaTokenSet);
	}
	
}
