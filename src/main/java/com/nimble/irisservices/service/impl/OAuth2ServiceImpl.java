package com.nimble.irisservices.service.impl;

import com.nimble.irisservices.Util.JwtKeyUtil;
import com.nimble.irisservices.Util.SecretManagerService;
import com.nimble.irisservices.dao.IOAuth2Dao;
import com.nimble.irisservices.dto.JOAuth2Token;
import com.nimble.irisservices.dto.JwtTokenData;
import com.nimble.irisservices.entity.UserEntity;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IOAuth2Service;
import com.nimbusds.jose.EncryptionMethod;
import com.nimbusds.jose.JWEAlgorithm;
import com.nimbusds.jose.JWEHeader;
import com.nimbusds.jose.crypto.RSADecrypter;
import com.nimbusds.jose.crypto.RSAEncrypter;
import com.nimbusds.jwt.EncryptedJWT;
import com.nimbusds.jwt.JWTClaimsSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerEndpointsConfiguration;
import org.springframework.security.oauth2.provider.NoSuchClientException;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.endpoint.TokenEndpoint;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.security.Principal;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.nimble.irisservices.helper.Helper.unzipContent;

@Service
public class OAuth2ServiceImpl implements IOAuth2Service{
	
	private static final Logger log = LogManager.getLogger(OAuth2ServiceImpl.class);
	
	@Autowired
	private TokenEndpoint tokenEndpoint;
	
	@Autowired
	IOAuth2Dao oAuth2Dao;
	
	@Autowired
	Helper _helper;

	@Value("${jwt_key_secret_name}")
	private String jwtKeySecretName;
	
	@Value("${config.oauth2.accesstoken.validation}")
	private int accessTokenValidationTime;
	
	@Autowired
	OAuth2CustomUserDetailsServiceImpl customUserDetailsService;

	@Autowired
	private AuthorizationServerEndpointsConfiguration configuration;

	@Autowired
	private SecretManagerService secretManagerService;
	
	@Override
	public byte[] generateOauth2Token(String username, String password, String clientId, String clientSecret) {
		log.info("Entered into generateOauth2Token :: username : " + username + "client ID : " + clientId );	
		try {
			Map<String, String> requestParameters = new HashMap<String, String>();
		    Map<String, Serializable> extensionProperties = new HashMap<String, Serializable>();
		    
		    List<String> scopes = new ArrayList<String>();
		    scopes.add("read");
		    scopes.add("write");
		    
		    Set<String> responseTypes = new HashSet<String>();
		    responseTypes.add("password");
		    
		    List<GrantedAuthority> authorities = new ArrayList<GrantedAuthority>();
		    authorities.add(new SimpleGrantedAuthority("ROLE_read"));
		    authorities.add(new SimpleGrantedAuthority("ROLE_write"));

		    boolean approved = true;
		    OAuth2Request oauth2Request = new OAuth2Request(requestParameters, clientId, authorities, approved, new HashSet<String>(scopes), new HashSet<String>(Arrays.asList("oauth2-resource")), null, responseTypes, extensionProperties);
		    UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, "", authorities);
		    OAuth2Authentication auth = new OAuth2Authentication(oauth2Request, authenticationToken);
		    AuthorizationServerTokenServices tokenService = configuration.getEndpointsConfigurer().getTokenServices();
		    
		    OAuth2AccessToken token = tokenService.createAccessToken(auth);			
			JOAuth2Token jOAuth2Token = new JOAuth2Token();
			 
			Date dateUTC = _helper.convertDateTimeToUTC(token.getExpiration());
			
			jOAuth2Token.setAccess_token(token.getValue());
			jOAuth2Token.setRefresh_token(token.getRefreshToken().getValue());
			jOAuth2Token.setAdditional_information(token.getAdditionalInformation());
			jOAuth2Token.setExpiration(dateUTC);
			jOAuth2Token.setAccess_token_validation_time(accessTokenValidationTime);
			
			DateFormat formatterUTC = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String exDate = formatterUTC.format(dateUTC);
			jOAuth2Token.setExpire_time(exDate);
			
			return _helper.zipContent(jOAuth2Token);
		} catch (Exception e) {
			log.error( "Error in generateOauth2Token" + e.getLocalizedMessage() );
			return null;
		}
				
	}
	
	@Override
	public ResponseEntity<OAuth2AccessToken> getOAuth2TokenByRefreshToken(String refreshToken, String clientId, String clientSecret) throws NoSuchClientException,InvalidTokenException {
		log.info("Entered into getRefreshTokenByAccessToken " );
		try {
			
			Collection<GrantedAuthority> grantedAuthoritiesList = new ArrayList<>();
			grantedAuthoritiesList.add(new SimpleGrantedAuthority("ROLE_read"));
			grantedAuthoritiesList.add(new SimpleGrantedAuthority("ROLE_write"));

			Principal principal = new UsernamePasswordAuthenticationToken(clientId, clientSecret, grantedAuthoritiesList);

			HashMap<String, String> parameters = new HashMap<String, String>();
			parameters.put("grant_type", "refresh_token");
			parameters.put("oauth2-resource", "resource");
			parameters.put("refresh_token", refreshToken);
			
			ResponseEntity<OAuth2AccessToken> token = tokenEndpoint.postAccessToken(principal, parameters);
			return token;
			
		}catch (NoSuchClientException e) {
			log.error( "Error in generateOauth2Token " + e.getLocalizedMessage() );
			throw new NoSuchClientException("client princepal not match");
		} catch (InvalidTokenException e) {
			log.error( "Error in token time expired " + e.getLocalizedMessage() );
			throw new InvalidTokenException("token time expired");
		} 
		catch (Exception e) {
			log.error( "Error in generateOauth2Token " + e.getLocalizedMessage() );
			return null;
		}

	}

	@Override
	public UserEntity getUserDetails(String emailId) {
		return oAuth2Dao.getUserDetails(emailId);
	}

	@Override
	public OAuth2Authentication getOAuth2(String username, String password) {
		return oAuth2Dao.getOAuth2(username, password);
	}

	@Override
	public String saveOauth2Code(OAuth2Authentication authentication) {
		return oAuth2Dao.saveOauth2Code(authentication);
	}

	@Override
	public String generateJWTToken(JwtTokenData jwtTokenData) {

        log.info("Entered into generateJWTToken :: user_id : {}", jwtTokenData.getUserId());
		JWTClaimsSet.Builder claimsBuilder = new JWTClaimsSet.Builder()
				.claim("userId", jwtTokenData.getUserId())
				.claim("gatewayId", jwtTokenData.getGatewayId())
				.claim("monitorTypeId", jwtTokenData.getMonitorTypeId())
				.claim("authKey", jwtTokenData.getAuthKey())
				.claim("routePath", jwtTokenData.getRoutePath())
				.claim("isOldUser", jwtTokenData.isOldUser())
				.claim("showPasswordUpdate", jwtTokenData.isShowPasswordUpdate())
				.claim("isSocialUser", jwtTokenData.isSocialUser())
				.issueTime(new Date())
				.expirationTime(new Date(System.currentTimeMillis() + 9 * 60 * 60 * 1000));

		JOAuth2Token decompressedOAuth2Token = new JOAuth2Token();
		try {
			decompressedOAuth2Token = unzipContent(jwtTokenData.getZippedAccessToken(), JOAuth2Token.class);
		} catch (Exception e) {
			log.error("Error while decompressing access token: {}", e.getLocalizedMessage());
		}

		if (jwtTokenData.getAccessToken() != null) {
			claimsBuilder.claim("accessToken", decompressedOAuth2Token.getAccess_token());
		}

		JWTClaimsSet claimsSet = claimsBuilder.build();

		JWEHeader header = new JWEHeader.Builder(JWEAlgorithm.RSA_OAEP_256, EncryptionMethod.A128GCM)
				.contentType("JWT")
				.build();

		String publicKeyString = secretManagerService.getSecretValue(jwtKeySecretName, "public_key");
		RSAPublicKey publicKey = JwtKeyUtil.loadPublicKey(publicKeyString);

		EncryptedJWT jwe = new EncryptedJWT(header, claimsSet);

        try {
            assert publicKey != null;
            jwe.encrypt(new RSAEncrypter(publicKey));
        } catch (Exception e) {
            log.error("Error while encrypting JWT token: {}", e.getLocalizedMessage());
        }

        return jwe.serialize();
	}

	@Override
	public JwtTokenData validateJWTToken(String token) {

		log.info("Entered into validateJWTToken :: token : {}", token);
		JwtTokenData jwtTokenData = new JwtTokenData();

		try {
			RSAPrivateKey privateKey = JwtKeyUtil.loadPrivateKey(secretManagerService.getSecretValue(jwtKeySecretName, "private_key"));
			EncryptedJWT jwe = EncryptedJWT.parse(token);

			assert privateKey != null;
			jwe.decrypt(new RSADecrypter(privateKey));

			JWTClaimsSet claims = jwe.getJWTClaimsSet();

			Date expiration = claims.getExpirationTime();
			if (expiration == null || expiration.before(new Date())) {
				log.error("JWT token is expired");
				return null;
			}

			jwtTokenData.setUserId(claims.getLongClaim("userId"));
			jwtTokenData.setGatewayId(claims.getLongClaim("gatewayId"));
			jwtTokenData.setMonitorTypeId(claims.getLongClaim("monitorTypeId"));
			jwtTokenData.setAuthKey(claims.getStringClaim("authKey"));
			jwtTokenData.setAccessToken(claims.getStringClaim("accessToken"));
			jwtTokenData.setRoutePath(claims.getStringClaim("routePath"));
			jwtTokenData.setOldUser(claims.getBooleanClaim("isOldUser"));
			jwtTokenData.setSocialUser(claims.getBooleanClaim("isSocialUser"));
			jwtTokenData.setShowPasswordUpdate(claims.getBooleanClaim("showPasswordUpdate"));

			return jwtTokenData;
		} catch (Exception e) {
			log.error("Error while validating JWT token: {}", e.getLocalizedMessage());
			return null;
		}
	}
}