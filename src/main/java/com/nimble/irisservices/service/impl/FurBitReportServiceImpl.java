package com.nimble.irisservices.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.service.IFurBitReportService;

@Service
@Transactional
public class FurBitReportServiceImpl implements IFurBitReportService{
	/*
	private static final Logger log = LogManager.getLogger(FurBitReportServiceImpl.class);
	
	@Autowired
	IFurBitReportDao iFurBitReportDao;

	@Override
	public List<FurBitDailyReport> getFurBitDailyRpt(String date) {
		// TODO Auto-generated method stub
		return iFurBitReportDao.getFurBitDailyRpt(date);
	}


	@Override
	public List<FurbitLastGatewayReport> getFurbitLastGatewayReport(String gateway) {
	// TODO Auto-generated method stub
		return iFurBitReportDao.getFurbitLastGatewayReport(gateway);
	}

	@Override
	public List<FurBitReport> getFurBitlastreport(String gatewayId) {
		return iFurBitReportDao.getFurBitlastReport(gatewayId);
	}

	@Override
	public JFurBitReportReportSummary getFurBitDailyReport(String date, long userId, String gatewayId, String hour,
			String timezone, String gatewayname, Gateway gateway) {
		 return iFurBitReportDao.getFurBitDailyReport(date, userId, gatewayId, hour, timezone,gatewayname,gateway);
	}

	@Override
	public JFurBitReportReportSummary getFurBitReport(String date, long userId, String gatewayId, String timezone,
			String days, String reportDays, String gatewayname, Gateway gateway) {
		return iFurBitReportDao.getFurBitReport(date, userId, gatewayId, timezone, days, reportDays, gatewayname,gateway);
	}
	
	@Override
	public List<JLeaderBoard> getLeaderBoardDetails(String rpttype,String startDate, String endDate, int limit,HashMap<Long, Long> usergaway){
		return iFurBitReportDao.getLeaderBoardDetails(rpttype,startDate, endDate, limit,usergaway);
	}

	@Override
	public List<JUserLeaderBoard> getUserLeaderBoardDetails(long userId,String startDate, String endDate,String rpttype){
		return iFurBitReportDao.getUserLeaderBoardDetails(userId,startDate, endDate,rpttype );
	}
	
	@Override
	public List<AdventureReport> getAdventureReport(long gatewayid){
		return iFurBitReportDao.getAdventureReport(gatewayid);
	}

	@Override
	public JResponse insertPowerModeDynamicCmd(JGatewayDetails gateway, String msg, int transportType, String status, String previousMode) {
		return iFurBitReportDao.insertPowerModeDynamicCmd(gateway, msg, transportType, status, previousMode);
	}
	
	@Override
	public JResponse getDevicePowerMode(String gatewayid) {
		// TODO Auto-generated method stub
		return iFurBitReportDao.getDevicePowerMode(gatewayid);
	}

	@Override
	public boolean deletePowerSaveModeDetailsForGateway(String gatewayId) {
		// TODO Auto-generated method stub
		return iFurBitReportDao.deletePowerSaveModeDetailsForGateway(gatewayId);
	}

	@Override
	public JResponse insertAdventureModeDynamicCmd(JGatewayDetails gateway, String msg, int transportType, String status,
			String enable) {
		// TODO Auto-generated method stub
		return iFurBitReportDao.insertAdventureModeDynamicCmd(gateway, msg, transportType, status, enable);
	}


	@Transactional
	public PowerModeConfig getPowerModeConfig(String key, String value) {
		// TODO Auto-generated method stub
		return iFurBitReportDao.getPowerModeConfig(key, value);
	}
	
	@Override
	public ArrayList<Mode> getAllDeviceModes(){
		return iFurBitReportDao.getAllDeviceModes();
	}

	@Override
	public AdventureModeStatus getAdventureModeStatus(long gateway_id) {
		return iFurBitReportDao.getAdventureModeStatus(gateway_id);
	}*/
}
