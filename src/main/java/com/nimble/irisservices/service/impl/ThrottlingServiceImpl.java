package com.nimble.irisservices.service.impl;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IThrottlingDao;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.service.IThrottlingService;
@Repository
@Transactional
public class ThrottlingServiceImpl implements IThrottlingService{

	private static final Logger log = LogManager.getLogger(ThrottlingServiceImpl.class);
	
	@Autowired
	private SessionFactory sessionFactory;
	
	@Autowired
	private SessionFactory slave5SessionFactory;
	
	@Autowired 
	IThrottlingDao thrDao ;

	@Override
	public List<ThrottlingSettings> getThrotSettings(String throtId) {
		// TODO Auto-generated method stub
		String qry = "from ThrottlingSettings";

		if(!throtId.isEmpty())
			qry = qry + " where id = '"+throtId+"'";
		try
		{
			List<ThrottlingSettings> throtsetting = this.slave5SessionFactory.getCurrentSession().createQuery(qry).list();			
			return throtsetting;
		}
		catch(Exception e)
		{
			log.error("Error in getThrotSettings :: Session Name : slave5SessionFactory :: error : " + e.getLocalizedMessage());
			return null;
		}

	}

	@Override
	public boolean saveOrUpdateThrotSetting(ThrottlingSettings throtSettings) {
		boolean issuccess = false;
		sessionFactory.getCurrentSession().saveOrUpdate(throtSettings);
		return issuccess;
	}
	
	@Override
	public ThrottlingSettings getThrotSettingsById(long throtId) {
		
		return thrDao.getThrotSettingsById(throtId);
	}
}
