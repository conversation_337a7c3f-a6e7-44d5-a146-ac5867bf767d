package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IPetBreedDao;
import com.nimble.irisservices.dto.JBreed;
import com.nimble.irisservices.entity.PetBreeds;
import com.nimble.irisservices.service.IPetBreedServices;
@Service
@Transactional
public class PetBreedServiceImpl implements IPetBreedServices {


	@Autowired
	IPetBreedDao petBreedDao;

	@Override
	public boolean saveORupdateBreed(PetBreeds petBreeds) {
		// TODO Auto-generated method stub
		return petBreedDao.saveORupdateBreed(petBreeds);
	}

	@Override
	public List<PetBreeds> getAllPetBreeds() {
		// TODO Auto-generated method stub
		return petBreedDao.getAllPetBreeds();
	}

	@Override
	public List<PetBreeds> getPetBreeds(int speciesId) {
		// TODO Auto-generated method stub
		return petBreedDao.getPetBreeds(speciesId);
	}

	@Override
	public List<PetBreeds> getPetBreedsByName(String speciesname) {
		// TODO Auto-generated method stub
		return petBreedDao.getPetBreedsByName(speciesname);
	}

	
	@Override
	public PetBreeds getPetBreedsByBName(String breedname){
		return petBreedDao.getPetBreedsByBName(breedname);
	}
	
	@Override
	public List<JBreed> listBreeds(int speciesId) {
		return petBreedDao.listBreeds(speciesId);
	}

}
