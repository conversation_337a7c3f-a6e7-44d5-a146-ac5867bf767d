package com.nimble.irisservices.service.impl;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.dao.IAdvertisementDao;
import com.nimble.irisservices.entity.Advertisement;
import com.nimble.irisservices.entity.AppImage;
import com.nimble.irisservices.service.IAdvertisementService;

@Service
public class AdvertisementServiceImpl implements IAdvertisementService{

	@Autowired
	@Lazy
	IAdvertisementDao advDao;
	
	
	@Override
	@Transactional
	public List<Advertisement> getAdvertismentUrl(String ver) {
		return advDao.getAdvertismentUrl(ver);
	}

	@Override
	@Transactional
	public boolean createAdvertisement(Advertisement adinfo) {
		return advDao.SaveOrUpdateAD(adinfo);
	}
	
	@Override
	@Transactional
	public List<Advertisement> getAllAdvertismentUrl(){
		return advDao.getAllAdvertismentUrl();
	}
	
	@Override
	@Transactional
	public AppImage getAppImages(String type, String img_name){
		return advDao.getAppImages(type, img_name);
	}
}
