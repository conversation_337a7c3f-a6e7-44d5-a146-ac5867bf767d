package com.nimble.irisservices.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IUserDaoV4;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.EntityHashing;
import com.nimble.irisservices.service.IUserServiceV4;

@Service
@Transactional
public class UserServiceImplV4 implements IUserServiceV4 {

	@Autowired
	IUserDaoV4 userDao;

	@Autowired
	IUserServiceV4 userServiceV4;

	EntityHashing entityHashing = new EntityHashing();

	@Override
	public Map<String, String> getUserId_cmpIdByAuthV2(String autho) throws InvalidAuthoException {
		return userDao.getUserId_cmpIdByAuthV2(autho);
	}

	@Override
	public UserV4 verifyAuthV4(String key, String value) throws InvalidAuthoException {
		return userDao.verifyAuthV4(key, value);
	}

	@Override
	public long getUserByUNameOrEmailV4(String email) {
		return userDao.getUserByUNameOrEmail(email);
	}

	@Override
	public boolean updateUserv4byuserid(User user, long userid) {
		return userDao.updateUserv4byuserid(user, userid);

	}

	@Override
	public Map<String, String> getUserId_cmpIdByAuth(String autho) throws InvalidAuthoException {
		return userDao.getUserId_cmpIdByAuth(autho);
	}

	@Override
	public List<JAlertV4> getUnackAlerts(long userid, String tempUnit, String monitortype) {
		return userDao.getUnackAlerts(userid, tempUnit, monitortype);
	}

	@Override
	public UserV4 verifyAuthV3(String key, String value) throws InvalidAuthoException {
		return userDao.verifyAuthV3(key, value);
	}

	@Override
	public List<UserV4> getUsersByUserId_CmpId(String userId, long cmpId) {
		return userDao.getUsersByUserId_CmpId(userId, cmpId);
	}

	@Override
	public int updateUserPassword(String authKey, String password) {
		return userDao.updateUserPassword(authKey, password);
	}

	@Override
	public boolean saveOrUpdateUserTokenV4(String userid, UserToken usertoken) throws DataIntegrityViolationException {
		return userDao.saveOrUpdateUserTokenV4(userid, usertoken);
	}

	@Override
	public boolean saveOrUpdateUserDeviceInfoV4(long userid, JUserDeviceInfo deviceInfo, String ipAddress,
			String Currdate) throws DataIntegrityViolationException {

		String update_qry = "UPDATE userdeviceinfo UDI set UDI.os = '" + deviceInfo.getOs() + "', UDI.version ='"
				+ deviceInfo.getVersion() + "', UDI.userId ='" + userid + "', UDI.devicemodel='"
				+ deviceInfo.getDevicemodel() + "' , UDI.deviceid='" + deviceInfo.getDeviceid()
				+ "' , UDI.ipaddress = '" + ipAddress + "' , UDI.createdon='" + Currdate + "', UDI.appversion='"
				+ deviceInfo.getAppversion() + "' WHERE UDI.userId = '" + userid + "' AND UDI.deviceid = '"
				+ deviceInfo.getDeviceid() + "' ;";
		String create_qry = "INSERT INTO userdeviceinfo(os,version,userId,devicemodel,deviceid,ipaddress,createdon,appversion) VALUES('"
				+ deviceInfo.getOs() + "','" + deviceInfo.getVersion() + "','" + userid + "','"
				+ deviceInfo.getDevicemodel() + "','" + deviceInfo.getDeviceid() + "','" + ipAddress + "','" + Currdate
				+ "','" + deviceInfo.getAppversion() + "')";

		int updateStatus = userDao.executeQuery(update_qry);

		if (updateStatus > 0) {
			System.out.println("User device info updated successful");
			return true;
		} else {
			int createStatus = userDao.executeQuery(create_qry);
			System.out.println("User device info inserted successful");
			if (createStatus > 0)
				return true;
		}
		return false;
	}

	@Override
	public String saveOrUpdateVPM_id(long user_id, String vpm_id) {
		return userDao.saveOrUpdateVPM_id(user_id, vpm_id);
	}

	@Override
	public Map<String, String> getVPMData(long user_id) {
		return userDao.getVPMData(user_id);
	}

	@Override
	public JResponse getuserlistbyfilter(String uKey, String uValue, String fType, String oType, long offset,
			long limit, String okey, JResponse response) {
		return userDao.getuserlistbyfilter(uKey, uValue, fType, oType, offset, limit, okey, response);
	}

	@Override
	public boolean updateViewCount(long user_id) {
		return userDao.updateViewCount(user_id);
	}

	@Override
	public boolean updateAmazonReview(AmazonUserReview auReview,long monitorType) {
		return userDao.updateAmazonReview(auReview,monitorType);
	}

	@Override
	public int getViewCount(long user_id) {
		return userDao.getViewCount(user_id);
	}

	@Override
	public AmazonUserReview getAmazonUserReview(long userid,long monitorType) {
		return userDao.getAmazonUserReview(userid,monitorType);
	}

	@Override
	public boolean updateUserv4byuseridWeb(UserV4 user) {
		return userDao.updateUserv4byuseridWeb(user);
	}

	@Override
	public List<AmazonReviewList> getAmazonReviewList() {
		return userDao.getAmazonReviewList();
	}

	@Override
	public JUserFeedback getFeedbackLink(long userid, String category) {
		return userDao.getFeedbackLink(userid, category);
	}
	
	@Override
	public GeneralReview getGeneralReviewData(long userid, long monitorType) {
		return userDao.getGeneralReview(userid,monitorType);
	}

	@Override
	public UserFeedbackTransaction getUserFeedbackTransaction(long userid, long formid) {
		return userDao.getUserFeedbackTransaction(userid, formid);
	}

	@Override
	public boolean saveOrUpdateUserFeedback(UserFeedbackTransaction feedbackObj) {
		return userDao.saveOrUpdateUserFeedback(feedbackObj);
	}

	@Override
	public ArrayList<OrderMappingDetails> getOrderMappingListByUser(long userid) {
		return userDao.getOrderMappingListByUser(userid);
	}

	@Override
	public String getLastFeedbackDate(long userid,long monitorType) {
		return userDao.getLastFeedbackDate(userid, monitorType);
	}

	@Override
	public void addFreeVPM(String featureName, long userId) {
		userDao.addFreeVPM(featureName, userId);
	}

	@Override
	public long getUserbyVPMid(String vpmid) {
		return userDao.getUserbyVPMid(vpmid);
	}

	@Override
	public int getInappRedirect(String osversion, String ostype) {
		return userDao.getInappRedirect(osversion, ostype);
	}

	@Override
	public boolean updateInAppPurchaseInfo(int inapp_redirect, long userid) {
		return userDao.updateInAppPurchaseInfo(inapp_redirect, userid);
	}

	@Override
	public boolean updateUserRVDetails(UserRvDetails userRvDetails, long userid) {
		return userDao.updateUserRVDetails(userRvDetails, userid);
	}

//	@Override
//	public UserRvDetails getUserRvDetails(long user_id) {
//		return userDao.getUserRvDetails(user_id);
//	}

	@Override
	public boolean createFeedbackFormWeb(FeedbackForm feedback) {
		return userDao.createFeedbackFormWeb(feedback);
	}

	@Override
	public List<FeedbackForm> listFeedbackWeb() throws Exception {
		return userDao.listFeedbackWeb();
	}

	@Override
	public String createUserInChargebee(String fName, String lName, String email, String phoneNo, String userName,
			int amount, String desc) {
		return userDao.createUserInChargebee(fName, lName, email, phoneNo, userName, amount, desc);
	}

	@Override
	public boolean saveOTP(OneTimePassword otpObj) {
		return userDao.saveOTP(otpObj);
	}

	@Override
	public boolean validateOTP(long userId, OneTimePassword otpObj, int validMinutes) {
		return userDao.validateOTP(userId, otpObj, validMinutes);
	}

	@Override
	public boolean updateOrderMappingDetails(long userId, String qrc, String orderId, String orderChannel,
			String deviceStatus, String orderSku, String orderedDate) {
		return userDao.updateOrderMappingDetails(userId, qrc, orderId, orderChannel, deviceStatus, orderSku,
				orderedDate);
	}

	@Override
	public int updateUserPasswordV2(String username, String password) {
		return userDao.updateUserPasswordV2(username, password);
	}

	@Override
	public int updateUserPasswordV3(String username, String password, String password_ver) {
		return userDao.updateUserPasswordV3(username, password, password_ver);
	}

	@Override
	public boolean updateEvalidation(String encryptId, String encryptPwd) {
		return userDao.updateEvalidation(encryptId, encryptPwd);
	}

	@Override
	public boolean insertEvalidation(String encryptId, String encryptPwd) {
		return userDao.insertEvalidation(encryptId, encryptPwd);
	}

	@Override
	public boolean getEvalidation(String encryptId) {
		return userDao.getEvalidation(encryptId);
	}

	@Override
	public String getEValidationPassword(long user_id) {
		String encryptId = entityHashing.encryptUserid(user_id);
		String decryptPwd = userDao.getEValidationPassword(encryptId);
		if (decryptPwd != null)
			return EntityHashing.decryptPassword(decryptPwd);
		else
			return null;
	}

	@Override
	public String getContryByPhoneNo(String userPhone) {
		try {

			String[] phoneNumber = userPhone.split("-");
			String country = phoneNumber[0];
			String mobileNo = phoneNumber[1];

			if (country.equalsIgnoreCase("+91") || country.equalsIgnoreCase("91")) {
				return "IN";
			} else if (country.equalsIgnoreCase("+44") || country.equalsIgnoreCase("44")) {
				return "GB";
			} else if (country.equalsIgnoreCase("+61") || country.equalsIgnoreCase("61")) {
				return "AU";
			} else if (country.equalsIgnoreCase("+52") || country.equalsIgnoreCase("52")) {
				return "MX";
			} else if (country.equalsIgnoreCase("+64") || country.equalsIgnoreCase("64")) {
				return "NZ";
			} else {
				return "US";
			}

		} catch (Exception e) {
			return "NA";
		}
	}

	@Override
	public boolean getFreeTrialDays(String chargebeeid) {
		return userDao.getFreeTrialDays(chargebeeid);
	}

	@Override
	public JValidateString validatePassword(String password) {
		JValidateString validatePassword = new JValidateString();
		try {

			String msg = "NA";
			boolean status = true;

			Pattern pattern = Pattern.compile("[^0-9a-zA-Z@#$&+=!*_\\-.]");
			Matcher matcher = pattern.matcher(password);

			if (matcher.find()) {
				msg = "Only allowed special characters @#$&+=!*_-. for Password";
				status = false;
			}

			// Not used for this release
			if (!Pattern.matches("^.{5,20}$", password)) {
				msg = "Password should be Min 5 characters to Max 20 characters";
				status = false;
			}
//
//	        if( !Pattern.matches(".*[a-z].*",password) ) {
//	            msg = "min one smaller case";	          
//	            status = false;
//	        }
//	        
//	        if( !Pattern.matches(".*[A-Z].*",password) ) {
//	            msg = "min one upper case";
//	            status = false;
//	        }
//	        
//	        if( !Pattern.matches(".*[0-9].*",password) ) {
//	            msg = "min one number";
//	            status = false;
//	        }
//	        
//	        if( !Pattern.matches(".*[@#$%^&+=!*_.].*",password) ) {
//	            msg = "min one special char";
//	            status = false;
//	        }

			validatePassword.setValid(status);
			validatePassword.setMsg(msg);

			return validatePassword;
		} catch (Exception e) {
			return validatePassword;
		}

	}

	@Override
	public JValidateString checkAlphabetOnly(String firstname, String lastname) {
		JValidateString validatePassword = new JValidateString();
		try {

			boolean status = true;
			String msg = "NA";

			if (firstname.length() > 20 || lastname.length() > 20) {
				status = false;
				msg = "First & last names can have max of 20 characters.";
			}

			if (patternMatcher(firstname) || patternMatcher(lastname)) {
				status = false;
				msg = "No special characters allowed for Firstname and Lastname.";
			}

			validatePassword.setValid(status);
			validatePassword.setMsg(msg);

			return validatePassword;
		} catch (Exception e) {
			return validatePassword;
		}
	}

	public boolean patternMatcher(String str) {

		Pattern pattern = Pattern.compile("[^a-zA-Z0-9\\s]");
		Matcher matcher = pattern.matcher(str);
		return matcher.find();
	}

	@Override
	public boolean saveFreshChatId(FreshChat freshChat) {
		return userDao.saveFreshChatId(freshChat);
	}

	@Override
	public FreshChat getFreshChatByUserId(long user_id) {
		return userDao.getFreshChatByUserId(user_id);
	}

	@Override
	public boolean recreateUserInChargebee(String fName, String lName, String email, String phoneNo, String cbId) {
		return userDao.recreateUserInChargebee(fName, lName, email, phoneNo, cbId);
	}

	@Override
	public long getUserGateway(String key, long value) {
		return userDao.getUserGateway(key, value);
	}

	@Override
	public boolean deleteUserById(long userid) {
		return userDao.deleteUserById(userid);
	}

	@Override
	public boolean saveOrUpdateUserDelete(UserDelete user) {
		return userDao.saveOrUpdateUserDelete(user);
	}

	@Override
	public long getUserDlt(long user_id) {
		return userDao.getUserDlt(user_id);
	}

	@Override
	public boolean saveOrUpdateUserDltDetails(UserDltInfo userDlt) {
		return userDao.saveOrUpdateUserDltDetails(userDlt);
	}

	@Override
	public long getUserDltId(String meid) {
		return userDao.getUserDltId(meid);
	}

	@Override
	public List<CountryCodeV4> getCountryCodeList(String req_from) {
		return userDao.getCountryCodeList(req_from);
	}

	@Override
	public String getUserCountryCode(String country) {
		return userDao.getUserCountryCode(country);
	}

	@Override
	public UserMetaData getUserMetaData(long user_id) {
		return userDao.getUserMetaData(user_id);
	}

	@Override
	public boolean enableDisableMarketingNotification(UserMetaData userMetaData) {
		return userDao.enableDisableMarketingNotification(userMetaData);
	}

	@Override
	public int executeQuery(String qry) {
		return userDao.executeQuery(qry);
	}

	@Override
	public boolean removeDeleteRequest(long user_id, boolean isdelete) {
		return userDao.removeDeleteRequest(user_id, isdelete);
	}

	@Override
	public boolean saveEmailOTP(EmailOtp emailOtpObj) {
		return userDao.saveEmailOTP(emailOtpObj);
	}

	@Override
	public boolean validateEmailOTP(String userName, EmailOtp otpObj, int validMinutesForOTP) {
		return userDao.validateEmailOTP(userName,otpObj, validMinutesForOTP);
	}
	
	@Override
	public boolean saveOrUpdateUserMini(long userId,String deviceId,String deviceName, boolean isPetCam) {
		return userDao.saveOrUpdateUserMini(userId,deviceId,deviceName,isPetCam);
	}
	
	@Override
	public List<Object> listUserMiniCam(long userId){
		return userDao.listUserMiniCam(userId);
	}
	
	@Override
	public boolean updateDeleteDeviceStatus(long userId, String deviceId) {
		return userDao.updateDeleteDeviceStatus(userId,deviceId);
	}

	@Override
	public ArrayList<String> getUserFirebaseEvents(long userid) {
		return userDao.getUserFirebaseEvents(userid);
	}

	@Override
	public ArrayList<String> checkReferralEligible(long userid) {
		return userDao.checkReferralEligible(userid);
	}
	
	@Override
	public ArrayList<String> checkProductBasedEligible(long userid) {
		return userDao.checkProductBasedEligible(userid);
	}
	
	@Override
	public ArrayList<String> checkActivateEligible(long userid){
		return userDao.checkActivateEligible(userid);
	}
	
	@Override
	public void updateUserTimezone(long userid, String timezone) {
		userDao.updateUserTimezone(userid,timezone);
	}
	
	@Override
	public void updateAddonTomorrowStatus(long gatewayid, boolean status) {
		userDao.updateAddonTomorrowStatus(gatewayid,status);
	}
	
	@Override
	public void updateDeviceNotoficationStatus(long gatewayid, boolean status,long userId, boolean habit_notification) {
		userDao.updateDeviceNotoficationStatus(gatewayid,status,userId,habit_notification);
	}

	@Override
	public boolean checkDeviceNotify(long userId, long gatewayid) {
		return userDao.checkDeviceNotify(userId, gatewayid);
	}

	@Override
	public boolean delUserGatewayV2(long user_id, long gateway_id) {
		return userDao.delUserGatewayV2(user_id, gateway_id);
	}
	
	@Override
	public List<Object> listUserPetCam(long userId){
		return userDao.listUserPetCam(userId);
	}

	@Override
	public boolean checkActiveSubsForAmazonReview(long user_id) {
		return userDao.checkActiveSubsForAmazonReview(user_id);
	}

	@Override
	public ArrayList<String> checkAmazonRatingEligible(long userid) {
		return userDao.checkAmazonRatingEligible(userid);
	}

	@Override
	public boolean updateFirebaseCnt(long userid,int view, int click, String event_id) {
		return userDao.updateFirebaseCnt(userid,view, click, event_id);
	}

	@Override
	public UserTimeZone getUserTimeZone(long user_id) {
		return userDao.getUserTimeZone(user_id);
	}
	
	@Override
	public boolean checkHabitalertNotify(long userId, long gatewayid) {
		return userDao.checkHabitalertNotify(userId, gatewayid);
	}
	
	@Override
	public PetCaloriesCalculation checkFoodFeededDetails(long userId, long gatewayid) {
		return userDao.checkFoodFeededDetails(userId, gatewayid);
	}
	
	@Override
	public boolean checkFoodFeededorNot(long userId, long gatewayid) {
		return userDao.checkFoodFeededorNot(userId, gatewayid);
	}
	
	@Override
	public boolean updateSensorlocation(long gatewayid, long sensorloc,String gname) {
		return userDao.updateSensorlocation(gatewayid, sensorloc,gname);
	}

	@Override
	public AskFeature saveOrUpdateAskFeature(AskFeature askFeature) {
		return userDao.saveOrUpdateAskFeature(askFeature);
	}
	
	public boolean updateUserInChargebee(String fromemail, String toemail, String userName) {
		return userDao.updateUserInChargebee(fromemail, toemail, userName);
	}
	
	public List<JSensorAlert> getSensorAlerts(long userid) {
		return userDao.getSensorAlerts(userid);
	}
	
	public boolean checkActiveSubsForMeariDevice(long user_id,long monitorType) {
		return userDao.checkActiveSubsForMeariDevice(user_id,monitorType);
	}
	
	@Override
	public JResponse getAskFeature(long offset, long limit) {
		return userDao.getAskFeature(offset, limit);
	}

	@Override
	public UserLocation getUserLatLon(long id, long gateway_id) {
		return userDao.getUserLatLon(id, gateway_id);
	}

	@Override
	public void updateUserLocation(UserLocation userLocation) {
		userDao.updateUserLocation(userLocation);
	}
	
	@Override
	public UserFeedbackTransaction getUserFeedbackTransactionWithMonitor(long userid, long formid, long monitorType) {
		return userDao.getUserFeedbackTransactionWithMonitor(userid, formid, monitorType);
	}
		
	@Override
	public String checkInappNotify(long gatewayid) {
		return userDao.checkInappNotify(gatewayid);
	}
	
	@Override
	public List<JAlertV4> getUnackAlertsByGateway(long userid, String tempUnit,String monitortype, long gatewayid) {
		return userDao.getUnackAlertsByGateway(userid,tempUnit,monitortype,gatewayid);
	}

	@Override
	public void updateEvent(InAppEvent inAppEvent) {
		userDao.updateEvent(inAppEvent);
    }

	@Override
	public User verifyUser(String key, String value) {
		return userDao.verifyUser(key, value);
	}

	@Override
	public boolean saveUserOTPVerify(UserOtpVerification otpVerify) {
		return userDao.saveUserOTPVerify(otpVerify);
	}

	@Override
	public boolean validateUserOTP(UserOtpVerification otpVerification, int validMinutesForOTP) {
		return userDao.validateUserOTP(otpVerification, validMinutesForOTP);
	}

	@Override
	public boolean isVetChatUser(long userId) {

		return userDao.isVetChatUser(userId);
	}

	@Override
	public ArrayList<String> checkPetMonitorPopupEligible(long userId) {

		return userDao.checkPetMonitorPopupEligible(userId);
	}

	@Override
	public ArrayList<String> isN7PopupEligible(long userId) {

		return userDao.isN7PopupEligible(userId);
	}


	@Override
	public int checkWithInOtpTimeLimit(String tableName,String whereKey ,String userValue) {

		return userDao.checkWithInOtpTimeLimit(tableName,whereKey,userValue);
	}

	@Override
	public int getOtpTimeLimit(String tableName,String whereKey ,String userValue) {

		return userDao.getOtpTimeLimit(tableName,whereKey,userValue);
	}

	@Override
	public boolean getFirstHitTimeDiff(String tableName, String whereKey, String userValue){

		return userDao.getFirstHitTimeDiff(tableName,whereKey,userValue);
	}

	@Override
	public void saveFirstHitTimeDiff(String tableName, String whereKey, String userValue){
		 userDao.saveFirstHitTimeDiff(tableName,whereKey,userValue);
	}

	@Override
	public boolean updateFullUserName(User user, long userId) {
		return userDao.updateFullUserName(user, userId);
	}

	@Override
	public boolean updateUserPlanPreference(UserPlanPreference userPlanPreference) {
		return userDao.updateUserPlanPreference(userPlanPreference);
	}

    @Override
    public String getChargebeeid(long userId) {
        return userDao.getChargebeeid(userId);
    }
}
