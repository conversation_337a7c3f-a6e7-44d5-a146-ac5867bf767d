package com.nimble.irisservices.service.impl;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IPetFitDao;
import com.nimble.irisservices.dao.impl.AccountDaoImpl;
import com.nimble.irisservices.entity.PetFitDailyReport;
import com.nimble.irisservices.service.IPetFitService;

@Service
@Transactional
public class PetFitServiceImpl implements IPetFitService {

	private static final Logger log = LogManager.getLogger(AccountDaoImpl.class);
	
	@Autowired
	IPetFitDao iPetFitDao;
	
	@Override
	public PetFitDailyReport getReport(String date, String gateway_Id) {
		// TODO Auto-generated method stub
		return iPetFitDao.getReport(date, gateway_Id);
	}

	
}
