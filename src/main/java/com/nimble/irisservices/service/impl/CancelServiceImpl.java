package com.nimble.irisservices.service.impl;

import java.util.List;

import com.nimble.irisservices.entity.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.ICancelDao;
import com.nimble.irisservices.service.ICancelService;

@Service
@Transactional
public class CancelServiceImpl implements ICancelService {

	private static final Logger log = LogManager.getLogger(CancelServiceImpl.class);
	
	@Autowired
	ICancelDao cancelDao;

	@Override
	public List<CancelFeedback> getAllCancelFeedback() {
		return cancelDao.getAllCancelFeedback();
	}

	@Override
	public List<CancelFeedbackContent> getAllCancelFeedbackContent() {
		return cancelDao.getAllCancelFeedbackContent();
	}

	@Override
	public UserCancelFeedBack getUserCancelFeedBackByUserId(long user_id) {
		return cancelDao.getUserCancelFeedBackByUserId(user_id);
	}

	@Override
	public UserCancelFeedBack saveOrUpdateUserCancelFeedBack(UserCancelFeedBack userCancelFeedBack) {
		return cancelDao.saveOrUpdateUserCancelFeedBack(userCancelFeedBack);
	}

	@Override
	public CancelFeedback getCancelFeedbackById(long cancel_feedback_id) {
		return cancelDao.getCancelFeedbackById(cancel_feedback_id);
	}

	@Override
	public AdditionBenefitsCancelReward getAdditionBenefitsCancelReward(long user_id) {
		return cancelDao.getAdditionBenefitsCancelReward(user_id);
	}

	@Override
	public AdditionBenefitsCancelReward saveOrUpdateAdditionBenefitsCancelReward(
			AdditionBenefitsCancelReward additionBenefitsCancelReward) {
		return cancelDao.saveOrUpdateAdditionBenefitsCancelReward(additionBenefitsCancelReward);
	}

	@Override
	public UserCancelFeedBackHistory saveOrUpdateUserCancelFeedBackHistory(
			UserCancelFeedBackHistory userCancelFeedBackHistory) {
		return cancelDao.saveOrUpdateUserCancelFeedBackHistory(userCancelFeedBackHistory);
	}

	@Override
	public boolean deleteUserCancelFeedBack(long user_id) {
		return cancelDao.deleteUserCancelFeedBack(user_id);
	}

	@Override
	public UserCancelFeedBack getUserCancelFeedBackByShowCancel(long user_id, boolean show_cancel_sub) {
		return cancelDao.getUserCancelFeedBackByShowCancel(user_id, show_cancel_sub);
	}
	
	@Override
	public List<NotTravelContent> getAllNotTravelContent() {
		return cancelDao.getAllNotTravelContent();
	}

	@Override
	public List<NotHappyServiceContent> getAllNotHappyServiceContent() {
		return cancelDao.getAllNotHappyServiceContent();
	}

	@Override
	public List<CancelFeedbackContentList> getAllCancelFeedbackContentList(boolean is_flexi, long periodid) {
		return cancelDao.getAllCancelFeedbackContentList(is_flexi,periodid);
	}
	
}
