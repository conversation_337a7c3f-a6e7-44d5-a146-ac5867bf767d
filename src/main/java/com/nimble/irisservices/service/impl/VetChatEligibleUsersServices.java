package com.nimble.irisservices.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.impl.VetChatEligibleUsersDaoImpl;

@Service
@Transactional
public class VetChatEligibleUsersServices {

	@Autowired
    private VetChatEligibleUsersDaoImpl vetChatEligibleUsersDaoImpl;

    public boolean isUserEligible(String email){
        return vetChatEligibleUsersDaoImpl.existsByEmail(email);
    }

}
