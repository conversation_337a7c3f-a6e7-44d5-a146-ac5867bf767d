package com.nimble.irisservices.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.nimble.irisservices.dto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import com.chargebee.org.json.JSONObject;
import com.nimble.irisservices.entity.EmailOtp;
import com.nimble.irisservices.entity.OneTimePassword;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Email;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.ICommonService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.IUserServiceV4;

import freemarker.template.Template;

@Service
public class CommonServiceImpl implements ICommonService {

	/*
	 * This Service used API for both external and app controllers
	 */

	private static final Logger log = LogManager.getLogger(CommonServiceImpl.class);

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;
	
	@Autowired
	IGatewayServiceV4 gatewayServiceV4;

	@Value("${valid_minutes_for_OTP}")
	private int validMinutesForOTP;

	@Autowired
	Helper _helper;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	IMessagingService messagingService;

	@Autowired
	freemarker.template.Configuration templates;

	@Value("${ip_locator_domain}")
	private String ip_locator_domain;

	@Autowired
	Email email_helper;
	
	@Override
	public JResponse validateOneTimePassword(String auth, long otp, String userName, String request_from) {
		JResponse response = new JResponse();

		UserV4 user = null;
		try {
			if (request_from.equalsIgnoreCase("forgotpass")) {
				try {
					user = userServiceV4.verifyAuthV4("username", userName);
				} catch (InvalidAuthoException e) {
					log.error("Invalid Authkey :" + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "Invalid Username");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
				log.info(" user id : " + user.getId());
			}

			OneTimePassword otpObj = new OneTimePassword();
			EmailOtp emailOtpObj = new EmailOtp();
			boolean validOTP = false;

			if (request_from.equalsIgnoreCase("verify_order") || request_from.equalsIgnoreCase("signup") || request_from.equalsIgnoreCase("registration")) {
				emailOtpObj.setEmail(userName);
				emailOtpObj.setOtp((int) otp);
				validOTP = userServiceV4.validateEmailOTP(userName, emailOtpObj, validMinutesForOTP);
			} else {
				otpObj.setOtp((int)otp);
				otpObj.setUserId(user.getId());
				validOTP = userServiceV4.validateOTP(user.getId(), otpObj, validMinutesForOTP);
			}

			log.info(" is valid OTP : " + validOTP);

			if (validOTP) {
				response.put("Status", 1);
				response.put("Msg", "Success");
			} else {
				response.put("Status", 0);
				response.put("Msg", "OTP invalid. Please check your sms/email and try again.");
			}
		} catch (Exception e) {
			log.error(" Error in validateOneTimePassword " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Failed");
		}
		return response;
	}

	@Override
	public JResponse validateCurrentPassword(UserV4 user, String currentPassword) {
		JResponse response = new JResponse();
		try {

			String password = _helper.base64Decoder(currentPassword);
			if (password == null) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session. Please try again later");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			boolean passwordMatch = _helper.checkUserCredencial(password, user.getPassword(), user.getPassword_ver());

			if (passwordMatch) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			} else {
				response.put("Status", 0);
				response.put("Msg", "Current Password is Incorrect.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

		} catch (Exception ex) {

			response.put("Status", 0);
			response.put("Msg", "Invalid Session, Please try again later");
			response.put("Error", ex.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;

		}
	}

	@Override
	public JResponse passwordUpdateV4(UserV4 user, UpdatePassword udatePassword) {
		log.info("Entered into passwordUpdateV4 :: username : "+ udatePassword.getUsername()+" :: password : "+udatePassword.getPassword());
		JResponse response = new JResponse();
		try {
			String userName = udatePassword.getUsername();
			String password = _helper.base64Decoder(udatePassword.getPassword());
			if (password == null) {
				response.put("Status", 0);
				response.put("Msg", "Invalid Session. Please try again later");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JValidateString validatePassword = userServiceV4.validatePassword(password);
			if (!validatePassword.isValid()) {
				response.put("Status", 0);
				response.put("Msg", validatePassword.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			String bCryptPassword = _helper.bCryptEncoder(password);

			int status = userServiceV4.updateUserPasswordV2(userName, bCryptPassword);

			if (status <= 0) {
				log.error("Invalid Username : ");
				response.put("Status", 0);
				response.put("Msg", "Password not updated. Please try again later.");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			// update evalidation
			// long userId = userService.getUserByName(userName).getId();
			async.updateEvalidation(user.getId(), password);

			response.put("Status", 1);
			response.put("Msg", "Password updated successfully");
		} catch (Exception e) {
			log.error("Exception : passwordUpdateV4 :" + user.getUsername() + ": " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Password not updated. Please try again later.");
			response.put("Error", e.getLocalizedMessage());
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	@Override
	public JResponse oneTimePassword(String userName, String via, String request_from, String mobileno) {
		JResponse response = new JResponse();
		UserV4 user = null;
		int localOtpTimer=0;
		localOtpTimer=validMinutesForOTP;
		try {
			if (request_from.equalsIgnoreCase("forgotpass")) {
				try {
					user = userServiceV4.verifyAuthV3("username", userName);
				} catch (InvalidAuthoException e) {
					log.error("Invalid Authkey :" + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "Invalid Username");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				if (user != null && user.isDelete_user()) {
					response.put("Status", 0);
					response.put("Msg", "Your account deletion is in process. For more details reach our support team");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				if (!user.isEnable()) {
					response.put("Status", 0);
					response.put("Msg", "Account Disabled. Please contact support!");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				log.info(" user id : " + user.getId());
			}

			long max = 9876;
			long min = 1234;
			int otp = (int) (Math.random() * (max - min + 1) + min);

			// System.out.println(otp);

			OneTimePassword otpObj = new OneTimePassword();
			EmailOtp emailOtpObj = new EmailOtp();

			boolean isOTPSaved = false;

			if (request_from.equalsIgnoreCase("verify_order") || request_from.equalsIgnoreCase("signup") || request_from.equalsIgnoreCase("registration")) {
				int getOtp = userServiceV4.checkWithInOtpTimeLimit("email_verify_otp","email",userName);
				otp=(getOtp==-1)?otp:getOtp;
				emailOtpObj.setEmail(userName);
				emailOtpObj.setOtp(otp);
				emailOtpObj.setCreated_on(_helper.getCurrentTimeinUTC());
				emailOtpObj.setUpdatedOn(_helper.getCurrentTimeinUTC());
				emailOtpObj.setRequest_from(request_from);
				isOTPSaved = getOtp==-1?userServiceV4.saveEmailOTP(emailOtpObj):false;

				if(getOtp!=-1)
				{
					int remainingTimer=0;
					remainingTimer=userServiceV4.getOtpTimeLimit("email_verify_otp","email",userName);
					localOtpTimer=	remainingTimer==-1 ? localOtpTimer : remainingTimer;
				}
			} else {
				int getOtp = userServiceV4.checkWithInOtpTimeLimit("one_time_password","userid",String.valueOf(user.getId()));
				otp=(getOtp==-1)?otp:getOtp;
				otpObj.setOtp(otp);
				otpObj.setCreatedOn(_helper.getCurrentTimeinUTC());
				otpObj.setUpdatedOn(_helper.getCurrentTimeinUTC());
				otpObj.setUserId(user.getId());
				if (request_from.equalsIgnoreCase("forgotpass"))
					request_from = "forget_password";
				otpObj.setRequest_from(request_from);
				isOTPSaved = getOtp==-1?userServiceV4.saveOTP(otpObj):false;

				if(getOtp!=-1)
				{
					int remainingTimer=0;
					remainingTimer=userServiceV4.getOtpTimeLimit("one_time_password","userid",String.valueOf(user.getId()));
					localOtpTimer=	remainingTimer==-1 ? localOtpTimer : remainingTimer;
				}
			}
			log.info(" OTP Generated : " + isOTPSaved + " OTP : " + otp);

			String smsMsg = otp + " is your OTP to reset your Waggle Pet App password.";
			String otpInfo = "";
			String otp_info_content = "";

			String email = "NA";
			String phoneNo = "NA";
			String companyName = "";
			String companyId = "";
			StringBuilder hiddenEmail = new StringBuilder();

			if (via.equalsIgnoreCase("mobileno") && (request_from.equalsIgnoreCase("forget_password") || request_from.equalsIgnoreCase("signup") || request_from.equalsIgnoreCase("registration")) ) {
				
				if (request_from.equalsIgnoreCase("signup") || request_from.equalsIgnoreCase("registration")) {
					mobileno = mobileno.replaceAll("\\s+", "");
					phoneNo = "+" + mobileno;
					smsMsg =  otp + " is your OTP to Waggle Pet App - Verify Your Account";
				}else {
					email = user.getEmail();
					phoneNo = user.getMobileno();
					companyName = companyService.getCompanyById(user.getCmpId()).getName();
					companyId = String.valueOf(user.getCmpId());
				}

				boolean smsSent = messagingService.savePlivoData(phoneNo, smsMsg,
						companyId, companyName, "waggle", "sms");

				phoneNo = _helper.encodeString(phoneNo, 4, '*'); // params ( String, no_of_String_to_visible, encodeChar
																	// )
				otpInfo = "<div style='color:#fff;'><p><center><h3>Verify your SMS</h3>" + "Please enter the 4 digit code sent to " + phoneNo
						+ " </center></p></div>";
				
				otp_info_content = "Verify your SMS # Please enter the 4 digit code sent to \n "+ phoneNo;

			} else {
				String ftl_file_name = "OneTimePassword.ftl";
				String mail_sub = "Reset Password";
				
				if( request_from.equalsIgnoreCase("verify_order") ) {
					email = userName;
					ftl_file_name = "EmailVerification.ftl";
					mail_sub = "Your Secure Code for Order Verification";
				} else if (request_from.equalsIgnoreCase("signup") || request_from.equalsIgnoreCase("registration")) {
					email = userName;
					ftl_file_name = "EmailVerification.ftl";
					mail_sub = "Welcome to Waggle Pet App - Verify Your Account";
				} else {
					email = user.getEmail();
					ftl_file_name = "OneTimePassword.ftl";
					mail_sub = "Reset Password";
				}

				for (int i = 0; i < email.length(); i++)
					hiddenEmail.append((i >= 2 && i < email.indexOf('@')) ? '*' : email.charAt(i));

				otpInfo = "<div style='color:#fff;'><p><center><h3>Verify your Email</h3>" + "Please enter the 4 digit code sent to <br>"
						+ hiddenEmail + " </center></p></div>";
				
				otp_info_content = "Verify your Email # Please enter the 4 digit code sent to \n "+ hiddenEmail;

				Template oneTimePassword = (Template) templates.getTemplate(ftl_file_name);
				Map<String, String> OTPEmailParams = new HashMap<>();
				String minutes = localOtpTimer==1?" minute ":" minutes ";
				OTPEmailParams.put("OTP", String.valueOf(otp));
				OTPEmailParams.put("valid_minutes", localOtpTimer+minutes);

				ResponseEntity<String> newEmailContent = ResponseEntity
						.ok(FreeMarkerTemplateUtils.processTemplateIntoString(oneTimePassword, OTPEmailParams));
				String emailContent = newEmailContent.getBody();
				email_helper.SendEmail_SES(email, null, "<EMAIL>",
						mail_sub, emailContent);

				for (int i = 0; i < email.length(); i++)
					hiddenEmail.append((i >= 2 && i < email.indexOf('@')) ? '*' : email.charAt(i));
			}

			response.put("Status", 1);
			response.put("Msg", "OTP sent successfully");
			response.put("otp_info", otpInfo);
			response.put("otp_info_content", otp_info_content);
			response.put("valid_otp_time", localOtpTimer * 60);
		} catch (Exception e) {
			log.error(" Error in forgetpassword v4 " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "OTP sent failed");
			response.put("Error", e.getMessage());
		}
		return response;
	}

	@Override
	public JResponse findCountry(HttpServletRequest httpRequest, HttpHeaders header) {
		JResponse response = new JResponse();
		try {

			String remoteAddress = httpRequest.getRemoteAddr();
			log.info("remote ip : " + remoteAddress);
			String clientIP = "NA";

			if (header != null && header.containsKey("x-forwarded-for")) {
				log.info("x-forwarded-for ip : " + header.containsKey("x-forwarded-for"));
				clientIP = header.getFirst("x-forwarded-for");
			} else {
				log.info("x-forwarded-for ip not found, So using remote ip : " + remoteAddress);
				clientIP = remoteAddress;
			}

			String ipResponse = _helper.getURL(ip_locator_domain + clientIP);
			JSONObject ipJSONResponse = new JSONObject(ipResponse);
			log.info("IP Response : " + ipResponse);

			if (ipJSONResponse != null) {
				if (ipJSONResponse.has("status") || ipJSONResponse.getString("status").equalsIgnoreCase("success")) {
					if (ipJSONResponse.has("countryCode")) {
						response.put("Status", 1);
						response.put("Msg", "Success");
						response.put("country_code", ipJSONResponse.getString("countryCode"));
						return response;
					}
				}
			}

			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("country_code", "US");
			return response;
		} catch (Exception e) {
			response.put("Status", 1);
			response.put("Msg", "Success");
			response.put("country_code", "US");
		}
		return response;
	}

	@Override
	public ArrayList<JCategory> getProductCategory() {		
		return gatewayServiceV4.getProductCategory();
	}

	@Override
	public ArrayList<JProductWithSubCategory> getProductList() {
		return gatewayServiceV4.getProductList();
	}

}
