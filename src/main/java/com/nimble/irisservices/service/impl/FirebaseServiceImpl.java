package com.nimble.irisservices.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

import javax.annotation.PostConstruct;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.messaging.AndroidConfig;
import com.google.firebase.messaging.AndroidConfig.Priority;
import com.google.firebase.messaging.AndroidNotification;
import com.google.firebase.messaging.ApnsConfig;
import com.google.firebase.messaging.Aps;
import com.google.firebase.messaging.BatchResponse;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.google.firebase.messaging.MulticastMessage;
import com.google.firebase.messaging.Notification;
import com.nimble.irisservices.dto.JFcmNotification;
import com.nimble.irisservices.service.IFirebaseService;

@Service
public class FirebaseServiceImpl implements IFirebaseService {
	
	private static final Logger log = LogManager.getLogger(FirebaseServiceImpl.class);

	@PostConstruct
	public void initialize() {

		try {
			if (FirebaseApp.getApps().isEmpty()) {
				log.info("Initializing Firebase App...");
				File file = ResourceUtils.getFile("classpath:iris3-2cb30-firebase-adminsdk-pkvvs-98c1e11b6d.json");
				FileInputStream serviceAccount = new FileInputStream(file);
				FirebaseOptions options = new FirebaseOptions.Builder()
						.setCredentials(GoogleCredentials.fromStream(serviceAccount))
						.setDatabaseUrl("https://iris3-2cb30.firebaseio.com").build();


				FirebaseApp.initializeApp(options);
			}
		} catch (FileNotFoundException e) {
			log.error("excep :"+e.getLocalizedMessage());
		} catch (IOException e) {
			log.error("Excep:"+e.getLocalizedMessage());
		}
	}

	public int sendNotification(JFcmNotification fcmNotification) {
		log.info("sendNotification : " + fcmNotification.getUserTokenList().size());
		try {
			MulticastMessage message = MulticastMessage.builder()
					.setNotification(Notification.builder().setTitle(fcmNotification.getTitle())
							.setBody(fcmNotification.getBody()).build())
					/* For Android */
					.setAndroidConfig(AndroidConfig.builder().setNotification(AndroidNotification.builder().build())
							.putData("source", fcmNotification.getSource()).putData("title", fcmNotification.getTitle())
							.putData("body", fcmNotification.getBody())
							.putData("imageUrl", fcmNotification.getImageUrl())
							.putData("shortDescription", fcmNotification.getShortDescription())
							.putData("redirectUrl", fcmNotification.getRedirectUrl())
							.putData("alert", fcmNotification.getBody()).putData("alertId", "1")
							.putData("gatewayname", fcmNotification.getTitle())
							.putData("monitor_type_id", fcmNotification.getMonitor_type_id() + "")
							.setPriority(Priority.HIGH).build())
					/* For iOS */
					.setApnsConfig(ApnsConfig.builder().setAps(Aps.builder().build())
							.putCustomData("source", fcmNotification.getSource())
							.putCustomData("title", fcmNotification.getTitle())
							.putCustomData("body", fcmNotification.getBody())
							.putCustomData("imageUrl", fcmNotification.getImageUrl())
							.putCustomData("shortDescription", fcmNotification.getShortDescription())
							.putCustomData("redirectUrl", fcmNotification.getRedirectUrl())
							.putCustomData("monitor_type_id", fcmNotification.getMonitor_type_id()).build())
					.addAllTokens(fcmNotification.getUserTokenList()).build();

			BatchResponse response;

			response = FirebaseMessaging.getInstance().sendEachForMulticast(message);
//			response = FirebaseMessaging.getInstance().sendMulticast(message);
			return response.getSuccessCount();
		} catch (FirebaseMessagingException e) {
			log.error("Error in sending batch message : " + e);
			if (e.getErrorCode().equals("UNIMPLEMENTED")) {
				// Handle the specific case for UNIMPLEMENTED if it's expected from API
				// responses
				log.error("Operation is not implemented.");
			} else {
				// Handle other error codes
				log.error("Other Firebase error: " + e.getLocalizedMessage());
			}
		} catch (Exception e) {
			log.error("sendNotification error: " + e.getLocalizedMessage());
		}
		return 0;
	}
}
