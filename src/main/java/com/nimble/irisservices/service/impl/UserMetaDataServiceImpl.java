package com.nimble.irisservices.service.impl;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.dao.IUserMetaDataDao;
import com.nimble.irisservices.entity.UserMetaData;
import com.nimble.irisservices.service.IUserMetaDataService;


@Service
@Transactional
public class UserMetaDataServiceImpl implements IUserMetaDataService {

	@Autowired
	IUserMetaDataDao userMetaDataDao;
	
	@Override
	public UserMetaData saveUserMetaData(UserMetaData userMetaData) {
		return userMetaDataDao.saveUserMetaData(userMetaData);
	}

	@Override
	public UserMetaData getUserMetaData(long user_id) {
		return userMetaDataDao.getUserMetaData(user_id);
	}

}
