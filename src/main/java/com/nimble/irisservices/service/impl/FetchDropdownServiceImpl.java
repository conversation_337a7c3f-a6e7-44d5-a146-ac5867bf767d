 package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IFetchDropdownDao;
import com.nimble.irisservices.entity.AlertType;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.MessageType;
import com.nimble.irisservices.entity.NotificationType;
import com.nimble.irisservices.entity.OrderChannel;
import com.nimble.irisservices.entity.ReportType;
import com.nimble.irisservices.service.IFetchDropdownService;
@Service
public class FetchDropdownServiceImpl implements IFetchDropdownService{
	
	@Autowired
	IFetchDropdownDao fetchDropdownDao;

	
	@Transactional
	public List<AlertType> getAlertTypes() {
		// TODO Auto-generated method stub
		return fetchDropdownDao.getAlertTypes();
	}

	@Transactional
	public List<ReportType> getReportTypes() {
		// TODO Auto-generated method stub
		return fetchDropdownDao.getReportTypes();
	}

	@Transactional
	public List<MessageType> getMessageTypes() {
		// TODO Auto-generated method stub
		return fetchDropdownDao.getMessageTypes();
	}
	
	@Transactional
	public List<AssetModel> getAssetModel() {
		// TODO Auto-generated method stub
		return fetchDropdownDao.getAssetModel();
	}
	
	@Transactional
	public List<AlertType> getAlertTypesByCmp(long cmpId) {
		// TODO Auto-generated method stub
		return fetchDropdownDao.getAlertTypesByCmp(cmpId);
	}

	@Transactional
	public List<ReportType> getReportTypesByCmp(long cmpId) {
		// TODO Auto-generated method stub
		return fetchDropdownDao.getReportTypesByCmp(cmpId);
	}

	@Override
	@Transactional
	public List<OrderChannel> getOrderChannel() {
		// TODO Auto-generated method stub
		return fetchDropdownDao.getOrderChannel();
	}

	@Override
	@Transactional
	public List<NotificationType> getNotificaitonType() {
		// TODO Auto-generated method stub
		return fetchDropdownDao.getNotificaitonType();
	}
	
	@Transactional
	public boolean saveAssetModel(AssetModel assetModel) {
		return fetchDropdownDao.saveAssetModel(assetModel);
	}

}
