package com.nimble.irisservices.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import com.google.gson.JsonSyntaxException;
import com.nimble.irisservices.dao.IUserDao;
import com.nimble.irisservices.dto.ExternalLogin;
import com.nimble.irisservices.dto.JAllUser;
import com.nimble.irisservices.dto.JRVUser;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JRvPetsafety;
import com.nimble.irisservices.dto.JUser;
import com.nimble.irisservices.dto.JUserDeviceInfo;
import com.nimble.irisservices.dto.Jorder;
import com.nimble.irisservices.dto.SignUp;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyType;
import com.nimble.irisservices.entity.DeviceReplaced;
import com.nimble.irisservices.entity.DeviceReplacedHistory;
import com.nimble.irisservices.entity.ForceUpdate;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.Offlineuserdetails;
import com.nimble.irisservices.entity.OrderMappingDetails;
import com.nimble.irisservices.entity.Role;
import com.nimble.irisservices.entity.SignupType;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserDeviceInfo;
import com.nimble.irisservices.entity.UserToken;
import com.nimble.irisservices.entity.UserVerification;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.exception.InvalidEmailException;
import com.nimble.irisservices.exception.InvalidUsernameException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Device_history;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Ordermap;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.service.ICcplistService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICompanyTypeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IGroupServices;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.ISignTypeService;
import com.nimble.irisservices.service.IThrottlingService;
import com.nimble.irisservices.service.IUserService;

import freemarker.template.Configuration;
import freemarker.template.Template;

@Service	
public class UserServiceImpl implements IUserService {
	private static final Logger log = LogManager.getLogger(UserServiceImpl.class);

	@Autowired
	IUserService userService;

	@Autowired
	ICompanyService companyService;

	@Autowired
	IGroupServices groupService;

	@Autowired
	IGatewayService gatewayService;
	
	@Autowired
	IGatewayServiceV4 gatewayServiceV4;

	@Autowired
	ICompanyTypeService companyTypeServ;

	@Autowired
	IThrottlingService throttlingService;

	@Autowired
	ISignTypeService signTypeService;

	@Autowired
	IUserDao userDao;

	@Autowired
	ICcplistService ccpservice;

	@Autowired
	Configuration templates;

	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	Helper _helper = new Helper();

	@Autowired
	ICreditSystemService crService;

	@Transactional
	public User getUserById(long id) throws InvalidUsernameException {
		return userDao.getUserById(id);
	}

	@Transactional
	public User getUserByName(String username) throws InvalidUsernameException {
		return userDao.getUserByName(username);
	}

	@Transactional
	public User verifyAuthKey(String authKey) throws InvalidAuthoException {
		return userDao.verifyAuthKey(authKey);
	}

	@Transactional
	public List<User> getUser(String userid, long cmpid) throws InvalidUsernameException {
		return userDao.getUser(userid, cmpid);
	}

	@Transactional
	public boolean enabledisableuser(String userid, String status) {
		return userDao.enabledisableuser(userid, status);
	}

	@Transactional
	@Override
	public boolean signUp(SignUp sup, Company scmp) {
		try {
			ThrottlingSettings throtsettings = throttlingService.getThrotSettings(sup.getThrotsettingsid()).get(0);

			CompanyType cmpType = companyTypeServ.getCompanyType(sup.getCmptype_id(), 1).get(0);

			// Appending current time in milli at the end of company name to avoid
			// duplicates
//			long timeMilli = Calendar.getInstance().getTimeInMillis();
//			String cmpName = sup.getCompanyname() + "-" + timeMilli;
//			sup.setCompanyname(cmpName);

//			Company cmp = new Company(sup.getCompanyname(), sup.getSupervisor(), sup.getEmail(), sup.getPhoneno(),
//					sup.getMobileno(), sup.getAddress(), throtsettings, cmpType);
//			Company scmp = companyService.saveCompany(cmp);
//
//			CompanyConfig cfg = new CompanyConfig(scmp);
//			companyService.saveCompanyConfg(cfg);

			Role role = userService.getRole(2);

			String email = sup.getEmail().replaceAll("\\s", "").toLowerCase();
			String username = sup.getUsername().replaceAll("\\s", "").toLowerCase();

			sup.setEmail(email);
			sup.setUsername(username);

			User usr = new User(sup.getUsername(), sup.getPassword(), role, scmp, sup.getEmail(), sup.getMobileno());

			usr.setWebappid(2);
			usr.setMobileappid(2);

			if (sup.getFirstname() != null) {
				usr.setFirstname(sup.getFirstname());
			}
			if (sup.getLastname() != null) {
				usr.setLastname(sup.getLastname());
			}
			if (sup.getZipcode() != null) {
				usr.setZipcode(sup.getZipcode());
			}
			if (sup.getCity() != null) {
				usr.setCity(sup.getCity());
			}
			if (sup.getState() != null) {
				usr.setState(sup.getState());
			}
			if (sup.getState() != null) {
				usr.setState(sup.getState());
			}
			if (sup.getCountry() != null) {
				usr.setCountry(sup.getCountry());
			}

			if (sup.getSignuptype_id() != null && !sup.getSignuptype_id().isEmpty()) {
				if (sup.getSignuptoken() != null) {
					usr.setSignuptoken(sup.getSignuptoken());
				}

				SignupType signupType = signTypeService.getSignType(sup.getSignuptype_id());
				if (signupType != null) {
					usr.setSignupType(signupType);
				}
			} else {
				SignupType signupType = signTypeService.getSignType(1 + "");
				if (signupType != null) {
					usr.setSignupType(signupType);
				}
			}

			if (cmpType.getId() == 3) {
				usr.setMobileappid(3); // RvPet
			} else if (cmpType.getId() == 5) { // restaurant monitoring app
				if (sup.getCmpsubtype_id().equals("2"))
					usr.setMobileappid(5);// background app
				else // default 1
					usr.setMobileappid(4);
			} else if (cmpType.getId() == 6) { // White labels Apps
				usr.setMobileappid(6);
				usr.setWebappid(3);
			}

			usr.setEnable(true);
			usr.setCreatedOn(_helper.getCurrentTimeinUTC());
			usr.setUpdatedOn(_helper.getCurrentTimeinUTC());
			usr.setVerified(false);
			userService.saveUser(usr);

//			Groups group = new Groups();
//			group.setName("Default");
//			groupService.saveORupdateGroups(group, scmp.getId());

			if (cmpType.getId() == 5)
				ccpservice.saveCCP(usr);

			return true;
		} catch (Exception e) {
			log.info("signUp Excp:" + e.getLocalizedMessage());
			return false;
		}
	}

	@Transactional
	public boolean saveOrUpdateJUser(JUser juser) throws DataIntegrityViolationException {

		return userDao.saveOrUpdateJUser(juser);
	}

	@Transactional
	public boolean updateUser(User user) {

		return userDao.updateUser(user);
	}

	@Transactional
	public boolean assignGatewaysToUser(Set<String> gatIds, long userid, User user) {

		Set<Gateway> gats = new HashSet<Gateway>();
		for (String gatewayid : gatIds) {
			if (gatewayid.equals(""))
				break;
			try {
				Gateway gateway = gatewayService.getGateways("", "", "", gatewayid, userid, "").get(0);
				gats.add(gateway);
			} catch (Exception e) {
				continue;
			}
		}
		/*
		 * for(Gateway gateways : user.getGateways()) { gats.add(gateways); }
		 */

		user.setGateways(gats);
		userDao.updateUser(user);

		return false;
	}

	@Transactional
	public boolean saveOrUpdateUserToken(User user, UserToken usertoken) throws DataIntegrityViolationException {
		// TODO Auto-generated method stub
		return userDao.saveOrUpdateUserToken(user, usertoken);
	}

	@Transactional
	public List<UserToken> getUserTokem(String userID) {
		// TODO Auto-generated method stub
		return userDao.getUserToken(userID);
	}

	@Transactional
	public boolean delUserGateway(long cmpid, String asset_id) {
		// TODO Auto-generated method stub
		return userDao.delUserGateway(cmpid, asset_id);
	}

	@Transactional
	public List<User> getUserInRole(String userid, long cmpid, long roleid, String p_roleid)
			throws InvalidUsernameException {
		// TODO Auto-generated method stub
		return userDao.getUserInRole(userid, cmpid, roleid, p_roleid);
	}

	@Transactional
	public boolean getDeviceLicense(Long userid, String deviceName, String deviceType) {
		// TODO Auto-generated method stub
		return userDao.getDeviceLicense(userid, deviceName, deviceType);
	}

	@Transactional
	public List<JRvPetsafety> getRvPetSafetyBlogUrl(long userId, int maxRows) {
		return userDao.getRvPetSafetyBlogUrl(userId, maxRows);
	}

	@Transactional
	public int saveorupdateRvpetsafetyUrl(List<JRvPetsafety> jrvPetsafetylist) {
		return userDao.saveorupdateRvpetsafetyUrl(jrvPetsafetylist);
	}

	@Transactional
	public int saveorupdateRvUser(JRVUser jrvUser) {
		return userDao.saveorupdateRvUser(jrvUser);
	}

	@Transactional
	@Override
	public boolean saveorupdateofflineUserDetails(Offlineuserdetails offlineUser) {
		// TODO Auto-generated method stub
		return userDao.saveorupdateofflineUserDetails(offlineUser);
	}

	@Transactional
	@Override
	public Offlineuserdetails getOfflineUser(Offlineuserdetails offlineUser) {
		// TODO Auto-generated method stub
		return userDao.getOfflineUser(offlineUser);
	}

	@Transactional
	@Override
	public boolean delBlogUrl(long blogId) {
		// TODO Auto-generated method stub
		return userDao.delBlogUrl(blogId);
	}

	@Override
	@Transactional
	public List<UserToken> getUserToken(String userID) {
		// TODO Auto-generated method stub
		return userDao.getUserToken(userID);
	}

	@Override
	@Transactional
	public List<JAllUser> getAllUser() {
		// TODO Auto-generated method stub
		return userDao.getAllUser();
	}

	@Override
	@Transactional
	public boolean updateUserNotification(String userId, String status) {
		// TODO Auto-generated method stub
		return userDao.updateUserNotification(userId, status);
	}

	@Override
	@Transactional
	public boolean deleteUser(User user) {
		// TODO Auto-generated method stub
		return userDao.deleteUser(user);
	}

	@Transactional
	public boolean saveOrUpdateUserDeviceInfo(User user, JUserDeviceInfo deviceInfo, String ipAddress)
			throws DataIntegrityViolationException {
		return userDao.saveOrUpdateUserDeviceInfo(user, deviceInfo, ipAddress);
	}

	@Override
	@Transactional
	public List<UserDeviceInfo> getUserDeviceInfo() {
		// TODO Auto-generated method stub
		return userDao.getUserDeviceInfo();
	}

	@Override
	@Transactional
	public User getUserByEmail(String email) throws InvalidEmailException {
		return userDao.getUserByEmail(email);
	}

	@Override
	@Transactional
	public UserVerification createEmailVerificationToken(User user) {
		// TODO Auto-generated method stub
		return userDao.createEmailVerificationToken(user);
	}

	@Override
	public ResponseEntity<String> verifyEmail(String token) {

		try {

			List<UserVerification> verificationTokens = userDao.findByToken(token);
			if (verificationTokens.isEmpty() || verificationTokens == null) {

				Template t = templates.getTemplate("verifyemailerror.ftl");
				Map<String, String> map = new HashMap<>();
				map.put("ERRORMESSAGE", "<p>Invalid token.<p></br><b>Please contact our support team.</b>");

				return ResponseEntity.badRequest().body(FreeMarkerTemplateUtils.processTemplateIntoString(t, map));
			}

			if (verificationTokens.get(0).getStatus().equalsIgnoreCase("verified")) {

				Template t = templates.getTemplate("verifyemailerror.ftl");
				Map<String, String> map = new HashMap<>();
				map.put("ERRORMESSAGE",
						"<p>Your Email has been already verified.</p></br><p>If you still has issue in SignIn,</br><b>Please contact our support team.</p>");
				return ResponseEntity.badRequest().body(FreeMarkerTemplateUtils.processTemplateIntoString(t, map));

			}

			UserVerification verificationToken = verificationTokens.get(0);

			if (_helper.getDate(verificationToken.getExpiredDateTime())
					.before(_helper.getDate(_helper.getCurrentTimeinUTC()))) {
				Template t = templates.getTemplate("verifyemailerror.ftl");
				Map<String, String> map = new HashMap<>();
				map.put("ERRORMESSAGE",
						"<p>Your token is Expired. Please click the resend verification link to verify your mail address from your account.<p>");

				return ResponseEntity.badRequest().body(FreeMarkerTemplateUtils.processTemplateIntoString(t, map));

			}

			verificationToken.setConfirmedDateTime(_helper.getCurrentTimeinUTC());
			verificationToken.setStatus(verificationToken.STATUS_VERIFIED);
			verificationToken.getUser().setVerified(true);
			userDao.save(verificationToken);

			return ResponseEntity.ok(FreeMarkerTemplateUtils
					.processTemplateIntoString(templates.getTemplate("verifyemailsuccess.ftl"), null));

		} catch (Exception e) {
			log.error("Exception in verifyEmail : "+e.getLocalizedMessage());
			return ResponseEntity.unprocessableEntity()
					.body("Unexpected error occured. Please contact our support team.");
		}

	}

	@Override
	@Transactional
	public User getUserById(String userId) {
		// TODO Auto-generated method stub
		return userDao.getUserById(userId);
	}

	@Transactional
	public JResponse loginViaGoogleOrFacebook(ExternalLogin externalLogin) {
		return userDao.loginViaGoogleOrFacebook(externalLogin);
	}

	@Transactional
	public Map<String, String> getUserId_cmpIdByAuth(String auth) throws InvalidAuthoException {
		return userDao.getUserId_cmpIdByAuth(auth);
	}

	@Override
	@Transactional
	public User verifyAuthKeyV2(String authKey) throws InvalidAuthoException {
		return userDao.verifyAuthKeyV2(authKey);
	}

	@Override
	@Transactional
	public User getUserByCBid(String cbid) {
		return userDao.getUserByCBid(cbid);
	}

	@Transactional
	public int updateLastLoginTypeAndTime(long id, int type, String time) {
		return userDao.updateLastLoginTypeAndTime(id, type, time);
	}

	@Transactional
	public void saveOrderMappingDetails(OrderMappingDetails orderMappingDetails) {
		userDao.saveOrderMappingDetails(orderMappingDetails);
	}

	@Transactional
	public double getCreditAmountBySKU(String sku) {
		return userDao.getCreditAmountBySKU(sku);
	}

	@Transactional
	public OrderMappingDetails getOrderMappingByUser(long userid) {
		return userDao.getOrderMappingByUser(userid);
	}

	@Transactional
	public OrderMappingDetails getOrderMappingById(long id) {
		return userDao.getOrderMappingById(id);
	}

	@Transactional
	public int getRemainingDays(long user_id) {
		return userDao.getRemainingDays(user_id);
	}

	@Transactional
	public long getUserIdByEmail(String email) {
		return userDao.getUserIdByEmail(email);
	}

	@Transactional
	public String deleteUserv2(User user) {
		return userDao.deleteUserv2(user);
	}

	@Transactional
	public User getUserByUNameOrEmail(String email) {
		return userDao.getUserByUNameOrEmail(email);
	}

	@Transactional
	public boolean resetPasswordRequest(UserV4 user) {
		return userDao.resetPasswordRequest(user);
	}

	@Override
	@Transactional
	public Role getRole(long id) {
		return userDao.getRole(id);
	}

	@Override
	@Transactional
	public boolean saveUser(User user) {
		return userDao.saveUser(user);
	}

	@Override
	public boolean updateMeidInOrderMap(String userID, String meid) {
		return niomDbservice.updateMeidInOrderMap(userID, meid);
	}

	@Override
	public JSONObject getNiomGetOrderCount(String orderchannel, String orderid) {

		JResponse response = new JResponse();

		List<Orders> orders = new ArrayList<Orders>();
		List<Ordermap> ordermap = new ArrayList<Ordermap>();

		try {
			orders = niomDbservice.getOrderById(orderchannel, orderid);

			String totalOrderedQuantity = "";

			String totalMappedCount = "";

			if (orders != null && !orders.isEmpty()) {

				log.info("Orders found for given orderchannel and order id : OrderChannel : " + orderchannel
						+ " Order ID  :" + orderid);

				String quantityList[] = orders.get(0).getQuantity().split(":");

				totalOrderedQuantity = quantityList[0];

				ordermap = niomDbservice.checkOrderMappedCount(Long.toString(orders.get(0).getOrder_id()));

				if (ordermap != null) {
					totalMappedCount = Integer.toString(ordermap.size());
				} else {
					totalMappedCount = "0";
				}

				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("Order", orders.get(0));
				response.put("Ordermap", ordermap);
				response.put("Totalordered", totalOrderedQuantity);
				response.put("Totalmapped", totalMappedCount);

				return new JSONObject(response).getJSONObject("response");

			} else {
				response.put("Status", 0);
				log.info("No Orders found for given orderchannel and order id : OrderChannel : " + orderchannel
						+ "Order ID  :" + orderid);
				response.put("Error", "No order found for respective order id");
				return new JSONObject(response).getJSONObject("response");
			}

		} catch (Exception e) {
			response.put("Status", 0);
			log.error("GetMappedOrderCount Exception : " + e.getLocalizedMessage());
			return null;
		}

	}

	@Override
	public JSONObject getInventory(String qrcCode) {

		JResponse response = new JResponse();
		try {
			List<Inventory> inventory = niomDbservice.getInventory(qrcCode);

			if (inventory != null) {
				response.put("Status", 1);
				response.put("Msg", "Success");
				response.put("inventory", inventory);
			} else {
				response.put("Status", 0);
				response.put("Msg", "Error in Getting Device Details");
				return null;
			}
		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Error in Getting Device Details");
			return null;
		}
		return new JSONObject(response).getJSONObject("response");

	}

	@Override
	public boolean orderMapping(String meids, String mapped_date, long devicestateid, String order_id,
			String devicemodel, String subscriptioncreated, String isuserregistered, String warrantyType, long gatewayId) {

		log.info("Entered into orderMapping : meids : " + meids);
		boolean res = false;
		// Check if given meids are valid and are not already mapped
		String validStatus = niomDbservice.checkmeidsvalid(meids, devicemodel);

		if (!validStatus.equalsIgnoreCase("valid")) {
			log.info("orderMapping:: One or more meids are not valid for mapping." + validStatus);
			return false;
		}
		// Update the device status and order_id for given meid in inventory
		boolean inventory_result = niomDbservice.updateInventoryNewMeid("6", meids, order_id);
		String meid_list[] = meids.split(",");
		ArrayList<Long> locationids = new ArrayList<Long>();
		Inventory inventory = niomDbservice.getInventoryByMeid(meids);
		locationids.add(inventory.getDevice_location().getId());
		if (inventory_result) {
			if (locationids != null && !locationids.isEmpty()) {
				for (int i = 0; i < meid_list.length; i++) {
					Device_history device_history = new Device_history(devicestateid, meid_list[i].replaceAll("\'", ""),
							locationids.get(i), mapped_date);

					boolean device_res = niomDbservice.saveDeviceHistory(device_history);
					log.info(" device history saved : " + device_res);
					if (device_res == false) {
						return res;
					}
				}

				boolean map_date_res = niomDbservice.updateOrderMappedDate(mapped_date, order_id);
				log.info(" updated ordermapped date : " + map_date_res);
				if (map_date_res) {
					log.info("vieworder : order_id = " + order_id);
					List<Jorder> jorders = niomDbservice.getJorder(order_id);

					if (jorders.size() > 0 && jorders != null) {

						String name = null;
						String username = null;

						if (jorders.get(0).getBilling_last_name() != "NA")
							name = jorders.get(0).getBilling_first_name() + " " + jorders.get(0).getBilling_last_name();
						else
							name = jorders.get(0).getBilling_first_name();

						if ((jorders.get(0).getBilling_email().length()) > 30) {
							int ind = (jorders.get(0).getBilling_email()).indexOf("@");
							username = (jorders.get(0).getBilling_email()).substring(0, ind);
						} else {
							username = jorders.get(0).getBilling_email();
						}

						for (int i = 0; i < meid_list.length; i++) {
							int orderid = Integer.valueOf(order_id);
							log.info("Input order id :" + order_id);

							Ordermap ordermap = new Ordermap(orderid, jorders.get(0).getDatetime(), name,
									jorders.get(0).getBilling_email(), jorders.get(0).getBilling_phone(),
									meid_list[i].replaceAll("\'", ""), username, "NA", jorders.get(0).getDevicemodel(),
									"1", jorders.get(0).getCustomer_note(), "Activated", "1111-11-11 11:11:11",
									_helper.getCurrentTimeinUTC(), subscriptioncreated, isuserregistered);

							String registered_date = _helper.getCurrentTimeinUTC();
							Gateway gateway = gatewayServiceV4.getGatewayByMeid(meids);
							
							String product="Waggle";
							if( gateway == null ) {
								AssetModel asset = gatewayService.getAssetModelByName( inventory.getDevicemodelnumber() );
								if(asset.getIsgps().equalsIgnoreCase("true") || asset.getIsgps().equalsIgnoreCase("1"))
									product = product+"-GPS";
								else
									product = product+"-Lite";

								ordermap.setMtype_id((int) asset.getMonitor_type().getId());
							} else {
								if(gateway.getModel().getIsgps().equalsIgnoreCase("true") || gateway.getModel().getIsgps().equalsIgnoreCase("1"))
									product = product+"-GPS";
								else
									product = product+"-Lite";

								ordermap.setMtype_id((int) gateway.getModel().getMonitor_type().getId());
							}
							
							ordermap.setProduct_type(product);
							
							if( gateway != null ) {
								Date date = new Date( gateway.getInstalled_date().getTime() );
								SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
								registered_date = df.format(date);
							}
							
							ordermap.setRegistered_date(registered_date);
							boolean result = niomDbservice.saveORupdateMappedOrder(ordermap);

							if (result == false) {
								return res;
							}
						}
						res = true;

						if(!gatewayService.saveWarrantyClaimType(warrantyType, gatewayId)) {
							log.info("Failed to save warranty claim type");
						}
					}
				}
			} else {
				log.info("Location ids are empty");
			}
		}
		return res;

	}

	@Override
	public boolean deleteordermap(String meid, int initialDeviceStateid) {

		boolean status = niomDbservice.deleteMappedOrder(meid);

		boolean inventory_result = niomDbservice.updateInventoryNewMeid(initialDeviceStateid + "", meid, null);

		if (status && inventory_result) {
			return true;
		} else {
			return false;
		}
	}

	@Override
	public boolean updateOrdersDataV2(String orderchannel, Orders order, Inventory inventory) {

		try {
			order.setProvision_status(1);
			order.setEmail_status("111");
			order.setTracking_summary("NA");
			order.setStatus("completed");
			order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
			order.setWelcome_status(order.getWelcome_status());
			if (inventory.getDevicestate().getName().toLowerCase().contains("amazon")) {
				order.setFulfillmentchannel("amazon");
			} else {
				order.setFulfillmentchannel("rv");
			}

			return niomDbservice.saveORupdateOrder(order);

		} catch (JsonSyntaxException e) {
			// TODO Auto-generated catch block
			log.error(e.getLocalizedMessage());
			return false;
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			log.error(e.getLocalizedMessage());
			return false;
		}

	}

	@Override
	@Transactional
	public DeviceReplaced getRecallDeviceDetails(long userId) {
		return userDao.getRecallDeviceDetails(userId); 
	}

	@Override
	@Transactional
	public boolean changeRecallDeviceStatus(long recalUserId, String oldMeid,int isRecallDevice) {
		return userDao.changeRecallDeviceStatus(recalUserId,oldMeid,isRecallDevice);
	}

	@Override
	@Transactional
	public boolean insertRecallDeviceHistory(DeviceReplacedHistory deviceRecallHistory) {
		return userDao.insertRecallDeviceHistory(deviceRecallHistory);
	}

	@Override
	@Transactional
	public ForceUpdate getForceUpdate(String userid) {
		return userDao.getForceUpdate(userid);
	}

	@Override
	@Transactional
	public boolean deleteRVUser(long user_id) {
		return userDao.deleteRVUser(user_id);
	}

	@Override
	@Transactional
	public boolean deleteUserDeviceInfo(long user_id) {
		return userDao.deleteUserDeviceInfo(user_id);
	}

	@Override
	@Transactional
	public boolean deleteUserPushNotifications(long user_id) {
		return userDao.deleteUserPushNotifications(user_id);
	}

	@Override
	@Transactional
	public boolean deleteUserToken(long user_id) {
		return userDao.deleteUserToken(user_id);
	}

	@Override
	@Transactional
	public boolean deleteUserVerification(long user_id) {
		return userDao.deleteUserVerification(user_id);
	}

	@Override
	@Transactional
	public boolean deleteTrendingVideoTransaction(long user_id) {
		return userDao.deleteTrendingVideoTransaction(user_id);
	}

	@Override
	@Transactional
	public boolean deleteUserById(long user_id) {
		return userDao.deleteUserById(user_id);
	}

	@Override
	public boolean deleteAllUserInfo(long user_id, String email) {
		log.info("Entered into deleteAllUserInfo :: email : "+email+" :: user id : "+user_id);
		try {
			
			boolean deleteRVUserStatus = userService.deleteRVUser(user_id);
			log.info("delete RV User Status : "+deleteRVUserStatus);
			boolean deleteUserDeviceInfoStatus = userService.deleteUserDeviceInfo(user_id);
			log.info("delete User Device Info Status : "+deleteUserDeviceInfoStatus);
			boolean deleteUserPushNotificationsStatus = userService.deleteUserPushNotifications(user_id);
			log.info("delete User Push Notifications Status : "+deleteUserPushNotificationsStatus);
			boolean deleteUserTokenStatus = userService.deleteUserToken(user_id);
			log.info("delete User Token Status : "+deleteUserTokenStatus);
			boolean deleteUserVerificationStatus = userService.deleteUserVerification(user_id);
			log.info("delete User Verification Status : "+deleteUserVerificationStatus);
			boolean deleteTrendingVideoTransactionStatus = userService.deleteTrendingVideoTransaction(user_id);
			log.info("delete Trending Video Transaction Status : "+deleteTrendingVideoTransactionStatus);
			boolean deleteUserByIdStatus = userService.deleteUserById(user_id);
			log.info("delete User By Id Status : "+deleteUserByIdStatus);
			
			Company company = companyService.getCompanyByEmail(email);
			groupService.deleteGroups(company.getId());
			companyService.deleteCompanyConfigByCMPId(company.getId());
			companyService.deleteCompany(company.getId());
			
			return true;
		} catch (Exception e) {
			log.info("Error in deleteAllUserInfo :: Error : "+e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	@Transactional
	public UserVerification createEmailVerificationTokenV2(long user_id) {
		return userDao.createEmailVerificationTokenV2(user_id);
	}

	@Override
	@Transactional
	public UserVerification getUserVerificationTokenV2(long user_id) {
		return userDao.getUserVerificationTokenV2(user_id);
	}

	@Override
	public void checkFirstNameAndLastNameInOrder(Orders order, UserV4 user) {
		log.info("Entered into checkFirstNameAndLastNameInOrder :: order_id : "+ order.getOrder_id() + " :: user_id : "+ user.getId());
		try {
			
			if( order.getBilling_first_name().trim().isEmpty() || order.getBilling_last_name().trim().isEmpty() || order.getBilling_first_name().equalsIgnoreCase("NA") || order.getBilling_last_name().equalsIgnoreCase("NA")  ) {
				order.setBilling_first_name( user.getFirstname() );
				order.setBilling_last_name( user.getLastname() );
			} 
			
		} catch (Exception e) {
			log.error("Error in checkFirstNameAndLastNameInOrder :: Error : "+ e.getLocalizedMessage());
		}
	}
	
	@Override
	@Transactional
	public boolean deleteUserGateway(long user_id,long gid) {
		return userDao.deleteUserGateway(user_id,gid);
	}

	@Override
	@Transactional
	public DeviceReplaced getRecallDeviceDetails(long userId,long mType) {
		return userDao.getRecallDeviceDetails(userId,mType);
	}



}
