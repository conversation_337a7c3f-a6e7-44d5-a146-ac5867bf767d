package com.nimble.irisservices.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IDeviceSubscriptionDao;
import com.nimble.irisservices.entity.DeviceSubscriptions;
import com.nimble.irisservices.service.IDeviceSubscriptionService;

@Service
public class DeviceSubscriptionServiceImpl implements IDeviceSubscriptionService {

	
	@Autowired
	IDeviceSubscriptionDao deviceSubscriptionDao;
	
	@Override
	@Transactional
	public DeviceSubscriptions getDeviceSubscription(String meid) {
		// TODO Auto-generated method stub
		return deviceSubscriptionDao.getDeviceSubscription(meid);
	}

	@Override
	@Transactional
	public boolean saveOrUpdateDeviceSubscription(DeviceSubscriptions subscription) {
		// TODO Auto-generated method stub
		return deviceSubscriptionDao.saveOrUpdateDeviceSubscription(subscription);
	}

	

}
