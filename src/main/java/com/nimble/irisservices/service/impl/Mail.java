package com.nimble.irisservices.service.impl;

import java.util.Properties;

import javax.mail.AuthenticationFailedException;
import javax.mail.Authenticator;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
public class Mail {
	private static final Logger log = LogManager.getLogger(Mail.class);

	public static boolean SendMail(String toAddr, String sub, String mailmsg)
    {        
        String login = "<EMAIL>";
        String password ="irisalert123";
       
        try
        {
            Properties props = new Properties();
            props.setProperty("mail.host", "mail.nimblewireless.com");
            props.setProperty("mail.smtp.port", "26");
            props.setProperty("mail.smtp.auth", "true");
            props.setProperty("mail.smtp.starttls.enable", "true");

            Authenticator auth = new SMTPAuthenticator(login, password);
            Session session = Session.getInstance(props, auth);

            MimeMessage msg = new MimeMessage(session);
            msg.setText(mailmsg.trim());
            msg.setSubject(sub.trim());
            msg.setFrom(new InternetAddress(login));
            msg.addRecipient(Message.RecipientType.TO, new InternetAddress(toAddr));
           
            Transport.send(msg);
            log.info("Message sent to "+toAddr);
                       
            return true;
        }
        catch (AuthenticationFailedException ex)
        {
        	log.info("Authentication Exception occured :" + ex.getMessage());
        }
        catch (AddressException ex)
        {
        	log.info("Address Exception occured :" + ex.getMessage());
        }
        catch (MessagingException ex)
        {
        	log.info("Message Exception occured :" + ex.getMessage()); 
        }
        return false;
    }

    private static class SMTPAuthenticator extends Authenticator
    {
        private PasswordAuthentication authentication;

        public SMTPAuthenticator(String login, String password)
        {
            authentication = new PasswordAuthentication(login, password);
        }

        protected PasswordAuthentication getPasswordAuthentication()
        {
            return authentication;
        }
    } 
}
