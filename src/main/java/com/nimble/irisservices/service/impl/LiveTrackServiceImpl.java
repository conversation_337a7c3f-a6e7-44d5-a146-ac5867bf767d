package com.nimble.irisservices.service.impl;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.ILiveTrackDao;
import com.nimble.irisservices.entity.Livetracking;
import com.nimble.irisservices.service.ILiveTrackService;
@Repository
public class LiveTrackServiceImpl implements ILiveTrackService {

	private static final Logger log = LogManager.getLogger(LiveTrackServiceImpl.class);



	@Autowired
	ILiveTrackDao liveTrackDao;

	@Override
	@Transactional
	public boolean saveOrUpdateLiveTrack(Livetracking liveTrackDetails) {
		// TODO Auto-generated method stub
		return liveTrackDao.saveOrUpdateLiveTrack(liveTrackDetails);

	}

	@Override
	@Transactional
	public boolean updateLiveTrack(Livetracking gatewayconfig) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	@Transactional
	public Livetracking getLiveTrackDetails(Long gatewayID) {

		return liveTrackDao.getLiveTrackDetails(gatewayID);

	}

	
	@Override
	@Transactional
	public List<Livetracking> getAllLiveTrackDetails() {
		return liveTrackDao.getAllLiveTrackDetails();
	}

}
