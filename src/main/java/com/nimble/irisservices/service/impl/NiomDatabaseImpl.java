package com.nimble.irisservices.service.impl;

import java.util.Collections;
import java.util.List;

import com.nimble.irisservices.entity.OrderSkuDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.INiomDataBaseDao;
import com.nimble.irisservices.dto.JQrcDetails;
import com.nimble.irisservices.dto.Jorder;
import com.nimble.irisservices.dto.OrderTrackingData;
import com.nimble.irisservices.dto.ShipmentDetailData;
import com.nimble.irisservices.dto.ShipmentDetailV2;
import com.nimble.irisservices.niom.entity.Device_history;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Ordermap;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.service.INiomDataBaseService;
@Service
@Transactional("niomtransactionManager")
public class NiomDatabaseImpl implements INiomDataBaseService{

	@Autowired
	INiomDataBaseDao niomDbDao;
	
	public boolean isMeidMappedInOrdermap(String meid) {
		// TODO Auto-generated method stub
		return niomDbDao.isMeidMappedInOrdermap(meid);
	}

	@Override
	public List<Orders>  getOrderById(String orderChannel, String orderID) {
		// TODO Auto-generated method stub
		return niomDbDao.getOrderById(orderChannel,orderID);
	}

	@Override
	public List<Ordermap> checkOrderMappedCount(String orderID) {
		return niomDbDao.checkOrderMappedCount(orderID);
	}

	@Override
	public boolean saveORupdateOrder(Orders order) {
		return niomDbDao.saveORupdateOrder(order);
	}

	@Override
	public List<Inventory> getInventory(String qrcCode) {
		return niomDbDao.getInventory(qrcCode);
	}

	@Override
	public String checkmeidsvalid(String meids, String devicemodel) {
		return niomDbDao.checkmeidsvalid(meids,devicemodel);
	}

	@Override
	public boolean updateInventoryNewMeid(String deviceState_id, String meids, String order_id) {
		return niomDbDao.updateInventoryNewMeid(deviceState_id,meids,order_id);
	}

	@Override
	public boolean saveDeviceHistory(Device_history device_history) {
		return niomDbDao.saveDeviceHistory(device_history);
	}

	@Override
	public boolean updateOrderMappedDate(String mapped_date, String order_id) {
		return niomDbDao.updateOrderMappedDate(mapped_date,order_id);
	}

	@Override
	public List<Jorder> getJorder(String order_id) {
		return niomDbDao.getJorder(order_id);
	}

	@Override
	public boolean saveORupdateMappedOrder(Ordermap ordermap) {
		return niomDbDao.saveORupdateMappedOrder(ordermap);
	}

	
	@Override
	public boolean updateOrdermapUserDetails(long order_id, String username, String userid, String password) {
		return niomDbDao.updateOrdermapUserDetails(order_id,username,userid,password);
	}

	@Override
	public boolean deleteMappedOrder(String meid) {
		return niomDbDao.deleteMappedOrder(meid);
	}

	@Override
	public boolean updatedevicestateqrccode(String qrc, String meid, int devicestate) {
		return niomDbDao.updatedevicestateqrccode(qrc,meid,devicestate) ;
	}

	@Override
	public Inventory getInventoryByMeid(String meid) {
		return niomDbDao.getInventoryByMeid(meid);
	}

	@Override
	public boolean updateExternalOrdersInNiom(Orders order) {
		return niomDbDao.updateExternalOrdersInNiom(order);
	}

	@Override
	public boolean updateMeidInOrderMap(String userID, String meid) {
		return niomDbDao.updateMeidInOrderMap(userID, meid);
	}

	@Override
	public boolean updateInventoryFotaVersion(String meid, String fota_version) {
		return niomDbDao.updateInventoryFotaVersion(meid, fota_version);
	}

	@Override
	public boolean changeMeidAndReplaceInOrderMap(String oldMeid, String newMeid) {
		return niomDbDao.changeMeidAndReplaceInOrderMap(oldMeid, newMeid);
	}

	@Override
	public boolean enableReplaceInOrderMapByMeid(String meid, long userid) {
		return niomDbDao.enableReplaceInOrderMapByMeid(meid, userid);
	}

	@Override
	public boolean checkMeidIsAvailableInOrderMap(String oldMeid) {
		return niomDbDao.checkMeidIsAvailableInOrderMap(oldMeid);
	}

	@Override
	public boolean disableReplaceInOrderMapByMeid(String meid, long userid) {
		return niomDbDao.disableReplaceInOrderMapByMeid(meid, userid);
	}
	
	@Override
	public Inventory getInventoryByIccid(String iccid) {
		return niomDbDao.getInventoryByIccid( iccid);
	}

	@Override
	public List<Ordermap> getMappedOrderByMeid(String meid) {
		return niomDbDao.getMappedOrderByMeid(meid);
	}

	@Override
	public String getOrderChannelById(int order_id) {
		return niomDbDao.getOrderChannelById(order_id);
	}

	@Override
	public boolean updateInventoryMdn(String msisdn, String iccid) {
		return niomDbDao.updateInventoryMdn(msisdn, iccid);
	}

	@Override
	public String getMeidBySim(String iccid) {
		return niomDbDao.getMeidBySim(iccid);
	}

	@Override
	public boolean updateOrdermapMdn(String msisdn, String meid) {
		return niomDbDao.updateOrdermapMdn(msisdn, meid);
	}
	

	@Override
	public Orders getOrderDetails(long order_id) {
		return niomDbDao.getOrderDetails(order_id);
	}
	
	@Override
	public boolean updateOrdersTable(long order_id, String quantity, int return_units, String account_status) {
		return niomDbDao.updateOrdersTable(order_id, quantity, return_units, account_status);
	}

	@Override
	public boolean updateOrderMap(long order_id, String meid, String updateKey) {
		return niomDbDao.updateOrderMap(order_id, meid, updateKey);
	}

	@Override
	public int getGatewayId(String meids) {
		return niomDbDao.getGatewayId(meids);
	}

	@Override
	public List<ShipmentDetailData> getShipmentDetails(String WhereKey, String value) {
		return niomDbDao.getShipmentDetails(WhereKey, value);
	}
	
	@Override
	public List<OrderTrackingData> getTrackingDetails(String TrackingID) {
		return niomDbDao.getTrackingDetails(TrackingID);
	}

	@Override
	public String getDateTimeByOrderId(String order_id) {
		return niomDbDao.getDateTimeByOrderId(order_id);
	}
	
	
	@Override
	public JQrcDetails getFirmwareAndFotaVersionDeviceModelNumber(String qrc){
		return niomDbDao.getFirmwareAndFotaVersionDeviceModelNumber(qrc);
	}

	@Override
	public List<Ordermap> getMappedOrderByOrderId(String orderId) {
		return niomDbDao.getMappedOrderByOrderId(orderId);
	}

	@Override
	public JQrcDetails getOrderAndUserInfo(String order_id) {
		return niomDbDao.getOrderAndUserInfo(order_id);
	}

	@Override
	public Orders getOrderByEmailOrPhone(long userId, String email, String mobile_no, String qrc) {
		return niomDbDao.getOrderByEmailOrPhone(userId, email, mobile_no, qrc);
	} 
	
	@Override
	public boolean updateOrdermapEmailDetails(String from_email, String to_email) {
		return niomDbDao.updateOrdermapEmailDetails(from_email, to_email);
	}

	@Override
	public int getIsBundle(String order_id, String orderChannel, long mtype_id) {
		return niomDbDao.getIsBundle(order_id, orderChannel, mtype_id); 
	}

	@Override
	public Ordermap getMappedOrderByMeidSql(String meid) {
		return niomDbDao.getMappedOrderByMeidSql(meid);
	} 
	
	@Override
	public String getInventoryBySerialNo(String qrcode) {
		return niomDbDao.getInventoryBySerialNo(qrcode);
	}

	@Override
	public Orders getOrderByOrderId(String orderId, String channel) {
		return niomDbDao.getOrderByOrderId(orderId, channel);
	}

	@Override
	public List<OrderSkuDetails> getSkuDetails() {
		return niomDbDao.getSkuDetails();
	}

	@Override
	public ShipmentDetailV2 getShipmentDetailsV2(String whereKey, String value) {
		return niomDbDao.getShipmentDetailsV2(whereKey, value);
	}

}
