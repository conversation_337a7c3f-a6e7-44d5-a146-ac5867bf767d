package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IMonitorTypeDao;
import com.nimble.irisservices.entity.MonitorType;
import com.nimble.irisservices.service.IMonitorTypeService;

@Service
public class MonitorTypeServiceImpl implements IMonitorTypeService{

	
	@Autowired
	IMonitorTypeDao MonitorTypeDao;
	
	@Transactional
	public boolean updateMonitorType(MonitorType monitorType) {
		
		return MonitorTypeDao.updateMonitorType(monitorType);
	}
	
	@Transactional
	public MonitorType getMonitorTypeById(long id) {
		// TODO Auto-generated method stub
		return MonitorTypeDao.getMonitorTypeById(id);
	}

	@Transactional
	public boolean deleteMonitorType(int id) {
		// TODO Auto-generated method stub
		return MonitorTypeDao.deleteMonitorType(id);
	}

	@Transactional
	public List<MonitorType> getAllMonitorTypes() {
		// TODO Auto-generated method stub
		return MonitorTypeDao.getAllMonitorTypes();
	}

	@Transactional
	public String getMonitorNameById(long id) {
		return MonitorTypeDao.getMonitorNameById(id);
	}

}
