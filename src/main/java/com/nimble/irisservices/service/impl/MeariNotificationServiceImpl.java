package com.nimble.irisservices.service.impl;

import com.nimble.irisservices.dao.IMeariNotificationDao;
import com.nimble.irisservices.entity.MeariNotification;
import com.nimble.irisservices.service.IMeariNotificationService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public class MeariNotificationServiceImpl implements IMeariNotificationService {

    private static final Logger log = LogManager.getLogger(MeariNotificationServiceImpl.class);

    @Autowired
    private IMeariNotificationDao meariNotificationDao;

    @Override
    public boolean saveNotifications(long userID, List<MeariNotification> notificationsList) {

        return meariNotificationDao.saveNotifications(userID, notificationsList);
    }

    @Override
    public List<MeariNotification> getNotifications(long userID) {

        return meariNotificationDao.getNotificationsHistory(userID);
    }
}
