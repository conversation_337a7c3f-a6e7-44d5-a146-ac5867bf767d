package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.impl.VetChatDaoImpl;
import com.nimble.irisservices.dto.JPetprofileFlutter;
import com.nimble.irisservices.entity.VetChatPlan;
import com.nimble.irisservices.entity.VetChatPlanUser;

@Service
@Transactional
public class VetChatServiceImpl {

	@Autowired
	private VetChatDaoImpl vetChatDaoImpl;

	public boolean isUserEligible(String email) {
		return vetChatDaoImpl.existsByEmail(email);
	}

	public List<VetChatPlan> getVetChatPlans() {
		return vetChatDaoImpl.getVetChatPlans();
	}

	public boolean saveVetChatPlan(VetChatPlanUser vetChatPlan) {
		return vetChatDaoImpl.saveVetChatPlan(vetChatPlan);
	}

	public VetChatPlanUser getVetChatUserPlan(long userid) {
		return vetChatDaoImpl.getVetChatUserPlan(userid);
	}

	public boolean updateVetChatPlanProfileId(JPetprofileFlutter jpetprofile) {
		return vetChatDaoImpl.updateVetChatPlanProfileId(jpetprofile);
	}

	public boolean updateVetChatPlan(VetChatPlanUser vetChatPlan) {
		return vetChatDaoImpl.updateVetChatPlan(vetChatPlan);
	}

	public boolean removeEligibleUser(String email) {
		return vetChatDaoImpl.removeEligibleUser(email);
	}

	public VetChatPlan getPlanById(int plan_id) {
		return vetChatDaoImpl.getPlanById(plan_id);
	}

	public boolean removeEligibleUserPlan(long id) {
		return vetChatDaoImpl.removeEligibleUserPlan(id);
	}

	public boolean saveVetChatMail(long userid) {
		return vetChatDaoImpl.saveVetChatMail(userid);
	}

	public boolean getVetChatUserEmail(long userid) {
		return vetChatDaoImpl.getVetChatUserEmail(userid);
	}
}
