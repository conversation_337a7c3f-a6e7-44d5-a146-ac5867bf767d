package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IGeneralConfigDao;
import com.nimble.irisservices.entity.ReferEarn;
import com.nimble.irisservices.service.IGeneralConfigService;
@Service
public class GeneralConfigServiceImpl implements IGeneralConfigService {

	@Autowired
	IGeneralConfigDao generalConfigDao;

	@Override
	@Transactional
	public boolean saveOrUpdateReferEarn(ReferEarn referEarn) {
		// TODO Auto-generated method stub
		return generalConfigDao.saveOrUpdateReferEarn(referEarn);
	}

	@Override
	@Transactional
	public List<ReferEarn> getAllReferEarn() {
		// TODO Auto-generated method stub
		return generalConfigDao.getAllReferEarn();
	}

	@Override
	@Transactional
	public ReferEarn getLatestReferEarn() {
		// TODO Auto-generated method stub
		return generalConfigDao.getLatestReferEarn();
	}

}
