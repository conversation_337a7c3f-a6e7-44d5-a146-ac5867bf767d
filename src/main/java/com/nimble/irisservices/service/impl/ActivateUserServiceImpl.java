package com.nimble.irisservices.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.lang.reflect.Type;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.validation.Valid;

import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.validation.BindingResult;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.appcontroller.ExternalController;
import com.nimble.irisservices.dao.IGatewayDao;
import com.nimble.irisservices.dto.ActivateUser;
import com.nimble.irisservices.dto.ComboPlanInfo;
import com.nimble.irisservices.dto.JAlertCfg;
import com.nimble.irisservices.dto.JCreateGateway;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JGatewayUserDetails;
import com.nimble.irisservices.dto.JGroups;
import com.nimble.irisservices.dto.JProductSubscription;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JValidateString;
import com.nimble.irisservices.dto.SignUp;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Asset;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.CompanyType;
import com.nimble.irisservices.entity.DeviceReplaced;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.Groups;
import com.nimble.irisservices.entity.Offlineuserdetails;
import com.nimble.irisservices.entity.OrderMappingDetails;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidUsernameException;
import com.nimble.irisservices.helper.Email;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Ordermap;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.service.IActivateUserService;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAlertCfgServiceV4;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICompanyTypeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IDropdownServiceV4;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IGroupServices;
import com.nimble.irisservices.service.IMailService;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IOAuth2Service;
import com.nimble.irisservices.service.IRVCentricDetailsService;
import com.nimble.irisservices.service.IRechargeService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IThrottlingService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IZipCodeDetailsService;

import freemarker.template.Template;

@Service
public class ActivateUserServiceImpl implements IActivateUserService {

	
private static final Logger log = LogManager.getLogger(ExternalController.class);
	
	@Autowired
	ICompanyTypeService companyTypeServ;

	@Autowired
	IThrottlingService throttlingService;

	@Autowired
	@Lazy
	IUserService userService;

	@Autowired
	@Lazy
	IUserServiceV4 userServV4;

	@Autowired
	@Lazy
	IUserServiceV4 userServiceV4;

	@Autowired
	@Lazy
	IGatewayService gatewayService;

	@Autowired
	@Lazy
	IGatewayDao gatewayDao;

	@Autowired
	freemarker.template.Configuration templates;

	@Autowired
	@Lazy
	IGroupServices groupservices;

	@Autowired
	@Lazy
	ICompanyService companyService;

	@Autowired
	@Lazy
	INiomDataBaseService niomDbservice;

	@Autowired
	@Lazy
	IAlertCfgService alertCfgService;

	@Autowired
	@Lazy
	private IAsyncService async;

	@Autowired
	IRVCentricDetailsService rvServ;
	
	@Autowired
	@Lazy
	IReportService reportService;

	@Autowired
	@Lazy
	IZipCodeDetailsService iZipCodeDetailsService;

	@Autowired
	IMailService mailService;

	@Autowired
	IRVCentricDetailsService rvcentricServ;

	@Autowired
	@Lazy
	IDropdownServiceV4 dropdownServiceV4;

	@Autowired
	IOAuth2Service oAuth2Service;

	@Autowired
	@Lazy
	ICreditSystemService crService;
	
	@Autowired
	@Lazy
	IRechargeService reService;
	
	@Value("${show_orderid_popup_while_register}")
	private boolean show_warranty_popup_config;

	@Value("${warranty_msg}")
	private String warranty_msg;

	@Value("#{${supportcontactnumber}}")
	private Map<String,String> supportContactNumber;
	
	@Value("#{${supportemail}}")
	private Map<String,String> supportContactEmail;

	@Value("${add_free_vpm}")
	private boolean addFreeVpmFlag;

	@Value("${vpm_freedays}")
	private String freeVPM_days;

	@Value("${google.api.key}")
	private String googleAPIkey;

	@Value("${config.oauth2.clientid.app}")
	private String clientIdApp;

	@Value("${config.oauth2.clientsecret.app}")
	private String clientSecretApp;

	@Value("${config.oauth2.clientid.web}")
	private String clientIdWeb;

	@Value("${config.oauth2.clientsecret.web}")
	private String clientSecretWeb;

	@Autowired
	Helper _helper;

	@Value("${validation_authkey}")
	private String validation_authkey = "NA";

	@Value("#{${terms_conditions}}")
	private Map<String,String>  terms_conditions;

	@Value("#{${Privacy_policy}}")
	private Map<String,String> privacy_policy;

	@Value("${nimbleauthkey}")
	private String nimbleAuthKey;

	@Value("${Facebook}")
	private String facebook;

	@Value("${amplitude_andriod}")
	private String amplitude_andriod;

	@Value("${amplitude_ios}")
	private String amplitude_ios;
	
	@Value("${enable_sku_based_vsim_activation}")
	private boolean enableSkuBasedActivation;

	private boolean enablegoogle = true;

	private boolean enablefb = true;

	private boolean enableapple = true;

	private boolean enable_tips = true;

	private String plan_version = "V1";

	@Value("${plivono}")
	private String plivoNumber;

	@Value("${valid_minutes_for_OTP}")
	private int validMinutesForOTP;

	@Value("${return.login.username}")
	private String returnLoginUsername;

	@Value("${subscription.buynow.popup}")
	private boolean subscriptionBuyNowPopUp_config;

	@Value("${subscription.buynow.content}")
	private String subscriptionBuyNowContent;

	@Value("${chatbot}")
	private boolean chatbot;

	@Value("${check.recall.device.qrc}")
	private boolean checkRecallQRC;

	@Value("${product_subs_enable}")
	private boolean product_subs_enable;

	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;

	@Value("${showpopupregisteration}")
	private boolean showPopUpRegisteration;

	private boolean show_orderid = true;

	@Value("${show.subs.page.first.config}")
	private boolean show_subs_page_first_config = true;

	@Value("${redirect.payment.page}")
	private boolean redirectPaymentPage_config = false;

	@Value("${redirect.payment.page.content}")
	private String redirectPaymentPageContent;

	@Value("${orderid.later.popup}")
	private boolean orderid_later_popup_config = false;

	@Value("${orderid.later.popup.content}")
	private String orderid_later_popup_content;
	
	@Value("${nav_to_warranty_scr}")
	private boolean nav_to_warranty_scr_config;
	
	@Value("${n13_registration_content}")
	private String n13_registration_content;

	@Value("${wagglehooks_url}")
	private String wagglehooks_url;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	@Lazy
	IChargebeeService cbService;
	
	@Autowired
	@Lazy
	IMessagingService  messagingService;
	
	@Autowired
	@Lazy
	IAlertCfgServiceV4 alertCfgServiceV4;
	
	@Autowired
	@Lazy
	IActivateUserService activateUserService;
	
	@Autowired
	Email email_helper;
	
	@Override
	public JResponse registerProduct(@Valid ActivateUser activateUser, BindingResult result,boolean giveToken) {
		JResponse response = new JResponse();
		
//		response = checkBundleSubscription(response, activateUser, 9901l, "AzZiyNTRl1JNb9Avt", true, Long.parseLong(activateUser.getOrderid()), activateUser.getEmail(), 0, "");
//		return response;
		boolean show_orderid = activateUser.isShow_orderid();
		boolean show_warranty_popup = show_warranty_popup_config;
		boolean subscriptionBuyNowPopUp = subscriptionBuyNowPopUp_config;
		boolean redirectPaymentPage = redirectPaymentPage_config;
		boolean show_subs_page_first = show_subs_page_first_config;
		boolean orderid_later_popup = orderid_later_popup_config;
		boolean nav_to_warranty_scr = nav_to_warranty_scr_config;
		boolean orderid_given = false;
		long user_id = 0;
		
		if (show_orderid) { // with order id
			show_warranty_popup = false;
			orderid_later_popup = false;
			nav_to_warranty_scr = false;
//			show_subs_page_first = true;    ** no need it will take config value **

//			if( bundledOrderIdList.contains(activateUser.getOrderid()) && subscriptionBuyNowPopUp ) {
//				subscriptionBuyNowPopUp = false;
////				redirectPaymentPage = true; ** no need it will take config value **
//			} else {
//				redirectPaymentPage = false;
//			}

		} else { // without order id

//			show_subs_page_first = false;     ** no need it will take config value **
			redirectPaymentPage = false;

			if (show_subs_page_first) {
				orderid_later_popup = false;
			}

		}

		if (activateUser.getLat() == 270.0 && activateUser.getLon() == 270.0) {
			double lat = 32.8914656, lon = -117.1506437;
			activateUser.setLat(lat);
			activateUser.setLon(lon);
		}
		
		JValidateString validString = userServiceV4.checkAlphabetOnly(activateUser.getFirstname(),
				activateUser.getLastname());
		if (!validString.isValid()) {
			response.put("Status", 0);
			response.put("Msg", validString.getMsg());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		if (activateUser.isPwd_update()) {

			String pwd = activateUser.getPassword();
			if (activateUser.getPassword_decode_ver().equalsIgnoreCase("V2")) {
				pwd = _helper.base64Decoder(activateUser.getPassword());
				if (pwd == null) {
					response.put("Status", 0);
					response.put("Msg", "Invalid Session. Please try again later");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			}

			activateUser.setPassword(pwd);
			JValidateString validatePassword = userServiceV4.validatePassword(pwd);
			if (!validatePassword.isValid()) {
				response.put("Status", 0);
				response.put("Msg", validatePassword.getMsg());
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		}
	
		/**Get country from App */
		String support_country = activateUser.getCountry();
		if( support_country == null || support_country.isEmpty() || support_country.equalsIgnoreCase("US")|| support_country.equalsIgnoreCase("NA")
				|| support_country.toLowerCase().contains("india") || support_country.equalsIgnoreCase("in")
			 ) {
			support_country = "US";
		}
		String supportM = supportContactEmail.get(support_country);
		String supportP = supportContactNumber.get(support_country);

		int initialDeviceStateid = 9;
		int model_id = 6;
		long monitortypeid = 0;
		String to_address = null;
		String cc_address = null;
		String bcc_address = null;
		String alertCreationStatus = null;
		String orderChannel = null;
		String subscriptionStatus = "0";
		String petName = activateUser.getGatewayName().trim();
		String orderID = activateUser.getOrderid().trim();
		String companyType = activateUser.getCmptype_id();
		String eRes = RegisterUserError.commonErrorMessage;
		String errorMsg = eRes.replace("#SP#", supportP).replace("#SM#", supportM);
		String qrcCode = activateUser.getQrcCode().trim();
		String errorResponse = "No Error";
		String errorCode = "ER000";
		String passwordType = "1";
		String sensor_available = null;
		String userName = null;
		String mobilePage = null;
		String userEmail = activateUser.getEmail().trim();
		userEmail = userEmail.toLowerCase();
		userEmail = userEmail.replaceAll("\\s+", "");
		String userPhone = activateUser.getPhoneno().trim();
		userPhone = userPhone.replace("(", "");
		userPhone = userPhone.replace(")", "");
		Gateway replacedGateway = null;

		/**Get country from App */
		String country = "NA";
		if (activateUser.getCountry().equalsIgnoreCase("NA")) {
			/**For old app country key is not available. So get country using phone number*/
			country = userServiceV4.getContryByPhoneNo(userPhone);
			if (country.equalsIgnoreCase("NA"))
				country = "US";
		} else
			country = activateUser.getCountry();
		
		int index = StringUtils.ordinalIndexOf(userPhone, "-", 2);
		if (index > 0) {
			userPhone = userPhone.substring(0, index) + userPhone.substring(index + 1);
		}

		userPhone = userPhone.replaceAll("\\s+", "");

		Long group_id = null;

		boolean activateSim = false;
		boolean updateExternalOrderDataStatus = false;
		boolean accountActivatedStatus = false;
		boolean orderMappedtatus = false;
		boolean activateSimStatus = false;
		boolean isTestUserDevice = false;
		boolean isQrcUserDevice = false;
		boolean provisionStatus = false;
		boolean loginCreationIssue = false;
		boolean gatewayCreationIssue = false;
		boolean accountCreated = false;
		String monitortype = "NA";
		boolean isAlreadyUser = Boolean.parseBoolean(activateUser.getAlreadyuser());
		boolean enableDelayFreq = false;
		int delayFreqSecs = 0;

		Properties prop = new Properties();
		Gson gson = new Gson();
		Orders order = new Orders();
		Inventory inventory = new Inventory();
		Helper _helper = new Helper();
		User intialUser = new User();
		User orderMapUser = null;
		long createuser_id = 0l;
		int devicecount = 0;

		long recalUserId = 0;
		String oldMeid = "NA";
		int isRecallDevice = 0;
		long gatewayId = 0;

		if (result.hasErrors()) {
			return activateUserValidation(result);
		}

		try {
			try {
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));
				to_address = prop.getProperty("to_address");
				cc_address = prop.getProperty("cc_address");
				bcc_address = prop.getProperty("bcc_address");

			} catch (Exception e) {
				log.info("signup:::" + e.getMessage() + "" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", errorMsg);
				errorCode = "ER001";
				errorResponse = RegisterUserError.ER001;
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			if (checkRecallQRC && activateUser.isCheck_recall_qrc()) {
				boolean qrcAvailable = gatewayServiceV4.checkRecallQRC(activateUser.getQrcCode());
				if (qrcAvailable) {
					errorCode = "ER049";
					response.put("Status", 0);
					response.put("recall_qrc", true);
					response.put("recall_qrc_msg", RegisterUserError.recallQRCInfoMsg);
					response.put("upgrade_msg", RegisterUserError.contectMsgNormalTxt);
					response.put("Msg", RegisterUserError.recallQRCMsg);
					errorResponse = "Old device need to recall";
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			}

			// Validate QRC Code
			if (!(qrcCode.matches("[a-zA-Z0-9]+")) || (qrcCode.length() != 6) || qrcCode.startsWith("8")) {
				log.info("Invalid QRCode . " + qrcCode);
				response.put("Status", 0);
				response.put("Msg", RegisterUserError.ER048);
				errorCode = "ER048";
				errorResponse = RegisterUserError.ER048;
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			mobilePage = activateUser.getMobilepage();

			if (mobilePage.equalsIgnoreCase("login") && !isAlreadyUser) {
				if (!_helper.isValidEmail(activateUser.getEmail().trim())) {
					response.put("Status", 0);
					response.put("Msg", "Please enter valid Email!");
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			}
			if (mobilePage.equalsIgnoreCase("account")) {

				if (activateUser.getUsername().trim().equals("") || activateUser.getUsername().trim().isEmpty()) {
					log.info("Username for product registration from account page is empty.");
					response.put("Status", 0);
					response.put("Msg", errorMsg);
					errorCode = "ER043";
					errorResponse = RegisterUserError.ER043;
					response.put("Return Time", System.currentTimeMillis());
					return response;
				} else {
					userName = activateUser.getUsername().trim();
				}
			} else if (mobilePage.equalsIgnoreCase("home") && !isAlreadyUser) {

				if (!activateUser.getQrcCode().trim().equals(activateUser.getUsername())) {
					response.put("Status", 0);
					response.put("Msg", RegisterUserError.ER047);
					errorCode = "ER047";
					errorResponse = RegisterUserError.ER047;
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				userName = activateUser.getQrcCode().trim();
			} else {
				userName = activateUser.getEmail().trim();
				userName = userName.toLowerCase();
				userName = userName.replaceAll("\\s+", "");
			}

			orderChannel = setOrderChannel(activateUser);
			if (show_orderid) {
				if (orderChannel.equalsIgnoreCase("rv")) {
					subscriptionStatus = "1";
				} else if (activateUser.getPurchasedfrom().equalsIgnoreCase("others")) {
					response.put("Status", 0);
					String eRes1 = RegisterUserError.otherUserMessage;
					String msg = eRes1.replace("#SP#", supportP).replace("#SM#", supportM);
					response.put("Msg", msg);
					errorCode = "ER042";
					errorResponse = RegisterUserError.ER042;
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			}

			log.info("Validating Pet Name");
			if (isAlreadyUser) {
				User petUser = new User();
				try {
					petUser = userService.getUserByName(userName);
					recalUserId = petUser.getId();
					DeviceReplaced deviceReplaced = userService.getRecallDeviceDetails(petUser.getId());

					if (deviceReplaced != null) {
						oldMeid = deviceReplaced.getMeid();
						isRecallDevice = deviceReplaced.getIsReplaced();

						if (isRecallDevice == 1) {
							show_warranty_popup = false;
						}
					}

					List<JGateway> userGateway = gatewayService.getGateway(null, null, null, null, petUser.getId(),
							null);

					if (userGateway != null && userGateway.size() > 0) {
						final String tempOldMeid = oldMeid;

						if (!userGateway.stream()
								.anyMatch(gateway -> gateway.getMeid().equalsIgnoreCase(tempOldMeid))) {
							isRecallDevice = 0;
							oldMeid = "NA";
						}

						if (userGateway.size() >= 5 && !(isRecallDevice > 0)) {
							response.put("Status", 0);
							response.put("Msg", RegisterUserError.deviceCountMessage);
							errorCode = "deviceCountMessage";
							response.put("Return Time", System.currentTimeMillis());
							errorResponse = "Device Count Restriction : " + RegisterUserError.deviceCountMessage;
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}
						response = checkGateway(userGateway, qrcCode, petName);

						if (response.getResponse().containsValue("ER006")) {
							errorCode = "ER006";
							errorResponse = RegisterUserError.ER006;
							response.put("Return Time", System.currentTimeMillis());
							return response;
						} else {
							int i = 1;
							String newPetname = petName;
							Loop: while (response != null) {
								if (response.getResponse().containsValue("ER044")) {
									newPetname = petName + "-" + i;
									response = checkGateway(userGateway, qrcCode, newPetname);
									i = i + 1;
								} else {
									activateUser.setGatewayName(newPetname);
									break Loop;
								}
							}
						}
					} else {
						isRecallDevice = 0;
						oldMeid = "NA";
					}
				} catch (InvalidUsernameException ex) {
					response.put("Status", 0);
					response.put("Msg", errorMsg);
					errorCode = "ER025";
					errorResponse = "Validating Pet Name : " + RegisterUserError.ER025;
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			}

			// Check if gateway name contains special
			String gateway_name = activateUser.getGatewayName().trim();
			Pattern special = Pattern.compile("[!@#$%&*()+=|<>?{}.,\\[\\]~'\"]");
			Matcher hasSpecial = special.matcher(gateway_name);
			if (hasSpecial.find()) {
				response.put("Status", 0);
				errorCode = "ER009";
				errorResponse = RegisterUserError.ER009;
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			JSONObject jorderIdCheckResponse = new JSONObject();
			if (show_orderid) {// hide order id
				jorderIdCheckResponse = userService.getNiomGetOrderCount(orderChannel, orderID);

				if (jorderIdCheckResponse == null) {
					response.put("Status", 0);
					response.put("Msg", errorMsg);
					log.info("Error Code : ER035. " + RegisterUserError.ER035);
					errorResponse = RegisterUserError.ER035;
					errorCode = "ER035";
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				int orderIdCheckStatus = jorderIdCheckResponse.getInt("Status");

				if (orderIdCheckStatus > 0 ? true : false) {
					order = gson.fromJson(jorderIdCheckResponse.getJSONObject("Order").toString(), Orders.class);
					int totalOrderCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalordered"));
					int totalMappedCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalmapped"));

					if (!(totalMappedCount < totalOrderCount)) {
						response.put("Status", 0);
						response.put("Msg", errorMsg);
						log.info("Error Code : ER040. " + RegisterUserError.ER040);
						errorResponse = RegisterUserError.ER040;
						errorCode = "ER040";
						response.put("Return Time", System.currentTimeMillis());
						return response;
					}
					log.info("Order id found in niom. Order Channel : " + order.getOrder_acc_type().getAcc_type()
							+ "Order ID : " + order.getOrder_id() + "Out Order ID  :" + order.getExternal_order_id());
					if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("amazon")
							|| order.getOrder_acc_type().getAcc_type().toLowerCase().contains("walmart") || order.getOrder_acc_type().getAcc_type().toLowerCase().contains("technorv")) {
						if (order.getBilling_email().toLowerCase().contains("na")
								|| order.getBilling_phone().toLowerCase().contains("na")) {
							order.setBilling_email(userEmail);
							order.setBilling_phone(userPhone);
							order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
							order.setWelcome_status(order.getWelcome_status());
							niomDbservice.updateExternalOrdersInNiom(order);
						}
					}
//					else if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("walmart")) {
//						if (order.getBilling_email().toLowerCase().contains("na")
//								|| order.getBilling_phone().toLowerCase().contains("na")) {
//							order.setBilling_email(userEmail);
//							order.setBilling_phone(userPhone);
//							order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
//							order.setWelcome_status(order.getWelcome_status());
//							boolean updateExternalOrderData = niomDbservice.updateExternalOrdersInNiom(order);
//						}
//					} 
					else if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("facebook")) {

						if (nameCheck(order, activateUser)) {
							response.put("Status", 0);
							response.put("Msg", RegisterUserError.orderIDNotMatched);
							log.info("Error Code : ER038 : " + RegisterUserError.ER038);
							errorCode = "ER038";
							String eRes1 = RegisterUserError.ER038;
							errorResponse = eRes1.replace("#SP#", supportP).replace("#SM#", supportM);
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}
					}
				} else {
					response.put("Status", 0);
					
					String eRes1 = RegisterUserError.ER039;
					errorResponse = eRes1.replace("#SP#", supportP).replace("#SM#", supportM);
					response.put("Msg", errorResponse);
					
					log.info("Order id not found in niom, Error Code : ER039 :" + errorResponse);
					//errorResponse = RegisterUserError.ER039;
					errorCode = "ER039";
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			}

			// Getting Inventory thru DataBase
			JSONObject jresponse = new JSONObject();
			jresponse = userService.getInventory(qrcCode);

			if (jresponse == null) {
				response.put("Status", 0);
				response.put("Msg", errorMsg);
				log.info("Error Code : ER002. : " + RegisterUserError.ER002);
				errorResponse = RegisterUserError.ER002;
				errorCode = "ER002";
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			int status = jresponse.getInt("Status");

			if (status > 0 ? true : false) {
				try {
					Type inventoryList = new TypeToken<ArrayList<Inventory>>() {
					}.getType();

					List<Inventory> _inventoryList = gson.fromJson(jresponse.getJSONArray("inventory").toString(),
							inventoryList);

					if (_inventoryList.size() == 0) {
						response.put("Status", 0);
						response.put("Msg",
								"No device found for scanned QR Code : " + activateUser.getQrcCode()
										+ ". Please contact us at " + supportContactNumber.get(support_country) + " / or send email to "
										+ supportContactEmail.get(support_country) + ".");

						errorResponse = "No device found for respective QR Code : " + activateUser.getQrcCode();

						log.info("No device found for respective qrccode" + activateUser.getQrcCode());
						errorCode = "ER003";
						response.put("Return Time", System.currentTimeMillis());
						return response;
					} else {

						inventory = gson.fromJson(jresponse.getJSONArray("inventory").get(0).toString(),
								Inventory.class);

						monitortype = inventory.getDevicemodelnumber();
						initialDeviceStateid = (int) inventory.getDevicestate().getId();

						AssetModel assetModel = gatewayDao.getAssetModelByName(monitortype);

						if (assetModel != null) {
							model_id = (int) assetModel.getId();
							monitortypeid = assetModel.getMonitor_type().getId();
							sensor_available = assetModel.getSensoravailable();
							enableDelayFreq = assetModel.isEnableDelayFreq();
							delayFreqSecs = assetModel.getDelayFreqSecs();

							response.put("Monitortypeid", monitortypeid);
						} else {
							response.put("Status", 0);
							response.put("Msg", errorMsg);

							log.info("Device Model not found in Assetmodel table :" + monitortype);

							errorCode = "ER014";
							errorResponse = "Assetmodel not found for " + monitortype;
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}

						// #8-shipped to reseller : Check whether the device is sold by reseller
						if (inventory.getDevicestate().getId() == 8 && show_orderid) {
							String emailContent = new com.nimble.irisservices.helper.EmailContent()
									.sucscripeResellerContent(activateUser);

							if (email_helper.SendEmail_SES(to_address, cc_address,
									bcc_address, "Offline Order User Details", emailContent)) {
								log.info("Offline Reseller User details send Successfully to internal team .");
							}

							response.put("Status", 0);
							response.put("Msg", errorMsg);
							log.info(errorMsg);
							errorResponse = RegisterUserError.ER030;
							errorCode = "ER030";
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}

						// #9-shipped to amazon #2-shipped full filment
						if (inventory.getDevicestate().getId() != 9 && inventory.getDevicestate().getId() != 2) {
							response.put("Status", 0);
							response.put("Msg", errorMsg);
							response.put("Return Time", System.currentTimeMillis());
							log.info("Device State ID is not suitable to activate the product . Device State ID :"
									+ inventory.getDevicestate().getId() + "  QRC Code : " + qrcCode);

							errorCode = "ER032";
							errorResponse = "Device State ID is not suitable to activate the product . Device State ID :"
									+ inventory.getDevicestate().getId() + "  QRC Code : " + qrcCode;
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}

						if (show_orderid) { // hide order id
							List<Ordermap> orderMapList = new ArrayList<Ordermap>();
							Gson _gson = new Gson();

							Type orderListType = new TypeToken<ArrayList<Ordermap>>() {
							}.getType();
							orderMapList = _gson.fromJson(jorderIdCheckResponse.getJSONArray("Ordermap").toString(),
									orderListType);

							if (orderMapList.size() > 0) {
								for (Ordermap map : orderMapList) {
									if (map.getMeid().trim().equals(inventory.getMeid().trim())) {
										response.put("Status", 0);
										response.put("Msg", errorMsg);
										log.info(
												"Device is already mapped to some other orders . Need to check in the NIOM order map table.");
										response.put("ErrorCode", "ER005");
										errorCode = "ER005";
										errorResponse = RegisterUserError.ER005;
										response.put("Return Time", System.currentTimeMillis());
										return response;
									}
								}
							}
						}
						// Check Device is already activated
						JGatewayUserDetails gatewayUserDetails = gatewayService.getGateway(inventory.getMeid());

						if (gatewayUserDetails != null) {
							// isDeviceAlredyActivated = true;

							if (gatewayUserDetails.getUserName().toLowerCase().contains("sanjeevitest")) {
								isTestUserDevice = true;
							} else if ((gatewayUserDetails.getUserName().equalsIgnoreCase(qrcCode)
									&& gatewayUserDetails.getUserName().matches("[0-9]+")
									&& gatewayUserDetails.getUserName().length() == 6)) {
								isQrcUserDevice = true;
							}

							if (isTestUserDevice || (isQrcUserDevice && !mobilePage.equalsIgnoreCase("home"))
									|| (isQrcUserDevice && mobilePage.equalsIgnoreCase("home") && isAlreadyUser)) {
								log.info("getGateway: Gateway " + inventory.getMeid() + " is mapped to username - "
										+ gatewayUserDetails.getUserName());

								try {
									User _user = userService.verifyAuthKey(gatewayUserDetails.getAuthkey());
									gatewayService.delGateway(_user, Long.toString(gatewayUserDetails.getGatewayid()));
								} catch (Exception e) {
									response.put("Status", 0);
									errorCode = "ER031";
									response.put("Msg", errorMsg);
									log.error("Error while deleting gateway :" + e.getLocalizedMessage());
									errorResponse = RegisterUserError.ER031;
									response.put("Return Time", System.currentTimeMillis());
									return response;
								}

							} else if (isQrcUserDevice && mobilePage.equalsIgnoreCase("home") && !isAlreadyUser) {
								isQrcUserDevice = true;
							} else {

								log.info("getGateway: Gateway " + inventory.getMeid() + " is mapped to username - "
										+ gatewayUserDetails.getUserName());
								response.put("Status", 0);
								response.put("Msg", errorMsg);
								errorCode = "ER033";
								errorResponse = RegisterUserError.ER033;
								response.put("Return Time", System.currentTimeMillis());
								return response;
							}
						}

						// check MEID is already activated if activated return
						// the response

						if (false) {
							return null;
						} else {

							boolean pushResponse = false;

							if (isRecallDevice == 1) { // 1 - Replaced Device
								boolean isAvailableInOrderMap = niomDbservice.checkMeidIsAvailableInOrderMap(oldMeid);

								if (isAvailableInOrderMap) {
									pushResponse = niomDbservice.changeMeidAndReplaceInOrderMap(oldMeid,
											inventory.getMeid());
								}

							}

							if (show_orderid) { // hide order id

								if (isRecallDevice != 1) {
									pushResponse = userService.orderMapping(inventory.getMeid(),
											_helper.getCurrentTimeinUTC(), inventory.getDevicestate().getId(),
											order.getOrder_id() + "", order.getDevicemodel(), subscriptionStatus,
											"1", "auto", gatewayId);
								}

								if (pushResponse) {
									orderMappedtatus = true;
									orderid_given = true;
								} else {
									response.put("Status", 0);
									response.put("SpecificMsg", "Unable to map the order");
									response.put("Msg", errorMsg);
									errorCode = "ER041";
									errorResponse = RegisterUserError.ER041;
									response.put("Return Time", System.currentTimeMillis());
									return response;
								}

								log.info("Push Data Response : " + orderMappedtatus);
								log.info("Activate Sim from Config : " + activateSim);
							} else {
								orderMappedtatus = true;
							}

							// Create Gateway
							try {

								if ((!isAlreadyUser && (isQrcUserDevice && !mobilePage.equalsIgnoreCase("home")))
										|| (!isAlreadyUser && mobilePage.equalsIgnoreCase("login")
												&& !isQrcUserDevice)) {

									JResponse jResponseSignUp = userSignUp(activateUser, userName, country);
									if ((Integer) jResponseSignUp.getResponse().get("Status") == 0) {
										errorCode = "ER007";
										errorResponse = RegisterUserError.ER007;
										return jResponseSignUp;
									}
								}
								log.info("==Get user===========");
								User user = new User();
								try {
									user = userService.getUserByName(userName);
									intialUser = user;
									orderMapUser = user;
									provisionStatus = true;
									createuser_id = user.getId();
								} catch (InvalidUsernameException ex) {
									log.error("==Invalid username exception===========");
									loginCreationIssue = true;
									response.put("Status", 0);
									response.put("Msg", errorMsg);
									errorCode = "ER025";
									errorResponse = "Getting user Details : " + RegisterUserError.ER025;
									response.put("Return Time", System.currentTimeMillis());
									return response;
								}
								
								if(orderid_given) {
									boolean isUserIdUpdated = crService.updateUserIdInProsuctSubs(String.valueOf(order.getOrder_id()), user.getId());
								}

								String levelid = "1";
								String group = "";

								List<JGroups> jgroups = groupservices.getGroups(group, group, levelid,
										user.giveCompany().getId());

								if (jgroups.get(0) != null) {
									group_id = jgroups.get(0).getId();
								}

								Timestamp orderDate = Timestamp.valueOf(order.getDatetime());
								Timestamp curDateTime = Timestamp.valueOf(IrisservicesUtil.getCurrentTimeUTC());
								long daysDiff = TimeUnit.DAYS.convert(curDateTime.getTime() - orderDate.getTime(),
										TimeUnit.MILLISECONDS);
								Template welcomeEmailTemplate = (Template) templates.getTemplate("welcomeemail.ftl");
								Map<String, String> welcomeEmailParams = new HashMap<>();
								welcomeEmailParams.put("FIRSTNAME", activateUser.getFirstname());
								welcomeEmailParams.put("USERNAME", user.getEmail());
								welcomeEmailParams.put("FREEVPMNOTE", "");
								welcomeEmailParams.put("COPYRIGHT_YEAR", _helper.getCurrentYear());
								welcomeEmailParams.put("SUPPORT_EMAIL", supportM);
								String support_num = supportP.substring(0);
								welcomeEmailParams.put("SUPPORT_PHONE", supportP);
								welcomeEmailParams.put("SUPPORT_PHONE_WITH_COUNTRYCODE", supportP);
								welcomeEmailParams.put("COPYRIGHT_YEAR", _helper.getCurrentYear());
								
								String siteUrl = "www.mywaggle.com";
								String operating_hours = "10 AM - 8 PM EST";
								if(country.equalsIgnoreCase("AU"))
								{
									siteUrl = "www.mywaggle.com.au";
									operating_hours = "7 AM - 6 PM AEDT";
								}
								
								welcomeEmailParams.put("SITE_URL", siteUrl);								
								welcomeEmailParams.put("OPERATING_HOURS", operating_hours);
									
								boolean isFreeVpmAdded = false;

								// Register QRC User
								if (isQrcUserDevice && mobilePage.equalsIgnoreCase("home") && !isAlreadyUser) {
									log.info("==QRC User===========");

									Gateway gateway = new Gateway();

									List<JGateway> gateways = gatewayService.getGateway("", "", "", "", user.getId(),
											inventory.getMeid());

									CompanyConfig cmp_cfg = companyService.getCompanyConfig(user.giveCompany().getId());

									boolean updateQRCGateway = false;

									if (gateways != null) {

										log.info("==Get Gateways not null===========");

										if (gateways.size() > 0) {

											JGateway jGateway = gateways.get(0);
											try {
												jGateway = gatewayService.gatewayExitsinDB(jGateway,
														user.giveCompany().getId());
												jGateway.setName(gateway_name);
												jGateway.setModelid(model_id);
												jGateway.setSensorEnable(sensor_available);

												if (jGateway.getQrcode().equals("NA")) {
													jGateway.setQrcode(qrcCode);
												}

												gateway = gatewayService.saveORupdateQRCGateway(jGateway,
														user.giveCompany().getId());

												updateQRCGateway = true;
											} catch (Exception ex) {

												updateQRCGateway = false;
												gatewayCreationIssue = true;

												log.error("Exception while updating gateway for QRC Login : " + userName
														+ "Exception : " + ex.getMessage());
											}

											try {

												log.info("==Updated Alerts ===========");
												List<JAlertCfg> alertcfgs = alertCfgService.getAlertCfg("", "",
														Long.toString(jGateway.getId()), user.getId(),
														cmp_cfg.getTemperatureunit());

												for (JAlertCfg alerts : alertcfgs) {

													Long[] assetIds = new Long[1];
													assetIds[0] = jGateway.getId();

													alerts.setEmailids(userEmail);
													alerts.setMobilenos(userPhone);
													// alerts.setAlerttypeid(alerts.getAlerttype().getId());
													alerts.setAssetids(assetIds);

													boolean result1 = alertCfgService.saveORupdateAlertCfg(alerts,
															user.giveCompany().getId(), "registerproduct");
												}
												updateQRCGateway = true;

											} catch (Exception ex) {
												log.error("==Excepion  Alerts Saved==========="
														+ ex.getLocalizedMessage());
												gatewayCreationIssue = true;
												updateQRCGateway = false;
												log.error("Exception while updating  alerts for QRC Login : " + userName
														+ "Exception : " + ex.getMessage());
											}
										}
									}
									boolean updateQRCLogin = false;
									if (updateQRCGateway) {
										try {
											log.info("==2.Update QRC User name===========");
											user = userService.getUserByName(qrcCode.trim());
											user.setUsername(userEmail.trim());

											Set<Gateway> _gateway = new HashSet<Gateway>();
											_gateway.add(gateway);
											String password = "";

											if (activateUser.isPwd_update()) {
												password = activateUser.getPassword().trim();
											} else {
												password = activateUser.getPhoneno();
												password = password.replaceAll("\\W", "");

												if (password.length() > 10) {
													password = password.substring(password.length() - 10);
												}
											}
											user.setPassword(_helper.bCryptEncoder(password));
											user.setPassword_ver("V2");
											user.setGateways(_gateway);
											user.setEmail(userEmail.trim());
											user.setMobileno(userPhone);
											user.setAuthKey(_helper.encryptAndSetUser(userEmail.trim()));
											user.setVerified(true);
											user.setCountry(country);
											userService.updateUser(user);

											// update evalidation
											async.updateEvalidation(user.getId(), password);

											orderMapUser = user;

											Company cmp = companyService.getCompany(user.giveCompany().getId());
											cmp.setMobileno(userPhone);
											cmp.setPhoneno(userPhone);
											cmp.setEmail(userEmail.trim());
											companyService.updateCompany(cmp);
											updateQRCLogin = true;
											sendDynamicMsg(gateway);

										} catch (Exception ex) {
											loginCreationIssue = true;
											log.error("Exception in updating the QRC User Name ="
													+ ex.getLocalizedMessage());
											log.error("Exception while updating username and password for QRC Login : "
													+ userName + "Exception : " + ex.getMessage());
										}
									}

									if (updateQRCLogin) {

										alertCreationStatus = "UpdateGatewayCreditStatus : True"
												+ "<br/><br/>SaveGatewayReportStatus : True"
												+ "<br/><br/>SaveLastGatewayReport : True";

										alertCreationStatus = alertCreationStatus + "<br/><br/>CreateTempAlertStatus :"
												+ "True" + "<br/><br/>CreateBatteryAlertStatus :" + "True"
												+ "<br/><br/>CreateOnBatteryAlertStatus : " + "True"
												+ "<br/><br/>CreateNetworkAlertStatus : " + "True";

										if (show_orderid) {
											orderMappedtatus = niomDbservice.updateOrdermapUserDetails(
													order.getOrder_id(), user.getUsername(), user.getId() + "",
													"NA");

											updateExternalOrderDataStatus = userService.updateOrdersDataV2(orderChannel,
													order, inventory);
										}

										accountActivatedStatus = true;

										String emailContent = "";

										if (daysDiff <= Long.valueOf(freeVPM_days) && addFreeVpmFlag) {
											welcomeEmailParams.put("FREEVPMNOTE",
													"<li>By registering, you just got a free WaggleVet session. Grab it on the Kennel screen.</li>");
											async.addFreeVPM("vpm", user.getId());
											isFreeVpmAdded = true;
										}

										ResponseEntity<String> newEmailContent = ResponseEntity.ok(
												FreeMarkerTemplateUtils.processTemplateIntoString(welcomeEmailTemplate,
														welcomeEmailParams));
										emailContent = newEmailContent.getBody();
										if (email_helper.SendEmail_SES(user.getEmail(),
												null, bcc_address, "Welcome to Waggle!", emailContent)) {
											log.info("User Created Successfully : " + user.getEmail());
											provisionStatus = true;
										}

										User updatedUser = userService.getUserByName(userEmail.trim());

										response.put("Status", 1);
										response.put("Msg",
												(isFreeVpmAdded ? RegisterUserError.homePageActivationWithVPM_Message
														: RegisterUserError.homePageActivationMessage));
										response.put("display_msg",
												(isFreeVpmAdded
														? RegisterUserError.homePageActivationWithVPM_DisplayMessage
														: RegisterUserError.homePageActivationMessage));
										response.put("gatewayid", gateway.getId());
										response.put("show_warranty_popup", show_warranty_popup);
										response.put("warranty_msg", warranty_msg);
										response.put("subscription_buynow_popup", subscriptionBuyNowPopUp);
										response.put("subscription_buynow_content", subscriptionBuyNowContent);

										response.put("show_subs_page_first", show_subs_page_first);
										response.put("show_orderid_later_popup", orderid_later_popup);
										response.put("orderid_later_popup_content", orderid_later_popup_content);
										response.put("Return Time", System.currentTimeMillis());
										
										if(!activateUser.getReq_ver().equalsIgnoreCase("V1")) {
											response.put("show_warranty_popup", false);
											response.put("show_orderid_later_popup", false);
											response.put("show_subs_page_first", true);
											response.put("nav_to_warranty_scr",nav_to_warranty_scr);
										}
										
										response.put("show_n13_popup", false);
										response.put("n13_popup_content", n13_registration_content);
										if( gateway.giveAsset() != null && gateway.giveAsset().getModel() != null && gateway.giveAsset().getModel().getModel().contains("N13") ) {
											response.put("show_n13_popup", true);
										}
										
										if( giveToken ) {
											byte[] oAuth2token = oAuth2Service.generateOauth2Token(user.getUsername(),
													user.getPassword(), clientIdApp, clientSecretApp);

											if (oAuth2token != null) {
												response.put("token", oAuth2token);
											} else {
												response.put("token", null);
											}
										}

										updatedUser.setPassword("NA");
										response.put("User", updatedUser);
										UserV4 userV4 = new UserV4(updatedUser);
										userV4.setPassword("NA");
										response.put("UserV4", userV4);
										user_id = updatedUser.getId();
										accountCreated = true;

										gatewayId = gateway.getId();

										response.put("Return Time", System.currentTimeMillis());
										return response;
									} else {
										log.info("==2.Error Update QRC User name DONE===========");
										response.put("Status", 0);
										response.put("Msg", errorMsg);
										errorResponse = "Updating QRC user Details : " + RegisterUserError.ER046;
										errorCode = "ER046";
										response.put("Return Time", System.currentTimeMillis());
										return response;
									}
								}
								// Create Gateway and Assign to the user
								// JGateway jgateway = new JGateway();

								Gateway gateway = new Gateway();
								boolean updateCreaditStatus = false;
								boolean saveGatewayReportStatus = false;
								boolean saveLastGatewayReport = false;

								JCreateGateway jcreateGateway = new JCreateGateway();

								if (!oldMeid.equalsIgnoreCase("NA"))
									replacedGateway = gatewayService.getGatewayDetails(oldMeid);

								jcreateGateway = createOrUpdateGateway(activateUser, inventory, model_id, group_id,
										sensor_available, passwordType, user, oldMeid, isRecallDevice);

								log.info("== gateway Created and assiged to user===========");

								if (jcreateGateway != null) {
									log.info("== Update credit status===========");
									updateCreaditStatus = jcreateGateway.isUpdateCreaditStatus();
									saveGatewayReportStatus = jcreateGateway.isSaveGatewayReportStatus();
									saveLastGatewayReport = jcreateGateway.isSaveLastGatewayReport();

									alertCreationStatus = "UpdateGatewayCreditStatus : " + updateCreaditStatus
											+ "<br/><br/>SaveGatewayReportStatus : " + saveGatewayReportStatus
											+ "<br/><br/>SaveLastGatewayReport :" + saveLastGatewayReport;

									if (jcreateGateway.isGatewayCreated()) {
										// JGateway jgateway = jcreateGateway.getJgateway();
										gateway = jcreateGateway.getGateway();

										log.info("== Create alerts===========");
//										boolean createBatteryAlertStatus = false;
//										boolean createOnIdleAlertStatus = false;
										long mtype = gateway.getModel().getMonitor_type().getId();

										if (mtype == 1 && isRecallDevice == 0) {

											String alertStatus = alertCfgServiceV4.createPMAlerts(user, gateway,
													enableDelayFreq, delayFreqSecs, activateUser.getLat(),
													activateUser.getLon(), "registerproduct", gateway.getModel().isIs_aqi());

											alertCreationStatus = alertCreationStatus + alertStatus;

										} else if( mtype == 4 ) {
											boolean gatewayStatus = gatewayService.createOrResetGatewayStatus(gateway.getId(), gateway.getMeid(), inventory.getSerialnumber(),mtype,isRecallDevice);
											log.info("create or reset gateway_status : " + gatewayStatus);	
										}

										if (jcreateGateway.isGatewayCreated()) {
											String emailContent = "";

											if (daysDiff <= Long.valueOf(freeVPM_days) && addFreeVpmFlag) {
												welcomeEmailParams.put("FREEVPMNOTE",
														"<li>By registering, you just got a free WaggleVet session. Grab it on the Kennel screen.</li>");
												async.addFreeVPM("vpm", user.getId());
												isFreeVpmAdded = true;
											}

											ResponseEntity<String> newEmailContent = ResponseEntity
													.ok(FreeMarkerTemplateUtils.processTemplateIntoString(
															welcomeEmailTemplate, welcomeEmailParams));
											emailContent = newEmailContent.getBody();

											if (email_helper.SendEmail_SES(
													user.getEmail(), null, bcc_address, "Welcome to Waggle!",
													emailContent)) {
												log.info("User Created Successfully : " + user.getEmail());
												provisionStatus = true;
											}
										}
										if (show_orderid) { // hide order id
											orderMappedtatus = niomDbservice.updateOrdermapUserDetails(
													order.getOrder_id(), user.getUsername(), user.getId() + "",
													"NA");

											updateExternalOrderDataStatus = userService.updateOrdersDataV2(orderChannel,
													order, inventory);
										}
										accountActivatedStatus = true;

									} else {
										gatewayCreationIssue = true;
									}
								} else {
									gatewayCreationIssue = true;
								}

								if (accountActivatedStatus) {

									sendDynamicMsg(gateway);

									response.put("Status", 1);
									response.put("gatewayid", gateway.getId());
									response.put("show_warranty_popup", show_warranty_popup);
									response.put("warranty_msg", warranty_msg);
									response.put("subscription_buynow_popup", subscriptionBuyNowPopUp);
									response.put("subscription_buynow_content", subscriptionBuyNowContent);

									response.put("show_subs_page_first", show_subs_page_first);
									response.put("show_orderid_later_popup", orderid_later_popup);
									response.put("orderid_later_popup_content", orderid_later_popup_content);
									response.put("Return Time", System.currentTimeMillis());
									response = getStatusMsg(response, mobilePage, monitortypeid, isFreeVpmAdded);

									if(!activateUser.getReq_ver().equalsIgnoreCase("V1")) {
										response.put("show_payment_page", false);
										response.put("show_orderid_later_popup", false);
										response.put("show_subs_page_first", false);
										response.put("show_warranty_popup", false);
										response.put("subscription_buynow_popup", false);
										response.put("nav_to_warranty_scr", nav_to_warranty_scr);
									}
									
									response.put("show_n13_popup", false);
									response.put("n13_popup_content", n13_registration_content);
									if( gateway.giveAsset() != null && gateway.giveAsset().getModel() != null && gateway.giveAsset().getModel().getModel().contains("N13") ) {
										response.put("show_n13_popup", true);
									}
									
//									if (mobilePage.toLowerCase().contains("login")) {
//										if (monitortypeid == 1) {
//											response.put("Msg",
//													(isFreeVpmAdded
//															? RegisterUserError.loginPageActivationWithVPM_Message
//															: RegisterUserError.loginPageActivationMessage));
//											response.put("display_msg", (isFreeVpmAdded
//													? RegisterUserError.loginPageActivationWithVPM_DisplayMessage
//													: RegisterUserError.loginPageActivationMessage));
//										} else {
//											response.put("Msg", RegisterUserError.loginPageFurbitActivationMessage);
//											response.put("display_msg",
//													RegisterUserError.loginPageFurbitActivationMessage);
//										}
									//
//									} else if (mobilePage.toLowerCase().contains("home")) {
//										if (monitortypeid == 1) {
//											response.put("Msg",
//													(isFreeVpmAdded
//															? RegisterUserError.homePageActivationWithVPM_Message
//															: RegisterUserError.homePageActivationMessage));
//											response.put("display_msg",
//													(isFreeVpmAdded
//															? RegisterUserError.homePageActivationWithVPM_DisplayMessage
//															: RegisterUserError.homePageActivationMessage));
//										} else {
//											response.put("Msg", RegisterUserError.loginPageFurbitActivationMessage);
//											response.put("display_msg",
//													RegisterUserError.loginPageFurbitActivationMessage);
//										}
									//
//									} else if (mobilePage.toLowerCase().contains("account")) {
//										if (monitortypeid == 1) {
//											response.put("Msg",
//													(isFreeVpmAdded
//															? RegisterUserError.accountPageActivationWithVPM_Message
//															: RegisterUserError.accountPageActivationMessage));
//											response.put("display_msg", (isFreeVpmAdded
//													? RegisterUserError.accountPageActivationWithVPM_DisplayMessage
//													: RegisterUserError.accountPageActivationMessage));
//										} else {
//											response.put("Msg", RegisterUserError.accountPageFurbitActivationMessage);
//											response.put("display_msg",
//													RegisterUserError.accountPageFurbitActivationMessage);
//										}
									//
//									} else {
//										if (monitortypeid == 1) {
//											response.put("Msg",
//													(isFreeVpmAdded
//															? RegisterUserError.loginPageActivationWithVPM_Message
//															: RegisterUserError.loginPageActivationMessage));
//											response.put("display_msg", (isFreeVpmAdded
//													? RegisterUserError.loginPageActivationWithVPM_DisplayMessage
//													: RegisterUserError.loginPageActivationMessage));
//										} else {
//											response.put("Msg", RegisterUserError.loginPageFurbitActivationMessage);
//											response.put("display_msg",
//													RegisterUserError.loginPageFurbitActivationMessage);
//										}
									//
//									}
									if( giveToken ) {
										byte[] oAuth2token = oAuth2Service.generateOauth2Token(user.getUsername(),
												user.getPassword(), clientIdApp, clientSecretApp);

										if (oAuth2token != null) {
											response.put("token", oAuth2token);
										} else {
											response.put("token", null);
										}
									}

									user.setPassword("NA");
									response.put("User", user);
									UserV4 userV4 = new UserV4(user);
									userV4.setPassword("NA");
									response.put("UserV4", userV4);
									accountCreated = true;
									user_id = user.getId();
									gatewayId = gateway.getId();

									response.put("Return Time", System.currentTimeMillis());
									return response;
								} else {
									response.put("Status", 0);
									response.put("Msg", errorMsg);
									response.put("Return Time", System.currentTimeMillis());
									errorResponse = "Getting user Details : " + RegisterUserError.ER045;
									errorCode = "ER045";
									response.put("Return Time", System.currentTimeMillis());
									return response;
								}

							} catch (DataIntegrityViolationException e) {
								gatewayCreationIssue = true;
								log.error("== Exception 1===========" + e.getLocalizedMessage());
								response.put("Status", 0);
								response.put("Return Time", System.currentTimeMillis());
								if (Integer.parseInt(companyType) == 3) {
									response.put("Msg", "Pet Name already exists. Please enter valid one. ");
									response.put("display_msg", "Pet Name already exists. Please enter valid one. ");
								} else {
									response.put("Msg", "Asset name or MEID already exits");
									response.put("display_msg", "Asset name or MEID already exits");
								}

								errorCode = "ER015";
								errorResponse = RegisterUserError.ER015;
								response.put("Return Time", System.currentTimeMillis());
								return response;
							} catch (Exception e) {
								gatewayCreationIssue = true;
								response.put("Status", 0);
								response.put("Msg", errorMsg);
								response.put("Return Time", System.currentTimeMillis());
								log.error("saveORupdateGateway::::" + e.getLocalizedMessage());
								errorResponse = RegisterUserError.ER016 + "Exception : " + e.getMessage();
								errorCode = "ER016";
								response.put("Return Time", System.currentTimeMillis());
								return response;
							}
						}
					}
				} catch (Exception ex) {
					log.error("Error while getting meid for the respective qrc code. Exception :  " + ex.getMessage());
					response.put("Status", 0);
					response.put("Msg", errorMsg);
					errorCode = "ER019";
					errorResponse = RegisterUserError.ER019 + "Exception : " + ex.getMessage();
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			} else {
				String msg = jresponse.getString("Msg");
				response.put("Status", 0);
				response.put("Msg", errorMsg);
				response.put("Return Time", System.currentTimeMillis());
				errorCode = "ER018";
				errorResponse = RegisterUserError.ER018 + "Response Message : - " + msg;
				log.info("Exception : No device found for respective qrccode. Response Message :  -" + msg);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		} catch (Exception e) {
			log.error("signup:::" + e.getMessage() + "" + e.getLocalizedMessage());
			gatewayCreationIssue = true;
			response.put("Status", 0);
			response.put("Msg", errorMsg);
			response.put("Return Time", System.currentTimeMillis());
			errorResponse = RegisterUserError.ER020;
			errorCode = "ER020";
			response.put("ErrorCode", "ER020");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		} finally {
			if (alertCreationStatus == null) {
				alertCreationStatus = "Alert Created : false" + "<br/><br/>";
			}

			if (loginCreationIssue || gatewayCreationIssue) {
				orderMappedtatus = false;

				if (show_orderid) {// hide order id
					log.info(
							"Deleted mapping for meid :" + inventory.getMeid() + " User : " + intialUser.getUsername());

					userService.deleteordermap(inventory.getMeid(), initialDeviceStateid);
				}
			}

			if (gatewayCreationIssue && !isAlreadyUser && !mobilePage.equalsIgnoreCase("home")) {
//				long timeMilli = Calendar.getInstance().getTimeInMillis();
//				String newUserName = intialUser.getUsername();
//				
//				if(newUserName.length()>10)
//					newUserName = intialUser.getUsername().substring(0, 9) + "-" + timeMilli;
				//
//				log.info("Username updated for new user : " + intialUser.getUsername() + " New UserName : "
//						+ newUserName);
				//
//				intialUser.setUsername(newUserName);
//				intialUser.setEmail(newUserName);
//				intialUser.setEnable(false);
//				intialUser.setAuthKey(newUserName);
//				intialUser.setMobileno("1234567890");
//				orderMappedtatus = false;
//				if (!(intialUser.getSignupType() == null)) {
//					userService.updateUser(intialUser);
//				}
				//
//				orderMapUser = intialUser;
				orderMappedtatus = false;
				// orderMapUser = updateUser(intialUser);

				boolean deleteAllUserInfoStatus = userService.deleteAllUserInfo(intialUser.getId(),
						activateUser.getEmail());
				log.info("Delete All User Info status : " + deleteAllUserInfoStatus);
			}

			if (isQrcUserDevice && !gatewayCreationIssue && isAlreadyUser) {
//				long timeMilli = Calendar.getInstance().getTimeInMillis();
//				
//				String newUserName = intialUser.getUsername();
//				
//				if(newUserName.length()>10)
//					newUserName = intialUser.getUsername().substring(0, 9) + "-" + timeMilli;
//				
//				log.info("Username updated for QRC user : " + intialUser.getUsername() + " New UserName : "
//						+ newUserName);

				User qrcUser = new User();
				try {
					qrcUser = userService.getUserByName(inventory.getQrc());
					orderMapUser = qrcUser;
					createuser_id = qrcUser.getId();
				} catch (InvalidUsernameException e) {
					log.error(e.getLocalizedMessage());
				}

				qrcUser = updateUser(qrcUser);

//				qrcUser.setUsername(newUserName);
//				qrcUser.setEmail(newUserName);
//				qrcUser.setAuthKey(newUserName);
//				qrcUser.setMobileno("1234567890");
//				qrcUser.setEnable(false);
//				userService.updateUser(qrcUser);
			}

			if (isQrcUserDevice && !gatewayCreationIssue && !isAlreadyUser && mobilePage.equalsIgnoreCase("login")) {

//				long timeMilli = Calendar.getInstance().getTimeInMillis();
//				String newUserName = intialUser.getUsername();
//				
//				if(newUserName.length()>10)
//					newUserName = intialUser.getUsername().substring(0, 9) + "-" + timeMilli;
//				log.info("Username updated for QRC user : " + intialUser.getUsername() + " New UserName : "
//						+ newUserName);

				User qrcUser = new User();
				try {
					qrcUser = userService.getUserByName(inventory.getQrc());
					createuser_id = qrcUser.getId();
				} catch (InvalidUsernameException e) {
					log.error(e.getLocalizedMessage());
				}
				qrcUser = updateUser(qrcUser);
//				qrcUser.setUsername(newUserName);
//				qrcUser.setEmail(newUserName);
//				qrcUser.setEnable(false);
//				qrcUser.setAuthKey(newUserName);
//				qrcUser.setMobileno("1234567890");
//				userService.updateUser(qrcUser);

			}
			long gateway_id = 0;
			if (isQrcUserDevice && (loginCreationIssue || gatewayCreationIssue)
					&& !mobilePage.equalsIgnoreCase("home")) {
				orderMappedtatus = false;
				User qrcUser = new User();
				try {
					qrcUser = userService.getUserByName(inventory.getQrc());
					createuser_id = qrcUser.getId();

					String levelid = "1";
					String group = "";

					// for order mapping details
					orderMapUser = qrcUser;
					List<JGroups> jgroups = groupservices.getGroups(group, group, levelid,
							qrcUser.giveCompany().getId());

					if (jgroups.get(0) != null) {
						group_id = jgroups.get(0).getId();
					}

					if (!oldMeid.equalsIgnoreCase("NA"))
						replacedGateway = gatewayService.getGatewayDetails(oldMeid);

					JCreateGateway jcreateGateway = createOrUpdateGateway(activateUser, inventory, model_id, group_id,
							sensor_available, passwordType, qrcUser, oldMeid, isRecallDevice);

					if (jcreateGateway.isGatewayCreated() && isRecallDevice == 0) {
						String status = alertCfgServiceV4.createPMAlerts(qrcUser, jcreateGateway.getGateway(),
								enableDelayFreq, delayFreqSecs, activateUser.getLat(), activateUser.getLon(), "registerproduct", jcreateGateway.getGateway().getModel().isIs_aqi());
					}
				} catch (InvalidUsernameException e) {
					log.error("QRC alert creation:" + e.getLocalizedMessage());
				}

			}

			String mailSub = "Failed : External Orders User Activation Status-Email : " + activateUser.getEmail();

			Offlineuserdetails offlineUser = new Offlineuserdetails();

			offlineUser.setQrccode(activateUser.getQrcCode());
			offlineUser.setName(activateUser.getFirstname() + " " + activateUser.getLastname());
			offlineUser.setEmail(activateUser.getEmail());
			offlineUser.setCompanytype(companyType);
			if (inventory.getDevicestate() == null) {
				offlineUser.setDevicestateId("NA");
			} else {
				offlineUser.setDevicestateId(Long.toString(inventory.getDevicestate().getId()));
			}

			if (activateUser.getAddress() == "" || activateUser.getAddress().isEmpty()) {
				offlineUser.setAddress("NA");
			} else {
				offlineUser.setAddress(activateUser.getAddress());
			}

			if (accountCreated) {

				if (isRecallDevice > 0) {

					async.mapOldGatewayToReturnAC(replacedGateway, returnLoginUsername, userName);
					// async.changeMeidAndReplaceInOrderMap( oldMeid,inventory.getMeid() );
					async.saveRecallHistory(recalUserId, oldMeid, inventory.getMeid(), isRecallDevice);
					async.changePetNameByGatewayId(petName, gatewayId);
				}

				devicecount = gatewayService.getDeviceCount(createuser_id);
				log.info("user device count for userid:" + createuser_id + " ,\n device count: " + devicecount);

				mailSub = "Success : External Orders User Activation Status-Email : " + activateUser.getEmail();

				String orderId = Long.toString(order.getOrder_id());
				if (!orderChannel.equals("rv")) {
					orderId = order.getExternal_order_id();
				}

				offlineUser.setStatus("Success");

				int amount = 0;
				String desc = "";

				String paymentURL = "NA";
				response.put("payment_page_url", paymentURL);
				response.put("show_payment_page", false);
				response.put("payment_page_content", redirectPaymentPageContent);

				String chargebeeId = "NA";
//				if (redirectPaymentPage || orderid_given) {
				chargebeeId = userServiceV4.createUserInChargebee(activateUser.getFirstname(),
						activateUser.getLastname(), activateUser.getEmail(), activateUser.getPhoneno(), userName,
						amount, orderID);
//				}

				ComboPlanInfo comboPlanInfo = cbService.checkActiveComboPlan(chargebeeId);
				if( comboPlanInfo != null ) {
					log.info("User have active combo plan, calling wagglehooks to check and assign gateway feature and all_product subscription");
					String waggleHooksURL = wagglehooks_url + "assigncomboplan?user_id="+user_id+"&gateway_id="+gateway_id+"&chargebee_planid="+comboPlanInfo.getPlan_name()+"&subscription_id="+comboPlanInfo.getSubscription_id();
					log.info("wagglehooks assigncomboplan url : "+waggleHooksURL);
					String res = _helper.httpPOSTRequest(waggleHooksURL, "");
					log.info("wagglehooks assigncomboplan response : "+res);
				}

				response = checkBundleSubscription(response,activateUser,user_id,chargebeeId,orderid_given,order.getOrder_id(),userName,amount,desc,gatewayId,monitortypeid);

//
//				try {
//					UserV4 usrObj = userServiceV4.verifyAuthV4("id", String.valueOf(createuser_id));
//					if (usrObj != null)
//						cbID = usrObj.getChargebeeid();
				//
//				} catch (Exception e) {
//					// TODO: handle exception
//				}

//				UserRvDetails rvObj = userServiceV4.getUserRvDetails(createuser_id);
//				boolean updatedRVProfile = (rvObj != null) ? true : false;

//				boolean userBadgeStat = rvServ.saveUserBadgeTxn(createuser_id, "NA", devicecount, updatedRVProfile,
//						"NA");
//				log.info("in activate user:" + createuser_id + " RVer Badge created:" + userBadgeStat);

			} else {
				offlineUser.setStatus("Failed");
			}

			try {
				// Order Mapping Details
				Timestamp curTime = Timestamp.valueOf(IrisservicesUtil.getCurrentTimeUTC());
				Timestamp orderdate = Timestamp.valueOf(order.getDatetime());
				long userid = (orderMapUser != null ? orderMapUser.getId() : (long) 0L);

				if (accountCreated == false)
					userid = 0L;

				if (orderChannel == null)
					orderChannel = "NA";

				OrderMappingDetails orderMapDetail = new OrderMappingDetails(activateUser.getFirstname(),
						activateUser.getEmail(), activateUser.getAddress(), offlineUser.getDevicestateId(),
						activateUser.getQrcCode(), companyType, offlineUser.getStatus(), orderID, orderChannel, userid,
						curTime, orderdate, order.getExternalsku());
				log.info("orderMapDetail block", orderMapDetail);
				userService.saveOrderMappingDetails(orderMapDetail);
			} catch (Exception e) {
				log.error(" ActivateUser: Order Mapping Details: ", e.getLocalizedMessage());
			}
			boolean is_recharge = false;
			String subs_status = "NA";
			
			try {
				is_recharge = (boolean)response.get("is_recharge");
				subs_status = (String)response.get("subs_status");
				response.remove("is_recharge"); // this is used for internal mail purpose.
				response.remove("subs_status");// this is used for internal mail purpose.
			}catch (Exception e) {
				log.error("checking subscription status:"+e.getLocalizedMessage());
			}
			
			String statusEmailContent = new com.nimble.irisservices.helper.EmailContent().activationStatus(activateUser,
					provisionStatus, orderMappedtatus, updateExternalOrderDataStatus, activateSimStatus,
					alertCreationStatus, errorResponse, monitortype, devicecount, isRecallDevice, oldMeid,
					inventory.getMeid(), returnLoginUsername,is_recharge,subs_status);
			
			async.saveorupdateofflineUserDetails(offlineUser);
			
			String skuNumber = getskuNumber(inventory.getMeid());
			String deviceId = inventory.getDeviceId();
			String tariffId = inventory.getTariffId();
			String qrc = inventory.getQrc();

			async.updateRegisterUserEmailStatus(to_address, cc_address, bcc_address, mailSub, statusEmailContent,
					qrcCode, errorCode, accountCreated, inventory.getMeid(), inventory.getSim_no(),
					inventory.getSim_vendor(), isRecallDevice,skuNumber,activateUser.getEmail(), deviceId, tariffId, qrc);

		}
	}

	private JResponse checkBundleSubscription(JResponse response, @Valid ActivateUser activateUser, long user_id, String chargebeeId, boolean orderid_given, long order_id, String userName, int amount, String desc, long gatewayId, long monitortypeid) {
		log.info("Entered into checkBundleSubscription :: chargebee id : "+chargebeeId+" :: user id : "+user_id+" :: order id : "+order_id);
		boolean redirectPaymentPage = redirectPaymentPage_config;
		boolean subscriptionBuyNowPopUp = subscriptionBuyNowPopUp_config;
		// Activate CB subscription
		String cbPlan = "NA";
		String recharge_cusid = "NA";
		String reSubId ="NA";
		String email_match ="NA";
		String nextrenewal_at = null;
		String price ="0.0";
		boolean is_email_match = false;
		boolean is_recharge = false;
		ArrayList<String> subDetails = new ArrayList<String>();
		String subs_status = "NA";
		
		String given_order_id = order_id+"";
		if( !orderid_given ) {
			given_order_id = "NA";
		}
		
		JResponse rechargeResponse = reService.createRechargeSubscription(userName, chargebeeId, user_id, given_order_id,gatewayId,monitortypeid);
		
		if( (boolean) rechargeResponse.get("is_recharge") ) {
			response.put( "is_recharge" , (boolean) rechargeResponse.get("is_recharge"));
			response.put( "subs_status" , (String) rechargeResponse.get("subs_status"));
			return response;
		}
		
		if (activateUser.getReq_ver().equalsIgnoreCase("V1") && orderid_given && !chargebeeId.equalsIgnoreCase("NA")
				&& product_subs_enable) {
			JProductSubscription jProductSubscription = crService.getCBPlan(String.valueOf(order_id));

			try {

				if (jProductSubscription != null && !jProductSubscription.getPlan_id().equalsIgnoreCase("NA")
						&& !jProductSubscription.getPlan_id().isEmpty() && product_subs_enable) {

					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
					Date orderedDate = orderedDate = sdf.parse(jProductSubscription.getOrder_date());

					Calendar cal = Calendar.getInstance();
					cal.setTime(orderedDate);
					cal.add(Calendar.DATE, jProductSubscription.getSubscription_period_days() + 21); // Adding plan
																										// expired
																										// date
					String output = sdf.format(cal.getTime());
					Date subsEndDate = cal.getTime();

					cal.setTime(new Date());
					Date curDate = cal.getTime();
					boolean stat = false;
					if (subsEndDate.after(curDate)) {
						cal.setTime(orderedDate);
						cal.add(Calendar.DATE, 21); // Adding 21 days into ordered date
						Date purchasedDate = cal.getTime();

						if (purchasedDate.after(curDate)) {
							stat = cbService.createPaidSubscription(chargebeeId, jProductSubscription, new Date());
						} else {
							stat = cbService.createPaidSubscription(chargebeeId, jProductSubscription, purchasedDate);
						}

					} else {
						redirectPaymentPage = false;
					}
					if (stat) {
						subs_status = "Bundle Subscription Activated";
						subscriptionBuyNowPopUp = false;
						stat = crService.updateSubStatus(String.valueOf(order_id), String.valueOf(user_id));
					} else {
						subs_status = "Bundle Subscription not Activated";
					}
				} else {
					subs_status = "Bundle Subscription not found/disabled";
				}
			} catch (Exception e) {
				subs_status = "Bundle Subscription not Activated" + e.getLocalizedMessage();
				log.error("Error in checkBundleSubscription :: Error : " + e.getLocalizedMessage());
			}
		} else {
			subs_status = "user ver:v2 / product subscription disabled";
			redirectPaymentPage = false;
		}
		
		response.put("subs_status", subs_status);

		response.put("subscription_buynow_popup", subscriptionBuyNowPopUp);
		String paymentURL = "NA ";
		if (redirectPaymentPage) {

			if( chargebeeId.equalsIgnoreCase("NA") ) {
				chargebeeId = userServiceV4.createUserInChargebee(activateUser.getFirstname(),
						activateUser.getLastname(), activateUser.getEmail(), activateUser.getPhoneno(), userName,
						amount, desc);
			}

			if (!chargebeeId.equalsIgnoreCase("NA")) {
				boolean isPaymentSrcAvail = cbService.checkCardDetails(chargebeeId);
				if( !isPaymentSrcAvail ) {
					paymentURL = cbService.generatePaymentURL(chargebeeId);

					if (paymentURL != null) {
						response.put("payment_page_url", paymentURL);
						response.put("show_payment_page", redirectPaymentPage);
						response.put("payment_page_content", redirectPaymentPageContent);
					}
				} 

			}

		} else if(chargebeeId.equalsIgnoreCase("NA")) {
			userServiceV4.createUserInChargebee(activateUser.getFirstname(), activateUser.getLastname(),
					activateUser.getEmail(), activateUser.getPhoneno(), userName, amount, desc);
		}
		
		if( !activateUser.getReq_ver().equalsIgnoreCase("V1") ) {
			response.put("show_payment_page", false);
			response.put("subscription_buynow_popup", false);
		}
		
		return response;
	}

	private String getskuNumber(String imei) {
		if (enableSkuBasedActivation) {
			AssetModel assetModel = gatewayService.getAssetModelByMeid(imei);
			if (assetModel != null && !assetModel.getSkuNumber().equalsIgnoreCase("NA"))
				return assetModel.getSkuNumber();
		}
		return null;
	}

	public JResponse activateUserValidation(BindingResult result) {
		JResponse response = new JResponse();

		response.put("Status", 0);
		if (result.getFieldError("firstname") != null)
			response.put("Msg", result.getFieldError("name").getDefaultMessage());
		else if (result.getFieldError("lastname") != null)
			response.put("Msg", result.getFieldError("cmptype_id").getDefaultMessage());
		else if (result.getFieldError("cmptype_id") != null)
			response.put("Msg", result.getFieldError("cmptype_id").getDefaultMessage());
		else if (result.getFieldError("isAlreadyUser") != null)
			response.put("Msg", "isAlreadyUser must be true or false");
		else if (result.getFieldError("qrcCode") != null)
			response.put("Msg", "QR Code should not be empty");
		else if (result.getFieldError("throtsettingsid") != null)
			response.put("Msg", result.getFieldError("throtsettingsid").getDefaultMessage());
		else if (result.getFieldError("phoneno") != null)
			response.put("Msg", "Phone Number should not be empty");
		else if (result.getFieldError("email") != null)
			response.put("Msg", "Email should not be empty");
		else if (result.getFieldError("gatewayName") != null) {
			response.put("Msg", "Gateway Name should not be empty");
		} else if (result.getFieldError("purchasedfrom") != null) {
			response.put("Msg", "Purchasedfrom should not be empty");
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	private String setOrderChannel(ActivateUser activateUser) {
		String orderChannel = "NA";
		String subscriptionStatus = "";
		if (activateUser.getPurchasedfrom().toLowerCase().contains("technorv")) {
			orderChannel = "technorv";
			subscriptionStatus = "0";
			
		} else if (activateUser.getPurchasedfrom().toLowerCase().contains("rv")) {
			orderChannel = "rv";
			subscriptionStatus = "1";
		} else if (activateUser.getPurchasedfrom().toLowerCase().contains("amazon")) {
			orderChannel = "amazon";
			subscriptionStatus = "0";

		} else if (activateUser.getPurchasedfrom().toLowerCase().contains("walmart")) {
			orderChannel = "walmart";
			subscriptionStatus = "0";

		} else if (activateUser.getPurchasedfrom().toLowerCase().contains("facebook")) {
			orderChannel = "facebook";
			subscriptionStatus = "0";

		} else if (activateUser.getPurchasedfrom().toLowerCase().contains("ebay")) {
			orderChannel = "ebay";
			subscriptionStatus = "0";

		} else if (activateUser.getPurchasedfrom().toLowerCase().contains("campingworld")) {
			orderChannel = "campingworld";
			subscriptionStatus = "0";

		}
		return orderChannel;
	}
	
	public JResponse checkGateway(List<JGateway> userGateway, String qrcCode, String petName) {
		JResponse response = new JResponse();
		for (JGateway jGateway : userGateway) {
			if (jGateway.getQrcode().toLowerCase().equals(qrcCode.toLowerCase())) {
				response.put("Status", 0);
				response.put("Msg", RegisterUserError.ER006);
				response.put("errorcode", "ER006");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			if (jGateway.getName().toLowerCase().equals(petName.toLowerCase())) {
				response.put("Status", 0);
				response.put("Msg", RegisterUserError.petNameUserMessage);
				response.put("errorcode", "ER044");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;

	}

	public boolean nameCheck(Orders order, ActivateUser activateUser) {
		if (!order.getBilling_first_name().equalsIgnoreCase(activateUser.getFirstname().toLowerCase())
				|| !order.getBilling_last_name().equalsIgnoreCase(activateUser.getLastname().toLowerCase())) {
			return true;
		} else {
			return false;
		}

	}

	private JResponse userSignUp(@Valid ActivateUser activateUser, String userName, String country) {
		boolean loginCreationIssue = false;
		JResponse response = new JResponse();
		String errorResponse;
		String errorCode;
		String errorMsg = "Unable to register the product. Please send email to " + supportContactEmail.get(country) + ".";
		Company company = null;
		try {

			String userEmail = activateUser.getEmail().trim();
			userEmail = userEmail.toLowerCase();
			userEmail = userEmail.replaceAll("\\s+", "");
			String userPhone = activateUser.getPhoneno().trim();
			userPhone = userPhone.replace("(", "");
			userPhone = userPhone.replace(")", "");
			// Sign up the new USER
			log.info("==Entered into signup Section===========");
			SignUp signUp = new SignUp();
			signUp.setUsername(userName.toLowerCase());

			if (activateUser.getPurchasedfrom().toLowerCase().contains("rv")) {
				signUp.setFirstname(activateUser.getFirstname());
				signUp.setLastname(activateUser.getLastname());
			}

			String password = "";

			if (activateUser.isPwd_update()) {
				password = activateUser.getPassword().trim();
			} else {
				password = activateUser.getPhoneno();
				// password = password.replaceAll("\\W", "");
				password = password.replaceAll("[^\\d.]", ""); // remove other than numbers
				if (password.length() > 10) {
					password = password.substring(password.length() - 10);
				}
			}

			signUp.setPassword(_helper.bCryptEncoder(password));
			signUp.setEmail(userEmail);
			signUp.setAddress(activateUser.getAddress());
			signUp.setPhoneno(userPhone);
			signUp.setMobileno(userPhone);
			signUp.setCompanyname(activateUser.getFirstname());
			signUp.setCmptype_id(activateUser.getCmptype_id());
			signUp.setSupervisor("PQA");
			signUp.setThrotsettingsid(activateUser.getThrotsettingsid());
			long timeMilli = Calendar.getInstance().getTimeInMillis();
			String cmpName = signUp.getCompanyname() + "-" + timeMilli;
			signUp.setCompanyname(cmpName);
			try {
				ThrottlingSettings throtsettings = throttlingService.getThrotSettings(signUp.getThrotsettingsid()).get(0);

				CompanyType cmpType = companyTypeServ.getCompanyType(signUp.getCmptype_id(), 1).get(0);
				
				log.info("Before entering company ");
				
				company = companyService.createCompany(signUp,throtsettings,cmpType);
				
				log.info("After entering company ");
//				company = companyService.getCompanyByEmail(signUp.getEmail());
				
			} catch (Exception e) {
				log.info("Error in company creation ");
				response.put("Status", 0);
				response.put("Msg", errorMsg);
				errorCode = "ER008";
				errorResponse = "Signup Error : " + RegisterUserError.ER008 + "Exception  : " + e.getMessage();
				return response;
			}
//			company = companyService.getCompanyByEmail(signUp.getEmail());
			CompanyConfig cfg = new CompanyConfig(company);
			
			//Set temperature unit as celcius
			if(country.equalsIgnoreCase("AU"))
				cfg.setTemperatureunit("C");
			
			boolean companyConfigCreationStatus = companyService.saveCompanyConfg(cfg);
			log.info("Company Config Creation Status : "+companyConfigCreationStatus);
			
			if( !companyConfigCreationStatus && company.getId()!=0 ) {
				companyService.deleteCompany(company.getId());
			}
			
			Groups group = new Groups();
			group.setName("Default");
			boolean groupCreationStatus = groupservices.saveORupdateGroups(group, company.getId());
			
			if( !groupCreationStatus & company!=null ) {
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
			
			boolean signUPResponse = userService.signUp(signUp,company);

			User user = userService.getUserByName(signUp.getEmail());
			user.setVerified(true);
			user.setCountry(country);
			userService.updateUser(user);

			log.info("==Signup done into signup Section===========");
			response.put("Status", 1);
			response.put("Msg", "success");

			// update evalidation
			async.updateEvalidation(user.getId(), password);

		} catch (ConstraintViolationException ce) {

			loginCreationIssue = true;

			log.info("signup: ConstraintViolationException:\n" + ce.getStackTrace());
			response.put("Status", 0);
			if (Integer.parseInt(activateUser.getCmptype_id()) == 3) {
				response.put("Msg", "Email ID / Name already exists. Please try with alternate one.");// RvPet
			} else {
				response.put("Msg",
						"Username / Company Name /Gateway Name already exists.Please try with alternate one.");
			}
			errorCode = "ER007";
			errorResponse = RegisterUserError.ER007;
			
			if( company != null && company.getId()!=0 ) {
				boolean status = groupservices.deleteGroups(company.getId());
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
			
		} catch (Exception e) {
			loginCreationIssue = true;
			log.error("signup:::" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", errorMsg);
			errorCode = "ER008";
			errorResponse = "Signup Error : " + RegisterUserError.ER008 + "Exception  : " + e.getMessage();
			if( company != null && company.getId()!=0 ) {
				boolean status = groupservices.deleteGroups(company.getId());
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
		} // Sigh up the User ends
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	private void sendDynamicMsg(Gateway gateway) {

		String messages = "";
		String deviceModel = gateway.getModel().getModel().toLowerCase();
		String gpsDynamicmsg = "gpsmode=standalone", gpsDynamicmsg2 = "SFWSTORAGE=ON",
				gpsDynamicmsg3 = "reportinterval=900", gpsDynamicmsg4 = "maxsleeptime=900",
				gpsDynamicmsg5 = "modemresetint=3600", gpsDynamicmsg6 = "setqrc=";
		String n7Cmd = "batoffset=-0.7,chgoffset=1.9,tempoffset=-1.2,fullchgoffset=-2.5";

		if (!gateway.getQrcode().isEmpty() && !gateway.getQrcode().equalsIgnoreCase("NA"))
			gpsDynamicmsg6 = gpsDynamicmsg6 + gateway.getQrcode().trim();
		else
			gpsDynamicmsg6 = "";
		
		if (deviceModel.contains("n13")) {
			gpsDynamicmsg3 = "reportinterval=600";
			gpsDynamicmsg4 = "maxsleeptime=600";
		}

		if (deviceModel.contains("nt3d")) {
			messages = gpsDynamicmsg3 + "," + gpsDynamicmsg4 + "," + gpsDynamicmsg2 + "," + gpsDynamicmsg5;
		} else if (deviceModel.contains("nt3f")) {
			messages = gpsDynamicmsg2 + "," + gpsDynamicmsg3 + "," + gpsDynamicmsg4 + "," + gpsDynamicmsg5;
		} else if (deviceModel.toLowerCase().contains("n13-500") || deviceModel.toLowerCase().contains("n13-503") || deviceModel.toLowerCase().contains("n13g-503") ) {
			messages = gpsDynamicmsg + "," + gpsDynamicmsg2 + "," + gpsDynamicmsg3;
		} else if (deviceModel.toLowerCase().contains("n13-502") || deviceModel.toLowerCase().contains("n13g-502") ) {
			messages = gpsDynamicmsg2 + "," + gpsDynamicmsg3;
		} else if (deviceModel.contains("n1") && !deviceModel.contains("n13")) {
			messages = gpsDynamicmsg + "," + gpsDynamicmsg2; // N7A-503 NT3K N7-504 NT3K
		} else if (deviceModel.contains("n7")) {
			messages = gpsDynamicmsg3 + "," + gpsDynamicmsg4 + "," + gpsDynamicmsg2 + "," + gpsDynamicmsg5;

			if (deviceModel.equalsIgnoreCase("n7-504 nt3g")) {
				messages = messages + "," + n7Cmd;
			}
		}
		if (!gpsDynamicmsg6.isEmpty())
			messages = messages + "," + gpsDynamicmsg6;
		
		if(gateway.getModel().getIsgps().equalsIgnoreCase("1") && !(deviceModel.contains("n13")))
			messages = messages + ",gpstrackmode=onetime";

		async.sendDynamicCommand(Long.toString(gateway.getId()), messages, 0L);
	}

	public JCreateGateway createOrUpdateGateway(ActivateUser activateUser, Inventory inventory, int model_id,
			long group_id, String sensor_available, String passwordType, User user, String oldMeid,
			int recallReplaceAction) {

		boolean updateCreaditStatus = false;
		boolean saveGatewayReportStatus = false;
		boolean saveLastGatewayReport = false;
		boolean isGatewayCreated = false;

		JCreateGateway jcreateGateway = new JCreateGateway();
		
		JGateway jgateway = new JGateway();
		jgateway.setName(activateUser.getGatewayName());
		jgateway.setMeid(inventory.getMeid());
		jgateway.setMdn(inventory.getMdn());
		jgateway.setCarrier("NA");
		jgateway.setModelid(model_id);
		jgateway.setGroupid(group_id);
		jgateway.setEnable(true);
		jgateway.setAlive(false);
		jgateway.setSensorEnable(sensor_available);
		jgateway.setPasswordtype(Long.parseLong(passwordType));
		jgateway.setDescription("");
		jgateway.setQrcode(activateUser.getQrcCode().trim());
		jgateway.setMacid(inventory.getMac_id());
		jgateway.setShowOrderId(activateUser.isShow_orderid());
		jgateway.setOldMeid(oldMeid);
		jgateway.setRecallReplaceAction(recallReplaceAction);
		jgateway = gatewayService.gatewayExitsinDB(jgateway, user.giveCompany().getId());

		Asset oldAsset = null;
		boolean updateGateway = false;
		if (jgateway.getId() != 0) {
			updateGateway = true;
			oldAsset = gatewayService.getAssetById(jgateway.getId());
		}
		
		Gateway gateway = new Gateway();
		try {
			if (oldMeid.equalsIgnoreCase("NA")) {
				gateway = gatewayService.saveORupdateGateway(jgateway, user.giveCompany().getId());
			} else {
				gateway = gatewayService.saveORupdateRecallGateway(jgateway, user.giveCompany().getId());
				boolean recallGatewaySaved = gatewayService.saveRecallGateway(gateway);
				boolean updatedInLastGatewayReport = gatewayService.changeDefalutRptInLastGateway(gateway.getId(),
						IrisservicesUtil.getCurrentTimeUTC(), 1);
				log.info("updated date saved in last gateway rpt : " + updatedInLastGatewayReport);
			}

			isGatewayCreated = true;
			
			if( gateway == null ) {
				if( updateGateway ) {
					 boolean revertStatus = gatewayService.updateAsset(oldAsset);
					 log.info("old asset revert status : "+revertStatus);
				} else {
					Asset asset = new Asset();
					asset.setAssetaddress( inventory.getMeid() );
					String changedMeid = gatewayService.changeOldAssertDetailsToZ(asset);
					boolean meidChangedInAssert = gatewayService.changeOldAssertDetails(gateway.getId(),changedMeid);
					log.info("meid changed in assert status : "+meidChangedInAssert);
				}	
				isGatewayCreated = false;
				jcreateGateway.setGatewayCreated(false);
			}
		} catch (Exception e1) {
			log.error("Exception in create or update gateway : "+e1.getLocalizedMessage());
			if( updateGateway ) {
				 boolean revertStatus = gatewayService.updateAsset(oldAsset);
				 log.info("old asset revert status : "+revertStatus);
			} else {
				Asset asset = new Asset();
				asset.setAssetaddress( inventory.getMeid() );
				String changedMeid = gatewayService.changeOldAssertDetailsToZ(asset);
				boolean meidChangedInAssert = gatewayService.changeOldAssertDetails(gateway.getId(),changedMeid);
				log.info("meid changed in assert status : "+meidChangedInAssert);
			}
			return null;
		}

		if (jgateway.isUserGatDis()) {
			if (!oldMeid.equalsIgnoreCase("NA")) {

				Object[] gatewayListO = user.getGateways().toArray();
				List<Gateway> gatewayList = new ArrayList<Gateway>();
				for (Object o : gatewayListO) {
					gatewayList.add((Gateway) o);
				}
				user.getGateways().removeAll(gatewayList);

				int index = 0;
				for (Gateway gate : gatewayList) {
					if (gate.getMeid().equalsIgnoreCase(oldMeid)) {
						break;
					}
					index++;
				}
				gatewayList.remove(index);
				gatewayList.add(gateway);
				user.getGateways().addAll(gatewayList);

			} else {
				user.getGateways().add(gateway);
			}

			user.setFirstname(activateUser.getFirstname());
			user.setLastname(activateUser.getLastname());

			userService.updateUser(user);
		}

		try {
			gatewayService.updateGatewayCredit(gateway.getId(), user.giveCompany().getId());
			updateCreaditStatus = true;

		} catch (Exception e) {
			log.error("Gateway credits not updated for Gateway: " + gateway.getId() + "  : " + e.getMessage());
			log.error("Gateway credits not updated for Gateway: " + gateway.getId() + "  : " + e.getLocalizedMessage());
		}

		if (jgateway.getId() == 0) {
			try {
				// gatewayService.updateGatewayCredit(gateway.getId(),
				// user.giveCompany().getId());
				// updateCreaditStatus = true;

				reportService.saveGatewayReport(gateway, activateUser.getLat(), activateUser.getLon());
				saveGatewayReportStatus = true;

				reportService.saveLastGatewayReport(gateway, activateUser.getLat(), activateUser.getLon());
				saveLastGatewayReport = true;
			} catch (Exception e) {
				log.error("6saveORupdateGateway-Default Reports Canot be Generated::::" + e.getLocalizedMessage());
			}
		} else {
			boolean gatewayIdIsThere = reportService.checkRecordInLastGatewayReport(gateway);

			if (!gatewayIdIsThere) {
				reportService.saveLastGatewayReport(gateway, activateUser.getLat(), activateUser.getLon());
				saveLastGatewayReport = true;
			} else {
				reportService.updateLastGatewayReport(gateway);
			}

		}
		
		jcreateGateway.setUpdateCreaditStatus(updateCreaditStatus);
		jcreateGateway.setSaveGatewayReportStatus(saveGatewayReportStatus);
		jcreateGateway.setSaveLastGatewayReport(saveLastGatewayReport);
		jcreateGateway.setGatewayCreated(isGatewayCreated);
		jcreateGateway.setJgateway(jgateway);
		jcreateGateway.setGateway(gateway);
		return jcreateGateway;
	}
	
	public JResponse getStatusMsg(JResponse response, String mobilePage, long monitortypeid, boolean isFreeVpmAdded) {
		if (mobilePage.toLowerCase().contains("login")) {
			if (monitortypeid == 1) {
				response.put("Msg", (isFreeVpmAdded ? RegisterUserError.loginPageActivationWithVPM_Message
						: RegisterUserError.loginPageActivationMessage));
				response.put("display_msg",
						(isFreeVpmAdded ? RegisterUserError.loginPageActivationWithVPM_DisplayMessage
								: RegisterUserError.loginPageActivationMessage));
			} else {
				response.put("Msg", RegisterUserError.loginPageFurbitActivationMessage);
				response.put("display_msg", RegisterUserError.loginPageFurbitActivationMessage);
			}

		} else if (mobilePage.toLowerCase().contains("home")) {
			if (monitortypeid == 1) {
				response.put("Msg", (isFreeVpmAdded ? RegisterUserError.homePageActivationWithVPM_Message
						: RegisterUserError.homePageActivationMessage));
				response.put("display_msg", (isFreeVpmAdded ? RegisterUserError.homePageActivationWithVPM_DisplayMessage
						: RegisterUserError.homePageActivationMessage));
			} else {
				response.put("Msg", RegisterUserError.loginPageFurbitActivationMessage);
				response.put("display_msg", RegisterUserError.loginPageFurbitActivationMessage);
			}

		} else if (mobilePage.toLowerCase().contains("account")) {
			if (monitortypeid == 1) {
				response.put("Msg", (isFreeVpmAdded ? RegisterUserError.accountPageActivationWithVPM_Message
						: RegisterUserError.accountPageActivationMessage));
				response.put("display_msg",
						(isFreeVpmAdded ? RegisterUserError.accountPageActivationWithVPM_DisplayMessage
								: RegisterUserError.accountPageActivationMessage));
			} else {
				response.put("Msg", RegisterUserError.accountPageFurbitActivationMessage);
				response.put("display_msg", RegisterUserError.accountPageFurbitActivationMessage);
			}

		} else {
			if (monitortypeid == 1) {
				response.put("Msg", (isFreeVpmAdded ? RegisterUserError.loginPageActivationWithVPM_Message
						: RegisterUserError.loginPageActivationMessage));
				response.put("display_msg",
						(isFreeVpmAdded ? RegisterUserError.loginPageActivationWithVPM_DisplayMessage
								: RegisterUserError.loginPageActivationMessage));
			} else {
				response.put("Msg", RegisterUserError.loginPageFurbitActivationMessage);
				response.put("display_msg", RegisterUserError.loginPageFurbitActivationMessage);
			}

		}

		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
	public User updateUser(User user) {

		long timeMilli = Calendar.getInstance().getTimeInMillis();
		String newUserName = user.getUsername();

		if (newUserName.length() > 10)
			newUserName = user.getUsername().substring(0, 9) + "-" + timeMilli;

		log.info("Username updated for new user : " + user.getUsername() + " New UserName : " + newUserName);

		user.setUsername(newUserName);
		user.setEmail(newUserName);
		user.setEnable(false);
		user.setAuthKey(newUserName);
		user.setMobileno("1234567890");
		if (!(user.getSignupType() == null)) {
			userService.updateUser(user);
		}
		return user;
	}
	
}
