package com.nimble.irisservices.service.impl;

import javax.transaction.Transactional;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dao.IVPMDao;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.VPMVetResponse;
import com.nimble.irisservices.entity.VPMChatHistory;
import com.nimble.irisservices.service.IVPMService;


@Service
@Transactional
public class VPMServiceImpl implements IVPMService{

	@Autowired
	IVPMDao vpmDao;
	
	IrisservicesUtil irisUtil;
	
	private static final Logger log = LogManager.getLogger(VPMServiceImpl.class);
	
	@Override
	public VPMChatHistory checkVPMChatHistory(String userId, int vpmChannelRetriveMinutes) {
		return vpmDao.checkVPMChatHistory(userId, vpmChannelRetriveMinutes) ;
	}

	@Override
	public boolean endVPMChannel(String channelId) {
		return vpmDao.endVPMChannel( channelId );
	}

	@Override
	public boolean saveVPMChannel(JResponse response) {
		boolean vpmChannelStored = false;
		try {
			
			String currentDateTime = irisUtil.getCurrentTimeUTC();
			VPMVetResponse vpmData = (VPMVetResponse) response.getResponse().get("data");
			VPMChatHistory vpmChatHistory = new VPMChatHistory();
			vpmChatHistory = new VPMChatHistory();
			vpmChatHistory.setVpmUserId(vpmData.getIdentity());
			vpmChatHistory.setVetId(vpmData.getVet());
			vpmChatHistory.setChannelSid(vpmData.getChannel_sid());
			vpmChatHistory.setCreatedOn(currentDateTime);
			vpmChatHistory.setUpdatedOn(currentDateTime);
			vpmChannelStored = vpmDao.saveOrUpdateVPMChatHistory(vpmChatHistory);
			log.info(" vpm new channel stored : " + vpmChannelStored);
			return vpmChannelStored;
		} catch (Exception e) {
			log.error("Error in saveVPMChannel : " + e.getLocalizedMessage());
			return vpmChannelStored;
		}
	}

	@Override
	public String getchannelIdByUserId(long userId) {
		return vpmDao.getchannelIdByUserId(userId);
	}

	
	
}
