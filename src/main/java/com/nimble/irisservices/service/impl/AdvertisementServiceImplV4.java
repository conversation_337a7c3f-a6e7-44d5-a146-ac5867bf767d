package com.nimble.irisservices.service.impl;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.dao.IAdvertisementDaoV4;
import com.nimble.irisservices.entity.Advertisement;
import com.nimble.irisservices.service.IAdvertisementServiceV4;

@Service
@Transactional
public class AdvertisementServiceImplV4 implements IAdvertisementServiceV4{

	@Autowired
	IAdvertisementDaoV4 advertisementDaoV4;
	
	@Override
	public List<Advertisement> getAdvertismentUrl() {
		return advertisementDaoV4.getAdvertismentUrl();
	}
}
