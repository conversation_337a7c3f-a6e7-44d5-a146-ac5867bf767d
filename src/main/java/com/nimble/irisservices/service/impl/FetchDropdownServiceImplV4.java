package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IDropdownDaoV4;
import com.nimble.irisservices.entity.AlertType;
import com.nimble.irisservices.entity.OrderChannel;
import com.nimble.irisservices.service.IDropdownServiceV4;

@Service
@Transactional
public class FetchDropdownServiceImplV4 implements IDropdownServiceV4{

	@Autowired
	IDropdownDaoV4 dropdownDaoV4;
	
	
	@Override
	public List<AlertType> getAlertTypesV4() {
		return dropdownDaoV4.getAlertTypesV4();
	}

	@Override
	public List<OrderChannel> getOrderChannelV4() {
		return dropdownDaoV4.getOrderChannelV4();
	}

}
