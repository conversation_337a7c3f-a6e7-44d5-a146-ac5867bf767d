package com.nimble.irisservices.service.impl;

import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IPetSpeciesDao;
import com.nimble.irisservices.dto.JBMIinfo;
import com.nimble.irisservices.dto.JBowlPetProfile;
import com.nimble.irisservices.dto.JCaloriesinfo;
import com.nimble.irisservices.dto.JInjuryinfo;
import com.nimble.irisservices.dto.JPetprofileFlutter;
import com.nimble.irisservices.dto.JPetprofileV2;
import com.nimble.irisservices.dto.JPetprofileWatch;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.PetSpecies;
import com.nimble.irisservices.service.IPetSpeciesServices;

@Service
@Transactional
public class PetSpeciesServiceImpl implements IPetSpeciesServices {

	@Autowired
	IPetSpeciesDao petSpeciesDao;

	@Override
	public boolean saveORupdateSpecies(PetSpecies petSpecies) {
		return petSpeciesDao.saveORupdateSpecies(petSpecies);
	}

	@Override
	public List<PetSpecies> getPetSpecies() {
		return petSpeciesDao.getPetSpecies();
	}

	@Override
	public PetSpecies getPetSpecies(Long id) {
		return petSpeciesDao.getPetSpecies(id);
	}

	@Override
	public PetSpecies getPetSpeciesByName(String name) {
		return petSpeciesDao.getPetSpeciesByName(name);
	}
	
	@Override
	public PetProfile createPetprofile(JPetprofileV2 jpetprofile) {
		return petSpeciesDao.createPetprofile(jpetprofile);
	}

	@Override
	public List<PetProfile> getPetprofile(String columnname, String value){
		return petSpeciesDao.getPetprofile(columnname, value);
	}
	
	@Override
	public List<JPetprofileV2> getJPetprofilesByUser(long userid,String speciesid){
		return petSpeciesDao.getJPetprofilesByUser(userid,speciesid);
				
	}
	
	@Override
	public JBMIinfo getBMIinfo(long breedid, String gender, float bmivalue) {
		return petSpeciesDao.getBMIinfo(breedid, gender, bmivalue);
	}
	
	@Override
	public JInjuryinfo getInjuryinfo(float injuryvalue) {
		return petSpeciesDao.getInjuryinfo(injuryvalue);
	}
	
	@Override
	public HashMap<String,String> getFormulas(){
		return petSpeciesDao.getFormulas();
	}
	
	@Override
	public JCaloriesinfo getFactor(JPetprofileV2 pet) {
		return petSpeciesDao.getFactor(pet);
	}
	
	@Override
	public boolean checkPetnameExist(String name, long userid, long petid) {
		return petSpeciesDao.checkPetnameExist( name,  userid,  petid);
	}
	
	@Override
	public boolean saveHealthReport(String qry) {
		return petSpeciesDao.saveHealthReport(qry);
	}
	
	@Override
	public JResponse retrieveHealthReport(long userid) {
		return petSpeciesDao.retrieveHealthReport(userid);
	}
	
	@Override 
	public boolean checkGatewayExist(long gateway_id, long petid) {
		return petSpeciesDao.checkGatewayExist(gateway_id,petid);
	}
	
	@Override
	public long getClassification( int age, float weight) {
		return petSpeciesDao.getClassification(age, weight);
	}

	@Override
	public List<JPetprofileFlutter> getJPetprofilesForFlutter(long userid, String species) {
		return petSpeciesDao.getJPetprofilesForFlutter(userid, species);
	}
	
	@Override
	public List<JPetprofileWatch> getJPetprofilesForWatch(long userid){
		return petSpeciesDao.getJPetprofilesForWatch(userid);
	}

	@Override
	public PetProfile createBowlPetProfile(JBowlPetProfile jBowlPetProfile) {
		return petSpeciesDao.createBowlPetProfile(jBowlPetProfile);
	}

	@Override
	public List<JBowlPetProfile> getJBowlPetProfile(long user_id, long gateway_id) {
		return petSpeciesDao.getJBowlPetProfile(user_id, gateway_id);
	}

	@Override
	public JPetprofileFlutter getJPetprofiles(long gatewayId) {
		return petSpeciesDao.getJPetprofiles(gatewayId);
	}
	
	@Override
	public List<JPetprofileV2> getJPetprofilesByUserAndMonitor(long userid,String speciesid,long monitortype){
		return petSpeciesDao.getJPetprofilesByUserAndMonitor(userid,speciesid,monitortype);
				
	}
	
	@Override
	public List<JBowlPetProfile> getJBowlPetProfileById(long user_id, long gateway_id) {
		return petSpeciesDao.getJBowlPetProfileById(user_id, gateway_id);
	}
}
