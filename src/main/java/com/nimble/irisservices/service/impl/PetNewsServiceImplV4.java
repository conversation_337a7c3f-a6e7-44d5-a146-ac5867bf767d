package com.nimble.irisservices.service.impl;

import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IPetNewsDaoV4;
import com.nimble.irisservices.dto.JPetNews;
import com.nimble.irisservices.entity.PetNews;
import com.nimble.irisservices.service.IPetNewsServiceV4;

@Service
@Transactional
public class PetNewsServiceImplV4 implements IPetNewsServiceV4 {

	@Autowired
	IPetNewsDaoV4 pnDao;

	@Override
	public ArrayList<JPetNews> listAppPetNews(String ostype, int offset, int rowcount, boolean is_device, boolean is_subscription) {
		return pnDao.listAppPetNews(ostype, offset, rowcount, is_device, is_subscription);
	}

	@Override
	public ArrayList<PetNews> listWebPetNews() {
		return pnDao.listWebPetNews();
	}

	@Override
	public boolean saveOrUpdatePetNews(PetNews petnews) {
		return pnDao.saveOrUpdatePetNews(petnews);
	}

	@Override
	public PetNews getPetNews(long id) {
		return pnDao.getPetNews(id);
	}

	@Override
	public int getPetNewsCount() {
		return pnDao.getPetNewsCount();
	}

}
