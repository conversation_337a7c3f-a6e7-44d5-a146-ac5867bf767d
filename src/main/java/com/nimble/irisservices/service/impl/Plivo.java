package com.nimble.irisservices.service.impl;

import java.io.File;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;
import java.util.Properties;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.AuthenticationFailedException;
import javax.mail.Authenticator;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import com.nimble.irisservices.Util.SecretManagerService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nimble.irisservices.service.SMSInterface;
import com.plivo.api.PlivoClient;
import com.plivo.api.models.call.Call;
import com.plivo.api.models.message.MessageCreateResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;

@Service
@Primary
public class Plivo implements SMSInterface{
	
	private static final Logger log = LogManager.getLogger(Plivo.class);
	
	private String authId	 = "MAY2NINZU4ODVLMMYWZT";
	private String authToken = "MWZiYmQwODlmOTI0ZGUzMGI5ODhhNDRkYzU2ODA4";

	@Autowired
	private SecretManagerService secretManagerService;

	public boolean SendMessage(String phoneNo, String message,String cmpid,
			String cmpname, String appname,String plivono,boolean enablePowerBack,String powerBackUUID) {
		try {
			PlivoClient client = new PlivoClient(authId, authToken);
			phoneNo = phoneNo.replace(",", "<");
			List<String> destPhList = Collections.singletonList(phoneNo); 
			
			MessageCreateResponse response = null;
			
			if(enablePowerBack) {
				response = com.plivo.api.models.message.Message.creator(destPhList, message,powerBackUUID).client(client).create();
				
			}else {
				response=com.plivo.api.models.message.Message.creator(plivono, destPhList, message).client(client).create();
			}			log.info("msg sent details : "+response);
		}
		catch (Exception e) {
			log.info("SendMessage: PlivoException = "+e.getLocalizedMessage());
			return false;
		}
//		RestAPI api = new RestAPI(authId, authToken, "v1");
//		System.out.println("plivo");
//		LinkedHashMap<String, String> parameters = new LinkedHashMap<String, String>();
//		
//		//parameters.put("url","");
//		//parameters.put("method","POST");
////		parameters.put("src","13308221830");
////		parameters.put("src","18442004954");
//		parameters.put("src",plivono);
//		parameters.put("dst",phoneNo);
//		parameters.put("text",message);
//		System.out.println("send message");
//		try {
//			@SuppressWarnings("unused")
//			MessageResponse response = api.sendMessage(parameters);
//			
//			System.out.println("sent message");
//		}
//		catch (PlivoException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//			log.info("SendMessage: PlivoException = "+e.getLocalizedMessage());
//			System.out.println("not sent");
//			return false;
//		}
		
		return true;
	}

	public boolean SendVoiceMessage(String phNo,String message, String cmpid,
			String cmpname, String appname,String ipaddress,String srcphno) {
	
		try{
			com.plivo.api.Plivo.init(authId, authToken);
			String phoneNo = phNo.replace(",", "<");
			List<String> destPhList = Collections.singletonList(phoneNo); 
			
			String msg_enc  = URLEncoder.encode(message.trim(),"UTF-8");
			String cmp_enc	= URLEncoder.encode(cmpname.trim(),"UTF-8");

			String URL = "http://"+ipaddress+":8080/irisservices/v3.0/texttoSpeech?msg="+msg_enc+"&phoneno="+phNo+
					"&cmpid="+cmpid+"&cmpname="+cmp_enc+"&appname=Iris3.0&type=voice";

			log.info("VoiceUrl:" + URL);

					
			Call.creator(srcphno, destPhList, URL).answerMethod("GET").create();
			    
//			String msg_enc  = URLEncoder.encode(message.trim(),"UTF-8");
//			String cmp_enc	= URLEncoder.encode(cmpname.trim(),"UTF-8");
//
//			String URL = "http://"+ipaddress+":8080/irisservices/v3.0/texttoSpeech?msg="+msg_enc+"&phoneno="+phoneNo+
//					"&cmpid="+cmpid+"&cmpname="+cmp_enc+"&appname=Iris3.0&type=voice";
//
//			log.info("VoiceUrl:" + URL);
//
//			System.out.println("VoiceUrl:" + URL);
//			RestAPI api = new RestAPI(authId, authToken, "v1");
//
//
//			LinkedHashMap<String, String> parameters = new LinkedHashMap<String, String>();
//			parameters.put("to",phoneNo); // The phone numer to which the all has to be placed
//			parameters.put("from","13308221830"); // The phone number to be used as the caller id
//
//			/* answer_url is the URL invoked by Plivo when the outbound call is answered 
//			 and contains instructions telling Plivo what to do with the call */
//
//
//			parameters.put("answer_url",URL);
//			parameters.put("answer_method","GET"); // method to invoke the answer_url
//
//			/* Example for asynchronous request
//			   callback_url is the URL to which the API response is sent.
//			   parameters.put("callback_url","http://myvoiceapp.com/callback/");
//			   parameters.put("callback_method","GET"); // The method used to notify the callback_url.*/
//
//			try {
//				/* Make an outbound call and print the response */
//				Call resp = api.makeCall(parameters);
//				log.info("ServerCode " +resp.serverCode);
//
//			} catch (PlivoException e) {
//				log.info("SendVoiceMessage: PlivoException = "+e.getLocalizedMessage());
//				return false;
//			}
//		
		}catch(Exception e){
			log.info("SendVoiceMessage Exception :" + e.getLocalizedMessage());
		}
		return true;
	}

	@Override
	public boolean SendEmail(String toAddr, String sub, String mailmsg)
    {   

        String login = "<EMAIL>";
        String password ="Nimble123";
       
        try
        {
            Properties props = new Properties();
            props.setProperty("mail.host", "mail.nimblewireless.com");
            props.setProperty("mail.smtp.auth", "true");
            props.setProperty("mail.smtp.port", "587");            
			props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");

            Authenticator auth = new SMTPAuthenticator(login, password);
            Session session = Session.getInstance(props, auth);

            MimeMessage msg = new MimeMessage(session);
            msg.setText(mailmsg.trim());
            msg.setSubject(sub.trim());
            msg.setFrom(new InternetAddress(login));
            msg.addRecipient(Message.RecipientType.TO, new InternetAddress(toAddr));
           
            Transport.send(msg);
            System.out.println();
            log.info("Message sent to "+toAddr);
            System.out.println("Message sent to "+toAddr);
                       
            return true;
        }
        catch (AuthenticationFailedException ex)
        {
        	log.info("Authentication Exception occured :" + ex.getMessage());
        }
        catch (AddressException ex)
        {
        	log.info("Address Exception occured :" + ex.getMessage());
        }
        catch (MessagingException ex)
        {
        	log.info("Message Exception occured :" + ex.getMessage()); 
        }
        return false;
    }

    private static class SMTPAuthenticator extends Authenticator
    {
        private PasswordAuthentication authentication;

        public SMTPAuthenticator(String login, String password)
        {
            authentication = new PasswordAuthentication(login, password);
        }

        protected PasswordAuthentication getPasswordAuthentication()
        {
            return authentication;
        }
    }
    
    
    @Override
    public boolean SendEmail_SES(String alert_email,String toAddr, String sub, String mailmsg,String filename,boolean isRVMailId)
    { 
//    	log.info("Entered into SendEmail_SES :: from_add : "+ alert_email + " :: to_add : "+toAddr);
    	String EMAIL_HOST 			= "email-smtp.us-west-2.amazonaws.com";
		String SES_SECRET_NAME = "/prod/svc/awsses";
    	String EMAIL_HOST_USER 		= secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_access_key");
    	String EMAIL_HOST_PASSWORD 	= secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_secret_key");
    	String login = null;
//    	if(isRVMailId)
//    		login = "<EMAIL>";
//    	else
//    		login = "<EMAIL>";
    	login = alert_email;
    	Transport transport = null;

    	try
    	{
    		Properties props = new Properties();
    		props.put("mail.transport.protocol", "smtps");     
    		props.put("mail.smtp.auth", "true");
    		props.setProperty("mail.smtp.port", "587");            
			props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");

    		// Create a Session object to represent a mail session with the specified properties. 
    		Session session = Session.getDefaultInstance(props);

    		// Create a message with the specified information. 
    		MimeMessage msg = new MimeMessage(session);
    		msg.setFrom(new InternetAddress(login));
    		msg.setRecipient(Message.RecipientType.TO, new InternetAddress(toAddr));
    		msg.setSubject(sub);
    		msg.setContent(mailmsg,"text/plain");
    		
    		// Create the message part
			BodyPart messageBodyPart = new MimeBodyPart();

			// Now set the actual message
			messageBodyPart.setText(mailmsg);
			
			if(filename!= null){
				File file = new File(filename);
				if(file.exists()){
					log.info("SendEmail_SES: attachment file exists, file path= "+file.getAbsolutePath());
					// Create a multipart message
					Multipart multipart = new MimeMultipart();
		
					// Set text message part
					multipart.addBodyPart(messageBodyPart);
		
					// Part two is attachment
					messageBodyPart = new MimeBodyPart();
					DataSource source = new FileDataSource(filename);
					messageBodyPart.setDataHandler(new DataHandler(source));
					messageBodyPart.setFileName(filename);
					multipart.addBodyPart(messageBodyPart);
		
					// Send the complete message parts
					msg.setContent(multipart);
				}else{
					log.info("SendEmail_SES:Attachment file not created successfully, File '"+filename+"' does not exists");
				}
				
				
			}

    		// Create a transport.        
    		transport = session.getTransport();
    		log.info("SendEmail_SES:Attempting to send an email through the Amazon SES SMTP interface.");

    		// Connect to Amazon SES using the SMTP username and password you specified above.
    		transport.connect(EMAIL_HOST, EMAIL_HOST_USER, EMAIL_HOST_PASSWORD);

    		// Send the email.
    		transport.sendMessage(msg, msg.getAllRecipients());
    		//System.out.println("Email sent!");
    		return true;
    	}
    	catch (AddressException ex)
    	{
    		log.error("SendEmail_SES :: from_add : "+ alert_email + " :: to_add : "+toAddr);
    		log.error("SendEmail_SES: AddressException : "+ex.getLocalizedMessage());
    	}
    	catch (MessagingException ex)
    	{
    		log.error("SendEmail_SES: MessagingException : "+ex.getLocalizedMessage()); 
    	}
    	catch (Exception ex) {
    		log.error("SendEmail_SES:Excep : "+ex.getLocalizedMessage());
    	}

    	finally
    	{
    		try {
    			if(null != transport)
    				transport.close();
    		}
    		catch(Exception e)
    		{
    			log.error("SendEmail_SES:Exception: " + e.getMessage());
    		}
    	}
    	return false;
    }
    
    @Override
    public boolean SendEmail_SESV2(String alert_email,String toAddr, String sub, String mailmsg,String filename,boolean isRVMailId)
    { 
//    	log.info("Entered into SendEmail_SES :: from_add : "+ alert_email + " :: to_add : "+toAddr);
    	String EMAIL_HOST 			= "email-smtp.us-west-2.amazonaws.com";
		String SES_SECRET_NAME = "/prod/svc/awsses";
    	String EMAIL_HOST_USER 		= secretManagerService.getSecretValue(SES_SECRET_NAME,"aws_access_key");
    	String EMAIL_HOST_PASSWORD 	= secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_secret_key");
    	String login = null;
//    	if(isRVMailId)
//    		login = "<EMAIL>";
//    	else
//    		login = "<EMAIL>";
    	login = alert_email;
    	Transport transport = null;

    	try
    	{
    		Properties props = new Properties();
    		props.put("mail.transport.protocol", "smtps");     
    		props.put("mail.smtp.auth", "true");
    		props.setProperty("mail.smtp.port", "587");            
			props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");

    		// Create a Session object to represent a mail session with the specified properties. 
    		Session session = Session.getDefaultInstance(props);

    		// Create a message with the specified information. 
    		MimeMessage msg = new MimeMessage(session);
    		msg.setFrom(new InternetAddress(login));
    		msg.setRecipient(Message.RecipientType.TO, new InternetAddress(toAddr));
    		msg.setSubject(sub);
    		msg.setContent(mailmsg,"text/html; charset=UTF-8");
    		
    		// Create the message part
			BodyPart messageBodyPart = new MimeBodyPart();

			// Now set the actual message
			messageBodyPart.setText(mailmsg);
			
			if(filename!= null){
				File file = new File(filename);
				if(file.exists()){
					log.info("SendEmail_SES: attachment file exists, file path= "+file.getAbsolutePath());
					// Create a multipart message
					Multipart multipart = new MimeMultipart();
		
					// Set text message part
					multipart.addBodyPart(messageBodyPart);
		
					// Part two is attachment
					messageBodyPart = new MimeBodyPart();
					DataSource source = new FileDataSource(filename);
					messageBodyPart.setDataHandler(new DataHandler(source));
					messageBodyPart.setFileName(filename);
					multipart.addBodyPart(messageBodyPart);
		
					// Send the complete message parts
					msg.setContent(multipart);
				}else{
					log.info("SendEmail_SES:Attachment file not created successfully, File '"+filename+"' does not exists");
				}
				
				
			}

    		// Create a transport.        
    		transport = session.getTransport();
    		log.info("SendEmail_SES:Attempting to send an email through the Amazon SES SMTP interface.");

    		// Connect to Amazon SES using the SMTP username and password you specified above.
    		transport.connect(EMAIL_HOST, EMAIL_HOST_USER, EMAIL_HOST_PASSWORD);

    		// Send the email.
    		transport.sendMessage(msg, msg.getAllRecipients());
    		//System.out.println("Email sent!");
    		return true;
    	}
    	catch (AddressException ex)
    	{
    		
    		log.error("SendEmail_SES: AddressException : " + ex.getMessage()+ " :: Error : "+ex.getLocalizedMessage());
    	}
    	catch (MessagingException ex)
    	{
    		log.error("SendEmail_SES: MessagingException :" + ex.getMessage() + " :: Error : "+ex.getLocalizedMessage()); 
    	}
    	catch (Exception ex) {
    		log.error("SendEmail_SES:Excep : " + ex.getMessage() + " :: Error : "+ex.getLocalizedMessage());
    	}

    	finally
    	{
    		try {
    			if(null != transport)
    				transport.close();
    		}
    		catch(Exception e)
    		{
    			log.error("SendEmail_SES:Exception: " + e.getMessage());
    		}
    	}
    	return false;
    }
    
}
