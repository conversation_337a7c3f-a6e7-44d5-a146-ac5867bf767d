package com.nimble.irisservices.service.impl;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.dao.IWaggleShopDao;
import com.nimble.irisservices.entity.ShopBanner;
import com.nimble.irisservices.entity.ShopFeature;
import com.nimble.irisservices.service.IWaggleShopService;

@Service
@Transactional
public class WaggleShopServiceImpl implements IWaggleShopService {

	@Autowired
	IWaggleShopDao waggleShopDao;
	
	@Override
	public List<ShopFeature> getShopFeature() {
		return waggleShopDao.getShopFeature();
	}

	@Override
	public List<ShopBanner> getShopBanner() {
		return waggleShopDao.getShopBanner();
	}

}
