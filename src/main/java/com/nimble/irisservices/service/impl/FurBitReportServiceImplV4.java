package com.nimble.irisservices.service.impl;

import javax.transaction.Transactional;

import org.springframework.stereotype.Service;

import com.nimble.irisservices.service.IFurBitReportServiceV4;

@Service
@Transactional
public class FurBitReportServiceImplV4 implements IFurBitReportServiceV4{
/*
	@Autowired
	IFurBitReportDaoV4 iFurBitReportDaoV4;

	
	@Override
	public JFurBitReportReportSummary getFurBitDailyReport(String date, long id, String string, String hour,
			String timezone, String name, Gateway gateway) {
		// TODO Auto-generated method stub
		return iFurBitReportDaoV4.getFurBitDailyReport( date,  id,  string,  hour,timezone,  name,  gateway);
	}
	
	@Override
	public JFurBitReportReportSummary getFurBitReport(String date, long id, String gatewayId, String timezone,
			String days, String reportDays, String gatewayName, Gateway gateway) {
		// TODO Auto-generated method stub
		return iFurBitReportDaoV4.getFurBitReport(date, id, gatewayId, timezone, days, reportDays, gatewayName,gateway);
		
	}
*/	
}
