package com.nimble.irisservices.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IPushNotificationDao;
import com.nimble.irisservices.dto.JPushNotifications;
import com.nimble.irisservices.entity.NotificationType;
import com.nimble.irisservices.entity.PushNotificationStatus;
import com.nimble.irisservices.entity.PushNotifications;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.WagglePushNotification;
import com.nimble.irisservices.service.IPushNotificationService;


@Service
public class PushNotificationServiceImpl  implements IPushNotificationService{

		
	@Autowired
	IPushNotificationDao notificationDao;
		
	

	@Override
	@Transactional
	public boolean savePushNotificaitons(JPushNotifications pushNotification) throws DataIntegrityViolationException {
		// TODO Auto-generated method stub
		return notificationDao.savePushNotificaitons(pushNotification);
	}

	@Override
	@Transactional
	public boolean updatePushNotifications(PushNotifications pushNotification) throws DataIntegrityViolationException {
		// TODO Auto-generated method stub
		return notificationDao.updatePushNotifications(pushNotification);
	}


	@Override
	@Transactional
	public boolean deleteNotification(long notificationId) {
		// TODO Auto-generated method stub
		return notificationDao.deleteNotification(notificationId);
	}

	@Override
	@Transactional
	public PushNotifications getNotification(String id) {
		// TODO Auto-generated method stub
		return notificationDao.getNotification(id);
	}

	@Override
	@Transactional
	public PushNotifications getPushNotificationById(String id) {
		// TODO Auto-generated method stub
		return notificationDao.getPushNotificationById(id);
	}
	
	@Override
	@Transactional
	public List<PushNotifications> getNotifications(String status) {
		// TODO Auto-generated method stub
		return notificationDao.getNotifications(status);
	}

	@Override
	@Transactional
	public List<PushNotifications> userNotifications(String userId, String status) {
		// TODO Auto-generated method stub
		return notificationDao.userNotifications(userId, status);
	}

	@Override
	@Transactional
	public boolean updatePushNotificationStatus(PushNotificationStatus pushNotificationStatus) {
		// TODO Auto-generated method stub
		return notificationDao.updatePushNotificationStatus(pushNotificationStatus);
	}

	@Override
	@Transactional
	public NotificationType getNotificationType(long id) {
		// TODO Auto-generated method stub
		return notificationDao.getNotificationType(id);
	}

	@Override
	@Transactional
	public Set<User> getUsersNotifications(String notificationId) {
		// TODO Auto-generated method stub
		return notificationDao.getUsersNotifications(notificationId);
	}

	@Override
	@Transactional
	public boolean deleteUserNotification(String userId, String notificationId) {
		// TODO Auto-generated method stub
		return notificationDao.deleteUserNotification(userId, notificationId);
	}

	@Override
	@Transactional
	public PushNotificationStatus getUserNotificationStatus(String userid, String notificationID) {
		// TODO Auto-generated method stub
		return notificationDao.getUserNotificationStatus(userid, notificationID);
	}
	
	@Transactional
	public boolean updatePushNotificationsForUsers(PushNotifications pushNotification, Set<User> users) throws DataIntegrityViolationException{
		return notificationDao.updatePushNotificationsForUsers(pushNotification, users);
	}
	
	@Transactional
	public boolean updateUserPushNotifications(PushNotifications pushNotification, Set<Long> users) throws DataIntegrityViolationException {
		return notificationDao.updateUserPushNotifications(pushNotification, users);
	}
	
	@Override
	@Transactional
	public boolean updateWagglePushNotification(ArrayList<WagglePushNotification> PushNotificationList) {
		return notificationDao.updateWagglePushNotification(PushNotificationList);
	}

	@Override
	@Transactional
	public boolean deletePushNotificationStatus(String pushNotificationStatusId) {
		return notificationDao.deletePushNotificationStatus(pushNotificationStatusId);
	}


}
