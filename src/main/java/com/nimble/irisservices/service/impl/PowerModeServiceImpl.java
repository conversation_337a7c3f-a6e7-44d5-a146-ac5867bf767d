package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IPowerModeDao;
import com.nimble.irisservices.dto.JGatewayModeDetails;
import com.nimble.irisservices.dto.JPowerModeConfig;
import com.nimble.irisservices.entity.GatewayPowerMode;
import com.nimble.irisservices.service.IPowerModeService;


@Service
@Transactional
public class PowerModeServiceImpl implements IPowerModeService {

	@Autowired
	IPowerModeDao powerModeDao;
	
	public JGatewayModeDetails getGatewayModeDetails(long gatewayId) {
		return powerModeDao.getGatewayModeDetails(gatewayId);
	}
	
	public List<JPowerModeConfig> getPowerModes(String powerModes) {
		return powerModeDao.getPowerModes(powerModes);
	}

	@Override
	public boolean updatePowerModeStatus(long gatewayId, int currMode, int prevMode, String modeStatus) {
		return powerModeDao.updatePowerModeStatus(gatewayId, currMode, prevMode, modeStatus);
	}
	
	@Override
	public List<GatewayPowerMode> getPowerModesForGateways(long userId, long gatewayIds, String os){
		return powerModeDao.getPowerModesForGateways(userId, gatewayIds, os);
	}
}
