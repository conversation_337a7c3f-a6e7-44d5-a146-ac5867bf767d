package com.nimble.irisservices.service.impl;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IReportDaoV4;
import com.nimble.irisservices.dto.JAssetLastReportV4;
import com.nimble.irisservices.dto.JAssetLastReportWatch;
import com.nimble.irisservices.dto.JGatewayGeneralInfo;
import com.nimble.irisservices.dto.JGatewayGeneralInfoV5;
import com.nimble.irisservices.dto.JGatewayMonthList;
import com.nimble.irisservices.dto.JGatewaySubSetup;
import com.nimble.irisservices.dto.JGatewayTimeList;
import com.nimble.irisservices.dto.JGatewayWeekList;
import com.nimble.irisservices.dto.JPetmonitorHistory;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.LastGatewayReport;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.dto.sensorReportData;
import com.nimble.irisservices.entity.DashboardBanner;
import com.nimble.irisservices.entity.MonitorType;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.service.IReportServiceV4;

@Service
@Transactional
public class ReportServiceImplV4 implements IReportServiceV4{

	@Autowired
	IReportDaoV4 reportDaoV4;	
	
	@Override
	public List<JAssetLastReportV4> getLastgatewayreportV4(String groupid,String subgroupid, String assetgroupid,
		String assetid,long userid, String offset,String limit,String tempunit, String country) {
		return reportDaoV4.getLastGatewayReportV4(groupid,subgroupid,assetgroupid,assetid,userid,offset,limit,tempunit,country);
	}
	
	@Override
	public List<JAssetLastReportWatch> getLastgatewayreportWatch(long userid,String tempunit,List<JGatewaySubSetup> gateway_setup) {
		return reportDaoV4.getLastGatewayReportWatch(userid,tempunit,gateway_setup);
	}
	
	@Override
	public List<sensorReportData> getSensorRep(long gatewayid, String Date, String timeZone) {
		return reportDaoV4.getSensorReports(gatewayid, Date, timeZone);
	}
	
	@Override
	public List<sensorReportData> getSensoGrpRep(long gatewayid, String startDate, String endDate, String Zone) {
		return reportDaoV4.getSensorGroupReports(gatewayid, startDate, endDate, Zone);
	}

	@Override
	public int getGatewayReportCount( long gatewayid) {
		return reportDaoV4.getGatewayReportCount(gatewayid);
	}
	
	@Override
	public String getLastGatewayReporttime( long gatewayid) {
		return reportDaoV4.getLastGatewayReporttime(gatewayid);
	}

	@Override
	public List<JGatewaySubSetup> getSubscriptionFromDB(UserV4 user) {
		return reportDaoV4.getSubscriptionFromDB(user);
	}
	
	@Override
	public List<LastGatewayReport> getjGatewayReport(long userid,String tempunit,List<JGatewaySubSetup> gateway_setup) {
		return reportDaoV4.getjGatewayReport(userid,tempunit,gateway_setup);
	}
	
	@Override
	public JGatewayGeneralInfo getGatewayGeneralInfo(long userid,String date,String timeZone) {
		return reportDaoV4.getGatewayGeneralInfo(userid,date,timeZone);
	}

	@Override
	public PetProfile getPetProfileByUserId(long userId) {
		return reportDaoV4.getGatewayGeneralInfo(userId);
	}

	@Override
	public List<JGatewayWeekList> getGatewayGenerallist(long gatewayId,String date, String timeZone, boolean is_freePlan) {
		return reportDaoV4.getGatewayGenerallist(gatewayId, date, timeZone, is_freePlan);
	}
	
	@Override
	public List<JGatewayTimeList> getGatewayGeneralTimelist(long gatewayId,String date,String timeZone) {
		return reportDaoV4.getGatewayGeneralTimelist(gatewayId,date,timeZone);
	}
	
	@Override
	public List<MonitorType> getAllmonitorType() {
		return reportDaoV4.getAllmonitorType();
	}
	
	@Override
	public Map<String,List<Object>> getLastgatewayreportV5(long userid, 
			String tempunit, String country, String category,String os) {
		return reportDaoV4.getLastGatewayReportV5(userid,tempunit,country, category,os);
	}
	
	@Override
	public List<JGatewayMonthList> getGatewayMonthlist(long gatewayId,String date, String timeZone, boolean is_freePlan) {
		return reportDaoV4.getGatewayMonthlist(gatewayId, date, timeZone, is_freePlan);
	}
	
	@Override
	public JGatewayGeneralInfoV5 getGatewayGeneralInfoV5(long userid,String date,String timeZone) {
		return reportDaoV4.getGatewayGeneralInfoV5(userid,date,timeZone);
	}
	
	@Override
	public Map<String,List<Object>> getLastgatewayreportV6(long userid, 
			String tempunit, String country, String category,String os, String req_ver) {
		return reportDaoV4.getLastGatewayReportV6(userid,tempunit,country, category,os, req_ver);
	}

	@Override
	public List<DashboardBanner> getMarkettingBannerreport(String os, String app_ver, boolean checkPaidPlan) {
		return reportDaoV4.getMarkettingBannerreport(os, app_ver, checkPaidPlan);
	}
	
	@Override
	public Map<String,Object> getLastGatewayReportV6(long gatewayid, long userid, long monitor_id, 
			String country, String os, String req_ver, JResponse response) {
		return reportDaoV4.getLastGatewayReportV6(gatewayid, userid, monitor_id, 
				country, os, req_ver, response);
	}
	
	@Override
	public Map<String,List<Object>> getLastgatewayreportV7(long userid, 
			String tempunit, String country, String category,String os, String req_ver) {
		return reportDaoV4.getLastGatewayReportV7(userid,tempunit,country, category,os, req_ver);
	}

	@Override
	public HashMap<String, String> getNSInfo(long gatewayid) {
		return reportDaoV4.getNSInfo(gatewayid);
	}
	
	public List<JPetmonitorHistory> getPetmonitorGrpRep(long gatewayid, String from, String todate, String timeZone, String tempunit) {
		return reportDaoV4.getPetmonitorGrpRep(gatewayid,from,todate,timeZone,tempunit);
	}

	@Override
	public Map<String, Object> getLastgatewayreportV8(long userid, 
			String tempunit, String country, String category,String os, String req_ver) {
		return reportDaoV4.getLastGatewayReportV8(userid,tempunit,country, category,os, req_ver);
	}
	
	public JResponse getLastGatewayReportV8(long gatewayid, long userid, long monitor_id, String country,
			String os, String req_ver, JResponse response) {
		return reportDaoV4.getLastGatewayReportV8(gatewayid, userid, monitor_id, 
				country, os, req_ver, response);
	}

	@Override
	public List<JGatewaySubSetup> getProductSubscriptions(UserV4 user) {
		return reportDaoV4.getProductSubscriptions(user);
	}

	@Override
	public boolean isSubscribedProduct(String chargebeeId){
		return reportDaoV4.isSubscribedProductFromDB(chargebeeId);
	}

	@Override
	public int compareVersions(String appVerFromConfig, String appVerFromRequest) {
		String[] arr1 = appVerFromConfig.split("\\.");
		String[] arr2 = appVerFromRequest.split("\\.");

		int len = Math.max(arr1.length, arr2.length);
		for (int i = 0; i < len; i++) {
			int part1 = i < arr1.length ? Integer.parseInt(arr1[i]) : 0;
			int part2 = i < arr2.length ? Integer.parseInt(arr2[i]) : 0;
			if (part1 != part2) {
				return Integer.compare(part1, part2);
			}
		}
		return 0;

	}
}
