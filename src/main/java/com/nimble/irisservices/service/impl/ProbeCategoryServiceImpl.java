package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IProbeCategoryDao;
import com.nimble.irisservices.entity.BatteryLookup;
import com.nimble.irisservices.entity.ProbeCategory;
import com.nimble.irisservices.entity.ProbeLookup;
import com.nimble.irisservices.service.IProbeCategoryService;

@Service
@Transactional
public class ProbeCategoryServiceImpl implements IProbeCategoryService {

	@Autowired
	IProbeCategoryDao probeDao;

	public boolean createProbeCategory(ProbeCategory probe) {
		return probeDao.createProbeCategory(probe);
	}

	@Override
	public List<ProbeCategory> getAllProbeCategory() {
		return probeDao.getAllProbeCategory();
	}

	@Override
	public ProbeCategory getProbeCategoryById(String probecategory) {
		return probeDao.getProbeCategoryById(probecategory);
	}

	@Override
	public boolean createProbeLookup(ProbeLookup probe) {
		return probeDao.createProbeLookup(probe);
	}

	@Override
	public List<ProbeLookup> getAllProbeLookup() {
		return probeDao.getAllProbeLookup();
	}

	@Override
	public boolean createBatteryLookup(BatteryLookup probe) {
		return probeDao.createBatteryLookup(probe);
	}

	@Override
	public List<BatteryLookup> getAllBatteryLookup() {
		return probeDao.getAllBatteryLookup();
	}

}
