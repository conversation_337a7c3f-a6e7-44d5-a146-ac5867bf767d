package com.nimble.irisservices.service.impl;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dao.ITrendingvideoDaoV4;
import com.nimble.irisservices.dao.IUserDaoV4;
import com.nimble.irisservices.dto.JTrendingVideo;
import com.nimble.irisservices.service.ITrendingvideoServiceV4;

@Service
@Transactional
public class TrendingvideoServiceImplV4 implements ITrendingvideoServiceV4{


	@Autowired
	IUserDaoV4 userDao;
	

	@Autowired
	ITrendingvideoDaoV4 ITrendingvidDaoV4;

	@Override
	public boolean CreateVideoInfoTransaction(long userid ,long videoid, int like, int viewcount) {

		String Createqry = "INSERT INTO trending_video_transaction (user_id,video_id,Enabled,likes,dislike,viewcount,lastseen) VALUES ";

		int likes= 0;
		int dislikes= 0;
		int viewCountvalue =0;
		String lastseen="'1753-01-01 00:00:00'";
		if(like==1)
		{
			likes=1;
			dislikes =0;
		}
		else if(like == -1)
		{
			likes=0;
			dislikes =1;
		}
		else if(like == -2)
		{
			dislikes =0;
		}
		else if(like == 2)
		{	
			likes=0;
		}
		if(viewcount==1)
		{
			viewCountvalue = 1;
			String Temp_lastseen =  IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC())+"" ;
			lastseen = "'"+Temp_lastseen.split("\\.")[0]+"'";
		}

		Createqry +=" ("+userid+","+videoid+","+1+","+likes+","+dislikes+","+viewCountvalue+","+lastseen+")  ;";
		if (userDao.executeQuery(Createqry) == 0)
			return false;

		return true;

	}



	@Override
	public boolean UpdateVideoInfoTransaction(long userid ,long videoid, int like, int viewcount) {

		String Updateqry = "UPDATE trending_video_transaction SET Enabled = '1' ";

		String lastseen="'1753-01-01 00:00:00'";
		if(like==1)
		{
			Updateqry +=", likes = '1' , dislike = '0' " ;
		}
		else if(like == -1)
		{
			Updateqry +=", likes = '0' , dislike = '1' " ;
		}
		else if(like == -2)
		{
			Updateqry +=", likes = '0' ";
		}
		else if(like == 2)
		{	
			Updateqry +=", dislike = '0' ";
		}
		if(viewcount==1)
		{
			String Temp_lastseen =  IrisservicesUtil.getDateTime_TS(IrisservicesUtil.getCurrentTimeUTC())+"" ;
			lastseen = "'"+Temp_lastseen.split("\\.")[0]+"'";
			Updateqry +=", lastseen = "+ lastseen +" , viewcount = viewcount+1 ";	
		}

		Updateqry +=" WHERE user_id= '"+userid+"' AND video_id= '"+videoid+"' ";

		if ( userDao.executeQuery(Updateqry) == 0)
			return false;

		return true;

	}


	@Override
	public List<JTrendingVideo> getTrendingvideoInfoV4(List<JTrendingVideo> TVUserInfo,long user_id) throws Exception {
		return ITrendingvidDaoV4.getTrendingvideosUserInfoV4(TVUserInfo,user_id);
	}
	
}
