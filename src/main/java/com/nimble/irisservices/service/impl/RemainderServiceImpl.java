package com.nimble.irisservices.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.dao.IReminderDao;
import com.nimble.irisservices.dto.JReminderDetails;
import com.nimble.irisservices.entity.ReminderRepeat;
import com.nimble.irisservices.service.IReminderService;

@Service
@Transactional
public class RemainderServiceImpl implements IReminderService {

	@Autowired
	IReminderDao reminderdao;

	@Override
	public boolean saveReminderDetails(long userid, String timezone, String remainderName, String displayname,
			String remainderDate, long repeatedId, String repeatedType, String remainderMsg, String jobstate,
			int enable) {
		return reminderdao.saveReminderDetails(userid, timezone, remainderName, displayname, remainderDate, repeatedId,
				repeatedType, remainderMsg, jobstate, enable);
	}

	@Override
	public boolean updateReminderDetails(long reminderId, long userid, String timezone, String remainderName,
			String displayname, String remainderDate, long repeatedId, String repeatedType, String remainderMsg,
			String jobstate, int enable) {
		return reminderdao.updateReminderDetails(reminderId, userid, timezone, remainderName, displayname,
				remainderDate, repeatedId, repeatedType, remainderMsg, jobstate, enable);
	}

	@Override
	public boolean disableReminderDetails(String id, String remainderName, String state, int enable) {
		// TODO Auto-generated method stub
		return reminderdao.disableReminderDetails(id, remainderName, state, enable);
	}

	@Override
	public String saveReminderViewDetails(long userid, String remainderName, Date triggerDate, int status, int ontime) {
		// TODO Auto-generated method stub
		return reminderdao.saveReminderViewDetails(userid, remainderName, triggerDate, status, ontime);
	}

	@Override
	public boolean updateReminderViewDetails(long viewid, boolean isdeleted) {
		// TODO Auto-generated method stub
		return reminderdao.updateReminderViewDetails(viewid, isdeleted);
	}

	@Override
	public Map<String, List<JReminderDetails>> getremainderOverDuelist(long id, int limitDays, int viewcomplete) {
		// TODO Auto-generated method stub
		return reminderdao.getreminderOverDuelist(id, limitDays, viewcomplete);
	}

	@Override
	public ArrayList<JReminderDetails> getReminderDetails(long userid, long remainderid, String remainderName,
			String timeZone) {
		return reminderdao.getReminderDetails(userid, remainderid, remainderName, timeZone);
	}

	@Override
	public String getRemainderViewDetails(long userid, long remainderid, Date triggerDate) {
		return reminderdao.getReminderViewDetails(userid, remainderid, triggerDate);
	}

	@Override
	public ArrayList<ReminderRepeat> getReminderRepeatTypeList(long repeatId) {
		return reminderdao.getReminderRepeatTypeList(repeatId);
	}

	@Override
	public int getremainderOverDueCount(long userId) {
		return reminderdao.getremainderOverDueCount(userId);
	}

	

}
