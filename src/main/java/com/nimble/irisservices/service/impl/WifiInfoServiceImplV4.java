package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IWifiInfoDaoV4;
import com.nimble.irisservices.dto.BluetoothDeviceList;
import com.nimble.irisservices.entity.WifiInfo;
import com.nimble.irisservices.service.IWifiInfoServiceV4;

@Service
@Transactional
public class WifiInfoServiceImplV4 implements IWifiInfoServiceV4{
	

	@Autowired
	@Lazy
	IWifiInfoDaoV4 wifiDaoV4;

	@Override
	public WifiInfo isAlreadycontain(long gatewayid) {
		return wifiDaoV4.isAlreadycontain(gatewayid);
	}
	
	@Override
	public List<BluetoothDeviceList> getWifiList(long gatewayID, long userID, String os) {
		return wifiDaoV4.getWifiList(gatewayID, userID, os);
	}
	
	@Override
	public List<Object> getSensorWifiList(long gatewayID) {
		return wifiDaoV4.getSensorWifiList(gatewayID);
	}

	@Override
	public boolean isWifiConPaired(long gatewayID) {
		return wifiDaoV4.isWifiConPaired(gatewayID);
	}
	
}
