package com.nimble.irisservices.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IVerizonDao;
import com.nimble.irisservices.entity.SimReactivationHistory;
import com.nimble.irisservices.service.IVerizonService;


@Service
public class VerionServiceImpl implements IVerizonService{
	
	
	@Autowired
	IVerizonDao verizonDao;
	
	@Transactional
	public boolean saveVerizonStatus(SimReactivationHistory simReactObj) {		
	
		return verizonDao.saveVerizonStatus(simReactObj);
	}

	@Transactional
	public boolean updateVerizonStatus(SimReactivationHistory simReactObj) {
		// TODO Auto-generated method stub
		return verizonDao.updateVerizonStatus(simReactObj);
	}

	@Transactional
	public SimReactivationHistory getSimReactivationStatus(String key,String value) {
		return verizonDao.getSimReactivationStatus(key,value);
	}

	@Transactional
	public SimReactivationHistory getSimReactivationStatusByMeid(String meid, String reqId) {
		return verizonDao.getSimReactivationStatusByMeid(meid,reqId);
	}

	@Override
	public boolean updateRequestId(String meid, String oldReqId, String reqId,String updateDate) {
		return verizonDao.updateRequestId(meid,oldReqId,reqId,updateDate);
	}

	@Override
	public boolean updateRetryScheduledDate(String imei, String reqId) {
		return verizonDao.updateRetryScheduledDate(imei,reqId);
	}

}
