package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IDynamicCmdDao;
import com.nimble.irisservices.dto.JSentMessage;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.service.IDynamicCmdService;
@Service
public class DynamicCmdServiceImpl implements IDynamicCmdService{

	@Autowired
	IDynamicCmdDao dynamicCmdDao; 
	
	@Transactional
	public boolean saveDynamicCmd(Gateway gateway, String msg,
			int transportType, String status) {
		// TODO Auto-generated method stub
		return dynamicCmdDao.saveDynamicCmd(gateway, msg, transportType, status);
	}

	@Transactional
	public List<JSentMessage> sentMessages(String groupid, String subgroupid,
			String gatewayid, long cmpid) {
		// TODO Auto-generated method stub
		return dynamicCmdDao.sentMessages(groupid, subgroupid, gatewayid, cmpid);
	}

	@Transactional
	public boolean savePlivoData(String phoneno, String msg, String cmpid,
			String cmpname, String appname, String type,int transport_type) {
		// TODO Auto-generated method stub
		return dynamicCmdDao.savePlivoData(phoneno, msg, cmpid, cmpname, appname, type,transport_type);
	}

	@Transactional
	public boolean delDynamicCmd(long userid, String assetid) {
		return dynamicCmdDao.delDynamicCmd(userid, assetid);
	}
	
	@Transactional
	public boolean saveDynamicCmdV2(Gateway gateway, String msg,
			int transportType, String status,long seqno) {
		return dynamicCmdDao.saveDynamicCmdV2(gateway, msg, transportType, status,seqno);
	}
	
	@Transactional
	public boolean insertDynamicCmd(Gateway gateway, String msg,int transportType,String status) {
		return dynamicCmdDao.insertDynamicCmd(gateway, msg, transportType, status);
	}
	
	@Transactional
	public boolean checkVaildUpgradePacket(long gatewayId,String upgVer) {
		return dynamicCmdDao.checkVaildUpgradePacket(gatewayId,  upgVer);
	}

	@Transactional
	public boolean saveDynamicCmdCalib(Gateway gateway, String msg,
			int transportType, String status,long seqno) {
		return dynamicCmdDao.saveDynamicCmdCalib(gateway, msg, transportType, status,seqno);
	}

	@Transactional
	public boolean isDynamicCmdInserted(Long gatewayId, String fotacommand){
		return dynamicCmdDao.isDynamicCmdInserted(gatewayId,fotacommand);
	}
}
