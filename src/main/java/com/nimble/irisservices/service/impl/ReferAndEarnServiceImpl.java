package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IReferAndEarnDao;
import com.nimble.irisservices.entity.CreditType;
import com.nimble.irisservices.entity.ReferralCredits;
import com.nimble.irisservices.entity.ReferralDetail;
import com.nimble.irisservices.service.IReferAndEarnService;

@Service
public class ReferAndEarnServiceImpl implements IReferAndEarnService {
	@Autowired
	IReferAndEarnDao refDao;

	@Transactional
	@Override
	public boolean deleteReferralCredits(long id) throws Exception {
		return refDao.deleteReferralCredits(id);
	}

	@Transactional
	@Override
	public List<ReferralCredits> listReferralCredits() {
		return refDao.listReferralCredits();
	}

	@Transactional
	@Override
	public boolean saveOrUpdateReferralCredits(ReferralCredits refCredit) throws Exception {
		return refDao.saveOrUpdateReferralCredits(refCredit);
	}
	
	@Transactional
	@Override
	public boolean saveOrUpdateReferralDetail(ReferralDetail ref) throws Exception {
		return refDao.saveOrUpdateReferralDetail(ref);
	}

	@Transactional
	@Override
	public CreditType getCreditTypeById(long id) {
		return refDao.getCreditTypeById(id);
	}

	@Transactional
	@Override
	public List<CreditType> listCreditType() {
		return refDao.listCreditType();
	}
	
	@Transactional
	@Override
	public ReferralCredits getLatestReferralCredits() {
		return refDao.getLatestReferralCredits();
	}

}
