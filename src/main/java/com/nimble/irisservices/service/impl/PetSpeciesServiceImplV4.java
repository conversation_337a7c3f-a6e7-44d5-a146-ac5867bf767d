package com.nimble.irisservices.service.impl;

import java.sql.SQLIntegrityConstraintViolationException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.TimeZone;

import javax.transaction.Transactional;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.dao.IPetSpeciesDaoV4;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.PetBreeds;
import com.nimble.irisservices.entity.PetFeedDetails;
import com.nimble.irisservices.entity.PetFood;
import com.nimble.irisservices.entity.PetFoodPerDay;
import com.nimble.irisservices.entity.PetSpecies;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.job.PetFeedRemainder;
import com.nimble.irisservices.quartz.JobService;
import com.nimble.irisservices.service.IPetSpeciesServicesV4;

@Service
@Transactional
public class PetSpeciesServiceImplV4 implements IPetSpeciesServicesV4 {

	private static final Logger log = LogManager.getLogger(PetSpeciesServiceImplV4.class);
	
	@Autowired
	IPetSpeciesDaoV4 petSpeciesDaoV4;

	@Autowired
	@Lazy
	JobService jobService;
	
	@Autowired
	Helper _helper;
	
	@Value("${sheduled_feed_remainder_time}")
	private int sheduled_feed_remainder_time;

	
	@Override
	public List<PetBreeds> getPetBreedsV4(String speciename) {
		return petSpeciesDaoV4.getPetBreedsV4(speciename);
	}
	

	@Override
	public List<PetSpecies> getPetSpecies() {
		return petSpeciesDaoV4.getPetSpecies();
	}


	@Override
	public List<PetFood> getPetFood() {
		return petSpeciesDaoV4.getPetFood();
	}

	@Override
	public List<PetFood> getPetFoodByUserToo(long user_id,boolean isPaid,long foodId) {
		return petSpeciesDaoV4.getPetFoodByUserToo(user_id,isPaid,foodId);
	}

	@Override
	public List<PetFoodPerDay> getPetFoodPerDay() {
		return petSpeciesDaoV4.getPetFoodPerDay();
	}


	@Override
	public PetFood saveOrUpdatePetFood(PetFood petFood) throws SQLIntegrityConstraintViolationException {
		return petSpeciesDaoV4.saveOrUpdatePetFood(petFood);
	}


	@Override
	public PetFeedDetails saveOrUpdatePetFeedDetails(PetFeedDetails petFeedDetails) {
		return petSpeciesDaoV4.saveOrUpdatePetFeedDetails(petFeedDetails);
	}


	@Override
	public PetFeedDetails getPetFeedDetails(long gateway_id, long user_id) {
		return petSpeciesDaoV4.getPetFeedDetails(gateway_id, user_id);
	}


	@Override
	public boolean updatePetFeedRemainder(PetFeedDetails petFeedDetails, UserV4 user) {
		log.info("Entered into updatePetFeedRemainder :: set_remainder : "+ petFeedDetails.isRemainder());
		try {
			
			if( petFeedDetails.isRemainder() ) {
				
				String[] dateStrArr = petFeedDetails.getMeal_times().split(",");
				List<Date> dateList = new ArrayList<>();
				
				Date now = new Date();
				Calendar cal = Calendar.getInstance();
				cal.setTimeZone(TimeZone.getTimeZone("UTC"));
				
				Arrays.asList(dateStrArr).forEach( date-> {
					Date convertedDate = _helper.timeZoneConverter( "HH:mm", petFeedDetails.getTimezone(), "+00:00", date); 
					cal.setTime( new Date() );
					cal.set(Calendar.HOUR_OF_DAY, convertedDate.getHours());
					cal.set(Calendar.MINUTE, convertedDate.getMinutes());
					Date conDate = new Date( (cal.getTime().getTime() - (1000*60*sheduled_feed_remainder_time)) );
					dateList.add( conDate );
				});
				
				String jobName = petFeedDetails.getUser_id()+"_"+petFeedDetails.getGateway_id();
				String groupName = "PET_FEED_REMAINDER";
				
				HashMap<String, Object> userDetails = new HashMap<>();
				userDetails.put("user_id", user.getId());
				userDetails.put("email", user.getEmail());
				userDetails.put("auth", user.getAuthKey());

				boolean jobCreatedStatus = jobService.scheduleRepeatJobWithMultipleTriggers(jobName, PetFeedRemainder.class, dateList, groupName, petFeedDetails.getUser_id()+"", groupName, 0, userDetails);				
				log.info("pet_feed_remainder job created status : "+ jobCreatedStatus);
				
			} else {
				log.info("Not a remainder");
			}
			
		} catch (Exception e) {
			log.info("Error in updatePetFeedRemainder :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}

	
	@Override
	public double getSignalment(double speciesId, boolean isIntact) {
		double signalment = 1;
		
		if(speciesId==1 && isIntact)
			signalment = 1.8;
		else if(speciesId==1 && !isIntact)
			signalment = 1.6;
		else if(speciesId==2 && isIntact)
			signalment = 1.4;
		else if(speciesId==2 && !isIntact)
			signalment = 1.2;
				
		return signalment;
	}


	@Override
	public double getBCS(String bodyCondition) {
		double BCS = 1;
		if(bodyCondition.equalsIgnoreCase("thin")|| bodyCondition.equalsIgnoreCase("verythin"))	
			BCS = 1.2;
		else if(bodyCondition.equalsIgnoreCase("Over weight")|| bodyCondition.equalsIgnoreCase("obese"))	
			BCS = 0.8;
						
		return BCS;
	}


	@Override
	public PetFood getPetFoodById(long id) {
		return petSpeciesDaoV4.getPetFoodById(id);
	}

	@Override
	public void updateReqCalories(long gatewayid, double req_calories) {
		petSpeciesDaoV4.updateReqCalories(gatewayid, req_calories);
	}
	
	@Override
	public PetFood getPetFoodByName(String name, long userId) {
		return petSpeciesDaoV4.getPetFoodByName(name,userId);
	}
	
	@Override
	public void updateReqCaloriesById(long petProfileid, double req_calories) {
		petSpeciesDaoV4.updateReqCaloriesById(petProfileid, req_calories);
	}
	
	@Override
	public void updateReqWeightById(long profileid, double req_weight) {
		petSpeciesDaoV4.updateReqWeightById(profileid, req_weight);
	}
	
	@Override
	public void updateReqWeightByProfId(long petProfileid, double req_calories) {
		petSpeciesDaoV4.updateReqWeightByProfId(petProfileid, req_calories);
	}
	
	@Override
	public double getActivityLevel(String activityLevel) {
		double actLvl = 1;
		if(activityLevel.equalsIgnoreCase("Inactive"))	
			actLvl = 1;
		else if( activityLevel.equalsIgnoreCase("Somewhat Active"))
			actLvl = 1.2;
		else if(activityLevel.equalsIgnoreCase("Active"))	
			actLvl = 1.4;
		else if(activityLevel.equalsIgnoreCase("Very Active"))	
			actLvl = 1.6;
						
		return actLvl;
	}
	
	@Override
	public String getFindActivitylevel(long actKey) {
		String actVal = "Inactive";
		if(actKey == 1)	
			actVal = "Inactive";
		else if(actKey == 2)	
			actVal = "Somewhat Active";
		else if(actKey == 3)	
			actVal = "Active";
		else if(actKey == 4)	
			actVal = "Very Active";
						
		return actVal;
	}
	
	@Override
	public long getFindActivitylevelVal(String actVal) {
		long actKey = 1;
		if(actVal.equalsIgnoreCase("Inactive"))	
			actKey = 1;
		else if(actVal.equalsIgnoreCase("Somewhat Active"))	
			actKey = 2;
		else if(actVal.equalsIgnoreCase("Active"))	
			actKey = 3;
		else if(actVal.equalsIgnoreCase("Very Active"))	
			actKey = 4;
						
		return actKey;
	}
	
	@Override
	public String getTimezoneconvertDate(String date,String timeZone) {
		
		Date parsedDate;
		try {
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
			dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"+timeZone));
		
			SimpleDateFormat inputFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        SimpleDateFormat outputFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        
	        inputFormatter.setTimeZone(TimeZone.getTimeZone("GMT" + timeZone));
	        
	        
				parsedDate = inputFormatter.parse(date + " 00:00:00");
			
	        
	        outputFormatter.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
	        
	        String startDateEx = outputFormatter.format(parsedDate);
	        
	        return startDateEx;
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			log.info("Error in getTimezoneconvertDate :: Error : "+ e.getLocalizedMessage());
		}
		
		return "";
	}
	
	@Override
	public boolean findDevicepaidplanornot(long gateway_id) {
		return petSpeciesDaoV4.findDevicepaidplanornot(gateway_id);
	}
	
	@Override
	public long getPetFoodFromFeeddetails(long gateway_id, long user_id) {
		return petSpeciesDaoV4.getPetFoodFromFeeddetails(gateway_id,user_id);
	}
	
	public boolean findDeviceFreeplanornot(long gateway_id){
		return petSpeciesDaoV4.findDeviceFreeplanornot(gateway_id);
	}
}
