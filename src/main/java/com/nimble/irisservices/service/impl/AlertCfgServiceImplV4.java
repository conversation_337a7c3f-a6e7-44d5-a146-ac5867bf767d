package com.nimble.irisservices.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.dao.IAlertCfgDaoV4;
import com.nimble.irisservices.dao.ICompanyDao;
import com.nimble.irisservices.dto.AlertCfgV4;
import com.nimble.irisservices.dto.JAlertCfg;
import com.nimble.irisservices.dto.JAlertCfgV4;
import com.nimble.irisservices.dto.JAlertSensor;
import com.nimble.irisservices.dto.JAlertsWC;
import com.nimble.irisservices.dto.JGeofence;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.AlertCfg;
import com.nimble.irisservices.entity.AlertCfgWC;
import com.nimble.irisservices.entity.AlertWC;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidAlertTypeException;
import com.nimble.irisservices.exception.InvalidAsseIdException;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAlertCfgServiceV4;

@Service
@Transactional
public class AlertCfgServiceImplV4 implements IAlertCfgServiceV4{
	private static final Logger log = LogManager.getLogger(AlertCfgServiceImplV4.class);

	@Autowired
	@Lazy
	IAlertCfgService alertCfgService;
	
	@Autowired
	@Lazy
	IAlertCfgServiceV4 alertCfgServiceV4;
	
	@Autowired
	@Lazy
	IAlertCfgDaoV4 alertCfgDaoV4;

	@Autowired
	@Lazy
	ICompanyDao companyDao;
		
	@Override
	public List<AlertCfgV4> getAlertCfgV4(long user_id, String tempunit,long asset_id,boolean  geofence_enable) {
		return alertCfgDaoV4.getAlertCfgV4(user_id,tempunit,asset_id,geofence_enable);
	}

	@Override
	public AlertCfg getAlertCfg(long id) {
		return alertCfgDaoV4.getAlertCfg(id);
	}
	
	@Override
	public int enabledisablealertcfg(String alertcfgids, boolean enable) {
		return alertCfgDaoV4.enabledisablealertcfg(alertcfgids, enable);
	}
	
	@Override
	@Transactional
	public int updateEmailPhone(String alertcfgids, String phonenos,String emails) {
		return alertCfgDaoV4.updateEmailPhone(alertcfgids, phonenos, emails);
	}
	@Override
	public int updateAlertCfg(long alertcfgid, long alerttypeid, float minval , float maxval, long gatewayid, long cmp_id, String reqFrom) {
		return alertCfgDaoV4.updateAlertCfg(alertcfgid, alerttypeid, minval, maxval, gatewayid,cmp_id, reqFrom);
	}

	@Override
	public int updateNotify(String alertcfgids, String alerttype,int  notifyfreq) {
		return alertCfgDaoV4.updateNotify(alertcfgids, alerttype, notifyfreq);
	}
	
	@Override
	public int updateNotificationType(String alertcfgids, String notifytype) {
		return alertCfgDaoV4.updateNotificationType(alertcfgids, notifytype);
	}

	@Override
	public JGeofence getGeofenceDetails(long gateway_id, String reqVer) {
		return alertCfgDaoV4.getGeofenceDetails(gateway_id, reqVer);
	}
	
	@Override
	public JResponse updateGeofenceDetails(JAlertCfgV4 jalertCfg,boolean isParkSafe) {
		return alertCfgDaoV4.updateGeofenceDetails(jalertCfg,isParkSafe);
	}
	
	@Override
	public boolean updateGeofenceState(long alertcfgId,int fenceState) {
		return alertCfgDaoV4.updateGeofenceState(alertcfgId, fenceState);
	}

	public JResponse createTemperatureAlert(User user, Gateway gateway, String reqFrom) {
		log.info("create TemperatureAlert : ");

		JResponse response = new JResponse();
		long monitortype = gateway.getModel().getMonitor_type().getId();
		CompanyConfig companyConfig = companyDao.getCompanyConfig(user.giveCompany().getId());

		if (monitortype == 1) {
			String notify_freq = "60";

			String minval = "41";

			String maxval = "89.6";
			
			if (companyConfig.getTemperatureunit().equalsIgnoreCase("C")) {
				minval = String.valueOf(FahrenheitToCelsius(Float.valueOf(minval)));
				maxval = String.valueOf(FahrenheitToCelsius(Float.valueOf(maxval)));
			}

			String alertname = "TempAlert" + "_" + gateway.getId();

			String notify_type = "1100";// sms,email,voicemsg,appnotification


			boolean enable = false;

			try {
				int freq = Integer.parseInt(notify_freq) * 60;

				String rule = "(" + minval + ">CalcAdc2) or (" + maxval + "<CalcAdc2)";

				Long[] assetids = new Long[5];

				assetids[0] = gateway.getId();

				JAlertCfg jalertcfg = new JAlertCfg();

				jalertcfg.setName(alertname);
				jalertcfg.setMinval(Float.valueOf(minval));
				jalertcfg.setMaxval(Float.valueOf(maxval));
				jalertcfg.setNotifyfreq(freq);
				jalertcfg.setMobilenos(user.getMobileno());
				jalertcfg.setEmailids(user.getEmail());
				jalertcfg.setNotificationtype(notify_type);
				jalertcfg.setAssetids(assetids);
				jalertcfg.setAlerttypeid(1);
				jalertcfg.setSeverity(4);
				jalertcfg.setEnable(enable);
				jalertcfg.setRule(rule);
				jalertcfg.setCountry("NA");
				jalertcfg.setMin_notifyfreq(600);

				boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(), reqFrom);

				response.put("Status", 1);
				response.put("Msg", "Success");

			} catch (ConstraintViolationException e) {
				log.error("ConstraintViolationException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (InvalidAlertTypeException e) {
				log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid alerttype id");
				return response;
			} catch (InvalidAsseIdException e) {
				log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (Exception e) {
				log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "alert name cannot be empty and should be unique");
				return response;
			}
		} else {
			log.info("Invalid Monitor Type ID");
			response.put("Status", 0);
			response.put("Msg", "Invalid Monitor Type ID");
		}
		return response;
	}

	public float FahrenheitToCelsius(float tempmvalIndegreeFahrenheit) {

		double degreeCelsius = (Double.valueOf(tempmvalIndegreeFahrenheit).floatValue() - 32) * 5 / 9d;
		double roundvalues = Math.round(degreeCelsius * 100.0) / 100.0;
		return (float) (roundvalues);
	}

	// Create Battery Alert
	public JResponse createBatteryAlert(User user, Gateway gateway, String reqFrom) {
		log.info("create BatteryAlert : ");

		JResponse response = new JResponse();
		long monitortype = gateway.getModel().getMonitor_type().getId();
		if (monitortype == 1 || monitortype == 9) {
			String notify_freq = "60";
			String minval = "30";
			String maxval = "170";
			String alertname = "BatteryAlert" + "_" + gateway.getId();
			String notify_type = "1100";// sms,email,voicemsg,appnotification
			boolean enable = false;
			try {
				int freq = Integer.parseInt(notify_freq) * 60;
				String rule = "(battStatus<" + minval + " and battStatus>=0) or (battStatus==-2)";
				Long[] assetids = new Long[5];
				assetids[0] = gateway.getId();
				JAlertCfg jalertcfg = new JAlertCfg();
				jalertcfg.setName(alertname);
				jalertcfg.setMinval(Float.valueOf(minval));
				jalertcfg.setNotifyfreq(freq);
				jalertcfg.setMobilenos(user.getMobileno());
				jalertcfg.setEmailids(user.getEmail());
				jalertcfg.setNotificationtype(notify_type);
				jalertcfg.setAssetids(assetids);
				jalertcfg.setAlerttypeid(2);// Alert Type ID for Battery Alert
				jalertcfg.setSeverity(4);
				jalertcfg.setEnable(enable);
				jalertcfg.setRule(rule);
				jalertcfg.setCountry("NA");
				jalertcfg.setMin_notifyfreq(3600);

				boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(), reqFrom);
				response.put("Status", 1);
				response.put("Msg", "Success");
			} catch (ConstraintViolationException e) {
				log.error("ConstraintViolationException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (InvalidAlertTypeException e) {
				log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid alerttype id");
				return response;
			} catch (InvalidAsseIdException e) {
				log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (Exception e) {
				log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "alert name cannot be empty and should be unique");
				return response;
			}
		} else {
			log.info("Invalid Monitor Type ID");
			response.put("Status", 0);
			response.put("Msg", "Invalid Monitor Type ID");
		}
		return response;
	}

	// Network Alert
	public JResponse createNetworkAlert(User user, Gateway gateway, String reqFrom) {
		log.info("create NetworkAlert : ");
		JResponse response = new JResponse();
		long monitortype = gateway.getModel().getMonitor_type().getId();
		if (monitortype == 1 || monitortype == 9) {
			String notify_freq = "60";

			String minval = "-70";

			String maxval = "170";

			String alertname = "NetworkAlert" + "_" + gateway.getId();
			String notify_type =  "1100";// sms,email,voicemsg,appnotification
			if(monitortype == 9) {
				notify_type = "0001";
			}


			boolean enable = false;

			try {
				int freq = Integer.parseInt(notify_freq) * 60;

				Long[] assetids = new Long[5];

				assetids[0] = gateway.getId();

				JAlertCfg jalertcfg = new JAlertCfg();

				jalertcfg.setName(alertname);
				jalertcfg.setMinval(Float.valueOf(minval));
				jalertcfg.setMaxval(Float.valueOf(maxval));
				jalertcfg.setNotifyfreq(freq);
				jalertcfg.setMobilenos(user.getMobileno());
				jalertcfg.setEmailids(user.getEmail());
				jalertcfg.setNotificationtype(notify_type);
				jalertcfg.setAssetids(assetids);
				jalertcfg.setAlerttypeid(11);// Alert Type ID for Network Alert
				jalertcfg.setSeverity(4);
				jalertcfg.setEnable(enable);
				jalertcfg.setMin_notifyfreq(3600);
				jalertcfg.setCountry("NA");

				boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(), reqFrom);

				response.put("Status", 1);
				response.put("Msg", "Success");

			} catch (ConstraintViolationException e) {
				log.error("ConstraintViolationException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (InvalidAlertTypeException e) {
				log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid alerttype id");
				return response;
			} catch (InvalidAsseIdException e) {
				log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (Exception e) {
				log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "alert name cannot be empty and should be unique");
				return response;
			}
		} else {
			log.info("Invalid Monitor Type ID");
			response.put("Status", 0);
			response.put("Msg", "Invalid Monitor Type ID");
		}
		return response;
	}

	// On Battery Alert
	public JResponse createOnBatteryAlert(User user, Gateway gateway, boolean enableDelayFreq, int delayFreqSecs, String reqFrom) {
		log.info("create OnBatteryAlert : ");

		JResponse response = new JResponse();
		long monitortype = gateway.getModel().getMonitor_type().getId();
		if (monitortype == 1) {
			String notify_freq = "60";

			String minval = "-70";

			String maxval = "170";

			String alertname = "PowerLossAlert" + "_" + gateway.getId();

			String notify_type = "1100";// sms,email,voicemsg,appnotification


			boolean enable = false;

			try {
				int freq = Integer.parseInt(notify_freq) * 60;

				String rule = "(battStatus <= 100 and battStatus >= 0) or (battStatus == -2)";

				Long[] assetids = new Long[5];

				assetids[0] = gateway.getId();

				JAlertCfg jalertcfg = new JAlertCfg();

				jalertcfg.setName(alertname);
				jalertcfg.setMinval(Float.valueOf(minval));
				jalertcfg.setMaxval(Float.valueOf(maxval));
				jalertcfg.setNotifyfreq(freq);
				jalertcfg.setMobilenos(user.getMobileno());
				jalertcfg.setEmailids(user.getEmail());
				jalertcfg.setNotificationtype(notify_type);
				jalertcfg.setAssetids(assetids);
				jalertcfg.setAlerttypeid(3);// Alert Type ID for Battery Alert
				jalertcfg.setSeverity(4);
				jalertcfg.setEnable(enable);
				jalertcfg.setRule(rule);
				jalertcfg.setCountry("NA");
				jalertcfg.setMin_notifyfreq(1800); // 30 min
				jalertcfg.setEnableDelayFreq(enableDelayFreq);
				jalertcfg.setDelayFreqSecs(delayFreqSecs);

				boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(), reqFrom);

				response.put("Status", 1);
				response.put("Msg", "Success");

			} catch (ConstraintViolationException e) {
				log.error("ConstraintViolationException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (InvalidAlertTypeException e) {
				log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid alerttype id");
				return response;
			} catch (InvalidAsseIdException e) {
				log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (Exception e) {
				log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "alert name cannot be empty and should be unique");
				return response;
			}
		} else {
			log.info("Invalid Monitor Type ID");
			response.put("Status", 0);
			response.put("Msg", "Invalid Monitor Type ID");
		}
		return response;
	}

	// Humidity Alert
	public JResponse createHumidityAlert(User user, Gateway gateway, String reqFrom) {
		JResponse response = new JResponse();
		long monitortype = gateway.getModel().getMonitor_type().getId();
		if (monitortype == 1) {
			log.info("create HumidityAlert : ");
			String notify_freq = "60";
			String minval = "40";
			String maxval = "60";
			String alertname = "HumidityAlert" + "_" + gateway.getId();
			String notify_type = "1100";// sms,email,voicemsg,appnotification

			boolean enable = false;

			try {
				int freq = Integer.parseInt(notify_freq) * 60;
				String rule = "";

				Long[] assetids = new Long[5];
				assetids[0] = gateway.getId();

				JAlertCfg jalertcfg = new JAlertCfg();

				jalertcfg.setName(alertname);
				jalertcfg.setMinval(Float.valueOf(minval));
				jalertcfg.setMaxval(Float.valueOf(maxval));
				jalertcfg.setNotifyfreq(freq);
				jalertcfg.setMobilenos(user.getMobileno());
				jalertcfg.setEmailids(user.getEmail());
				jalertcfg.setNotificationtype(notify_type);
				jalertcfg.setAssetids(assetids);
				jalertcfg.setAlerttypeid(14);// Alert Type ID for Humidity Alert
				jalertcfg.setSeverity(4);
				jalertcfg.setEnable(enable);
				jalertcfg.setRule(rule);
				jalertcfg.setCountry("NA");
				jalertcfg.setMin_notifyfreq(3600);

				boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(), reqFrom);

				response.put("Status", 1);
				response.put("Msg", "Success");

			} catch (ConstraintViolationException e) {
				log.error("ConstraintViolationException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (InvalidAlertTypeException e) {
				log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid alerttype id");
				return response;
			} catch (InvalidAsseIdException e) {
				log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (Exception e) {
				log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "alert name cannot be empty and should be unique");
				return response;
			}
		} else {
			log.info("Invalid Monitor Type ID");
			response.put("Status", 0);
			response.put("Msg", "Invalid Monitor Type ID");
		}
		return response;
	}

	// Create Power Recovery Alert
	public JResponse createPowerRecoveryAlert(User user, Gateway gateway, boolean enableDelayFreq, int delayFreqSecs, String reqFrom) {
		JResponse response = new JResponse();
		long monitortype = gateway.getModel().getMonitor_type().getId();
		log.info("create PowerRecoveryAlert : ");
		if (monitortype == 1) {
			String notify_freq = "60";
			String minval = "-4";
			String maxval = "-3";
			String alertname = "PowerRecoveryAlert" + "_" + gateway.getId();
			String notify_type = "1100";// sms,email,voicemsg,appnotification

			boolean enable = false;
			try {
				int freq = Integer.parseInt(notify_freq) * 60;
				// String rule = "(PrevBattStatus >= -2 and PrevBattStatus <= 100 ) and
				// (battStatus == -3 or battStatus == -4)";
				Long[] assetids = new Long[5];
				assetids[0] = gateway.getId();
				JAlertCfg jalertcfg = new JAlertCfg();
				jalertcfg.setName(alertname);
				jalertcfg.setMinval(Float.valueOf(minval));
				jalertcfg.setMaxval(Float.valueOf(maxval));
				jalertcfg.setNotifyfreq(freq);
				jalertcfg.setMobilenos(user.getMobileno());
				jalertcfg.setEmailids(user.getEmail());
				jalertcfg.setNotificationtype(notify_type);
				jalertcfg.setAssetids(assetids);
				jalertcfg.setAlerttypeid(17);// Alert Type ID for PowerRecoveryAlert
				jalertcfg.setSeverity(2);
				jalertcfg.setEnable(enable);
				// jalertcfg.setRule(rule);
				jalertcfg.setCountry("NA");
				jalertcfg.setMin_notifyfreq(3600);
				jalertcfg.setEnableDelayFreq(enableDelayFreq);
				jalertcfg.setDelayFreqSecs(delayFreqSecs);

				boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(), reqFrom);
				response.put("Status", 1);
				response.put("Msg", "Success");
			} catch (ConstraintViolationException e) {
				log.error("ConstraintViolationException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (InvalidAlertTypeException e) {
				log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid alerttype id");
				return response;
			} catch (InvalidAsseIdException e) {
				log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (Exception e) {
				log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "alert name cannot be empty and should be unique");
				return response;
			}
		} else {
			log.info("Invalid Monitor Type ID");
			response.put("Status", 0);
			response.put("Msg", "Invalid Monitor Type ID");
		}
		return response;
	}

	// Create Geofence Alert
	public JResponse createGeofenceAlert(User user, Gateway gateway, double lat, double lon, String reqFrom) {
		JResponse response = new JResponse();
		long monitortype = gateway.getModel().getMonitor_type().getId();
		log.info("create GeofenceAlert : ");
		if (monitortype == 1 && gateway.getModel().getIsgps().equalsIgnoreCase("1")) {
			try {
				String minval = "0";
				String maxval = "10";// in miles
				float radius = 804.672f; // im meter
				String alertname = "GeofenceAlert" + "_" + gateway.getId();
				String notify_type = "1100";// sms,email,voicemsg,appnotification
				boolean enable = false;
				int notify_freq = 3600;
				// String rule = "NA";
				Long[] assetids = new Long[5];
				assetids[0] = gateway.getId();
				JAlertCfg jalertcfg = new JAlertCfg();

				jalertcfg.setMobilenos(user.getMobileno());
				jalertcfg.setEmailids(user.getEmail());
				jalertcfg.setName(alertname);
				jalertcfg.setMinval(Float.valueOf(minval));
				jalertcfg.setMaxval(Float.valueOf(maxval));
				jalertcfg.setRadius(radius);
				jalertcfg.setNotifyfreq(notify_freq);
				jalertcfg.setLat(lat);
				jalertcfg.setLon(lon);
				jalertcfg.setNotificationtype(notify_type);
				jalertcfg.setAssetids(assetids);
				jalertcfg.setAlerttypeid(4);// Geofence Alert
				jalertcfg.setSeverity(4);
				jalertcfg.setEnable(enable);
				// jalertcfg.setRule(rule);
				jalertcfg.setCountry("NA");
				jalertcfg.setMin_notifyfreq(3600);
				jalertcfg.setFencetype(2);

				boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(), reqFrom);

				response.put("Status", 1);
				response.put("Msg", "Success");
			} catch (ConstraintViolationException e) {
				log.error("ConstraintViolationException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (InvalidAlertTypeException e) {
				log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid alerttype id");
				return response;
			} catch (InvalidAsseIdException e) {
				log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "invalid asset id");
				return response;
			} catch (Exception e) {
				log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "alert name cannot be empty and should be unique");
				return response;
			}
		} else {
			log.info("Invalid Monitor Type ID");
			response.put("Status", 0);
			response.put("Msg", "Invalid Monitor Type ID");
		}
		return response;
	}

	public String createPMAlerts(User user, Gateway gateway, boolean enableDelayFreq, int delayFreqSecs, double lat,double lon, String reqFrom, boolean isAqi) {
		boolean createBatteryAlertStatus = false;
		boolean createNetworkAlertStatus = false;
		boolean createOnBatteryAlertStatus = false;
		boolean createTempAlertStatus = false;
		boolean createGeofenceAlertStatus = false;
		boolean createAqirangeAlertStatus = false;
		boolean createco2RangeAlertStatus = false;

		JResponse createTempAlertResponse = createTemperatureAlert(user, gateway, reqFrom);
		if (createTempAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
			createTempAlertStatus = true;
		}

		// Create Battery Alert
		JResponse createBatteryAlertResponse = createBatteryAlert(user, gateway, reqFrom);
		if (createBatteryAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
			createBatteryAlertStatus = true;
		}

		// Create On Battery Alert
		JResponse createOnBatteryAlertResponse = createOnBatteryAlert(user, gateway, enableDelayFreq, delayFreqSecs, reqFrom);
		if (createOnBatteryAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
			createOnBatteryAlertStatus = true;
		}

		// Create Network Alert
		JResponse createNetworkAlertResponse = createNetworkAlert(user, gateway, reqFrom);
		if (createNetworkAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
			createNetworkAlertStatus = true;
		}

		// Create Humidity Alert
		boolean createHumidityAlertStatus = false;
		JResponse createHumidityAlertResponse = createHumidityAlert(user, gateway, reqFrom);
		if (createHumidityAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
			createHumidityAlertStatus = true;
		}

		// Create Power Recovery Alert
		boolean createRecoveryAlertStatus = false;
		JResponse createRecoveryAlertResponse = createPowerRecoveryAlert(user, gateway, enableDelayFreq, delayFreqSecs, reqFrom);
		if (createRecoveryAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
			createRecoveryAlertStatus = true;
		}

		// Create genfence Alert
		JResponse createGeofenceAlertResponse = createGeofenceAlert(user, gateway, lat, lon, reqFrom);
		if (createGeofenceAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
			createGeofenceAlertStatus = true;
		}
		
		if (isAqi) {
			// Create aqi Alert
			JResponse createAqirangeAlertResponse = createAqiAlert(user, gateway, lat, lon, reqFrom);
			if (createAqirangeAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
				createAqirangeAlertStatus = true;
			}

			// Create aqi Alert
			JResponse createco2RangeAlertResponse = createCo2Alert(user, gateway, lat, lon, reqFrom);
			if (createco2RangeAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
				createco2RangeAlertStatus = true;
			}
		}
		
		String alertCreationStatus = "<br/><br/>CreateTempAlertStatus :" + createTempAlertStatus
				+ "<br/><br/>CreateBatteryAlertStatus :" + createBatteryAlertStatus
				+ "<br/><br/>CreateOnBatteryAlertStatus : " + createOnBatteryAlertStatus
				+ "<br/><br/>CreateNetworkAlertStatus : " + createNetworkAlertStatus
				+ "<br/><br/>CreateHumidityAlertStatus : " + createHumidityAlertStatus
				+ "<br/><br/>CreatePowerRecoveryAlertStatus : " + createRecoveryAlertStatus
				+ "<br/><br/> CreateGeofenceAlertStatus : " + createGeofenceAlertStatus
				+ "<br/><br/>CreateAqiRangeAlertStatus : " + createAqirangeAlertStatus
				+ "<br/><br/> CreateCo2RangeAlertStatus : " + createco2RangeAlertStatus;

		return alertCreationStatus;
	}
	
	// Create Aqi Alert
		public JResponse createAqiAlert(User user, Gateway gateway, double lat, double lon, String reqFrom) {
			JResponse response = new JResponse();
			long monitortype = gateway.getModel().getMonitor_type().getId();
			log.info("create createAqiAlert : ");
			if (monitortype == 1) {
				try {
					String minval = "1";
					String maxval = "5";// in miles
					float radius = 0f; // im meter
					String alertname = "AQIAlert" + "_" + gateway.getId();
					String notify_type = "1100";// sms,email,voicemsg,appnotification
					boolean enable = false;
					int notify_freq = 3600;
					String rule = "( aqi >= 4 )";
					Long[] assetids = new Long[5];
					assetids[0] = gateway.getId();
					JAlertCfg jalertcfg = new JAlertCfg();

					jalertcfg.setMobilenos(user.getMobileno());
					jalertcfg.setEmailids(user.getEmail());
					jalertcfg.setName(alertname);
					jalertcfg.setMinval(Float.valueOf(minval));
					jalertcfg.setMaxval(Float.valueOf(maxval));
					jalertcfg.setRadius(radius);
					jalertcfg.setNotifyfreq(notify_freq);
					jalertcfg.setNotificationtype(notify_type);
					jalertcfg.setAssetids(assetids);
					jalertcfg.setAlerttypeid(18);// AQI Alert
					jalertcfg.setSeverity(4);
					jalertcfg.setEnable(enable);
					jalertcfg.setRule(rule);
					jalertcfg.setCountry("NA");
					jalertcfg.setMin_notifyfreq(3600);
					jalertcfg.setFencetype(0);

					boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(), reqFrom);

					response.put("Status", 1);
					response.put("Msg", "Success");
				} catch (ConstraintViolationException e) {
					log.error("ConstraintViolationException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (InvalidAlertTypeException e) {
					log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid alerttype id");
					return response;
				} catch (InvalidAsseIdException e) {
					log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (Exception e) {
					log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "alert name cannot be empty and should be unique");
					return response;
				}
			} else {
				log.info("Invalid Monitor Type ID");
				response.put("Status", 0);
				response.put("Msg", "Invalid Monitor Type ID");
			}
			return response;
		}
		
		
		// Create Co2 Alert
		public JResponse createCo2Alert(User user, Gateway gateway, double lat, double lon, String reqFrom) {
			JResponse response = new JResponse();
			long monitortype = gateway.getModel().getMonitor_type().getId();
			log.info("create createCo2Alert : ");
			if (monitortype == 1) {
				try {
					String minval = "800";
					String maxval = "0";// in miles
					float radius = 0f; // im meter
					String alertname = "CO2Alert" + "_" + gateway.getId();
					String notify_type = "1100";// sms,email,voicemsg,appnotification
					boolean enable = false;
					int notify_freq = 3600;
					String rule = "( co2 >= 800 )";
					Long[] assetids = new Long[5];
					assetids[0] = gateway.getId();
					JAlertCfg jalertcfg = new JAlertCfg();

					jalertcfg.setMobilenos(user.getMobileno());
					jalertcfg.setEmailids(user.getEmail());
					jalertcfg.setName(alertname);
					jalertcfg.setMinval(Float.valueOf(minval));
					jalertcfg.setMaxval(Float.valueOf(maxval));
					jalertcfg.setRadius(radius);
					jalertcfg.setNotifyfreq(notify_freq);
					jalertcfg.setNotificationtype(notify_type);
					jalertcfg.setAssetids(assetids);
					jalertcfg.setAlerttypeid(19);// Co2 Alert
					jalertcfg.setSeverity(1);
					jalertcfg.setEnable(enable);
					jalertcfg.setRule(rule);
					jalertcfg.setCountry("NA");
					jalertcfg.setMin_notifyfreq(3600);
					jalertcfg.setFencetype(0);

					boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(),
							reqFrom);

					response.put("Status", 1);
					response.put("Msg", "Success");
				} catch (ConstraintViolationException e) {
					log.error("ConstraintViolationException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (InvalidAlertTypeException e) {
					log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid alerttype id");
					return response;
				} catch (InvalidAsseIdException e) {
					log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (Exception e) {
					log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "alert name cannot be empty and should be unique");
					return response;
				}
			} else {
				log.info("Invalid Monitor Type ID");
				response.put("Status", 0);
				response.put("Msg", "Invalid Monitor Type ID");
			}
			return response;
		}
		
		@Override
		public int updatealertrangeDetails(long alertcfgid, long alerttypeid, int alert_range_id,float min,float max) {
			return alertCfgDaoV4.updatealertrangeDetails(alertcfgid, alerttypeid, alert_range_id,min,max);
		}

		@Override
		public String createWCAlerts(User user, Gateway gateway) {
			log.info("Entered into createWCAlerts :: user_id : "+ user.getId()+" :: gateway_id : "+ gateway.getId());
			try {
			
				boolean createBatteryAlertStatus = false;
				boolean createOnBatteryAlertStatus = false;
				boolean createTempAlertStatus = false;
				boolean createHumidityAlertStatus = false;
				boolean createRecoveryAlertStatus = false;
				boolean createMotionDetectionHuman = false;
				boolean createMotionDetectionCat = false;
				boolean createMotionDetectionDog = false;
				boolean createMotionDetection = false;
				boolean createSoundAlert = false;
				boolean createSoundAlertDog = false;
				boolean createSoundAlertCat = false;
				boolean createOfflineAlert = false;
				boolean createScheduleTreatTossAlert = false;
				boolean createDNRAlert = false;
				
				AlertCfgWC alertCfgWC = new AlertCfgWC();
				alertCfgWC.setGateway_id( gateway.getId() );
				alertCfgWC.setEmail_ids( user.getEmail() );
				alertCfgWC.setMobile_nos( user.getMobileno() );
				
				if( gateway.getModel().isTemp_alert() ) {
					alertCfgWC.setAlert_type_id(1);
					alertCfgWC.setMin((float) 15.55);
					alertCfgWC.setMax( (float) 26.6667 );
					alertCfgWC.setId(0);
					alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
					createTempAlertStatus = alertCfgWC != null;
					
					alertCfgWC.setId(0);
					alertCfgWC.setAlert_type_id(14);
					alertCfgWC.setMin(40);
					alertCfgWC.setMax(60);
					alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
					createHumidityAlertStatus = alertCfgWC != null;
					alertCfgWC.setMin(0);
					alertCfgWC.setMax(0);
				}
				
				if( gateway.getModel().isPowermode() ) {
					alertCfgWC.setMin(30);
					alertCfgWC.setId(0);
					alertCfgWC.setAlert_type_id(2);
					alertCfgWC.setMin( Float.parseFloat( IrisservicesConstants.POWER_MIN ));
					alertCfgWC.setMax( Float.parseFloat( IrisservicesConstants.POWER_MAX ));
					alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
					createBatteryAlertStatus = alertCfgWC != null;
					
					alertCfgWC.setId(0);
					alertCfgWC.setMin(0);
					alertCfgWC.setMax(0);
					alertCfgWC.setAlert_type_id(3);
					alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
					createOnBatteryAlertStatus = alertCfgWC!= null;
					
					alertCfgWC.setId(0);
					alertCfgWC.setAlert_type_id(17);
					alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
					createRecoveryAlertStatus = alertCfgWC!= null;
				}
				
				alertCfgWC.setId(0);
				alertCfgWC.setAlert_type_id(11);
				alertCfgWC = saveOrUpdateAlertCfgWC( alertCfgWC );
				createDNRAlert = alertCfgWC != null;
				
				if( gateway.getModel().isMotion_detection() ) {
					alertCfgWC.setId(0);
					alertCfgWC.setAlert_type_id(7);
					alertCfgWC.setNotify_freq(1);
					alertCfgWC.setEnable(true);
					alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
					createMotionDetection = alertCfgWC!= null;
					
					alertCfgWC.setId(0);
					alertCfgWC.setAlert_type_id(20);
					alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
					createMotionDetectionHuman = alertCfgWC!= null;
					
					alertCfgWC.setId(0);
					alertCfgWC.setAlert_type_id(21);
					alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
					createMotionDetectionDog = alertCfgWC!= null;
					
					alertCfgWC.setId(0);
					alertCfgWC.setAlert_type_id(22);
					alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
					createMotionDetectionCat = alertCfgWC!= null;
				}
				
				if (gateway.getModel().isSound_alert() ) {
					alertCfgWC.setId(0);
					alertCfgWC.setAlert_type_id(27);
					alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
					createSoundAlert = alertCfgWC!= null;

					alertCfgWC.setId(0);
					alertCfgWC.setAlert_type_id(23);
					alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
					createSoundAlertDog = alertCfgWC!= null;
					
					alertCfgWC.setId(0);
					alertCfgWC.setAlert_type_id(24);
					alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
					createSoundAlertCat = alertCfgWC!= null;
				}
				
				alertCfgWC.setId(0);
				alertCfgWC.setAlert_type_id(25);
				alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
				createOfflineAlert = alertCfgWC != null;
				
				alertCfgWC.setId(0);
				alertCfgWC.setAlert_type_id(26);
				alertCfgWC = saveOrUpdateAlertCfgWC( new AlertCfgWC(alertCfgWC) );
				createScheduleTreatTossAlert = alertCfgWC != null;
				
				String alertCreationStatus = "<br/><br/> CreateTempAlertStatus :" + createTempAlertStatus
						+ "<br/><br/> createHumidityAlertStatus :" + createHumidityAlertStatus
						+ "<br/><br/> CreateBatteryAlertStatus :" + createBatteryAlertStatus
						+ "<br/><br/> CreateOnBatteryAlertStatus : " + createOnBatteryAlertStatus
						+ "<br/><br/> CreateNetworkAlertStatus : " + createDNRAlert
						+ "<br/><br/> CreateOfflineAlert : " + createOfflineAlert
						+ "<br/><br/> CreatePowerRecoveryAlertStatus : " + createRecoveryAlertStatus
						+ "<br/><br/> CreateMotionDetection : " + createMotionDetection
						+ "<br/><br/> CreateMotionDetectionHuman : " + createMotionDetectionHuman
						+ "<br/><br/> CreateMotionDetectionDog : " + createMotionDetectionDog
						+ "<br/><br/> CreateMotionDetectionCat : " + createMotionDetectionCat
						+ "<br/><br/> CreateSoundAlert : " + createSoundAlert
						+ "<br/><br/> CreateSoundAlertDog : " + createSoundAlertDog
						+ "<br/><br/> CreateSoundAlertCat : " + createSoundAlertCat
						+ "<br/><br/> CreateScheduleTreatTossAlert : " + createScheduleTreatTossAlert;
				
				return alertCreationStatus;
				
			} catch (Exception e) {
				log.error("Error in createWCAlerts :: Error : "+ e.getLocalizedMessage());
			}
			return null;
		}

		@Override
		public AlertCfgWC saveOrUpdateAlertCfgWC(AlertCfgWC alertCfgWC) {
			return alertCfgDaoV4.saveOrUpdateAlertCfgWC(alertCfgWC);
		}

		@Override
		public JAlertsWC getWCAlertStatus(long gatewayid, long user_id) {
			return alertCfgDaoV4.getWCAlertStatus(gatewayid, user_id);
		}

		@Override
		public List<AlertCfgWC> getAlertCfgWC(long gateway_id, String alert_type_id) {
			return alertCfgDaoV4.getAlertCfgWC(gateway_id, alert_type_id);
		}

		@Override
		public boolean updateAlertCfgRange(String min, String max, long gateway_id, String alert_type_id) {
			return alertCfgDaoV4.updateAlertCfgRange(min, max, gateway_id, alert_type_id);
		}

		@Override
		public boolean updateAlertCfgWCEmailOrSmsOrPushNotification(long gateway_id, String value, String key) {
			return alertCfgDaoV4.updateAlertCfgWCEmailOrSmsOrPushNotification(gateway_id, value, key);
		}

		@Override
		public ArrayList<AlertWC> getCurrentDateWCAlerts(long gateway_id) {
			return alertCfgDaoV4.getCurrentDateWCAlerts(gateway_id);
		}
		
		@Override
		public float CelsiusToFahrenheit(float temp_val_degree_celsius) {

			double degreeFahrenheit = Double.valueOf(temp_val_degree_celsius).floatValue() * 9 / 5 + 32;
			double roundvalues = Math.round(degreeFahrenheit * 100.0) / 100.0;
			return (float) roundvalues;
		}

		@Override
		public boolean updateNotifyFrequency(long notify_frequency, long gateway_id, String alert_type_ids) {
			return alertCfgDaoV4.updateNotifyFrequency(notify_frequency, gateway_id, alert_type_ids);
		}
		
		public String createSensorAlerts(User user, Gateway gateway, boolean enableDelayFreq, int delayFreqSecs, double lat,double lon, String reqFrom, int sensorId) {
			
			log.info("Entered into createSensorAlerts :: user_id : "+ user.getId()+" :: gateway_id : "+ gateway.getId());
			
			boolean createBatteryAlertStatus = false;
			boolean createWaterLeakAlertStatus = false;
			boolean createWaterLevelAlertStatus = false;
			boolean createDoorOpenAlertStatus = false;
			boolean createDNRAlert = false;
			boolean createDoorReminderAlertStatus = false;

			// Create Battery Alert
			JResponse createBatteryAlertResponse = createSensorBatteryAlert(user, gateway, reqFrom);
			if (createBatteryAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
				createBatteryAlertStatus = true;
			}

			// Create Network Alert
			JResponse createNetworkAlertResponse = createNetworkAlert(user, gateway, reqFrom);
			if (createNetworkAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
				createDNRAlert = true;
			}
			
			if (sensorId == 1) {
				// Create Door Open Alert
				JResponse createDoorOpenAlertResponse = createDoorOpenAlert(user, gateway, enableDelayFreq,
						delayFreqSecs, reqFrom);
				if (createDoorOpenAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
					createDoorOpenAlertStatus = true;
				}
				
				JResponse createDoorReminderAlertResponse = createDoorReminderAlert(user, gateway, enableDelayFreq,
						delayFreqSecs, reqFrom);
				if (createDoorReminderAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
					createDoorReminderAlertStatus = true;
				}
			}

			if (sensorId == 2) {
				// Create On Water Leak Alert
				JResponse createWaterLeakAlertResponse = createWaterLeakAlert(user, gateway, enableDelayFreq,
						delayFreqSecs, reqFrom);
				if (createWaterLeakAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
					createWaterLeakAlertStatus = true;
				}
			}

			if (sensorId == 3) {
				// Create Water Level Alert
				JResponse createWaterLevelAlertResponse = createWaterLevelAlert(user, gateway, enableDelayFreq,
						delayFreqSecs, reqFrom);
				if (createWaterLevelAlertResponse.getResponse().get("Status").toString().equalsIgnoreCase("1")) {
					createWaterLevelAlertStatus = true;
				}
			}
			
			String alertCreationStatus = "<br/><br/>createBatteryAlertStatus :" + createBatteryAlertStatus
					+ "<br/><br/>createWaterLeakAlertStatus :" + createWaterLeakAlertStatus
					+ "<br/><br/>createWaterLevelAlertStatus : " + createWaterLevelAlertStatus
					+ "<br/><br/>CreateDNRAlertStatus : " + createDNRAlert
					+ "<br/><br/>createDoorOpenAlertStatus : " + createDoorOpenAlertStatus
					+ "<br/><br/>createDoorReminderAlertStatus : " + createDoorReminderAlertStatus;

			return alertCreationStatus;
		}
		
		@Override
		public JAlertSensor getSensorAlertStatus(long gatewayid, long user_id) {
			return alertCfgDaoV4.getSensorAlertStatus(gatewayid, user_id);
		}
		
		// Create Water Leak Alert
		public JResponse createWaterLeakAlert(User user, Gateway gateway, boolean enableDelayFreq, int delayFreqSecs, String reqFrom) {
			JResponse response = new JResponse();
			long monitortype = gateway.getModel().getMonitor_type().getId();
			log.info("create createWaterLeakAlert : ");
			if (monitortype == 9) {
				String notify_freq = "0";
				String minval = "0";
				String maxval = "1";
				String alertname = "WaterLeakAlert" + "_" + gateway.getId();
				String notify_type = "0001";// sms,email,voicemsg,appnotification

				boolean enable = true;
				try {
					int freq = Integer.parseInt(notify_freq) * 60;
					Long[] assetids = new Long[5];
					assetids[0] = gateway.getId();
					JAlertCfg jalertcfg = new JAlertCfg();
					jalertcfg.setName(alertname);
					jalertcfg.setMinval(Float.valueOf(minval));
					jalertcfg.setMaxval(Float.valueOf(maxval));
					jalertcfg.setNotifyfreq(freq);
					jalertcfg.setMobilenos(user.getMobileno());
					jalertcfg.setEmailids(user.getEmail());
					jalertcfg.setNotificationtype(notify_type);
					jalertcfg.setAssetids(assetids);
					jalertcfg.setAlerttypeid(29);// Alert Type ID for WaterLeakAlert
					jalertcfg.setSeverity(2);
					jalertcfg.setEnable(enable);
					// jalertcfg.setRule(rule);
					jalertcfg.setCountry("NA");
					jalertcfg.setMin_notifyfreq(0);
					jalertcfg.setEnableDelayFreq(enableDelayFreq);
					jalertcfg.setDelayFreqSecs(delayFreqSecs);

					boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(), reqFrom);
					response.put("Status", 1);
					response.put("Msg", "Success");
				} catch (ConstraintViolationException e) {
					log.error("ConstraintViolationException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (InvalidAlertTypeException e) {
					log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid alerttype id");
					return response;
				} catch (InvalidAsseIdException e) {
					log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (Exception e) {
					log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "alert name cannot be empty and should be unique");
					return response;
				}
			} else {
				log.info("Invalid Monitor Type ID");
				response.put("Status", 0);
				response.put("Msg", "Invalid Monitor Type ID");
			}
			return response;
		}
		
		// Create Door Open Alert
		public JResponse createDoorOpenAlert(User user, Gateway gateway, boolean enableDelayFreq, int delayFreqSecs,
				String reqFrom) {
			JResponse response = new JResponse();
			long monitortype = gateway.getModel().getMonitor_type().getId();
			log.info("create DoorOpenAlert : ");
			if (monitortype == 9) {
				String notify_freq = "0";
				String minval = "0";
				String maxval = "1";
				String alertname = "DoorOpenAlert" + "_" + gateway.getId();
				String notify_type = "0001";// sms,email,voicemsg,appnotification

				boolean enable = true;
				try {
					int freq = Integer.parseInt(notify_freq) * 60;
					Long[] assetids = new Long[5];
					assetids[0] = gateway.getId();
					JAlertCfg jalertcfg = new JAlertCfg();
					jalertcfg.setName(alertname);
					jalertcfg.setMinval(Float.valueOf(minval));
					jalertcfg.setMaxval(Float.valueOf(maxval));
					jalertcfg.setNotifyfreq(freq);
					jalertcfg.setMobilenos(user.getMobileno());
					jalertcfg.setEmailids(user.getEmail());
					jalertcfg.setNotificationtype(notify_type);
					jalertcfg.setAssetids(assetids);
					jalertcfg.setAlerttypeid(30);// Alert Type ID for DoorOpenAlert
					jalertcfg.setSeverity(2);
					jalertcfg.setEnable(enable);
					jalertcfg.setCountry("NA");
					jalertcfg.setMin_notifyfreq(0);
					jalertcfg.setEnableDelayFreq(enableDelayFreq);
					jalertcfg.setDelayFreqSecs(delayFreqSecs);

					boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(),
							reqFrom);
					response.put("Status", 1);
					response.put("Msg", "Success");
				} catch (ConstraintViolationException e) {
					log.error("ConstraintViolationException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (InvalidAlertTypeException e) {
					log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid alerttype id");
					return response;
				} catch (InvalidAsseIdException e) {
					log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (Exception e) {
					log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "alert name cannot be empty and should be unique");
					return response;
				}
			} else {
				log.info("Invalid Monitor Type ID");
				response.put("Status", 0);
				response.put("Msg", "Invalid Monitor Type ID");
			}
			return response;
		}
		
		// Create Water Level Alert
		public JResponse createWaterLevelAlert(User user, Gateway gateway, boolean enableDelayFreq, int delayFreqSecs,
				String reqFrom) {
			JResponse response = new JResponse();
			long monitortype = gateway.getModel().getMonitor_type().getId();
			log.info("create WaterLevelAlert : ");
			if (monitortype == 9) {
				String notify_freq = "0";
				String minval = "0";
				String maxval = "1";
				String alertname = "WaterLevelAlert" + "_" + gateway.getId();
				String notify_type = "0001";// sms,email,voicemsg,appnotification

				boolean enable = true;
				try {
					int freq = Integer.parseInt(notify_freq) * 60;
					Long[] assetids = new Long[5];
					assetids[0] = gateway.getId();
					JAlertCfg jalertcfg = new JAlertCfg();
					jalertcfg.setName(alertname);
					jalertcfg.setMinval(Float.valueOf(minval));
					jalertcfg.setMaxval(Float.valueOf(maxval));
					jalertcfg.setNotifyfreq(freq);
					jalertcfg.setMobilenos(user.getMobileno());
					jalertcfg.setEmailids(user.getEmail());
					jalertcfg.setNotificationtype(notify_type);
					jalertcfg.setAssetids(assetids);
					jalertcfg.setAlerttypeid(31);// Alert Type ID for WaterLevelAlert
					jalertcfg.setSeverity(2);
					jalertcfg.setEnable(enable);
					jalertcfg.setCountry("NA");
					jalertcfg.setMin_notifyfreq(0);
					jalertcfg.setEnableDelayFreq(enableDelayFreq);
					jalertcfg.setDelayFreqSecs(delayFreqSecs);

					boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(),
							reqFrom);
					response.put("Status", 1);
					response.put("Msg", "Success");
				} catch (ConstraintViolationException e) {
					log.error("ConstraintViolationException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (InvalidAlertTypeException e) {
					log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid alerttype id");
					return response;
				} catch (InvalidAsseIdException e) {
					log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (Exception e) {
					log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "alert name cannot be empty and should be unique");
					return response;
				}
			} else {
				log.info("Invalid Monitor Type ID");
				response.put("Status", 0);
				response.put("Msg", "Invalid Monitor Type ID");
			}
			return response;
		}

		@Override
		public boolean deleteAlertCfgById(ArrayList<Long> alertCfgIds) {
			return alertCfgDaoV4.deleteAlertCfgById(alertCfgIds);
		}
		
		@Override
		public boolean deleteAlertByAssetId(long asset_id) {
			return alertCfgDaoV4.deleteAlertByAssetId(asset_id);
		}
		
		// Create Door Reminder Alert
		public JResponse createDoorReminderAlert(User user, Gateway gateway, boolean enableDelayFreq, int delayFreqSecs,
				String reqFrom) {
			JResponse response = new JResponse();
			long monitortype = gateway.getModel().getMonitor_type().getId();
			log.info("create createDoorReminderAlert : ");
			if (monitortype == 9) {
				String notify_freq = "10";
				String minval = "0";
				String maxval = "1";
				String alertname = "DoorReminderAlert" + "_" + gateway.getId();
				String notify_type = "0001";// sms,email,voicemsg,appnotification

				boolean enable = true;
				try {
					int freq = Integer.parseInt(notify_freq) * 60;
					Long[] assetids = new Long[5];
					assetids[0] = gateway.getId();
					JAlertCfg jalertcfg = new JAlertCfg();
					jalertcfg.setName(alertname);
					jalertcfg.setMinval(Float.valueOf(minval));
					jalertcfg.setMaxval(Float.valueOf(maxval));
					jalertcfg.setNotifyfreq(freq);
					jalertcfg.setMobilenos(user.getMobileno());
					jalertcfg.setEmailids(user.getEmail());
					jalertcfg.setNotificationtype(notify_type);
					jalertcfg.setAssetids(assetids);
					jalertcfg.setAlerttypeid(32);// Alert Type ID for DoorOpenAlert
					jalertcfg.setSeverity(2);
					jalertcfg.setEnable(enable);
					jalertcfg.setCountry("NA");
					jalertcfg.setMin_notifyfreq(0);
					jalertcfg.setEnableDelayFreq(enableDelayFreq);
					jalertcfg.setDelayFreqSecs(delayFreqSecs);

					boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(),
							reqFrom);
					response.put("Status", 1);
					response.put("Msg", "Success");
				} catch (ConstraintViolationException e) {
					log.error("ConstraintViolationException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (InvalidAlertTypeException e) {
					log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid alerttype id");
					return response;
				} catch (InvalidAsseIdException e) {
					log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (Exception e) {
					log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "alert name cannot be empty and should be unique");
					return response;
				}
			} else {
				log.info("Invalid Monitor Type ID");
				response.put("Status", 0);
				response.put("Msg", "Invalid Monitor Type ID");
			}
			return response;
		}
		
		// Create Sensor Battery Alert
		public JResponse createSensorBatteryAlert(User user, Gateway gateway, String reqFrom) {
			log.info("create SensorBatteryAlert : ");

			JResponse response = new JResponse();
			long monitortype = gateway.getModel().getMonitor_type().getId();
			if (monitortype == 9) {
				String notify_freq = "60";
				String minval = "20";
				String maxval = "40";
				String alertname = "BatteryAlert" + "_" + gateway.getId();
				String notify_type = "0001";// sms,email,voicemsg,appnotification
				boolean enable = false;
				try {
					int freq = Integer.parseInt(notify_freq) * 60;
					String rule = "(battStatus<" + minval + " and battStatus>=0) or (battStatus==-2)";
					Long[] assetids = new Long[5];
					assetids[0] = gateway.getId();
					JAlertCfg jalertcfg = new JAlertCfg();
					jalertcfg.setName(alertname);
					jalertcfg.setMinval(Float.valueOf(minval));
					jalertcfg.setMaxval(Float.valueOf(maxval));
					jalertcfg.setNotifyfreq(freq);
					jalertcfg.setMobilenos(user.getMobileno());
					jalertcfg.setEmailids(user.getEmail());
					jalertcfg.setNotificationtype(notify_type);
					jalertcfg.setAssetids(assetids);
					jalertcfg.setAlerttypeid(2);// Alert Type ID for Battery Alert
					jalertcfg.setSeverity(4);
					jalertcfg.setEnable(enable);
					jalertcfg.setRule(rule);
					jalertcfg.setCountry("NA");
					jalertcfg.setMin_notifyfreq(3600);

					boolean result = alertCfgService.saveORupdateAlertCfg(jalertcfg, user.giveCompany().getId(), reqFrom);
					response.put("Status", 1);
					response.put("Msg", "Success");
				} catch (ConstraintViolationException e) {
					log.error("ConstraintViolationException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (InvalidAlertTypeException e) {
					log.error("InvalidAlertTypeException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid alerttype id");
					return response;
				} catch (InvalidAsseIdException e) {
					log.error("InvalidAsseIdException: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "invalid asset id");
					return response;
				} catch (Exception e) {
					log.error("saveORupdateAlertcfg: " + e.getLocalizedMessage());
					response.put("Status", 0);
					response.put("Msg", "alert name cannot be empty and should be unique");
					return response;
				}
			} else {
				log.info("Invalid Monitor Type ID");
				response.put("Status", 0);
				response.put("Msg", "Invalid Monitor Type ID");
			}
			return response;
		}

		@Override
		public int enableAlldisablealertcfg(long assetid, boolean enable) {
			return alertCfgDaoV4.enableAlldisablealertcfg(assetid, enable);
		}

		@Override
		public float getGeofenceDetails(int alertcfgId) {
			return alertCfgDaoV4.getGeofenceDetails(alertcfgId);
		}
}
