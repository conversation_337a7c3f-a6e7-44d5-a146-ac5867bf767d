package com.nimble.irisservices.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.pinpoint.AmazonPinpoint;
import com.amazonaws.services.pinpoint.AmazonPinpointClientBuilder;
import com.amazonaws.services.pinpoint.model.AddressConfiguration;
import com.amazonaws.services.pinpoint.model.DirectMessageConfiguration;
import com.amazonaws.services.pinpoint.model.MessageRequest;
import com.amazonaws.services.pinpoint.model.SMSMessage;
import com.amazonaws.services.pinpoint.model.SendMessagesRequest;
import com.nimble.irisservices.service.SMSInterface;

@Service("amazonPinPoint")
public class AmazonPinPointServiceImpl implements SMSInterface {

	private static final Logger log = LogManager.getLogger(AmazonPinPointServiceImpl.class);
	
	@Value("${pinpoint_aws_access_key_id}")
	private String pinpoint_aws_access_key_id;
	
	@Value("${pinpoint_aws_secret_access_key}")
	private String pinpoint_aws_secret_access_key;
	
	@Value("${region}")
	private String region;
	
	@Value("${origination_number}")
	private String originationNumber;
	
	@Value("${appid}")
	private String appId;
	
	@Value("${registered_keyword}")
	private String registeredKeyword;
	
	@Value("${sender_id}")
	private String senderId;	
	
	@Override
	public boolean SendMessage(String phoneNo, String message, String cmpid, String cmpname, String appname,
			String srcPhNo, boolean enablePowerBack, String powerBackUUid) {// The AWS Region that you want to use to

		log.info("Entered into SendMessage : Amazon PinPoint");

		String messageType = "TRANSACTIONAL";

		try {
			
			String[] phNumberArr = phoneNo.split(","); 
			
			for( String phNumber : phNumberArr ) {
				String destinationNumber = phNumber;
				log.info("Phone Number : "+ destinationNumber);
				Map<String, AddressConfiguration> addressMap = new HashMap<String, AddressConfiguration>();

				addressMap.put(destinationNumber, new AddressConfiguration().withChannelType(com.amazonaws.services.pinpoint.model.ChannelType.SMS));
				
				BasicAWSCredentials awsCreds = new BasicAWSCredentials(pinpoint_aws_access_key_id, pinpoint_aws_secret_access_key);
				
				AmazonPinpoint client = AmazonPinpointClientBuilder.standard().withRegion(region).withCredentials(new AWSStaticCredentialsProvider(awsCreds)).build();
				
				SendMessagesRequest request = new SendMessagesRequest().withApplicationId(appId)
						.withMessageRequest(new MessageRequest().withAddresses(addressMap).withMessageConfiguration(
								new DirectMessageConfiguration().withSMSMessage(new SMSMessage().withBody(message)
										.withMessageType(messageType).withOriginationNumber(originationNumber)
										.withSenderId(senderId).withKeyword(registeredKeyword))));
				log.info("Sending message...");
				client.sendMessages(request);
				log.info("Message sent!");
			}			
			return true;
		} catch (Exception ex) {
			log.error("The message wasn't sent. Error message: " + ex.getMessage());
			return false;
		}
	}

	@Override
	public boolean SendVoiceMessage(String phoneNo, String message, String cmpid, String cmpname, String appname,
			String ip, String srcphno) {
		return false;
	}

	@Override
	public boolean SendEmail(String emailds, String subject, String content) {
		return false;
	}

	@Override
	public boolean SendEmail_SES(String from_email,String emailds, String subject, String content, String filename, boolean isRVMailId) {
		return false;
	}

	@Override
	public boolean SendEmail_SESV2(String alert_email, String emailds, String subject, String content, String filename,
			boolean isRVMailId) {
		return false;
	}

}
