package com.nimble.irisservices.service.impl;

import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Properties;

import javax.imageio.ImageIO;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.InternetHeaders;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.transaction.Transactional;

import com.nimble.irisservices.Util.SecretManagerService;
import org.apache.http.entity.StringEntity;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.amazonaws.services.sqs.model.SendMessageResult;
import com.chargebee.Environment;
import com.chargebee.Result;
import com.chargebee.models.CreditNote;
import com.chargebee.models.CreditNote.ReasonCode;
import com.chargebee.models.CreditNote.Type;
import com.google.gson.Gson;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.appcontroller.OrderMappingControllerApp;
import com.nimble.irisservices.common.ReverseGeocoder;
import com.nimble.irisservices.controller.PushNotificatonController;
import com.nimble.irisservices.dao.IRVCentricDetailsDao;
import com.nimble.irisservices.dto.JCreateGateway;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JGroups;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JSendNotification;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.DeviceReplacedHistory;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.GatewayToAlexa;
import com.nimble.irisservices.entity.Offlineuserdetails;
import com.nimble.irisservices.entity.PetFeedDetails;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.RemoveGatewayRequestHistory;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserCancelFeedBackHistory;
import com.nimble.irisservices.entity.UserLocation;
import com.nimble.irisservices.entity.UserMetaData;
import com.nimble.irisservices.entity.UserToken;
import com.nimble.irisservices.entity.WagglePushNotification;
import com.nimble.irisservices.entity.WifiInfo;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.exception.InvalidUsernameException;
import com.nimble.irisservices.helper.EntityHashing;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.niom.entity.Ordermap;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.pojo.CloudNotifications;
import com.nimble.irisservices.pojo.FCMMultiNotification;
import com.nimble.irisservices.pojo.JSendNotifications;
import com.nimble.irisservices.service.IAlexaService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.ICancelService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IDynamicCmdService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IGroupServices;
import com.nimble.irisservices.service.IMessagingService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IPetSpeciesServicesV4;
import com.nimble.irisservices.service.IPushNotificationService;
import com.nimble.irisservices.service.IPushNotificationServiceV4;
import com.nimble.irisservices.service.IRVCentricDetailsService;
import com.nimble.irisservices.service.IRegisterUserEmailService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IWifiInfoService;

@Service
@Transactional
public class AsyncServiceImpl implements IAsyncService {

	private static final Logger log = LogManager.getLogger(AsyncServiceImpl.class);
	@Autowired
	Helper _helper = new Helper();

	@Autowired
	@Lazy
	IRVCentricDetailsService rvCentService;

	@Autowired
	IRegisterUserEmailService emailService;

	@Autowired
	IMessagingService messagingService;

//	@Autowired
//	IMailService mailService;

	@Autowired
	IUserService userService;

	@Autowired
	IUserServiceV4 userServicev4;

	@Autowired
	@Lazy
	IPushNotificationService iNotificationService;

	@Autowired
	@Lazy
	IExternalConfigService externalConfigService;

	@Autowired
	IRVCentricDetailsDao rvDao;

	@Autowired
	@Lazy
	IWifiInfoService wifiService;

	@Autowired
	@Lazy
	ICreditSystemService crService;

	@Autowired
	@Lazy
	IPushNotificationServiceV4 notificationService;
	
	@Autowired
	@Lazy
	IChargebeeService cbService;
	
	@Autowired
	@Lazy
	IGatewayService gatewayService;
	
	@Autowired
	@Lazy
	IGatewayServiceV4 gatewayServiceV4;
	
	@Autowired
	@Lazy
	INiomDataBaseService niomService;
	
	@Autowired
	@Lazy
	IPetSpeciesServicesV4 ipetSpeciesServicesv4;
	
	@Autowired
	INiomDataBaseService niomDbservice;
	
	@Autowired
	PushNotificatonController pushNotificatonController;

	@Autowired
	private SecretManagerService secretManagerService;

	@Value("${automate_warranty_push_notification}")
	private int automate_warranty_push_notification;

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Value("${iris.services.amazonSQS.microserviceQueue.url}")
	private String amazonSqs_microserviceUrl;

	@Value("${aws_sqs_secret_name}")
	private String SQS_SECRET_NAME;

	@Value("${aws_s3_secret_name}")
	private String S3_SECRET_NAME;

	private String AWS_SQS_ACCESS_KEY;

	private String AWS_SQS_SECRET_KEY;

	@Value("${profileimages_bucketname}")
	private String bucketName;

	private String s3_accessKey;

	private String s3_secretKey;

	@Value("${profileimages_url}")
	private String mapboximages_url;

	@Value("${mapbox_folder_name}")
	private String mapbox_folderName;
	
	@Value("${mapboxtoken}")
	private String mapBoxKeyToken;
	
	@Value("${return.gateway.name}")
	private String returnDeviceGatewayName;
	
	@Autowired
	IDynamicCmdService dynamicService;

	@Autowired
	IAlexaService alexaService;
	
	IrisservicesUtil irisUtil;

	@Autowired
	INiomDataBaseService niomServiceV4;
	
	EntityHashing entityHashing = new EntityHashing();
	
	@Autowired
	@Lazy
	IReportService reportService;
	
	@Autowired
	@Lazy
	IGroupServices groupservices;
	
	@Autowired
	ICancelService cancelService;
	
	@Autowired
	@Lazy
	IPetSpeciesServicesV4 petSpeciesServicesv4;
	
	@Autowired
	OrderMappingControllerApp orderMappingControllerApp;
	
	@Override
	@Async("asyncExecutor")
	public void runAsync() {
		int i = 0;
		while (i < 1000000) {
			i++;
		}
		log.info("AsyncEnd");
	}

	@Async("asyncExecutor")
	public void SendEmail_SES(String toAddr, String ccAddr, String bccAddr, String sub, String mailmsg) {

		String EMAIL_HOST = "email-smtp.us-west-2.amazonaws.com";
		String SES_SECRET_NAME = "/prod/svc/awsses";
		String EMAIL_HOST_USER = secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_access_key");
		String EMAIL_HOST_PASSWORD = secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_secret_key");

		String login = "<EMAIL>";
		Transport transport = null;

		try {
			Properties props = new Properties();
			props.put("mail.transport.protocol", "smtps");
			props.put("mail.smtp.auth", "true");
			props.setProperty("mail.smtp.port", "587");
	           
			props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");

			// Create a Session object to represent a mail session with the
			// specified properties.
			Session session = Session.getDefaultInstance(props);

			// Create a message with the specified information.
			MimeMessage msg = new MimeMessage(session);
			msg.setFrom(new InternetAddress(login));

			if (toAddr != null) {
				msg.setRecipients(Message.RecipientType.TO, toAddr);
			}

			if (ccAddr != null) {
				msg.setRecipients(Message.RecipientType.CC, ccAddr);
			}

			if (bccAddr != null) {
				msg.setRecipients(Message.RecipientType.BCC, bccAddr);
			}
			msg.setSubject(sub);
			// msg.setContent(mailmsg,"text/plain");

			InternetHeaders headers = new InternetHeaders();
			headers.addHeader("Content-type", "text/html; charset=UTF-8");
			MimeBodyPart body = new MimeBodyPart(headers, mailmsg.getBytes("UTF-8"));
			Multipart multipart = new MimeMultipart();
			multipart.addBodyPart(body);

			// Send the complete message parts
			msg.setContent(multipart);

			// Create a transport.
			transport = session.getTransport();
			log.info("Attempting to send an email through the Amazon SES SMTP interface...");

			// Connect to Amazon SES using the SMTP username and password you
			// specified above.
			transport.connect(EMAIL_HOST, EMAIL_HOST_USER, EMAIL_HOST_PASSWORD);

			// Send the email.

			transport.sendMessage(msg, msg.getAllRecipients());
			log.info("Email sent!");
		} catch (AddressException ex) {
			log.error("Address Exception occured :" + ex.getMessage());
		} catch (MessagingException ex) {
			log.error("Message Exception occured :" + ex.getMessage());
		} catch (Exception ex) {
			log.error("SendEmail Exception: " + ex.getMessage());
		} finally {
			try {
				if (null != transport)
					transport.close();
			} catch (Exception e) {
				log.error("SendEmail Exception: " + e.getMessage());
			}
		}
	}

	@Async("asyncExecutor")
	public void updateRegisterUserEmailStatus(String toAddr, String ccAddr, String bccAddr, String sub, String mailmsg,
			String qrCode, String errorCode, boolean status, String meid, String iccid, String vendor,
											  int isRecallDevice,String skuNumber,String userEmail, String deviceId, String tariffId, String qrc) {
		emailService.updateRegisterUserEmailStatus(toAddr, ccAddr, bccAddr, sub, mailmsg, qrCode, errorCode, status,
				meid, iccid, vendor, isRecallDevice, skuNumber,userEmail, deviceId, tariffId, qrc);
	}

	@Async("asyncExecutor")
	public void sendDynamicCommand(String gatewayid, String msg, long seqno) {
		messagingService.saveMessageV2(gatewayid, msg, seqno);
	}

	@Async("asyncExecutor")
	public void saveorupdateofflineUserDetails(Offlineuserdetails offlineUser) {
		userService.saveorupdateofflineUserDetails(offlineUser);
	}

	@Async("asyncExecutor")
	public void createChargebeeUser(String fName, String lName, String email, String phoneno, String chargebeeCusId,
			boolean inapp, String addr, String city, String state, String country) {
		try {
			User user;
			try {
				user = userService.getUserByUNameOrEmail(email);
			} catch (Exception ex) {
				user = null;
				log.error("email not exists in db" + email);
			}

			if (user != null) {
				user.setFirstname(fName);
				user.setLastname(lName);

				if (email.equalsIgnoreCase("NA") || email.equals(null))
					user.setEmail(email);
				user.setMobileno(phoneno);
				user.setChargebeeid(chargebeeCusId);
				user.setUpdatedOn(_helper.getCurrentTimeinUTC());
				userService.updateUser(user);
			} else {
//				SignUp signup = new SignUp();
//
//				signup.setUsername(email);
//				signup.setFirstname(fName);
//				signup.setLastname(lName);
//				signup.setPhoneno(phoneno);
//				signup.setMobileno(phoneno);
//				signup.setEmail(email);
//
//				String password = phoneno;
//				password = password.replaceAll("\\W", "");
//
//				if (password.length() > 10) {
//					password = password.substring(password.length() - 10);
//				}
//				signup.setPassword(password);
//
//				// Assign Default things
//				//int rand = _helper.getRandomNumber(100, 9999999);
//				signup.setCompanyname(signup.getFirstname());
//				signup.setSupervisor("Nimble");
//				signup.setThrotsettingsid("5");
//				signup.setCmptype_id("3");
//				signup.setAddress(addr);
//				signup.setCity(city);
//				signup.setState(state);
//				signup.setCountry(country);
//
//				boolean res = userService.signUp(signup);
//
//				if (res) {
//					user = userService.getUserByEmail(email);
//					user.setChargebeeid(chargebeeCusId);
//					user.setIn_app(inapp); // False for website users
//					userService.updateUser(user);
//
//					UserVerification userVerificationToken = userService.createEmailVerificationToken(user);
//
//					if (userVerificationToken.getStatus().equalsIgnoreCase("pending")) {
//						mailService.sendVerificationMail(user, userVerificationToken.getToken());
//					}
//				}

				// creating chargebee customer

//				try {
//
//					Environment.configure(chargebeeSiteName, chargebeeSiteKey);
//					ListResult resultSet = Customer.list().email().is(email).request(); //
//
//					if (resultSet.size() == 0) {
//						Customer cust = Customer.create().firstName(fName).lastName(lName).email(email).phone(phoneno)
////						.billingAddressFirstName(fName).billingAddressLastName(lName)
////						.billingAddressEmail(email).billingAddressPhone(phoneno)
//						.request().customer();
//					
//						user.setChargebeeid(cust.id());
//						userService.updateUser(user);
//					}
//				} catch (Exception e) {
//					log.error("Error in creating chargebee user : " + e.getMessage());
//				}

			}
		} catch (Exception e) {
			log.error("saveUser : " + e.getMessage());
		}

	}

	@Override
	@Async("asyncExecutor")
	public void createUserInChargebee(String fName, String lName, String email, String phoneNo, String userName,
			int amount, String desc) {
		userServicev4.createUserInChargebee(fName, lName, email, phoneNo, userName, amount, desc);
	}

	@Override
	@Async("asyncExecutor")
	public void updateCBCustomerDetails(User user) {
		cbService.updateCBCustomerDetails(user);
	}

	@Async("asyncExecutor")
	public void sendEmailDataToQueue(String sub, String mailmsg, String qrCode, String errorCode, boolean status,
			String meid, String iccid, String vendor) {

		String mailPacket = sub + "|" + mailmsg + "|" + qrCode + "|" + errorCode + "|" + status + "|" + meid + "|"
				+ iccid + "|" + vendor;
		try {
			AWS_SQS_ACCESS_KEY = secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_access_key");
			AWS_SQS_SECRET_KEY = secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_secret_key");
			AWSCredentials credentials = new BasicAWSCredentials(AWS_SQS_ACCESS_KEY, AWS_SQS_SECRET_KEY);
			AWSStaticCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(credentials);
			AmazonSQS sqs = AmazonSQSClientBuilder.standard().withCredentials(credentialsProvider)
					.withRegion(Regions.US_WEST_2).build();
			mailPacket = "nimble|petmonitor|registeruser|emailstatus|" + mailPacket + "#";
			log.info("sendEmailDataToQueue - Packet ::: " + mailPacket);
			SendMessageRequest sendMessageRequest = new SendMessageRequest(amazonSqs_microserviceUrl, mailPacket);
			SendMessageResult sendMessageResult = sqs.sendMessage(sendMessageRequest);
			String sequenceNumber = sendMessageResult.getSequenceNumber();
			String messageId = sendMessageResult.getMessageId();
			log.info("SendMessage succeed with messageId " + messageId + ",sequence number " + sequenceNumber);
		} catch (Exception e) {
			log.error("Exception - updateRegisterUserEmailStatus : " + e.getLocalizedMessage());
		}

	}

	@Async("asyncExecutor")
	public void externalQrcActivationStatus(String qrcode, String errorCode, String toAddr, String ccAddr,
			String bccAddr, String sub, String mailmsg) {
		log.info("Entered externalQrcActivationStatus. ");
		emailService.externalQrcActivationStatus(qrcode, errorCode, toAddr, ccAddr, bccAddr, sub, mailmsg);
	}

	@Async("asyncExecutor")
	public void updateWifiInfoAddress(WifiInfo wifi) {
		log.info("Entered updateWifiInfoAddress(async) : for ssid : " + wifi.getSsid());
		try {
			ReverseGeocoder revgeo = new ReverseGeocoder();
			String address = revgeo.getAddress_MapBox(wifi.getLat() + "", wifi.getLon() + "");
			wifi.setAddress(address);
			boolean isAddressUpdated = wifiService.updateWifiInfoAddress(wifi.getGatewayid(), wifi.getSsidCategory(),
					wifi.getUserid(), address);
			log.info("Update address for wifi info. isAddressUpdated : " + isAddressUpdated);
		} catch (Exception e) {
			log.error("Error while updateWifiInfoAddress : " + e.getLocalizedMessage());
		}
	}
	
	@Async("asyncExecutor")
	public void addFreeVPM(String featureName, long userId) {
		userServicev4.addFreeVPM(featureName, userId);
	}

	@Async("asyncExecutor")
	public void updateAlexaId(String userid, String AlexaId) {
		alexaService.updateAlexaId(userid, AlexaId);
	}

//	@Transactional
//	@Async("asyncExecutor")
//	public RvTripHistory updateMapboxImage(RvTripHistory rvTripHistory) {
//		boolean imageIsThere = true;
//
//		MapboxDetails mapbox = new MapboxDetails();
//
//		mapbox.setFrom_lon(rvTripHistory.getFrom_lon());
//		mapbox.setFrom_lat(rvTripHistory.getFrom_lat());
//		mapbox.setTo_lon(rvTripHistory.getTo_lon());
//		mapbox.setTo_lat(rvTripHistory.getTo_lat());
//
//		String imagePath = "NA";
//		String imageName = "NA";
//		//imagePath = rvDao.checkMapboxImage(mapbox);
//
//		if (imagePath.contains("-tempName")) {
//			String fileName[] = imagePath.split("-");
//			fileName[0] = String.valueOf(Integer.parseInt(fileName[0]) + 1);
//			imageName = fileName[0] + ".png";
//			imageIsThere = false;
//		}
//
//		if (!imageIsThere) {
//			imageName = mapboxGetImage(
//					String.valueOf(mapbox.getFrom_lon()) + "," + String.valueOf(mapbox.getFrom_lat()),
//					String.valueOf(mapbox.getTo_lon()) + "," + String.valueOf(mapbox.getTo_lat()), imageName);
//
//			if (!imageName.equalsIgnoreCase("NA")) {
//				imagePath = mapboxImageS3BucketUpload(imageName);
//				if (imagePath.contains("-tempName"))
//					imagePath = "NA";
//			}
//
//			if (!imagePath.equalsIgnoreCase("NA")) {
//				rvTripHistory.setImage_path(imagePath);
//				mapbox.setImage_path(imagePath);
//				//rvDao.saveMapboxImage(mapbox);
//			}
//
//			if (!imageName.equalsIgnoreCase("NA")) {
//				File myObj = new File(imageName);
//				if (myObj.delete()) {
//					log.info("Deleted the file: " + myObj.getName());
//				} else {
//					log.info("Failed to delete the file.");
//				}
//			}
//		} else {
//			rvTripHistory.setImage_path(imagePath);
//		}
//
//		rvTripHistory
//				.setShortFromAdd(toGetFromAddress(rvTripHistory.getFrom_lon() + "," + rvTripHistory.getFrom_lat()));
//		rvTripHistory.setShortToAdd(toGetFromAddress(rvTripHistory.getTo_lon() + "," + rvTripHistory.getTo_lat()));
//		rvCentService.saveUserTripHistory(rvTripHistory);
//		return rvTripHistory;
//	}

//	private String toGetFromAddress(String url) {
//		String city = null;
//		String state = null;
//		boolean placeObjIsThere = false;
//		try {
//			JSONObject object = rvCentService.toParseJson(url);
//			JSONArray arr = object.getJSONArray("features");
//			JSONObject o = null;
//			for (int itr = 0; itr < arr.length(); itr++) {
//				o = (JSONObject) arr.get(itr);
//				JSONArray place = o.getJSONArray("place_type");
//				String ob = (String) place.get(0);
//				if (ob.equalsIgnoreCase("place")) {
//					placeObjIsThere = true;
//					JSONArray context = o.getJSONArray("context");
//					city = o.getString("text");
//					int inItr = 0;
//					while (inItr < context.length()) {
//						JSONObject stateObj = (JSONObject) context.get(inItr);
//						if (stateObj.getString("id").contains("region")) {
//							state = stateObj.getString("short_code").toString();
//							String stateNew[] = state.split("-");
//							state = stateNew[1];
//							break;
//						}
//						inItr++;
//
//					}
//					break;
//				}
//			}
//			if (!placeObjIsThere) {
//				for (int itr = 0; itr < arr.length(); itr++) {
//					o = (JSONObject) arr.get(itr);
//					JSONArray place = o.getJSONArray("place_type");
//					String ob = (String) place.get(0);
//					if (ob.equalsIgnoreCase("region")) {
//						city = o.getString("text");
//						JSONObject properties = o.getJSONObject("properties");
//						state = properties.getString("short_code").toString();
//						String stateNew[] = state.split("-");
//						state = stateNew[0];
//						break;
//					}
//				}
//			}
//		} catch (Exception e) {
//			log.error("Exception in toGetFromAddress : "+e.getLocalizedMessage());
//		}
//		return city + "," + state;
//	}

	private String mapboxGetImage(String from, String to, String imageName) {
		try {

			String reqMapbox = "https://api.mapbox.com/styles/v1/mapbox/streets-v11/static/pin-s-embassy+000(" + from
					+ "),pin-s-embassy+f74e4e(" + to
					+ "),path-5+f44-0.5(0)/auto/300x300?access_token="+mapBoxKeyToken;
			URL url = new URL(reqMapbox);

			InputStream in = new BufferedInputStream(url.openStream());
			ByteArrayOutputStream out = new ByteArrayOutputStream();

			byte[] buf = new byte[1024];
			int n = 0;
			while (-1 != (n = in.read(buf))) {
				out.write(buf, 0, n);
			}
			out.close();
			in.close();
			byte[] response = out.toByteArray();

			ByteArrayInputStream bis = new ByteArrayInputStream(response);
			BufferedImage bImage2 = ImageIO.read(bis);
			ImageIO.write(bImage2, "png", new File(imageName));

			return imageName;

		} catch (Exception e) {
			log.error("Exception in mapboxGetImage : "+e.getLocalizedMessage());
			return "NA";
		}
	}

	private String mapboxImageS3BucketUpload(String filename_Sub) {
		String SUFFIX = "/";
		s3_accessKey = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_access_key");
		s3_secretKey = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_secret_key");
		AWSCredentials credentials = new BasicAWSCredentials(s3_accessKey, s3_secretKey);
		// create a client connection based on credentials
		AmazonS3 s3client = new AmazonS3Client(credentials);
		String fileName = mapbox_folderName + SUFFIX + filename_Sub;
		String imageUrl = mapboximages_url + bucketName + SUFFIX + fileName;
		try {
			s3client.putObject(new PutObjectRequest(bucketName, fileName, new File(filename_Sub))
					.withCannedAcl(CannedAccessControlList.PublicRead));
		} catch (Exception ex) {
			return "NA";
		}
		return imageUrl;
	}

	@Transactional
	@Async("asyncExecutor")
	public void sendPushNotification(String userid, String title, String notifyMsg, long reminderid,
			String notificationType) {

		long startTime = System.nanoTime();
		String validateTokenUrl = _helper.getExternalConfigValue("validatetokenurl", externalConfigService);
		String fcmsendUrl = _helper.getExternalConfigValue("fcmsendurl", externalConfigService);
		String authKey = _helper.getExternalConfigValue("fcmauthorization", externalConfigService);
		SimpleDateFormat sdfcurr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		JResponse response = new JResponse();
		HashMap<String, WagglePushNotification> androidPushnotification = new HashMap<String, WagglePushNotification>();
		LinkedList<String> androidRegId = new LinkedList<String>();

		HashMap<String, WagglePushNotification> iosPushnotification = new HashMap<String, WagglePushNotification>();
		LinkedList<String> iosRegId = new LinkedList<String>();

		try {
			UserV4 user = userServicev4.verifyAuthV4("id", "" + userid);

			JSendNotifications sendNotification = new JSendNotifications();
			sendNotification.setSource(notificationType);
			sendNotification.setTitle(title);
			sendNotification.setShortDescription(notifyMsg);
			sendNotification.setReminderViewId(reminderid);
			sendNotification.setImageUrl("NA");

			CloudNotifications cloudNotifications = new CloudNotifications();
			cloudNotifications.setTitle(title);
			cloudNotifications.setBody(notifyMsg);
			cloudNotifications.setSound("default");

			log.info("before: sendPushNotification");

			List<UserToken> userToken = new ArrayList<UserToken>();

			long newUserId = Long.parseLong(userid);

			userToken = userService.getUserToken(newUserId + "");
			boolean notificationSent = false;

			if (null != userToken && userToken.size() > 0) {
				for (UserToken token : userToken) {
					try {
						if (token.getVer().equalsIgnoreCase("v1") || token.getVer().equalsIgnoreCase("v3"))
							continue;

						String validationResponse = null;
						validationResponse = _helper.validateToken(validateTokenUrl + token.getToken(), authKey);
						JSONObject _res = new JSONObject(validationResponse);
						if (_res.has("error")) {
							continue;
						} else {

							String platForm;
							try {
								platForm = _res.getString("platform");
							} catch (Exception ex) {
								log.error("No Platform object found in token validation");
								continue;
							}

							WagglePushNotification wPushNotification = new WagglePushNotification();
							wPushNotification.setReminderId(reminderid);
							wPushNotification.setUserId(newUserId);
							wPushNotification.setUsertokenId(token.getId());

							wPushNotification.setNotification_datetime(sdfcurr.format(new Date()));
							wPushNotification.setNotificationType(notificationType);
							wPushNotification.setOs(platForm);

							if (platForm.equalsIgnoreCase("IOS")) {
								iosPushnotification.put(token.getToken(), wPushNotification);
								iosRegId.add(token.getToken());
							} else {
								androidPushnotification.put(token.getToken(), wPushNotification);
								androidRegId.add(token.getToken());
							}

							notificationSent = true;
						}
					} catch (Exception ex) {
						log.error("Issue  found in token validation : "+ex.getLocalizedMessage());
						notificationSent = false;
						continue;
					}
				}

			} else {
				notificationSent = false;
			}

			if (notificationSent && !androidRegId.isEmpty()) {
				Object[] objArr = androidRegId.toArray();
				String[] androidreglist = Arrays.copyOf(objArr, objArr.length, String[].class);

				FCMMultiNotification fcmNotification = new FCMMultiNotification(androidreglist, "high",
						sendNotification, true, true);

				androidPushnotification = updateNoticationList(androidPushnotification, androidreglist, fcmNotification,
						fcmsendUrl, authKey);

			}
			if (notificationSent && !iosRegId.isEmpty()) {
				Object[] objArr = iosRegId.toArray();
				String[] iosreglist = Arrays.copyOf(objArr, objArr.length, String[].class);

				FCMMultiNotification fcmNotification = new FCMMultiNotification(iosreglist, "high", sendNotification,
						cloudNotifications, true, true);
				iosPushnotification = updateNoticationList(iosPushnotification, iosreglist, fcmNotification, fcmsendUrl,
						authKey);
			}

			ArrayList<WagglePushNotification> totalobject = new ArrayList<WagglePushNotification>();

			List<WagglePushNotification> wpkIos = new ArrayList<WagglePushNotification>(iosPushnotification.values());
			List<WagglePushNotification> wpkAndroid = new ArrayList<WagglePushNotification>(
					androidPushnotification.values());

			totalobject.addAll(wpkIos);
			totalobject.addAll(wpkAndroid);

			boolean Status = iNotificationService.updateWagglePushNotification(totalobject);
			log.info("update Waggle PushNotification : " + Status);

		} catch (InvalidAuthoException ex) {
			log.error("in valid auth:" + ex.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "invalid authentication key");
			return;
		} catch (Exception ex) {
			log.error("sendPushNotification : " + ex.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error in getting the pushnotifications.");
			return;
		}
		return;

	}

	private HashMap<String, WagglePushNotification> updateNoticationList(
			HashMap<String, WagglePushNotification> Pushnotification, String[] reglist,
			FCMMultiNotification fcmNotification, String fcmsendUrl, String authKey) {

		Gson gson = new Gson();
		StringEntity postingString = null;
		String jsonString = null;

		try {
			jsonString = gson.toJson(fcmNotification);
			log.info(jsonString);
			postingString = new StringEntity(jsonString);
			String postResponse = _helper.postFCMRequest(fcmsendUrl, "application/json", authKey, postingString);
			JSONObject sendFCMResponse = new JSONObject(postResponse);

			JSONArray resultlist = sendFCMResponse.getJSONArray("results");

			if (resultlist.length() == reglist.length) {
				for (int i = 0; i < reglist.length; i++) {
					JSONObject result = (JSONObject) resultlist.get(i);
					if (result.has("error"))
						Pushnotification.get(reglist[i]).setStatus("Failed");
					else
						Pushnotification.get(reglist[i]).setStatus("Succeed");
				}

			} else {
				log.info("Error occur While Pusnotification : ");
			}

		} catch (Exception e) {
			log.error("updateNoticationList: " + e.getLocalizedMessage());
		}
		return Pushnotification;
	}

	public boolean updateNotificationStatus(long userId, ArrayList<Long> notificationIds) {
		return notificationService.updateNotificationStatus(userId, notificationIds);
	}

	@Transactional
	@Async("asyncExecutor")
	public void asynPostRequest(String url,String urlParams) {
		
		_helper.httpPOSTRequest(url, urlParams,null);
		return ;
		
	}
	
	@Transactional
	@Async("asyncExecutor")
	public void asynPostRequestV2(String url,String urlParams, String auth, String contentType) {
		
		_helper.httpPOSTRequest(url, urlParams, auth, contentType);
		return ;
		
	}

	@Override
	@Async("asyncExecutor")
	public void saveRecallHistory(long recalUserId, String oldMeid, String newMeid,int isRecallDevice) {
		log.info(" Entered into saveRecallHistory :: userId : "+recalUserId);
		
		DeviceReplacedHistory deviceRecallHistory = new DeviceReplacedHistory();
		
		if( isRecallDevice == 1 ) {
			isRecallDevice = 3;
			deviceRecallHistory.setAction("replace");
		} else if( isRecallDevice == 2 ) {
			isRecallDevice = 4;
			deviceRecallHistory.setAction("recall");
		}
		userService.changeRecallDeviceStatus( recalUserId,oldMeid,isRecallDevice );
		
		deviceRecallHistory.setUserId(recalUserId);
		deviceRecallHistory.setOldMeid(oldMeid);
		deviceRecallHistory.setNewMeid(newMeid);
		deviceRecallHistory.setRegisteredDate( irisUtil.getCurrentTimeUTC() );
		
		boolean deviceRecalHistorySaved = userService.insertRecallDeviceHistory( deviceRecallHistory );
		log.info(" Device Recal History Saved "+ deviceRecalHistorySaved);
	}

	@Override
	@Async("asyncExecutor")
	public void changeMeidAndReplaceInOrderMap(String oldMeid, String newMeid) {
		boolean newMeidUpdated = niomServiceV4.changeMeidAndReplaceInOrderMap(oldMeid,newMeid);
		log.info(" new Meid updated in orderMap : "+newMeidUpdated);
	}

	@Override
	@Async("asyncExecutor")
	public void changePetNameByGatewayId(String petName, long gateway_id) {
		boolean petnameUpdated = false;
		PetProfile petProfile = gatewayService.getPetProfile(gateway_id);
		if( petProfile != null ) {
			petProfile.setName(petName);
			petnameUpdated = gatewayService.updatePetProfile(petProfile);
		}		
		log.info("Pet name updated : "+petnameUpdated);
		
	}

	@Override
	@Async("asyncExecutor")
	public void updateEvalidation(long id, String password) {
		log.info("Create/Update evalidation for user id : "+id);
		String encryptPwd = entityHashing.encryptPassword(password);
		String encryptId = entityHashing.encryptUserid(id);
		boolean isUIdExist = userServicev4.getEvalidation(encryptId);
		
		if (isUIdExist)
			userServicev4.updateEvalidation(encryptId, encryptPwd);
		else
			userServicev4.insertEvalidation(encryptId, encryptPwd);
	}

	@Override
	@Async("asyncExecutor")
	public void mapOldGatewayToReturnAC(Gateway replacedGateway, String returnLoginUsername, String OldUsername) {
		log.info("Entered into mapOldGatewayToReturnAC :: returnLoginUsername : "+returnLoginUsername +" Old Gateway Meid : "+replacedGateway.getMeid());
		try {
			
			User user = null;
			try {
				user = userService.getUserByName(returnLoginUsername);
			} catch (InvalidUsernameException e) {
				log.info("Invalid return login username : "+returnLoginUsername+ " Error : " +e.getLocalizedMessage());
			}
			
			List<JGateway> userGateway = gatewayService.getGateway(null, null, null, null, user.getId(),
					null);
			
			boolean gatewayAlreadyIn = false;
			int i = 1;
			String petName = returnDeviceGatewayName;
			String newPetname = petName;
			String errorCode = checkGateway(userGateway, replacedGateway.getQrcode(), newPetname);
			
			if( errorCode.equalsIgnoreCase("ER006") ) {
				gatewayAlreadyIn = true;
			}
			
			Loop: while (errorCode != null) {
				if (errorCode.equalsIgnoreCase("ER044")) {
					newPetname = petName + "-" + i;
					errorCode = checkGateway(userGateway, replacedGateway.getQrcode(), newPetname);
					i = i + 1;
				} else {
					replacedGateway.setName(newPetname);
					break Loop;
				}
			}
			
			createOrUpdateGateway(user,replacedGateway);
			
		} catch (Exception e) {
			log.error("Error in mapOldGatewayToReturnAC :: returnLoginUsername : "+returnLoginUsername +" oldUsername : "+OldUsername +" meid : "+replacedGateway.getMeid() + " : "+e.getLocalizedMessage());
		}	
	}
	
	public JCreateGateway createOrUpdateGateway(User user, Gateway replacedGateway) {

		boolean updateCreaditStatus = false;
		boolean saveGatewayReportStatus = false;
		boolean saveLastGatewayReport = false;
		boolean isGatewayCreated = false;

		JCreateGateway jcreateGateway = new JCreateGateway();

		JGateway jgateway = new JGateway();
		jgateway.setName(replacedGateway.getName());
		jgateway.setMeid(replacedGateway.getMeid());
		jgateway.setMdn(replacedGateway.getMdn());
		jgateway.setCarrier("NA");
		jgateway.setModelid(replacedGateway.getModel().getId());
		jgateway.setEnable(true);
		jgateway.setAlive(false);
		jgateway.setSensorEnable(replacedGateway.getSensorenable());
		jgateway.setPasswordtype(1);
		jgateway.setDescription("");
		jgateway.setQrcode(replacedGateway.getQrcode());
		jgateway.setMacid(replacedGateway.getMacid());
		jgateway.setShowOrderId(false);
		jgateway.setOldMeid("NA");
		jgateway.setRecallReplaceAction(0);

		String levelid = "1";
		String group = "";
		Long group_id = null;
		
		List<JGroups> jgroups = groupservices.getGroups(group, group, levelid,
				user.giveCompany().getId());

		if (jgroups.get(0) != null) {
			group_id = jgroups.get(0).getId();
		}
		
		jgateway.setGroupid(group_id);
		jgateway = gatewayService.gatewayExitsinDB(jgateway, user.giveCompany().getId());

		Gateway gateway = new Gateway();
		try {
			
			gateway = gatewayService.saveORupdateReturnGateway(jgateway, user.giveCompany().getId());
			gateway = gatewayService.saveReturnGateway(gateway);
			isGatewayCreated = true;
		} catch (Exception e1) {
			log.error("Exception in createOrUpdateGateway : "+e1.getLocalizedMessage());
			return null;
		}

		if (jgateway.isUserGatDis()) {
			user.getGateways().add(gateway);
			user.setFirstname(user.getFirstname());
			user.setLastname(user.getLastname());
			userService.updateUser(user);
		}

		try {
			gatewayService.updateGatewayCredit(gateway.getId(), user.giveCompany().getId());
			updateCreaditStatus = true;

		} catch (Exception e) {
			log.error("Gateway credits  not updated for Gateway: " + gateway.getId() + "  : " + e.getMessage());
			log.error("Gateway credits not updated for Gateway: " + gateway.getId() + "  : " + e.getLocalizedMessage());
		}

		if (jgateway.getId() == 0) {
			try {
				// gatewayService.updateGatewayCredit(gateway.getId(),
				// user.giveCompany().getId());
				// updateCreaditStatus = true;

				reportService.saveGatewayReport(gateway, 270.0, 270.0);
				saveGatewayReportStatus = true;

				reportService.saveLastGatewayReport(gateway, 270.0, 270.0);
				saveLastGatewayReport = true;
			} catch (Exception e) {
				log.error("7saveORupdateGateway-Default Reports Canot be Generated::::" + e.getLocalizedMessage());
			}
		}

		jcreateGateway.setUpdateCreaditStatus(updateCreaditStatus);
		jcreateGateway.setSaveGatewayReportStatus(saveGatewayReportStatus);
		jcreateGateway.setSaveLastGatewayReport(saveLastGatewayReport);
		jcreateGateway.setGatewayCreated(isGatewayCreated);
		jcreateGateway.setJgateway(jgateway);
		jcreateGateway.setGateway(gateway);
		return jcreateGateway;
	}
	
	public String checkGateway(List<JGateway> userGateway, String qrcCode, String petName) {
		for (JGateway jGateway : userGateway) {
			
			if (jGateway.getQrcode().toLowerCase().equals(qrcCode.toLowerCase())) 
				return "ER006";
			
			if (jGateway.getName().toLowerCase().equals(petName.toLowerCase())) 
				return "ER044";	 
		}
		return "No Error";
	}

	@Override
	@Async("asyncExecutor")
	public void sendSlackMessage(String title, String message) {
		_helper.sendtoSlack(title, message);
		return;
	}

	@Override
	@Async("asyncExecutor")
	public void saveCurrentPlanDetails(long user_id,String chargebee_id,int plan_id, int period_id, String chargebee_plan_id, JResponse response) {
		cbService.saveCurrentPlanDetails(user_id,chargebee_id,plan_id, period_id, chargebee_plan_id, response);
	}

	@Override
	@Async("asyncExecutor")
	public void enableDisableMarketingNotification(UserMetaData userNotif) {
		userServicev4.enableDisableMarketingNotification(userNotif);
	}

	@Override
	@Async("asyncExecutor")
	public void updateSalesChannel(String user_id, String orderChannel, long gatewayId, String meid, String order_id, long mtype_id) {
		List<Ordermap> order_map = niomService.getMappedOrderByMeid(meid);
		if (order_map != null) {
			int is_bundle = niomService.getIsBundle(order_id, orderChannel, mtype_id);
			cbService.updateSalesChannel(user_id, orderChannel, gatewayId, order_map.get(0).getOrder_date(), order_id,
					is_bundle);
		}
	}
	
	@Override
	@Async("asyncExecutor")
	public void generateAddiBenefits(UserV4 user, String period,String createdFrom) {
		cbService.generateAddiBenefits(user, period,createdFrom);
	}

	@Override
	@Async("asyncExecutor")
	public void saveOrUpdateUserCancelFeedBackHistory(UserCancelFeedBackHistory userCancelFeedBackHistory) {
		cancelService.saveOrUpdateUserCancelFeedBackHistory(userCancelFeedBackHistory);
	}

	@Override
	@Async("asyncExecutor")
	public void saveOrUpdateRemoveGatewayRequestHistory(RemoveGatewayRequestHistory removeGatewayRequestHistory) {
		gatewayServiceV4.saveOrUpdateRemoveGatewayRequestHistory(removeGatewayRequestHistory);
	}

	@Override
	@Async("asyncExecutor")
	public void updatePetFeedRemainder(PetFeedDetails petFeedDetails, UserV4 user) {
		boolean status = petSpeciesServicesv4.updatePetFeedRemainder(petFeedDetails, user);
	}

	@Override
	@Async("asyncExecutor")
	public void updateReqCalories(long gatewayid, double req_calories) {
		ipetSpeciesServicesv4.updateReqCalories(gatewayid, req_calories);
	}

	@Override
	@Async("asyncExecutor")
	public void checkAndUpdateGatewayToAlexa(long user_id) {
		log.info("Entered into checkAndUpdateGatewayToAlexa :: user_id : " + user_id);
		try {
			
			GatewayToAlexa gatewayToAlexa = gatewayServiceV4.getGatewayToAlexaByUserId( user_id );
			
			if( gatewayToAlexa == null ) {
				
				List<JGateway> gatewayList = gatewayService.getJGatewayByUser(user_id, "");
				
				if( gatewayList == null || gatewayList.isEmpty()) {
					log.info("User dont have gateway :: user_id : "+user_id);
					return;
				}
				
				long gatewayId = 0;
				for( JGateway gateway : gatewayList ) {
					if( gateway.getMonitorTypeId() == 4 ) {
						gatewayId = gateway.getId();
					}
				}
				
				if( gatewayId == 0 ) {
					log.info("No wagglecam found for that user");
					return;
				}
				
				gatewayToAlexa = new GatewayToAlexa(0, user_id, gatewayId);
				gatewayToAlexa = gatewayServiceV4.saveOrUpdateGatewayToAlexa( gatewayToAlexa );
				
				if( gatewayToAlexa == null ) 
					log.error("saveOrUpdateGatewayToAlexa failed");
				
				log.info("saveOrUpdateGatewayToAlexa success");
			} else {
				log.info("gateway_to_alexa already found :: user_id : "+ user_id+" :: gateway_id : "+ gatewayToAlexa.getGateway_id());
			}
			
		} catch (Exception e) {
			log.error("Error in checkAndUpdateGatewayToAlexa :: Error : "+ e.getLocalizedMessage());
		}
	}
	
	@Override
	@Async("asyncExecutor")
	public void updateReqCaloriesById(long petProfileid, double req_calories) {
		ipetSpeciesServicesv4.updateReqCaloriesById(petProfileid, req_calories);
	}
	
	@Override
	@Async("asyncExecutor")
	public void updateReqWeightById( long profileid, double req_weight){
		ipetSpeciesServicesv4.updateReqWeightById(profileid, req_weight);
	}
	
	@Override
	@Async("asyncExecutor")
	public void updateReqWeightByProfId(long petProfileid, double req_calories) {
		ipetSpeciesServicesv4.updateReqWeightByProfId(petProfileid, req_calories);
	}
	
	@Override
	@Async("asyncExecutor")
	public void updateCxRefund(String invoiceid, int refundAmt) {
		
		try {
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);
			Result result = CreditNote.create()
					.referenceInvoiceId(invoiceid)
					.total(refundAmt)
					.type(Type.REFUNDABLE)
					.reasonCode(ReasonCode.PRODUCT_UNSATISFACTORY)
					.customerNotes("Subscription updated with coupon")
					.request();
				CreditNote creditNote = result.creditNote();
				
//			Result result = Invoice.retrieve(invoiceid).request();
//			Invoice invoice = result.invoice();
			
			//int refundable_cr = invoice.
//			result = Invoice.refund(invoiceid)
//					.refundAmount(refundAmt)																
//					.creditNoteReasonCode(ReasonCode.SERVICE_UNSATISFACTORY)
//					.request();
			
//					Invoice invoice = result.invoice();
//					Transaction transaction = result.transaction();
//					CreditNote creditNote = result.creditNote();

		} catch (Exception e) {
			log.error("cancelsubplan : refund exception : "
					+ e.getLocalizedMessage());
		}
	}

	@Override
	public void orderMap(long userId, String email, String mobile_no, long gatewayId, String country, String qrc, long monitor_type_id, String warrantyClaimType) {
		log.info("Entered into orderMap :: email : "+email+" :: mobile_no : "+ mobile_no+" :: gateway_id : "+gatewayId+" :: country : "+ country + " :: qrc : "+ qrc +" :: monitor_type_id : "+monitor_type_id);
		try {
			Orders orderInfo = niomDbservice.getOrderByEmailOrPhone(userId, email, mobile_no, qrc );
			UserV4 user = null;
			
			if( orderInfo != null ) {
				
				String order_id = String.valueOf( orderInfo.getOrder_id() );
				
				if( orderInfo.getOrder_acc_type().getAcc_type().equalsIgnoreCase("amazon") ) 
					order_id = orderInfo.getExternal_order_id();
				JResponse response = orderMappingControllerApp.orderMapV5(null, null, 
						order_id,
						orderInfo.getOrder_acc_type().getAcc_type(),
						gatewayId,
						"NA",
						"ios",
						"1.1.1", false, "register_product", false, country, email, false,false,"1753-01-01");
				
				if(  (int) response.get("Status") == 1 ) {
					
					
					try {
						user = userServicev4.verifyAuthV3("username", email);
					} catch (InvalidAuthoException e) {
						log.error("Invalid Authkey :" + e.getLocalizedMessage());
						response.put("Status", 0);
						response.put("Msg", "User not found. " + RegisterUserError.warrantySupportMsg);
					}
					
					Thread.sleep(15000);

					JSendNotification sendNotification  = new JSendNotification();
					
					long[] user_id = { user.getId() };
					String[] emailArr = { email };
					String auth = user.getAuthKey();
					
					log.info("order map automation remainder data :: user_id : "+ user_id[0] + " :: email : "+ emailArr[0] + " :: gateway_id : "+ gatewayId+" :: auth : "+ auth);
					
					if( ( monitor_type_id != 5 && monitor_type_id != 6 ) && gatewayId != 0 && userServicev4.checkDeviceNotify( user_id[0], gatewayId ) ) {
						
						sendNotification.setPushNotificationId( automate_warranty_push_notification );
						sendNotification.setUserID(user_id);
						sendNotification.setEmaiID(emailArr);
						
						JResponse res = pushNotificatonController.sendNotifications( auth, sendNotification);	
					}
				}


				if(!gatewayService.saveWarrantyClaimType(warrantyClaimType, gatewayId)) {
					log.info("Failed to save warranty claim type");
				}
			}	
		} catch (Exception e) {
			log.error("Error in orderMap :: Error : "+ e.getLocalizedMessage());
		}
		
	}

	@Override
	@Async("asyncExecutor")
	public void insertDeviceSub(long userid,long gatewayid,String orderdate,long monitortypeid,long sales_channel,boolean isgps) {
		gatewayServiceV4.insertDeviceSub(userid, gatewayid, orderdate,monitortypeid,sales_channel,isgps);
	}


	@Override
	public void insertDynamicCmd(Gateway gateway, String cmdValue, int i, String status) {
		dynamicService.insertDynamicCmd(gateway, cmdValue, i, status);
	}

	@Override
	@Async("asyncExecutor")
	public void updateUserLocation(UserLocation userLocation) {
		userServicev4.updateUserLocation(userLocation);
	}
		
}
