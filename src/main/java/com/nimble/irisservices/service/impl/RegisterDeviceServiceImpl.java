package com.nimble.irisservices.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.lang.reflect.Type;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.nimble.irisservices.appcontroller.OrderMappingControllerApp;
import com.nimble.irisservices.controller.ManageController;
import com.nimble.irisservices.dto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.ResourceUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dao.IGatewayDao;
import com.nimble.irisservices.dao.impl.RegisterUserEmailImpl;
import com.nimble.irisservices.dto.JCreateGateway;
import com.nimble.irisservices.dto.JGateway;
import com.nimble.irisservices.dto.JGatewayUserDetails;
import com.nimble.irisservices.dto.JGroups;
import com.nimble.irisservices.dto.JPetprofile;
import com.nimble.irisservices.dto.JRegisterDevice;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JValidateString;
import com.nimble.irisservices.dto.SignUp;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Asset;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.CompanyType;
import com.nimble.irisservices.entity.DeviceReplaced;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.Groups;
import com.nimble.irisservices.entity.Offlineuserdetails;
import com.nimble.irisservices.entity.OrderMappingDetails;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.ProductSubscription;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.entity.UpgradeDeviceHistory;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.error.RegisterUserError;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.exception.InvalidUsernameException;
import com.nimble.irisservices.helper.Email;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.helper.Thinkspace;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Ordermap;
import com.nimble.irisservices.niom.entity.Orders;
import com.nimble.irisservices.service.IAlertCfgService;
import com.nimble.irisservices.service.IAlertCfgServiceV4;
import com.nimble.irisservices.service.IAlertService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IChargebeeService;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICompanyTypeService;
import com.nimble.irisservices.service.ICreditSystemService;
import com.nimble.irisservices.service.IGatewayService;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IGroupServices;
import com.nimble.irisservices.service.IMonitorTypeService;
import com.nimble.irisservices.service.INiomDataBaseService;
import com.nimble.irisservices.service.IOAuth2Service;
import com.nimble.irisservices.service.IRVCentricDetailsService;
import com.nimble.irisservices.service.IRechargeService;
import com.nimble.irisservices.service.IRegisterDeviceService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IThrottlingService;
import com.nimble.irisservices.service.IUserService;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IVerizonService;

import freemarker.template.Template;

@Service
public class RegisterDeviceServiceImpl implements IRegisterDeviceService {

	private static final Logger log = LogManager.getLogger(RegisterDeviceServiceImpl.class);

	@Autowired
	IUserService userService;

	@Autowired
	IGatewayService gatewayService;

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	IGatewayServiceV4 gatewayServiceV4;

	@Autowired
	INiomDataBaseService niomDbservice;

	@Autowired
	IGatewayDao gatewayDao;

	@Autowired
	Helper _helper;

	@Autowired
	ICompanyTypeService companyTypeServ;

	@Autowired
	IThrottlingService throttlingService;

	@Autowired
	ICompanyService companyService;

	@Autowired
	IGroupServices groupservices;

	@Autowired
	IAsyncService async;
	
	@Autowired
	ICreditSystemService crService;
	
	@Autowired
	freemarker.template.Configuration templates;
	
	@Autowired
	IReportService reportService;
	
	@Autowired
	IAlertCfgServiceV4 alertCfgServiceV4;
	
	@Autowired
	Email email_helper;
	
	@Autowired
	IOAuth2Service oAuth2Service;
	
	@Autowired
	IChargebeeService cbService;
	
	@Autowired
	IRechargeService reService;
	
	@Autowired
	IRVCentricDetailsService rvServ;
	
	@Autowired
	Thinkspace thinkspace;

	@Value("#{${supportcontactnumber}}")
	private Map<String, String> supportContactNumber;

	@Value("#{${supportemail}}")
	private Map<String, String> supportContactEmail;

	@Value("${check.recall.device.qrc}")
	private boolean checkRecallQRC;

	@Value("${show_orderid_popup_while_register}")
	private boolean show_warranty_popup_config;
	
	@Value("${config.oauth2.clientid.app}")
	private String clientIdApp;

	@Value("${config.oauth2.clientsecret.app}")
	private String clientSecretApp;
	
	@Value("${n13_registration_content}")
	private String n13_registration_content;
	
	@Value("${return.login.username}")
	private String returnLoginUsername;
	
	@Value("${product_subs_enable}")
	private boolean product_subs_enable;
	
	@Value("${redirect.payment.page}")
	private boolean redirectPaymentPage_config = false;
	
	@Value("${subscription.buynow.popup}")
	private boolean subscriptionBuyNowPopUp_config;
	
	@Value("${redirect.payment.page.content}")
	private String redirectPaymentPageContent;
	
	@Value("${enable_sku_based_vsim_activation}")
	private boolean enableSkuBasedActivation;
	
	@Value("${cust_name}")
	private String cust_name;

	@Value("${acc_name}")
	private String acc_name;
	
	@Value("${plan}")
	private String plan;
	
	@Value("${wc_ultra_msg}")
	private String wc_ultra_msg;
	
	@Value("${bundle_contact_us_content}")
	private String bundle_contact_us_content;
	
	@Value("${eseye_tariffid}")
	private String tariffId;
	
	@Autowired
	RegisterUserEmailImpl registerUserEmailImpl;
	
	@Autowired
	@Lazy
	IVerizonService verizonService;
	
	@Autowired
	@Lazy
	IAlertService alertService;

	@Autowired
	@Lazy
	IAlertCfgService alertcfgService;

	@Autowired
	IMonitorTypeService MonitorTypeService;

	@Autowired
	ManageController manageController;

	@Override
	public JResponse registerDevice(JRegisterDevice registerDevice) {

		log.info("Entered into registerDevice :: email : " + registerDevice.getEmail());
		log.info("detailed info :: " + registerDevice.toString());

		JResponse response = new JResponse();
		boolean bundledOrderFailed = false;
		String to_address = null;
		String cc_address = null;
		String bcc_address = null;
		Properties prop = new Properties();

		String errorResponse = "No Error";
		String errorCode = "ER000";

		Gateway replacedGateway = null;
		long recalUserId = 0;
		String oldMeid = "NA";
		int isRecallDevice = 0;
		long gatewayId = 0;

		String petName = registerDevice.getPet_name();
		if(registerDevice.getMonitor_type() > 0 && (petName == null || petName.trim().equalsIgnoreCase(""))) {
			String monitorType = MonitorTypeService.getMonitorNameById(registerDevice.getMonitor_type());
			petName = monitorType != null ? monitorType.replace("+", "Plus") : petName;
			registerDevice.setPet_name(petName);
		}

		boolean show_warranty_popup = show_warranty_popup_config;

		Orders order = new Orders();
		Inventory inventory = new Inventory();
		Gson gson = new Gson();

		String monitortype = "NA";
		int model_id = 6;
		long monitortypeid = 0;
		boolean isgps = false;
		int initialDeviceStateid = 9;
		boolean enableDelayFreq = false;
		int delayFreqSecs = 0;
		String sensor_available = null;
		String subscriptionStatus = "0";
		String passwordType = "1";

		boolean activateSim = false;
		boolean updateExternalOrderDataStatus = false;
		boolean accountActivatedStatus = false;
		boolean orderMappedtatus = false;
		boolean activateSimStatus = false;
		//boolean isTestUserDevice = false;
		//boolean isQrcUserDevice = false;
		boolean provisionStatus = false;
		boolean loginCreationIssue = false;
		boolean gatewayCreationIssue = false;
		boolean accountCreated = false;
		boolean orderid_given = false;
		String alertCreationStatus = null;

		User intialUser = new User();
		User orderMapUser = null;
		long createuser_id = 0l;
		int devicecount = 0;
		Long group_id = null;
		long user_id = 0;
		String companyType = "3";
		List<JGateway> userGateway = null;
		JGateway oldGatewayUpgrade = null;
		Orders orderInfo = null;
		String authKey = null;
		
		try {

			String support_country = registerDevice.getCountry();
			if (support_country.equalsIgnoreCase("US") || support_country.equalsIgnoreCase("NA")
					|| support_country.toLowerCase().contains("india") || support_country.equalsIgnoreCase("in")) {
				support_country = "US";
			}
			String supportM = supportContactEmail.get(support_country);
			String supportP = supportContactNumber.get(support_country);

			String eRes = RegisterUserError.commonErrorMessage;
			String errorMsg = eRes.replace("#SP#", supportP).replace("#SM#", supportM);

			response.put("show_bundle_contact_us", false);
			response.put("bundle_contact_us_content", bundle_contact_us_content);
			
			try {
				File file = ResourceUtils.getFile("classpath:iris3.properties");
				prop.load(new FileInputStream(file));
				to_address = prop.getProperty("to_address");
				cc_address = prop.getProperty("cc_address");
				bcc_address = prop.getProperty("bcc_address");

			} catch (Exception e) {
				log.info("signup:::" + e.getMessage() + "" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", errorMsg);
				errorCode = "ER001";
				errorResponse = RegisterUserError.ER001;
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			
			log.info("email : "+ registerDevice.getEmail() +" :: already_user : "+ registerDevice.isAlready_user()+" :: mobile_no : "+ registerDevice.getMobile_no()+" :: country : "+ registerDevice.getCountry());
			
			if( !registerDevice.isAlready_user() && !registerDevice.getMobile_no().contains("-") ) {
				String country = registerDevice.getCountry().toUpperCase();
				String mobileNo = registerDevice.getMobile_no();
				switch ( country ) {
				case "US":
					registerDevice.setMobile_no( "1-" + mobileNo );
					break;
				case "IN":
					registerDevice.setMobile_no( "91-" + mobileNo );
					break;
				case "GB":
					registerDevice.setMobile_no( "44-" + mobileNo );
					break;
				case "AU":
					registerDevice.setMobile_no( "61-" + mobileNo );
					break;
				case "MX":
					registerDevice.setMobile_no( "52-" + mobileNo );
					break;
				case "NZ":
					registerDevice.setMobile_no( "64-" + mobileNo );
					break;
				case "DE":
					registerDevice.setMobile_no( "49-" + mobileNo );
					break;
				case "IT":
					registerDevice.setMobile_no( "39-" + mobileNo );
					break;
				default:
					registerDevice.setMobile_no( "1-" + mobileNo );
					break;
				}
				
			}

			if (registerDevice.getLat() == 270.0 && registerDevice.getLon() == 270.0) {
				double lat = 32.8914656, lon = -117.1506437;
				registerDevice.setLat(lat);
				registerDevice.setLon(lon);
			}
			
			UserV4 userDto = null;
			if (registerDevice.isAlready_user()) {

				try {
					userDto = userServiceV4.verifyAuthV3("email", registerDevice.getEmail());
					authKey = userDto.getAuthKey();
				} catch (InvalidAuthoException e) {
					log.error("Email not found :: email " + registerDevice.getEmail());
					response.put("Status", 0);
					response.put("Msg", "Enter valid email id");
					return response;
				}

			} else {

				JValidateString validString = userServiceV4.checkAlphabetOnly(registerDevice.getFirst_name(),
						registerDevice.getLast_name());
				if (!validString.isValid()) {
					response.put("Status", 0);
					response.put("Msg", validString.getMsg());
					return response;
				}

			}

			if (checkRecallQRC && registerDevice.isCheck_recall_qrc()) {
				boolean qrcAvailable = gatewayServiceV4.checkRecallQRC(registerDevice.getQrc());
				if (qrcAvailable) {
					errorCode = "ER049";
					response.put("Status", 0);
					response.put("recall_qrc", true);
					response.put("recall_qrc_msg", RegisterUserError.recallQRCInfoMsg);
					response.put("upgrade_msg", RegisterUserError.contectMsgNormalTxt);
					response.put("Msg", RegisterUserError.recallQRCMsg);
					errorResponse = "Old device need to recall";
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			}

			// Validate QRC Code
			if (!(registerDevice.getQrc().matches("[a-zA-Z0-9]+")) || !(registerDevice.getQrc().length() == 6 || (registerDevice.getQrc().length() == 18 && registerDevice.getQrc().substring(0, 4).equalsIgnoreCase("BSCN")))
					|| registerDevice.getQrc().startsWith("8")) {
				log.info("Invalid QRCode . " + registerDevice.getQrc());
				response.put("Status", 0);
				response.put("Msg", RegisterUserError.ER048);
				errorCode = "ER048";
				errorResponse = RegisterUserError.ER048;
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			
			log.info("Validating Pet Name");
			if (registerDevice.isAlready_user()) {
				User petUser = new User();
				try {
					petUser = userService.getUserByName( userDto.getUsername() );
					recalUserId = petUser.getId();
					registerDevice.setMobile_no( userDto.getMobileno() );
					DeviceReplaced deviceReplaced = userService.getRecallDeviceDetails(petUser.getId(),registerDevice.getMonitor_type());

					if (deviceReplaced != null) {
						oldMeid = deviceReplaced.getMeid();
						isRecallDevice = deviceReplaced.getIsReplaced();

						if (isRecallDevice == 1) {
							show_warranty_popup = false;
						}
					}

//					List<JGateway> jGatewayList = gatewayService.getJGatewayByUser(petUser.getId(), "");
					userGateway = gatewayService.getGateway(null, null, null, null, petUser.getId(),
							null);

					if( registerDevice.isDevice_upgrade() ) {
					log.info("get old gateway details for device updrage :: old_gateway_id : "+ registerDevice.getUpgrade_gateway_id());
						for(JGateway gateway : userGateway ) {
							
							if( gateway.getId() == registerDevice.getUpgrade_gateway_id() ) {
								oldGatewayUpgrade = gateway;
							}
							
						}
						
						if( oldGatewayUpgrade == null ) {
							log.info("No old gateway found :: gateway_id : "+ registerDevice.getUpgrade_gateway_id());
							response.put("Status", 0);
							response.put("Msg", errorMsg);
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}
						
					}
					
					if (userGateway != null && userGateway.size() > 0) {
						final String tempOldMeid = oldMeid;

						if (!userGateway.stream()
								.anyMatch(gateway -> gateway.getMeid().equalsIgnoreCase(tempOldMeid))) {
							isRecallDevice = 0;
							oldMeid = "NA";
						}

//						if (userGateway.size() >= 5 && !(isRecallDevice > 0)) {
//							response.put("Status", 0);
//							response.put("Msg", RegisterUserError.deviceCountMessage);
//							errorCode = "deviceCountMessage";
//							response.put("Return Time", System.currentTimeMillis());
//							errorResponse = "Device Count Restriction : " + RegisterUserError.deviceCountMessage;
//							response.put("Return Time", System.currentTimeMillis());
//							return response;
//						}
						
//						if (registerDevice.isDevice_upgrade() && oldGatewayUpgrade != null) {
//							registerDevice.setPet_name(oldGatewayUpgrade.getName());
//							petName = oldGatewayUpgrade.getName();
//						}
						response = checkGateway(userGateway, registerDevice.getQrc(), petName);

						if (response.getResponse().containsValue("ER006")) {
							errorCode = "ER006";
							errorResponse = RegisterUserError.ER006;
							response.put("Return Time", System.currentTimeMillis());
							return response;
						} else {
							int i = 1;
							String newPetname = petName;
							Loop: while (response != null) {
								if (response.getResponse().containsValue("ER044")) {
									newPetname = petName + "-" + i;
									response = checkGateway(userGateway, registerDevice.getQrc(), newPetname);
									i = i + 1;
								} else {
									registerDevice.setPet_name(newPetname);
									break Loop;
								}
							}
						}
					} else {
						isRecallDevice = 0;
						oldMeid = "NA";
					}
				} catch (InvalidUsernameException ex) {
					response.put("Status", 0);
					response.put("Msg", errorMsg);
					errorCode = "ER025";
					errorResponse = "Validating Pet Name : " + RegisterUserError.ER025;
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			}

			// Check if gateway name contains special
			String gateway_name = registerDevice.getPet_name();
			Pattern special = Pattern.compile("[!@#$%&*()+=|<>?{}.,\\[\\]~'\"]");
			Matcher hasSpecial = special.matcher(gateway_name);
			if (hasSpecial.find()) {
				response.put("Status", 0);
				errorCode = "ER009";
				errorResponse = RegisterUserError.ER009;
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			
			orderInfo = niomDbservice.getOrderByEmailOrPhone(userDto.getId(), registerDevice.getEmail(), registerDevice.getMobile_no(), registerDevice.getQrc() );
			
			if( orderInfo != null ) {
				if( orderInfo.getOrder_acc_type().getAcc_type().equalsIgnoreCase("rv") ) {
					try {
						ArrayList<String> subDetails = reService.getReSubscription("NA", orderInfo.getOrder_id()+"");
						if( subDetails != null && !subDetails.isEmpty() ) {
							if ( subDetails.get(3) != registerDevice.getEmail() ) {
								orderInfo = null;
							}
						}
					} catch (Exception e) {
						log.error("Error while checking recharge customer");
					}
				}
			}
			
			if( registerDevice.isAlready_user() && orderInfo != null ) {
				ProductSubscription productSubscription = cbService.getProductSubscriptionByOrderId( orderInfo.getOrder_id() );
//				ArrayList<String> subDetails = reService.getReSubscription( registerDevice.getEmail() , orderInfo.getOrder_id()+"" );
//				if( productSubscription != null || !subDetails.isEmpty()) {
				if( productSubscription != null ) {
					String planver = cbService.getPlanVersionbyplanname(productSubscription.getPlan_id());
					if( planver != null && !planver.equalsIgnoreCase("NA") && !planver.equalsIgnoreCase("V3")) {
						boolean checkPaidPlan = cbService.checkCurrentPaidPlan( userDto.getChargebeeid(), 1 );
						if (checkPaidPlan) {
							productSubscription.setUser_id(userDto.getId());
							productSubscription.setActive_subscription(true);
							cbService.saveOrUpdateProductSubscription(productSubscription);
							log.info("bundled order failed due to already have paid plan");
							bundledOrderFailed = true;
							response.put("Status", 0);
							response.put("Msg", errorMsg);
							response.put("show_bundle_contact_us", true);
							response.put("bundle_contact_us_content", bundle_contact_us_content);
							return response;
						}
					}
				}
			}

			if(registerDevice.getPurchased_from() != null && !registerDevice.getPurchased_from().isEmpty() && registerDevice.getPurchased_from().equalsIgnoreCase("Mywaggle/Waggle Merch")){
				registerDevice.setPurchased_from("rv");
			}
			
			JSONObject jorderIdCheckResponse = new JSONObject();
			if (registerDevice.isShow_order_id()) {// hide order id
				jorderIdCheckResponse = userService.getNiomGetOrderCount(registerDevice.getPurchased_from(),
						registerDevice.getOrder_id());

				if (jorderIdCheckResponse == null) {
					response.put("Status", 0);
					response.put("Msg", errorMsg);
					log.info("Error Code : ER035. " + RegisterUserError.ER035);
					errorResponse = RegisterUserError.ER035;
					errorCode = "ER035";
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}

				int orderIdCheckStatus = jorderIdCheckResponse.getInt("Status");

				if (orderIdCheckStatus > 0) {
					order = gson.fromJson(jorderIdCheckResponse.getJSONObject("Order").toString(), Orders.class);
					int totalOrderCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalordered"));
					int totalMappedCount = Integer.parseInt(jorderIdCheckResponse.getString("Totalmapped"));

					if (!(totalMappedCount < totalOrderCount)) {
						response.put("Status", 0);
						response.put("Msg", errorMsg);
						log.info("Error Code : ER040. " + RegisterUserError.ER040);
						errorResponse = RegisterUserError.ER040;
						errorCode = "ER040";
						response.put("Return Time", System.currentTimeMillis());
						return response;
					}
					log.info("Order id found in niom. Order Channel : " + order.getOrder_acc_type().getAcc_type()
							+ "Order ID : " + order.getOrder_id() + "Out Order ID  :" + order.getExternal_order_id());
					if (order.getOrder_acc_type().getAcc_type().toLowerCase().contains("amazon")
							|| order.getOrder_acc_type().getAcc_type().toLowerCase().contains("walmart")
							|| order.getOrder_acc_type().getAcc_type().toLowerCase().contains("technorv")) {
						if (order.getBilling_email().toLowerCase().contains("na")
								|| order.getBilling_phone().toLowerCase().contains("na")) {
							order.setBilling_email(registerDevice.getEmail());
							order.setBilling_phone(registerDevice.getMobile_no());
							order.setOrder_acc_typeid(order.getOrder_acc_type().getId());
							order.setWelcome_status(order.getWelcome_status());
							niomDbservice.updateExternalOrdersInNiom(order);
						}
					}

				} else {
					response.put("Status", 0);

					String eRes1 = RegisterUserError.ER039;
					errorResponse = eRes1.replace("#SP#", supportP).replace("#SM#", supportM);
					response.put("Msg", errorResponse);

					log.info("Order id not found in niom, Error Code : ER039 :" + errorResponse);
					errorCode = "ER039";
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			}

			// Getting Inventory thru DataBase
			JSONObject jresponse = new JSONObject();
			jresponse = userService.getInventory(registerDevice.getQrc());
			boolean isAlreadyWarrentyClaimedWC = false;
			
			if (jresponse == null) {
				response.put("Status", 0);
				response.put("Msg", errorMsg);
				log.info("Error Code : ER002. : " + RegisterUserError.ER002);
				errorResponse = RegisterUserError.ER002;
				errorCode = "ER002";
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

			int status = jresponse.getInt("Status");

			if (status > 0) {
				try {
					Type inventoryList = new TypeToken<ArrayList<Inventory>>() {
					}.getType();

					List<Inventory> _inventoryList = gson.fromJson(jresponse.getJSONArray("inventory").toString(),
							inventoryList);

					if (_inventoryList.size() == 0) {
						response.put("Status", 0);
						response.put("Msg",
								"No device found for scanned QR Code : " + registerDevice.getQrc()
										+ ". Please contact us at " + supportContactNumber.get(support_country)
										+ " / or send email to " + supportContactEmail.get(support_country) + ".");

						errorResponse = "No device found for respective QR Code : " + registerDevice.getQrc();

						log.info("No device found for respective qrccode" + registerDevice.getQrc());
						errorCode = "ER003";
						response.put("Return Time", System.currentTimeMillis());
						return response;
					} else {

						inventory = gson.fromJson(jresponse.getJSONArray("inventory").get(0).toString(),
								Inventory.class);
						List<Ordermap> orderMap = niomDbservice.getMappedOrderByMeid( inventory.getMeid() );
						if( orderMap != null && !orderMap.isEmpty() ) {
							log.info("ordermap found for meid : "+ inventory.getMeid());
							isAlreadyWarrentyClaimedWC = true;
						} else {
							log.info("No order map found for meid : "+ inventory.getMeid());
						}
							
						

						monitortype = inventory.getDevicemodelnumber();
						initialDeviceStateid = (int) inventory.getDevicestate().getId();

						AssetModel assetModel = gatewayDao.getAssetModelByName(monitortype);

						if (assetModel != null) {
							model_id = (int) assetModel.getId();
							monitortypeid = assetModel.getMonitor_type().getId();
							sensor_available = assetModel.getSensoravailable();
							enableDelayFreq = assetModel.isEnableDelayFreq();
							delayFreqSecs = assetModel.getDelayFreqSecs();
							isgps =Boolean.getBoolean(assetModel.getIsgps());
							int userGatewaySize = 0;
							final long monitorTypeId = monitortypeid;
							if( userGateway != null ) {
								
								userGatewaySize = userGateway
													.stream()
													.filter( gateway -> gateway.getMonitorTypeId() == monitorTypeId )
													.collect( Collectors.toList() ).size();
								
							}
							
							if ( userGatewaySize >= 5 && !(isRecallDevice > 0) ) {
								response.put("Status", 0);
								if(monitorTypeId==1)
									response.put("Msg", "You can add up to 5 Pet Monitors.  For more details, please contact our support team.");
								else if(monitorTypeId==3)
									response.put("Msg", "You can add up to 5 Smart AI Bowl.  For more details, please contact our support team.");
								else if(monitorTypeId==4)
									response.put("Msg", "You can add up to 5 WaggleCam.  For more details, please contact our support team.");
								else if(monitorTypeId==5)
									response.put("Msg", "You can add up to 5 RV Cam AI Mini.  For more details, please contact our support team.");
								else if(monitorTypeId==6)
									response.put("Msg", "You can add up to 5 WaggleCam Pro.  For more details, please contact our support team.");
								else if(monitorTypeId==8)
									response.put("Msg", "You can add up to 5 RV 4G Camera.  For more details, please contact our support team.");
								else if(monitorTypeId==9)
									response.put("Msg", "You can add up to 5 RV Smart Sensors.  For more details, please contact our support team.");
								else if(monitorTypeId==12)
									response.put("Msg", "You can add up to 5 RV 4G Mini.  For more details, please contact our support team.");
								else
									response.put("Msg", RegisterUserError.deviceCountMessage);
								
								errorCode = "deviceCountMessage";
								response.put("Return Time", System.currentTimeMillis());
								errorResponse = "Device Count Restriction : " + RegisterUserError.deviceCountMessage;
								response.put("Return Time", System.currentTimeMillis());
								return response;
							}
							
							if( registerDevice.getMonitor_type() != 0 && registerDevice.getMonitor_type() != monitortypeid ) {
								
								response.put("Status", 0);
								response.put("Msg", errorMsg);

								log.error("user register a different product :" + monitortype);

								errorCode = "ER014";
								errorResponse = "user register a different product " + monitortype;
								response.put("Return Time", System.currentTimeMillis());
								return response;								
							}
							

							response.put("Monitortypeid", monitortypeid);
						} else {
							response.put("Status", 0);
							response.put("Msg", errorMsg);

							log.info("Device Model not found in Assetmodel table :" + monitortype);

							errorCode = "ER014";
							errorResponse = "Assetmodel not found for " + monitortype;
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}

						// #9-shipped to amazon #2-shipped full filment
						if ( !isAlreadyWarrentyClaimedWC && inventory.getDevicestate().getId() != 9 && inventory.getDevicestate().getId() != 2) {
							response.put("Status", 0);
							response.put("Msg", errorMsg);
							response.put("Return Time", System.currentTimeMillis());
							log.info("Device State ID is not suitable to activate the product . Device State ID :"
									+ inventory.getDevicestate().getId() + "  QRC Code : " + registerDevice.getQrc());

							errorCode = "ER032";
							errorResponse = "Device State ID is not suitable to activate the product . Device State ID :"
									+ inventory.getDevicestate().getId() + "  QRC Code : " + registerDevice.getQrc();
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}

						if (registerDevice.isShow_order_id()) { // hide order id
							List<Ordermap> orderMapList = new ArrayList<Ordermap>();
							Gson _gson = new Gson();

							Type orderListType = new TypeToken<ArrayList<Ordermap>>() {
							}.getType();
							orderMapList = _gson.fromJson(jorderIdCheckResponse.getJSONArray("Ordermap").toString(),
									orderListType);

							if (orderMapList.size() > 0) {
								for (Ordermap map : orderMapList) {
									if (map.getMeid().trim().equals(inventory.getMeid().trim())) {
										response.put("Status", 0);
										response.put("Msg", errorMsg);
										log.info(
												"Device is already mapped to some other orders . Need to check in the NIOM order map table.");
										response.put("ErrorCode", "ER005");
										errorCode = "ER005";
										errorResponse = RegisterUserError.ER005;
										response.put("Return Time", System.currentTimeMillis());
										return response;
									}
								}
							}
						}
						// Check Device is already activated
						JGatewayUserDetails gatewayUserDetails = gatewayService.getGateway(inventory.getMeid());

						// check MEID is already activated if activated return
						// the response

						boolean pushResponse = false;

						if (isRecallDevice == 1) { // 1 - Replaced Device
							boolean isAvailableInOrderMap = niomDbservice.checkMeidIsAvailableInOrderMap(oldMeid);

							if (isAvailableInOrderMap) {
								pushResponse = niomDbservice.changeMeidAndReplaceInOrderMap(oldMeid,
										inventory.getMeid());
							}

						}

						if (registerDevice.isShow_order_id()) { // hide order id

							if (isRecallDevice != 1) {
								pushResponse = userService.orderMapping(inventory.getMeid(),
										_helper.getCurrentTimeinUTC(), inventory.getDevicestate().getId(),
										order.getOrder_id() + "", order.getDevicemodel(), subscriptionStatus,
										"1", "manual", gatewayId);
							}

							if (pushResponse) {
								orderMappedtatus = true;
								orderid_given = true;
								// Update order details in device subscription table
								async.updateSalesChannel(user_id+"", registerDevice.getPurchased_from(), gatewayId, inventory.getMeid(), 
										order.getOrder_id()+"" ,monitortypeid);

							} else {
								response.put("Status", 0);
								response.put("SpecificMsg", "Unable to map the order");
								response.put("Msg", errorMsg);
								errorCode = "ER041";
								errorResponse = RegisterUserError.ER041;
								response.put("Return Time", System.currentTimeMillis());
								return response;
							}

							log.info("Push Data Response : " + orderMappedtatus+"\n Activate Sim from Config : " + activateSim);
						} else {
							orderMappedtatus = true;
						}

						// Create Gateway
						try {

							if (!registerDevice.isAlready_user()) {

								JResponse jResponseSignUp = userSignUp(registerDevice, registerDevice.getEmail(),
										registerDevice.getCountry());
								if ((Integer) jResponseSignUp.getResponse().get("Status") == 0) {
									errorCode = "ER007";
									errorResponse = RegisterUserError.ER007;
									return jResponseSignUp;
								}
							}
							User user = new User();
							try {
								
								if( registerDevice.isAlready_user() && userDto != null)
									user = userService.getUserByName( userDto.getUsername() );
								else 
									user = userService.getUserByName( registerDevice.getEmail() );
								
								intialUser = user;
								orderMapUser = user;
								provisionStatus = true;
								createuser_id = user.getId();
							} catch (InvalidUsernameException ex) {
								log.error("==Invalid username exception===========");
								loginCreationIssue = true;
								response.put("Status", 0);
								response.put("Msg", errorMsg);
								errorCode = "ER025";
								errorResponse = "Getting user Details : " + RegisterUserError.ER025;
								response.put("Return Time", System.currentTimeMillis());
								return response;
							}

							if (orderid_given) {
								boolean isUserIdUpdated = crService
										.updateUserIdInProsuctSubs(String.valueOf(order.getOrder_id()), user.getId());
							}

							String levelid = "1";
							String group = "";

							List<JGroups> jgroups = groupservices.getGroups(group, group, levelid,
									user.giveCompany().getId());

							if (jgroups.get(0) != null) {
								group_id = jgroups.get(0).getId();
							}

							Template welcomeEmailTemplate = (Template) templates.getTemplate("welcomeemail.ftl");
							
							switch (monitortypeid+"") {
							case "3": {
								// smart bowl
								break;
							}
							case "4": {
								welcomeEmailTemplate = (Template) templates.getTemplate("welcomeemail_wc.ftl");
								break;	
							}
							case "6": {
								welcomeEmailTemplate = (Template) templates.getTemplate("welcomeemail_wc_pro.ftl");
								break;
							}
							
							}
							
							Map<String, String> welcomeEmailParams = new HashMap<>();
							welcomeEmailParams.put("FIRSTNAME", registerDevice.getFirst_name());
							welcomeEmailParams.put("USERNAME", user.getEmail());
							welcomeEmailParams.put("FREEVPMNOTE", "");
							welcomeEmailParams.put("COPYRIGHT_YEAR", _helper.getCurrentYear());
							welcomeEmailParams.put("SUPPORT_EMAIL", supportM);
							welcomeEmailParams.put("SUPPORT_PHONE", supportP);
							welcomeEmailParams.put("SUPPORT_PHONE_WITH_COUNTRYCODE", supportP);
							welcomeEmailParams.put("COPYRIGHT_YEAR", _helper.getCurrentYear());

							String siteUrl = "www.mywaggle.com";
							String operating_hours = "10 AM - 8 PM EST";
							if ( registerDevice.getCountry().equalsIgnoreCase("AU") ) {
								siteUrl = "www.mywaggle.com.au";
								operating_hours = "7 AM - 6 PM AEDT";
							}

							welcomeEmailParams.put("SITE_URL", siteUrl);
							welcomeEmailParams.put("OPERATING_HOURS", operating_hours);

							Gateway gateway = new Gateway();
							boolean updateCreaditStatus = false;
							boolean saveGatewayReportStatus = false;
							boolean saveLastGatewayReport = false;

							JCreateGateway jcreateGateway = new JCreateGateway();

							if (!oldMeid.equalsIgnoreCase("NA"))
								replacedGateway = gatewayService.getGatewayDetails(oldMeid);

							jcreateGateway = createOrUpdateGateway( registerDevice, inventory, model_id, group_id,
									sensor_available, passwordType, user, oldMeid, isRecallDevice, isAlreadyWarrentyClaimedWC);

							if (jcreateGateway != null) {
								log.info("== Update credit status===========");
								updateCreaditStatus = jcreateGateway.isUpdateCreaditStatus();
								saveGatewayReportStatus = jcreateGateway.isSaveGatewayReportStatus();
								saveLastGatewayReport = jcreateGateway.isSaveLastGatewayReport();

								alertCreationStatus = "UpdateGatewayCreditStatus : " + updateCreaditStatus
										+ "<br/><br/>SaveGatewayReportStatus : " + saveGatewayReportStatus
										+ "<br/><br/>SaveLastGatewayReport :" + saveLastGatewayReport;

								if (jcreateGateway.isGatewayCreated()) {
									gateway = jcreateGateway.getGateway();
									String  username = user.getUsername();
									
									JPetprofile jpp = new JPetprofile();
									jpp.setGateway_id(String.valueOf(gateway.getId()));
									jpp.setGatewayName(gateway.getName());
									jpp.setName(gateway.getName());

									jpp.setUser_id(user.getId());
									long mtype = gateway.getModel().getMonitor_type().getId();
									
									if(mtype ==1 || mtype==3 || mtype==7) {
										boolean pp_created = createDefaultPetProfile(jpp);
										log.info("pp_created:"+gateway.getId()+" : "+pp_created);
									}
									
									if(!username.contains("wagvu.co")&& !username.contains("mywaggle.")
										&& !username.contains("nimble.co")&& !username.contains("nim.co")) {
										
										async.insertDeviceSub(user.getId(), gateway.getId(), IrisservicesUtil.getCurrentTimeUTC(),monitortypeid,gateway.getOrder_channel(),isgps);
									}

									log.info("== Create alerts===========");


									if (mtype == 1 && isRecallDevice == 0) {

										String alertStatus = alertCfgServiceV4.createPMAlerts(user, gateway,
												enableDelayFreq, delayFreqSecs, registerDevice.getLat(),
												registerDevice.getLon(), "registerproduct",gateway.getModel().isIs_aqi());

										alertCreationStatus = alertCreationStatus + alertStatus;

									} else if (mtype == 4 || mtype == 5 || mtype == 6 || mtype == 8 ||  mtype == 12) {
										boolean gatewayStatus = gatewayService
												.createOrResetGatewayStatus(gateway.getId(), gateway.getMeid(), inventory.getSerialnumber(),mtype,isRecallDevice);
										
										log.info("create or reset gateway_status : " + gatewayStatus);
										if ( mtype == 4 ) {
											String alertStatus = alertCfgServiceV4.createWCAlerts(user, gateway);
											alertCreationStatus = alertCreationStatus + alertStatus;
										}
									} else if(mtype == 9 && isRecallDevice == 0) {
										String alertStatus = alertCfgServiceV4.createSensorAlerts(user, gateway,
												enableDelayFreq, delayFreqSecs, registerDevice.getLat(),
												registerDevice.getLon(), "registerproduct",registerDevice.getSensor_type_id());
										alertCreationStatus = alertCreationStatus + alertStatus;
									}

									if (jcreateGateway.isGatewayCreated()) {
										String emailContent = "";

										ResponseEntity<String> newEmailContent = ResponseEntity.ok(
												FreeMarkerTemplateUtils.processTemplateIntoString(welcomeEmailTemplate,
														welcomeEmailParams));
										emailContent = newEmailContent.getBody();
										String emailSubject= monitortypeid==6 ? "Welcome to WaggleCam – Your Pet Moments, Captured Perfectly .":"Welcome to Waggle!";
										if (email_helper.SendEmail_SES(user.getEmail(), null, bcc_address,
												emailSubject, emailContent)) {
											log.info("User Created Successfully : " + user.getEmail());
											provisionStatus = true;
										}
									}
									if ( registerDevice.isShow_order_id()) { // hide order id
										if(orderMappedtatus &&
												!gatewayService.saveWarrantyClaimType("manual", gateway.getId())) {
											log.info("Failed to save warranty claim type");
										}
										orderMappedtatus = niomDbservice.updateOrdermapUserDetails(order.getOrder_id(),
												user.getUsername(), user.getId() + "", "NA");

										updateExternalOrderDataStatus = userService.updateOrdersDataV2( registerDevice.getPurchased_from() ,
												order, inventory);
									}
									accountActivatedStatus = true;

								} else {
									gatewayCreationIssue = true;
								}
							} else {
								gatewayCreationIssue = true;
							}

							if (accountActivatedStatus) {
								
								if (gateway.getModel().getMonitor_type().getId() == 1 || (gateway.getModel().getMonitor_type().getId() == 9))
									sendDynamicMsg(gateway);
								
								boolean subscriptionBuyNowPopUp = false;
								if( (show_warranty_popup && orderInfo != null )|| !show_warranty_popup ) {
									show_warranty_popup = false;
//									if( monitortypeid == 1 )
										subscriptionBuyNowPopUp = true;
								}

								
								response.put("Status", 1);
								response.put("Msg", RegisterUserError.loginPageActivationMessage);
								response.put("display_msg", RegisterUserError.loginPageActivationMessage);
								response.put("gatewayid", gateway.getId());
								response.put("meid", gateway.getMeid());
								response.put("gname", gateway.getName());
								if (gateway.getModel().getMonitor_type().getId() == 9) {
									response.put("bleId", "M10_" + gateway.getMacid());
									response.put("sensor_type_code", gateway.getSensor_type_id());
								}else {
									response.put("bleId", "");
									response.put("sensor_type_code", 0);
								}
										
								response.put("show_warranty_popup", show_warranty_popup);
//								response.put("warranty_msg", warranty_msg);
								response.put("subscription_buynow_popup", subscriptionBuyNowPopUp);
//								response.put("subscription_buynow_content", subscriptionBuyNowContent);
//
//								response.put("show_subs_page_first", show_subs_page_first);
//								response.put("show_orderid_later_popup", orderid_later_popup);
//								response.put("orderid_later_popup_content", orderid_later_popup_content);
								response.put("Return Time", System.currentTimeMillis());

								response.put("show_n13_popup", false);
								response.put("n13_popup_content", n13_registration_content);
								if (gateway.giveAsset() != null && gateway.giveAsset().getModel() != null
										&& gateway.giveAsset().getModel().getModel().contains("N13")) {
									response.put("show_n13_popup", true);
								}
								response.put("token", null);
								
								if( userGateway != null && userGateway.size() >= 1 ) {
									boolean statusCompany = companyService.updateCustomPlan( user.giveCompany().getId(), true );
								}

								if ( !registerDevice.isAlready_user() ) {
									byte[] oAuth2token = oAuth2Service.generateOauth2Token(user.getUsername(),
											user.getPassword(), clientIdApp, clientSecretApp);

									if (oAuth2token != null) {
										response.put("token", oAuth2token);
									} else {
										response.put("token", null);
									}
								}

								user.setPassword("NA");
								response.put("User", user);
								accountCreated = true;
								user_id = user.getId();
								gatewayId = gateway.getId();

								if (assetModel.isTemp_alert()) {
									response.put("Msg", RegisterUserError.loginWCUltraActivationMessage);
									response.put("display_msg", RegisterUserError.loginWCUltraActivationMessage);
									response.put("wc_ultra", true);
									response.put("wc_ultra_msg", wc_ultra_msg);
								} else
									getMessage(response, monitortypeid);
								response.put("Return Time", System.currentTimeMillis());
								return response;
							} else {
								response.put("Status", 0);
								response.put("Msg", errorMsg);
								response.put("Return Time", System.currentTimeMillis());
								errorResponse = "Getting user Details : " + RegisterUserError.ER045;
								errorCode = "ER045";
								response.put("Return Time", System.currentTimeMillis());
								return response;
							}

						} catch (DataIntegrityViolationException e) {
							gatewayCreationIssue = true;
							log.error("== Exception 1===========" + e.getLocalizedMessage());
							response.put("Status", 0);
							response.put("Return Time", System.currentTimeMillis());
							response.put("Msg", "Pet Name already exists. Please enter valid one. ");
							response.put("display_msg", "Pet Name already exists. Please enter valid one. ");

							errorCode = "ER015";
							errorResponse = RegisterUserError.ER015;
							response.put("Return Time", System.currentTimeMillis());
							return response;
						} catch (Exception e) {
							gatewayCreationIssue = true;
							response.put("Status", 0);
							response.put("Msg", errorMsg);
							response.put("Return Time", System.currentTimeMillis());
							log.error("saveORupdateGateway::::" + e.getLocalizedMessage());
							errorResponse = RegisterUserError.ER016 + "Exception : " + e.getMessage();
							errorCode = "ER016";
							response.put("Return Time", System.currentTimeMillis());
							return response;
						}

					}
				} catch (Exception ex) {
					log.error("Error while getting meid for the respective qrc code. Exception :  " + ex.getMessage());
					response.put("Status", 0);
					response.put("Msg", errorMsg);
					errorCode = "ER019";
					errorResponse = RegisterUserError.ER019 + "Exception : " + ex.getMessage();
					response.put("Return Time", System.currentTimeMillis());
					return response;
				}
			} else {
				String msg = jresponse.getString("Msg");
				response.put("Status", 0);
				response.put("Msg", errorMsg);
				response.put("Return Time", System.currentTimeMillis());
				errorCode = "ER018";
				errorResponse = RegisterUserError.ER018 + "Response Message : - " + msg;
				log.info("Exception : No device found for respective qrccode. Response Message :  -" + msg);
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}

		} catch (Exception e) {
			log.error("Error in registerDevice :: Error : " + e.getLocalizedMessage());

			// To Do Error Response

		} finally {

			if (alertCreationStatus == null) {
				alertCreationStatus = "Alert Created : false" + "<br/><br/>";
			}

			if (loginCreationIssue || gatewayCreationIssue) {
				orderMappedtatus = false;

				if ( registerDevice.isShow_order_id()) {// hide order id
					log.info(
							"Deleted mapping for meid :" + inventory.getMeid() + " User : " + intialUser.getUsername());

					userService.deleteordermap(inventory.getMeid(), initialDeviceStateid);
				}
			}

			if (gatewayCreationIssue && !registerDevice.isAlready_user() ) {

				//
//				orderMapUser = intialUser;
				orderMappedtatus = false;
				// orderMapUser = updateUser(intialUser);

				boolean deleteAllUserInfoStatus = userService.deleteAllUserInfo(intialUser.getId(),
						registerDevice.getEmail());
				log.info("Delete All User Info status : " + deleteAllUserInfoStatus);
			}

			long gateway_id = 0;

			String mailSub = "Failed : External Orders User Activation Status-Email : " + registerDevice.getEmail();

			Offlineuserdetails offlineUser = new Offlineuserdetails();

			offlineUser.setQrccode( registerDevice.getQrc() );
			offlineUser.setName(registerDevice.getFirst_name() + " " + registerDevice.getLast_name());
			offlineUser.setEmail( registerDevice.getEmail() );
			offlineUser.setCompanytype(companyType);
			offlineUser.setAddress("NA");
			if (inventory.getDevicestate() == null) {
				offlineUser.setDevicestateId("NA");
			} else {
				offlineUser.setDevicestateId(Long.toString(inventory.getDevicestate().getId()));
			}
			
			

			if (accountCreated) {

				if (isRecallDevice > 0) {

					async.mapOldGatewayToReturnAC(replacedGateway, returnLoginUsername, registerDevice.getEmail());
					// async.changeMeidAndReplaceInOrderMap( oldMeid,inventory.getMeid() );
					async.saveRecallHistory(recalUserId, oldMeid, inventory.getMeid(), isRecallDevice);
					async.changePetNameByGatewayId(petName, gatewayId);
				}

				if( orderInfo != null )
					async.orderMap(user_id, registerDevice.getEmail(), registerDevice.getMobile_no(), gatewayId, registerDevice.getCountry(), registerDevice.getQrc(), monitortypeid, "auto");
				
				devicecount = gatewayService.getDeviceCount(createuser_id);
				log.info("user device count for userid:" + createuser_id + " ,\n device count: " + devicecount);

				mailSub = "Success : External Orders User Activation Status-Email : " + registerDevice.getEmail();

				if( registerDevice.isDevice_upgrade() && oldGatewayUpgrade != null ) {
					boolean status = userServiceV4.delUserGatewayV2( user_id, oldGatewayUpgrade.getId() );
					log.info(" usergateway deleted status : "+status);
					
					oldMeid = oldGatewayUpgrade.getMeid();
					UpgradeDeviceHistory upgradeDeviceHistory = new UpgradeDeviceHistory(0, user_id, oldMeid, inventory.getMeid(), _helper.getCurrentTimeinUTC(), oldGatewayUpgrade.getQrcode(), registerDevice.getQrc());
					upgradeDeviceHistory = gatewayServiceV4.saveOrUpdateUpgradeDeviceHistory( upgradeDeviceHistory );
					log.info(" upgrade_device_history inserted status : "+ (upgradeDeviceHistory.getId() != 0) );
					
					boolean updatePetProfile = gatewayServiceV4.updatePetProfileGatewayId(oldGatewayUpgrade.getId(), gatewayId);
					log.info("update pet_profile status : "+ updatePetProfile );
					
					boolean updateGatewayProfile = gatewayServiceV4.updateOldGatewayProfile(oldGatewayUpgrade.getId(), gatewayId);
					log.info("update gateway profile status : "+ updateGatewayProfile );
					
					boolean updateOldGatewayName = gatewayService.updateGatewayName( oldGatewayUpgrade.getId() + "" );
					log.info("update updateOldGatewayName status : "+ updateGatewayProfile );
					
					boolean updateGatewayName = gatewayServiceV4.updateGatewayNameByPetProfile( gatewayId );
					log.info("update gateway name status : "+ updateGatewayName );
					
					boolean updateDeviceSub = gatewayServiceV4.updateDevicebasedSubGatewayId(oldGatewayUpgrade.getId(), gatewayId);
					log.info("update device based sub gateway : "+ updateDeviceSub );
					
					Inventory oldInventory = niomDbservice.getInventoryByMeid(oldMeid);
					if (inventory.getSim_vendor().equalsIgnoreCase("verizon")) {
						String deactivationStatus = thinkspace.simDeactivate(oldMeid, cust_name, acc_name, plan, oldInventory.getSim_no(), "", verizonService);
						log.info("verizon old device upgrade deactivation status:"+oldMeid+" : "+ deactivationStatus);
					} else {
						String deactivationStatus = registerUserEmailImpl.eseyeSIMActivation("", oldInventory.getSim_no(), 
								oldMeid, tariffId, "AUS", true);
						log.info("Eseye old device upgrade deactivation status:"+oldMeid+" : "+ deactivationStatus);
					}
					
					boolean deleteRemoveGatewayRequestStatus = gatewayServiceV4.deleteRemoveGatewayRequest( user_id, oldGatewayUpgrade.getId() );
					log.info("Delete remove gateway request status : "+deleteRemoveGatewayRequestStatus);
				

					try {
						ArrayList<Long> alertCfgIds = alertcfgService.getAlertCfgIds(oldGatewayUpgrade.getId());
						boolean alert_status = alertCfgServiceV4.deleteAlertByAssetId( oldGatewayUpgrade.getId() );
						log.info("delete  status of alert :: status : "+ alert_status);
						alert_status = alertcfgService.delAlertcfg(user_id, oldGatewayUpgrade.getId()+"");
						log.info("detele status of alertcfg_to_asset :: status : "+ alert_status);
						alert_status = alertCfgServiceV4.deleteAlertCfgById( alertCfgIds );
						log.info("detele status of alertcfg :: status : "+ alert_status);
					} catch (Exception e) {
						log.error("Error while deleting alert and alert_cfg :: Error : "+e.getLocalizedMessage());
					}
				}
				
				
				String orderId = Long.toString(order.getOrder_id());
				if (! registerDevice.getPurchased_from().equals("rv")) {
					orderId = order.getExternal_order_id();
				}

				offlineUser.setStatus("Success");

				int amount = 0;
				String desc = "";

				String paymentURL = "NA";
				response.put("payment_page_url", paymentURL);
				response.put("show_payment_page", false);
				response.put("payment_page_content", redirectPaymentPageContent);

				String chargebeeId = "NA";
				chargebeeId = userServiceV4.createUserInChargebee( registerDevice.getFirst_name() ,
						registerDevice.getLast_name(), registerDevice.getEmail(), registerDevice.getMobile_no(), registerDevice.getEmail(),
						amount, registerDevice.getOrder_id());				

				response = checkBundleSubscription(response, registerDevice,user_id,chargebeeId,orderid_given,order.getOrder_id(), registerDevice.getEmail(),amount,desc,show_warranty_popup, monitortypeid, orderInfo,gatewayId,monitortypeid);

				cbService.AssignGatewayFeatureForComboPlan( chargebeeId, user_id, gatewayId );

				MappedSubscriptionAddeddevicce(authKey, gatewayId, chargebeeId, monitortypeid);

//				UserRvDetails rvObj = userServiceV4.getUserRvDetails(createuser_id);
//				boolean updatedRVProfile = (rvObj != null) ? true : false;
//
//				boolean userBadgeStat = rvServ.saveUserBadgeTxn(createuser_id, "NA", devicecount, updatedRVProfile,
//						"NA");
//				log.info("in activate user:" + createuser_id + " RVer Badge created:" + userBadgeStat);

			} else {
				offlineUser.setStatus("Failed");
			}

			try {
				// Order Mapping Details
				Timestamp curTime = Timestamp.valueOf(IrisservicesUtil.getCurrentTimeUTC());
				Timestamp orderdate = Timestamp.valueOf(order.getDatetime());
				long userid = (orderMapUser != null ? orderMapUser.getId() : (long) 0L);

				if (accountCreated == false)
					userid = 0L;

				OrderMappingDetails orderMapDetail = new OrderMappingDetails( registerDevice.getFirst_name() ,
						registerDevice.getEmail(), "NA", offlineUser.getDevicestateId(),
						registerDevice.getQrc(), companyType, offlineUser.getStatus(), registerDevice.getOrder_id(), registerDevice.getPurchased_from(), userid,
						curTime, orderdate, order.getExternalsku());
				log.info("orderMapDetail block", orderMapDetail);
				userService.saveOrderMappingDetails(orderMapDetail);
			} catch (Exception e) {
				log.error(" ActivateUser: Order Mapping Details: ", e.getLocalizedMessage());
			}
			boolean is_recharge = false;
			String subs_status = "NA";
			
			try {
				is_recharge = (boolean)response.get("is_recharge");
				subs_status = (String)response.get("subs_status");
				response.remove("is_recharge"); // this is used for internal mail purpose.
				response.remove("subs_status");// this is used for internal mail purpose.
			}catch (Exception e) {
				log.error("checking subscription status:"+e.getLocalizedMessage());
			}
			
			if( bundledOrderFailed ) {
				mailSub = "Failed : Bundle Device Orders User Activation Status-Email : " + registerDevice.getEmail();
			}
			
			String statusEmailContent = new com.nimble.irisservices.helper.EmailContent().activationStatusV5(registerDevice,
					provisionStatus, orderMappedtatus, updateExternalOrderDataStatus, activateSimStatus,
					alertCreationStatus, errorResponse, monitortype, devicecount, isRecallDevice, oldMeid,
					inventory.getMeid(), returnLoginUsername,is_recharge,subs_status);
			
			async.saveorupdateofflineUserDetails(offlineUser);
			
			String skuNumber = getskuNumber(inventory.getMeid());
			String deviceId = inventory.getDeviceId();
			String tariffId = inventory.getTariffId();
			String qrc = inventory.getQrc();

			async.updateRegisterUserEmailStatus(to_address, cc_address, bcc_address, mailSub, statusEmailContent,
					registerDevice.getQrc(), errorCode, accountCreated, inventory.getMeid(), inventory.getSim_no(),
					inventory.getSim_vendor(), isRecallDevice,skuNumber, registerDevice.getEmail(), deviceId, tariffId, qrc);

		
		}

		return response;
	}

	private void getMessage(JResponse response, long monitortypeid) {
		
		switch (monitortypeid + "") {
//		case "0":
//			response.put("Msg", RegisterUserError.loginPageActivationMessage);
//			response.put("display_msg", RegisterUserError.loginPageActivationMessage);
		case "1":
			response.put("Msg", RegisterUserError.loginPageActivationMessage);
			response.put("display_msg", RegisterUserError.loginPageActivationMessage);
			return;
//		case "2":
//			response.put("Msg", RegisterUserError.loginPageActivationMessage);
//			response.put("display_msg", RegisterUserError.loginPageActivationMessage);
		case "3":
			response.put("Msg", RegisterUserError.loginSMActivationMessage);
			response.put("display_msg", RegisterUserError.loginSMActivationMessage);
			return;
		case "4":
			response.put("Msg", RegisterUserError.loginWCActivationMessage);
			response.put("display_msg", RegisterUserError.loginWCActivationMessage);
			return;
		case "5":
			response.put("Msg", RegisterUserError.loginRVActivationMessage);
			response.put("display_msg", RegisterUserError.loginRVActivationMessage);
			return;
		case "6":
			response.put("Msg", RegisterUserError.loginWCProActivationMessage);
			response.put("display_msg", RegisterUserError.loginWCProActivationMessage);
			return;
		case "8":
			response.put("Msg", RegisterUserError.loginRVSActivationMessage);
			response.put("display_msg", RegisterUserError.loginRVSActivationMessage);
			return;
		case "9":
			response.put("Msg", RegisterUserError.loginM10ActivationMessage);
			response.put("display_msg", RegisterUserError.loginM10ActivationMessage);
			return;
			case "12":
				response.put("Msg", RegisterUserError.loginRVMiniSActivationMessage);
				response.put("display_msg", RegisterUserError.loginRVMiniSActivationMessage);
				return;
			
		}
	}

	public JResponse checkGateway(List<JGateway> userGateway, String qrcCode, String petName) {
		JResponse response = new JResponse();
		for (JGateway jGateway : userGateway) {
			if (jGateway.getQrcode().toLowerCase().equals(qrcCode.toLowerCase())) {
				response.put("Status", 0);
				response.put("Msg", RegisterUserError.ER006);
				response.put("errorcode", "ER006");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
			if (jGateway.getName().toLowerCase().equals(petName.toLowerCase())) {
				response.put("Status", 0);
				response.put("Msg", RegisterUserError.petNameUserMessage);
				response.put("errorcode", "ER044");
				response.put("Return Time", System.currentTimeMillis());
				return response;
			}
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;

	}

	public boolean nameCheck(Orders order, JRegisterDevice registerDevice) {
		if (!order.getBilling_first_name().equalsIgnoreCase(registerDevice.getFirst_name().toLowerCase())
				|| !order.getBilling_last_name().equalsIgnoreCase(registerDevice.getLast_name().toLowerCase())) {
			return true;
		} else {
			return false;
		}

	}

	private JResponse userSignUp(JRegisterDevice registerDevice, String userName, String country) {
		boolean loginCreationIssue = false;
		JResponse response = new JResponse();
		String errorResponse;
		String errorCode;
		String errorMsg = "Unable to register the product. Please send email to " + supportContactEmail.get(country)
				+ ".";
		Company company = null;
		try {

			String userEmail = registerDevice.getEmail().trim();
			userEmail = userEmail.toLowerCase();
			userEmail = userEmail.replaceAll("\\s+", "");
			String userPhone = registerDevice.getMobile_no().trim();
			userPhone = userPhone.replace("(", "");
			userPhone = userPhone.replace(")", "");
			// Sign up the new USER
			log.info("==Entered into signup Section===========");
			SignUp signUp = new SignUp();
			signUp.setUsername(userName.toLowerCase());

			if (registerDevice.getPurchased_from().toLowerCase().contains("rv")) {
				signUp.setFirstname(registerDevice.getFirst_name());
				signUp.setLastname(registerDevice.getLast_name());
			}

			String password = registerDevice.getPassword();
			password = _helper.base64Decoder(password);
//			password = registerDevice.getMobile_no();
//		
//			password = password.replaceAll("[^\\d.]", ""); // remove other than numbers
//			if (password.length() > 10) {
//				password = password.substring(password.length() - 10);
//			}

			signUp.setPassword( _helper.bCryptEncoder( password ));
			signUp.setEmail(userEmail);
			signUp.setPhoneno(userPhone);
			signUp.setMobileno(userPhone);
			signUp.setCompanyname( registerDevice.getFirst_name() );
			signUp.setCmptype_id( "3" );
			signUp.setSupervisor("PQA");
			signUp.setThrotsettingsid( "5" );
			long timeMilli = Calendar.getInstance().getTimeInMillis();
			String cmpName = signUp.getCompanyname() + "-" + timeMilli;
			signUp.setCompanyname(cmpName);
			try {
				ThrottlingSettings throtsettings = throttlingService.getThrotSettings(signUp.getThrotsettingsid())
						.get(0);

				CompanyType cmpType = companyTypeServ.getCompanyType(signUp.getCmptype_id(), 1).get(0);

				log.info("Before entering company ");

				company = companyService.createCompany(signUp, throtsettings, cmpType);

				log.info("After entering company ");

			} catch (Exception e) {
				log.info("Error in company creation ");
				response.put("Status", 0);
				response.put("Msg", errorMsg);
				errorCode = "ER008";
				errorResponse = "Signup Error : " + RegisterUserError.ER008 + "Exception  : " + e.getMessage();
				return response;
			}

			CompanyConfig cfg = new CompanyConfig(company);

			// Set temperature unit as celcius
			if (country.equalsIgnoreCase("AU"))
				cfg.setTemperatureunit("C");

			boolean companyConfigCreationStatus = companyService.saveCompanyConfg(cfg);
			log.info("Company Config Creation Status : " + companyConfigCreationStatus);

			if (!companyConfigCreationStatus && company.getId() != 0) {
				companyService.deleteCompany(company.getId());
			}

			Groups group = new Groups();
			group.setName("Default");
			boolean groupCreationStatus = groupservices.saveORupdateGroups(group, company.getId());

			if (!groupCreationStatus & company != null) {
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}

			boolean signUPResponse = userService.signUp(signUp, company);

			User user = userService.getUserByName(signUp.getEmail());
			user.setVerified(true);
			user.setCountry(country);
			userService.updateUser(user);

			log.info("==Signup done into signup Section===========");
			response.put("Status", 1);
			response.put("Msg", "success");

			// update evalidation
			async.updateEvalidation(user.getId(), password);

		} catch (ConstraintViolationException ce) {

			loginCreationIssue = true;

			log.info("signup: ConstraintViolationException:\n" + ce.getStackTrace());
			response.put("Status", 0);
			response.put("Msg", "Email ID / Name already exists. Please try with alternate one.");// RvPet
			errorCode = "ER007";
			errorResponse = RegisterUserError.ER007;

			if (company != null && company.getId() != 0) {
				boolean status = groupservices.deleteGroups(company.getId());
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}

		} catch (Exception e) {
			loginCreationIssue = true;
			log.error("signup:::" + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", errorMsg);
			errorCode = "ER008";
			errorResponse = "Signup Error : " + RegisterUserError.ER008 + "Exception  : " + e.getMessage();
			if (company != null && company.getId() != 0) {
				boolean status = groupservices.deleteGroups(company.getId());
				companyService.deleteCompanyConfigByCMPId(company.getId());
				companyService.deleteCompany(company.getId());
			}
		} // Sigh up the User ends
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}

	
	
	public JCreateGateway createOrUpdateGateway(JRegisterDevice registerDevice, Inventory inventory, int model_id,
			long group_id, String sensor_available, String passwordType, User user, String oldMeid,
			int recallReplaceAction, boolean isAlreadyWarrentyClaimedWC) {

		boolean updateCreaditStatus = false;
		boolean saveGatewayReportStatus = false;
		boolean saveLastGatewayReport = false;
		boolean isGatewayCreated = false;

		JCreateGateway jcreateGateway = new JCreateGateway();
		
		JGateway jgateway = new JGateway();
		jgateway.setName( registerDevice.getPet_name() );
		jgateway.setMeid(inventory.getMeid());
		jgateway.setMdn(inventory.getMdn());
		jgateway.setCarrier("NA");
		jgateway.setModelid(model_id);
		jgateway.setGroupid(group_id);
		jgateway.setEnable(true);
		jgateway.setAlive(false);
		jgateway.setSensorEnable(sensor_available);
		jgateway.setPasswordtype(Long.parseLong(passwordType));
		jgateway.setDescription("");
		jgateway.setQrcode( registerDevice.getQrc().trim() );
		jgateway.setMacid(inventory.getMac_id());
		jgateway.setShowOrderId( registerDevice.isShow_order_id() );
		jgateway.setSensor_type_id( registerDevice.getSensor_type_id() );
		jgateway.setSensor_location_type_id( registerDevice.getSensor_location_type_id() );
		jgateway.setOrder_channel(registerDevice.getOrder_channel());
		jgateway.setSim_vendor(inventory.getSim_vendor());
		jgateway.setIswithoutsub(registerDevice.getOrder_channel() == 5 ? false : registerDevice.isIswithoutsub());
		jgateway.setIsnewverdevice(registerDevice.isIsnewverdevice());

		if( isAlreadyWarrentyClaimedWC )
			jgateway.setShowOrderId( true );
		
		jgateway.setOldMeid(oldMeid);
		jgateway.setRecallReplaceAction(recallReplaceAction);
		jgateway.setFota_version( inventory.getFota_version() );
		jgateway = gatewayService.gatewayExitsinDB(jgateway, user.giveCompany().getId());

		Asset oldAsset = null;
		boolean updateGateway = false;
		if (jgateway.getId() != 0) {
			updateGateway = true;
			oldAsset = gatewayService.getAssetById(jgateway.getId());
		}
		
		Gateway gateway = new Gateway();
		try {
			if (oldMeid.equalsIgnoreCase("NA")) {
				gateway = gatewayService.saveORupdateGateway(jgateway, user.giveCompany().getId());
			} else {
				gateway = gatewayService.saveORupdateRecallGateway(jgateway, user.giveCompany().getId());
				boolean recallGatewaySaved = gatewayService.saveRecallGateway(gateway);
				boolean updatedInLastGatewayReport = gatewayService.changeDefalutRptInLastGateway(gateway.getId(),
						IrisservicesUtil.getCurrentTimeUTC(), 1);
				log.info("updated date saved in last gateway rpt : " + updatedInLastGatewayReport);
			}

			isGatewayCreated = true;
			
			if( gateway == null ) {
				if( updateGateway ) {
					 boolean revertStatus = gatewayService.updateAsset(oldAsset);
					 log.info("old asset revert status : "+revertStatus);
				} else {
					Asset asset = new Asset();
					asset.setAssetaddress( inventory.getMeid() );
					String changedMeid = gatewayService.changeOldAssertDetailsToZ(asset);
					boolean meidChangedInAssert = gatewayService.changeOldAssertDetails(gateway.getId(),changedMeid);
					log.info("meid changed in assert status : "+meidChangedInAssert);
				}	
				isGatewayCreated = false;
				jcreateGateway.setGatewayCreated(false);
			}
		} catch (Exception e1) {
			log.error("Exception in create or update gateway : "+e1.getLocalizedMessage());
			if( updateGateway ) {
				 boolean revertStatus = gatewayService.updateAsset(oldAsset);
				 log.info("old asset revert status : "+revertStatus);
			} else {
				Asset asset = new Asset();
				asset.setAssetaddress( inventory.getMeid() );
				String changedMeid = gatewayService.changeOldAssertDetailsToZ(asset);
				boolean meidChangedInAssert = gatewayService.changeOldAssertDetails(gateway.getId(),changedMeid);
				log.info("meid changed in assert status : "+meidChangedInAssert);
			}
			return null;
		}

		if (jgateway.isUserGatDis()) {
			if (!oldMeid.equalsIgnoreCase("NA")) {

				Object[] gatewayListO = user.getGateways().toArray();
				List<Gateway> gatewayList = new ArrayList<Gateway>();
				for (Object o : gatewayListO) {
					gatewayList.add((Gateway) o);
				}
				user.getGateways().removeAll(gatewayList);

				int index = 0;
				for (Gateway gate : gatewayList) {
					if (gate.getMeid().equalsIgnoreCase(oldMeid)) {
						break;
					}
					index++;
				}
				gatewayList.remove(index);
				gatewayList.add(gateway);
				user.getGateways().addAll(gatewayList);

			} else {
				user.getGateways().add(gateway);
			}

			user.setFirstname( registerDevice.getFirst_name() );
			user.setLastname( registerDevice.getLast_name() );

			userService.updateUser(user);
		}

		try {
			gatewayService.updateGatewayCredit(gateway.getId(), user.giveCompany().getId());
			updateCreaditStatus = true;

		} catch (Exception e) {
			log.error("Gateway credits not updated for Gateway: " + gateway.getId() + "  : " + e.getMessage());
			log.error("Gateway credits not updated for Gateway: " + gateway.getId() + "  : " + e.getLocalizedMessage());
		}
		if(gateway.getModel().getMonitor_type().getId()==1 || gateway.getModel().getMonitor_type().getId()==4) {
			if (jgateway.getId() == 0) {
				try {
					// gatewayService.updateGatewayCredit(gateway.getId(),
					// user.giveCompany().getId());
					// updateCreaditStatus = true;
	
					reportService.saveGatewayReport(gateway, registerDevice.getLat(), registerDevice.getLon());
					saveGatewayReportStatus = true;
	
					reportService.saveLastGatewayReport(gateway, registerDevice.getLat(), registerDevice.getLon());
					saveLastGatewayReport = true;
				} catch (Exception e) {
					log.error("6saveORupdateGateway-Default Reports Canot be Generated::::" + e.getLocalizedMessage());
				}
			} else {
				boolean gatewayIdIsThere = reportService.checkRecordInLastGatewayReport(gateway);
	
				if (!gatewayIdIsThere) {
					reportService.saveLastGatewayReport(gateway, registerDevice.getLat(), registerDevice.getLon());
					saveLastGatewayReport = true;
				} else {
					reportService.updateLastGatewayReport(gateway);
				}
	
			}
		}
		jcreateGateway.setUpdateCreaditStatus(updateCreaditStatus);
		jcreateGateway.setSaveGatewayReportStatus(saveGatewayReportStatus);
		jcreateGateway.setSaveLastGatewayReport(saveLastGatewayReport);
		jcreateGateway.setGatewayCreated(isGatewayCreated);
		jcreateGateway.setJgateway(jgateway);
		jcreateGateway.setGateway(gateway);
		return jcreateGateway;
	}
	
	
	private void sendDynamicMsg(Gateway gateway) {

		String messages = "";
		String deviceModel = gateway.getModel().getModel().toLowerCase();
		String gpsDynamicmsg = "gpsmode=standalone", gpsDynamicmsg2 = "SFWSTORAGE=ON",
				gpsDynamicmsg3 = "reportinterval=900", gpsDynamicmsg4 = "maxsleeptime=900",
				gpsDynamicmsg5 = "modemresetint=3600", gpsDynamicmsg6 = "setqrc=";
		String n12_5Cmd = "fotabytecontrol=3";
		String n12_5Cellular = "aqistate=1";
		String n7Cmd = "batoffset=-0.7,chgoffset=1.9,tempoffset=-1.2,fullchgoffset=-2.5";

		if (!gateway.getQrcode().isEmpty() && !gateway.getQrcode().equalsIgnoreCase("NA"))
			gpsDynamicmsg6 = gpsDynamicmsg6 + gateway.getQrcode().trim();
		else
			gpsDynamicmsg6 = "";
		
		if (deviceModel.contains("n13") || deviceModel.contains("n12")) {
			gpsDynamicmsg3 = "reportinterval=600";
			gpsDynamicmsg4 = "maxsleeptime=600";
		}

		if (deviceModel.contains("nt3d")) {
			messages = gpsDynamicmsg3 + "," + gpsDynamicmsg4 + "," + gpsDynamicmsg2 + "," + gpsDynamicmsg5;
		} else if (deviceModel.contains("nt3f")) {
			messages = gpsDynamicmsg2 + "," + gpsDynamicmsg3 + "," + gpsDynamicmsg4 + "," + gpsDynamicmsg5;			
		} else if (deviceModel.toLowerCase().contains("n13-500") || deviceModel.toLowerCase().contains("n13-503")
				|| deviceModel.toLowerCase().contains("n13g-503") || deviceModel.toLowerCase().contains("n13g-500") ) {
			messages = gpsDynamicmsg + "," + gpsDynamicmsg2 + "," + gpsDynamicmsg3;
		} else if (deviceModel.toLowerCase().contains("n13-502") || deviceModel.toLowerCase().contains("n13g-502") ) {
			messages = gpsDynamicmsg2 + "," + gpsDynamicmsg3;
		}else if (deviceModel.toLowerCase().contains("n12.5g-502-m")) {	// N12.5 wifi		
			messages = gpsDynamicmsg3 + "," + gpsDynamicmsg2 + "," + n12_5Cmd; 
		}else if (deviceModel.toLowerCase().contains("n12.5g-503-m")) {	// N12.5 GPS+AQI
			messages = gpsDynamicmsg+","+gpsDynamicmsg3 + "," + gpsDynamicmsg2 +","+n12_5Cellular; 
		} else if (deviceModel.contains("n1") && !deviceModel.contains("n13")) {
			messages = gpsDynamicmsg + "," + gpsDynamicmsg2; // N7A-503 NT3K N7-504 NT3K
		} else if (deviceModel.contains("n7")) {
			messages = gpsDynamicmsg3 + "," + gpsDynamicmsg4 + "," + gpsDynamicmsg2 + "," + gpsDynamicmsg5;

			if (deviceModel.equalsIgnoreCase("n7-504 nt3g")) {
				messages = messages + "," + n7Cmd;
			}
		}else if (deviceModel.contains("m10")) {
			gpsDynamicmsg3 = "reportinterval=28800"; //8hrs
			messages = gpsDynamicmsg3 + "," + gpsDynamicmsg2 ;			
		}
				
		if (!gpsDynamicmsg6.isEmpty())
			messages = messages + "," + gpsDynamicmsg6;
		
		if(gateway.getModel().getIsgps().equalsIgnoreCase("1") && (deviceModel.toLowerCase().contains("n7")))
			messages = messages + ",gpstrackmode=onetime";

		async.sendDynamicCommand(Long.toString(gateway.getId()), messages, 0L);
	}
	
	private JResponse checkBundleSubscription(JResponse response,JRegisterDevice registerDevice, long user_id, String chargebeeId, boolean orderid_given, long order_id, String userName, int amount, String desc, boolean show_warranty_popup, long monitortype, Orders orderInfo, long gatewayId, long monitortypeid) {
		log.info("Entered into checkBundleSubscription :: chargebee id : "+chargebeeId+" :: user id : "+user_id+" :: order id : "+order_id);
		try {
			response.put( "is_bundle_subs" , false);
			boolean redirectPaymentPage = redirectPaymentPage_config;
			boolean subscriptionBuyNowPopUp = show_warranty_popup ? false : subscriptionBuyNowPopUp_config;
			// Activate CB subscription
			String cbPlan = "NA";
			String recharge_cusid = "NA";
			String reSubId ="NA";
			String email_match ="NA";
			String nextrenewal_at = null;
			String price ="0.0";
			boolean is_email_match = false;
			boolean is_recharge = false;
			ArrayList<String> subDetails = new ArrayList<String>();
			String subs_status = "NA";
			
			String given_order_id = order_id+"";
			if( !orderid_given ) {
				given_order_id = "NA";
			}
			
			if( orderInfo != null ) {
				ProductSubscription productSubscription = cbService.getProductSubscriptionByOrderId( orderInfo.getOrder_id() );
				if( productSubscription != null ) {
					response.put( "is_bundle_subs" , true);
				}
			}
			
			JResponse rechargeResponse = reService.createRechargeSubscription(userName, chargebeeId, user_id, given_order_id,gatewayId,monitortypeid);
			
			if( (boolean) rechargeResponse.get("is_recharge") ) {
				response.put( "is_recharge" , (boolean) rechargeResponse.get("is_recharge"));
				response.put( "subs_status" , (String) rechargeResponse.get("subs_status"));
				return response;
			}
			
			subs_status = "user ver:v2 / product subscription disabled";
			redirectPaymentPage = false;
			
			response.put("subs_status", subs_status);

			response.put("subscription_buynow_popup", subscriptionBuyNowPopUp);
			String paymentURL = "NA ";
			if (redirectPaymentPage) {

				if( chargebeeId.equalsIgnoreCase("NA") ) {
					chargebeeId = userServiceV4.createUserInChargebee( registerDevice.getFirst_name() ,
							registerDevice.getLast_name(), registerDevice.getEmail(), registerDevice.getMobile_no(), registerDevice.getEmail(),
							amount, registerDevice.getOrder_id());	
				}

				if (!chargebeeId.equalsIgnoreCase("NA")) {
					boolean isPaymentSrcAvail = cbService.checkCardDetails(chargebeeId);
					if( !isPaymentSrcAvail ) {
						paymentURL = cbService.generatePaymentURL(chargebeeId);

						if (paymentURL != null) {
							response.put("payment_page_url", paymentURL);
							response.put("show_payment_page", redirectPaymentPage);
							response.put("payment_page_content", redirectPaymentPageContent);
						}
					} 

				}
				
//				if( monitortype != 1 )
					response.put("subscription_buynow_popup", false);

			} else if(chargebeeId.equalsIgnoreCase("NA")) {
				userServiceV4.createUserInChargebee( registerDevice.getFirst_name() ,
						registerDevice.getLast_name(), registerDevice.getEmail(), registerDevice.getMobile_no(), registerDevice.getEmail(),
						amount, registerDevice.getOrder_id());	
			}
			
//			response.put("show_payment_page", false);
//			response.put("subscription_buynow_popup", false);
			
			
		} catch (Exception e) {
			log.error("Error in checkBundleSubscription");
		}
		
		return response;
	}
	
	private String getskuNumber(String imei) {
		if (enableSkuBasedActivation) {
			AssetModel assetModel = gatewayService.getAssetModelByMeid(imei);
			if (assetModel != null && !assetModel.getSkuNumber().equalsIgnoreCase("NA"))
				return assetModel.getSkuNumber();
		}
		return null;
	}
	
	@Override
	public boolean createDefaultPetProfile(JPetprofile jpetprofile) {
		boolean isprofileCreated = false;
		try {
			if (jpetprofile != null) {
				
					int status = 0;
						List<JGateway> gateways = gatewayServiceV4.getGatewayV4(null, null, null, null, 
								jpetprofile.getUser_id(), null);
						for (JGateway jGateway : gateways) {
							if (!jpetprofile.getGateway_id().equals(Long.toString(jGateway.getId()))) {
								if (jpetprofile.getName().equalsIgnoreCase(jGateway.getName()) && jpetprofile.getMonitortype() == jGateway.getMonitorTypeId()) {
									
								}
							}
						}

						if (jpetprofile != null) {
							if (!jpetprofile.getGateway_id().isEmpty() && !jpetprofile.getName().isEmpty() ) {
								status = gatewayServiceV4.saveorupdatePetprofileV6(jpetprofile, jpetprofile.getUser_id(), 
										"1753-01-01", 1l); //default -dog
							} else {
								log.info("saveORupdatePetprofile: Error:mandatory fields are missing");

							}
						}

					log.info("Enter Into Gateway");
					if (status == 1) {							
							long gatewayId = Long.valueOf(jpetprofile.getGateway_id());
							long profileId = jpetprofile.getId();
							if (profileId == 0) { 
								PetProfile petProfile = gatewayService.getPetProfile(gatewayId);
								if( petProfile != null ) {
									profileId = petProfile.getId();
								}
								
								status = gatewayServiceV4.saveOrUpdateGatewayProfile(gatewayId, profileId);
								boolean issuccess = gatewayService.updateGatewayNameByPetId(jpetprofile.getName(),
										profileId);
								log.info("GatewayName updated with respect to petname");
							}
							else {
								status = gatewayServiceV4.saveOrUpdateGatewayProfile(gatewayId, profileId);
								
								boolean issuccess = gatewayService.updateGatewayNameByPetId(jpetprofile.getName(),	profileId);
								
								log.info("GatewayName updated with respect to petname");
							}

							log.info("GatewayName and profileId updated with respect to petname");

					} else {
						
					}
			} else {
				
			}

		} catch (DataIntegrityViolationException e) {
			e.printStackTrace();
			isprofileCreated = false;
			log.error("saveorupdatePetProfile::::" + e.getMessage());
	
		} catch (Exception e) {
			e.printStackTrace();

			isprofileCreated = false;
			log.error("saveorupdatePetProfile::::" + e.getLocalizedMessage());
		}
		log.info("Exit saveorupdatepetprofile");
		return isprofileCreated;
	}

	private void MappedSubscriptionAddeddevicce(String authKey, long gatewayId, String chargebeeId, long monitortypeid) {
		try {
			if (authKey != null && gatewayId > 0) {
				HttpHeaders headers = new HttpHeaders();
				headers.add("auth", authKey);

				String sub_id = cbService.getUnmappedPlaninprodSubscription(chargebeeId, monitortypeid);

				if (sub_id != null) {
					long featureAvil = cbService.getGatewayFeatureBySubId(sub_id);
					if(featureAvil == 0) {
						manageController.updatesubscriptionfeature(headers, sub_id, gatewayId);
					}
				}
			}
		} catch (Exception e) {
			log.error("MappedSubscriptionAddeddevicce ::" + e.getLocalizedMessage());
		}
	}
}
