package com.nimble.irisservices.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IRegisterUserEmail;
import com.nimble.irisservices.entity.RegisterUserEmailStatus;
import com.nimble.irisservices.service.IRegisterUserEmailService;

@Service
public class RegisterUserEmailServiceImpl implements IRegisterUserEmailService {


    @Autowired
    IRegisterUserEmail registerUseremail;

    @Override
    @Transactional
    public void updateRegisterUserEmailStatus(String toAddr, String ccAddr, String bccAddr, String sub, String mailmsg,
											  String qrCode, String errorCode, boolean status, String meid,
											  String iccid, String vendor, int isRecallDevice, String skuNumber,
											  String userEmail, String deviceId, String tariffId, String qrc) {
        registerUseremail.updateRegisterUserEmailStatus(toAddr, ccAddr, bccAddr, sub, mailmsg, qrCode, errorCode,
				status, meid, iccid, vendor, isRecallDevice, skuNumber, userEmail, deviceId, tariffId, qrc);

    }

    @Override
    @Transactional
    public void externalQrcActivationStatus(String qrcode, String errorCode, String toAddr, String ccAddr, String bccAddr, String sub, String mailmsg) {
        registerUseremail.externalQrcActivationStatus(qrcode, errorCode, toAddr, ccAddr, bccAddr, sub, mailmsg);
    }

    @Override
    @Transactional
    public RegisterUserEmailStatus getEmailStatus(String qrCode, String errorCode) {
        return registerUseremail.getEmailStatus(qrCode, errorCode);
    }


}
