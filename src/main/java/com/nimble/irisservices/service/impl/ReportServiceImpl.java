package com.nimble.irisservices.service.impl;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nimble.irisservices.dao.IReportDao;
import com.nimble.irisservices.dto.JAssetDescription;
import com.nimble.irisservices.dto.JAssetLastReport;
import com.nimble.irisservices.dto.JAssetLastReportV4;
import com.nimble.irisservices.dto.JAssetReport;
import com.nimble.irisservices.dto.JDeviceReport;
import com.nimble.irisservices.dto.JNodeReport;
import com.nimble.irisservices.dto.JSensorReport;
import com.nimble.irisservices.dto.JSreiAssetLastReport;
import com.nimble.irisservices.dto.JTempReport;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.Node;
import com.nimble.irisservices.entity.PetFitReport;
import com.nimble.irisservices.exception.InvalidGroupNameException;
import com.nimble.irisservices.service.IReportService;
@Service
@Transactional
public class ReportServiceImpl implements IReportService{

	@Autowired
	IReportDao reportDao;
	
	public List<JAssetLastReport> getLastgatewayreport(String groupid,	String subgroupid, String assetgroupid,
		String assetid,long userid, String offset, 	String limit,String tempunit,String monitortype) {
		return reportDao.getLastGatewayReport(groupid,subgroupid,assetgroupid,assetid,userid,offset,limit,tempunit,monitortype);
	}
	
	public List<JNodeReport> getLastNodereport(String groupid,String subgroupid, String assetgroupid,
		String gatewayid, String nodeid,long userid,String offset, String limit,String tempunit) {
		return  reportDao.getLastNodeReport(groupid,subgroupid,assetgroupid,gatewayid,nodeid,userid,offset,limit,tempunit);
	}
	
	public List<JAssetReport> getgatewayreport(String fromtime, String totime,String assetgroupid, String assetid,
			long cmpid,String offset,String limit,String tempunit) {
		return reportDao.getGatewayReport(fromtime,totime,assetgroupid,assetid,cmpid,offset,limit,tempunit);
	}
	
	public List<JNodeReport> geNodereport(String fromtime, String totime,String assetgroupid,String gatewayid,
		String nodeid,long cmpid,String offset, String limit,String tempunit) {
		return reportDao.getNodeReport(fromtime,totime,assetgroupid,gatewayid,nodeid,cmpid,offset,limit,tempunit);
	}
	
	public JAssetDescription getAssetDescription(long cmpid) {
		return reportDao.getAssetDescription(cmpid);
	}
	
	public List<JSreiAssetLastReport> getSreiLastgatewayreport(String groupid,String subgroupid, String assetgroupid,
		String assetid,long userid, String deliquencyStatus, String levelid, String offset, String limit) {
		return reportDao.getSreiLastGatewayReport(groupid,subgroupid,assetgroupid,assetid,
				userid,deliquencyStatus,levelid,offset,limit);
	}
	
	public List<JSreiAssetLastReport> getSreiLastgatewayreportByCmp(String groupid,	String subgroupid, 
		String assetgroupid,String assetid,long cmpid, 	String deliquencyStatus, String levelid) {
		return reportDao.getSreiLastGatewayReportByCmp(groupid,subgroupid,assetgroupid,assetid,
				cmpid,deliquencyStatus,levelid);
	}
	
	public boolean saveGatewayReport(Gateway gateway, double lat1 , double lon1) {
		return reportDao.saveGatewayReport(gateway,lat1,lon1);
	}
	
	public boolean saveLastGatewayReport(Gateway gateway, double lat1 , double lon1) {
		return reportDao.saveLastGatewayReport(gateway, lat1 , lon1);
	}
	
	public long getgatewayreportCount(String fromtime, String totime,String assetgroupid, String assetid, long cmpid) {
		return reportDao.getgatewayreportCount(fromtime, totime, assetgroupid, assetid, cmpid);
	}
	
	public long getNodereportCount(String fromtime, String totime,String assetgroupid, String gatewayid, String nodeid, long cmpid) {
		return reportDao.getNodereportCount(fromtime, totime, assetgroupid, gatewayid, nodeid, cmpid);
	}
	
	public List<JDeviceReport> getDeviceSummary(String groupname,String subgroupid, String assetgroupid, String assetid,long userid,
			String offset,String limit,String tempunit,long cmp_id, boolean isMobileApp) throws InvalidGroupNameException{
		return reportDao.getDeviceSummary(groupname, subgroupid, assetgroupid, assetid, userid, offset, limit, tempunit, cmp_id, isMobileApp);
	}
	
	public List<JDeviceReport> getDeviceSummaryV2(String groupname,String subgroupid, String assetgroupid,String assetid,
		long userid, String offset,String limit,String tempunit,long cmp_id, boolean isMobileApp) throws InvalidGroupNameException{
		return reportDao.getDeviceSummaryV2(groupname, subgroupid, assetgroupid, assetid, userid, offset, limit, tempunit, cmp_id,  isMobileApp);
	}
	
	public boolean saveNodeReport(Node node) {
		return reportDao.saveNodeReport(node);
	}

	public boolean saveLastNodeReport(Node node) {
		return reportDao.saveLastNodeReport(node);
	}

	@Override	
	public List<PetFitReport> getPetFitReport() {
		return reportDao.getPetFitReport();
	}
	
	
	public List<JAssetLastReportV4> getLastgatewayreportV4(String groupid,
			String subgroupid, String assetgroupid, String assetid,long userid, String offset, 
			String limit,String tempunit) {
		return reportDao.getLastGatewayReportV4(groupid,subgroupid,assetgroupid,assetid,userid,offset,limit,tempunit);
	}
	
	
	public List<JTempReport> getGatewayTempReports(String fromtime, String assetid,	String tempunit, String offset, String limit){
		return reportDao.getGatewayTempReports(fromtime, assetid, tempunit, offset, limit);
	}

	@Override
	public boolean updateLastGatewayReport(Gateway gateway) {
		return reportDao.updateLastGatewayReport(gateway);
	}

	@Override
	public boolean checkRecordInLastGatewayReport(Gateway gateway) {
		return reportDao.checkRecordInLastGatewayReport(gateway);
	}
	
	@Override
	public List<JSensorReport> getGatewaySmartBowlReport(long userid,long gatewayId,String tblName,String fromtime,String totime){
		return reportDao.getGatewaySmartBowlReport(userid,gatewayId,tblName,fromtime, totime);
	}
}
