package com.nimble.irisservices.service.impl;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.ICompanyDao;
import com.nimble.irisservices.dto.SignUp;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.CompanyType;
import com.nimble.irisservices.entity.ThrottlingSettings;
import com.nimble.irisservices.service.ICompanyService;
import com.nimble.irisservices.service.ICompanyTypeService;
import com.nimble.irisservices.service.IThrottlingService;
@Service
public class CompanyServiceImpl implements ICompanyService {
	
	private static final Logger log = LogManager.getLogger(CompanyServiceImpl.class);
	
	@Autowired
	ICompanyDao companyDao;

	@Autowired
	ICompanyTypeService companyTypeServ;

	@Autowired
	IThrottlingService throttlingService;
	
	@Transactional
	public Company getCompanyById(long cmpId) {
		// TODO Auto-generated method stub
		return companyDao.getCompanyById(cmpId);
	}

	@Transactional
	public Company saveCompany(Company cmp) {
		// TODO Auto-generated method stub
		
		return companyDao.saveCompany(cmp);
	}

	@Transactional
	public boolean updateCompany(Company cmp) throws Exception {
		// TODO Auto-generated method stub
		
		return companyDao.updateCompany(cmp);
	}
	
	@Transactional
	public boolean deleteCompany(long cmp_id) {
		// TODO Auto-generated method stub
		return companyDao.deleteCompany(cmp_id);
	}

	@Transactional
	public CompanyConfig getCompanyConfig(long id) {
		// TODO Auto-generated method stub
		return companyDao.getCompanyConfig(id);
	}
	
	@Transactional
	public Company getCompany(long id) {
		// TODO Auto-generated method stub
		return companyDao.getCompany(id);
	}
	
	@Transactional
	public boolean saveCompanyCfg(CompanyConfig cmpcfg) {
		// TODO Auto-generated method stub
		return companyDao.saveCompanyConfg(cmpcfg);
	}
	
	@Transactional
	public boolean updateCompanyCfg(CompanyConfig cmpcfg ,boolean prevRealTimeMonitor) throws Exception {
		// TODO Auto-generated method stub
		return companyDao.updateCompanyConfg(cmpcfg, prevRealTimeMonitor);
	}

	@Transactional
	public List<Company> getCompanyList(long roleid,long cmpid) {
		// TODO Auto-generated method stub
		return companyDao.getCompanyList(roleid,cmpid);
	}

	@Transactional
	public List<CompanyConfig> getCompanyConfigList(long roleid,long cmpid) {
		// TODO Auto-generated method stub
		return companyDao.getCompanyConfigList(roleid,cmpid);
	}
	
	@Transactional
	public ThrottlingSettings getThrottlingSettingsByCompany(long cmpId) {
		return companyDao.getThrottlingSettingsByCompany(cmpId);
	}
	
	@Transactional
	public CompanyConfig getCompanyConfigsForCmpCfgResponse(long id) {
		return companyDao.getCompanyConfigsForCmpCfgResponse(id);
	}
	
	@Transactional
	public boolean getVetCallStaus(long cmpid) {
		return companyDao.getVetCallStaus(cmpid);
	}
	@Transactional
	public boolean saveCompanyConfg(CompanyConfig cmpcfg) {
		return companyDao.saveCompanyConfg(cmpcfg);
	}

	@Override
	@Transactional
	public Company createCompany(SignUp sup,ThrottlingSettings throtsettings,CompanyType cmpType ) {
		log.info("Entered into createCompany :: email : "+sup.getEmail());
		try {
//			ThrottlingSettings throtsettings = throttlingService.getThrotSettings(sup.getThrotsettingsid()).get(0);
//
//			CompanyType cmpType = companyTypeServ.getCompanyType(sup.getCmptype_id(), 1).get(0);
			
			Company cmp = new Company(sup.getCompanyname(), sup.getSupervisor(), sup.getEmail(), sup.getPhoneno(),
					sup.getMobileno(), sup.getAddress(), throtsettings, cmpType);
			
			return companyDao.saveCompany(cmp); 
		} catch (Exception e) {
			log.error("Error in createCompany :: error : "+e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	@Transactional
	public Company getCompanyByEmail(String email) {
		return companyDao.getCompanyByEmail(email);
	}

	@Override
	@Transactional
	public boolean deleteCompanyConfigByCMPId(long cmpid) {
		return companyDao.deleteCompanyConfigByCMPId(cmpid);
	}
	
	@Override
	@Transactional
	public UserV4 getCompanyId(String username) {
		return companyDao.getCompanyId(username);
	}
	
	@Override
	@Transactional
	public boolean updateCmpIdInGateway(long cmp_id, String meid) {
		return companyDao.updateCmpIdInGateway(cmp_id, meid);
	}

	@Override
	@Transactional
	public boolean updateCustomPlan(long cmp_id, boolean customPlanStatus) {
		return companyDao.updateCustomPlan(cmp_id, customPlanStatus);
	}
}
