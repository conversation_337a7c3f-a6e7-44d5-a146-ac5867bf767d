package com.nimble.irisservices.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.ICheckserviceDao;
import com.nimble.irisservices.entity.Testtable;
import com.nimble.irisservices.service.ICheckserviceService;


@Service
public class CheckserviceServiceImpl implements ICheckserviceService {

	@Autowired
	ICheckserviceDao checkserviceDao;
	

	@Override
	@Transactional
	public Testtable testSaveOrUpdatequery(Testtable testtable) {
		return checkserviceDao.DBSaveOrUpdatequery(testtable);
	}

	@Override
	@Transactional
	public Testtable testDeletequery(Testtable testtable) {
		return checkserviceDao.DBDeletequery(testtable);
	}

	@Override
	@Transactional
	public Testtable testselectquery(Testtable testtable) {
		return checkserviceDao.DBselectquery(testtable);
	}


}
