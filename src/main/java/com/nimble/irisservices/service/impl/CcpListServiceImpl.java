package com.nimble.irisservices.service.impl;

import java.util.List;

import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.ICcplistDao;
import com.nimble.irisservices.dto.JCCP;
import com.nimble.irisservices.dto.JCCPReport;
import com.nimble.irisservices.dto.JCCPResponseList;
import com.nimble.irisservices.dto.JCCPSummary;
import com.nimble.irisservices.dto.JCCPTemplateConfig;
import com.nimble.irisservices.dto.JCalibration;
import com.nimble.irisservices.dto.JF5Monitor;
import com.nimble.irisservices.dto.JTemplate;
import com.nimble.irisservices.entity.CCP;
import com.nimble.irisservices.entity.CCPLastReport;
import com.nimble.irisservices.entity.CCPType;
import com.nimble.irisservices.entity.CCP_Checklist;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidTemplateIdException;
import com.nimble.irisservices.exception.InvalidUsernameException;
import com.nimble.irisservices.service.ICcplistService;

@Service
public class CcpListServiceImpl implements ICcplistService {

	@Autowired
	@Lazy
	ICcplistDao ccpdao;

	@Transactional
	public List<CCP_Checklist> getCcpChecklist(String type_id) {
		// TODO Auto-generated method stub
		return ccpdao.getCcpChecklist(type_id);
	}
	
	@Transactional
	public List<CCPType> getCCPType(String type_id) {
		// TODO Auto-generated method stub
		return ccpdao.getCCPType(type_id);
	}
	
	@Transactional
	public List<JCCP> getCcpListByUser(long auth_user,String param_user_id,String type_id) {
		// TODO Auto-generated method stub
		return ccpdao.getCcpListByUser(auth_user,param_user_id,type_id);
	}
	
	@Transactional
	public List<CCP> getCCPById(String id) {
		// TODO Auto-generated method stub
		return ccpdao.getCCPById(id);
	}
	
	@Transactional
	public List<JCCPReport> getCCPReport(String param_user_id,String type_id,String fromtime,String totime,
			String offset, String limit,String template_id,String ccp_summary_id) {
		// TODO Auto-generated method stub
		return ccpdao.getCCPReport(param_user_id,type_id,fromtime,totime, offset, limit,template_id,ccp_summary_id);
	}
	
	@Transactional
	public List<JCCPReport> getCCPLastReport(long userid,String param_user_id,String type_id,String template_id) {
		// TODO Auto-generated method stub
		return ccpdao.getCCPLastReport(userid,param_user_id,type_id,template_id);
	}

	@Transactional
	public void saveCCP(CCP ccp) {
		ccpdao.saveCCP(ccp);
		// TODO Auto-generated method stub
		
	}
	
	@Transactional
	public void saveCCP(User user) {
		ccpdao.saveCCP(user);
		// TODO Auto-generated method stub
		
	}
	
	@Transactional
	public void saveOrUpdateCCP(JCCP jccp) throws InvalidUsernameException,InvalidTemplateIdException{
		ccpdao.saveOrUpdateCCP(jccp);
		// TODO Auto-generated method stub
		
	}
	
	@Transactional
	public boolean deleteCCP(List<CCP> ccp){
		return ccpdao.deleteCCP(ccp);
		// TODO Auto-generated method stub
		
	}
	
	@Transactional
	public List<JCCPResponseList>  saveOrUpdateCCPReport(long userid,List<JCCPReport> jccpreport,
			String startTime,String endTime,String endDateTime,String freqEnable,String startDate) throws InvalidUsernameException,InvalidTemplateIdException{
		return ccpdao.saveOrUpdateCCPReport(userid,jccpreport,startTime,endTime,endDateTime,freqEnable,startDate);
		// TODO Auto-generated method stub
		
	}
	
	@Transactional
	public List<JCCPResponseList> saveOrUpdateCCPList(long userid,List<JCCP> jccplist) throws InvalidUsernameException,InvalidTemplateIdException{
		return ccpdao.saveOrUpdateCCPList(userid, jccplist);
	}
	
	@Transactional
	public List<JCCPResponseList> saveOrUpdateCCPLastReport(long userid,List<JCCPReport> jccpreport) throws InvalidUsernameException,InvalidTemplateIdException{
		return ccpdao.saveOrUpdateCCPLastReport(userid,jccpreport);
		// TODO Auto-generated method stub
		
	}
	
	@Transactional
	public List<CCPLastReport> verifyCCPLastReport(String ccp_id, long user_id) {
		return ccpdao.verifyCCPLastReport(ccp_id, user_id);
		
	}
	
	@Transactional
	public void saveOrUpdateF5Monitor(JF5Monitor monitor) throws InvalidUsernameException  {
		ccpdao.saveOrUpdateF5Monitor(monitor);
		
	}
	
	@Transactional
	public List<JF5Monitor> getF5Monitor(String id, String user_id) {
		return ccpdao.getF5Monitor(id,user_id);
		
	}
	
	@Transactional
	public void sendEmail(String emailids, String subject, String content, String month, String year) {
		ccpdao.sendEmail(emailids, subject, content,month,year);
		
	}

	@Transactional
	public List<JF5Monitor> getF5MonitorByName(String name, String meid ,User user) throws  ConstraintViolationException {
		return ccpdao.getF5MonitorByName(name, meid, user);
		
	}
	
	@Transactional
	public void editTemplate(JTemplate jTemplate) throws InvalidUsernameException,InvalidTemplateIdException{
		
		ccpdao.editUserTemplate(jTemplate);
		// TODO Auto-generated method stub
		
	}
	
	@Transactional
	public List<JCCPTemplateConfig> getCcpTemplatesListByUser(long auth_user,String param_user_id,String template_enable) {
		// TODO Auto-generated method stub
		return ccpdao.getCcpTemplatesListByUser(auth_user,param_user_id,template_enable);
	}
	
	@Transactional
	public List<JCCP> getEnabledCcpListByUser(long auth_user,String param_user_id,String type_id) {
		// TODO Auto-generated method stub
		return ccpdao.getEnabledCcpListByUser(auth_user,param_user_id,type_id);
	}
	
	@Transactional
	public List<JCCPSummary> getCCPSummary(long auth_user,String user_id,String startdate,String startTime,
			String endDate,String endTime) {
		// TODO Auto-generated method stub
		return ccpdao.getCCPSummary(auth_user,user_id,startdate,startTime,endDate,endTime);
	}
	
	@Transactional
	public int saveOrUpdateCCPSummary(JCCPSummary jccpsummary,String freqEnable) throws InvalidTemplateIdException{
		return ccpdao.saveOrUpdateCCPSummary(jccpsummary,freqEnable);
	}
	@Transactional
	public Long getCCPSummaryStatus(String templateId,String date,String startTime,String endTime,int status){
		return ccpdao.getCCPSummaryStatus(templateId, date, startTime, endTime,status);
	}
	@Transactional
	public void saveOrUpdateCCPTemplateConfig(JCCPTemplateConfig jccpTemplateConfig){
		ccpdao.saveOrUpdateCCPTemplateConfig(jccpTemplateConfig);
	}
	@Transactional
	public void saveOrUpdateCalibration(JCalibration jcalibration)throws InvalidUsernameException{
		ccpdao.saveOrUpdateCalibration(jcalibration);
	}
	
	@Transactional
	public List<JCalibration> getCalibration(JCalibration jcalib)throws InvalidUsernameException{
		return ccpdao.getCalibration(jcalib);
	}
	
	@Transactional
	public void initializeTemplateSlots(){
		ccpdao.initializeTemplateSlots();
	}
}
