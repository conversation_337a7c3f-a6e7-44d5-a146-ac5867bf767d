package com.nimble.irisservices.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.ICompanyDaoV4;
import com.nimble.irisservices.dto.CompanyConfigResponse;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.service.ICompanyServiceV4;

@Service
@Transactional
public class CompanyServiceImplV4 implements ICompanyServiceV4{


	@Autowired
	ICompanyDaoV4 companyDaoV4;

	@Override
	public CompanyConfig getCompanyConfigsForCmpCfgResponse(long id) {
		return companyDaoV4.getCompanyConfigsForCmpCfgResponse(id);
	}
	
	@Override
	public boolean updateCompanyCfg(String cmpid, String cmpcfgid, String temperatureunit) {
		return companyDaoV4.updateCompanyCfg(cmpid, cmpcfgid, temperatureunit);
	}

	@Override
	public CompanyConfigResponse getCompanyConfigAndCompany(long cmpid) {
		return companyDaoV4.getCompanyConfigAndCompany(cmpid);
	}

	@Override
	public JResponse getCompanyListByFilter(String sKey, String sValue, String fType, String otype, long offset,
			long limit, String oKey, String cmpid, JResponse response) {
		return companyDaoV4.getCompanyListByFilter(sKey, sValue, fType, otype, offset, limit, oKey, cmpid, response);
	}

	@Override
	public List<CompanyConfig> getCompanyConfigListWeb(long cmp_id) {
		// TODO Auto-generated method stub
		return companyDaoV4.getCompanyConfigListWeb(cmp_id);
	}

	
}
