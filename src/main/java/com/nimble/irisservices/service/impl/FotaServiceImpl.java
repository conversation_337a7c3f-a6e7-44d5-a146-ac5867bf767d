package com.nimble.irisservices.service.impl;

import java.util.List;

import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IFotaDao;
import com.nimble.irisservices.dto.FotaUpgrade;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.FotaModel;
import com.nimble.irisservices.entity.FotaVersion;
import com.nimble.irisservices.entity.LastFotaRpt;
import com.nimble.irisservices.service.IFotaService;

@Service
@Transactional
public class FotaServiceImpl implements IFotaService{

	@Autowired
	@Lazy
	IFotaDao fotaDao;
	
	@Override
	public List<FotaModel> getFotaModelList() {
		return fotaDao.getFotaModelList();
	}
	
	@Override
	public List<FotaVersion> getFotaVersionList() {
		return fotaDao.getFotaVersionList();
	}
	
	@Override
	public boolean createOrUpdateModel(FotaModel fota) throws DataIntegrityViolationException,ConstraintViolationException{
		return fotaDao.createOrUpdateModel(fota);
	}
	
	@Override
	public boolean createOrUpdateVersion(FotaVersion fota) throws DataIntegrityViolationException,ConstraintViolationException{
		return fotaDao.createOrUpdateVersion(fota);
	}
	
	@Override
	public AssetModel getAssetModelById(long modelId) {
		return fotaDao.getAssetModelById(modelId);
	}	
	@Override
	public JResponse getFotaDetails(String sKey, String sValue, String fType, String otype, long offset, long limit, String oKey,long model_id, JResponse response, FotaUpgrade meid) {
		return fotaDao.getFotaDetails(sKey, sValue, fType, otype, offset, limit, oKey, model_id,response, meid);
	}

	@Override
	public LastFotaRpt getLastfotareport(String meid) {
		return fotaDao.getLastfotareport(meid);
	}
	
}
