package com.nimble.irisservices.service.impl;

import java.net.URL;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.nimble.irisservices.dao.IFileStorageDao;
import com.nimble.irisservices.service.IFileStorageService;

@Service
public class FileStorageServiceImpl implements IFileStorageService {	
	
	@Autowired
	IFileStorageDao fileStorageDao;
	
	@Value("${s3.presigned.valid.min}")
	private int s3_presigned_valid_min;
	
	private static final Logger log = LogManager.getLogger(FileStorageServiceImpl.class);
	
	@Transactional
	public boolean uploadImageToS3Bucket(MultipartFile mulfile,String imagePath, String bucketName) {
		// TODO Auto-generated method stub
		return fileStorageDao.uploadImageToS3Bucket(mulfile,imagePath,bucketName);
	}

	@Override
	public String createPresignedGetUrl(String access_key, String secret_key, String bucket_name, String key_path) {
		log.info("Entered into createPresignedGetUrl :: access_key : "+ access_key +" :: secret_key : "+ secret_key +" :: bucketName : "+ bucket_name+" :: keyName : "+ key_path);
    	try {
    		
    		AWSCredentials credentials = new BasicAWSCredentials(access_key, secret_key);
			AmazonS3 s3Client = new AmazonS3Client(credentials);
			s3Client.setS3ClientOptions(new S3ClientOptions().withPathStyleAccess(true));
			s3Client.setRegion(Region.getRegion(Regions.US_WEST_2));
			
			
			Date expiration = new Date();
		    long expTimeMillis = expiration.getTime();
		    expTimeMillis += 1000 * 60 * s3_presigned_valid_min;
		    expiration.setTime(expTimeMillis);

		    // Generate the presigned URL.
		    GeneratePresignedUrlRequest generatePresignedUrlRequest =
		            new GeneratePresignedUrlRequest(bucket_name, key_path)
		                    .withMethod(HttpMethod.GET)
		                    .withExpiration(expiration);

		    URL url = s3Client.generatePresignedUrl(generatePresignedUrlRequest);
		    
		    return url.toString();
        } catch (Exception e) {
        	log.error("Error in createPresignedGetUrl :: Error : "+e.getLocalizedMessage());
		}
    	return "NA";
	}
}
