package com.nimble.irisservices.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.irisservices.dao.IWaggleEmailDao;
import com.nimble.irisservices.dto.EmailData;
import com.nimble.irisservices.service.IWaggleEmailService;

@Service
@Transactional
public class WaggleEmailServiceImpl implements IWaggleEmailService {

	@Autowired 
	IWaggleEmailDao waggleEmailDao;
	
	@Override
	public boolean sendEmailFromWaggle(EmailData emailData) {
		return waggleEmailDao.sendEmailFromWaggle(emailData);
	}

}
