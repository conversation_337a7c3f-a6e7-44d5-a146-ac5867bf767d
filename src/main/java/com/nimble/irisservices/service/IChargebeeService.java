package com.nimble.irisservices.service;

import java.util.Date;
import java.util.List;

import com.chargebee.models.Subscription;
import com.nimble.irisservices.dto.ComboPlanInfo;
import com.nimble.irisservices.dto.JCancelSubDetail;
import com.nimble.irisservices.dto.JPlan;
import com.nimble.irisservices.dto.JProdSubReq;
import com.nimble.irisservices.dto.JProductSubRes;
import com.nimble.irisservices.dto.JProductSubResV2;
import com.nimble.irisservices.dto.JProductSubscription;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.*;

public interface IChargebeeService {

	public Subscription createDefaultSubsPlan(String cb_id);

	public void updateCBCustomerDetails(User user);

	public JResponse getSubscriptionFromDB(UserV4 user, int inapp_redirect);

	public JResponse getIosSubscriptionV2(UserV4 user, int inapp_redirect);

	public List<AllSubscription> getSubscriptionByChargebeeId(String chargebeeid);

	public JPlan getPlanDesc(String planId);

	public Credits getChargebeeUserCredits(String customerId);

	public JResponse getSubscriptionFromCB(UserV4 user, int inapp_redirect, String os, String app_ver, String timezone,
			boolean create_benefit, Long gateway_id);

	public boolean createPaidSubscription(String chargebeeId, JProductSubscription jProductSubscription,
			Date purchasedDate);

	public String generatePaymentURL(String chargebeeId);

	public void cancelCbSubscription(String subscriptionId, String chargebeeId, int count);

	public void deleteCbCustomer(String chargebeeid);

	public boolean checkCardDetails(String chargebeeid);

	public ProductSubscription getProductSubscription(long user_id);

	public boolean checkExistingPaidPlan(String chargebeeId);
	
	public boolean checkCurrentPaidPlan(String chargebeeId, long monitor_type_id);

	public String checkOutURLForBundleSubs(ProductSubscription productSubscription, String chargebeeid);

	public List<UnpaidInvoices> getUnpaidInvoice(String type);

	public String getSubscriptionPeriod(String periodUnit);

	public void saveCurrentPlanDetails(long user_id, String chargebee_id, int plan_id, int period_id,
			String chargebee_plan_id, JResponse response);

	public String createCBSubsForRecharge(String subId, String cbID, String planId, String reSubId,
			String nextrenewal_at, String price);

	public void updateSalesChannel(String user_id, String orderChannel, long gatewayId, String order_date, String order_id, int is_bundle);

	public boolean saveCancelSubscription(JCancelSubDetail csDetail, long userid, String cancel_type,
			String cancelled_at, int refund_amount, double exchange_rate, String curr_code);

	public boolean generateAddiBenefits(UserV4 user, String period, String createdFrom);

	public JResponse getProductSubscriptionFromDB(UserV4 user, String os, String app_ver,
			String timezone, Long monitor_id, Long monitor_id2);

	public AllProductSubscription getProductSubscriptionByGatewayId(long gateway_id, String chargebee_id, long monitor_type);
	
	public JResponse updateproductsub(UserV4 user, JProdSubReq subReq);

	public boolean checkDueInvoice(String cbSubId);
	public JResponse cancelsubplanV5(UserV4 user,JCancelSubDetail jCancelSubDetail,boolean end_term_cancel);
	
	public String getMeariSubCode(long userid,long gatewayid,long period_id,boolean isSolar);
	
	public boolean updateMeariSubStatus(long userid, long gatewayid, long period_id,String type,String keyname);
	
	public boolean saveTemp_sub_purchase( long userid,long gatewayid, long monitortype_id, String  hp_id);
	
	public ProductSubscription getProductSubscriptionByOrderId(long order_id);

	public ProductSubscription saveOrUpdateProductSubscription(ProductSubscription productSubscription);
	
	public boolean saveReturnCancelSubscription(String cbsubid, String reasonType, String reasonDesc, long userid, String cancel_type,
			String cancelled_at, int refund_amount, double exchange_rate, String curr_code);
	
	public JResponse cancelsubplanByreturn(long userId, String meid);
	
	public AllSubscription getSubscriptionByGatewayId(String subscrp_id);
	
	public AllProductSubscription getProductSubscriptionBySubId(String subscrp_id);
	
	public boolean saveCancelSubHistory( CancelsubHistory canSub);
	
	public String getSubscriptionByChargebeeIdNotinProductsub(String chargebeeid);
	
	public String getProductSubscriptionStatus(long gateway_id, String chargebee_id);
	
	public String getPlanVersionbyplanname(String planid);
	
	public String generatePaymentURLForProductsub(ProductSubscription productSubscription, String chargebeeid, long userId, long gatewayId, long monitorType);
	
	public String createCBSubsForRechargePlanv3(String subId, String cbID, String planId, String reSubId,
			String nextrenewal_at, String price);
	
	public String getProductSubscriptionByChargebee(String chargebee_id);

	public AllProductSubscription getProductSubscriptionByGateway(long id, String chargebeeid);

	public JProductSubRes getProductSubscriptionFromDBV1(UserV4 user, String os, String app_ver,
			String timezone, Long monitor_id, Long monitor_id2);
	
	public JProductSubResV2 getProductSubscriptionplanV2FromDB(UserV4 user, String os, String app_ver,
			String timezone, Long monitor_id, Long monitor_id2);
	public boolean updategatewaybysubcriptionId(long gatewayId,String subId);

	public boolean isVetPlanAvailable(String chargebeeid,long monitortype);
	
	public String getVetPlanSub(String chargebeeid,long monitortype);

	public long getCurrentVetPlanid(String chargebeeid,long monitortype);

	List<AllProductSubscription> getproductSubscriptions(String chargebeeid, long gateway_id);

	void createVetChatSubscription(String userName, String couponCode);

    public ComboPlanInfo checkActiveComboPlan(String chargebeeId);

    public void AssignGatewayFeatureForComboPlan(String chargebeeId, long userId, long gatewayId);

	public String getUnmappedPlaninprodSubscription(String chargebeeid, long monitorType);

	public long getGatewayFeatureBySubId(String subId);

	public List<ManageList> getManageList();

	boolean getVetchatSetUpActivate(String email);
}
