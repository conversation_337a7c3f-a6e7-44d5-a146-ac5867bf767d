package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.SignUp;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.CompanyConfig;
import com.nimble.irisservices.entity.CompanyType;
import com.nimble.irisservices.entity.ThrottlingSettings;


public interface ICompanyService {
	public Company getCompanyById(long cmpId);

	public Company saveCompany(Company cmp);

	public boolean updateCompany(Company cmp) throws Exception;

	public boolean deleteCompany(long cmp_id);

	public CompanyConfig getCompanyConfig(long id);

	public Company getCompany(long id);

	public boolean saveCompanyCfg(CompanyConfig cmpcfg);

	public boolean updateCompanyCfg(CompanyConfig cmpcfg ,boolean prevRealTimeMonitor) throws Exception;
	
	public List<Company> getCompanyList(long roleid,long cmpid);
	
	public List<CompanyConfig> getCompanyConfigList(long roleid,long cmpid);
	
	public ThrottlingSettings getThrottlingSettingsByCompany(long cmpId);

	public CompanyConfig getCompanyConfigsForCmpCfgResponse(long cmp_id);
	
	public boolean getVetCallStaus(long cmpid);
	
	public boolean saveCompanyConfg(CompanyConfig cmpcfg);

	public Company createCompany(SignUp signUp,ThrottlingSettings throtsettings,CompanyType cmpType );

	public Company getCompanyByEmail(String email);

	public boolean deleteCompanyConfigByCMPId(long id);
	
	public UserV4 getCompanyId(String username);

	public boolean updateCmpIdInGateway(long cmp_id, String gid);

	public boolean updateCustomPlan(long cmp_id, boolean customPlanStatus);
}
