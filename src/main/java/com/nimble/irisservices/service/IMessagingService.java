package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.JSentMessage;
import com.nimble.irisservices.exception.InvalidGatewayIdException;

public interface IMessagingService {

	
	public boolean sendMessage(String assetid, String msg, long cmpId) throws  InvalidGatewayIdException;
	
	public boolean sendLiveTrackMessage(String assetid, String msg, long cmpId) throws  InvalidGatewayIdException;
	
	
	public boolean saveMessage(String assetid, String msg, long cmpId);
	public boolean saveFotaMessage(long gatewayid, String msg);
	public void sendEmail(String alert_email,String mailid, String subject, String content,boolean rvMailId);
	public void sendEmailV2(String alert_email,String mailid, String subject, String content,boolean rvMailId);
	public String formMessage(String assetid, String message,long cmpId) throws  InvalidGatewayIdException;
	public List<JSentMessage> sentMessages(String groupid, String subgroupid,
			String gatewayid, long cmpId);
	
	public boolean savePlivoData(String phoneno, String msg,String cmpid, String cmpname,String appname,String type);
	
	public boolean savePlivoVoiceData(String phoneno, String msg,String cmpid, String cmpname,String appname,
					String type,String ip);

	public boolean saveMessageV2(String assetid, String msg,long seqno);
	
	public String getMessageIp();

	boolean isDynamicCmdInserted(Long gatewayId, String fotacommand);
}
