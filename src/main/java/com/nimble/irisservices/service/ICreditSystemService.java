package com.nimble.irisservices.service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.nimble.irisservices.dto.*;
import com.nimble.irisservices.entity.*;

public interface ICreditSystemService {
	
	public boolean createSubscriptionPlan(SubscriptionPlan plan) throws Exception;
	public boolean deleteSubscriptionPlan(long id) throws Exception;
	public SubscriptionPlan getSubsPlanById(long id);
	public List<SubscriptionPlan> listSubscriptionPlan() throws Exception;

	
	public boolean createPlanToMonitorType(PlanToMonitorType plan) throws Exception;
	public boolean deletePlanToMonitorType(long id) throws Exception;
	public PlanToMonitorType viewPlanToMonitorType(long id) throws Exception;	
	public List<PlanToMonitorType> listPlanToMonitorType();
	
	public boolean createPlanToPeriod(PlanToPeriod plan) throws Exception;
	public boolean deletePlanToPeriod(long id) throws Exception;
	public PlanToPeriod viewPlanToPeriod(long id) throws Exception;	
	public List<PlanToPeriod> listPlanToPeriod() throws Exception;
	public int getPlanToPeriodByName(String chargebee_planid);
	public int getPlanFreeTrialPeriodByName(String chargebee_planid);
	public SubscriptionPeriod getSubsPeriodById(long id);	
	public List<SubscriptionPeriod> listSubPeriod() throws Exception;
	
	public boolean createPlanToUpgrade(PlanToUpgrade plan) throws Exception;
	public boolean deletePlanToUpgrade(long id) throws Exception;
	public List<PlanToUpgrade> listPlanToUpgrade() throws Exception;
		
	public Map<String,Object> getAvailUpgradePlans(long planid, long periodid,long userid,String country);
	public Map<String, Object> getIosAvailUpgradePlan(long planid, long periodid,long userid,long monitortype,String country);
	public Map<String, Object> getAvailUpgradePlanNew(long planid, long periodid,long userid,long monitortype, String country);
	public Map<String, Object> getAvailUpgradePlanV4(long planid, long periodid,long userid,long monitortype, String country);
	public ArrayList<Integer> getPlanAndPeriod(String chargebee_planid);
	public String[] getChargebeePlanById(long planid, long periodid, String country_code);
	public ArrayList<String> getDeviceConfig(long planid);
	public int getDeviceConfigV4(long userid,long planid);
	public Map<String,Object> getplanlist(String qry,String country);
	public long getDeviceCountByUser(long userid, long monitor_id) ;
	public List<Object> getDeviceCountByMonitorType(long userid, long monitortype);
	public LinkedHashMap<String, Gateway> getGatewaysByReportTime(long userid, int monitortype);
	
	public ResetType viewResetType(long id) throws Exception;
	public List<ResetType> listResetType() throws Exception;
	
	public FeatureType viewFeatureType(long id) throws Exception;
	public List<FeatureType> listFeatureType() throws Exception;
	
	public boolean createFeature(Feature feature) throws Exception;
	public boolean deleteFeature(long id) throws Exception;
	public Feature viewFeature(long id) throws Exception;
	public Feature viewFeatureByname(String name);
	public long getFeatureId(String fName);
	public List<Feature> listFeature() throws Exception;

	public boolean createUsertoFeature(UsertoFeature plan) throws Exception;
	public boolean deleteUsertoFeature(long id) throws Exception;
	public List<UsertoFeature> listUsertoFeature(long user_id) throws Exception;
	
	public boolean createPlantoFeature(PlanToFeature plan) throws Exception;
	public boolean deletePlantoFeature(long id) throws Exception;
	public List<PlanToFeature> listPlantoFeature(long planid) throws Exception;
	public PlanToFeature getPFByPlanFeature(long planid, long featureid) ;
	public UsertoFeature getUFByUserFeature(long userid, long featureid) ;
	public ArrayList<String> getFeatureList(long userid, long plan_id);
	public int getUserFeatureAvailabilty(long userid, String fName,long plan_id); 
	public ArrayList<Integer> getVPMAvailabilty(long userid, String fName,long plan_id,String cb_planid);
	
	public boolean updateUserTransaction(UserTransaction uTxn) throws Exception;
	public UserTransaction getUserTransactionByUserFeature(long user_id, String feature);
//	public List<UserTransaction> getUserTransactionByUser(long user_id);
	
	public boolean updateUserTransactionHistory(UserTransactionHistory uTxnHistory) throws Exception;
	public UserTransactionHistory getUserTransactionHistoryByUserFeature(long user_id, String feature,String date);
//	public List<UserTransactionHistory> getUserTransactionHistoryByUser(long user_id);
	
	public CompanyCreditMonitor getCompanyCreditMonitorByCmpy(long cmp_id);
	public boolean upateCredits(CompanyCreditMonitor ccm)throws Exception;
	
	public boolean upadteTransactionSummary(CompanyTransactionSummary cts)throws Exception;
	
	public boolean saveOrUpdateUserTxn(long user_id,long feature_id);
	
	public boolean saveOrUpdateUserTxnHistory(long user_id,long feature_id);
	
	public float getIosPlanPrice(long planid, long periodid);
	
	public VersionMapping getVersionMapping(String app_version, String os_type);
	
	public JSubscriptionPlanReport getInappSubscriptionByUser(long userid);
	
	public List<JVPMPlan> getVPMPlanList(String plantype);
	
	public int getVPMPlanTxnCount(String cb_planid);
	
	public List<PlanToPeriod> listPlanToPeriodByPlanId(long plan_id);
	
	public String[] getCouponId(String cbPlanid);
	
	public JResponse upgradePlanList(long userid, long plan_id, long period_id,boolean freetrial, String country);
	
	public String getPlanVersion(String cb_planid);
	
	public ArrayList<JFeatureCredit> getSettingFeatures(long user_id);
	
	public List<JAddonPlan> getAddonPlanList(long plan_id, long period_id);
	
	public ArrayList<JAlertRemaining> getalertslimit(long user_id, String alertlimit_basedon);
	
	public boolean saveOrUpdateUserFeatureCount(long user_id,long feature_id);
	
	public List<JGatewaySubSetup> checkDeviceConfigStatusV2(long planid, long userid, int days_remaining) ;
	
	public JProductSubscription getCBPlan(String order_id);
	
	public boolean updateSubStatus(String order_id,String user_id);
	
	public boolean updateReSubStatus(String order_id,String cb_sub_status);
	
	public JResponse getalertslimitV2(long user_id, String alertlimit_basedon,boolean enable_appnotify, String reqVer);
	
	public JResponse getplanoffers(String plan_ver,long planid,long periodid, String country);
	
	public String[] getOfferStatus(String chargebee_planid);
	
	public boolean updateUserIdInProsuctSubs(String valueOf, long id);
	
	public String getNextRenewalDate(long user_id);
	
	public JResponse getalertslimitV5(long id, String alertlimit_basedon, boolean enable_appnotify, String reqVer,
			boolean smsEnable, boolean emailEnable, boolean appNotifyEnable, long gatewayId);
	
	public JResponse upgradePlanList_v5(UserV4 user, long planid, long period, boolean freetrial, String country, String type);
	
	public int getMaxDeviceCount(long planid);
	
	public LinkedList<String> listPlanBenefits(long periodid);
	
	public boolean checkAdditionalBenifits(String user_name);
	
	public boolean checkAdditionalBenifitsCreated(String user_name,int periodId);

	public boolean insertAddiUser(String username);
	
	public JResponse generateSubCoupon(UserV4 user, String period, String due_date);
	
	public JPlanToUpgrade getPlanToUpgrade(int upgrade_plan_to_period_id, int cur_plan_to_period_id);
	
	public JPlanInfo getPlan(long curplan_id);
	
	public UserRetained getUserRetainedById(long id);
	
	public UserRetained getUserRetainedByUserId(long user_id);
	
	public UserRetained saveOrUpdateUserRetained(UserRetained userRetained);
	
	public boolean checkRenewalDateInUserRetained(long user_id);
	
	public boolean cancelRechargeSubscription(String username);
	
	public String getRechargeSubscriptionId(String username);

	public PlanMigration getCurrentValidPlan(long planid, long periodid);
	
	public long findaqiqndcoenabledevice(long user_id);
	
	public JResponse getupgradesubplansV5(UserV4 user, long curplan_id, long curperiod_id,long monitor_type,String country,
			boolean defaultFreePlan,long gatewayid, String type);

	public JGatewayFeature getGatewayFeatureById(Long gateway_id);
	
	public List<JGatewayFeature> getGatewayFeaturesByPlanId(int plan_id, String sub_id);
	
	public boolean assignGatewayFeature(UserV4 user, JSubManage jSubDetail);

	boolean assignGatewayFeatureForPetMonitor(UserV4 user, JSubManage jSubDetail,
											  String eventType, boolean b);

	public String getMeariKey(long userID, long gatewayId, int iris_speriod);
	
	public boolean isUpgradeAvailable(int plan_id, int period_id);

	public long getMonitorTypeByCBPlan(String subs_planid);
	
	public boolean updateSubsStatus(String status, String subId);

	public CancelCustomerRetain getCancelCustomerRetain(long user_id);
	
	public CancelCustomerRetain saveOrUpdateCancelCustomerRetain(CancelCustomerRetain cancelCustomerRetain);
	
	public long getplanByCountryCode(long planid, String Country, long monitortype_id);
	
	public boolean saveOrUpdatePauseHistory(String cbsubid, long userId, String date, int status, String feedbackid, String review);
	
	public JPauseHistory getPauseHistoryRecord(String cbsubid);
	
	public ArrayList<JFeatureCredit> getSettingGatewayFeatures(long gateway_id);
	
	public long findaqiqndcoenabledevicebyGateway(long gateway_id);

	public JResponse getalertslimitV5ByGateway(long id, String alertlimit_basedon, boolean enable_appnotify, String reqVer,
			boolean smsEnable, boolean emailEnable, boolean appNotifyEnable, long gatewayId);
	
	public int getDeviceConfigV4Gateway(long gatewayid,long planid);
	
	public JPauseHistory getPauseHistoryRecordAllCBS(String cbsubid);
	
	public JGatewayFeature getGatewayFeatureByIdWithoutactive(Long gateway_id);
	
	public boolean getMeariPlanExpired(long userID, long gatewayId, int iris_speriod);
	
	public JResponse getUpgradeSubPlanV6(UserV4 user, long curplan_id, long curperiod_id, long monitor_type, String country,
								  boolean defaultFreePlan, long gatewayid, String type, String plantype, String sub_id, boolean restrict_flexi_plan_period, boolean is_flexi);

	public FlexiPlanHistory getFlexiplandetailsbySubid(String sub_id);

	public boolean saveorupdateflexiplanhistory(FlexiPlanHistory flexiPlan);

    public JProductSubResV2 getFlexiPlanHistory(Long gatewayId, String subscriptionId);
    
    public ArrayList<JVetPlanDetails>  getVetPlanTerms(String plan_type);
    
    public JResponse getUpgradeSubPlanV7(UserV4 user, long curplan_id, long curperiod_id, long monitor_type, String country,
    		boolean defaultFreePlan, long gatewayid, String type, String plantype, String sub_id, boolean restrict_flexi_plan_period,
    		boolean is_flexi);
    
    String getCurrentSubStatusForChargebeeUser(String chargeBeeId);
    
    public String getSubscriptionStatus(String subId);

	public JResponse getUpgradeSubPlanV8(UserV4 user, long curplan_id, long curperiod_id, long monitor_type, String country,
		boolean defaultFreePlan, long gatewayid, String type, String plantype, String sub_id, boolean restrict_flexi_plan_period,
		boolean is_flexi);
	
	public boolean checkComboExists(String cbid);

	public boolean checkComboExistsinallchargebee(String cbid);

	List<GatewaytoFeature> listGatewaytoFeature(long gatewayId);

	boolean createGatewaytoFeature(GatewaytoFeature gatewayfeature);

	public GatewaytoFeature getGFByGatewayFeature(long gatewayId, long featureid);

	public String getplantypebychargebeeid(String planId);

    boolean deleteGatewaytoFeature(long id);

	List<Feature> listFeatureByGatewayId(int gatewayId);

	public JResponse getUpgradeSubPlanV9(UserV4 user, long curplan_id, long curperiod_id, long monitor_type, String country,
										 boolean defaultFreePlan, long gatewayid, String type, String plantype, String sub_id, boolean restrict_flexi_plan_period,
										 boolean is_flexi);

	JResponse getUpgradeSubPlanV10(UserV4 user, long curplan_id, long curperiod_id, long monitor_type, String country,
										 boolean defaultFreePlan, long gatewayid, String type, String plantype, String sub_id, boolean restrict_flexi_plan_period,
										 boolean is_flexi);
}
