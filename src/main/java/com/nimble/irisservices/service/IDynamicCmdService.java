package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.JSentMessage;
import com.nimble.irisservices.entity.Gateway;

public interface IDynamicCmdService {
	
	public boolean saveDynamicCmd(Gateway gateway, String msg, int transportType,String status);

	public List<JSentMessage> sentMessages(String groupid, String subgroupid,
			String gatewayid, long cmpid);

	public boolean savePlivoData(String phoneno, String msg,String cmpid, String cmpname,String appname,String type,
			int transport_type);
	
	public boolean delDynamicCmd(long userid,String assetid);
	
	public boolean saveDynamicCmdV2(Gateway gateway, String msg, int transportType,String status,long seqno);
	
	public boolean insertDynamicCmd(Gateway gateway, String msg,int transportType,String status);
	
	public boolean checkVaildUpgradePacket(long gatewayId, String upgVer);

	public boolean saveDynamicCmdCalib(Gateway gateway, String message, int i, String string, long l);

	public boolean isDynamicCmdInserted(Long gatewayId, String fotacommand);
}
