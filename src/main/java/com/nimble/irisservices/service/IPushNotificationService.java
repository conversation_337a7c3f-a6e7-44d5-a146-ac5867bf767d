package com.nimble.irisservices.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.springframework.dao.DataIntegrityViolationException;

import com.nimble.irisservices.dto.JPushNotifications;
import com.nimble.irisservices.entity.NotificationType;
import com.nimble.irisservices.entity.PushNotificationStatus;
import com.nimble.irisservices.entity.PushNotifications;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.WagglePushNotification;

public interface IPushNotificationService {
	
    public boolean savePushNotificaitons(JPushNotifications pushNotification ) throws DataIntegrityViolationException;
    
    public boolean updatePushNotifications(PushNotifications pushNotification ) throws DataIntegrityViolationException;
    
    public PushNotifications getNotification(String id );
    
    public PushNotificationStatus getUserNotificationStatus(String userid, String notificationID);
	
    public boolean deleteNotification(long id);
    
    public boolean deleteUserNotification(String userId, String notificationId);

    public List<PushNotifications> getNotifications(String status); 
    
    public NotificationType getNotificationType(long id);
    
    public Set<User> getUsersNotifications(String notificationId);
    
    public List<PushNotifications> userNotifications(String userId,String status);

    public boolean updatePushNotificationStatus(PushNotificationStatus pushNotificationStatus);
    
//	public boolean saveOrUpdateApiDetails(Apidetails apiDetails ) throws DataIntegrityViolationException;
//	
//	public List<Apidetails> getApi(Apidetails apiDetails); 
//	
//	public boolean deleteApi(Apidetails apiDetails);
	
	public boolean updatePushNotificationsForUsers(PushNotifications pushNotification, Set<User> users) throws DataIntegrityViolationException;
	
	public boolean updateUserPushNotifications(PushNotifications pushNotification, Set<Long> users) throws DataIntegrityViolationException;
	
	public PushNotifications getPushNotificationById(String id);

	public boolean updateWagglePushNotification(ArrayList<WagglePushNotification> totalobject);

	public boolean deletePushNotificationStatus(String pushNotificationStatusId);

}
