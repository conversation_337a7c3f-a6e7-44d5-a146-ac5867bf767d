package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.JNode;
import com.nimble.irisservices.dto.JNodeOverview;
import com.nimble.irisservices.entity.Node;
import com.nimble.irisservices.exception.InvalidAsseIdException;
import com.nimble.irisservices.exception.InvalidAssetGroupIdException;
import com.nimble.irisservices.exception.InvalidGatewayIdException;
import com.nimble.irisservices.exception.InvalidModelIdException;

public interface INodeService {

	List<JNode> getNode(String assetgroupid, String groupid, String subgroupid,
			String gatewayid, String nodeid, long id);

	JNodeOverview getNodeoverview(String groupid, String subgroupid,long userid);

	Node saveORupdateNode(JNode node, long l, long userId) throws InvalidAssetGroupIdException, InvalidGatewayIdException, InvalidModelIdException, InvalidAsseIdException;

}
