package com.nimble.irisservices.service;

import java.util.List;

import org.hibernate.exception.ConstraintViolationException;

import com.nimble.irisservices.dto.JCCP;
import com.nimble.irisservices.dto.JCCPReport;
import com.nimble.irisservices.dto.JCCPResponseList;
import com.nimble.irisservices.dto.JCCPSummary;
import com.nimble.irisservices.dto.JCCPTemplateConfig;
import com.nimble.irisservices.dto.JCalibration;
import com.nimble.irisservices.dto.JF5Monitor;
import com.nimble.irisservices.dto.JTemplate;
import com.nimble.irisservices.entity.CCP;
import com.nimble.irisservices.entity.CCPLastReport;
import com.nimble.irisservices.entity.CCPType;
import com.nimble.irisservices.entity.CCP_Checklist;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.exception.InvalidTemplateIdException;
import com.nimble.irisservices.exception.InvalidUsernameException;

public interface ICcplistService {

	List<CCP_Checklist> getCcpChecklist(String type_id);
	
	List<CCPType> getCCPType(String type_id);
	
	List<JCCP> getCcpListByUser(long auth_user,String param_user_id,String type_id);
	
	List<CCP> getCCPById(String id);
	
	List<JCCPReport> getCCPReport(String param_user_id,String type_id,String fromtime, String totime, String offset, String limit,String template_id,String ccp_summary_id);
	
	List<JCCPReport> getCCPLastReport(long userid, String param_user_id,String type_id,String template_id);
	
	void saveOrUpdateCCP(JCCP jccp) throws InvalidUsernameException,InvalidTemplateIdException;
	
	boolean deleteCCP(List<CCP> ccp);
	
	List<JCCPResponseList> saveOrUpdateCCPReport(long userid,List<JCCPReport> jccpreport,
			String startTime,String endTime,String endDateTime,String freqEnable,String startDate) throws InvalidUsernameException,InvalidTemplateIdException;
	
	List<JCCPResponseList> saveOrUpdateCCPList(long userid,List<JCCP> jccplist) throws InvalidUsernameException,InvalidTemplateIdException;
	
	List<JCCPResponseList> saveOrUpdateCCPLastReport(long userid,List<JCCPReport> jccpreport) throws InvalidUsernameException,InvalidTemplateIdException;
	
	void saveCCP(CCP ccp);
	
	void saveOrUpdateF5Monitor(JF5Monitor monitor) throws InvalidUsernameException;
	
	List<JF5Monitor> getF5Monitor(String id,String user_id);
	
	List<JF5Monitor> getF5MonitorByName(String name, String meid, User user) throws  ConstraintViolationException;
	
	void saveCCP(User user);
	
	List<CCPLastReport> verifyCCPLastReport(String ccp_id, long userid);
	
	void sendEmail(String emailids, String subject, String content, String month, String year);
	
	void editTemplate(JTemplate jTemplate) throws InvalidUsernameException,InvalidTemplateIdException;
	
	List<JCCPTemplateConfig> getCcpTemplatesListByUser(long auth_user,String param_user_id,String template_enable);
	
	List<JCCP> getEnabledCcpListByUser(long auth_user,String param_user_id,String type_id);
	
	List<JCCPSummary> getCCPSummary(long auth_user,String user_id,String startdate,String startTime,
			String endDate,String endTime);
	
	Long getCCPSummaryStatus(String templateId,String date,String startTime,String endTime,int status);
	
	int saveOrUpdateCCPSummary(JCCPSummary jccpsummary,String freqEnable) throws InvalidTemplateIdException;
	
	void saveOrUpdateCCPTemplateConfig(JCCPTemplateConfig jccpTemplateConfig);
	
	void saveOrUpdateCalibration(JCalibration jcalibration)throws InvalidUsernameException;
	
	List<JCalibration> getCalibration(JCalibration jcalib)throws InvalidUsernameException;
	void initializeTemplateSlots();
}
