package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.JGatewayModeDetails;
import com.nimble.irisservices.dto.JPowerModeConfig;
import com.nimble.irisservices.entity.GatewayPowerMode;

public interface IPowerModeService {
	
	public JGatewayModeDetails getGatewayModeDetails(long gatewayId);
	
	public List<JPowerModeConfig> getPowerModes(String powerModes);
	
	boolean updatePowerModeStatus(long gatewayId, int currMode, int prevMode, String modeStatus);
	
	List<GatewayPowerMode> getPowerModesForGateways(long userId, long gatewayIds, String os);
}
