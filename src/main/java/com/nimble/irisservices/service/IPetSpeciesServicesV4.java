package com.nimble.irisservices.service;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;

import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.PetBreeds;
import com.nimble.irisservices.entity.PetFeedDetails;
import com.nimble.irisservices.entity.PetFood;
import com.nimble.irisservices.entity.PetFoodPerDay;
import com.nimble.irisservices.entity.PetSpecies;

public interface IPetSpeciesServicesV4 {

	public List<PetBreeds> getPetBreedsV4(String speciename);

	public List<PetSpecies> getPetSpecies();

	public List<PetFood> getPetFood();
	
	public List<PetFood> getPetFoodByUserToo(long user_id,boolean isPaid,long foodId);

	public List<PetFoodPerDay> getPetFoodPerDay();

	public PetFood saveOrUpdatePetFood(PetFood petFood) throws SQLIntegrityConstraintViolationException;

	public PetFeedDetails saveOrUpdatePetFeedDetails(PetFeedDetails petFeedDetails);

	public PetFeedDetails getPetFeedDetails(long gateway_id, long user_id);

	public boolean updatePetFeedRemainder(PetFeedDetails petFeedDetails, UserV4 user);
	
	public double getSignalment(double speciesId, boolean isIntact);

	public double getBCS(String bodyCondition);
	
	public PetFood getPetFoodById(long id);
	
	public void updateReqCalories( long gatewayid, double req_calories);
	
	public PetFood getPetFoodByName(String name, long userId);

	public void updateReqCaloriesById(long petProfileid, double req_calories);

	public void updateReqWeightById(long profileid, double req_weight);

	public void updateReqWeightByProfId(long petProfileid, double req_calories);
	
	public double getActivityLevel(String activityLevel);
	
	public String getFindActivitylevel(long actCode);
	
	public long getFindActivitylevelVal(String actVal);
	
	public String getTimezoneconvertDate(String date,String timezone);

	public boolean findDevicepaidplanornot(long gateway_id);
	
	public long getPetFoodFromFeeddetails(long gateway_id, long user_id);
	
	public boolean findDeviceFreeplanornot(long gateway_id);
}
