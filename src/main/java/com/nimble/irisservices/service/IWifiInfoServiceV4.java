package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.BluetoothDeviceList;
import com.nimble.irisservices.entity.WifiInfo;

public interface IWifiInfoServiceV4 {


	public WifiInfo isAlreadycontain(long gatewayid);

	public List<BluetoothDeviceList> getWifiList(long gatewayID, long userID, String os);
	
	public List<Object> getSensorWifiList(long gatewayID);
	
	public boolean isWifiConPaired(long gatewayID);

}
