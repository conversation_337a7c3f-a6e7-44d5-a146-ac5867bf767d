package com.nimble.irisservices.service;

import java.util.HashMap;
import java.util.List;

import com.nimble.irisservices.dto.JBMIinfo;
import com.nimble.irisservices.dto.JBowlPetProfile;
import com.nimble.irisservices.dto.JCaloriesinfo;
import com.nimble.irisservices.dto.JInjuryinfo;
import com.nimble.irisservices.dto.JPetprofileFlutter;
import com.nimble.irisservices.dto.JPetprofileV2;
import com.nimble.irisservices.dto.JPetprofileWatch;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.entity.PetProfile;
import com.nimble.irisservices.entity.PetSpecies;

public interface IPetSpeciesServices {

	boolean saveORupdateSpecies(PetSpecies petSpecies);
	
	List<PetSpecies> getPetSpecies();
	
	PetSpecies getPetSpecies(Long id);
	
	PetSpecies getPetSpeciesByName(String name);
	
	public PetProfile createPetprofile(JPetprofileV2 jpetprofile);
	
	public List<PetProfile> getPetprofile(String columnname, String value);
	
	public List<JPetprofileV2> getJPetprofilesByUser(long userid,String speciesid);
	
	public JBMIinfo getBMIinfo(long breedid, String gender, float bmivalue);
	
	public JInjuryinfo getInjuryinfo(float injuryvalue);
	
	public HashMap<String,String> getFormulas();
	
	public JCaloriesinfo getFactor(JPetprofileV2 pet);
	
	public boolean checkPetnameExist(String name, long userid, long petid);
	
	public boolean saveHealthReport(String qry);
	
	public JResponse retrieveHealthReport(long userid);
	
	public boolean checkGatewayExist(long gateway_id, long petid);
	
	public long getClassification( int age, float weight);
	
	public List<JPetprofileFlutter> getJPetprofilesForFlutter(long userid, String species);
	
	public List<JPetprofileWatch> getJPetprofilesForWatch(long userid);

	public PetProfile createBowlPetProfile(JBowlPetProfile jBowlPetProfile);

	public List<JBowlPetProfile> getJBowlPetProfile(long user_id, long gateway_id);

	public JPetprofileFlutter getJPetprofiles(long gatewayId);
	
	public List<JPetprofileV2> getJPetprofilesByUserAndMonitor(long userid,String speciesid,long monitortype);
	
	public List<JBowlPetProfile> getJBowlPetProfileById(long user_id, long gateway_id);
}
