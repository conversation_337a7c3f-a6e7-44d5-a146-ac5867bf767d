package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.JTrendingVideo;

public interface ITrendingvideoServiceV4 {

	public boolean UpdateVideoInfoTransaction(long id, long videoid, int like, int viewcount);

	public boolean CreateVideoInfoTransaction(long userid, long videoid2, int like, int viewcount);

	public List<JTrendingVideo> getTrendingvideoInfoV4(List<JTrendingVideo> trendvideoList, long parseLong) throws Exception;
	
}
