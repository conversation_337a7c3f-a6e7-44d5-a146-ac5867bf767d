package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.entity.AlertType;
import com.nimble.irisservices.entity.AssetModel;
import com.nimble.irisservices.entity.MessageType;
import com.nimble.irisservices.entity.NotificationType;
import com.nimble.irisservices.entity.OrderChannel;
import com.nimble.irisservices.entity.ReportType;

public interface IFetchDropdownService {

	List<AlertType> getAlertTypes();

	List<ReportType> getReportTypes();

	List<MessageType> getMessageTypes();

	List<AssetModel> getAssetModel();
	
	List<AlertType> getAlertTypesByCmp(long cmpId);
	
	List<ReportType> getReportTypesByCmp(long cmpId);
	
	List<OrderChannel> getOrderChannel();
	
	List<NotificationType> getNotificaitonType();
	
	boolean saveAssetModel(AssetModel assetModel);

}
