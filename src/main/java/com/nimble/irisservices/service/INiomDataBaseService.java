package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.JQrcDetails;
import com.nimble.irisservices.dto.Jorder;
import com.nimble.irisservices.dto.OrderTrackingData;
import com.nimble.irisservices.dto.ShipmentDetailData;
import com.nimble.irisservices.dto.ShipmentDetailV2;
import com.nimble.irisservices.entity.OrderSkuDetails;
import com.nimble.irisservices.niom.entity.Device_history;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Ordermap;
import com.nimble.irisservices.niom.entity.Orders;

public interface INiomDataBaseService {

	public boolean isMeidMappedInOrdermap(String meid);

	public List<Orders> getOrderById(String orderChannel, String orderID);

	public List<Ordermap> checkOrderMappedCount(String string);

	public boolean saveORupdateOrder(Orders order);

	public List<Inventory> getInventory(String qrcCode);

	public String checkmeidsvalid(String meids, String devicemodel);

	public boolean updateInventoryNewMeid(String deviceState_id, String meids, String order_id);

	public boolean saveDeviceHistory(Device_history device_history);

	public boolean updateOrderMappedDate(String mapped_date, String order_id);

	public List<Jorder> getJorder(String order_id);

	public boolean saveORupdateMappedOrder(Ordermap ordermap);

	public boolean updateOrdermapUserDetails(long order_id, String encode, String string, String encode2);

	public boolean deleteMappedOrder(String meid);

	public boolean updatedevicestateqrccode(String qrc, String meid, int deviceModelId);

	public Inventory getInventoryByMeid(String meid);

	public boolean updateExternalOrdersInNiom(Orders order);

	public boolean updateMeidInOrderMap(String userID, String meid);

	public boolean updateInventoryFotaVersion(String meid, String fota_version);

	public boolean changeMeidAndReplaceInOrderMap(String oldMeid, String newMeid);

	public boolean enableReplaceInOrderMapByMeid(String meid, long userId);

	public boolean checkMeidIsAvailableInOrderMap(String oldMeid);

	public boolean disableReplaceInOrderMapByMeid(String meid, long userId);

	public Inventory getInventoryByIccid(String iccid);

	public List<Ordermap> getMappedOrderByMeid(String meid);

	public String getOrderChannelById(int order_id);

	public boolean updateInventoryMdn(String msisdn, String iccid);

	public String getMeidBySim(String iccid);

	public boolean updateOrdermapMdn(String msisdn, String meid);

	Orders getOrderDetails(long order_id);

	public boolean updateOrdersTable(long order_id, String quantity, int return_units, String account_status);

	public int getGatewayId(String meids);

	boolean updateOrderMap(long order_id, String meid, String updateKey);

	public List<ShipmentDetailData> getShipmentDetails(String WhereKey, String value);

	public List<OrderTrackingData> getTrackingDetails(String trackingID);

	public String getDateTimeByOrderId(String order_id);

	public JQrcDetails getFirmwareAndFotaVersionDeviceModelNumber(String qrc);

	public List<Ordermap> getMappedOrderByOrderId(String order_id);

	public JQrcDetails getOrderAndUserInfo(String order_id);

	public Orders getOrderByEmailOrPhone(long userId, String email, String mobile_no, String qrc);

	public boolean updateOrdermapEmailDetails(String from_email, String to_email);

	public int getIsBundle(String order_id, String orderChannel, long mtype_id);

	public Ordermap getMappedOrderByMeidSql(String meid);

	public String getInventoryBySerialNo(String qrcode);

	Orders getOrderByOrderId(String orderId, String channel);

    List<OrderSkuDetails> getSkuDetails();
    
    public ShipmentDetailV2 getShipmentDetailsV2(String whereKey, String value);
    
}
