package com.nimble.irisservices.service;

import java.util.ArrayList;

import com.nimble.irisservices.dto.JPetprofile;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.Offlineuserdetails;
import com.nimble.irisservices.entity.PetFeedDetails;
import com.nimble.irisservices.entity.RemoveGatewayRequestHistory;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserCancelFeedBackHistory;
import com.nimble.irisservices.entity.UserLocation;
import com.nimble.irisservices.entity.UserMetaData;
import com.nimble.irisservices.entity.WifiInfo;

public interface IAsyncService {

	public void runAsync();

	public void SendEmail_SES(String toAddr, String ccAddr, String bccAddr, String sub, String mailmsg);

	public void updateRegisterUserEmailStatus(String to_address, String cc_address, String bcc_address, String mailSub,
			String statusEmailContent, String qrcCode, String errorCode, boolean status, String meid, String iccid,
			String vendor, int isRecallDevice, String skuNumber, String userEmail, String deviceId, String tariffId, String qrc);

	public void sendDynamicCommand(String gatewayid, String msg, long seqno);

	public void saveorupdateofflineUserDetails(Offlineuserdetails offlineUser);

	public void createChargebeeUser(String fName, String lName, String email, String phoneno, String chargebeeCusId,
			boolean inapp, String addr, String city, String state, String country);

	public void createUserInChargebee(String fName, String lName, String email, String phoneNo, String userName,
			int amount, String desc);
	
	public void updateCBCustomerDetails(User user);

	public void sendEmailDataToQueue(String sub, String mailmsg, String qrCode, String errorCode, boolean status,
			String meid, String iccid, String vendor);

	public void externalQrcActivationStatus(String qrcode, String errorCode, String toAddr, String ccAddr,
			String bccAddr, String sub, String mailmsg);

	public void updateWifiInfoAddress(WifiInfo wifi);

	public void addFreeVPM(String featureName, long userId);

	public void updateAlexaId(String userIdMetaData, String userId);

	//public RvTripHistory updateMapboxImage(RvTripHistory rvTripHistory);

	public void sendPushNotification(String userid, String title, String notifyMsg, long reminderid,
			String notificationType);
	
	public boolean updateNotificationStatus(long userId, ArrayList<Long> notificationIds);

	public void asynPostRequest(String url,String urlParams);

	public void saveRecallHistory(long recalUserId, String oldMeid, String meid, int isRecallDevice);

	public void changeMeidAndReplaceInOrderMap(String oldMeid, String meid);

	public void changePetNameByGatewayId(String petName, long gateway_id);

	public void updateEvalidation(long id, String password);

	public void mapOldGatewayToReturnAC(Gateway replacedGateway, String returnLoginUsername, String oldUsername);

	public void sendSlackMessage(String title, String text);

	public void saveCurrentPlanDetails(long user_id, String chargebee_id, int plan_id, int period_id, String chargebee_plan_id, JResponse response);

	public void enableDisableMarketingNotification(UserMetaData userNotif);

	public void updateSalesChannel(String user_id, String orderChannel, long gatewayId, String meid, String orderId, long mtype_id);
	
	public void asynPostRequestV2(String url,String urlParams, String auth, String contentType);
	
	public void generateAddiBenefits(UserV4 user, String period,String createdFrom);

	public void saveOrUpdateUserCancelFeedBackHistory(UserCancelFeedBackHistory userCancelFeedBackHistory);

	public void saveOrUpdateRemoveGatewayRequestHistory(RemoveGatewayRequestHistory removeGatewayRequestHistory);

	public void updatePetFeedRemainder(PetFeedDetails petFeedDetails, UserV4 user);
	
	public void updateReqCalories( long gatewayid, double req_calories);

	public void checkAndUpdateGatewayToAlexa(long id);
	
	public void updateReqCaloriesById( long petProfileid, double req_calories);
	
	public void updateReqWeightById( long profileid, double req_weight);
	
	public void updateReqWeightByProfId( long petProfileid, double req_calories);
	
	public void updateCxRefund(String invoiceid, int refundAmt);

	public void orderMap(long userId, String email, String mobile_no, long gatewayId, String country, String qrc, long monitortypeid, String warrantyClaimType);

	public void insertDeviceSub(long userid,long gatewayid,String orderdate,long monitortype,long sales_channel,boolean isgps);
	
	public void insertDynamicCmd(Gateway gateway, String cmdValue, int i, String status);

	public void updateUserLocation(UserLocation userLocation);
}
