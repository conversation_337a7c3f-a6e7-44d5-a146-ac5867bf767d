package com.nimble.irisservices.service;

import java.util.List;

import com.nimble.irisservices.dto.JAlert;
import com.nimble.irisservices.dto.JAlertOverview;
import com.nimble.irisservices.dto.JAlertRange;
import com.nimble.irisservices.dto.JAlertV4;
import com.nimble.irisservices.dto.JSreiAlertOverview;
import com.nimble.irisservices.entity.User;

public interface IAlertService {

	List<JAlert> getalerts(String groupid, String subgroupid, String gatewayid, String alerttype, long l,
			String deliquencyStatus, String tempunit, String fromtime, String totime, String nodeid, String id);

	// To get alerts where ack=0 with limit 100;
	List<JAlert> getackalerts(String groupid, String subgroupid, String gatewayid, String alerttype, long l,
			String deliquencyStatus, String tempunit, String fromtime, String totime, String nodeid, String id);

	JAlertOverview getalertoverview(String groupid, String subgroupid, long id);

	JSreiAlertOverview getsreialertoverview(String groupid, String subgroupid, long id);

	boolean delAlert(long userid, String assetid);

	List<JAlertRange> getAlertRange(String assetid, User user);

	public List<JAlertV4> getUnAckFurbitAlerts(long userid, String tempUnit, String monitortype, String timezone);
	
	public List<JAlert> getFurbitalerts(String groupid, String subgroupid,String gatewayid, String alerttypeid, 
			long userid ,String deliquencyStatus,String tempunit,String fromtime,String totime, String nodeid, String id);
}
