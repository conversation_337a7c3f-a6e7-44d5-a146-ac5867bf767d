package com.nimble.irisservices.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.nimble.irisservices.dto.JAssetLastReportV4;
import com.nimble.irisservices.dto.JAssetLastReportWatch;
import com.nimble.irisservices.dto.JGatewayGeneralInfo;
import com.nimble.irisservices.dto.JGatewayGeneralInfoV5;
import com.nimble.irisservices.dto.JGatewayMonthList;
import com.nimble.irisservices.dto.JGatewaySubSetup;
import com.nimble.irisservices.dto.JGatewayTimeList;
import com.nimble.irisservices.dto.JGatewayWeekList;
import com.nimble.irisservices.dto.JPetmonitorHistory;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.LastGatewayReport;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.dto.sensorReportData;
import com.nimble.irisservices.entity.DashboardBanner;
import com.nimble.irisservices.entity.MonitorType;
import com.nimble.irisservices.entity.PetProfile;

public interface IReportServiceV4 {
	
	public List<JAssetLastReportV4> getLastgatewayreportV4(String groupid, String subgroupid, String assetgroupid,
			String gatewayid, long userId, String offset, String limit, String string, String country);
	
	public int getGatewayReportCount( long gatewayid);
	
	public String getLastGatewayReporttime( long gatewayid);
	
	public List<JAssetLastReportWatch> getLastgatewayreportWatch(long userid,String tempunit,List<JGatewaySubSetup> gateway_setup) ;
	
	public List<JGatewaySubSetup> getSubscriptionFromDB(UserV4 user);
	
	public List<LastGatewayReport> getjGatewayReport(long userid,String tempunit,List<JGatewaySubSetup> gateway_setup) ;

	public JGatewayGeneralInfo getGatewayGeneralInfo(long userId, String date, String timeZone);

	public PetProfile getPetProfileByUserId(long userId);

	public List<JGatewayWeekList> getGatewayGenerallist(long gatewayId, String date, String timeZone, boolean is_freePlan);
	
	public List<JGatewayTimeList> getGatewayGeneralTimelist(long gatewayId, String date, String timeZone);
	
	public List<MonitorType> getAllmonitorType();

	Map<String, List<Object>> getLastgatewayreportV5( long userid, 
			 String tempunit, String country, String category, String os);
	
	public List<JGatewayMonthList> getGatewayMonthlist(long gatewayId, String date, String timeZone, boolean is_freePlan);
	
	public JGatewayGeneralInfoV5 getGatewayGeneralInfoV5(long userId, String date, String timeZone);
	
	Map<String, List<Object>> getLastgatewayreportV6( long userid, 
			 String tempunit, String country, String category, String os, String req_ver);

	public List<DashboardBanner> getMarkettingBannerreport(String os, String app_ver, boolean checkPaidPlan);

	Map<String, Object> getLastGatewayReportV6(long gatewayid, long userid, long monitor_id, String country,
			String os, String req_ver, JResponse response);

	public List<sensorReportData> getSensoGrpRep(long gatewayid, String todate, String todate2, String timeZone);

	List<sensorReportData> getSensorRep(long gatewayid, String Date, String timeZone);

	Map<String, List<Object>> getLastgatewayreportV7( long userid, 
			 String tempunit, String country, String category, String os, String req_ver);
	
	public HashMap<String, String> getNSInfo(long gatewayid);
	
	public List<JPetmonitorHistory> getPetmonitorGrpRep(long gatewayid, String todate, String todate2, String timeZone, String tempunit);

	public Map<String, Object> getLastgatewayreportV8(long userId, String string, String country, String string2,
			String os, String req_ver);
	
	public JResponse getLastGatewayReportV8(long gatewayid, long userid, long monitor_id, String country,
			String os, String req_ver, JResponse response);

	List<JGatewaySubSetup> getProductSubscriptions(UserV4 user);

	boolean isSubscribedProduct(String chargebeeId);

    int compareVersions(String appVerFromConfig, String appVer);
}
