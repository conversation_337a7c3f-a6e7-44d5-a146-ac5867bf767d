package com.nimble.irisservices.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.json.JSONObject;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.ResponseEntity;

import com.nimble.irisservices.dto.ExternalLogin;
import com.nimble.irisservices.dto.JAllUser;
import com.nimble.irisservices.dto.JRVUser;
import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.JRvPetsafety;
import com.nimble.irisservices.dto.JUser;
import com.nimble.irisservices.dto.JUserDeviceInfo;
import com.nimble.irisservices.dto.SignUp;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Company;
import com.nimble.irisservices.entity.DeviceReplaced;
import com.nimble.irisservices.entity.DeviceReplacedHistory;
import com.nimble.irisservices.entity.ForceUpdate;
import com.nimble.irisservices.entity.Offlineuserdetails;
import com.nimble.irisservices.entity.OrderMappingDetails;
import com.nimble.irisservices.entity.Role;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.entity.UserDeviceInfo;
import com.nimble.irisservices.entity.UserToken;
import com.nimble.irisservices.entity.UserVerification;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.exception.InvalidEmailException;
import com.nimble.irisservices.exception.InvalidUsernameException;
import com.nimble.irisservices.niom.entity.Inventory;
import com.nimble.irisservices.niom.entity.Orders;

public interface IUserService {

	public User getUserById(long id) throws InvalidUsernameException;
	
	public User getUserByName(String username) throws InvalidUsernameException;

	public User verifyAuthKey(String autho) throws InvalidAuthoException;

	public List<User> getUser(String userid, long cmpid) throws InvalidUsernameException;

	boolean signUp(SignUp signUp, Company cmp);

	public boolean updateUser(User user);

	public boolean saveOrUpdateJUser(JUser juser) throws DataIntegrityViolationException;

	boolean assignGatewaysToUser(Set<String> gatIds, long userid, User user);

	public boolean saveOrUpdateUserToken(User user, UserToken usertoken) throws DataIntegrityViolationException;
	
	public boolean saveOrUpdateUserDeviceInfo(User user, JUserDeviceInfo deviceInfo, String ipAddress) throws DataIntegrityViolationException;

	public List<UserDeviceInfo> getUserDeviceInfo();
	
	public boolean delUserGateway(long cmpid, String asset_id);
	
	public boolean deleteUser(User user);

	public List<User> getUserInRole(String userid, long cmpid, long roleid, String p_roleid) throws InvalidUsernameException;

	boolean getDeviceLicense(Long userid, String deviceName, String deviceType);

	public List<JRvPetsafety> getRvPetSafetyBlogUrl(long userid, int maxRows);

	public boolean delBlogUrl(long blogId);

	public int saveorupdateRvpetsafetyUrl(List<JRvPetsafety> jrvPetsafetyList);

	public int saveorupdateRvUser(JRVUser jrvUser);

	boolean enabledisableuser(String userid, String status);

	boolean saveorupdateofflineUserDetails(Offlineuserdetails offlineUser);

	public Offlineuserdetails getOfflineUser(Offlineuserdetails offlineUser);

	public List<UserToken> getUserToken(String userID);

	public List<JAllUser> getAllUser();
	
	public boolean updateUserNotification(String userId, String status);
	
	//New Service
	public User getUserByEmail(String email) throws InvalidEmailException;
	
	//New Service
	public UserVerification createEmailVerificationToken(User user);
	
	//New Service
	public ResponseEntity<String> verifyEmail(String token);
	
	public User getUserById(String userId);
	
	public JResponse loginViaGoogleOrFacebook(ExternalLogin externalLogin);
	
	public Map<String, String> getUserId_cmpIdByAuth(String auth) throws InvalidAuthoException;

	public User verifyAuthKeyV2(String authKey) throws InvalidAuthoException ;
	
	public User getUserByCBid(String cbid);
	
	public int updateLastLoginTypeAndTime(long id, int type, String time);
	
	public void saveOrderMappingDetails(OrderMappingDetails orderMappingDetails);
	
	public double getCreditAmountBySKU(String sku);
	
	public OrderMappingDetails getOrderMappingByUser(long userid);
	
	public int getRemainingDays(long user_id);
	
	public OrderMappingDetails getOrderMappingById(long id);
	
	public long getUserIdByEmail(String email);
	
	public String deleteUserv2(User user);
	
	public User getUserByUNameOrEmail(String email) ;
	
	public boolean resetPasswordRequest(UserV4 user);
	
	public Role getRole(long id);
	
	public boolean saveUser(User user);

	
	public JSONObject getNiomGetOrderCount(String orderchannel, String orderid) ;
	
	public JSONObject getInventory(String qrcCode);
	
	public boolean orderMapping(String meids, String mapped_date, long devicestateid, String order_id,
			String devicemodel, String subscriptioncreated, String isuserregistered, String warrantyType, long gatewayId);
	
	public boolean deleteordermap(String meid, int initialDeviceStateid);
	
	public boolean updateOrdersDataV2(String orderchannel, Orders order, Inventory inventory) ;

	boolean updateMeidInOrderMap(String userID, String meid);

	public DeviceReplaced getRecallDeviceDetails(long userId);

	public boolean changeRecallDeviceStatus(long recalUserId, String oldMeid, int isRecallDevice);

	public boolean insertRecallDeviceHistory(DeviceReplacedHistory deviceRecallHistory);

	public ForceUpdate getForceUpdate(String userid);

	public boolean deleteRVUser(long user_id);

	public boolean deleteUserDeviceInfo(long user_id);

	public boolean deleteUserPushNotifications(long user_id);

	public boolean deleteUserToken(long user_id);

	public boolean deleteUserVerification(long user_id);

	public boolean deleteTrendingVideoTransaction(long user_id);

	public boolean deleteUserById(long user_id);

	public boolean deleteAllUserInfo(long id, String email);

	public UserVerification createEmailVerificationTokenV2(long id);

	public UserVerification getUserVerificationTokenV2(long user_id);

	public void checkFirstNameAndLastNameInOrder(Orders order, UserV4 user);

	public boolean deleteUserGateway(long userid, long id);

	public DeviceReplaced getRecallDeviceDetails(long userId,long mType);

}
