package com.nimble.irisservices.service;

import java.util.List;

import org.springframework.dao.DataIntegrityViolationException;

import com.nimble.irisservices.entity.Apidetails;
import com.nimble.irisservices.entity.CompanyAccountSettings;

public interface IAccountService {
	
    public boolean saveOrUpdateCmpAccount(CompanyAccountSettings apiDetails ) throws DataIntegrityViolationException;
    
    public boolean update(CompanyAccountSettings cmpAccDetails ) throws DataIntegrityViolationException;
	
    public boolean deleteCmpAccount(long apiId,String serviceType,String api,String description);

    public List<CompanyAccountSettings> getCmpAccount(CompanyAccountSettings cmpAccDetails); 
	
	

	public boolean saveOrUpdateApiDetails(Apidetails apiDetails ) throws DataIntegrityViolationException;
	
	public List<Apidetails> getApi(Apidetails apiDetails); 
	
	public boolean deleteApi(Apidetails apiDetails);
	

}
