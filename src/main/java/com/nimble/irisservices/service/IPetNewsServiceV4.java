package com.nimble.irisservices.service;

import java.util.ArrayList;

import com.nimble.irisservices.dto.JPetNews;
import com.nimble.irisservices.entity.PetNews;

public interface IPetNewsServiceV4 {
	public ArrayList<JPetNews> listAppPetNews(String ostype,int offset, int rowcount, boolean is_device, boolean is_subscription);

	public ArrayList<PetNews> listWebPetNews();

	public boolean saveOrUpdatePetNews(PetNews petnews);

	public PetNews getPetNews(long id);
	
	public int getPetNewsCount();
}
