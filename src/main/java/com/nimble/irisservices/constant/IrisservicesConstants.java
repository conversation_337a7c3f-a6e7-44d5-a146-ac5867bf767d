package com.nimble.irisservices.constant;

public class IrisservicesConstants {

	public static final String DATETIMEFORMAT = "yyyy-MM-dd HH:mm:ss";
	
	public static final String DATEFORMAT = "yyyy-MM-dd";
	
	public static final String UTCFORMAT = "UTC";
	
	public static final String UTCTIMEZONE = "+0:00";
	
	public static final String TIMEFORMAT = "HH:mm:ss";
	
	public static final String IDLE = "IDLE";
	
	public static final String RUN = "RUN";
	
	public static final String WALK = "WALK";
	
	public static final int NUM_0 = 0;
	
	public static final int NUM_1 = 1;
	
	public static final int NUM_2 = 2;
	
	public static final int NUM_3 = 3;
	
	public static final int NUM_4 = 4;
	
	public static final String SPACE_STRING = " ";
	
	public static final String CHAR_COLON = ":";
	
	public static final String STARTTIME = "00:00:00";
	
	public static final String ENDTIME = "23:59:59";
	
	public static final String DEFAULT_DATE = "1753-01-01 11:11:11";
	
	public static final String WAGGLECAM = "WaggleCam";
	
	public static final String SMARTBOWL = "SmartBowl";
	
	public static final String WAGGLE = "PetMonitor";
	
	public static final String MINICAM = "MiniCam";
	
	public static final String WAGGLECAMPRO = "WaggleCamPro";
	
	public static final String WAGGLE_CAM_PRO_PLUS = "WaggleCam Pro+";
	
	public static final String WAGGLE_CAM_ULTRA = "WaggleCam Ultra";
	
	
	//Alerts Type
	public static final String ALERT_TEMPERATURE = "1";
	
	public static final String ALERT_BATTERY = "2";
	
	public static final String ALERT_ON_BATTERY = "3";
	
	public static final String ALERT_DNR = "11";
	
	public static final String ALERT_OFFLINE = "25";
	
	public static final String ALERT_POWER_RECOVERY = "17";
	
	public static final String ALERT_MOTION_DETECTION = "7";
	
	public static final String ALERT_MOTION_DETECTION_HUMAN = "20";
	
	public static final String ALERT_MOTION_DETECTION_DOG = "21";
	
	public static final String ALERT_MOTION_DETECTION_CAT = "22";
	
	public static final String ALERT_SOUND = "27";
	
	public static final String ALERT_SOUND_DOG = "23";
	
	public static final String ALERT_SOUND_CAT = "24";
	
	public static final String ALERT_SCHEDULE_TREAT_TOSS = "26";
	
	public static final String ALERT_HUMIDITY = "14";
	
	
	//Config Table Names
	public static final String TEMPERATURE_ALERT_CFG = "temp_alert_cfg";
	
	public static final String HUMIDITY_ALERT_CFG = "humidity_alert_cfg";
	
	public static final String POWER_ALERT_CFG = "power_alert_cfg";
	
	public static final String EMAIL = "email";
	
	public static final String SMS = "sms";
	
	public static final String EMAIL_IDS = "email_ids";
	
	public static final String MOBILE_NOS = "mobile_nos";
		
	public static final String POWER_MIN = "30";
	
	public static final String POWER_MAX = "80";
	
	public static final String NODESENDOR = "NodeSensor";
	
	public static final String RVSOLARCAM = "RVSolarCam";
	
	public static final String WATER_LEAKAGE = "29";
	
	public static final String DOOR_ALERM = "30";
	
	public static final String WATER_LEVEL = "31";
	
	public static final String DOOR_REMINDER = "32";

	public static final String RVSOLARMINI = "RVSolarMiniCam";
}
