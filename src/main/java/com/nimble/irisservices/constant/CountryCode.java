package com.nimble.irisservices.constant;

import java.util.HashMap;

public  class CountryCode {
	
	private static final HashMap<String, String> countryCode = new HashMap<String, String>();
	
	static {
		
		countryCode.put("US","+1-");
		countryCode.put("CA","+1-");
		countryCode.put("AF","+93-");
		countryCode.put("AL","+355-");
		countryCode.put("DZ","+213-");
		countryCode.put("AS","+1-");
		countryCode.put("AD","+376-");
		countryCode.put("AO","+244-");
		countryCode.put("AI","+1-");
		countryCode.put("AG","+1-");
		countryCode.put("AR","+54-");
		countryCode.put("AM","+374-");
		countryCode.put("AW","+297-");
		countryCode.put("AU","+61-");
		countryCode.put("AT","+43-");
		countryCode.put("AZ","+994-");
		countryCode.put("BS","+1-");
		countryCode.put("BH","+973-");
		countryCode.put("BD","+880-");
		countryCode.put("BB","+1-");
		countryCode.put("BY","+375-");
		countryCode.put("BE","+32-");
		countryCode.put("BZ","+501-");
		countryCode.put("BJ","+229-");
		countryCode.put("BM","+1-");
		countryCode.put("BT","+975-");
		countryCode.put("BO","+591-");
		countryCode.put("BA","+387-");
		countryCode.put("BW","+267-");
		countryCode.put("BR","+55-");
		countryCode.put("IO","+246-");
		countryCode.put("BN","+673-");
		countryCode.put("BG","+359-");
		countryCode.put("BF","+226-");
		countryCode.put("BI","+257-");
		countryCode.put("KH","+855-");
		countryCode.put("CM","+237-");
		countryCode.put("CV","+238-");
		countryCode.put("KY","+1-");
		countryCode.put("CF","+236-");
		countryCode.put("TD","+235-");
		countryCode.put("CL","+56-");
		countryCode.put("CN","+86-");
		countryCode.put("CX","+61-");
		countryCode.put("CC","+61-");
		countryCode.put("CO","+57-");
		countryCode.put("KM","+269-");
		countryCode.put("CG","+242-");
		countryCode.put("CK","+682-");
		countryCode.put("CR","+506-");
		countryCode.put("CI","+225-");
		countryCode.put("HR","+385-");
		countryCode.put("CU","+53-");
		countryCode.put("CY","+357-");
		countryCode.put("CZ","+420-");
		countryCode.put("DK","+45-");
		countryCode.put("DJ","+253-");
		countryCode.put("DM","+1-");
		countryCode.put("DO","+1-");
		countryCode.put("EC","+593-");
		countryCode.put("EG","+20-");
		countryCode.put("SV","+503-");
		countryCode.put("GQ","+240-");
		countryCode.put("ER","+291-");
		countryCode.put("EE","+372-");
		countryCode.put("ET","+251-");
		countryCode.put("FK","+500-");
		countryCode.put("FO","+298-");
		countryCode.put("FJ","+679-");
		countryCode.put("FI","+358-");
		countryCode.put("FR","+33-");
		countryCode.put("GF","+594-");
		countryCode.put("PF","+689-");
		countryCode.put("GA","+241-");
		countryCode.put("GM","+220-");
		countryCode.put("GE","+995-");
		countryCode.put("DE","+49-");
		countryCode.put("GH","+233-");
		countryCode.put("GI","+350-");
		countryCode.put("GR","+30-");
		countryCode.put("GL","+299-");
		countryCode.put("GD","+1-");
		countryCode.put("GP","+590-");
		countryCode.put("GU","+1-");
		countryCode.put("GT","+502-");
		countryCode.put("GN","+224-");
		countryCode.put("GW","+245-");
		countryCode.put("GY","+592-");
		countryCode.put("HT","+509-");
		countryCode.put("HN","+504-");
		countryCode.put("HK","+852-");
		countryCode.put("HU","+36-");
		countryCode.put("IS","+354-");
		countryCode.put("IN","+91-");
		countryCode.put("ID","+62-");
		countryCode.put("IR","+98-");
		countryCode.put("IQ","+964-");
		countryCode.put("IE","+353-");
		countryCode.put("IL","+972-");
		countryCode.put("IT","+39-");
		countryCode.put("JM","+1-");
		countryCode.put("JP","+81-");
		countryCode.put("JO","+962-");
		countryCode.put("KZ","+7-");
		countryCode.put("KE","+254-");
		countryCode.put("KI","+686-");
		countryCode.put("KW","+965-");
		countryCode.put("KG","+996-");
		countryCode.put("LA","+856-");
		countryCode.put("LV","+371-");
		countryCode.put("LB","+961-");
		countryCode.put("LS","+266-");
		countryCode.put("LR","+231-");
		countryCode.put("LY","+218-");
		countryCode.put("LI","+423-");
		countryCode.put("LT","+370-");
		countryCode.put("LU","+352-");
		countryCode.put("MO","+853-");
		countryCode.put("MK","+389-");
		countryCode.put("MG","+261-");
		countryCode.put("MW","+265-");
		countryCode.put("MY","+60-");
		countryCode.put("MV","+960-");
		countryCode.put("ML","+223-");
		countryCode.put("MT","+356-");
		countryCode.put("MH","+692-");
		countryCode.put("MQ","+596-");
		countryCode.put("MR","+222-");
		countryCode.put("MU","+230-");
		countryCode.put("YT","+262-");
		countryCode.put("MX","+52-");
		countryCode.put("FM","+691-");
		countryCode.put("MD","+373-");
		countryCode.put("MC","+377-");
		countryCode.put("MN","+976-");
		countryCode.put("ME","+382-");
		countryCode.put("MS","+1-");
		countryCode.put("MA","+212-");
		countryCode.put("MZ","+258-");
		countryCode.put("MM","+95-");
		countryCode.put("NR","+674-");
		countryCode.put("NP","+977-");
		countryCode.put("NL","+31-");
		countryCode.put("NC","+687-");
		countryCode.put("NZ","+64-");
		countryCode.put("NI","+505-");
		countryCode.put("NE","+227-");
		countryCode.put("NG","+234-");
		countryCode.put("NU","+683-");
		countryCode.put("NF","+672-");
		countryCode.put("MP","+1-");
		countryCode.put("NO","+47-");
		countryCode.put("OM","+968-");
		countryCode.put("PK","+92-");
		countryCode.put("PW","+680-");
		countryCode.put("PA","+507-");
		countryCode.put("PG","+675-");
		countryCode.put("PY","+595-");
		countryCode.put("PE","+51-");
		countryCode.put("PH","+63-");
		countryCode.put("PN","+64-");
		countryCode.put("PL","+48-");
		countryCode.put("PT","+351-");
		countryCode.put("PR","+1-");
		countryCode.put("QA","+974-");
		countryCode.put("RE","+262-");
		countryCode.put("RO","+40-");
		countryCode.put("RU","+7-");
		countryCode.put("RW","+250-");
		countryCode.put("KN","+1-");
		countryCode.put("LC","+1-");
		countryCode.put("VC","+1-");
		countryCode.put("WS","+685-");
		countryCode.put("SM","+378-");
		countryCode.put("ST","+239-");
		countryCode.put("SA","+966-");
		countryCode.put("SN","+221-");
		countryCode.put("RS","+381-");
		countryCode.put("SC","+248-");
		countryCode.put("SL","+232-");
		countryCode.put("SG","+65-");
		countryCode.put("SK","+421-");
		countryCode.put("SI","+386-");
		countryCode.put("SB","+677-");
		countryCode.put("SO","+252-");
		countryCode.put("ZA","+27-");
		countryCode.put("KR","+82-");
		countryCode.put("ES","+34-");
		countryCode.put("LK","+94-");
		countryCode.put("SH","+290-");
		countryCode.put("PM","+508-");
		countryCode.put("SD","+249-");
		countryCode.put("SR","+597-");
		countryCode.put("SJ","+47-");
		countryCode.put("SZ","+268-");
		countryCode.put("SE","+46-");
		countryCode.put("CH","+41-");
		countryCode.put("SY","+963-");
		countryCode.put("TW","+886-");
		countryCode.put("TJ","+992-");
		countryCode.put("TZ","+255-");
		countryCode.put("TH","+66-");
		countryCode.put("TG","+228-");
		countryCode.put("TK","+690-");
		countryCode.put("TO","+676-");
		countryCode.put("TT","+1-");
		countryCode.put("TN","+216-");
		countryCode.put("TR","+90-");
		countryCode.put("TM","+993-");
		countryCode.put("TC","+1-");
		countryCode.put("TV","+688-");
		countryCode.put("UG","+256-");
		countryCode.put("UA","+380-");
		countryCode.put("AE","+971-");
		countryCode.put("GB","+44-");
		countryCode.put("UY","+598-");
		countryCode.put("UZ","+998-");
		countryCode.put("VU","+678-");
		countryCode.put("VA","+379-");
		countryCode.put("VE","+58-");
		countryCode.put("VN","+84-");
		countryCode.put("VG","+1-");
		countryCode.put("VI","+1-");
		countryCode.put("WF","+681-");
		countryCode.put("EH","+212-");
		countryCode.put("YE","+967-");
		countryCode.put("ZM","+260-");
		countryCode.put("ZW","+263-");
		
	}

	public String getCountryCode(String code) 
    { 
	
        String cnCode = "+1-";
        
        if(countryCode.containsKey(code))
        	cnCode = countryCode.get(code);
        
        return cnCode;
        
    }
}
