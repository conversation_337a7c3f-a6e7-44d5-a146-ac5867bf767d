package com.nimble.irisservices.common;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;

import javax.net.ssl.HttpsURLConnection;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

public class ReverseGeocoder {

	private String MAP_PROVIDER = "GOOGLE";
	private int locatescnt      = 0;

	private static final Logger log = LogManager.getLogger(ReverseGeocoder.class);

	public String getAddress_MapBox(String lat,String lon)
	{
		log.info("Entered getAddress_MapBox : lat : "+lat+" lon : "+lon);
		String address = "";
		HttpsURLConnection  connection = null;
		try 
		{
			String mapboxeurl  = "https://api.mapbox.com/geocoding/v5/mapbox.places/";
			String webserviceURL  = mapboxeurl + lon + "," + lat + ".json?access_token=pk.eyJ1IjoibmltYmxld2lyZWxlc3MiLCJhIjoiY2preHBjcnV3MGE0MjNwcjBucmNnZzd5MSJ9.iEpIubbVkS8Wc8t4UlIybQ";

			log.info("Address by :"+webserviceURL);
			URL url = new URL(webserviceURL);
			connection = (HttpsURLConnection)url.openConnection();
			//Get Response 
			BufferedReader rd = new BufferedReader(new InputStreamReader(connection.getInputStream()));
			String line;
			StringBuffer response = new StringBuffer(); 

			while((line = rd.readLine()) != null) {
				response.append(line);
				response.append('\r');
			}
			rd.close();

			JSONObject obj   =  new JSONObject(response.toString());
			JSONArray jsonArry  = (JSONArray)(obj.get("features"));
			if(jsonArry.length() > 0) {
				JSONObject add_obj  = (JSONObject)jsonArry.get(0);
				address    = add_obj.getString("place_name").trim(); 
				locatescnt = 1;
				log.info("getAddress_MapBox - address = "+address);
			}else {
				log.info("getAddress_MapBox - response array size = "+jsonArry.length());
			}			
		} 
		catch (Exception e) 
		{
			log.info("Exception occured in getAddress using google mailbox : "+e.getMessage());
		} 
		finally 
		{
			if(connection != null) {
				connection.disconnect(); 
			}
		}
		return address;
	}
}
