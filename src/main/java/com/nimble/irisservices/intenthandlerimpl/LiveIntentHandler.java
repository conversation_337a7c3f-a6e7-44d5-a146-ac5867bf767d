package com.nimble.irisservices.intenthandlerimpl;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.amazon.speech.slu.Intent;
import com.amazon.speech.speechlet.IntentRequest;
import com.amazon.speech.speechlet.Session;
import com.amazon.speech.speechlet.SpeechletException;
import com.amazon.speech.speechlet.SpeechletResponse;
import com.amazon.speech.ui.PlainTextOutputSpeech;
import com.amazon.speech.ui.SimpleCard;
import com.nimble.irisservices.Util.AlexaUtils;
import com.nimble.irisservices.dto.JGatewayToAlexa;
import com.nimble.irisservices.entity.GatewayStatus;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.intenthandlerservice.IntentHandler;
import com.nimble.irisservices.service.IAlexaService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IGatewayService;



@Component
public class LiveIntentHandler implements IntentHandler {	

	@Autowired
	IAlexaService AlexaService;

	@Autowired
	@Lazy
	IAsyncService async;
	
	@Autowired
	IGatewayService gatewayService;
	
	@Autowired
	Helper _helper;
	
	private static final Logger log = LogManager.getLogger(LiveIntentHandler.class);

	@Override
	public SpeechletResponse handleIntent(Intent intent, IntentRequest request, Session session, String user_id) {
		log.info("Entered into handleIntent :: user_id : " + user_id);
		String speechText = "";
		try {

			if (intent == null) {
				try {
					throw new SpeechletException("Unrecognized intent");
				} catch (SpeechletException e) {
					log.error("Unrecognized intent : " + e.getMessage());
				}
			}

			speechText = prepareLiveIntent(user_id);
			
		} catch (Exception e) {
			speechText = AlexaUtils.ERROR_TEXT;
			log.error("Error While Getting temperature info : "+e.getLocalizedMessage());
		}

		SimpleCard card = new SimpleCard();
		card.setTitle("Live Result");
		card.setContent(speechText);

		PlainTextOutputSpeech speech = new PlainTextOutputSpeech();
		speech.setText(speechText);

		SpeechletResponse response = SpeechletResponse.newTellResponse(speech, card);
		return response;
	}

	public String prepareLiveIntent(String user_id) {
		log.info("Entered into prepareLiveIntent :: user_id : "+ user_id);
		String speechText = "";
		try {
			JGatewayToAlexa gatewayToAlexa = gatewayService.getJGatewayToAlexa(user_id);
			if( gatewayToAlexa == null )
				return AlexaUtils.WC_ADD_DEVICE_TEXT;	
			
			GatewayStatus gatewayStatus = gatewayService.getGatewayStatusByMeid( gatewayToAlexa.getMeid() );
			if( gatewayStatus == null )
				return AlexaUtils.WC_ADD_DEVICE_TEXT;
			
			if( gatewayStatus.isIs_online() ) {		
				speechText = AlexaUtils.DEVICE_LIVE_TEXT;	
			} else {
				speechText = AlexaUtils.DEVICE_NOT_LIVE_TEXT;	
			}
		} catch (Exception e) {
			log.error("error in prepareLiveIntent :: Error : "+ e.getLocalizedMessage());
			speechText = "OOPS!, Something went wrong, Please try again...";
		}
		return speechText;
	}

}
