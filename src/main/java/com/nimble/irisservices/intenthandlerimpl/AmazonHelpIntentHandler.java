package com.nimble.irisservices.intenthandlerimpl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.amazon.speech.slu.Intent;
import com.amazon.speech.speechlet.IntentRequest;
import com.amazon.speech.speechlet.Session;
import com.amazon.speech.speechlet.SpeechletResponse;
import com.amazon.speech.ui.Card;
import com.amazon.speech.ui.PlainTextOutputSpeech;
import com.nimble.irisservices.Util.AlexaUtils;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.intenthandlerservice.IntentHandler;
import com.nimble.irisservices.service.IUserServiceV4;

@Component
public class AmazonHelpIntentHandler implements IntentHandler {

	@Autowired
	IUserServiceV4 userServiceV4;
	
	protected Logger log = LoggerFactory.getLogger(AmazonHelpIntentHandler.class);


	@Override
	public SpeechletResponse handleIntent(Intent intent, IntentRequest request, Session session, String userid) {
		log.info("Entered into handleIntent ");
		Card card = null;
		log.info("Help Intent : User Id : " + userid  );
		try {
			UserV4 user = userServiceV4.verifyAuthV3("id", userid);

			String speechText = "Hey " + user.getFirstname() + ", " + AlexaUtils.HelpText;

			card = AlexaUtils.newCard("Help", speechText);
			PlainTextOutputSpeech speech = AlexaUtils.newSpeech(speechText, false);
			return AlexaUtils.newSpeechletResponse(card, speech, session, false);

		} catch (InvalidAuthoException e) {
			log.error(" Error occured at  handleIntent : "+e.getLocalizedMessage());
			// TODO Auto-generated catch block
			String speechText = "Hey " + AlexaUtils.HelpText;
			card = AlexaUtils.newCard("Help", speechText);
			PlainTextOutputSpeech speech = AlexaUtils.newSpeech(speechText, false);
			return AlexaUtils.newSpeechletResponse(card, speech, session, false);
			
		}


	}

}
