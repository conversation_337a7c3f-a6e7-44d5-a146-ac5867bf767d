package com.nimble.irisservices.intenthandlerimpl;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.amazon.speech.slu.Intent;
import com.amazon.speech.speechlet.IntentRequest;
import com.amazon.speech.speechlet.Session;
import com.amazon.speech.speechlet.SpeechletException;
import com.amazon.speech.speechlet.SpeechletResponse;
import com.amazon.speech.ui.PlainTextOutputSpeech;
import com.amazon.speech.ui.SimpleCard;
import com.nimble.irisservices.Util.AlexaUtils;
import com.nimble.irisservices.dto.HttpResponse;
import com.nimble.irisservices.dto.JGatewayToAlexa;
import com.nimble.irisservices.entity.GatewayStatus;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.helper.HttpRequest;
import com.nimble.irisservices.intenthandlerservice.IntentHandler;
import com.nimble.irisservices.service.IAlexaService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IGatewayService;


@Component
public class TossIntentHandler implements IntentHandler {	

	@Autowired
	IAlexaService AlexaService;

	@Autowired
	@Lazy
	IAsyncService async;
	
	@Autowired
	IGatewayService gatewayService;
	
	@Autowired
	Helper _helper;
	
	@Value("${custom.ping.duration.minutes.alexa}")
	private int custom_ping_duration_alexa;
	
	@Value("${wagglecamlistener.url}")
	private String wagglecamlistener_url;
	
	@Value("${alexa.toss.url}")
	private String alexa_toss_url;
	
	@Autowired
	HttpRequest httpRequest;
	
	private static final Logger log = LogManager.getLogger(TossIntentHandler.class);

	@Override
	public SpeechletResponse handleIntent(Intent intent, IntentRequest request, Session session, String user_id) {
		log.info("Entered into handleIntent :: user_id : " + user_id);
		String speechText = "";
		try {

			if (intent == null) {
				try {
					throw new SpeechletException("Unrecognized intent");
				} catch (SpeechletException e) {
					log.error("Unrecognized intent : " + e.getMessage());
				}
			}

			speechText = prepareTossIntent(user_id);
			
		} catch (Exception e) {
			speechText = AlexaUtils.ERROR_TEXT;
			log.error("Error While Getting temperature info : "+e.getLocalizedMessage());
		}

		SimpleCard card = new SimpleCard();
		card.setTitle("Toss value");
		card.setContent(speechText);

		PlainTextOutputSpeech speech = new PlainTextOutputSpeech();
		speech.setText(speechText);

		SpeechletResponse response = SpeechletResponse.newTellResponse(speech, card);
		return response;
	}

	public String prepareTossIntent(String user_id) {
		log.info("Entered into prepareTossIntent :: user_id : "+ user_id);
		String speechText = "";
		try {
			JGatewayToAlexa gatewayToAlexa = gatewayService.getJGatewayToAlexa(user_id);
			
			if( gatewayToAlexa == null ) {
				return AlexaUtils.WC_ADD_DEVICE_TEXT;
			}
				
			
			GatewayStatus gatewayStatus = gatewayService.getGatewayStatusByMeid( gatewayToAlexa.getMeid() );
			
			if( gatewayStatus == null ) {
				return AlexaUtils.WC_ADD_DEVICE_TEXT;
			}
			
			long activeTimeInMillis = custom_ping_duration_alexa * 60 * 1000;
			
			long timeDiff= _helper.formatDate( _helper.getCurrentTimeinUTC() ).getTime() - _helper.formatDate( gatewayStatus.getLast_online_updated_on() ).getTime();
	    	
	    	if( activeTimeInMillis < timeDiff  ) 
	    		return AlexaUtils.OFFLINE_TEXT;
	    	
	    	
			
			if( gatewayStatus.isIs_online() ) {
				
//				String meid = gatewayStatus.getMeid();
//				
//				Map<String, Object> event_info = new HashMap<>();
//				event_info.put("event", WCConstants.TOSS);
//				event_info.put("value", 1);
//				
//				CommonPkt commonPkt = new CommonPkt();
//				commonPkt.setUser_id( user_id );
//				commonPkt.setMeid( meid );
//				commonPkt.setTime( String.valueOf( _helper.getCurrentTimeinMillis(false, 0) ));
//				commonPkt.setFrom_mobile( false );
//				commonPkt.setType( WCConstants.ACTION );
//				commonPkt.setEvent_id( pktService.generateEventId(meid) );
//				commonPkt.setEvent_info( event_info );
//				commonPkt.setEvent_add( _helper.createEventAdd( meid ) );
//				
//				pktService.savePkt(commonPkt);
//				
//				if( Server.socketServer != null ) {
//					Server.socketServer.getBroadcastOperations().sendEvent( WCConstants.TOSS+"_"+ meid, commonPkt);
//				}		
				speechText = AlexaUtils.TREAT_TEXT;	
				
				String url = wagglecamlistener_url + alexa_toss_url;
				HttpResponse response = httpRequest.httpReq("alexa toss request", "POST", url, null, null);
				
			} else {
				speechText = AlexaUtils.OFFLINE_TEXT;
			}
		} catch (Exception e) {
			log.error("error in prepareTossIntent :: Error : "+ e.getLocalizedMessage());
			speechText = AlexaUtils.ERROR_TEXT;
		}
		return speechText;
	}

}
