package com.nimble.irisservices.intenthandlerimpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.amazon.speech.slu.Intent;
import com.amazon.speech.speechlet.IntentRequest;
import com.amazon.speech.speechlet.Session;
import com.amazon.speech.speechlet.SpeechletException;
import com.amazon.speech.speechlet.SpeechletResponse;
import com.amazon.speech.ui.PlainTextOutputSpeech;
import com.amazon.speech.ui.SimpleCard;
import com.nimble.irisservices.Util.AlexaUtils;
import com.nimble.irisservices.dto.JAssetLastReportV4;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.intenthandlerservice.IntentHandler;
import com.nimble.irisservices.service.IAlertCfgServiceV4;
import com.nimble.irisservices.service.IAlexaService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IReportServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;

@Component
public class TemperatureIntentHandler implements IntentHandler {	
	
	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	IReportServiceV4 reportServiceV4;

	@Autowired
	IReportService reportService;

	@Autowired
	IAlexaService AlexaService;

	@Autowired
	@Lazy
	IAsyncService async;

	@Autowired
	@Lazy
	IAlertCfgServiceV4 alertCfgServiceV4;
	
	private static final Logger log = LogManager.getLogger(TemperatureIntentHandler.class);

	@Override
	public SpeechletResponse handleIntent(Intent intent, IntentRequest request, Session session, String userid) {
		log.info("Entered into handleIntent ::: ");
		String speechText = "";
		try {

			if (intent == null) {
				try {
					throw new SpeechletException("Unrecognized intent");
				} catch (SpeechletException e) {
					log.error("Unrecognized intent : " + e.getMessage());
				}
			}

			speechText = getTemperatureText(userid);

		} catch (Exception e) {
			speechText = "OOPS! something was not right ";
			log.error("Error While Getting temperature info : "+e.getLocalizedMessage());
		}

		SimpleCard card = new SimpleCard();
		card.setTitle("temperature value");
		card.setContent(speechText);

		PlainTextOutputSpeech speech = new PlainTextOutputSpeech();
		speech.setText(speechText);

		SpeechletResponse response = SpeechletResponse.newTellResponse(speech, card);
		return response;
	}
	
	private String getTemperatureText(String userIdMetaData) {
		log.info("Entered into getTemperatureText");
		String speechText = "Hello,  Welcome to " + AlexaUtils.Skilname + " ";

		try {
			UserV4 user = userServiceV4.verifyAuthV3("id", userIdMetaData);
			speechText = "Hello, " + user.getFirstname() + " " + user.getLastname() + " Welcome to "
					+ AlexaUtils.Skilname;

			Map<String, String> map = new HashMap<String, String>();

			try {
				map = userServiceV4.getUserId_cmpIdByAuth(user.getAuthKey());

			} catch (InvalidAuthoException ex) {
				log.info("InvalidAuthoException : " + ex.getMessage());
			}

			long userId = Long.valueOf(map.get("user_id"));
			long cmp_id = Long.valueOf(map.get("cmp_id"));

			List<JAssetLastReportV4> reportsummmary = reportServiceV4.getLastgatewayreportV4("", "", "", "", userId, "",
					"", map.get("tempunit"),"US");

			if (reportsummmary.size() > 0) {
				speechText = "Hey " + user.getFirstname() + ", The current temperature reported ";
				for (JAssetLastReportV4 rpt : reportsummmary) {

					String tempUnit = map.get("tempunit").equalsIgnoreCase("F") ? "Fahrenheit" : "Celsius";

					speechText += " from the monitor " + rpt.getAssetname() + " is "
							+ rpt.getTemperature() + " " + tempUnit + " as on "
							+ AlexaUtils.spokenDate(rpt.getDatetime()) + ", \r\n";
				}
				speechText = speechText.substring(0, speechText.length() - 4);
			} else {
				speechText = "Sorry! I could not find what you are looking for. Please check with your monitor configuration.";
			}

			log.info("Temperature SpeechText : " + speechText);

		} catch (Exception e) {
			speechText = "OOPS! something was not right ";
			log.error("Error While Getting temperature info : "+e.getLocalizedMessage());
			return speechText;
		}

		return speechText;
	}

}
