package com.nimble.irisservices.intenthandlerimpl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.amazon.speech.slu.Intent;
import com.amazon.speech.speechlet.IntentRequest;
import com.amazon.speech.speechlet.Session;
import com.amazon.speech.speechlet.SpeechletException;
import com.amazon.speech.speechlet.SpeechletResponse;
import com.amazon.speech.ui.PlainTextOutputSpeech;
import com.amazon.speech.ui.SimpleCard;
import com.nimble.irisservices.Util.AlexaUtils;
import com.nimble.irisservices.dto.JAssetLastReportV4;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.intenthandlerservice.IntentHandler;
import com.nimble.irisservices.service.IAlertCfgServiceV4;
import com.nimble.irisservices.service.IAlexaService;
import com.nimble.irisservices.service.IAsyncService;
import com.nimble.irisservices.service.IReportService;
import com.nimble.irisservices.service.IReportServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;

@Component
public class HumidityHandler implements IntentHandler {
	protected Logger log = LoggerFactory.getLogger(HumidityHandler.class);

	@Autowired
	IUserServiceV4 userServiceV4;

	@Autowired
	IReportServiceV4 reportServiceV4;

	@Autowired
	IReportService reportService;

	@Autowired
	IAlexaService AlexaService;

	@Autowired
	@Lazy
	IAsyncService async;

	@Autowired
	@Lazy
	IAlertCfgServiceV4 alertCfgServiceV4;

	@Override
	public SpeechletResponse handleIntent(Intent intent, IntentRequest request, Session session, String userid) {
		log.info("Entered into handleIntent");
		String speechText = "";
		try {
			if (intent == null) {
				try {
					throw new SpeechletException("Unrecognized intent");
				} catch (SpeechletException e) {
					// TODO Auto-generated catch block
					log.info("Unrecognized intent : " + e.getMessage());
				}
			}
			speechText = getHumidityText(userid);

		} catch (Exception e) {
			speechText = "OOPS! something was not right ";
			log.error("Error While Getting Humidity info");
		}

		SimpleCard card = new SimpleCard();
		card.setTitle("Humidity value");
		card.setContent(speechText);

		PlainTextOutputSpeech speech = new PlainTextOutputSpeech();
		speech.setText(speechText);

		SpeechletResponse response = SpeechletResponse.newTellResponse(speech, card);
		return response;

	}

	private String getHumidityText(String userIdMetaData) {
		log.info("Entered into getHumidityText");

		String speechText = "Hello,  Welcome to " + AlexaUtils.Skilname + " ";

		try {

			UserV4 user = userServiceV4.verifyAuthV3("id", userIdMetaData);
			speechText = "Hello, " + user.getFirstname() + " " + user.getLastname() + " Welcome to "
					+ AlexaUtils.Skilname;


			List<JAssetLastReportV4> reportsummmary = reportServiceV4.getLastgatewayreportV4("", "", "", "",
					user.getId(), "", "", "F","US");
			if (reportsummmary.size() > 0) {
				speechText = "Hey " + user.getFirstname() + ", The current Humidity reported ";
				for (JAssetLastReportV4 rpt : reportsummmary) {
					speechText += " from the monitor " + rpt.getAssetname() + " is "
							+ rpt.getHumidity() + " Relative humidity as on " + AlexaUtils.spokenDate(rpt.getDatetime())
							+ ", \r\n";
				}
				speechText = speechText.substring(0, speechText.length() - 4);
			} else {
				speechText = "Sorry! I could not find what you are looking for. Please check with your monitor configuration.";
			}
			log.info("Humidity speechText : " + speechText);

		} catch (Exception e) {
			speechText = "OOPS! something was not right ";
			log.error("Error While Getting Humidity info : "+e.getLocalizedMessage());
			return speechText;
		}

		return speechText;
	}

}
