package com.nimble.irisservices.intenthandlerimpl;

import org.springframework.stereotype.Component;

import com.amazon.speech.slu.Intent;
import com.amazon.speech.speechlet.IntentRequest;
import com.amazon.speech.speechlet.Session;
import com.amazon.speech.speechlet.SpeechletResponse;
import com.amazon.speech.ui.Card;
import com.amazon.speech.ui.PlainTextOutputSpeech;
import com.nimble.irisservices.Util.AlexaUtils;
import com.nimble.irisservices.intenthandlerservice.IntentHandler;

@Component
public class AmazonFallbackIntentHandler implements IntentHandler {

	@Override
	public SpeechletResponse handleIntent(Intent intent, IntentRequest request, Session session,String userid) {

		Card card = AlexaUtils.newCard("Help", AlexaUtils.fallBackHelpText);
		PlainTextOutputSpeech speech = AlexaUtils.newSpeech(AlexaUtils.fallBackHelpText,false);

		return AlexaUtils.newSpeechletResponse(card, speech, session, false);
	}
	
}