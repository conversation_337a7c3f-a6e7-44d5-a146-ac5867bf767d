package com.nimble.irisservices.pojo;


/**
 * <AUTHOR>
 *
 */
public class SendNotifications {
	
	/*Notification Type will be as follows,
	
	Type 1 - Only Text Message
	
	It has only short description and message body. It does not contain the image url
	
	Type 2 :
	
	It has short description and message body and it has image url
	
	Type 3. It has only short description and bannerurl
	
	*/
	
	private long notificationId;
	
	private String notificationType;

	private String shortDescription;

	private String title;

	private String message;

	private String bannerImageUrl;

	private String imageUrl;

	private String createdOn;

	private String expiryOn;
	
	private String hyperLink;
	
	private String sendDate;
	 
	private long monitortype;
	
	private String motion_detection_img = "NA";
	
	private boolean is_motion_alert = false;
	
	private long gateway_id = 0;
	
	private String meid = "NA";
	
	private long push_notification_status_id = 0;
	
	private String senddateformate = "NA";
	
	private boolean is_video_alert = false;

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getBannerImageUrl() {
		return bannerImageUrl;
	}

	public void setBannerImageUrl(String bannerImageUrl) {
		this.bannerImageUrl = bannerImageUrl;
	}

	public String getImageUrl() {
		return imageUrl;
	}

	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}

	public String getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}

	public String getExpiryOn() {
		return expiryOn;
	}

	public void setExpiryOn(String expiryOn) {
		this.expiryOn = expiryOn;
	}

	public String getNotificationType() {
		return notificationType;
	}

	public void setNotificationType(String notificationType) {
		this.notificationType = notificationType;
	}



	public long getNotificationId() {
		return notificationId;
	}

	public void setNotificationId(long notificationId) {
		this.notificationId = notificationId;
	}

	public String getSendDate() {
		return sendDate;
	}

	public void setSendDate(String sendDate) {
		this.sendDate = sendDate == null ? null : sendDate.substring(0, 19);;
	}

	public String getHyperLink() {
		return hyperLink;
	}

	public void setHyperLink(String hyperLink) {
		this.hyperLink = hyperLink;
	}

	public String getShortDescription() {
		return shortDescription;
	}

	public void setShortDescription(String shortDescription) {
		this.shortDescription = shortDescription;
	}
	
	

	public long getMonitortype() {
		return monitortype;
	}

	public void setMonitortype(long monitortype) {
		this.monitortype = monitortype;
	}

	public String getMotion_detection_img() {
		return motion_detection_img;
	}

	public void setMotion_detection_img(String motion_detection_img) {
		this.motion_detection_img = motion_detection_img;
	}

	public boolean isIs_motion_alert() {
		return is_motion_alert;
	}

	public void setIs_motion_alert(boolean is_motion_alert) {
		this.is_motion_alert = is_motion_alert;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public long getPush_notification_status_id() {
		return push_notification_status_id;
	}

	public void setPush_notification_status_id(long push_notification_status_id) {
		this.push_notification_status_id = push_notification_status_id;
	}

	public String getSenddateformate() {
		return senddateformate;
	}

	public void setSenddateformate(String senddateformate) {
		this.senddateformate = senddateformate;
	}

	public boolean isIs_video_alert() {
		return is_video_alert;
	}

	public void setIs_video_alert(boolean is_video_alert) {
		this.is_video_alert = is_video_alert;
	}

}
