package com.nimble.irisservices.pojo;


public class FCMNofitication {

	private String to ;

	private String priority;
	
	private JSendNotifications data;
	
	private  CloudNotifications notification;
	
	private boolean  content_available=true;
	
	private boolean  mutable_content=true;

	public String getTo() {
		return to;
	}

	public void setTo(String to) {
		this.to = to;
	}

	public String getPriority() {
		return priority;
	}

	public void setPriority(String priority) {
		this.priority = priority;
	}

	public JSendNotifications getData() {
		return data;
	}

	public void setData(JSendNotifications data) {
		this.data = data;
	}

	public CloudNotifications getNotification() {
		return notification;
	}

	public void setNotification(CloudNotifications notification) {
		this.notification = notification;
	}

	public boolean isContent_available() {
		return content_available;
	}

	public void setContent_available(boolean content_available) {
		this.content_available = content_available;
	}

	public boolean isMutable_content() {
		return mutable_content;
	}

	public void setMutable_content(boolean mutable_content) {
		this.mutable_content = mutable_content;
	}

	public FCMNofitication(String to, String priority, JSendNotifications data, boolean content_available,
			boolean mutable_content) {
		super();
		this.to = to;
		this.priority = priority;
		this.data = data;
		this.content_available = content_available;
		this.mutable_content = mutable_content;
	}

	public FCMNofitication(String to, String priority, JSendNotifications data, CloudNotifications notification,
			boolean content_available, boolean mutable_content) {
		super();
		this.to = to;
		this.priority = priority;
		this.data = data;
		this.notification = notification;
		this.content_available = content_available;
		this.mutable_content = mutable_content;
	}

	public FCMNofitication() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	
}
