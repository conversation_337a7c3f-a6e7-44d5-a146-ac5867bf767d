package com.nimble.irisservices.pojo;

/**
 * <AUTHOR>
 *
 */
public class JSendNotifications {

	/*
	 * Notification Type will be as follows,
	 * 
	 * Type 1 - Only Text Message
	 * 
	 * It has only short description and message body. It does not contain the image
	 * url
	 * 
	 * Type 2 :
	 * 
	 * It has short description and message body and it has image url
	 * 
	 * Type 3. It has only short description and bannerurl
	 * 
	 */
	private String source;

	private String shortDescription;

	private String title;

	private String imageUrl;

	private long reminderViewId;

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getImageUrl() {
		return imageUrl;
	}

	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getShortDescription() {
		return shortDescription;
	}

	public void setShortDescription(String shortDescription) {
		this.shortDescription = shortDescription;
	}

	public long getReminderViewId() {
		return reminderViewId;
	}

	public void setReminderViewId(long reminderViewId) {
		this.reminderViewId = reminderViewId;
	}

}
