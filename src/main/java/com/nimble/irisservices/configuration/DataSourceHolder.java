package com.nimble.irisservices.configuration;

import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

public class DataSourceHolder {
	
	private Map<String, DataSource> dataSources;
	
	@Autowired
	private ApplicationContext applicationContext;
	
	public DataSourceHolder() {
	}	
	
	private void init() {		
		dataSources = applicationContext.getBeansOfType(DataSource.class);	
	}	
	
	public Map<String, DataSource> getListOfDataSources() {
		return dataSources;
	}
}
