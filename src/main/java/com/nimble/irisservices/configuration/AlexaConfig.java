package com.nimble.irisservices.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.amazon.speech.speechlet.servlet.SpeechletServlet;
import com.nimble.irisservices.intenthandlerservice.HandlerSpeechlet;

@Configuration
public class AlexaConfig {
	
	@Autowired
	private HandlerSpeechlet handlerSpeechlet;	

	@Bean
	public ServletRegistrationBean registerServlet() {
		SpeechletServlet speechletServlet = new SpeechletServlet();
		speechletServlet.setSpeechlet(handlerSpeechlet);
		ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean(speechletServlet, "/alexawaggle");
		return servletRegistrationBean;
	}
}
