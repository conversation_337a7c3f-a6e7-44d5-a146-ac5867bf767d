package com.nimble.irisservices.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import springfox.documentation.builders.*;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@EnableSwagger2
@Configuration
public class SwaggerConfig extends WebMvcConfigurerAdapter {

    @Value("${service.url}")
    private String baseUrl;

    @Value("${swagger.enable}")
    private boolean swagger_enable;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");

        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }


    private List<RequestParameter> getCustomHeaders() {
        List<RequestParameter> globalRequestParameters = new ArrayList<>();
        RequestParameterBuilder customHeadersAuth = new RequestParameterBuilder();
        customHeadersAuth.name("auth")
                //.description("")
                .in(ParameterType.HEADER)
                .required(false)
                .build();
        RequestParameterBuilder customHeadersBacking = new RequestParameterBuilder();
        customHeadersBacking.name("backing")
                //.description("")
                .in(ParameterType.HEADER)
                .required(false)
                .build();
        globalRequestParameters.add(customHeadersAuth.build());
        globalRequestParameters.add(customHeadersBacking.build());
        return globalRequestParameters;
    }

    @Bean
    public Docket api() {

        return new Docket(DocumentationType.SWAGGER_2)
                .enable(swagger_enable)
                .ignoredParameterTypes(Authentication.class)
                .globalRequestParameters( getCustomHeaders() )
                .select()
                .apis(RequestHandlerSelectors.any())
                .paths(PathSelectors.any())
                .build()
                .securitySchemes(Arrays.asList(securityScheme()))
                .securityContexts(Arrays.asList(securityContext()));
    }

    private SecurityScheme securityScheme() {

        String url = baseUrl + "/web/oauth/token";

        GrantType grantType2 = new ResourceOwnerPasswordCredentialsGrant(url);

        SecurityScheme oauth = new OAuthBuilder().name("spring_oauth")
                .grantTypes(Arrays.asList(grantType2))
                .scopes(Arrays.asList(scopes()))
                .build();

        return oauth;
    }

    private SecurityContext securityContext() {
        return SecurityContext.builder()
                .securityReferences(
                        Arrays.asList(new SecurityReference("spring_oauth", scopes())))
                .forPaths(PathSelectors.any())
                .build();
    }

    private AuthorizationScope[] scopes() {
        AuthorizationScope[] scopes = {
                new AuthorizationScope("read", "for read operations"),
        };
        return scopes;
    }

}
