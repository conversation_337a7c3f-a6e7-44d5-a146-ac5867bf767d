package com.nimble.irisservices.WCWebcontroller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.irisservices.dto.JResponse;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.exception.InvalidAuthoException;
import com.nimble.irisservices.helper.Helper;
import com.nimble.irisservices.service.IUserServiceV4;

@RestController
@RequestMapping("/web/wc")
public class WCUserController {
	
	private static final Logger log = LogManager.getLogger(WCUserController.class);
	
	@Autowired
	IUserServiceV4 userServiceV4;
	
	@Autowired
	Helper _helper;
	
	// Used in web
	@RequestMapping(value = "v5.0/userlistbyfilter/", method = RequestMethod.GET, headers = "Accept=application/json")
	public @ResponseBody JResponse userlistbyfilter(@RequestHeader HttpHeaders header, Authentication authentication,
			@RequestParam(value = "ukey", required = false) String uKey,
			@RequestParam(value = "uvalue", required = false) String uValue,
			@RequestParam(value = "ftype", defaultValue = "equal", required = false) String fType,
			@RequestParam(value = "offset") long offset, @RequestParam(value = "limit") long limit,
			@RequestParam(value = "otype", defaultValue = "asc", required = false) String otype,
			@RequestParam(value = "okey", required = false) String oKey) {

		String autho = header.getFirst("auth");
		log.info("Entered into userlistbyfilter : " + uKey + " - " + uValue);
		JResponse response = new JResponse();
		UserV4 user = null;
		try {
			try {
				user = userServiceV4.verifyAuthV3("authkey", autho);
			} catch (InvalidAuthoException e) {
				log.error("Invalid Authkey :" + e.getLocalizedMessage());
				response.put("Status", 0);
				response.put("Msg", "Invalid Authkey");
				response.put("Error", e.getLocalizedMessage());
				return response;
			}
//				JResponse errResponse = _helper.validateUser(user.getUsername(), authentication.getName());
//				if (errResponse != null) {
//					return errResponse;
//				}

			response = userServiceV4.getuserlistbyfilter(uKey, uValue, fType, otype, offset, limit, oKey, response);

			return response;

		} catch (Exception e) {
			response.put("Status", 0);
			response.put("Msg", "Exception while getting userlistbyfilter");
			response.put("Error", e.getLocalizedMessage());
			log.error("Exception : userlistbyfilter : " + e.getLocalizedMessage());
		}
		return response;
	}

}
