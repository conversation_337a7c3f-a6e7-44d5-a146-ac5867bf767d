package com.nimble.irisservices;

import javax.annotation.PreDestroy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication
@RestController
@ComponentScan({ "com.nimble.irisservices.helper.AdvanceLogHelper", "com.nimble.irisservices",
		"com.nimble.irisservices.dao", "com.nimble.irisservices.service", "com.nimble.irisservices.entity",
		"com.nimble.irisservices.controller", "com.nimble.irisservices.configuration", "com.nimble.irisservices.cors",
		"com.nimble.irisservices.quartz" })
//@ComponentScan(basePackages ="com.nimble.irisservices,com.nimble.irisservices.dao,com.nimble.irisservices.service,com.nimble.irisservices.entity,com.nimble.irisservices.controller")
//@ComponentScan({"com.nimble.irisservices.*"})
//@Configuration
@EnableAutoConfiguration(exclude = { org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration.class })
@ImportResource("classpath:spring.xml")
@EnableAsync
public class Application extends SpringBootServletInitializer {

//	@Value("${verificationAPIURL}")
//	private String verificationAPIURL ;

	@Autowired
	public static ApplicationContext context;

	public static void main(String[] args) {
		System.setProperty("Log4jContextSelector", "org.apache.logging.log4j.core.async.AsyncLoggerContextSelector");
		// SpringApplication.run(Application.class, args);
		context = SpringApplication.run(Application.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(Application.class);
	}

//    @Bean
//     public TaskExecutor workExecutor() {
//        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
//        threadPoolTaskExecutor.setThreadNamePrefix("Async-");
//        threadPoolTaskExecutor.setCorePoolSize(3);
//        threadPoolTaskExecutor.setMaxPoolSize(3);
//        threadPoolTaskExecutor.setQueueCapacity(600);
//        threadPoolTaskExecutor.afterPropertiesSet();
//        logger.info("ThreadPoolTaskExecutor set");
//        return threadPoolTaskExecutor;
//    }

	
	@PreDestroy
	public void exit() {
		System.out.println("exit called");
		System.gc();
	}
	
}
