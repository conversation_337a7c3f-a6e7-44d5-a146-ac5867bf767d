package com.nimble.irisservices.Util;

import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;

import java.security.KeyFactory;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class JwtKeyUtil {

    private static final Logger log = LogManager.getLogger(JwtKeyUtil.class);

    private JwtKeyUtil() {}

    public static RSAPublicKey loadPublicKey(String base64PublicKey){

        log.info("Entered into loadPublicKey");
        try {
            byte[] decoded = Base64.getDecoder().decode(base64PublicKey);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decoded);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");

            return (RSAPub<PERSON>Key) keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            log.error("Error while loading public key: " + e.getLocalizedMessage());
        }

        return null;
    }

    public static RSAPrivateKey loadPrivateKey(String base64PrivateKey) {

        log.info("Entered into loadPrivateKey");
        try {
            byte[] decoded = Base64.getDecoder().decode(base64PrivateKey);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decoded);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");

            return (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            log.error("Error while loading private key: " + e.getLocalizedMessage());
        }

        return null;
    }
}
