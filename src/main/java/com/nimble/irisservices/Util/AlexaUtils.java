package com.nimble.irisservices.Util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.amazon.speech.speechlet.Session;
import com.amazon.speech.speechlet.SpeechletResponse;
import com.amazon.speech.ui.Card;
import com.amazon.speech.ui.PlainTextOutputSpeech;
import com.amazon.speech.ui.Reprompt;
import com.amazon.speech.ui.StandardCard;

public final class AlexaUtils {

	protected static final String SESSION_CONVERSATION_FLAG = "conversation";

	public static final String onLaunchText = "I can help you know the real-time temperature and humidity of your environment from anywhere, anytime. What would you like to know?";
	public static final String RepromptText = "What else can I tell you?  Say \"Help\" for some suggestions.";
	public static final String Skilname = "waggle";
	public static final String HelpText = "waggle can help you know the real-time temperature and humidity of your environment from anywhere, anytime. What would you like to ask?"; 
	public static final String fallBackHelpText = "Whoops! that was unexpected. You can ask for information like the temperature and humidity of your environment. What would you like to know?"; 

	
	public static final String ERROR_TEXT = "OOPS! something was not right" ;
	
	public static final String OFFLINE_TEXT = "OOPS!, your WaggleCam is offline. Please check your WaggleCam network!";
	
	public static final String DEVICE_LIVE_TEXT = "Your WaggleCam is live!";
	
	public static final String DEVICE_NOT_LIVE_TEXT = "Your WaggleCam is not live!";
	
	public static final String TREAT_TEXT = "Your treat is tossed!";
	
	public static final String WC_ADD_DEVICE_TEXT = "No WaggleCam found in your account!" ;
	
	
	public static Card newCard(String cardTitle, String cardText) {

		//A standard card that includes a title, a text content and an image.
		StandardCard card = new StandardCard();
		card.setTitle((cardTitle == null) ? "waggle" : cardTitle);
		card.setText(cardText);	
		return card;
	}
	
	public static PlainTextOutputSpeech newSpeech(String speechText, boolean appendRepromptText) {
		PlainTextOutputSpeech speech = new PlainTextOutputSpeech();
		speech.setText(appendRepromptText ? speechText + "\n\n" + AlexaUtils.RepromptText : speechText);
		return speech;
	}
	
	public static SpeechletResponse newSpeechletResponse(Card card, PlainTextOutputSpeech speech, Session session,
			boolean shouldEndSession) {

		// Say it...
		if (AlexaUtils.inConversationMode(session) && !shouldEndSession) {
			PlainTextOutputSpeech repromptSpeech = new PlainTextOutputSpeech();
			repromptSpeech.setText(AlexaUtils.RepromptText);

			Reprompt reprompt = new Reprompt();
			reprompt.setOutputSpeech(repromptSpeech);

			return SpeechletResponse.newAskResponse(speech, reprompt, card);
		} else {
			return SpeechletResponse.newTellResponse(speech, card);
		}
	}
	
	public static boolean inConversationMode(Session session) {
		return session.getAttribute(SESSION_CONVERSATION_FLAG) != null;
	}
	
	public static void setConversationMode(Session session, boolean conversationMode) {
		if (conversationMode)
			session.setAttribute(SESSION_CONVERSATION_FLAG, "true");
		else
			session.removeAttribute(SESSION_CONVERSATION_FLAG);
	}
	
	public static String spokenDate(String dbdate) {

		SimpleDateFormat dbfmt = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
		Date date = null;
		try {
			date = dbfmt.parse(dbdate);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
		}

		SimpleDateFormat dateFormat = new SimpleDateFormat("MMMM d");
		SimpleDateFormat timeForamt = new SimpleDateFormat("hh:mm a");

		return dateFormat.format(date)+" at "+timeForamt.format(date);
	}
}
