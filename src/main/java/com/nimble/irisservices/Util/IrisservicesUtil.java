package com.nimble.irisservices.Util;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;
import java.util.TimeZone;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.nimble.irisservices.constant.IrisservicesConstants;

public class IrisservicesUtil {
	private static final Logger log = LogManager.getLogger(IrisservicesUtil.class);
	public static String getCurrentTimeUTC() {
		Calendar calendar = Calendar.getInstance();
		Date d = calendar.getTime();

		SimpleDateFormat df = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
		df.setTimeZone(TimeZone.getTimeZone(IrisservicesConstants.UTCFORMAT));

		String currentTime = df.format(d);
		return currentTime;
	}
	
	public static String getCurrentDateTime(String timeFormat, String timezone) {
		Calendar calendar = Calendar.getInstance();
		Date d = calendar.getTime();
		SimpleDateFormat df = new SimpleDateFormat(timeFormat);
		df.setTimeZone(TimeZone.getTimeZone(timezone));
		String currentTime = df.format(d);
		return currentTime;
	}

	public static String getCurrentDateTime(String timeFormat, String timezone, int days) {
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, days);
		Date d = calendar.getTime();
		SimpleDateFormat df = new SimpleDateFormat(timeFormat);
		df.setTimeZone(TimeZone.getTimeZone(timezone));
		String currentTime = df.format(d);
		return currentTime;
	}

	/**
	 * 
	 * returns the dateTime string in "yyyy-mm-dd HH:mm:ss" format 
	 * 
	 * @param date - in ddmmyy format
	 * @param time - in HHmmss format
	 * @return  the combined date and time parameters in "yyyy-mm-dd HH:mm:ss" format
	 */
	public static String getDateTime(String date, String time)	{
		String  dateYYYYMMDD;   
		String  dateTime;
		dateYYYYMMDD = new String("20" + date.substring(4, 6) + "-" + date.substring(2, 4) + "-" + date.substring(0, 2));
		dateTime     = new String(dateYYYYMMDD + " " + time.substring(0, 2) + ":" + time.substring(2, 4) + ":" + time.substring(4, 6));
		return dateTime;
	}
	
	public static Timestamp getDateTime_TS(String dateTime) {
		return Timestamp.valueOf(dateTime);
	}

	public static String getDate(String date) {
		String  newdate = new String("20" + date.substring(4, 6) + "-" + date.substring(2, 4) + "-" + date.substring(0, 2));
		return newdate;
	}

	public static String getTime(String time) {   
		String  newtime  = new String(time.substring(0, 2) + ":" + time.substring(2, 4) + ":" + time.substring(4, 6));
		return newtime;
	}

	public static String getUTCTime(String datetime, String timeZoneOffset){

		SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);

		formatter.setTimeZone(TimeZone.getTimeZone("GMT" + timeZoneOffset));

		Date date = null;
		try {
			date = formatter.parse(datetime);
			SimpleDateFormat dateFormat = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
			dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
			return dateFormat.format(date);
		} catch (ParseException e) {
			return null;
		}
	}

	public static String AddCurrentDateTimev2(String timeFormat, String days, int sec, int hour, int day,
			String intimezone, String outtimezone) {
		SimpleDateFormat df = new SimpleDateFormat(timeFormat);

		Date date;
		try {
			date = df.parse(days);
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);

			calendar.add(Calendar.SECOND, sec);
			calendar.add(Calendar.HOUR, hour);
			calendar.add(Calendar.DATE, day);

			Date d = calendar.getTime();
			String currentTime = df.format(d);
			return getDateime_Timezone(currentTime, intimezone, outtimezone);

		} catch (ParseException e) {
			return null;
		}
	}

	public static long calcTimeDiffInMillis(String currTime, String prevTime) {
		long timeDiff = 0;
		try {
			SimpleDateFormat format = new SimpleDateFormat(IrisservicesConstants.TIMEFORMAT);
			Date prevDate = format.parse(prevTime);
			Date currDate = format.parse(currTime);
			if(currDate.getTime() > prevDate.getTime()) {
				timeDiff = currDate.getTime() - prevDate.getTime();
			}
		} catch (ParseException e) {
		}
		return timeDiff;
	}
	
	public static String getDateime_Timezone(String datetime, String inTimeZoneOffset, String outTimeZoneOffset){

		SimpleDateFormat formatter = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
		formatter.setTimeZone(TimeZone.getTimeZone("GMT" + inTimeZoneOffset));
		Date date = null;
		try {
			date = formatter.parse(datetime);			
			SimpleDateFormat dateFormat = new SimpleDateFormat(IrisservicesConstants.DATETIMEFORMAT);
			dateFormat.setTimeZone(TimeZone.getTimeZone("GMT" +outTimeZoneOffset));
			return dateFormat.format(date);
		} catch (ParseException e) {
			return null;
		}
	}
	
	
	
	public static long calDaysDiff(String startDate, String endDate) {
		//Parsing the date
		LocalDate dateBefore = LocalDate.parse(startDate);
		LocalDate dateAfter = LocalDate.parse(endDate);
		//calculating number of days in between
		long noOfDaysBetween = ChronoUnit.DAYS.between(dateBefore, dateAfter);
		return noOfDaysBetween;
	}

	public static Boolean ischeckcurrentdate(String dataformat, String date) {
		SimpleDateFormat df = new SimpleDateFormat(dataformat);
		Date date1;
		try {
			date1 = df.parse(date);
			Calendar calendar = Calendar.getInstance();
			Date currdate = calendar.getTime();
			System.out.println(date1.getTime()+"-"+currdate.getTime());
			System.out.println(date1.getTime()<currdate.getTime());
			System.out.println(date1.getDate()+"=="+currdate.getDate());
			if(date1.getDate()==currdate.getDate())
				return true;
			return false;
		} catch (ParseException e) {
			return false;
		}

	}
	
	public static String AddCurrentDateTime(String timeFormat, int hour, String days) {
		SimpleDateFormat df = new SimpleDateFormat(timeFormat+" HH:mm:ss");
		
		Date date;
		try {
			date = df.parse(days);
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			calendar.add(Calendar.HOUR, hour);
			Date d = calendar.getTime();
			String currentTime = df.format(d);
			return currentTime;
		} catch (ParseException e) {
			return null;
		}
	}
	
	
	public static String AddCurrentDateTimesec(String timeFormat, String days) {
		SimpleDateFormat df = new SimpleDateFormat(timeFormat+" HH:mm:ss");
		
		Date date;
		try {
			date = df.parse(days);
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			calendar.add(Calendar.SECOND, 1);
			Date d = calendar.getTime();
			String currentTime = df.format(d);
			return currentTime;
		} catch (ParseException e) {
			return null;
		}
	}
	
	public static  String getUtcDateTime() {

		Calendar currDateCal = Calendar.getInstance();
		currDateCal.setTimeZone(TimeZone.getTimeZone("UTC"));
		currDateCal.setTime(new Date(System.currentTimeMillis()));

		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");         
		dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

		String utcDateTime = dateFormat.format(currDateCal.getTime());
		return utcDateTime;
	}
	
	public static long getAge(Date birth_date, String type) {
		long age = 0;
		Calendar currDateCal = Calendar.getInstance();
		currDateCal.setTimeZone(TimeZone.getTimeZone("UTC"));
		Date curDate = currDateCal.getTime();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-mm-dd");
		String strCurrdate = sdf.format(curDate);
		try {
			curDate = sdf.parse(strCurrdate);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			System.out.println(e.getMessage());
		}

		Calendar birthDateCal = Calendar.getInstance();
		birthDateCal.setTimeZone(TimeZone.getTimeZone("UTC"));
		birthDateCal.setTime(birth_date);

		int diffYear = currDateCal.get(Calendar.YEAR) - birthDateCal.get(Calendar.YEAR);
		int monthsdiff = currDateCal.get(Calendar.MONTH) - birthDateCal.get(Calendar.MONTH);
		int totMonthsDiff = diffYear * 12 + monthsdiff;

		long diffdays = curDate.getTime() - birthDateCal.getTime().getTime();
		if(type.equalsIgnoreCase("YEAR"))
			age = diffYear;
		else if(type.equalsIgnoreCase("MONTH"))
			age = totMonthsDiff;
		else if(type.equalsIgnoreCase("DAYS"))
			age = diffdays;

		log.info("getAge: returns null,diffYear = " + diffYear + " monsdiff = " + totMonthsDiff + " diffdays = "
					+ diffdays);
		return age;

	}
	
	public static double calculateDistance( double longitude1, double latitude1, 
  		  double longitude2, double latitude2) {
		double c =  Math.sin(Math.toRadians(latitude1)) *
        		Math.sin(Math.toRadians(latitude2)) +
        		Math.cos(Math.toRadians(latitude1)) *
        		Math.cos(Math.toRadians(latitude2)) *
        		Math.cos(Math.toRadians(longitude2) - 
                Math.toRadians(longitude1));
		c = c > 0 ? Math.min(1, c) : Math.max(-1, c);
		return 3959 * 1.609 * 1000 * Math.acos(c); //in meter
	    
	}
	
	public static int checkTimeDiff(String datetime) {
		Calendar userDateCal = Calendar.getInstance();
		userDateCal.setTimeZone(TimeZone.getTimeZone("UTC"));
		
		Date currDate = userDateCal.getTime();
		userDateCal.setTime(currDate);
		userDateCal.add(Calendar.DATE, 1);
		
		Date userDate = userDateCal.getTime();
		
		return currDate.compareTo(userDate);
	}
	
	public static  String getUtcDate() {

		Calendar currDateCal = Calendar.getInstance();
		currDateCal.setTimeZone(TimeZone.getTimeZone("UTC"));
		currDateCal.setTime(new Date(System.currentTimeMillis()));

		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");         
		dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

		String utcDateTime = dateFormat.format(currDateCal.getTime());
		return utcDateTime;
	}

	public static String generateOtp() {

		Random random = new Random();
		int number = random.nextInt(10000); // generates 0 to 9999

		return String.format("%04d", number); // ensures 4 digits with leading zeros
	}
}
