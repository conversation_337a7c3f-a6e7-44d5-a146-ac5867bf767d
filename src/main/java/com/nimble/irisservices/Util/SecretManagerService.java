package com.nimble.irisservices.Util;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * Service class responsible for managing secrets using AWS Secrets Manager.
 * Caches retrieved secrets for a configurable amount of time using Caffeine cache.
 */
@Service
public class SecretManagerService {

    private static final Logger log = LogManager.getLogger(SecretManagerService.class);

    /** Cache expiration time in minutes, configured via application properties. */
    @Value("${aws_sm_cache_expire_minutes}")
    private int cacheTtlMinutes;

    private static final String REGION = "us-west-2";

    /** Local cache to store secrets temporarily for performance optimization. */
    private Cache<String, String> secretCache;

    /**
     * Initializes the Caffeine cache with expiration and size constraints.
     * This method is invoked automatically after dependency injection.
     */
    @PostConstruct
    public void initCache() {
        secretCache = Caffeine.newBuilder()
                .expireAfterWrite(cacheTtlMinutes, TimeUnit.MINUTES)
                .maximumSize(100)
                .build();
    }

    /**
     * Retrieves a secret value for a given key.
     * First checks the local cache, and if not present, loads it from AWS Secrets Manager.
     *
     * @param secretName   Name of the secret in AWS Secrets Manager.
     * @param requestedKey Key within the secret JSON to retrieve.
     * @return Value of the requested key, or an empty string if retrieval fails.
     */
    public String getSecretValue(String secretName, String requestedKey) {

        String value = secretCache.get(requestedKey, key -> loadSecretFromAWS(secretName, key));

        log.info("Loaded from AWS Secret manager {}",requestedKey);

        return value;
    }

    /**
     * Loads a secret from AWS Secrets Manager and parses its JSON to extract the desired key.
     *
     * @param secretName   Name of the AWS secret.
     * @param requestedKey Key to extract from the secret payload.
     * @return Secret value corresponding to the requested key, or an empty string if an error occurs.
     */
    private String loadSecretFromAWS(String secretName, String requestedKey) {

        log.info("Loading secret key from AWS: {}", requestedKey);

        try (SecretsManagerClient secretsClient = SecretsManagerClient.builder()
                .region(Region.of(REGION))
                .build()) {

            GetSecretValueRequest request = GetSecretValueRequest.builder()
                    .secretId(secretName)
                    .build();

            GetSecretValueResponse response = secretsClient.getSecretValue(request);

            JSONObject secretJson = new JSONObject(response.secretString());

            return secretJson.getString(requestedKey);
        } catch (Exception e) {
            log.error("Failed to load secret key from AWS: {}, error {}", requestedKey, e);
        }

        return "";
    }
}
