package com.nimble.irisservices.interceptor;

import java.net.InetAddress;
import java.time.Instant;
import java.util.Date;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.ModelAndView;

import com.nimble.irisservices.dto.AdvanceLog;
import com.nimble.irisservices.dto.ApiContext;
import com.nimble.irisservices.helper.AdvanceLogHelper;

public class RequestProcessingInterceptor implements HandlerInterceptor {

	@Autowired
	private AdvanceLogHelper _helper;

	private static final Logger LOGGER = LogManager.getLogger(RequestProcessingInterceptor.class);

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {

		//System.out.println("The Url is" + request.getRequestURI());

		long startTime = System.currentTimeMillis();
		Date startDateTime = Date.from(Instant.now());
		LOGGER.info(
				"Request URL::" + request.getRequestURL().toString() + ":: Start Time=" + System.currentTimeMillis());
		request.setAttribute("startTime", startTime);
		InetAddress ip = InetAddress.getLocalHost();
		ApiContext apiContext = new ApiContext();
		// Get server and client address
		apiContext.setRequestUri(request.getRequestURI());

		final Map<String, String> variableValueMap = (Map<String, String>) request
				.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);

		variableValueMap.forEach((k,v)->System.out.println("Key:"+k+"->"+"Value : "+v));
		apiContext.setAuthKey(variableValueMap.get("autho"));

		String[] segments = request.getRequestURI().split("/");
		String api = segments[segments.length-2];
		
		try {
			api = segments[2]+"_"+segments[3]+"_"+request.getMethod();
			apiContext.setApi(api);
		}catch(Exception ex ) {
			api= "genericapi";	
		}

		//		api = segments[2]+"_"+segments[3]+"_"+request.getMethod();
		//		apiContext.setApi(api);

		apiContext.setServerAddress(ip.getHostName() + "/" + ip.getHostAddress());
		apiContext.setInTime(startDateTime);
		apiContext.setAdvanceLog(new AdvanceLog());
		apiContext.setMethod(request.getMethod());
		request.setAttribute("ApiContext", apiContext);
		return true;
	}

	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {

		LOGGER.info("Request URL::" + request.getRequestURL().toString() + " Sent to Handler :: Current Time="
				+ System.currentTimeMillis());
	}

	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
			throws Exception {

		Date endDateTime = Date.from(Instant.now());
		ApiContext apiContext = _helper.getApiContext();
		apiContext.setOutTime(endDateTime);
		apiContext.setElapsedTime(_helper.getTimeDifference(apiContext.getInTime(), apiContext.getOutTime()));

		String req_uri = apiContext.getRequestUri();

		if (apiContext.isAdvanceLog()) {
			if(req_uri.contains("/v3.0/orderchannel") || req_uri.contains("/v3.0/checkwifistatusv2/") 
					|| req_uri.contains("/v3.0/getavailableupgradeplan/") || req_uri.contains("/v3.0/alertV3/") 
					|| req_uri.contains("/v3.0/company/") || req_uri.contains("v3.0/alerttype") 
					|| req_uri.contains("/v3.0/getPetProfile/") || req_uri.contains("/v3.0/getuserleaderboard/")  
					|| req_uri.contains("v3.0/getleaderboard/") || req_uri.contains("/v3.0/updategoalsettings/") 
					|| req_uri.contains("v3.0/getspecies") || req_uri.contains("v3.0/getbreeds")
					|| req_uri.contains("/v3.0/getzipcodedetails") || req_uri.contains("v3.0/userupdate/") 
					|| req_uri.contains("v3.0/pwdupdate/") || req_uri.contains("/v3.0/usersignup") || 
					req_uri.contains("/v3.0/forceUpdate") || req_uri.contains("/v3.0/getUserByUsernameV2") 
					|| req_uri.contains("/v3.0/getsubsplanbymonitortype/") ||
					req_uri.contains("/v3.0/updatesubscriptionplan/") || req_uri.contains("/v3.0/resendverificationlink")
					|| req_uri.contains("/v3.0/userdeviceinfo/") || req_uri.contains("/v3.0/activateuser") 
					|| req_uri.contains("/v3.0/gettemperature/") || req_uri.contains("/v3.0/nimbleemail") 
					|| req_uri.contains("/v3.0/configuredevice/") || req_uri.contains("/v3.0/furbitlastgatewayreport/")
					|| req_uri.contains("/v3.0/furbitreport/")) 
			{
				_helper.updatePerformanceLog2(apiContext);
			}
			else if(req_uri.contains("/v4.0/company/") || req_uri.contains("/v4.0/alertV3") ||
					req_uri.contains("/v4.0/alerttype") || req_uri.contains("/v4.0/getgeneraldata") ||
					req_uri.contains("/v4.0/generatereferrallink/") || req_uri.contains("/v4.0/userV2/") ||  
					req_uri.contains("/v4.0/getPetProfile/")  || req_uri.contains("/v4.0/trendingvideoslist/") || 
					req_uri.contains("/v4.0/orderchannel")  || req_uri.contains("/v4.0/getbreeds") ||
					req_uri.contains("/v4.0/usertoken/")  || req_uri.contains("/v4.0/getUserByUsernameV2")  ||
					req_uri.contains("/v4.0/pwdupdate/") || req_uri.contains("/v4.0/loginV2") 
					|| req_uri.contains("v4.0/usernotifications/") || req_uri.contains("v4.0/updategoalsettings/")
					|| req_uri.contains("/v4.0/userupdate/") || req_uri.contains("/v4.0/updatevideostatus/") 
					|| req_uri.contains("v4.0/userdeviceinfo/") ||req_uri.contains("v4.0/companyconfig/") 
					|| req_uri.contains("v4.0/getAdvertisements") || req_uri.contains("v4.0/getspecies")
					|| req_uri.contains("v4.0/alertcfg/") || req_uri.contains("v4.0/saveorupdatePetProfile") 
					|| req_uri.contains("v4.0/furbitdailyreport/") || req_uri.contains("v4.0/furbitreport") ) {
				_helper.updatePerformanceLog3(apiContext);
			}else if(req_uri.contains("/v3.0/checkqrcexist/") || req_uri.contains("/v3.0/savewifiinfo/") ||
					req_uri.contains("/v3.0/getwifiinfolist/") || req_uri.contains("v3.0/forgetpassword")) {
				_helper.updatePerformanceLog4(apiContext);
			}
			else {
				_helper.updatePerformanceLog(apiContext);
			}

		}

		long startTime = (Long) request.getAttribute("startTime");
		LOGGER.info("Request URL::" + request.getRequestURL().toString() + ":: End Time=" + System.currentTimeMillis());
		LOGGER.info("Request URL::" + request.getRequestURL().toString() + ":: Time Taken in Milliseconds ="
				+ (System.currentTimeMillis() - startTime) + ":: Time Taken in Seconds ="
				+ ((System.currentTimeMillis() - startTime) / 1000.0));
	}
}
