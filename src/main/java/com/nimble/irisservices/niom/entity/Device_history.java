package com.nimble.irisservices.niom.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.validator.constraints.NotEmpty;

@Entity
@Table(name="device_history", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class Device_history implements Serializable{

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@NotEmpty(message ="meid cannot be null")
	@Column(name = "meid")
	private String meid;
	
	@Column(name = "datetime")
	private String datetime = "1753-01-01 00:00:00";

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="current_state_id")
    private Devicestate devicestate;
	@Transient
	private long devicestateid = 7;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name="device_locationid")
    private Devicelocation device_location;
	@Transient
	private long locationid = 1;
	
	public Device_history() {
		super();
	}
	
	public Device_history(long devicestateid,String meid,long locationid,String datetime) {
		super();
		this.devicestateid = devicestateid;
		this.meid = meid;
		this.locationid = locationid;
		this.datetime = datetime;
	}
	
	public long getId() {
		return id;
	}
	
	public void setId(long id) {
		this.id = id;
	}
	
	public String getMeid() {
		return meid;
	}
	
	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getDatetime() {
		return datetime;
	}

	public void setDatetime(String datetime) {
		this.datetime = datetime;
	}

	public Devicestate getDevicestate() {
		return devicestate;
	}

	public void setDevicestate(Devicestate devicestate) {
		this.devicestate = devicestate;
	}

	public Devicelocation getDevice_location() {
		return device_location;
	}

	public void setDevice_location(Devicelocation device_location) {
		this.device_location = device_location;
	}

	public long givedevicestateid() {
		return devicestateid;
	}

	public void setdevicestateid(long devicestateid) {
		this.devicestateid = devicestateid;
	}

	public long givelocationid() {
		return locationid;
	}

	public void setlocationid(long locationid) {
		this.locationid = locationid;
	}

	
}
