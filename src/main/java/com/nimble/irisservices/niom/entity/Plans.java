package com.nimble.irisservices.niom.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name="verizon_plan", uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class Plans implements Serializable{

	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;

	@Column(name = "acc_name")
	private String acc_name;
	
	@Column(name = "service_plan")
	private String service_plan;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getAcc_name() {
		return acc_name;
	}

	public void setAcc_name(String acc_name) {
		this.acc_name = acc_name;
	}

	public String getService_plan() {
		return service_plan;
	}

	public void setService_plan(String service_plan) {
		this.service_plan = service_plan;
	}
}
