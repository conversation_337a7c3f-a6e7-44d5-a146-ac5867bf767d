package com.nimble.irisservices.niom.entity;

import java.io.Serializable;
import java.util.Map;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "orders", uniqueConstraints = @UniqueConstraint(columnNames = { "id", "order_id" }))
public class Orders implements Serializable {

	@Column(name = "id")
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	@Id
	@NotNull
	@Min(1)
	@Column(name = "order_id")
	private long order_id;
	@NotNull
	@Column(name = "woocom_id")
	private long woocom_id;
	/*
	 * @NotNull
	 * 
	 * @Min(0)
	 * 
	 * @Column(name = "order_number") private int order_number = 0;
	 */
//	@NotNull(message="Please enter a date")
//	@DateTimeFormat(pattern="yyyy-MM-dd hh:mm:ss")
	@Column(name = "datetime")
	private String datetime = "1753-01-01 00:00:00";
	@Column(name = "status")
	private String status = "processing";
	@Column(name = "shipping_total")
	private float shipping_total = 0;
	@Column(name = "shipping_tax_total")
	private float shipping_tax_total = 0;
	@Column(name = "fee_total")
	private float fee_total = 0;
	@Column(name = "fee_tax_total")
	private float fee_tax_total = 0;
	@Column(name = "tax_total")
	private float tax_total = 0;
	@Column(name = "discount_total")
	private float discount_total = 0;
	@Column(name = "order_total")
	private float order_total = 0;
	@Column(name = "refunded_total")
	private float refunded_total = 0;
	@Column(name = "order_currency")
	private String order_currency = "NA";
	@Column(name = "payment_method")
	private String payment_method = "NA";
	@Column(name = "shipping_method")
	private String shipping_method = "NA";
	@Column(name = "customer_id")
	private int customer_id = 0;
	@Column(name = "billing_first_name")
	private String billing_first_name = "NA";
	@Column(name = "billing_last_name")
	private String billing_last_name = "NA";
	@Column(name = "billing_company")
	private String billing_company = "NA";
	@Column(name = "billing_email")
	private String billing_email = "NA";
	@Column(name = "billing_phone")
	private String billing_phone = "NA";
	@Column(name = "billing_address_1")
	private String billing_address_1 = "NA";
	@Column(name = "billing_address_2")
	private String billing_address_2 = "NA";
	@Column(name = "billing_postcode")
	private String billing_postcode = "NA";
	@Column(name = "billing_city")
	private String billing_city = "NA";
	@Column(name = "billing_state")
	private String billing_state = "NA";
	@Column(name = "billing_country")
	private String billing_country = "NA";
	@Column(name = "shipping_first_name")
	private String shipping_first_name = "NA";
	@Column(name = "shipping_last_name")
	private String shipping_last_name = "NA";
	@Column(name = "shipping_address_1")
	private String shipping_address_1 = "NA";
	@Column(name = "shipping_address_2")
	private String shipping_address_2 = "NA";
	@Column(name = "shipping_postcode")
	private String shipping_postcode = "NA";
	@Column(name = "shipping_city")
	private String shipping_city = "NA";
	@Column(name = "shipping_state")
	private String shipping_state = "NA";
	@Column(name = "shipping_country")
	private String shipping_country = "NA";
	@Column(name = "shipping_company")
	private String shipping_company = "NA";
	@Column(name = "customer_note")
	private String customer_note = "NA";
	@Column(name = "line_items")
	private String line_items = "NA";
	@Column(name = "shipping_items")
	private String shipping_items = "NA";
	@Column(name = "fee_items")
	private int fee_items = 0;
	@Column(name = "tax_items")
	private int tax_items = 0;
	@Column(name = "coupons")
	private String coupons = "NA";
	@Column(name = "order_notes")
	private String order_notes = "NA";
	@Column(name = "download_permissions_granted")
	private int download_permissions_granted = 0;
	@Column(name = "alert_mobile")
	private String alert_mobile = "NA";
	@Column(name = "alert_email")
	private String alert_email = "NA";
	@Column(name = "low_temp_alert")
	private String low_temp_alert = "NA";
	@Column(name = "high_temp_alert")
	private String high_temp_alert = "NA";
	@Column(name = "mobile_network")
	private String mobile_network = "NA";
	@Column(name = "order_comments")
	private String order_comments = "NA";
	@Column(name = "comments")
	private String comments = "NA";
	@Column(name = "devicemodel")
	private String devicemodel = "NA";
	@Column(name = "order_sku")
	private String order_sku = "NA";
	@Column(name = "quantity")
	private String quantity = "0";

	@Column(name = "pet_name")
	private String pet_name = "NA";

	@Column(name = "provision_status")
	private int provision_status = 0;

	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "order_acc_typeid")
//	@JsonIgnore
	private Order_account order_acc_type;
	@Transient
	private long order_acc_typeid = 4;
	@Column(name = "mapped_date")
	private String mapped_date = "1753-01-01 00:00:00";
	@Column(name = "label")
	private String label = "NA";
	@Column(name = "tracking_num")
	private String tracking_num = "NA";
	@Column(name = "email_status")
	private String email_status = "000";

	@Column(name = "return_units")
	private int return_units = 0;
	/*
	 * 000- No emails sent; 1st bit for 1st welcome mail sent from tool; 2nd bit for
	 * 2nd email sent after provisioning successful for all mapped meids 3rd bit for
	 * 3rd email sent after receiving tracking number
	 */

	@Column(name = "postal_service")
	private int postal_service = 1;
	/*
	 * postal_service 1- USPS 2- UPS 3- Fedex
	 */

	@Column(name = "delivery_status")
	private String delivery_status = "NA";

	@Column(name = "delivered_date")
	private String delivered_date = "1753-01-01 00:00:00";

	@Column(name = "tracking_summary")
	private String tracking_summary;

	@Column(name = "fulfillmentchannel")
	private String fulfillmentchannel = "NA";

	@Column(name = "externalsku")
	private String externalsku = "NA";

	@Column(name = "external_order_id")
	private String external_order_id = "NA";

	@Column(name = "welcome_status")
	private int welcome_status;

	@Column(name = "account_status")
	private String account_status = "NA";

	@Column(name = "account_status_updated_date")
	private String account_status_updated_date = "1753-01-01 00:00:00";

	@Column(name = "tracking_mail_status")
	private int tracking_mail_status = 0;

	@Column(name = "product_category")
	private String product_category = "NA";

	@Transient
	private Map<String, String> orderSkuList;

	public Orders() {
		super();
	}

	public Orders(long order_id, long woocom_id, String datetime, String status, float shipping_total,
			float shipping_tax_total, float fee_total, float fee_tax_total, float tax_total, float discount_total,
			float order_total, float refunded_total, String order_currency, String payment_method,
			String shipping_method, int customer_id, String billing_first_name, String billing_last_name,
			String billing_company, String billing_email, String billing_phone, String billing_address_1,
			String billing_address_2, String billing_postcode, String billing_city, String billing_state,
			String billing_country, String shipping_first_name, String shipping_last_name, String shipping_address_1,
			String shipping_address_2, String shipping_postcode, String shipping_city, String shipping_state,
			String shipping_country, String shipping_company, String customer_note, String line_items,
			String shipping_items, int fee_items, int tax_items, String coupons, String order_notes,
			int download_permissions_granted, String alert_mobile, String alert_email, String low_temp_alert,
			String high_temp_alert, String mobile_network, String order_comments, String comments, String order_sku,
			String devicemodel, String quantity, long order_acc_typeid, String mapped_date, String pet_name,
			String email_status, int provision_status, int postal_service, String externalOrderId,
			String fullFillmentChannel, String externalSku) {
		super();
		this.order_id = order_id;
		this.woocom_id = woocom_id;
		this.datetime = datetime;
		this.status = status;
		this.shipping_total = shipping_total;
		this.shipping_tax_total = shipping_tax_total;
		this.fee_total = fee_total;
		this.fee_tax_total = fee_tax_total;
		this.tax_total = tax_total;
		this.discount_total = discount_total;
		this.order_total = order_total;
		this.refunded_total = refunded_total;
		this.order_currency = order_currency;
		this.payment_method = payment_method;
		this.shipping_method = shipping_method;
		this.customer_id = customer_id;
		this.billing_first_name = billing_first_name;
		this.billing_last_name = billing_last_name;
		this.billing_company = billing_company;
		this.billing_email = billing_email;
		this.billing_phone = billing_phone;
		this.billing_address_1 = billing_address_1;
		this.billing_address_2 = billing_address_2;
		this.billing_postcode = billing_postcode;
		this.billing_city = billing_city;
		this.billing_state = billing_state;
		this.billing_country = billing_country;
		this.shipping_first_name = shipping_first_name;
		this.shipping_last_name = shipping_last_name;
		this.shipping_address_1 = shipping_address_1;
		this.shipping_address_2 = shipping_address_2;
		this.shipping_postcode = shipping_postcode;
		this.shipping_city = shipping_city;
		this.shipping_state = shipping_state;
		this.shipping_country = shipping_country;
		this.shipping_company = shipping_company;
		this.customer_note = customer_note;
		this.line_items = line_items;
		this.shipping_items = shipping_items;
		this.fee_items = fee_items;
		this.tax_items = tax_items;
		this.coupons = coupons;
		this.order_notes = order_notes;
		this.download_permissions_granted = download_permissions_granted;
		this.alert_mobile = alert_mobile;
		this.alert_email = alert_email;
		this.low_temp_alert = low_temp_alert;
		this.high_temp_alert = high_temp_alert;
		this.mobile_network = mobile_network;
		this.order_comments = order_comments;
		this.comments = comments;
		this.order_sku = order_sku;
		this.devicemodel = devicemodel;
		this.quantity = quantity;
		this.order_acc_typeid = order_acc_typeid;
		this.mapped_date = mapped_date;
		this.pet_name = pet_name;
		this.email_status = email_status;
		this.provision_status = provision_status;
		this.postal_service = postal_service;
		this.external_order_id = externalOrderId;
		this.fulfillmentchannel = fullFillmentChannel;
		this.externalsku = externalSku;
	}

	public Orders(long id, long order_id, long woocom_id, String datetime, String status, float shipping_total,
			float shipping_tax_total, float fee_total, float fee_tax_total, float tax_total, float discount_total,
			float order_total, float refunded_total, String order_currency, String payment_method,
			String shipping_method, int customer_id, String billing_first_name, String billing_last_name,
			String billing_company, String billing_email, String billing_phone, String billing_address_1,
			String billing_address_2, String billing_postcode, String billing_city, String billing_state,
			String billing_country, String shipping_first_name, String shipping_last_name, String shipping_address_1,
			String shipping_address_2, String shipping_postcode, String shipping_city, String shipping_state,
			String shipping_country, String shipping_company, String customer_note, String line_items,
			String shipping_items, int fee_items, int tax_items, String coupons, String order_notes,
			int download_permissions_granted, String alert_mobile, String alert_email, String low_temp_alert,
			String high_temp_alert, String mobile_network, String order_comments, String comments, String order_sku,
			String devicemodel, String quantity, long order_acc_typeid, String mapped_date, String pet_name,
			String email_status, int provision_status, int postal_service) {
		super();
		this.id = id;
		this.order_id = order_id;
		this.woocom_id = woocom_id;
		this.datetime = datetime;
		this.status = status;
		this.shipping_total = shipping_total;
		this.shipping_tax_total = shipping_tax_total;
		this.fee_total = fee_total;
		this.fee_tax_total = fee_tax_total;
		this.tax_total = tax_total;
		this.discount_total = discount_total;
		this.order_total = order_total;
		this.refunded_total = refunded_total;
		this.order_currency = order_currency;
		this.payment_method = payment_method;
		this.shipping_method = shipping_method;
		this.customer_id = customer_id;
		this.billing_first_name = billing_first_name;
		this.billing_last_name = billing_last_name;
		this.billing_company = billing_company;
		this.billing_email = billing_email;
		this.billing_phone = billing_phone;
		this.billing_address_1 = billing_address_1;
		this.billing_address_2 = billing_address_2;
		this.billing_postcode = billing_postcode;
		this.billing_city = billing_city;
		this.billing_state = billing_state;
		this.billing_country = billing_country;
		this.shipping_first_name = shipping_first_name;
		this.shipping_last_name = shipping_last_name;
		this.shipping_address_1 = shipping_address_1;
		this.shipping_address_2 = shipping_address_2;
		this.shipping_postcode = shipping_postcode;
		this.shipping_city = shipping_city;
		this.shipping_state = shipping_state;
		this.shipping_country = shipping_country;
		this.shipping_company = shipping_company;
		this.customer_note = customer_note;
		this.line_items = line_items;
		this.shipping_items = shipping_items;
		this.fee_items = fee_items;
		this.tax_items = tax_items;
		this.coupons = coupons;
		this.order_notes = order_notes;
		this.download_permissions_granted = download_permissions_granted;
		this.alert_mobile = alert_mobile;
		this.alert_email = alert_email;
		this.low_temp_alert = low_temp_alert;
		this.high_temp_alert = high_temp_alert;
		this.mobile_network = mobile_network;
		this.order_comments = order_comments;
		this.comments = comments;
		this.order_sku = order_sku;
		this.devicemodel = devicemodel;
		this.quantity = quantity;
		this.order_acc_typeid = order_acc_typeid;
		this.mapped_date = mapped_date;
		this.pet_name = pet_name;
		this.email_status = email_status;
		this.provision_status = provision_status;
		this.postal_service = postal_service;
	}

	// Added amazon order id and fullfilment on 13 March 2019 - Separate
	// constructors for amazon orders

//	public Orders(long id, long order_id, long woocom_id,String datetime,
//			String status, float shipping_total, float shipping_tax_total,
//			float fee_total, float fee_tax_total, float tax_total,
//			float discount_total, float order_total, float refunded_total,
//			String order_currency, String payment_method,
//			String shipping_method, int customer_id, String billing_first_name,
//			String billing_last_name, String billing_company,
//			String billing_email, String billing_phone,
//			String billing_address_1, String billing_address_2,
//			String billing_postcode, String billing_city, String billing_state,
//			String billing_country, String shipping_first_name,
//			String shipping_last_name, String shipping_address_1,
//			String shipping_address_2, String shipping_postcode,
//			String shipping_city, String shipping_state,
//			String shipping_country, String shipping_company,
//			String customer_note, String line_items, String shipping_items,
//			int fee_items, int tax_items, String coupons, String order_notes,
//			int download_permissions_granted, String alert_mobile,
//			String alert_email, String low_temp_alert, String high_temp_alert,
//			String mobile_network, String order_comments, String comments, String order_sku,
//			String devicemodel, String quantity,long order_acc_typeid,String mapped_date,
//			String pet_name,String email_status,int provision_status,int postal_service,String fullfilmentchannel,String amazonOrderId) {
//		super();
//		this.id = id;
//		this.order_id = order_id;
//		this.woocom_id = woocom_id;
//		this.datetime = datetime;
//		this.status = status;
//		this.shipping_total = shipping_total;
//		this.shipping_tax_total = shipping_tax_total;
//		this.fee_total = fee_total;
//		this.fee_tax_total = fee_tax_total;
//		this.tax_total = tax_total;
//		this.discount_total = discount_total;
//		this.order_total = order_total;
//		this.refunded_total = refunded_total;
//		this.order_currency = order_currency;
//		this.payment_method = payment_method;
//		this.shipping_method = shipping_method;
//		this.customer_id = customer_id;
//		this.billing_first_name = billing_first_name;
//		this.billing_last_name = billing_last_name;
//		this.billing_company = billing_company;
//		this.billing_email = billing_email;
//		this.billing_phone = billing_phone;
//		this.billing_address_1 = billing_address_1;
//		this.billing_address_2 = billing_address_2;
//		this.billing_postcode = billing_postcode;
//		this.billing_city = billing_city;
//		this.billing_state = billing_state;
//		this.billing_country = billing_country;
//		this.shipping_first_name = shipping_first_name;
//		this.shipping_last_name = shipping_last_name;
//		this.shipping_address_1 = shipping_address_1;
//		this.shipping_address_2 = shipping_address_2;
//		this.shipping_postcode = shipping_postcode;
//		this.shipping_city = shipping_city;
//		this.shipping_state = shipping_state;
//		this.shipping_country = shipping_country;
//		this.shipping_company = shipping_company;
//		this.customer_note = customer_note;
//		this.line_items = line_items;
//		this.shipping_items = shipping_items;
//		this.fee_items = fee_items;
//		this.tax_items = tax_items;
//		this.coupons = coupons;
//		this.order_notes = order_notes;
//		this.download_permissions_granted = download_permissions_granted;
//		this.alert_mobile = alert_mobile;
//		this.alert_email = alert_email;
//		this.low_temp_alert = low_temp_alert;
//		this.high_temp_alert = high_temp_alert;
//		this.mobile_network = mobile_network;
//		this.order_comments = order_comments;
//		this.comments = comments;
//		this.order_sku = order_sku;
//		this.devicemodel = devicemodel;
//		this.quantity = quantity;
//		this.order_acc_typeid = order_acc_typeid;
//		this.mapped_date = mapped_date;
//		this.pet_name = pet_name;
//		this.email_status = email_status;
//		this.provision_status = provision_status;
//		this.postal_service=postal_service;
//		this.fulfillmentchannel = fullfilmentchannel;
//		this.amazon_order_id=amazonOrderId;
//	}

	public int getProvision_status() {
		return provision_status;
	}

	public int getWelcome_status() {
		return welcome_status;
	}

	public void setWelcome_status(int welcome_status) {
		this.welcome_status = welcome_status;
	}

	public void setProvision_status(int provision_status) {
		this.provision_status = provision_status;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getOrder_id() {
		return order_id;
	}

	public void setOrder_id(long order_id) {
		this.order_id = order_id;
	}

	public long getWoocom_id() {
		return woocom_id;
	}

	public void setWoocom_id(long woocom_id) {
		this.woocom_id = woocom_id;
	}

	public String getDatetime() {
		return datetime;
	}

	public void setDatetime(String datetime) {
		this.datetime = datetime.substring(0, 18);
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public float getShipping_total() {
		return shipping_total;
	}

	public void setShipping_total(float shipping_total) {
		this.shipping_total = shipping_total;
	}

	public float getShipping_tax_total() {
		return shipping_tax_total;
	}

	public void setShipping_tax_total(float shipping_tax_total) {
		this.shipping_tax_total = shipping_tax_total;
	}

	public float getFee_total() {
		return fee_total;
	}

	public void setFee_total(float fee_total) {
		this.fee_total = fee_total;
	}

	public float getFee_tax_total() {
		return fee_tax_total;
	}

	public void setFee_tax_total(float fee_tax_total) {
		this.fee_tax_total = fee_tax_total;
	}

	public float getTax_total() {
		return tax_total;
	}

	public void setTax_total(float tax_total) {
		this.tax_total = tax_total;
	}

	public float getDiscount_total() {
		return discount_total;
	}

	public void setDiscount_total(float discount_total) {
		this.discount_total = discount_total;
	}

	public float getOrder_total() {
		return order_total;
	}

	public void setOrder_total(float order_total) {
		this.order_total = order_total;
	}

	public float getRefunded_total() {
		return refunded_total;
	}

	public void setRefunded_total(float refunded_total) {
		this.refunded_total = refunded_total;
	}

	public String getOrder_currency() {
		return order_currency;
	}

	public void setOrder_currency(String order_currency) {
		this.order_currency = order_currency;
	}

	public String getPayment_method() {
		return payment_method;
	}

	public void setPayment_method(String payment_method) {
		this.payment_method = payment_method;
	}

	public String getShipping_method() {
		return shipping_method;
	}

	public void setShipping_method(String shipping_method) {
		this.shipping_method = shipping_method;
	}

	public int getCustomer_id() {
		return customer_id;
	}

	public void setCustomer_id(int customer_id) {
		this.customer_id = customer_id;
	}

	public String getBilling_first_name() {
		return billing_first_name;
	}

	public void setBilling_first_name(String billing_first_name) {
		this.billing_first_name = billing_first_name;
	}

	public String getBilling_last_name() {
		return billing_last_name;
	}

	public void setBilling_last_name(String billing_last_name) {
		this.billing_last_name = billing_last_name;
	}

	public String getBilling_company() {
		return billing_company;
	}

	public void setBilling_company(String billing_company) {
		this.billing_company = billing_company;
	}

	public String getBilling_email() {
		return billing_email;
	}

	public void setBilling_email(String billing_email) {
		this.billing_email = billing_email;
	}

	public String getBilling_phone() {

		return billing_phone;
	}

	public void setBilling_phone(String billing_phone) {
		this.billing_phone = billing_phone;
	}

	public String getBilling_address_1() {
		return billing_address_1;
	}

	public void setBilling_address_1(String billing_address_1) {
		this.billing_address_1 = billing_address_1;
	}

	public String getBilling_address_2() {
		return billing_address_2;
	}

	public void setBilling_address_2(String billing_address_2) {
		this.billing_address_2 = billing_address_2;
	}

	public String getBilling_postcode() {
		return billing_postcode;
	}

	public void setBilling_postcode(String billing_postcode) {
		this.billing_postcode = billing_postcode;
	}

	public String getBilling_city() {
		return billing_city;
	}

	public void setBilling_city(String billing_city) {
		this.billing_city = billing_city;
	}

	public String getBilling_state() {
		return billing_state;
	}

	public void setBilling_state(String billing_state) {
		this.billing_state = billing_state;
	}

	public String getBilling_country() {
		return billing_country;
	}

	public void setBilling_country(String billing_country) {
		this.billing_country = billing_country;
	}

	public String getShipping_first_name() {
		return shipping_first_name;
	}

	public void setShipping_first_name(String shipping_first_name) {
		this.shipping_first_name = shipping_first_name;
	}

	public String getShipping_last_name() {
		return shipping_last_name;
	}

	public void setShipping_last_name(String shipping_last_name) {
		this.shipping_last_name = shipping_last_name;
	}

	public String getShipping_address_1() {
		return shipping_address_1;
	}

	public void setShipping_address_1(String shipping_address_1) {
		this.shipping_address_1 = shipping_address_1;
	}

	public String getShipping_address_2() {
		return shipping_address_2;
	}

	public void setShipping_address_2(String shipping_address_2) {
		this.shipping_address_2 = shipping_address_2;
	}

	public String getShipping_postcode() {
		return shipping_postcode;
	}

	public void setShipping_postcode(String shipping_postcode) {
		this.shipping_postcode = shipping_postcode;
	}

	public String getShipping_city() {
		return shipping_city;
	}

	public void setShipping_city(String shipping_city) {
		this.shipping_city = shipping_city;
	}

	public String getShipping_state() {
		return shipping_state;
	}

	public void setShipping_state(String shipping_state) {
		this.shipping_state = shipping_state;
	}

	public String getShipping_country() {
		return shipping_country;
	}

	public void setShipping_country(String shipping_country) {
		this.shipping_country = shipping_country;
	}

	public String getShipping_company() {
		return shipping_company;
	}

	public void setShipping_company(String shipping_company) {
		this.shipping_company = shipping_company;
	}

	public String getCustomer_note() {
		return customer_note;
	}

	public void setCustomer_note(String customer_note) {
		this.customer_note = customer_note;
	}

	public String getLine_items() {
		return line_items;
	}

	public void setLine_items(String line_items) {
		this.line_items = line_items;
	}

	public String getShipping_items() {
		return shipping_items;
	}

	public void setShipping_items(String shipping_items) {
		this.shipping_items = shipping_items;
	}

	public int getFee_items() {
		return fee_items;
	}

	public void setFee_items(int fee_items) {
		this.fee_items = fee_items;
	}

	public int getTax_items() {
		return tax_items;
	}

	public void setTax_items(int tax_items) {
		this.tax_items = tax_items;
	}

	public String getCoupons() {
		return coupons;
	}

	public void setCoupons(String coupons) {
		this.coupons = coupons;
	}

	public String getOrder_notes() {
		return order_notes;
	}

	public void setOrder_notes(String order_notes) {
		this.order_notes = order_notes;
	}

	public int getDownload_permissions_granted() {
		return download_permissions_granted;
	}

	public void setDownload_permissions_granted(int download_permissions_granted) {
		this.download_permissions_granted = download_permissions_granted;
	}

	public String getAlert_mobile() {
		return alert_mobile;
	}

	public void setAlert_mobile(String alert_mobile) {
		this.alert_mobile = alert_mobile;
	}

	public String getAlert_email() {
		return alert_email;
	}

	public void setAlert_email(String alert_email) {
		this.alert_email = alert_email;
	}

	public String getLow_temp_alert() {
		return low_temp_alert;
	}

	public void setLow_temp_alert(String low_temp_alert) {
		this.low_temp_alert = low_temp_alert;
	}

	public String getHigh_temp_alert() {
		return high_temp_alert;
	}

	public void setHigh_temp_alert(String high_temp_alert) {
		this.high_temp_alert = high_temp_alert;
	}

	public String getMobile_network() {
		return mobile_network;
	}

	public void setMobile_network(String mobile_network) {
		this.mobile_network = mobile_network;
	}

	public String getOrder_comments() {
		return order_comments;
	}

	public void setOrder_comments(String order_comments) {
		this.order_comments = order_comments;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getOrder_sku() {
		return order_sku;
	}

	public void setOrder_sku(String order_sku) {
		this.order_sku = order_sku;
	}

	public String getDevicemodel() {
		return devicemodel;
	}

	public void setDevicemodel(String devicemodel) {
		this.devicemodel = devicemodel;
	}

	public String getQuantity() {
		return quantity;
	}

	public void setQuantity(String quantity) {
		this.quantity = quantity;
	}

	/*
	 * public long getOrder_acc_typeid() { return order_acc_type.getId(); }
	 */

	public Order_account getOrder_acc_type() {
		return order_acc_type;
	}

	public void setOrder_acc_type(Order_account order_acc_type) {
		this.order_acc_type = order_acc_type;
	}

	public long giveOrder_acc_typeid() {
		return order_acc_typeid;
	}

	public void setOrder_acc_typeid(long order_acc_typeid) {
		this.order_acc_typeid = order_acc_typeid;
	}

	public String getMapped_date() {
		return mapped_date;
	}

	public void setMapped_date(String mapped_date) {
		this.mapped_date = mapped_date;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getTracking_num() {
		return tracking_num;
	}

	public void setTracking_num(String tracking_num) {
		this.tracking_num = tracking_num;
	}

	public String getPet_name() {
		return pet_name;
	}

	public void setPet_name(String pet_name) {
		this.pet_name = pet_name;
	}

	public String getEmail_status() {
		return email_status;
	}

	public void setEmail_status(String email_status) {
		this.email_status = email_status;
	}

	public int getPostal_service() {
		return postal_service;
	}

	public void setPostal_service(int postal_service) {
		this.postal_service = postal_service;
	}

	public String getDelivery_status() {
		return delivery_status;
	}

	public void setDelivery_status(String delivery_status) {
		this.delivery_status = delivery_status;
	}

	public String getDelivered_date() {
		return delivered_date;
	}

	public void setDelivered_date(String delivered_date) {
		this.delivered_date = delivered_date;
	}

	public String getTracking_summary() {
		return tracking_summary;
	}

	public void setTracking_summary(String tracking_summary) {
		this.tracking_summary = tracking_summary;
	}

	public long getOrder_acc_typeid() {
		return order_acc_typeid;
	}

	public String getFulfillmentchannel() {
		return fulfillmentchannel;
	}

	public void setFulfillmentchannel(String fulfillmentchannel) {
		this.fulfillmentchannel = fulfillmentchannel;
	}

	public String getExternalsku() {
		return externalsku;
	}

	public void setExternalsku(String externalsku) {
		this.externalsku = externalsku;
	}

	public String getExternal_order_id() {
		return external_order_id;
	}

	public void setExternal_order_id(String external_order_id) {
		this.external_order_id = external_order_id;
	}

	public String getAccount_status() {
		return account_status;
	}

	public void setAccount_status(String account_status) {
		this.account_status = account_status;
	}

	public String getAccount_status_updated_date() {
		return account_status_updated_date;
	}

	public void setAccount_status_updated_date(String account_status_updated_date) {
		this.account_status_updated_date = account_status_updated_date;
	}

	public int getTracking_mail_status() {
		return tracking_mail_status;
	}

	public void setTracking_mail_status(int tracking_mail_status) {
		this.tracking_mail_status = tracking_mail_status;
	}

	public int getReturn_units() {
		return return_units;
	}

	public void setReturn_units(int return_units) {
		this.return_units = return_units;
	}

	public String getProduct_category() {
		return product_category;
	}

	public void setProduct_category(String product_category) {
		this.product_category = product_category;
	}

	public Map<String, String> getOrderSkuList() {
		return orderSkuList;
	}

	public void setOrderSkuList(Map<String, String> orderSkuList) {
		this.orderSkuList = orderSkuList;
	}
}
