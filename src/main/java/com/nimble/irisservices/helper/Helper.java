package com.nimble.irisservices.helper;

import java.io.*;
import java.math.BigInteger;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import com.nimble.irisservices.Util.SecretManagerService;
import com.nimble.irisservices.dto.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.json.JsonParser;
import org.springframework.boot.json.JsonParserFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.jwt.Jwt;
import org.springframework.security.jwt.JwtHelper;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.chargebee.Environment;
import com.chargebee.Result;
import com.chargebee.models.Customer;
import com.google.gson.Gson;
import com.mashape.unirest.http.Unirest;
import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.constant.IrisservicesConstants;
import com.nimble.irisservices.entity.ExternalConfig;
import com.nimble.irisservices.entity.User;
import com.nimble.irisservices.error.OAuth2Error;
import com.nimble.irisservices.service.IAlexaService;
import com.nimble.irisservices.service.IExternalConfigService;
import com.opencsv.CSVWriter;

import io.jsonwebtoken.JwsHeader;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;

public class Helper {

	@Autowired
	IExternalConfigService externalConfigService;

	@Autowired
	SecretManagerService secretManagerService;

	@Value("${aws_s3_secret_name}")
	private String S3_SECRET_NAME;

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Value("${inapp.verifyreceipt.password}")
	private String inAppPassword;

	@Value("${inapp.verifyreceipt.url}")
	private String inAppVerifyReceiptUrl;

	@Value("${amazon.alexa.clientid}")
	private String alexaClientId;
	
	@Value("${amazon.alexa.clientsecret}")
	private String alexaClientSecret;
	
	@Value("${waggle.universal_url}")
	private String waggleUniversalUrl;
	
	@Value("${amazon.alexa.accesstoken_url}")
	private String alexaAccessTokenUrl;
	
	@Autowired
	IAlexaService alexaService;
	
	@Value("${backingkey.time.diff}")
	private int backingKeyTimeDiff;
	
	@Value("${backingkey.time.diff.niom}")
	private int backingKeyTimeDiffNiom;
	
	@Value("${slackurl}")
	private String slackurl;
	
	@Value("${eseye_username}")
	private String eseye_username;
	
	@Value("${eseye_password}")
	private String eseye_password;
	
	@Value("${eseye_portfolioid}")
	private String eseye_portfolioid;
	
	@Value("${path.prefix}")
	private String path_prefix;
	
	@Value("${wowza.playback.hash.timing}")
	private int hash_end_time;
	
	private String awsBucketName = "nimblereports.nimblewireless.com";

	private String awsFolderName = "InvalidPacket";

	private static final Logger log = LogManager.getLogger(Helper.class);

	char hexDigit[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };

	private static String APPLE_AUTH_URL = "https://appleid.apple.com/auth/token";

	private static String KEY_ID = "GL3V7VRD47";
	private static String TEAM_ID = "693VK974MK";
	private static String CLIENT_ID = "com.nimble.rvpetsafetydist";

	private static PrivateKey pKey;

	public String getURL(String webServiceURL) throws Exception {

		HttpURLConnection request = null;
		BufferedReader in = null;
		try {

			// Setup the Request
			URL url = new URL(webServiceURL);
			request = (HttpURLConnection) url.openConnection();
			request.setRequestMethod("GET");
			request.setRequestProperty("Accept", "application/json");
			request.setDoOutput(true);

			// Get Response
			int responseCode = request.getResponseCode();
			// System.out.println(responseCode);
			StringBuilder response = new StringBuilder();
			response.append("");
			String inputLine;
			if (responseCode >= 400) {
				in = new BufferedReader(new InputStreamReader(request.getErrorStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					response.append(inputLine);
					response.append("\n");
				}
				in.close();
			} else {
				in = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					response.append(inputLine);
					response.append("\n");
				}
				in.close();
			}
			log.info("HTTP Response received");

			// JSONArray niomResponse = new JSONArray(response.toString());

			// JSONObject jobj = niomResponse.getResponse();

			request.disconnect();
			return response.toString();
		} catch (IOException e) {
			// This exception will be raised if the serve didn't return 200 - OK
			throw e;
		} catch (Exception e) {
			// This exception will be raised if the serve didn't return 200 - OK
			throw e;
		} finally {
			if (in != null) {
				in.close();
			}
			if (request != null) {
				request.disconnect();
			}
		}
	}
	public String getShopifyUrl(String webServiceURL) throws Exception {

		HttpURLConnection request = null;
		BufferedReader in = null;
		try {

			// Setup the Request
			URL url = new URL(webServiceURL);
			request = (HttpURLConnection) url.openConnection();
			request.setRequestMethod("GET");
			request.setRequestProperty("Accept", "application/json");
			request.setRequestProperty("X-Shopify-Access-Token","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");
			request.setDoOutput(true);

			// Get Response
			int responseCode = request.getResponseCode();
			// System.out.println(responseCode);
			StringBuilder response = new StringBuilder();
			response.append("");
			String inputLine;
			if (responseCode >= 400) {
				in = new BufferedReader(new InputStreamReader(request.getErrorStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					response.append(inputLine);
					response.append("\n");
				}
				in.close();
			} else {
				in = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					response.append(inputLine);
					response.append("\n");
				}
				in.close();
			}
			log.info("HTTP Response received");

			// JSONArray niomResponse = new JSONArray(response.toString());

			// JSONObject jobj = niomResponse.getResponse();

			request.disconnect();
			return response.toString();
		} catch (IOException e) {
			// This exception will be raised if the serve didn't return 200 - OK
			throw e;
		} catch (Exception e) {
			// This exception will be raised if the serve didn't return 200 - OK
			throw e;
		} finally {
			if (in != null) {
				in.close();
			}
			if (request != null) {
				request.disconnect();
			}
		}
	}
	public String validateToken(String webServiceURL, String authKey) throws Exception {

		HttpURLConnection request = null;
		BufferedReader in = null;
		try {

			// Setup the Request
			URL url = new URL(webServiceURL);
			request = (HttpURLConnection) url.openConnection();
			request.setRequestMethod("GET");
			request.setRequestProperty("Accept", "application/json");
			request.setRequestProperty("Authorization", authKey);

			request.setDoOutput(true);

			// Get Response
			int responseCode = request.getResponseCode();
			// System.out.println(responseCode);
			StringBuilder response = new StringBuilder();
			response.append("");
			String inputLine;
			if (responseCode >= 400) {
				in = new BufferedReader(new InputStreamReader(request.getErrorStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					response.append(inputLine);
					response.append("\n");
				}
				in.close();
			} else {
				in = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					response.append(inputLine);
					response.append("\n");
				}
				in.close();
			}
			log.info("HTTP Response received");

			// JSONArray niomResponse = new JSONArray(response.toString());

			// JSONObject jobj = niomResponse.getResponse();

			request.disconnect();
			return response.toString();
		} catch (IOException e) {
			// This exception will be raised if the serve didn't return 200 - OK
			throw e;
		} catch (Exception e) {
			// This exception will be raised if the serve didn't return 200 - OK
			throw e;
		} finally {
			if (in != null) {
				in.close();
			}
			if (request != null) {
				request.disconnect();
			}
		}
	}

	// public String sendHTTPRequest(String webServiceURL, String httpMethod,
	// String oauthAuthorization, String contentType, String acceptType, String
	// requestBody) throws Exception {
	// HttpURLConnection request = null;
	// BufferedReader in = null;
	// try {
	// // Setup the Request
	// URL url= new URL(webServiceURL);
	// ApiResponse apiResponse = new ApiResponse();
	// apiResponse.responseData = "";
	// LOGGER.info("Sending HTTP request to the web service " + webServiceURL);
	// // Setup the Request
	// request = (HttpURLConnection) url.openConnection();
	// request.setRequestMethod("POST");
	// request.setRequestProperty("Content-Type", "application/json");
	//
	// request.setDoOutput(true);
	//
	// // Set the request body if making a POST request
	// if (HttpMethod.POST.toString().equalsIgnoreCase(httpMethod) ||
	// HttpMethod.PUT.toString().equalsIgnoreCase(httpMethod)) {
	// byte[] byteArray = requestBody.getBytes("UTF-8");
	//
	// request.setRequestProperty("Content-Length", "" +
	// Integer.toString(byteArray.length));
	// request.setUseCaches(false);
	// request.setDoInput(true);
	//
	// try (DataOutputStream wr = new
	// DataOutputStream(request.getOutputStream()); BufferedWriter writer = new
	// BufferedWriter(new OutputStreamWriter(wr, "UTF-8"))) {
	// writer.write(requestBody);
	// }
	// }
	// // Get Response
	// int responseCode = request.getResponseCode();
	// StringBuilder response = new StringBuilder();
	// response.append("");
	// String inputLine;
	// if (responseCode >= 400) {
	// in = new BufferedReader(new InputStreamReader(request.getErrorStream(),
	// "UTF-8"));
	// while ((inputLine = in.readLine()) != null) {
	// response.append(inputLine);
	// response.append("\n");
	// }
	// in.close();
	// } else {
	// in = new BufferedReader(new InputStreamReader(request.getInputStream(),
	// "UTF-8"));
	// while ((inputLine = in.readLine()) != null) {
	// response.append(inputLine);
	// response.append("\n");
	// }
	// in.close();
	// }
	//
	// LOGGER.info("HTTP Response received");
	//
	// if (acceptType.equals(Headers.ApplicationJSON.toString())) {
	// apiResponse = _helper.getObjectFromJson(response.toString());
	// } else {
	// String url = webServiceURL.toString().toLowerCase(Locale.ENGLISH);
	// JAXBContext jaxbContext = JAXBContext.newInstance(ApiResponse.class);
	//
	// Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
	// apiResponse = (ApiResponse) jaxbUnmarshaller.unmarshal(new
	// StringReader(response.toString()));
	//
	// if (url.contains("/" +
	// _helper.getSoapResourceName("GetLicenseRequest").toLowerCase(Locale.ENGLISH))
	// || url.contains("/" +
	// _helper.getSoapResourceName("GetUsageLogByIdRequest").toLowerCase(Locale.ENGLISH))
	// || url.contains("/" +
	// _helper.getSoapResourceName("GetErrorLogsRequest").toLowerCase(Locale.ENGLISH))
	// || url.contains("/" +
	// _helper.getSoapResourceName("GetUsageCounterByIdRequest").toLowerCase(Locale.ENGLISH)))
	// {
	// apiResponse.responseData = null;
	// apiResponse.responseData =
	// _helper.getResponseDataOuterXML(response.toString());
	// }
	// }
	// request.disconnect();
	// return apiResponse;
	// } catch (IOException e) {
	// // This exception will be raised if the serve didn't return 200 - OK
	// throw e;
	// } finally {
	// if (in != null) {
	// in.close();
	// }
	// if (request != null) {
	// request.disconnect();
	// }
	// }
	// }

	/**
	 * Check the string is empty
	 *
	 * @param input
	 * @return
	 */
	public boolean isEmpty(String input) {
		if (input == null) {
			return false;
		}
		return input.trim().equals("");
	}

	/**
	 * Check string is not empty
	 *
	 * @param input
	 * @return
	 */
	public boolean isNotEmpty(String input) {
		if (input == null) {
			return false;
		}
		return !input.trim().equals("");
	}

	/**
	 * Check object null
	 *
	 * @param input
	 * @return
	 */
	public boolean isNull(Object input) {
		return input == null;
	}

	/**
	 * Check object not null
	 *
	 * @param input
	 * @return
	 */
	public boolean isNotNull(Object input) {
		return input != null;
	}

	/**
	 * Check string is null or empty
	 *
	 * @param s
	 * @return
	 */
	public boolean isNullOrEmpty(String s) {
		return s == null || s.trim().length() == 0;
	}

	public String getCurrentTimeinUTC() {

		Calendar calendar = Calendar.getInstance();
		Date d = calendar.getTime();

		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		df.setTimeZone(TimeZone.getTimeZone("UTC"));
		//System.out.println(df.format(d));
		return df.format(d);
	}
	
	public String getCurrentDateOnlyinUTC() {

		Calendar calendar = Calendar.getInstance();
		Date d = calendar.getTime();

		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		df.setTimeZone(TimeZone.getTimeZone("UTC"));
		//System.out.println(df.format(d));
		return df.format(d);
	}

	public String getExternalConfigValue(String name, IExternalConfigService externalConfigService) {

		log.info("External Configuration Name : " + name);
		ExternalConfig configuration = new ExternalConfig();
		try {
			configuration = externalConfigService.getExternalConfig(name);
			log.info("External Configuration Value : " + configuration.getValue());
			return configuration.getValue().trim();
		} catch (Exception ex) {
			log.info("Exception while getting value for given external name. Exception " + ex.getMessage());

			return null;
		}

	}

	public JSONObject getJSONObject(String reponse) {

		log.info("Convert response string to JSON Object");

		JSONObject jresponse = new JSONObject();

		Gson gson = new Gson();
		try {
			JSONObject nresponse = new JSONObject(reponse);
			jresponse = nresponse.getJSONObject("response");
			return jresponse;
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			log.info("Error : Convert response string to JSON Object  : " + e.getLocalizedMessage());
			return null;
		}

	}

	public Date getDate(String dateString) throws ParseException {

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date = new Date(sdf.parse(dateString).getTime());
		return date;
	}

	public String postFCMRequest(String url, String acceptType, String authKey, StringEntity postingString) {

		String _response = null;
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpPost post = new HttpPost(url);

		post.setEntity(postingString);
		post.setHeader("Content-type", "application/json");
		post.setHeader("Authorization", authKey);
		try {
			HttpResponse response = httpClient.execute(post);
			_response = EntityUtils.toString(response.getEntity());

		} catch (Exception e) {


			return null;
		}

		return _response;
	}

	public String postShopifyRequest(String url, String acceptType, StringEntity postingString) {

		String _response = null;
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpPost post = new HttpPost(url);

		post.setEntity(postingString);
		post.setHeader("Content-type", acceptType);
		post.setHeader("X-Shopify-Access-Token","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");


		try {
			HttpResponse response = httpClient.execute(post);
			_response = EntityUtils.toString(response.getEntity());

		} catch (Exception e) {


			return null;
		}

		return _response;
	}
	public static byte[] zipContent(Object obj) throws IOException {

		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		GZIPOutputStream gzipOut = new GZIPOutputStream(baos);

		Gson gson = new Gson();
		String json = gson.toJson(obj);
		gzipOut.write(json.getBytes());

		gzipOut.close();
		// objectOut.close();
		byte[] bytes = baos.toByteArray();

		return bytes;
	}

	public Date formatDate(String date) throws ParseException {

		Date formatedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(date);

		return formatedDate;
	}

	public String getCurrentDate() throws ParseException {

		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		Date dateobj = new Date();
		return df.format(dateobj);
	}

	public String encryptAndSetUser(String user) {
		String authKey = null;
		if (user != null) {
			MessageDigest msgDigest = null;
			try {
				msgDigest = MessageDigest.getInstance("SHA1");
				msgDigest.update(user.getBytes());
			} catch (NoSuchAlgorithmException e) {
			}

			byte rawByte[] = msgDigest.digest();
			authKey = bytesToHex(rawByte).toLowerCase();

		}
		return authKey;
	}

	public String bytesToHex(byte[] b) {

		StringBuffer buf = new StringBuffer();
		for (int j = 0; j < b.length; j++) {
			buf.append(hexDigit[(b[j] >> 4) & 0x0f]);
			buf.append(hexDigit[b[j] & 0x0f]);
		}
		return buf.toString();
	}

	public String getUTCDate(String datetime, String timeZoneOffset) {

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		formatter.setTimeZone(TimeZone.getTimeZone("GMT" + timeZoneOffset));

		Date date = null;
		try {
			date = formatter.parse(datetime);

			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));


			return dateFormat.format(date);
		} catch (ParseException e) {
			return null;
		}

	}

	public String getPlusDays(String dateTime, String addDays) {

		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		Calendar cal = Calendar.getInstance();

		try {
			cal.setTime(this.formatDate(dateTime));
		} catch (ParseException e) {
			return getCurrentTimeinUTC();
		}

		cal.add(Calendar.HOUR, Integer.parseInt(addDays));

		return df.format(cal.getTime());
	}

	public int getRandomNumber(int min, int max) {
		Random rand = new Random();
		return (min + rand.nextInt((max - min) + 1));
	}

	public void updateCBCustomerDetails(User user) {
		try {
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);

			Result result1 = Customer.update(user.getChargebeeid()).firstName(user.getFirstname())
					.lastName(user.getLastname()).request();

		} catch (Exception e) {
			log.error(" update CB customer detail:" + e.getMessage());
		}
	}

	public List<JTrendingVideo> updateYoutubeStatistics(List<JTrendingVideo> trendvideoList, String youtubeAPI2) {

		if (youtubeAPI2.equalsIgnoreCase("false"))
			return trendvideoList;

		log.info("update Statistics from Youtube..");

		for (JTrendingVideo tvideos : trendvideoList) {
			try {

				String videoID = tvideos.getVideoUrl().split("=")[1];
				String URl = youtubeAPI2.replace("VIDEOID", videoID);
				String resp = getURL(URl);

				if (resp.contains("error")) {
					log.info("Bad Request : Invalid Access key or video id");
					continue;
				}

				JSONObject res = new JSONObject(resp);
				JSONObject items = (JSONObject) res.getJSONArray("items").get(0);
				JSONObject statistics = items.getJSONObject("statistics");

				String viewcount = statistics.getString("viewCount");
				String likeCount = statistics.getString("likeCount");
				String dislikeCount = statistics.getString("dislikeCount");

				tvideos.setTotalvideoViewCount(Integer.parseInt(viewcount));
				tvideos.setTotalvideoLike(Integer.parseInt(likeCount));
				tvideos.setTotalvideoDislike(Integer.parseInt(dislikeCount));

			} catch (NullPointerException | NumberFormatException e) {
				log.error("updateYoutubeStatistics : invalid video id :");
			} catch (Exception e) {
				log.error("updateYoutubeStatistics : invalid video id :");
			}
		}

		return trendvideoList;
	}

	private static PrivateKey getPrivateKey() throws Exception {
		// read your key
		String path = new ClassPathResource("AuthKey_GL3V7VRD47.p8").getFile().getAbsolutePath();

		final PEMParser pemParser = new PEMParser(new FileReader(path));
		final JcaPEMKeyConverter converter = new JcaPEMKeyConverter();
		final PrivateKeyInfo object = (PrivateKeyInfo) pemParser.readObject();
		final PrivateKey pKey = converter.getPrivateKey(object);

		return pKey;
	}

	private static String generateJWT() throws Exception {
		if (pKey == null) {
			pKey = getPrivateKey();
		}

		String token = Jwts.builder().setHeaderParam(JwsHeader.KEY_ID, KEY_ID).setIssuer(TEAM_ID)
				.setAudience("https://appleid.apple.com").setSubject(CLIENT_ID)
				.setExpiration(new Date(System.currentTimeMillis() + (1000 * 60 * 5)))
				.setIssuedAt(new Date(System.currentTimeMillis())).signWith(pKey, SignatureAlgorithm.ES256).compact();

		return token;
	}

	/*
	 * Returns unique user id from apple
	 */
	public static String appleAuth(String authorizationCode) throws Exception {
		log.info("Entered into appleAuth : authorizationCode " + authorizationCode);
		try {
			String token = generateJWT();

			com.mashape.unirest.http.HttpResponse<String> response = Unirest.post(APPLE_AUTH_URL)
					.header("Content-Type", "application/x-www-form-urlencoded").field("client_id", CLIENT_ID)
					.field("client_secret", token).field("grant_type", "authorization_code")
					.field("code", authorizationCode).asString();

			TokenResponse tokenResponse = new Gson().fromJson(response.getBody(), TokenResponse.class);
			String idToken = tokenResponse.getId_token();
			String payload = idToken.split("\\.")[1]; // 0 is header we ignore it for now
			String decoded = new String(Decoders.BASE64.decode(payload));

			IdTokenPayload idTokenPayload = new Gson().fromJson(decoded, IdTokenPayload.class);

			return idTokenPayload.getSub();
		} catch (Exception e) {
			log.error("Exception ocured at appleAuth : " + e.getLocalizedMessage());
		}
		return null;
	}
	
	public File convertZip(File originFile, String fileName) {
		log.info("Entered into convertZip");
		try {
			String zipFileName = fileName.concat(".zip");
			
			File file = new File(zipFileName);
			if (file.exists()) {
				file.delete();
				System.out.println("File already exists");
			}
			
			FileOutputStream fos = new FileOutputStream(zipFileName);
			ZipOutputStream zos = new ZipOutputStream(fos);
			zos.putNextEntry(new ZipEntry(originFile.getName()));
			
			int BUFFER = 1024;
			FileInputStream fis = new FileInputStream(originFile);
			BufferedInputStream bis = new BufferedInputStream(fis, BUFFER);
			
			byte[] bytes = new byte[BUFFER];
			
			int count;
			while((count = bis.read(bytes, 0, BUFFER)) != -1) {
				zos.write(bytes, 0, count);
			}
			
//			zos.write(bytes, 0, bytes.length);
			fis.close();
			zos.closeEntry();
			zos.close();
			return file;
		} catch (Exception e) {
			log.error("Error in convertZip :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}
	
	public boolean deleteFile(File file) {
		log.info("Entered into deleteFile");
		try {
			boolean deleteStatus = file.delete();
			log.info("Filename : " + file.getName() + ":: File deleted status : " + deleteStatus);
			return true;
		} catch (Exception e) {
			log.error(
					"Error in delete File :: FileName : " + file.getName() + " :: Error : " + e.getLocalizedMessage());
		}
		return false;
	}
	
	public File writeCSVDataLineByLine(List<sensorReportData> reports, String fromDate, String toDate, String deviceName) {
		log.info("Entered into writeCSVDataLineByLine");
		try {
			String fileName = deviceName + "_" + fromDate + "_" + toDate + "_" +System.currentTimeMillis() + ".csv";

			File file = new File(fileName);
			if (file.exists()) {
				file.delete();
				System.out.println("File already exists");
			}
			CSVWriter csvWriter = new CSVWriter(new FileWriter(file));
			String[] header = {"Date","Status"};

			// Write headers
			csvWriter.writeNext(header);

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

			int length = reports.size();
			for (int i = 0; i < length; i++) {
				try {

					String dateTime = reports.get(i).getReportdatetime();

					Date dt = sdf.parse(dateTime);

					String date = sdf.format(dt);

					csvWriter.writeNext(new String[] {date, reports.get(i).getStatus()});
				} catch (Exception e) {
					log.error("Error While Write Data Row by Row " + e.getMessage());
				}
			}
			csvWriter.close();
			return file;
		} catch (Exception e) {
			log.error("Error in writeCSVDataLineByLine :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}
	

	public String signUpInReferralCandy(String fName, String lName, String email, String referralCandySecretKey,
			String referralCandyAccessKey) throws Exception {
		log.info("Entered signUpInReferralCandy : " + fName + lName);
		String unixTime = getUnixTime();
		String sigParam = referralCandySecretKey + "accessID=" + referralCandyAccessKey + "email=" + email
				+ "first_name=" + fName + "last_name=" + lName + "timestamp=" + unixTime;
		String signature = getMD5_Value(sigParam);
		String httpPostUrl = "https://my.referralcandy.com/api/v1/signup.json?" + "accessID=" + referralCandyAccessKey
				+ "&email=" + email + "&" + "first_name=" + fName + "&last_name=" + lName + "&signature=" + signature
				+ "&" + "timestamp=" + unixTime;

		try {
			URL url_ = new URL(httpPostUrl);
			HttpURLConnection http = (HttpURLConnection) url_.openConnection();
			http.setRequestMethod("POST");
			http.setRequestProperty("Content-Type", "application/json; utf-8");
			http.setRequestProperty("Accept", "application/json");
			http.setDoOutput(true);
			int resCode = http.getResponseCode();
			if (resCode > 200) {
				log.info("Invalid response code : " + resCode);
				throw new Exception();
			}
			try {
				BufferedReader in = new BufferedReader(new InputStreamReader(http.getInputStream()));
				String line;
				StringBuilder response = new StringBuilder();
				while ((line = in.readLine()) != null) {
					response.append(line);
				}
				return response.toString();
			} catch (Exception e) {
				log.error("Error : " + e.getLocalizedMessage());
				throw e;
			}
		} catch (Exception e) {
			log.error("Exception occured while signUpInReferralCandy : " + e.getLocalizedMessage());
			throw e;
		}
	}

	public static String getReferralURL(String webServiceURL) throws Exception {
		HttpURLConnection request = null;
		BufferedReader in = null;
		try {
			// Setup the Request
			URL url = new URL(webServiceURL);
			request = (HttpURLConnection) url.openConnection();
			request.setRequestMethod("GET");
			request.setRequestProperty("Accept", "application/json");
			request.setDoOutput(true);
			// Get Response
			int responseCode = request.getResponseCode();
			StringBuilder response = new StringBuilder();
			response.append("");
			String inputLine;
			if (responseCode >= 400) {
				in = new BufferedReader(new InputStreamReader(request.getErrorStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					response.append(inputLine);
					response.append("\n");
				}
				in.close();
			} else {
				in = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					response.append(inputLine);
					response.append("\n");
				}
				in.close();
			}
			request.disconnect();
			return response.toString();
		} catch (IOException e) {
			throw e;
		} catch (Exception e) {
			throw e;
		} finally {
			if (in != null) {
				in.close();
			}
			if (request != null) {
				request.disconnect();
			}
		}
	}

	// To verify our referral candy Access key and Secret key are valid
	public static void verifyReferralCandy(String API_ACCESS_ID, String unixTime, String signature) {
		log.info("Entered verifyReferralCandy");
		try {
			String verifyurl = "https://my.referralcandy.com/api/v1/verify.json?accessID=" + API_ACCESS_ID
					+ "&timestamp=" + unixTime + "&signature=" + signature;
			String response = getReferralURL(verifyurl);
			log.info("Verify Result Response " + response);
		} catch (Exception e) {
			log.error("Error @ verifyReferralCandy : " + e.getLocalizedMessage());
		}
	}

	// Get Current Unix Time Stamp
	public static String getUnixTime() {
		log.info("Entered getUnixTime");
		Date now = new Date();
		long ut3 = now.getTime() / 1000L;
		return ut3 + "";
	}

	// MD5 Digest
	public static String getMD5_Value(String keyString) throws NoSuchAlgorithmException {
		log.info("Entered getMD5_Value");
		MessageDigest md5 = MessageDigest.getInstance("MD5");
		md5.update(StandardCharsets.UTF_8.encode(keyString));
		return String.format("%032x", new BigInteger(1, md5.digest()));
	}

	public String getUTCDatev2(String datetime, String timeZoneOffset) {

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		formatter.setTimeZone(TimeZone.getTimeZone(timeZoneOffset));

		Date date = null;
		try {
			date = formatter.parse(datetime);

			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));


			return dateFormat.format(date);
		} catch (ParseException e) {
			return null;
		}

	}

	public String verifyReceiptThruAppStore(String token) {
		log.info("Entered verifyReceipt : " + token);

		JResponse response = new JResponse();
		String httpPostUrl = inAppVerifyReceiptUrl;
		// "https://sandbox.itunes.apple.com/verifyReceipt";

		HttpClient httpClient = new DefaultHttpClient();

		try {
			
			HttpPost request = new HttpPost(httpPostUrl);
			String requestparam = "{\"password\": \"" + inAppPassword + "\"," + " \"receipt-data\": \"" + token
					+ "\" }";

			StringEntity params = new StringEntity(requestparam);

			request.addHeader("content-type", "application/json");
			request.setEntity(params);
			HttpResponse response1 = httpClient.execute(request);

			BufferedReader rd = new BufferedReader(new InputStreamReader(response1.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {

				result.append(line);
			}
			rd.close();

			log.info("AppleStore request: " + request + "AppleStore request param: " + requestparam);
			log.info("AppleStore result: " + result.toString());

			return result.toString();

		} catch (Exception ex) {
			log.error("While Getting AppleStore Response :  exception: " + ex);
		} finally {
			httpClient.getConnectionManager().shutdown(); // Deprecated
		}

		return null;
	}

	public String httpPOSTRequest(String url, String urlParams,String auth) {
		log.info("Entered into httpPOSTRequest  : "+url);
		String response = null;
		try {			
			URL url_ = new URL (url);
			HttpURLConnection http = (HttpURLConnection)url_.openConnection();
			http.setRequestMethod("POST");
			http.setDoOutput(true);
			http.setInstanceFollowRedirects(false);
			http.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			http.setRequestProperty("charset", "utf-8");
			http.setUseCaches(false);
			if(auth != null) {
				http.setRequestProperty("auth", auth);
			}
			if(urlParams!=null) {
				byte[] postData = urlParams.getBytes( StandardCharsets.UTF_8 );
				http.setRequestProperty("Content-Length", Integer.toString(postData.length));
				DataOutputStream wr = new DataOutputStream(http.getOutputStream());
				wr.write( postData );
			}					
			int responseCode = http.getResponseCode();	
			log.info("Response Code : "+responseCode);
			InputStream content = (InputStream)http.getInputStream();
			InputStreamReader inpStrmRdr = new InputStreamReader (content);
			BufferedReader bufRdr   = new BufferedReader(inpStrmRdr);
			String line;
			StringBuilder sb = new StringBuilder();
			while ((line = bufRdr.readLine()) != null) {
				sb.append(line);
			}
			log.info("Http POST Response : "+ sb.toString());
			return sb.toString();
		}catch(Exception e) {
			log.error("Exception occured at httpPOSTRequest : "+e.getLocalizedMessage());
		}		
		return response;		
	}

	public String httpPOSTRequest(String url, String urlParams) {
//		log.info("Entered into httpPOSTRequest : "+url);
		String response = null;
		try {
			URL url_ = new URL(url);
			HttpURLConnection http = (HttpURLConnection) url_.openConnection();
			http.setRequestMethod("POST");
			http.setDoOutput(true);
			http.setInstanceFollowRedirects(false);
			http.setRequestProperty("Content-Type", "application/json");
			http.setRequestProperty("charset", "utf-8");

			http.setUseCaches(false);
			if (urlParams != null) {
				byte[] postData = urlParams.getBytes(StandardCharsets.UTF_8);
				http.setRequestProperty("Content-Length", Integer.toString(postData.length));
				DataOutputStream wr = new DataOutputStream(http.getOutputStream());
				wr.write(postData);
			}
			int responseCode = http.getResponseCode();
			InputStream content = (InputStream) http.getInputStream();
			InputStreamReader inpStrmRdr = new InputStreamReader(content);
			BufferedReader bufRdr = new BufferedReader(inpStrmRdr);
			String line;
			StringBuilder sb = new StringBuilder();
			while ((line = bufRdr.readLine()) != null) {
				sb.append(line);
			}
			return sb.toString();
		} catch (Exception e) {
			log.error("Exception occured at httpPOSTRequest : " + e.getLocalizedMessage());
		}
		return response;
	}
	
	public String httpGETRequest(String url, String authorization) {
		log.info("Entered into httpGETRequest : URL : "+url);
		String response = null;
		try {			
			URL url_ = new URL (url);
			HttpURLConnection http = (HttpURLConnection)url_.openConnection();
			http.setRequestMethod("GET");
			http.setDoOutput(true);
			http.setInstanceFollowRedirects(false);
			http.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			http.setRequestProperty("charset", "utf-8");
			if(authorization != null) {
				http.setRequestProperty("Authorization", authorization);
			}
			http.setUseCaches(false);
			int responseCode = http.getResponseCode();	
			log.info("Response Code : "+responseCode);
			StringBuilder sb = new StringBuilder();
			String inputLine;
			BufferedReader in = null;
			if (responseCode >= 400) {
				in = new BufferedReader(new InputStreamReader(http.getErrorStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					sb.append(inputLine);
				}
				in.close();
			} else {
				in = new BufferedReader(new InputStreamReader(http.getInputStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					sb.append(inputLine);
				}
				in.close();
			}			
			log.info("Http GET Response : "+ sb.toString());
			return sb.toString();
		}catch(Exception e) {
			log.error("Exception occured at httpGETRequest : "+e.getLocalizedMessage());
		}		
		return response;		
	}
	
	public String getAwsAccessToken(String authCode, long userId) {
		log.info("Entered into getAwsAccessToken : aws_authCode : "+authCode);
		try {
			String urlParameters  = "grant_type=authorization_code"
					+ "&code="+authCode
					+ "&client_id="+alexaClientId
					+ "&client_secret="+alexaClientSecret
					+ "&redirect_uri="+waggleUniversalUrl;
			log.info("urlParameters : "+urlParameters);
			String accessTokenResponse = httpPOSTRequest(alexaAccessTokenUrl,urlParameters,null);
						
			log.info("Access token response from amazon : "+accessTokenResponse.toString());
			org.json.JSONObject jobj = new org.json.JSONObject(accessTokenResponse.toString());
			String accessToken = jobj.getString("access_token");
			long expiresIn = jobj.getLong("expires_in");
			String refreshToken = jobj.getString("refresh_token");
			String tokenType = jobj.getString("token_type");	
			String CurrentTime = IrisservicesUtil.getCurrentDateTime(IrisservicesConstants.DATETIMEFORMAT, IrisservicesConstants.UTCFORMAT);
			Timestamp currTimeStamp=IrisservicesUtil.getDateTime_TS(CurrentTime);
			
			AlexaTokenSet ats = new AlexaTokenSet(accessToken, refreshToken, userId);
			ats.setExpiresIn(expiresIn);
			ats.setTokenType(tokenType);
			ats.setUpdatedOn(currTimeStamp);
			AlexaTokenSet oldAts = alexaService.getAlexaTokens(userId);
			if(oldAts != null) {
				boolean isUpdated = alexaService.updateAlexaTokens(ats);
				log.info("isUpdated : "+isUpdated);
			}else {
				ats.setCreatedOn(currTimeStamp);
				boolean isInserted = alexaService.insertAlexaTokens(ats);
				log.info("isInserted : "+isInserted);
			}
			return accessToken;
		}catch (Exception e) {
			log.error("Exception occured at getAwsAccessToken : "+e.getLocalizedMessage());
		}
		return null;
	}
	
	public String getAmazonUserEndpointArray(String accessToken) {
		log.info("Entered to getAmazonUserEndpointArray");
		//		Host: <api.amazonalexa.com, api.eu.amazonalexa.com, or api.fe.amazonalexa.com>
		String alexaApiEndpoint = "https://api.eu.amazonalexa.com/v1/alexaApiEndpoint";
		try {			
			String endPointResponse = httpGETRequest(alexaApiEndpoint,"Bearer "+accessToken);
			log.info("Response : "+endPointResponse); 	
			return endPointResponse;
		}catch(Exception e) {
			log.error("Exception occured at getAmazonUserEndpointArray : "+e.getLocalizedMessage());
		}
		return null;
	}
	
	public String getAppleTokenFromJwt(String accessToken) {
		log.info("Entered to getAppleTokenFromJwt");
		String userIdMetaData = null;
		try {
			log.info("------------ Decode JWT ------------");
			String[] split_string = accessToken.split("\\.");
			String base64EncodedHeader = split_string[0];
			String base64EncodedBody = split_string[1];
			String base64EncodedSignature = split_string[2];

			log.info("~~~~~~~~~ JWT Header ~~~~~~~");
			Base64 base64Url = new Base64(true);
			String header = new String(base64Url.decode(base64EncodedHeader));
			log.info("JWT Header : " + header);

			log.info("~~~~~~~~~ JWT Body ~~~~~~~");
			String body = new String(base64Url.decode(base64EncodedBody));
			log.info("JWT Body : " + body);
			JSONObject jObj = new JSONObject(body.toString());
			// userIdMetaData = AES.decrypt(jObj.get("sub"));
			userIdMetaData = (String) jObj.get("sub");

			return userIdMetaData;
		} catch (Exception e) {
			log.info("Error While Getting Token " + e.getMessage());
			return null;
		}
	}

public String[] decodeInternalKey(String secretkey) {
		
		String [] decodeKeys =  new String[2];
		
		try {
			String[] parse = secretkey.split(":");

			String encodeValue = parse[0];
			int keyvalue = Integer.parseInt(parse[1]);
			int secretValue = Integer.parseInt(parse[2]);

			String output = "";

			int hashcount = 0;
			int secretcount = 0;

			String hashstr = "";
			String secretstr = "";

			boolean rev = false;
			boolean isHash = true;

			for (int i = 0; i < encodeValue.length();) {

				String tempKeyvalue = "";
				String secret1Value = "";

				for (int j = 0; j < 4 && hashcount++ < keyvalue; j++) {

					char ch = encodeValue.charAt(i++);

					if (rev)
						tempKeyvalue = tempKeyvalue + ch;
					else
						tempKeyvalue = ch + tempKeyvalue;

				}

				hashstr = hashstr + tempKeyvalue;

				for (int j = 0; j < 4 && secretcount++ < secretValue; j++) {
					char ch = encodeValue.charAt(i++);

					if (rev)
						secret1Value = secret1Value + ch;
					else
						secret1Value = ch + secret1Value;
				}

				secretstr = secretstr + secret1Value;

				rev = !rev;

			}

			decodeKeys[0]=hashstr;
			decodeKeys[1]=secretstr;
			
			return decodeKeys;
		}catch(Exception e)
		{
			log.error("Error While decode Internal Key : "+e.getMessage());
		}
		return decodeKeys;
	}

	public boolean isValidEmail(String email) {
		String emailRegex = "^[a-zA-Z0-9_+&*-]+(?:\\." + "[a-zA-Z0-9_+&*-]+)*@" + "(?:[a-zA-Z0-9-]+\\.)+[a-z"
				+ "A-Z]{2,10}$";
		Pattern pat = Pattern.compile(emailRegex);
		
		if (email == null)
			return false;
		email = email.trim();
		return pat.matcher(email).matches();
	}

	public JBacking backingKeyValidation(String backing) {
		log.info("backing key :  " + backing);
		JBacking jBacking = new JBacking();
		String logoutErrorMsg = OAuth2Error.FORCELOGOUT;
		try {
			backing = URLDecoder.decode(backing, StandardCharsets.UTF_8.name());
			Helper _helper = new Helper();
			int backingKeytimeDifference = backingKeyTimeDiff * 60 * 1000;
			AES aes = new AES();
			
			if (backing != null) {

				String[] credential = _helper.decodeInternalKey(backing);
				String timeInMillis = timeInMillis = credential[1].substring(0, credential[1].length() - 3);

				Timestamp curTime = new Timestamp(System.currentTimeMillis());
				long timeDiff = Long.valueOf(timeInMillis);
				timeDiff = curTime.getTime() - timeDiff;
				if (timeDiff > backingKeytimeDifference) {
					jBacking.setStatus(-3);
					jBacking.setMsg("Invalid Session. Please try again later.");
					return jBacking;
				}
				
				
//				String finalOut = "NA";
//				if( credential[1].contains("faa") || credential[1].contains("fia")) {
//					finalOut = aes.decodeV2(credential[0], credential[1]);
//				} else {
//					finalOut = aes.decode(credential[0], credential[1]);
//				}

				String finalOut = aes.decode(credential[0], credential[1]);
				if (finalOut == null) {
					jBacking.setStatus(-2);
					jBacking.setMsg(logoutErrorMsg);
					return jBacking;
				}

				log.info("AES decryption success : " + backing + " : " + finalOut);
				jBacking.setStatus(1);
				jBacking.setMsg("Success");
				jBacking.setAuthKey(finalOut);

				return jBacking;
			} else {
				jBacking.setStatus(-2);
				jBacking.setMsg(logoutErrorMsg);
				return jBacking;
			}

		} catch (Exception e) {
			jBacking.setStatus(0);
			jBacking.setMsg("Invalid Session. Please try again later.");
			return jBacking;
		}

	}
	
	public JBacking backingKeyValidationNiom(String backing) {
		log.info("backing key :  " + backing);
		JBacking jBacking = new JBacking();
		String logoutErrorMsg = OAuth2Error.FORCELOGOUT;
		try {
			backing = URLDecoder.decode(backing, StandardCharsets.UTF_8.name());
			Helper _helper = new Helper();
			int backingKeytimeDifference = backingKeyTimeDiffNiom * 60 * 60 * 1000;
			AES aes = new AES();
			
			if (backing != null) {

				String[] credential = _helper.decodeInternalKey(backing);
				String timeInMillis = timeInMillis = credential[1].substring(0, credential[1].length() - 3);

				Timestamp curTime = new Timestamp(System.currentTimeMillis());
				long timeDiff = Long.valueOf(timeInMillis);
				timeDiff = curTime.getTime() - timeDiff;
				if (timeDiff > backingKeytimeDifference) {
					jBacking.setStatus(-3);
					jBacking.setMsg("Invalid Session. Please try again later.");
					return jBacking;
				}

				String finalOut = aes.decode(credential[0], credential[1]);
				if (finalOut == null) {
					jBacking.setStatus(-2);
					jBacking.setMsg(logoutErrorMsg);
					return jBacking;
				}

				log.info("AES decryption success : " + backing + " : " + finalOut);
				jBacking.setStatus(1);
				jBacking.setMsg("Success");
				jBacking.setAuthKey(finalOut);

				return jBacking;
			} else {
				jBacking.setStatus(-2);
				jBacking.setMsg(logoutErrorMsg);
				return jBacking;
			}

		} catch (Exception e) {
			jBacking.setStatus(0);
			jBacking.setMsg("Invalid Session. Please try again later.");
			return jBacking;
		}

	}
	
	public String getCurrentYear() throws ParseException {
		Calendar cal = Calendar.getInstance(); 
		return String.valueOf(cal.get(Calendar.YEAR)); 
	}

	public JResponse validateUser(String username, String principalName) {
		log.info("username : "+username +", principal name : "+principalName); 
		JResponse response = new JResponse();
		String forceLogout = OAuth2Error.FORCELOGOUT;
		try {
			if( username.equalsIgnoreCase(principalName) ) { 
				log.info("is Authenticated");
				return null;
			} 
			log.error("Not Authenticated : user credentials not match ");
			response.put("Status", -2);
			response.put("Msg", forceLogout);
			return response;
		} catch (Exception e) {
			log.error("Error in validateUser : "+e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Error");
			return response;
		}
	}
	
	public String bCryptEncoder(String rawPassword) {
		 PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
		 return passwordEncoder.encode(rawPassword);
	}

	public Date convertDateTimeToUTC(Date date) {
		try {
			DateFormat formatterUTC = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
			formatterUTC.setTimeZone(TimeZone.getTimeZone("UTC")); // UTC timezone
			DateFormat format = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
			Date dateUTC = format.parse( formatterUTC.format(date) );
			return dateUTC;
		} catch (Exception e) {
			return date;
		}
	}

	public Map<Integer, String> getPhoneAndCountryCode(String phno) {
		Map<Integer, String> CodeNumber = new HashMap<Integer, String>();
		CodeNumber.put(0, phno.split("-")[0]);
		if (phno == null)
			return null;
		else if (!phno.contains("-")) {
			CodeNumber.put(0, "");
			phno = "-" + phno;
		}
		String[] phoneNumber = phno.split("-");
		CodeNumber.put(1, phoneNumber[1].replaceAll("\\W", ""));
		return CodeNumber;
	}
	
	public String urlDecoder(String url) {
		try {
			return URLDecoder.decode(url, StandardCharsets.UTF_8.name());
		} catch (Exception e) {
			return url;
		}
	}

	public boolean checkUserCredencial(String inputPassword, String passwordInDB, String password_ver) {
		boolean passwordMatch = false;
		PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
		if( (password_ver.equalsIgnoreCase("V2") && passwordEncoder.matches(inputPassword, passwordInDB) ) ) {
			passwordMatch = true;
		} else if( inputPassword.equals(passwordInDB) || (urlDecoder(inputPassword)).equals(passwordInDB) ) {
			passwordMatch = true;
		}
		return passwordMatch;
	}
	
	public Map<String, Object> convetTokenToMap(String token) {
		Jwt jwt = JwtHelper.decode(token);
		String jwtInfo = jwt.getClaims();
		JsonParser jsonParser = JsonParserFactory.getJsonParser();
		Map<String, Object> claimMap = jsonParser.parseMap(jwtInfo);
		return claimMap;
	}

	public String base64Decoder(String passwordBase64) {
		log.info("Entered into base64Decoder");
		try {
			byte[] passwordByte = {};
			String password = "";
			passwordByte = Base64.decodeBase64(passwordBase64.getBytes("UTF-8"));
			return new String(passwordByte, "UTF-8");
		} catch (UnsupportedEncodingException e) {
			log.error("Error in base64Decoder :: error : "+e.getLocalizedMessage());			
			return null;
		} catch (Exception e) {
			log.error("Error in base64Decoder :: error : "+e.getLocalizedMessage());
			return null;
		}
	}
	
	public String encodeString(String encodeStr, int visible, char encodeChar) {
		log.info("Entered into encodeString");
		String encodedString = "";
		encodeStr = encodeStr.replace("-", "");
		encodeStr = encodeStr.replace("+", "");
		int strLen = encodeStr.length();
		int startFrom = strLen - visible;
		
		encodedString = encodeStr.substring(startFrom, encodeStr.length());
		encodedString = "******"+encodedString;
		encodedString = encodedString.replace("*", encodeChar+"");
		return encodedString;
	}

	public void sendtoSlack(String title, String message) {
		String URL = slackurl;
		try {
			JSONObject fieldObj = new JSONObject();
			fieldObj.put("title", "Irisservices : " +title);
			fieldObj.put("value", message);
			fieldObj.put("short", false);

			JSONArray fieldArr = new JSONArray();
			fieldArr.put(fieldObj);
			JSONObject attachmentObj = new JSONObject();
			attachmentObj.put("color", "#9733EE");

			attachmentObj.put("fields", fieldArr);

			JSONArray attachmentArr = new JSONArray();
			attachmentArr.put(attachmentObj);
			JSONObject postParams = new JSONObject();
			postParams.put("attachments", attachmentArr);
		} catch (JSONException e) {
		}
	}
	
	public float CelsiusToFahrenheit(float tempmvalIndegreeCelsius) {

		double degreeFahrenheit = Double.valueOf(tempmvalIndegreeCelsius).floatValue() * 9 / 5 + 32;
		double roundvalues = Math.round(degreeFahrenheit * 100.0) / 100.0;
		return (float) roundvalues;
	}
	
	public float FahrenheitToCelsius(float tempmvalIndegreeFahrenheit) {
		double degreeCelcius = (Double.valueOf(tempmvalIndegreeFahrenheit).floatValue() - 32) * 5 / 9;
		double roundvalues = Math.round(degreeCelcius * 100.0) / 100.0;
		return (float) roundvalues;
	}
	
	/** Calls Eseye Login API  */ 
	public String callEseyeLogin(String url){
		log.info("callEseyeLogin...");
		HttpClient httpClient = HttpClientBuilder.create().build();

		try {

			HttpPost request = new HttpPost(url);

			String requestparam = "{\"username\":\""+eseye_username+"\",\"password\":\""+eseye_password+"\",\"portfolioID\":\""+eseye_portfolioid+"\"}";

			StringEntity params = new StringEntity(requestparam);
			log.info("requestparam");

			request.addHeader("content-type", "application/json");

			request.setEntity(params);
			HttpResponse response = httpClient.execute(request);
			log.info(response.getEntity().toString());

			BufferedReader rd = new BufferedReader(
					new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {

				result.append(line);
			}
			rd.close();
			try {
				JSONObject res = new JSONObject(result.toString());
				String cookie = (String) res.get("cookie");
				return cookie;
			} catch (Exception e) {
				log.error("Exception in JSON parsing : "+e);
			}
		}catch (IOException e) {
			log.error("IO Exception : "+e);
		} 
		catch (Exception e) {
			log.error("Exception : "+e);
		}
		return null;
	}
	
	/** Eseye SIM Activation */
	public boolean activateSIMReq(String url, String iccid, String tariffId, String cookie, String group) {
		log.info("Entered activateSIMReq...");
		HttpClient httpClient = HttpClientBuilder.create().build();
		try {
			HttpPost request = new HttpPost(url);

			String requestparam = "{\"sims\":[{\"ICCID\":\""+iccid+"\",\"friendlyName\":null,\"IMEI\":null}"
					+ "],\"tariffID\":"+tariffId+",\"group\":\""+group+"\",\"device\":null}";

			StringEntity params = new StringEntity(requestparam);
			log.info("RequestParam : "+requestparam);

			request.addHeader("content-type", "application/json");
			request.addHeader("Cookie","PHPSESSID="+cookie);

			request.setEntity(params);
			HttpResponse response = httpClient.execute(request);

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}
			rd.close();
			log.info("output "+result.toString());

			try {
				JSONObject res = new JSONObject(result.toString());
				JSONObject status = new JSONObject( (res.get("status").toString()));
				String success = (String) status.get("status");
				if(success.equalsIgnoreCase("OK"))
				{
					return true;
				}
				log.info("Request failed code : "+status.get("code") + " Message : "+status.get("message"));
			} catch (Exception e) {
				log.error("Exception 1 JSON "+e);
			}
		}catch (IOException e) {
			log.error("Exception 2 IO "+e);
		} 
		catch (Exception e) {
			log.error("Exception 3 "+e);
		}
		return false;
	}
	
	/** Eseye SIM get SIM status */
	public static String getSims(String url, String sim,String cookie){
		log.info("Entered getsims...");
		HttpClient httpClient = HttpClientBuilder.create().build();
		try {
			HttpPost request = new HttpPost(url);
			String requestParamSrchCriteria = "{\"state\":null,\"group\":null,\"tariff\":null,\"matchString\":\""+sim+"\",\"matchType\":\"M\",\"matchFields\":\"I\"}";

			String reqParam = "{\"searchCriteria\":"+requestParamSrchCriteria+",\"sortOrder\":null,\"startRec\":null,\"numRecs\":null}";
			log.info("getsims : "+reqParam);

			StringEntity params = new StringEntity(reqParam);
			request.addHeader("content-type", "application/json");	
			request.addHeader("Cookie","PHPSESSID="+cookie );

			request.setEntity(params);
			HttpResponse response = httpClient.execute(request);

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {

				result.append(line);
			}
			rd.close();
			JSONObject res = new JSONObject(result.toString());
			JSONObject stat = new JSONObject( (res.get("status").toString()));
			String success = (String) stat.get("status");
			if((!success.equalsIgnoreCase("OK"))||(res.get("totRecs").toString().equals("0")))
			{
				log.info("Request failed code : "+stat.get("errorCode") + " Message : "+stat.get("errorMessage"));
				return "NA";
			}

			log.info("Sim status : "+success);
			JSONArray getArray = res.getJSONArray("sims");
			if(getArray.length() == 1)
			{
				JSONObject objects = getArray.getJSONObject(0);
				return objects.toString();
			}else
				log.info("No records received with given sim:"+sim);


		} catch (Exception e) {
			log.error("getsims Exception:"+e.getMessage());
		}

		return "NA";
	}
	
	/** Eseye SIM unsuspend */
	public boolean reactivateSIMReq(String url,String iccid, String cookie){
		log.info("Entered reactivateSIMReq...");
		HttpClient httpClient = HttpClientBuilder.create().build();

		try {
			HttpPost request = new HttpPost(url);
			String requestparam = "{\"sims\":[\""+iccid+"\"]}";
			log.info(requestparam);

			StringEntity params = new StringEntity(requestparam);

			request.addHeader("content-type", "application/json");
			request.addHeader("Cookie","PHPSESSID="+cookie );

			request.setEntity(params);
			HttpResponse response = httpClient.execute(request);

			BufferedReader rd = new BufferedReader(
					new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line 		= "";

			while ((line = rd.readLine()) != null) {

				result.append(line);
			}
			rd.close();
			log.info("output "+result.toString());

			try {
				JSONObject res = new JSONObject(result.toString());
				JSONObject status = new JSONObject( (res.get("status").toString()));
				String success = (String) status.get("status");
				if(success.equalsIgnoreCase("OK")) {
					return true;
				}
				log.info("Request failed code : "+status.get("code") + " Message : "+status.get("message"));
			} catch (Exception e) {
				log.error("Exception : "+e);
			}
		} catch (Exception e) {
			log.error("Exception 3 "+e);
		}
		return false;
	}
	
	/** Eseye SIM suspend */
	public boolean deactivateSIMReq(String url,String iccid, String cookie){
		log.info("Entered deactivateSIMReq...");
		HttpClient httpClient = HttpClientBuilder.create().build();

		try {
			HttpPost request = new HttpPost(url);
			String requestparam = "{\"sims\":[\""+iccid+"\"]}";
			log.info(requestparam);

			StringEntity params = new StringEntity(requestparam);

			request.addHeader("content-type", "application/json");
			request.addHeader("Cookie","PHPSESSID="+cookie );

			request.setEntity(params);
			HttpResponse response = httpClient.execute(request);

			BufferedReader rd = new BufferedReader(
					new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line 		= "";

			while ((line = rd.readLine()) != null) {

				result.append(line);
			}
			rd.close();
			log.info("output "+result.toString());

			try {
				JSONObject res = new JSONObject(result.toString());
				JSONObject status = new JSONObject( (res.get("status").toString()));
				String success = (String) status.get("status");
				if(success.equalsIgnoreCase("OK")) {
					return true;
				}
				log.info("Request failed code : "+status.get("code") + " Message : "+status.get("message"));
			} catch (Exception e) {
				log.error("Exception : "+e);
			}
		} catch (Exception e) {
			log.error("Exception 3 "+e);
		}
		return false;
	}
	
	public String httpPOSTRequest(String url, String urlParams,String auth, String contentType) {
		log.info("Entered into httpPOSTRequest  : "+url);
		String response = null;
		try {			
			URL url_ = new URL (url);
			HttpURLConnection http = (HttpURLConnection)url_.openConnection();
			http.setRequestMethod("POST");
			http.setDoOutput(true);
			http.setInstanceFollowRedirects(false);
			http.setRequestProperty("Content-Type", contentType);
			http.setRequestProperty("charset", "utf-8");
			http.setUseCaches(false);
			if(auth != null) {
				http.setRequestProperty("auth", auth);
			}
			if(urlParams!=null) {
				byte[] postData = urlParams.getBytes( StandardCharsets.UTF_8 );
				http.setRequestProperty("Content-Length", Integer.toString(postData.length));
				DataOutputStream wr = new DataOutputStream(http.getOutputStream());
				wr.write( postData );
			}					
			int responseCode = http.getResponseCode();	
			log.info("Response Code : "+responseCode);
			InputStream content = (InputStream)http.getInputStream();
			InputStreamReader inpStrmRdr = new InputStreamReader (content);
			BufferedReader bufRdr   = new BufferedReader(inpStrmRdr);
			String line;
			StringBuilder sb = new StringBuilder();
			while ((line = bufRdr.readLine()) != null) {
				sb.append(line);
			}
			log.info("Http POST Response : "+ sb.toString());
			return sb.toString();
		}catch(Exception e) {
			log.error("Exception occured at httpPOSTRequest : "+e.getLocalizedMessage());
		}		
		return response;		
	}
	
	/**
	 * Created By Balaji.
	 * 
	 * This function convert one time zone to another
	 * 
	 * @param simpleDateFormatOfInputDateTime :: Ex : "yyyy-MM-dd HH:mm:ss"
	 * 
	 * @param fromTimeZone :: Ex : "+05:30"
	 * 
	 * @param toTimeZone :: Ex : "+00:00"
	 * 
	 * @param dateTimeToConvert :: Ex : "1999-04-27 05:30:00"
	 * 
	 * @return converted date as a date object
	 */
	public Date timeZoneConverter(String simpleDateFormatOfInputDateTime, String fromTimeZone, String toTimeZone, String dateTimeToConvert) {
		log.info("Entered into timeZoneConverter :: date_format : "+ simpleDateFormatOfInputDateTime +" :: from_time_zone : "+ fromTimeZone+" :: to_time_zone : "+ toTimeZone+" :: date_time : "+ dateTimeToConvert);
		try {
			
			SimpleDateFormat format = new SimpleDateFormat( simpleDateFormatOfInputDateTime);
			format.setTimeZone(TimeZone.getTimeZone("GMT"+fromTimeZone));
			
			SimpleDateFormat formatUTC = new SimpleDateFormat(simpleDateFormatOfInputDateTime);
			formatUTC.setTimeZone(TimeZone.getTimeZone("GMT"+toTimeZone));

			String convertedDateTime = formatUTC.format( format.parse( dateTimeToConvert ) ) ;
			Date date = new SimpleDateFormat( simpleDateFormatOfInputDateTime ).parse( convertedDateTime );
			
			log.info("Date : "+ dateTimeToConvert+" :: converted to : "+ convertedDateTime);
			return date;
			
		} catch (Exception e) {
			log.error("Error in timeZoneConverter :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}
	
	public Date timeZoneConverterfromutc(String simpleDateFormatOfInputDateTime, String fromTimeZone, String toTimeZone, String dateTimeToConvert) {
		log.info("Entered into timeZoneConverter :: date_format : "+ simpleDateFormatOfInputDateTime +" :: from_time_zone : "+ fromTimeZone+" :: to_time_zone : "+ toTimeZone+" :: date_time : "+ dateTimeToConvert);
		try {
			
			SimpleDateFormat format = new SimpleDateFormat( simpleDateFormatOfInputDateTime );
			format.setTimeZone(TimeZone.getTimeZone("GMT"+fromTimeZone));
			
			SimpleDateFormat formatUTC = new SimpleDateFormat(simpleDateFormatOfInputDateTime);
			formatUTC.setTimeZone(TimeZone.getTimeZone("GMT"+toTimeZone));
			formatUTC.setTimeZone(TimeZone.getTimeZone("+00:00"));
			String convertedDateTime = formatUTC.format( format.parse( dateTimeToConvert ) ) ;
			Date date = format.parse( dateTimeToConvert );
			
			log.info("Date : "+ dateTimeToConvert+" :: converted to : "+ convertedDateTime);
			return date;
			
		} catch (Exception e) {
			log.error("Error in timeZoneConverter :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}
	
	public String generateWowzaHash( String meid, String stream_sub_path) {
		log.info("Entered into generateWowzaHash :: MEID : "+ meid);
		try {
			
			String sharedSecret =  base64Encoder( path_prefix + meid );
			String starttime = getCurrentTimeinMillis( false, 0 ) / 1000 + "";
			String endtime = getCurrentTimeinMillis( true, hash_end_time ) / 1000 + "";
			String tokenPrefix = "wowzatoken";
			String contentPath = path_prefix + meid + "/" + stream_sub_path;
			
			ArrayList<String> params = new ArrayList<>();
			
			params.add( tokenPrefix+"startTime="+starttime );
			params.add( tokenPrefix+"endTime="+endtime );
			params.add( sharedSecret );
			
			Collections.sort( params );
			
			String hashString = contentPath + "?";
			
			for( int i = 0; i < params.size(); i++ ) {
				if((i + 1) == (params.size())){
					hashString = hashString + params.get(i);
				} else {
					hashString = hashString + params.get(i) + "&" ;
				}
			}
			
			
	        String securedHash = generateHashString(hashString);

	        securedHash = securedHash.replace("+", "-").replace("/", "_");
	        
	        String hashParams = "?wowzatokenstartTime="+ starttime +"&wowzatokenendTime="+ endtime +"&wowzatokenhash="+securedHash;
	        
	        log.info("Secured Hash: " + securedHash);
			
	        return hashParams;
		} catch (Exception e) {
			log.error("Error in generateWowzaHash :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}
	
	private static String generateHashString(String hashString) {
		log.info("Entered into generateHashString :: hashString : "+ hashString);
		try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = md.digest(hashString.toString().getBytes(StandardCharsets.UTF_8));
            String hash = bytesToBase64(hashBytes);
            log.info("Generated Hash: " + hash);
            return hash;
        } catch (NoSuchAlgorithmException e) {
            log.error("Error in generateHashString :: Error : "+ e.getLocalizedMessage());
        }
		return null;
    }
	
	private static String bytesToBase64(byte[] bytes) {
		log.info("Entered into bytesToBase64");
		try {
			byte[] encodedBytes = Base64.encodeBase64(bytes);
			log.info("encodedBytes " + new String(encodedBytes));
			return new String(encodedBytes);	
		} catch (Exception e) {
			log.error("Error in bytesToBase64 :: Error : "+ e.getLocalizedMessage());
		}
		return null;
    }
	
	public String base64Encoder(String valueBase64) {
		log.info("Entered into base64Encoder");
		try {
			byte[] valueByte = {};
			valueByte = Base64.encodeBase64(valueBase64.getBytes("UTF-8"));
			return new String(valueByte, "UTF-8");
		} catch (Exception e) {
			log.error("Error in base64Encoder :: error : "+e.getLocalizedMessage());
			return null;
		}
	}
	
	public long getCurrentTimeinMillis( boolean addMinutes, int minutes ) {
		log.info("Entered into getCurrentTimeinMillis");
		try {
			Calendar calendar = Calendar.getInstance();
			
			if( addMinutes ) {
				calendar.add( Calendar.MINUTE, minutes);
			}
			
			return calendar.getTimeInMillis();
		} catch (Exception e) {
			log.error("Error in getCurrentTimeinMillis :: Error : "+e.getLocalizedMessage());
			return 0;
		}

	}

	public static CellStyle getHeadersStyle(HSSFWorkbook workbook) {
		/* create style for header cells */
		CellStyle headStyle = workbook.createCellStyle();
		headStyle.setFillForegroundColor(IndexedColors.BLUE_GREY.getIndex());
		headStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
		headStyle.setBorderBottom(HSSFCellStyle.BORDER_MEDIUM);
		headStyle.setBorderLeft(HSSFCellStyle.BORDER_MEDIUM);
		headStyle.setBorderRight(HSSFCellStyle.BORDER_MEDIUM);
		headStyle.setBorderTop(HSSFCellStyle.BORDER_MEDIUM);
		Font headFont = workbook.createFont();
		headFont.setFontHeightInPoints((short) 15);
		headFont.setColor(IndexedColors.WHITE.getIndex());
		headStyle.setAlignment(CellStyle.ALIGN_CENTER);
		headStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
		headStyle.setFont(headFont);
		headStyle.setWrapText(true);
		return headStyle;
	}

	public static void createHeadersRow_2(HSSFWorkbook workbook, HSSFSheet sheet, String headers[]) {
		/* create header row for headers */
		Row headRow = sheet.createRow(0);
		int lastelement = headers.length;

		for (int i = 0; i < lastelement; i++) {
			Cell headCell = headRow.createCell(i);
			headCell.setCellValue(headers[i]);
			headCell.setCellStyle(getHeadersStyle(workbook));
		}
	}

	public String uploadToS3bucket(String filename_Sub) {
		log.info(" Entered Helper :: uploadToS3bucket ");

		String SUFFIX = "/";

		String bucketName = awsBucketName;

		String folderName = awsFolderName;

		try {

			String AWS_ACCESS_KEY = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_access_key");
			String AWS_SECRET_KEY = secretManagerService.getSecretValue(S3_SECRET_NAME, "aws_s3_secret_key");
			BasicAWSCredentials credentials = new BasicAWSCredentials(AWS_ACCESS_KEY, AWS_SECRET_KEY);

			AmazonS3 s3client = AmazonS3ClientBuilder.standard().withRegion("us-west-2")
					.withCredentials(new AWSStaticCredentialsProvider(credentials)).build();

			String fileName = folderName + SUFFIX + filename_Sub;

			s3client.putObject(new PutObjectRequest(bucketName, fileName, new File(filename_Sub))
					.withCannedAcl(CannedAccessControlList.PublicRead));

		} catch (Exception ex) {
			log.error("Error in UploadToS3bucket : " + ex.getMessage());
			return null;
		}
		return "https://s3-us-west-2.amazonaws.com/" + bucketName + "/" + folderName + "/" + filename_Sub;

	}
	public boolean compareDateFor24Hrs(String checkDateTime) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss Z");
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DATE, -1);
		try {
			Date previousDate = sdf.parse(sdf.format(cal.getTime()) + " UTC");
			Date checkDate = sdf.parse(checkDateTime + " UTC");
			long diff = previousDate.getTime() - checkDate.getTime();
			if (diff < 0) {
				return true;
			}
		} catch (ParseException e) {
			log.error("Error in compareDateFor24Hrs : "+e.getLocalizedMessage());
		}
		return false;
	}
	
	public File writeCSVDataPetMonitorHistory(List<JPetmonitorHistory> reports, String fromDate, String toDate, String deviceName, String tempunit) {
		log.info("Entered into writeCSVDataLineByLine");
		try {
			String fileName = deviceName + "-reports-" + fromDate + "---" + toDate + ".csv";

			File file = new File(fileName);
			if (file.exists()) {
				file.delete();
				System.out.println("File already exists");
			}
			CSVWriter csvWriter = new CSVWriter(new FileWriter(file));
			String[] header = {"Date","Temperature","Humidity"};

			// Write headers
			csvWriter.writeNext(header);

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

			int length = reports.size();
			for (int i = 0; i < length; i++) {
				try {

					String dateTime = reports.get(i).getDatetime();

					Date dt = sdf.parse(dateTime);

					String date = sdf.format(dt);

					csvWriter.writeNext(new String[] {date, String.valueOf(reports.get(i).getTemperature())+" °"+tempunit, String.valueOf(reports.get(i).getHumidity())+" %RH" });
				} catch (Exception e) {
					log.error("Error While Write Data Row by Row " + e.getMessage());
				}
			}
			csvWriter.close();
			return file;
		} catch (Exception e) {
			log.error("Error in writeCSVDataLineByLine :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}

    public long getDaysBetweenDate(String startDate, String endDate) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date start_date = sdf.parse(startDate);
			Date end_date = sdf.parse(endDate);

			long diffInMillis = end_date.getTime() - start_date.getTime();
			return TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS);

        } catch (ParseException e) {
            log.error("Error in getDaysBetweenDate : "+e.getLocalizedMessage());
        }
        return -1;
    }

	public long getHoursBetweenDate(String startDate, String endDate) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			Date start_date = sdf.parse(startDate);
			Date end_date = sdf.parse(endDate);

			long diffInMillis = end_date.getTime() - start_date.getTime();
			return TimeUnit.HOURS.convert(diffInMillis, TimeUnit.MILLISECONDS);

		} catch (ParseException e) {
			log.error("Error in getDaysBetweenDate : "+e.getLocalizedMessage());
		}
		return -1;
	}

	public String getCurrentTimeInFormat(String givenDate, String givenPattern, String pattern) {
		SimpleDateFormat givenSdf = new SimpleDateFormat(givenPattern);
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            Date convertDate = givenSdf.parse(givenDate);
			return sdf.format(convertDate);
        } catch (ParseException e) {
			log.error("Error in getCurrentTimeInFormat : " + e.getLocalizedMessage());
		}
		return "NA";
	}
	
	public String httpPOSTRequestFeature(String url, String urlParams,String auth) {
		log.info("Entered into httpPOSTRequest  : "+url);
		String response = null;
		try {			
			URL url_ = new URL (url);
			HttpURLConnection http = (HttpURLConnection)url_.openConnection();
			http.setRequestMethod("POST");
			http.setDoOutput(true);
			http.setInstanceFollowRedirects(false);
			http.setRequestProperty("Content-Type", "application/json");
			http.setRequestProperty("charset", "utf-8");
			http.setUseCaches(false);
			if(auth != null) {
				http.setRequestProperty("auth", auth);
			}
			if(urlParams!=null) {
				byte[] postData = urlParams.getBytes( StandardCharsets.UTF_8 );
				http.setRequestProperty("Content-Length", Integer.toString(postData.length));
				DataOutputStream wr = new DataOutputStream(http.getOutputStream());
				wr.write( postData );
			}					
			int responseCode = http.getResponseCode();	
			log.info("Response Code : "+responseCode);
			InputStream content = (InputStream)http.getInputStream();
			InputStreamReader inpStrmRdr = new InputStreamReader (content);
			BufferedReader bufRdr   = new BufferedReader(inpStrmRdr);
			String line;
			StringBuilder sb = new StringBuilder();
			while ((line = bufRdr.readLine()) != null) {
				sb.append(line);
			}
			log.info("Http POST Response : "+ sb.toString());
			return sb.toString();
		}catch(Exception e) {
			log.error("Exception occured at httpPOSTRequest : "+e.getLocalizedMessage());
		}		
		return response;		
	}

	public String htmlContent(String html) {
		String htmlFront = "<html>" + "<head>"
				+ "<link href='https://fonts.googleapis.com/css?family=Poppins' rel='stylesheet'>" + "<style>"
				+ "body {" + "    font-family: 'Poppins';font-size: 13px;" + "}" + "</style>" + "</head>" + "<body>"
				+ html + "</body>" + "</html>";
		return htmlFront;
	}

	public File writeTextDataTermcondition(List<JTerms.Terms> reports) {
		log.info("Entered into writeTextDataTermcondition");
		try {
			String fileName = "VettermsandConditions.txt";
			File file = new File(fileName);

			if (file.exists()) {
				file.delete();
				System.out.println("File already exists and deleted.");
			}

			BufferedWriter writer = new BufferedWriter(new FileWriter(file));
			writer.write("Terms and Conditions");
			for (JTerms.Terms report : reports) {
				try {
					writer.newLine();
					writer.newLine();
					writer.write("• "+report.getDesc());
					writer.newLine(); // Add blank line between entries
				} catch (Exception e) {
					log.error("Error while writing text line: " + e.getMessage());
				}
			}

			writer.close();
			return file;
		} catch (Exception e) {
			log.error("Error in writeTextDataTermcondition :: " + e.getLocalizedMessage());
		}
		return null;
	}

	public static <T> T unzipContent(byte[] compressedBytes, Class<T> clazz) throws IOException {

		ByteArrayInputStream bais = new ByteArrayInputStream(compressedBytes);
		GZIPInputStream gzipIn = new GZIPInputStream(bais);

		BufferedReader reader = new BufferedReader(new InputStreamReader(gzipIn, StandardCharsets.UTF_8));
		StringBuilder jsonBuilder = new StringBuilder();
		String line;
		while ((line = reader.readLine()) != null) {
			jsonBuilder.append(line);
		}

		gzipIn.close();

		String json = jsonBuilder.toString();
		Gson gson = new Gson();

		return gson.fromJson(json, clazz);
	}

}
