package com.nimble.irisservices.helper;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.log4j.Logger;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import com.nimble.irisservices.dto.HttpResponse;


@Component
public class HttpRequest {

	private static final Logger log = Logger.getLogger(HttpRequest.class);
	
	public HttpResponse httpReq(String reqName, String method, String URL, Map<String, String> headers,
			Map<String, Object> bodyData) {
		log.info("Entered into httpReq :: reqName : "+reqName+" :: method : "+ method +" :: url : "+ URL);
		try {log.info("header : "+ headers.toString());} catch (Exception e) {}
		try {log.info("body data : "+ bodyData.toString());} catch (Exception e) {}
		String response = null;
		HttpResponse httpResponse = new HttpResponse();
		try {

			URL url_ = new URL(URL);
			HttpURLConnection http = (HttpURLConnection) url_.openConnection();
			http.setRequestMethod(method.toUpperCase());
			http.setDoOutput(true);
			http.setInstanceFollowRedirects(false);
			http.setUseCaches(false);

			if (headers != null) {
				headers.forEach((headerName, headerValue) -> {
					http.setRequestProperty(headerName, headerValue);
				});
			}

			boolean isJsonBody = false;
			if (headers != null && !headers.containsKey("Content-Type")) {
				http.setRequestProperty("Content-Type", "application/json");
				isJsonBody = true;
			}

			if (bodyData != null && !bodyData.isEmpty()) {
				String urlParams = "";
				boolean first = true;

				if (!isJsonBody) {
					for (Entry<String, Object> entry : bodyData.entrySet()) {
						if (first)
							first = false;
						else
							urlParams += "&";
						urlParams += URLEncoder.encode(entry.getKey(), "UTF-8") + "="
								+ URLEncoder.encode(String.valueOf(entry.getValue()), "UTF-8");
					}
				} else {
					urlParams = new JSONObject(bodyData).toString();
				}

				byte[] reqBody = urlParams.getBytes(StandardCharsets.UTF_8);
				http.setRequestProperty("Content-Length", Integer.toString(reqBody.length));
				DataOutputStream wr = new DataOutputStream(http.getOutputStream());
				wr.write(reqBody);
			}

			int responseCode = http.getResponseCode();
			log.info("reaponse_code : "+ responseCode);

			InputStream content = (InputStream) http.getInputStream();
			InputStreamReader inpStrmRdr = new InputStreamReader(content);
			BufferedReader bufRdr = new BufferedReader(inpStrmRdr);
			String line;
			StringBuilder sb = new StringBuilder();
			while ((line = bufRdr.readLine()) != null) {
				sb.append(line);
			}
			
			httpResponse.setResponse_code(responseCode);
			httpResponse.setJsonObject( new JSONObject(sb.toString()) );
			
			return httpResponse;
		} catch (Exception e) {
			log.info("Error in httpReq : " + e.getLocalizedMessage());
			return null;
		}
	}

}
