package com.nimble.irisservices.helper;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


public class AES {
	
	private static String defaultSecret = "bluewhale20211e6";
    private static SecretKeySpec secretKey;
    private static byte[] key;
    
	private static final Logger log = LogManager.getLogger(AES.class);

 
    public static void setKey(String myKey) 
    {
        MessageDigest sha = null;
        try {
            key = myKey.getBytes("UTF-8");
            sha = MessageDigest.getInstance("SHA-1");
            key = sha.digest(key);
            key = Arrays.copyOf(key, 16); 
            secretKey = new SecretKeySpec(key, "AES");
        } 
        catch (NoSuchAlgorithmException e) {
        } 
        catch (UnsupportedEncodingException e) {
        }
    }
 
    public static String encrypt(String strToEncrypt) 
    {
    	log.info("Entered into encrypt : "+strToEncrypt);
        try
        {
            setKey(defaultSecret);
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            strToEncrypt = Base64.getEncoder().encodeToString(cipher.doFinal(strToEncrypt.getBytes("UTF-8")));
            strToEncrypt = strToEncrypt.replaceAll("[^a-zA-Z0-9]", "-");
        } 
        catch (Exception e) 
        {
            log.error("Error while encrypting: " + e.getLocalizedMessage());
        }
        return strToEncrypt;
    }
 
    public static String decrypt(String strToDecrypt) 
    {
        try
        {
            setKey(defaultSecret);
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5PADDING");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            return new String(cipher.doFinal(Base64.getDecoder().decode(strToDecrypt)));
        } 
        catch (Exception e) 
        {
            log.error("Error while decrypting: " + e.getLocalizedMessage());
        }
        return null;
    }
    public static String encode(String encryptionKeyString, String originalMessage) {

		try {

			byte[] encryptionKeyBytes = encryptionKeyString.getBytes();
			Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
			
			SecretKey secretKey = new SecretKeySpec(encryptionKeyBytes, "AES");
			int maxKeyLen = Cipher.getMaxAllowedKeyLength("AES");
			cipher.init(Cipher.ENCRYPT_MODE, secretKey);
			byte[] encryptedMessageBytes = cipher.doFinal(originalMessage.getBytes("UTF-8"));
			String base = Base64.getEncoder().encodeToString(encryptedMessageBytes);
			return base;

		} catch (IllegalBlockSizeException | BadPaddingException | UnsupportedEncodingException e) {
			log.error("Error while encode:  " + e.toString());
		} catch (NoSuchAlgorithmException | NoSuchPaddingException e) {
			log.error("Error while encode: " + e.toString());
		} catch (InvalidKeyException e) {
			log.error("Error while encode: " + e.toString());
		}

		return null;
	}

	public static String decode( String encodeMessage,String encryptionKeyString) {
		try {

			byte[] encryptionKeyBytes = encryptionKeyString.getBytes();
			Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
			SecretKey secretKey = new SecretKeySpec(encryptionKeyBytes, "AES");
			byte[] base = Base64.getDecoder().decode(encodeMessage);
			cipher.init(Cipher.DECRYPT_MODE, secretKey);
			
			byte[] aes = cipher.doFinal(base);
			String finaloutput = new String(aes);

			return finaloutput;

		} catch (IllegalBlockSizeException | BadPaddingException e) {
			// TODO Auto-generated catch block
			log.error("Error while decode: " + e.toString());
		} catch (NoSuchAlgorithmException | NoSuchPaddingException e) {
			// TODO Auto-generated catch block
			log.error("Error while decode: " + e.toString());
		} catch (InvalidKeyException e) {
			// TODO Auto-generated catch block
			log.error("Error while decode: " + e.toString());
		} catch (Exception e) {
			log.error("Error while decode: " + e.toString());
		}
		return null;
	}
	
	// For New Flutter 
	public static String decodeV2( String encodeMessage,String encryptionKeyString) {
		try {

			byte[] encryptionKeyBytes = encryptionKeyString.getBytes();
			Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
			SecretKey secretKey = new SecretKeySpec(encryptionKeyBytes, "AES");
			GCMParameterSpec params = new GCMParameterSpec(128, encryptionKeyBytes);
			byte[] base = Base64.getDecoder().decode(encodeMessage);
			cipher.init(Cipher.DECRYPT_MODE, secretKey, params);
			
			byte[] aes = cipher.doFinal(base);
			String finaloutput = new String(aes);

			return finaloutput;

		} catch (IllegalBlockSizeException | BadPaddingException e) {
			log.error("Error while decode: " + e.toString());
		} catch (NoSuchAlgorithmException | NoSuchPaddingException e) {
			log.error("Error while decode: " + e.toString());
		} catch (InvalidKeyException e) {
			log.error("Error while decode: " + e.toString());
		} catch (Exception e) {
			log.error("Error while decode: " + e.toString());
		}
		return null;
	}
}
