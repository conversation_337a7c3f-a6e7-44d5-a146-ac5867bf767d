package com.nimble.irisservices.helper;

import java.io.File;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.appender.RollingRandomAccessFileAppender;
import org.apache.logging.log4j.core.config.Configuration;
import org.springframework.stereotype.Repository;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.nimble.irisservices.dto.AdvanceLog;
import com.nimble.irisservices.dto.ApiContext;


@Repository
public class AdvanceLogHelper {

	private static final Logger log = LogManager.getLogger(AdvanceLogHelper.class);
	private static final Logger ADLOGGER = LogManager.getLogger("AdvanceLogger");
	private static final Logger ADLOGGER2 = LogManager.getLogger("AdvanceLogger2");
	private static final Logger ADLOGGER3 = LogManager.getLogger("AdvanceLogger3");
	private static final Logger ADLOGGER4 = LogManager.getLogger("AdvanceLogger4");

	/**
	 * Get ApiContext
	 *
	 * @return
	 */
	public ApiContext getApiContext() {
		ApiContext apiContext = new ApiContext();
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
				.getRequest();
		if (request.getAttribute("ApiContext") != null) {
			apiContext = (ApiContext) request.getAttribute("ApiContext");
		}
		return apiContext;
	}

	/**
	 * Set ApiContext
	 *
	 * @param apiContext
	 */
	public void setApiContext(ApiContext apiContext) {
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
				.getRequest();
		request.setAttribute("ApiContext", apiContext);
	}

	/**
	 * Get Time difference in seconds
	 *
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public String getTimeDifference(Date startTime, Date endTime) {
		double diffenceFloat = (double) (endTime.getTime() - startTime.getTime()) / 1000;
		DecimalFormat df = new DecimalFormat("#.###");

		//	        String diffenceSeconds = String.valueOf((diffenceFloat / 1000 % 60));
		return String.valueOf(df.format(diffenceFloat));
	}

	/**
	 * Update performance log
	 *
	 * @param apiContext
	 */
	public void updatePerformanceLog(ApiContext apiContext) {
		try {
			// Get log file from the advanced logger file appender
			LoggerContext ctx = (LoggerContext) LogManager.getContext(false);
			Configuration config = ctx.getConfiguration();
			//	            ApiContext apiContext = getApiContext();
			RollingRandomAccessFileAppender fileAppender = null;

			//			
			fileAppender = (RollingRandomAccessFileAppender) config.getAppender("AdvanceLogger");
			if (fileAppender != null) {

				// Check file access permission
				File file = new File(fileAppender.getFileName());
				if (!file.canWrite()) {
					log.error("Unable to create/update advanced log");
				} else {
					// Generate log data and update log file
					DateFormat advformatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

					AdvanceLog advanceLog = apiContext.getAdvanceLog();
					String fileContent =  apiContext.getRequestUri() + ","
							+ apiContext.getMethod() +","
							+apiContext.getApi() +","
							+apiContext.getAuthKey() +","
							+ advanceLog.getAuthenticationValidation() + ","
							+ advanceLog.getGetUserElapTime() + ","
							+ advanceLog.getGetNumberElapTime() + ","

							+ advanceLog.getGetGeneralDataElapTime() + ","
							+ advanceLog.getGetLatestReferEarnElapTime() +","
							+ advanceLog.getGetExternalConfigValueElapTime()+","

							+ advanceLog.getGetUserByNameElapTime()+","
							+ advanceLog.getGetUserByEmailElapTime()+","
							+ advanceLog.getUpdateLastLoginTypeAndTimeElapTime()+","
							+ advanceLog.getLoginViaGoogleOrFacebook()+","
							+advanceLog.getGetSignTypeElapTime()+","
							+advanceLog.getGetUserBySignUpTokenElapTime()+","
							+advanceLog.getSignUpElapTime()+","


							+advanceLog.getUpdateUserCompleteSetUpElapTime()+","
							+advanceLog.getGetZipCodeDetailsElapTime()+","
							+advanceLog.getSaveZipCodeElapTime()+","
							+advanceLog.getUpdateUserElapTime()+","
							+advanceLog.getCreateEmailVerificationTokenElapTime()+","
							+advanceLog.getSendVerificationMailElapTime()+","
							+advanceLog.getGetUserByIdElapTime()+","

							+advanceLog.getUsertokenElapTime()+","
							+advanceLog.getSaveOrUpdateUserTokenElapTime()+","

							+advanceLog.getGetUserByNameV2()+","

							+advanceLog.getGetAssetSummaryTestElapTime()+","
							+advanceLog.getGetCompanyConfigServiceElapTime()+","
							+advanceLog.getGetLastgatewayreportElapTime()+","
							+advanceLog.getGetLstGatrptElapTime()+","
							+advanceLog.getGetLastGatewayReportsElapTime()+","
							+advanceLog.getGetLastGatewayElapTime()+","
							+advanceLog.getGetLastGroupsElapTime()+","
							+advanceLog.getGetLastAssetinformationElapTime()+","
							+advanceLog.getGetAssetDescriptionElapTime()+","

							+advanceLog.getGetGatewaySummaryTestV4ElapTime()+","
							+advanceLog.getGetUserId_cmpIdByAuthElapTime()+","
							+advanceLog.getGetLastgatewayreportV4ElapTime()+","

							+advanceLog.getGetCompanyConfigControllerElapTime()+","
							+advanceLog.getGetGatewaysElapTime()+","
							+advanceLog.getGetURLElapTime()+","

							+advanceLog.getGetCompanyConfigV4ElapTime()+","
							+advanceLog.getVerifyAuthKeyV2ElapTime()+","
							+advanceLog.getGetCompanyConfigAndCompanyElaptime()+","
							+advanceLog.getIsMeidMappedInOrdermapElapTime()+","

							+advanceLog.getGetAlertCfgByIdElapTime()+","
							+advanceLog.getGetAlertCfgElapTime()+","
							+advanceLog.getGetGatewayElapTime()+","

							+advanceLog.getGetAlertCfgByIdV4ElapTime()+","
							+advanceLog.getGetAlertCfgV4ElapTime()+","
							+advanceLog.getGetGatewayConfigElapTime()+","

							+advanceLog.getGetCurrentSubscriptionPlanElapTime()+","
							+advanceLog.getGetSubscriptionElapTime()+","
							+advanceLog.getGetChargebeePlanByIdElapTime()+","
							+advanceLog.getGetPlanAndPeriodElapTime()+","
							+advanceLog.getGetSubsPlanByIdElapTime()+","
							+advanceLog.getCheckDeviceConfigStatusElapTime()+","
							+advanceLog.getGetGatewayByUserElapTime()+","
							+advanceLog.getGetGatewaysByReportTime()+","

							+advanceLog.getGetFurBitDailyReportElapTime()+","
							+advanceLog.getGetGatewayByMonitorTypeElapTime()+","
							+advanceLog.getGetFurBitDailyReportServiceElapTime()+","
							+advanceLog.getGetFurBitBatteryLifeElapTime()+","
							+advanceLog.getGetPetProfileElapTime()+","

							+advanceLog.getGetPetSafetyUrlsElapTime()+ ","
							+advanceLog.getGetRvPetSafetyBlogUrlElapTime()+ ","

							+advanceLog.getGetadvertisementinfoElapTime()+","
							+advanceLog.getGetAdvertismentUrlElapTime()+","

							+advanceLog.getEnabledisablealertcfgElapTime()+","
							+advanceLog.getGetApiElpaTime()+","
							+advanceLog.getGetCmpAccountElapTime()+","
							+advanceLog.getUpdateElapTime()+","
							+advanceLog.getSaveOrUpdateCmpAccountElapTime()+","

							+advanceLog.getGetAssetSummaryv3ElapTime()+","
							+advanceLog.getGetProbeCategoryElapTime()+","
							+advanceLog.getGetDeviceSubscriptionElapTime()+","

							+advanceLog.getGetNotificaitonByUserIDElapTime()+","
							+advanceLog.getUserNotificationsElapTime()+","
							+advanceLog.getGetUserNotificationStatusElapTime()+","
							+advanceLog.getUpdateUserNotificationElapTime()+","

							+advanceLog.getGenerateReferralLinkElapTime()+","
							+advanceLog.getGetLatestReferralCreditsElapTime()+","

							+advanceLog.getGetTrendingVideosListElapTime()+","
							+advanceLog.getGetTrendingvideosElapTime()+","
							+advanceLog.getGetTrendingvideoInfoElapTime()+","

							+advanceLog.getSaveORupdateGatewayByIdElapTime()+","

							+advanceLog.getSaveCompanyConfigElapTime()+","
							+advanceLog.getUpdateCompanyCfgElapTime()+","

							+ advformatter.format(apiContext.getInTime()) + ","
							+ advformatter.format(apiContext.getOutTime()) + "," 
							+ apiContext.getElapsedTime();

					ADLOGGER.info(fileContent);
				}
			} else {
				log.error("Unable to intialize advance logger. Please check the configuration.");
			}
			//			

		} catch (Exception ex) {
			log.error(ex.getMessage());
		}

	}

	public void updatePerformanceLog2(ApiContext apiContext) {
		try {
			// Get log file from the advanced logger file appender
			LoggerContext ctx = (LoggerContext) LogManager.getContext(false);
			Configuration config = ctx.getConfiguration();
			//	            ApiContext apiContext = getApiContext();
			RollingRandomAccessFileAppender fileAppender = null;

			//			
			fileAppender = (RollingRandomAccessFileAppender) config.getAppender("AdvanceLogger2");
			if (fileAppender != null) {

				// Check file access permission
				File file = new File(fileAppender.getFileName());
				if (!file.canWrite()) {
					log.error("Unable to create/update advanced log");
				} else {
					// Generate log data and update log file
					DateFormat advformatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

					AdvanceLog advanceLog = apiContext.getAdvanceLog();
					String fileContent =  apiContext.getRequestUri() + ","
							+ apiContext.getMethod() +","
							+apiContext.getApi() +","
							+apiContext.getAuthKey() +","
							+ advanceLog.getAuthenticationValidation() + ","

							+advanceLog.getGetOrderChannelElapTime()+","
							+advanceLog.getGetOrderChannelServiceElapTime()+","

							+advanceLog.getCheckWifiStatusV2ElapTime()+","
							+advanceLog.getIsAlreadycontainElapTime()+","
							+advanceLog.getGetFurBitBatteryLifeElapTime()+","
							+advanceLog.getGetFurBitlastreportElapTime()+","

							+advanceLog.getGetAvailableUpgradeSubscriptionPlanElapTime()+","
							+advanceLog.getGetAvailUpgradePlansElapTime()+","
							+advanceLog.getGetRemainingDaysElapTime()+","

							+advanceLog.getGetAlertSummaryElapTime()+","
							+advanceLog.getGetCompanyConfigServiceElapTime()+","
							+advanceLog.getGetackalertsElapTime()+","
							+advanceLog.getGetAssetDescriptionElapTime()+","

							+advanceLog.getGetCompanyByIdElapTime()+","
							+advanceLog.getGetCompanyElapTime()+","

							+advanceLog.getGetAlertTypeElapTime()+","
							+advanceLog.getGetAlertTypesElapTime()+","

							+advanceLog.getGetPetProfile_ElapTime()+","
							+advanceLog.getGetJPetprofilesByUserElapTime()+","

							+advanceLog.getGetUserLeaderboardDetails_c_ElapTime()+","
							+advanceLog.getGetUserLeaderboardDetails_s_ElapTime()+","

							+advanceLog.getGetLeaderboardDetails_C_ElepTime()+","
							+advanceLog.getGetGatewayByUserElapTime()+","
							+advanceLog.getGetLeaderBoardDetails_S_ElepTime()+","

							+advanceLog.getUpdateGoalSetting_C_ElepTime()+","
							+advanceLog.getUpdateGoalSetting_S_ElepTime()+","

							+advanceLog.getGetSpeciesElapTime()+","
							+advanceLog.getGetPetSpeciesElapTime()+","

							+advanceLog.getGetPetBreedsElapTime()+","
							+advanceLog.getGetAllPetBreedsElapTime()+","
							+advanceLog.getGetPetSpeciesByNameElapTime()+","
							+advanceLog.getGetPetBreedsElapTime()+","

							+advanceLog.getGetZipcodeElapTime()+","
							+advanceLog.getGetZipCodeDetailsElapTime()+","
							+advanceLog.getSaveZipCodeElapTime()+","

							+advanceLog.getUserUpdateElapTime()+","
							+advanceLog.getGetUserByEmailElapTime()+","
							+advanceLog.getGetUserByNameElapTime()+","
							+advanceLog.getUpdateUserElapTime()+","

							+advanceLog.getPasswordUpdateElapTime()+","

							+advanceLog.getIsForceUpdateElapTime()+","

							//							+advanceLog.getUserSignupElapTime()+","

							+advanceLog.getGetUserByNameV2()+","

							+advanceLog.getGetSubscriptionPlanByMonitortypeElapTime()+","
							+advanceLog.getGetUserByIdElapTime()+","
							+advanceLog.getGetPlanAndPeriodElapTime()+","
							+advanceLog.getGetAvailUpgradePlanNewElapTime()+","

							+advanceLog.getUpdateSubscriptionPlanElapTime()+","
							+advanceLog.getGetChargebeePlanByIdElapTime()+","
							+advanceLog.getGetOrderMappingByUserElapTime()+","
							+advanceLog.getGetCreditAmountBySKUElapTime()+","

							+advanceLog.getResendVerificationLinkElapTime()+","
							+advanceLog.getSendVerificationMailElapTime()+","
							+advanceLog.getGetUserInRoleElapTime()+","

							+advanceLog.getUserdeviceinfoElapTime()+","
							+advanceLog.getSaveOrUpdateUserDeviceInfoElapTime()+","

							+advanceLog.getActivateUserElapTime()+","
							+advanceLog.getGetAssetModelByNameElapTime()+","
							+advanceLog.getGetGroupsElapTime()+","
							+advanceLog.getGatewayExitsinDBElapTime()+","
							+advanceLog.getSaveORupdateQRCGatewayElapTime()+","
							+advanceLog.getSaveORupdateAlertCfgElapTime()+","
							+advanceLog.getCreateUserInChargebeeElapTime()+","
							+advanceLog.getSaveOrderMappingDetailsElapTime()+","
							+advanceLog.getSaveorupdateofflineUserDetailsElapTime()+","
							+advanceLog.getUpdateRegisterUserEmailStatusElapTime()+","

							+advanceLog.getGetTempoeratureElapTime()+","

							+advanceLog.getSendEmailControllerElapTime()+","
							+advanceLog.getSendEmailServiceElapTime()+","

							+advanceLog.getConfigureDeviceElapTime()+","

							+advanceLog.getGetFurBitLastGatewayReportElapTime()+","
							+advanceLog.getGetFLastGatewayReportByUserElapTime()+","
							+advanceLog.getConvertJFurBitLastGatewayReportElapTime()+","

							+advanceLog.getGetFurBitReportControllerElapTime()+","
							+advanceLog.getGetGatewayElapTime()+","
							+advanceLog.getGetFurBitReportServiceElapTime()+","

							+ advformatter.format(apiContext.getInTime()) + ","
							+ advformatter.format(apiContext.getOutTime()) + "," 
							+ apiContext.getElapsedTime();


					ADLOGGER2.info(fileContent);
				}
			} else {
				log.error("Unable to intialize advance logger. Please check the configuration.");
			}
			//			

		} catch (Exception ex) {
			log.error(ex.getMessage());
		}

	}

	public void updatePerformanceLog3(ApiContext apiContext) {


		try {
			LoggerContext ctx = (LoggerContext) LogManager.getContext(false);
			Configuration config = ctx.getConfiguration();
			RollingRandomAccessFileAppender fileAppender = null;

			fileAppender = (RollingRandomAccessFileAppender) config.getAppender("AdvanceLogger2");
			if (fileAppender != null) {

				File file = new File(fileAppender.getFileName());
				if (!file.canWrite()) {
					log.error("Unable to create/update advanced log");
				} else {
					DateFormat advformatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

					AdvanceLog advanceLog = apiContext.getAdvanceLog();
					String fileContent =  apiContext.getRequestUri() + ","
							+ apiContext.getMethod() +","
							+apiContext.getApi() +","
							+apiContext.getAuthKey() +","
							+ advanceLog.getAuthenticationValidation() + ","

							+advanceLog.getGetUserId_cmpIdByAuthElapTime()+","
							+advanceLog.getGetCompanyById_V4_C_ElapTime()+","
							+advanceLog.getGetCompanyElapTime()+","

							+advanceLog.getGetAlertV3_V4Elaptimes()+","
							+advanceLog.getGetackalertsElapTime()+","
							+advanceLog.getGetAssetDescriptionElapTime()+","

							+advanceLog.getGetGeneraldataV4ElapTime()+","
							+advanceLog.getGetLatestReferEarnV4ElapTime()+","

							+advanceLog.getGenerateReferralLinkV4ElapTime()+","
							+advanceLog.getGetEmailByAuthElapTime()+","
							+advanceLog.getGetLatestReferralCreditsElapTime()+","

							+advanceLog.getGetUserV2_V4ElapTime()+","
							+advanceLog.getGetUserById_CmpIdElapTime()+","

							+advanceLog.getGetPetProfileV4ElapTime()+","
							+advanceLog.getGetJPetprofilesByUserV4ElapTime()+","

							+advanceLog.getGetTrendingVideosListV4ElapTime()+","
							+advanceLog.getGetTrendingvideoInfoserviceV4ElapTime()+","
							+advanceLog.getUpdateYoutubeStatisticsElapTime()+","

							+advanceLog.getGetOrderChannelV4ElapTime()+","
							+advanceLog.getGetOrderChannelV4ServiceElapTime()+","

							+advanceLog.getGetBreedsV4ElapTime()+","
							+advanceLog.getGetBreedsV4ServiceElapTime()+","

							+advanceLog.getUsertokenV4ElapTime()+","
							+advanceLog.getSaveOrUpdateUserTokenV4ElapTime()+","

							+advanceLog.getGetUserByUsernameV4ElapTime()+","
							+advanceLog.getGetUserByNameV4ElapTime()+","

							+advanceLog.getPasswordUpdateV4ElapTime()+","
							+advanceLog.getUpdateUserV4ElapTime()+","

							+advanceLog.getLoginV4ElapTime()+","
							+advanceLog.getGetUserByUsernameV4forloginElapTime()+","

							+advanceLog.getUpdateGoalSettingsV4ElapTime()+","
							+advanceLog.getUpdateGoalSetting_S_ElepTime()+","

							+advanceLog.getGetNotificaitonByUserIDV4ElapTime()+","
							+advanceLog.getUserNotificationsV4ElapTime()+","
							+advanceLog.getGetUserNotificationStatusV4ElapTime()+","
							+advanceLog.getUpdateUserNotificationElapTime()+","							


							+advanceLog.getUserUpdateV4ElapTime()+","
							+advanceLog.getGetUserId_cmpIdByAuthV4ElapTime()+","
							+advanceLog.getGetUserByUNameOrEmailV4ElapTime()+","
							+advanceLog.getUpdateUserV4byuseridbyuseridV4forloginElapTime()+","	

							+advanceLog.getUpdateVideoStatusV4V4ElapTime()+","
							+advanceLog.getUpdateVideoInfoTransactionV4ElapTime()+","
							+advanceLog.getCreateVideoInfoTransactionV4ElapTime()+","

							+advanceLog.getGetAlertType_V4()+","
							+advanceLog.getGetAlertTypesV4()+","

							+advanceLog.getUserdeviceinfoV4ElapTime()+","
							+advanceLog.getSaveOrUpdateUserDeviceInfoV4ElapTime()+","

							+advanceLog.getUpdateCompanyCfgV4ElapTime()+","
							+advanceLog.getGetCompanyConfigV2V4ElapTime()+","
							+advanceLog.getIsMeidMappedInOrdermapElapTime()+","

							+advanceLog.getVerifyAuthV3()+","
							+advanceLog.getVerifyAuthV4()+","
							
							
							+advanceLog.getUpdateAlertcfgControllerV4ElapTime()+","
							+advanceLog.getEnabledisablealertcfgV4ElapTime()+","
							+advanceLog.getUpdateEmailPhoneV4ElapTime()+","
							+advanceLog.getUpdateNotifyV4ElapTime()+","
							+advanceLog.getUpdateAlertCfgServiceV4ElapTime()+","
					
							+advanceLog.getGetUnackAlertsV4ElapTime()+","
							+advanceLog.getGetAlertCfgV4ControllerV4ElapTime()+","

							+advanceLog.getSaveorupdatePetProfileV4ControllerElapTime()+","
							+advanceLog.getSaveorupdatePetProfileV4ServiceElapTime()+","
							+advanceLog.getUpdateGatewayNameElapTime()+","

							+advanceLog.getGetUserId_cmpIdByAuthV2()+","	

							+advanceLog.getGetGatewayElapTime()+","
							+advanceLog.getGetPetSpeciesByNameElapTime()+","

							+advanceLog.getGetGatewayV4ElapTime()+","
							
							+advanceLog.getGetadvertisementinfoV4()+","
							+advanceLog.getGetSpeciesV4()+","
							
							+advanceLog.getGetFurBitDailyReportv4_controller()+","
							+advanceLog.getGetFurBitDailyReport_Service()+","
							+advanceLog.getGetFurBitReportv4_controller()+","
							+advanceLog.getGetFurBitReport_Service()+","
													
							+ advformatter.format(apiContext.getInTime()) + ","
							+ advformatter.format(apiContext.getOutTime()) + "," 
							+ apiContext.getElapsedTime();


					ADLOGGER3.info(fileContent);
				}
			} else {
				log.error("Unable to intialize advance logger. Please check the configuration.");
			}

		} catch(Exception ex) {
			log.error(ex.getMessage());
		}

	}

	public void updatePerformanceLog4(ApiContext apiContext) {


		try {
			LoggerContext ctx = (LoggerContext) LogManager.getContext(false);
			Configuration config = ctx.getConfiguration();
			RollingRandomAccessFileAppender fileAppender = null;

			fileAppender = (RollingRandomAccessFileAppender) config.getAppender("AdvanceLogger4");
			if (fileAppender != null) {

				File file = new File(fileAppender.getFileName());
				if (!file.canWrite()) {
					log.error("Unable to create/update advanced log");
				} else {
					DateFormat advformatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

					AdvanceLog advanceLog = apiContext.getAdvanceLog();
					String fileContent =  apiContext.getRequestUri() + ","
							+ apiContext.getMethod() +","
							+apiContext.getApi() +","
							+apiContext.getAuthKey() +","
							+ advanceLog.getAuthenticationValidation() + ","

							+advanceLog.getVerifyAuthV3()+","
							+advanceLog.getVerifyAuthV4()+","

							+advanceLog.getCheckQrcExistInDbElapTime()+ ","
							+advanceLog.getExternalQrcActivationStatusElapTime()+ ","
							+advanceLog.getCheckQrcExistElapTime()+ ","

							+advanceLog.getSaveWifiInfoElapTime()+ ","
							+advanceLog.getGetGatewayAndUserDetailsElapTime()+ ","
							+advanceLog.getIsAlreadycontainElapTime()+ ","
							+advanceLog.getSaveOrUpdateWifiInfoElapTime()+ ","

							+advanceLog.getGetWifiinfoListElapTime()+ ","
							+advanceLog.getGetWifiListElapTime()+ ","

							+advanceLog.getForgetPasswordElapTime()+ ","
							+advanceLog.getSendForgotPasswordMailElapTime()+ ","

							+ advformatter.format(apiContext.getInTime()) + ","
							+ advformatter.format(apiContext.getOutTime()) + "," 
							+ apiContext.getElapsedTime();

					ADLOGGER4.info(fileContent);
				}
			} else {
				log.error("Unable to intialize advance logger. Please check the configuration.");
			}

		} catch(Exception ex) {
			log.error(ex.getMessage());
		}
	}

}
