package com.nimble.irisservices.helper;

import java.text.DecimalFormat;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class EntityHashing {

	private static final Logger log = LogManager.getLogger(EntityHashing.class);

	public static String encryptPassword(String password) {
		log.info("Entered encrypt password");
		int[] dec = new int[password.length()];

		int octVal = 0;
		// Convert given password to byte value
		try {
			for (int i = 0; i < password.length(); i++) {
				byte[] incDec = {};

				if (i % 2 == 0) {
					incDec = Character.toString(password.charAt(i)).getBytes("Windows-1252");

					if (i == 0) {
						// Get octal value for first char to increment or decrement
						String octValue = Integer.toOctalString(incDec[i]);
						for (int j = 0; j < octValue.length(); j++) {
							octVal = octVal + Character.getNumericValue(octValue.charAt(j));
						}
					}
					dec[i] = (incDec[0] & 0xff) + octVal;
				} else {
					incDec = Character.toString(password.charAt(i)).getBytes("Windows-1252");
					dec[i] = (incDec[0] & 0xff) - octVal;
				}
			}

			String hexStr = "";
			// Convert decimal to hexa decimal value
			for (int i = 0; i < dec.length; i++) {
				String str = Integer.toHexString(dec[i]);
				hexStr = hexStr + str;
			}

			// reverse the string to encrypt
			StringBuilder revFirst = new StringBuilder();
			revFirst.append(hexStr);
			revFirst.reverse();

			String one = revFirst.substring(0, revFirst.length() / 2);
			String two = revFirst.substring(revFirst.length() / 2, revFirst.length());

			// Encrypted password
			String revStr = two + one + new DecimalFormat("00").format(octVal);
			return revStr;
		} catch (Exception e) {
			log.error("Exception in EValidation password encryption - " + e.getLocalizedMessage());
		}
		return null;
	}

	public static String decryptPassword(String encrypt) {
		log.info("Entered decrypt password");
		String decryptedPwd = "";

		// Get increment value and encrypted password
		String getIncVal = encrypt.substring(encrypt.length() - 2, encrypt.length());
		String encryptPwd = encrypt.substring(0, encrypt.length() - 2);

		try {
			// reverse the string to convert actual password hex value
			StringBuilder revFirst = new StringBuilder();
			revFirst.append(encryptPwd);
			revFirst.reverse();

			String one = revFirst.substring(0, revFirst.length() / 2);
			String two = revFirst.substring(revFirst.length() / 2, revFirst.length());

			// Reverse and get actual hexa decimal value
			String hexVal = two + one;
			String[] hex = new String[encrypt.length() / 2];
			char[] hexArray = hexVal.toCharArray();
			byte decimalVal;
			char decToChar;
			byte[] dec = new byte[encryptPwd.length() / 2];
			for (int i = 0, j = 0; i < hexArray.length; i = i + 2, j++) {
				hex[j] = hexArray[i] + "" + hexArray[i + 1];
				// Converting hexadecimal to byte
				decimalVal = (byte) Integer.parseInt(hex[j], 16);
				// Increment and decrement to get actual byte value
				if (j % 2 == 0) {
					decimalVal = (byte) (decimalVal - Integer.parseInt(getIncVal));
				} else {
					decimalVal = (byte) (decimalVal + Integer.parseInt(getIncVal));
				}
				// Push byte value into byte array
				dec[j] = (byte) (decimalVal & 0xff);
			}

			// Convert byte array to ansi string
			decryptedPwd = new String(dec, "Windows-1252");
			return decryptedPwd;
		} catch (Exception e) {
			log.error("Exception in EValidation password decryption - " + e.getLocalizedMessage());
		}

		return null;
	}

	public static String encryptUserid(long userid) {
		log.info("Entered encrypt user id : " + userid);
		String useridStr = String.valueOf(userid);
		String byteMerge = "";
		try {
			for (int i = 0, j = useridStr.length(); i < useridStr.length(); i++, j--) {
				// getting single character from userid
				char currChar = useridStr.charAt(i);

				// getting ascii value for character
				byte charToByte = (byte) currChar;
				int addByte = charToByte + j;

				byteMerge += addByte;
			}
			long encodedVal = Long.parseLong(byteMerge);
			// converting long value to hexa string
			String toEncode = Long.toHexString(encodedVal);

			StringBuffer encodedStr = new StringBuffer(toEncode);
			encodedStr.reverse();

			return new String(encodedStr);

		} catch (Exception e) {
			log.error("Exception in EValidation userid encryption - " + e.getLocalizedMessage());
		}
		return null;
	}

	public static String decryptUserid(long userid) {
		log.info("Entered decrypt user id : " + userid);
		StringBuffer strBuff = new StringBuffer(String.valueOf(userid));
		strBuff.reverse();

		try {
			String actual = new String(strBuff);
			// converting userid string to hexadecimal value
			long val = Long.parseLong(actual, 16);
			String actualStr = String.valueOf(val);
			String number = "";
			for (int i = actualStr.length() - 1, j = 1; i > 0; i = i - 2, j++) {
				// get last two characters from actualStr
				String numToInt = Character.toString(actualStr.charAt(i - 1)) + Character.toString(actualStr.charAt(i));
				// subtract the length of userid from numToInt
				int currNum = Integer.parseInt(numToInt) - j;

				byte currByte = (byte) currNum;
				// add ascii value to the final string
				number = Character.toString((char) currByte) + number;
			}
			return number;
		} catch (Exception e) {
			log.error("Exception in EValidation userid decryption - " + e.getLocalizedMessage());
		}
		return null;
	}
}
