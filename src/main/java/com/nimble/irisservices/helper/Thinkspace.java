package com.nimble.irisservices.helper;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.nimble.irisservices.Util.IrisservicesUtil;
import com.nimble.irisservices.dto.UserV4;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.SimReactivationHistory;
import com.nimble.irisservices.service.IGatewayServiceV4;
import com.nimble.irisservices.service.IUserServiceV4;
import com.nimble.irisservices.service.IVerizonService;


@Component
public class Thinkspace {
	
	private final Logger log = LogManager.getLogger(Thinkspace.class);
	
	@Value("#{${zip_code_map}}")
	private Map<String,String> zipCodeMap;
	
	IrisservicesUtil irisUtil;
	
	private int zipCodeType = 1;
	
	@Autowired
	IUserServiceV4 userServiceV4;
	
	@Autowired
	IGatewayServiceV4 gatewayServiceV4;

	public String SimActivation(String meids, String cust_name, String acc_name, String plan, String iccid,String reqId,IVerizonService verizonService, String skuNumber) {
		String simActivateStatus = "";
		String status="";
		String previousSIMStatus = "";
		try {

			String simStatus="";
			String access_token = getAPItoken();
			String session_token = callVerLogin(access_token);

			if (!session_token.contains("no-token")) {
				status=getversiminfo( access_token, session_token,iccid);
				previousSIMStatus = status;
			}
			if(isvalid_for_activate(status))
			{
				simStatus=callVerActivate(meids, acc_name, plan, cust_name, access_token, session_token,iccid,reqId,verizonService,skuNumber);
				simActivateStatus = simStatus.replace(":", "");
			}
			else if(isvalid_for_restore(status)) {
				simStatus = callVerService(meids, acc_name, plan, cust_name, access_token, session_token,iccid,"restore",reqId,verizonService);
				simActivateStatus = simStatus.replace(":", "");
			}
			else if(status.toLowerCase().contains("<error>"))
			{	
				log.info( "SIM status error : "+status );
				simActivateStatus = "SIM Activation Failed. <br> Error message from Verizon : "+status ;
			}
			else
			{
				log.info( "SIM status else part : "+status );
				simActivateStatus = "SIM Activation Failed.<br> Error message from Verizon : "+status ;
			}

			status=getversiminfo( access_token, session_token,iccid);

			log.info("SimActivation : ",simActivateStatus);
			log.info("Current Sim State : ",status);

		} catch (JSONException e) {
			log.error("SimActivation : ",e.getMessage());
			simActivateStatus = " Error Occured during activation.:"+status;
		} catch (Exception e1) {
			log.error("SimActivation : ",e1.getMessage());
			simActivateStatus = " Error Occured during sim activation.:"+status;
		}

		return simActivateStatus+" <br> Previous SIM status : "+ previousSIMStatus +" <br> After new request SIM Status :"+status;

	}

	public String SimSuspend(String meids, String cust_name, String acc_name, String plan, String iccid,String reqId,IVerizonService verizonService) {
		String status="";
		String simStatus = "";
		String previousSIMStatus = "";
		try {			
			String access_token = getAPItoken();
			String session_token = callVerLogin(access_token);

			if (!session_token.contains("no-token")) {
				status=getversiminfo( access_token, session_token,iccid);
				previousSIMStatus = status;
			}

			if(isvalid_for_suspend(status))
			{
				String statusMsg = callVerService(meids, acc_name, plan, cust_name, access_token, session_token, iccid, "suspend",reqId,verizonService);
				simStatus = statusMsg.replace(":", "");
			}
			else if(status.toLowerCase().contains("<error>"))
			{
				log.info( "SIM status error : "+status );
				simStatus = " SIM Suspend Failed .<br> Error message from Verizon : "+status ;
			}
			else 
			{
				log.info( "SIM status else part : "+status );
				simStatus = " SIM Suspend Failed.<br> Error message from Verizon : "+status ;
			}

			status=getversiminfo( access_token, session_token,iccid);

			log.info("SimSuspend : ",simStatus);
			log.info("Current Sim State : ",status);
			
		} catch (JSONException e) {
			log.error("SimSuspend : ",e.getMessage());
			simStatus = " SIM Suspend Failed:"+status;			
		} catch (Exception e1) {
			log.error("SimSuspend : ",e1.getMessage());
			simStatus = " SIM Suspend Failed:"+status;
		}
		return simStatus+" <br> Previous SIM status : "+ previousSIMStatus +" <br> After new request SIM Status :"+status;
	}

	public String simRestore(String meids, String cust_name, String acc_name, String plan, String iccid,String reqId,IVerizonService verizonService) {
		String status="";
		String simStatus = "";
		String previousSIMStatus = "";
		try {			
			String access_token = getAPItoken();
			String session_token = callVerLogin(access_token);

			if (!session_token.contains("no-token")) {
				status=getversiminfo( access_token, session_token,iccid);
				previousSIMStatus = status;
			}

			if(isvalid_for_restore(status))
			{
				String statusMsg = callVerService(meids, acc_name, plan, cust_name, access_token, session_token,iccid,"restore",reqId,verizonService);
				simStatus =statusMsg.replace(":", "");
			}
			else if(status.toLowerCase().contains("<error>"))
			{
				log.info( "SIM status error : "+status );
				simStatus = " SIM Restore Failed .<br> Error message from Verizon : "+status ;
			}
			else 
			{
				log.info( "SIM status else part : "+status );
				simStatus = " SIM Restore Failed.<br> Error message from Verizon : "+status ;
			}
			
			status=getversiminfo( access_token, session_token,iccid);

			log.info("simRestore : ",simStatus);
			log.info("Current Sim State : ",status);
		} catch (JSONException e) {
			log.error("simRestore : ",e.getMessage());
			simStatus = " SIM Suspend Failed:"+status;
		} catch (Exception e1) {
			log.error("simRestore :", e1.getMessage());
			simStatus = " SIM Suspend Failed:"+status;
		}
		return simStatus+" <br> Previous SIM status : "+ previousSIMStatus +" <br> After new request SIM Status :"+status;

	}

	public String simDeactivate(String meids, String cust_name, String acc_name, String plan, String iccid,String reqId,IVerizonService verizonService) {
		String status="";
		String simStatus="";
		String previousSIMStatus = "";
		try {
			String access_token = getAPItoken();
			String session_token = callVerLogin(access_token);

			if (!session_token.contains("no-token")) {
				status=getversiminfo( access_token, session_token,iccid);
				previousSIMStatus = status;
			}

			if(isvalid_for_deactivate(status))
			{
				String statusMsg = callVerService(meids, acc_name, plan, cust_name, access_token, session_token,iccid,"deactivate",reqId,verizonService);
				simStatus= statusMsg.replace(":", "");
			}
			else if(status.toLowerCase().contains("<error>"))
			{
				log.info( "SIM status error : "+status );
				simStatus = " SIM Deactivate Failed.<br> Error message from Verizon : "+status ;
			}
			else 
			{
				log.info( "SIM status else part : "+status );
				simStatus = " SIM Deactivate Failed.<br> Error message from Verizon : "+status ;
			}
			
			status = getversiminfo( access_token, session_token,iccid);

			log.info("simDeactivate : ",simStatus);
			log.info("Current Sim State : ",status);
			
		} catch (JSONException e) {
			log.error("simDeactivate : ",e.getMessage());
			simStatus = " SIM Deactivation Failed.:"+status;
		} catch (Exception e1) {
			log.error("simDeactivate : ",e1.getMessage());
			simStatus = " SIM Deactivation Failed.:"+status;
		}

		return simStatus+" <br> Previous SIM status : "+ previousSIMStatus +" <br> After new request SIM Status :"+status;
	}


	private String callVerLogin(String access_token) throws JSONException {


		HttpClient httpClient = new DefaultHttpClient();

		String op = "no-token";
		try {

			HttpPost request = new HttpPost("https://thingspace.verizon.com/api/m2m/v1/session/login");
			String requestparam = "{\"username\": \"NIMBLESSUWS\",\"password\": \"S973rp%$\"}";


			StringEntity params = new StringEntity(requestparam);

			request.addHeader("content-type", "application/json");
			request.addHeader("Authorization", ("Bearer " + access_token));
			request.setEntity(params);
			HttpResponse response = httpClient.execute(request);

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {

				result.append(line);
			}
			rd.close();
			log.info("Session response : "+ result);

			try {
				JSONObject res = new JSONObject(result.toString());
				if ((result.toString().contains("sessionToken"))) {
					op = (String) res.get("sessionToken");					
				} else if ((result.toString().contains("errorCode"))) {
					op = (String) res.get("errorCode");
				}
				log.info("Session :" +op);
				return op;
			} catch (JSONException e) {
				log.error(e.getMessage());
			}

		} catch (IOException e) {
			log.error(e.getMessage());

		} catch (Exception e) {
			log.error(e.getMessage());

		} finally {
			try {
				httpClient.getConnectionManager().shutdown(); // Deprecated
			} catch (Exception e) {
				log.error(e.getMessage());
			}
		}
		log.error("ERROR : GETTING SESSION ");
		return op;
	}

	private String getAPItoken() throws JSONException {

		log.info("\nGetting Access token....\n");

		HttpClient httpClient = new DefaultHttpClient();

		String op = "no-access token";
		try {

			HttpPost request = new HttpPost("https://thingspace.verizon.com/api/ts/v1/oauth2/token");
			String requestparam = "grant_type=client_credentials";
			// System.out.println(requestparam);

			StringEntity params = new StringEntity(requestparam);

			request.addHeader("content-type", "application/x-www-form-urlencoded");
			request.addHeader("Authorization",
					"Basic QXhZckh4SXBiOWt4cThmQUFOSVRWMW9NYU4wYTpxdDc2VkFYUkg1S0xOOGplNVhNSVlhUk1ZR1Fh");
			request.setEntity(params);
			HttpResponse response = httpClient.execute(request);

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {

				result.append(line);
			}
			rd.close();
			log.info("TOKEN response : "+result);

			try {
				JSONObject res = new JSONObject(result.toString());
				if ((result.toString().contains("access_token"))) { 
					op = (String) res.get("access_token");
					log.info("TOKEN : "+op);
				}

				return op;
			} catch (JSONException e) {
				log.error(e.getMessage());
			}

		} catch (IOException e) {
			log.error(e.getMessage());

		} catch (Exception e) {
			log.error(e.getMessage());

		} finally {
			try {
				httpClient.getConnectionManager().shutdown(); // Deprecated
			} catch (Exception e) {
				log.error(e.getMessage());
			}
		}
		log.error("ERROR : GETTING TOKEN ");
		return op;
	}

	private String getversiminfo(String access_token, String session_token,String iccid) {

		HttpClient httpClient = new DefaultHttpClient();

		log.info("\nGetting Device status...");

		String op = "no-token";
		String err = "Error resuming device/s : ";
		try {
			HttpPost request = new HttpPost("https://thingspace.verizon.com/api/m2m/v1/devices/actions/list");

			String requestparam = "{\"deviceId\":{\"id\": \""+iccid+"\",\"kind\": \"iccid\"}}";
			StringEntity params = new StringEntity(requestparam);

			request.addHeader("content-type", "application/json");
			request.addHeader("Authorization", "Bearer " + access_token);
			request.addHeader("VZ-M2M-Token", session_token);
			request.setEntity(params);
			HttpResponse response = httpClient.execute(request);

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}
			rd.close();

			log.info("SIM_info response : "+result);
			op=result.toString();
			try {
				JSONObject res = new JSONObject(result.toString());
				if(res.has("devices"))
				{
					int jsize=res.getJSONArray("devices").length();

					if(jsize != 0)
					{
						JSONObject devices = (JSONObject) res.getJSONArray("devices").get(0);

						if(devices.has("carrierInformations"))
						{
							JSONObject carr=(JSONObject)devices.getJSONArray("carrierInformations").get(0);
							if(carr.has("state"))
							{
								op=carr.getString("state");
								log.info("Sim Status : "+op);
							}
						}
					}
					else
					{
						op="PASS";
						log.info("Sim Status : "+op);
					}
				}
				else
				{
					op="<ERROR>"+res.getString("errorMessage");
				}

				return op;

			} catch (JSONException e) {
				log.error(e.getMessage());
			}

			if (err.equalsIgnoreCase("Error activating device/s : ")) {
				err = "";
			}
			log.error("ERROR message 1 " + err);
		} catch (IOException e) {
			log.error(e.getMessage());

		} catch (Exception e) {
			log.error(e.getMessage());

		} finally {
			try {
				httpClient.getConnectionManager().shutdown(); // Deprecated
			} catch (Exception e) {
				log.error(e.getMessage());
			}
		}
		return op;

	}

	private String callVerActivate(String meid, String acc_name, String plan, String cust_name,
			String apitoken, String token,String iccid, String oldReqId,IVerizonService verizonService, String skuNumber) throws JSONException {

		log.info("\nStart sim Activation ....");
		HttpClient httpClient = new DefaultHttpClient();

//		String op = "no-token";
		String err = "Error resuming device/s : ";
		String simStatus="";
		try {

			HttpPost request = new HttpPost("https://thingspace.verizon.com/api/m2m/v1/devices/actions/activate");

			String cust_fname = cust_name.split(" ")[0];
			String cust_lname = cust_name.split(" ")[1];

			Gateway gateway = gatewayServiceV4.getGatewayByMeid(meid);
			long userId = 0;
			UserV4 user = null;
			String zipCode = "NA";
			if( gateway!=null ) {
				userId = userServiceV4.getUserGateway("gatewayId",gateway.getId());
			}
			
			if( userId !=0 ) {
				user = userServiceV4.verifyAuthV3("id", userId+"");
			}
			
			if( user != null && !user.getZipcode().equalsIgnoreCase("NA") ) {
				zipCode = user.getZipcode();
			} else {
				if( zipCodeType > zipCodeMap.size() ) {
					zipCodeType = 1;
				}
				zipCode = zipCodeMap.get(""+zipCodeType++);
			}	
			
			
			String meidVKind = "";
			String skuVkind ="";
			if(skuNumber == null)//meid)
			{
				meidVKind = "{\"id\": \"" + meid + "\",\"kind\": \"imei\"},";
			}else
			{
				skuVkind = " \"skuNumber\":\""+skuNumber+"\",";
			}			
			
			String requestparam = "{\"devices\": [{\"deviceIds\": ["+meidVKind+"{\"kind\":\"iccid\",\"id\":\""+iccid+"\"}]}],"
					+ skuVkind
					+"\"accountName\":\"" + acc_name + "\",\"servicePlan\":\"" + plan
					+ "\",\"mdnZipCode\":\"92069\"," + "\"primaryPlaceOfUse\":{\"customerName\":{\"firstName\":\""
					+ cust_fname + "\",\"lastName\":\"" + cust_lname + "\"},"
					+ "\"address\":{\"addressLine1\":\"1220 CORTE ZAFIRO\",\"city\":\"SAN MARCOS\",\"state\":\"CA\","
					+ "\"zip\":\" "+ zipCode + "\",\"country\":\"USA\",\"emailAddress\":\"<EMAIL>\","
					+ "\"cbrPhone\":\"**********\"}}}";

			log.info("Activate request : "+requestparam);

			StringEntity params = new StringEntity(requestparam);

			request.addHeader("content-type", "application/json");
			request.addHeader("Authorization", "Bearer " + apitoken);
			request.addHeader("VZ-M2M-Token", token);
			request.setEntity(params);
			HttpResponse response = httpClient.execute(request);

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {

				result.append(line);
			}
			rd.close();
			log.info("Activate response : "+result);

			try {
				JSONObject res = new JSONObject(result.toString());
				String reqId = "";
				String resStatus = "";
				
				if ((result.toString().contains("requestId"))) {
					//request id
					reqId = (String) res.get("requestId");
					simStatus = "SUCCESS : requestId ("+reqId+")";
					log.info("SUCCESS : requestId ("+reqId+")");
				} else if ((result.toString().contains("errorCode"))) {
					// op = (String) res.get("errorCode");
					log.info("Error code : "+(String) res.get("errorCode"));
					err += meid + " ";
					
					err += "<br> Error Code from Verizon : "+ (String) res.get("errorCode");
					
					if( result.toString().contains("errorMessage") ) {
						log.info("Error code : "+(String) res.get("errorCode"));
						err += "\n :: Error Message from Verizon : "+ (String) res.get("errorMessage");
					}
					
					simStatus = err;
				}
				
				boolean simHistoryStatus = saveOrUpdateSimHistory(reqId,oldReqId,"activate",meid,verizonService);
				log.info("simhistory status saves: "+ simHistoryStatus);
				//return op;
			} catch (JSONException e) {
				log.error(e.getMessage());
			}

			if (err.equalsIgnoreCase("Error activating device/s : ")) {
				err = "";
			}
			log.error("ERROR message 1 " + err);
		} catch (IOException e) {
			log.error(e.getMessage());

		} catch (Exception e) {
			log.error(e.getMessage());

		} finally {
			try {
				httpClient.getConnectionManager().shutdown(); // Deprecated
			} catch (Exception e) {
				log.error(e.getMessage());
			}
		}
		return simStatus;
	}

	private boolean saveOrUpdateSimHistory(String reqId, String oldReqId, String action,String meid,IVerizonService verizonService) {
		log.info("Entered saveOrUpdateSimHistory :: meid : "+meid );
		try {
			
			String datetime   = irisUtil.getUtcDateTime();
			SimReactivationHistory simReactivationStatus = null;
			if(oldReqId != null && !oldReqId.isEmpty() ) {
				//simReactivationStatus = verizonService.getSimReactivationStatus("request_id", oldReqId);
				boolean isUpdated = verizonService.updateRequestId(meid,oldReqId,reqId,datetime);
				log.info("updateVerizonStatus : "+isUpdated);
			} else {
//				simReactivationStatus = verizonService.getSimReactivationStatus("request_id", reqId);
				boolean isNewSimActivation = false;

				//new sim history process 
				if(simReactivationStatus == null) {
					isNewSimActivation = true;
					simReactivationStatus = new SimReactivationHistory();
					simReactivationStatus.setMeid(meid);
					simReactivationStatus.setCreatedOn(datetime);
					simReactivationStatus.setUpdatedOn(datetime);
					simReactivationStatus.setStatus("inprogress");
					simReactivationStatus.setAction(action);
					simReactivationStatus.setRetrycount(0);
					simReactivationStatus.setRequestId(reqId);
					
					boolean isSaved = verizonService.saveVerizonStatus(simReactivationStatus);
					log.info("saveVerizonStatus : "+isSaved);
				}
			}
			
			return true;
		} catch (Exception e) {
			e.getLocalizedMessage();
			return false;
		}
	}

	private String callVerService(String meids, String acc_name, String plan, String cust_name,
			String access_token, String session_token, String iccid, String URL,String oldReqId,IVerizonService verizonService) {

		
		HttpClient httpClient = new DefaultHttpClient();

		String op = "no-token";
		String err = "Error resuming device/s : ";
		String statusMsg = "";
		try {

			HttpPost request = new HttpPost("https://thingspace.verizon.com/api/m2m/v1/devices/actions/"+URL);

			String requestparam = "{\r\n" + 
					"    \"accountName\": \""+acc_name+"\",\r\n" + 
					"    \"devices\": [{\r\n" + 
					"            \"deviceIds\": [{\r\n" + 
					"                    \"id\": \""+meids+"\",\r\n" + 
					"                    \"kind\": \"imei\"\r\n" + 
					"                }\r\n" + 
					"            ]\r\n" + 
					"        }\r\n" + 
					"    ]\r\n" + 
					"}\r\n" + 
					"";

			if(URL.equals("deactivate") )
			{
				requestparam = "{\r\n" + 
						"  \"accountName\": \""+acc_name+"\",\r\n" + 
						"  \"devices\": [\r\n" + 
						"    {\r\n" + 
						"      \"deviceIds\": [\r\n" + 
						"        {\r\n" + 
						"          \"id\": \""+meids+"\",\r\n" + 
						"          \"kind\": \"imei\"\r\n" + 
						"        }\r\n" + 
						"      ]\r\n" + 
						"    }\r\n" + 
						"  ],\r\n" + 
						"  \"reasonCode\": \"FF\",\r\n" + 
						"  \"etfWaiver\": true\r\n" + 
						"}";
			}

			log.info("Restore request : "+requestparam);

			StringEntity params = new StringEntity(requestparam);

			request.addHeader("content-type", "application/json");
			request.addHeader("Authorization", "Bearer " + access_token);
			request.addHeader("VZ-M2M-Token", session_token);
			request.setEntity(params);
			HttpResponse response = httpClient.execute(request);

			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));

			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {

				result.append(line);
			}
			rd.close();
			log.info("Service response : "+result);

			try {
				String datetime   = irisUtil.getUtcDateTime();
				boolean isNewSimActivation = false;
				
				JSONObject res = new JSONObject(result.toString());
				if( !URL.equalsIgnoreCase("restore") ) {
					if ((result.toString().contains("requestId"))) {
						op = (String) res.get("requestId");
						log.info("SUCCESS : requestId ("+op+")");
						statusMsg = "SUCCESS : requestId ("+op+")";
					} else if ((result.toString().contains("errorCode"))) {
						// op = (String) res.get("errorCode");
						err += meids + " ";
						statusMsg = err;
					}
				} else {	
				
					if ((result.toString().contains("requestId"))) {
						op = (String) res.get("requestId");
						statusMsg = "SUCCESS : requestId ("+op+")";
						log.info("SUCCESS : requestId ("+op+")");
					} else if ((result.toString().contains("errorCode"))) {
						// op = (String) res.get("errorCode");
						log.info("Error code : "+(String) res.get("errorCode"));
						err += meids + " ";
						
						err += "<br> Error Code from Verizon : "+ (String) res.get("errorCode");
						
						if( result.toString().contains("errorMessage") ) {
							log.info("Error message : "+(String) res.get("errorMessage"));
							err += "\n :: Error Message from Verizon : "+ (String) res.get("errorMessage");
						}
						
						statusMsg = err;
					}
					
				}
				String reqId = "";
				if ((result.toString().contains("requestId"))) 
					reqId = (String) res.get("requestId");
				boolean simHistoryStatus = saveOrUpdateSimHistory(reqId, oldReqId, URL , meids,verizonService);
				log.info("simhistory status saves: "+ simHistoryStatus);
				return statusMsg ;
			} catch (JSONException e) {
				log.error(e.getMessage());
			}

			if (err.equalsIgnoreCase("Error Service device/s : ")) {
				err = "";
			}
			log.error("ERROR message 1 " + err);
		} catch (IOException e) {
			log.error(e.getMessage());
			statusMsg = e.getLocalizedMessage();
		} catch (Exception e) {
			log.error(e.getMessage());
			statusMsg = e.getLocalizedMessage();

		} finally {
			try {
				httpClient.getConnectionManager().shutdown(); // Deprecated
			} catch (Exception e) {
				log.error(e.getMessage());
			}
		}
		return statusMsg ;

	}


	private boolean isvalid_for_activate(String status) {
		status=status.toLowerCase();
		if(status.equals("active") || status.equals("pending preactive") || status.equals("pending activation")  
				|| status.contains("<error>") || status.equals("suspend") )
			return false;
		return true;
	}

	private boolean isvalid_for_suspend(String status) {
		status=status.toLowerCase();
		if(status.equals("suspend") )
			return false;
		return true;
	}

	private boolean isvalid_for_deactivate(String status) {
		status=status.toLowerCase();
		if(status.equals("active") || status.equals("pending preactive") || status.equals("pending activation"))
			return true;
		return false;
	}

	private boolean isvalid_for_restore(String status) {
		status=status.toLowerCase();
		if(status.equals("suspend") )
			return true;
		return false;
	}


	//Response from verizon
	
//	public static String serviceResponseStatus(HttpPost request, HttpResponse response) {
//
//		String op = "no-token";
//		String meid = "";
//		String err = "Error resuming device/s : ";
//		String statusMsg = "";
//		try {
//
//			BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
//
//			StringBuffer result = new StringBuffer();
//			String line = "";
//
//			while ((line = rd.readLine()) != null) {
//
//				result.append(line);
//			}
//			System.out.println(result);
//			rd.close();
//			log.info("Service response : "+result);
//
//			try {
//				JSONObject res = new JSONObject(result.toString());
//				if ((result.toString().contains("requestId"))) {
//					op = (String) res.get("requestId");
//					log.info("SUCCESS : requestId ("+op+")");
//					statusMsg = "SUCCESS : requestId ("+op+")";
//				} else if ((result.toString().contains("errorCode"))) {
//					
//					statusMsg = err;
//				}
//				return statusMsg ;
//			} catch (JSONException e) {
//				log.error(e.getMessage());
//			}
//
//			System.out.println("ERROR message " + err);
//
//		} catch (IOException e) {
//			log.error(e.getMessage());
//			statusMsg = e.getLocalizedMessage();
//		} catch (Exception e) {
//			log.error(e.getMessage());
//			statusMsg = e.getLocalizedMessage();
//
//		} 
//		return statusMsg ;
//
//	}
}
