package com.nimble.irisservices.dto;

import javax.validation.constraints.Size;

import org.codehaus.jackson.annotate.JsonIgnoreProperties;
import org.hibernate.validator.constraints.NotEmpty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class JUserEmailUpdate {
	
	private long id = 0;
	
	@NotEmpty(message = "Please enter Username")
	@Size(min = 3, max = 30, message = "Username size must be between 3 and 30")
	private String username;

	@NotEmpty(message = "Please enter New Email")
	@Size(min = 3, max = 30, message = "Email size must be between 3 and 30")
	private String newEmail;
	
	private String authKey;

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getNewEmail() {
		return newEmail;
	}

	public void setNewEmail(String newEmail) {
		this.newEmail = newEmail;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getAuthKey() {
		return authKey;
	}

	public void setAuthKey(String authKey) {
		this.authKey = authKey;
	}
	
	
}
