package com.nimble.irisservices.dto;

import java.io.Serializable;

@SuppressWarnings("serial")
public class JAssetLastReportWatch implements Serializable {
	
	private long assetid;

	private String assetname;

	private String updatedon;

	private String humidity;

	private String temperature;

	private int default_report;

	private String default_rpt_msg;

	private String title;

	private String heat_index;

	private String tempunit;

	private String temp_min;

	private String temp_max;

	private String air_quality;

	public JAssetLastReportWatch() {
		super();
	}

	public long getAssetid() {
		return assetid;
	}

	public void setAssetid(long assetid) {
		this.assetid = assetid;
	}

	public String getAssetname() {
		return assetname;
	}

	public void setAssetname(String assetname) {
		this.assetname = assetname;
	}

	public String getUpdatedon() {
		return updatedon;
	}

	public void setUpdatedon(String updatedon) {
		this.updatedon = updatedon;
	}

	public String getHumidity() {
		return humidity;
	}

	public void setHumidity(String humidity) {
		this.humidity = humidity;
	}

	public String getTemperature() {
		return temperature;
	}

	public void setTemperature(String temperature) {
		this.temperature = temperature;
	}

	public int getDefault_report() {
		return default_report;
	}

	public void setDefault_report(int default_report) {
		this.default_report = default_report;
	}

	public String getDefault_rpt_msg() {
		return default_rpt_msg;
	}

	public void setDefault_rpt_msg(String default_rpt_msg) {
		this.default_rpt_msg = default_rpt_msg;
	}

	public String getHeat_index() {
		return heat_index;
	}

	public void setHeat_index(String heat_index) {
		this.heat_index = heat_index;
	}

	public String getTempunit() {
		return tempunit;
	}

	public void setTempunit(String tempunit) {
		this.tempunit = tempunit;
	}

	public String getTemp_min() {
		return temp_min;
	}

	public void setTemp_min(String temp_min) {
		this.temp_min = temp_min;
	}

	public String getTemp_max() {
		return temp_max;
	}

	public void setTemp_max(String temp_max) {
		this.temp_max = temp_max;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public JAssetLastReportWatch(long assetid, String assetname, String updatedon,
			String humidity, String temperature, int default_report, String default_rpt_msg,
			String datetime, String heat_index, String tempunit, String air_quality) {
		super();
		this.assetid = assetid;
		this.assetname = assetname;
		this.updatedon = updatedon;
		this.humidity = humidity;
		this.temperature = temperature;
		this.default_report = default_report;
		this.default_rpt_msg = default_rpt_msg;
		this.heat_index = heat_index;
		this.tempunit = tempunit;
		this.air_quality = air_quality;
	}

	public String getAir_quality() {
		return air_quality;
	}

	public void setAir_quality(String air_quality) {
		this.air_quality = air_quality;
	}
}

