package com.nimble.irisservices.dto;

import java.util.List;

public class JFurBitReportReportSummary {
	
	List<JFurBitReport> jFurBitDailyRptLis =null;

	List<JFurBitReportBest> furBitDailyBestLis =null;
	List<JFurBitReportAvg> furBitDailyAverageLis=null;
	List<JFurBitReportTotal> furBitDailyTotal =null;
	
	
	public List<JFurBitReportTotal> getFurBitDailyTotal() {
		return furBitDailyTotal;
	}
	public void setFurBitDailyTotal(List<JFurBitReportTotal> furBitDailyTotal) {
		this.furBitDailyTotal = furBitDailyTotal;
	}
	
	public List<JFurBitReport> getjFurBitDailyRptLis() {
		return jFurBitDailyRptLis;
	}
	public void setjFurBitDailyRptLis(List<JFurBitReport> jFurBitDailyRptLis) {
		this.jFurBitDailyRptLis = jFurBitDailyRptLis;
	}
	public List<JFurBitReportBest> getFurBitDailyBestLis() {
		return furBitDailyBestLis;
	}
	public void setFurBitDailyBestLis(List<JFurBitReportBest> furBitDailyBestLis) {
		this.furBitDailyBestLis = furBitDailyBestLis;
	}
	public List<JFurBitReportAvg> getFurBitDailyAverageLis() {
		return furBitDailyAverageLis;
	}
	public void setFurBitDailyAverageLis(List<JFurBitReportAvg> furBitDailyAverageLis) {
		this.furBitDailyAverageLis = furBitDailyAverageLis;
	}
}
