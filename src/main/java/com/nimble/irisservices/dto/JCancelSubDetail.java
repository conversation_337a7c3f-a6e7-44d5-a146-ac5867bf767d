package com.nimble.irisservices.dto;

public class JCancelSubDetail {
private static final long serialVersionUID = 0l;
	
	String reason_type="NA";
	String reason_desc="NA";
	String cb_subid = "NA";
	long monitortype_id=1;
	long gateway_id=0;
	int sub_qty = 1;
	
	public JCancelSubDetail() {
	}

	public JCancelSubDetail(String reason_type, String reason_desc, String cb_subid, long monitortype_id,long gateway_id) {
		super();
		this.reason_type = reason_type;
		this.reason_desc = reason_desc;
		this.cb_subid = cb_subid;
		this.monitortype_id = monitortype_id;
		this.gateway_id = gateway_id;
	}

	public JCancelSubDetail(String reason_type, String reason_desc, String cb_subid) {
		super();
		this.reason_type = reason_type;
		this.reason_desc = reason_desc;
		this.cb_subid = cb_subid;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public String getReason_type() {
		return reason_type;
	}

	public void setReason_type(String reason_type) {
		this.reason_type = reason_type;
	}

	public String getReason_desc() {
		return reason_desc;
	}

	public void setReason_desc(String reason_desc) {
		this.reason_desc = reason_desc;
	}

	public String getCb_subid() {
		return cb_subid;
	}

	public void setCb_subid(String cb_subid) {
		this.cb_subid = cb_subid;
	}

	public long getMonitortype_id() {
		return monitortype_id;
	}

	public void setMonitortype_id(long monitortype_id) {
		this.monitortype_id = monitortype_id;
	}

	public int getSub_qty() {
		return sub_qty;
	}

	public void setSub_qty(int sub_qty) {
		this.sub_qty = sub_qty;
	}
	
}
