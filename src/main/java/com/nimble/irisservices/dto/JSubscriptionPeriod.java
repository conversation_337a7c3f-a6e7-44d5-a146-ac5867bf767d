package com.nimble.irisservices.dto;

import java.io.Serializable;
import java.util.LinkedList;

public class JSubscriptionPeriod implements Serializable {

	private static final long serialVersionUID = 0l;
	long plan_id;
	long period_id;
	String price_detail;
	String buynow_desc = "Buy Now";
	String content_1 = "";
	String content_2 = "";
	String content_3 = "";
	String content_4 = "";
	String strike_price_v2 = "NA";
	String img_url = "NA";
	boolean is_best_deal = false;
	LinkedList<String> benefits = new LinkedList<String>();
	boolean show_benefits  = false;
	String content_5 = "";
	public JSubscriptionPeriod() {
		super();
	}

	public JSubscriptionPeriod(long plan_id, long period_id, String price_detail, String content_1, String content_2,
			String content_3, String content_4) {
		super();
		this.plan_id = plan_id;
		this.period_id = period_id;
		this.price_detail = price_detail;
		this.content_1 = content_1;
		this.content_2 = content_2;
		this.content_3 = content_3;
		this.content_4 = content_4;
	}

	public long getPlan_id() {
		return plan_id;
	}

	public void setPlan_id(long plan_id) {
		this.plan_id = plan_id;
	}

	public long getPeriod_id() {
		return period_id;
	}

	public void setPeriod_id(long period_id) {
		this.period_id = period_id;
	}

	public String getPrice_detail() {
		return price_detail;
	}

	public void setPrice_detail(String price_detail) {
		this.price_detail = price_detail;
	}

	public String getBuynow_desc() {
		return buynow_desc;
	}

	public void setBuynow_desc(String buynow_desc) {
		this.buynow_desc = buynow_desc;
	}

	public String getContent_1() {
		return content_1;
	}

	public void setContent_1(String content_1) {
		this.content_1 = content_1;
	}

	public String getContent_2() {
		return content_2;
	}

	public void setContent_2(String content_2) {
		this.content_2 = content_2;
	}

	public String getContent_3() {
		return content_3;
	}

	public void setContent_3(String content_3) {
		this.content_3 = content_3;
	}

	public String getContent_4() {
		return content_4;
	}

	public void setContent_4(String content_4) {
		this.content_4 = content_4;
	}

	public String getStrike_price_v2() {
		return strike_price_v2;
	}

	public void setStrike_price_v2(String strike_price_v2) {
		this.strike_price_v2 = strike_price_v2;
	}

	public String getImg_url() {
		return img_url;
	}

	public void setImg_url(String img_url) {
		this.img_url = img_url;
	}

	public boolean isIs_best_deal() {
		return is_best_deal;
	}

	public void setIs_best_deal(boolean is_best_deal) {
		this.is_best_deal = is_best_deal;
	}

	public LinkedList<String> getBenefits() {
		return benefits;
	}

	public void setBenefits(LinkedList<String> benefits) {
		this.benefits = benefits;
	}

	public boolean isShow_benefits() {
		return show_benefits;
	}

	public void setShow_benefits(boolean show_benefits) {
		this.show_benefits = show_benefits;
	}

	public String getContent_5() {
		return content_5;
	}

	public void setContent_5(String content_5) {
		this.content_5 = content_5;
	}

}
