package com.nimble.irisservices.dto;

public class JUserLeaderBoard {

	private long gateway_id;
	private String pet_name;
	private String breed;
	private String imageurl;
	private double activetime;
	private double calories;
	private double stepcount;
	private String startdate;
	private String enddate;
	private int rank;
	public JUserLeaderBoard() {
	}
	
	public long getGateway_id() {
		return gateway_id;
	}
	
	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}
	
	public String getPet_name() {
		return pet_name;
	}
	
	public void setPet_name(String pet_name) {
		this.pet_name = pet_name;
	}
	
	public String getBreed() {
		return breed;
	}
	
	public void setBreed(String breed) {
		this.breed = breed;
	}

	public double getActivetime() {
		return activetime;
	}

	public void setActivetime(double activetime) {
		this.activetime = activetime;
	}

	public double getCalories() {
		return calories;
	}

	public void setCalories(double calories) {
		this.calories = calories;
	}

	public double getStepcount() {
		return stepcount;
	}

	public void setStepcount(double stepcount) {
		this.stepcount = stepcount;
	}

	public String getImageurl() {
		return imageurl;
	}

	public void setImageurl(String imageurl) {
		this.imageurl = imageurl;
	}

	public String getStartdate() {
		return startdate;
	}

	public void setStartdate(String startdate) {
		this.startdate = startdate;
	}

	public String getEnddate() {
		return enddate;
	}

	public void setEnddate(String enddate) {
		this.enddate = enddate;
	}

	public int getRank() {
		return rank;
	}

	public void setRank(int rank) {
		this.rank = rank;
	}

}
