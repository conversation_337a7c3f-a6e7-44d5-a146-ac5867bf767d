package com.nimble.irisservices.dto;

import java.io.Serializable;

import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

public class JReminderDetails implements Serializable {

	private long id;

	@JsonIgnoreProperties
	private String jobName;

	private String reminderName;

	private String reminderMsg;

	private String reminderDate;

	private String repeattype;
	
	private long repeatId;

	@Transient
	private String timezone;

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getReminderName() {
		return reminderName;
	}

	public void setReminderName(String reminderName) {
		this.reminderName = reminderName;
	}

	public String getReminderMsg() {
		return reminderMsg;
	}

	public void setReminderMsg(String reminderMsg) {
		this.reminderMsg = reminderMsg;
	}

	public String getReminderDate() {
		return reminderDate;
	}

	public void setReminderDate(String reminderDate) {
		this.reminderDate = reminderDate;
	}

	public String getRepeattype() {
		return repeattype;
	}

	public void setRepeattype(String repeattype) {
		this.repeattype = repeattype;
	}

	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	public long getRepeatId() {
		return repeatId;
	}

	public void setRepeatId(long repeatId) {
		this.repeatId = repeatId;
	}

}
