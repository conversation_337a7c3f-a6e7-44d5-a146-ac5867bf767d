package com.nimble.irisservices.dto;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

import javax.persistence.Column;

public class JFurBitLastGatewayReport implements Serializable {
	private static final long serialVersionUID = 0l;
	
	private String version;
	
	private Timestamp datetime;
	
	private Date date;
	
	private Time time;
	
	private String timezone;
	
	private int battery;
	
	private String eventid1;
	
	private String eventid2;
	
	private int iostatus;

	private int gpsmode;

	private int txnMode;
	
	private double lat;
	
	private String latdir;
	
	private double lon;
	
	private String londir;

	private String gpsstatus;
	
	private String gpsinfo;
	
	private int rawrssi;
	
	@Column(name="rssi")
	private String rssi;

	private int lastpkt;
		
    private long gateway;	
     
    private String meid;
   
    public JFurBitLastGatewayReport() {
    	super();
    }
	
	public JFurBitLastGatewayReport(String version, Timestamp datetime, Date date, Time time,	String timezone, double lat, 
			String latdir, double lon,String londir,String gpsstatus, String gpsinfo, String eventid1,
			String eventid2, int iostatus, int battery, int rawrssi, String rssi, int gpsmode,int txnMode,int lastpkt, long gateway, String meid)
	{
		this.version = version;
		this.datetime = datetime;
		this.date = date;
		this.time = time;
		this.timezone = timezone;
		this.lat = lat;
		this.latdir = latdir;
		this.lon = lon;
		this.londir = londir;
		this.gpsstatus = gpsstatus;
		this.gpsinfo = gpsinfo;
		this.iostatus = iostatus;
		this.battery = battery;
		this.eventid1 = eventid1;
		this.eventid2 = eventid2;
		this.rawrssi = rawrssi;
		this.rssi = rssi;
		this.lastpkt  = lastpkt;
		this.gpsmode = gpsmode;
		this.gateway = gateway;
		this.meid = meid;
	}
	
	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public Timestamp getDatetime() {
		return datetime;
	}

	public void setDatetime(Timestamp datetime) {
		this.datetime = datetime;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Time getTime() {
		return time;
	}

	public void setTime(Time time) {
		this.time = time;
	}

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}

	public int getBattery() {
		return battery;
	}

	public void setBattery(int battery) {
		this.battery = battery;
	}

	public String getEventid1() {
		return eventid1;
	}

	public void setEventid1(String eventid1) {
		this.eventid1 = eventid1;
	}

	public String getEventid2() {
		return eventid2;
	}

	public void setEventid2(String eventid2) {
		this.eventid2 = eventid2;
	}

	public int getIostatus() {
		return iostatus;
	}

	public void setIostatus(int iostatus) {
		this.iostatus = iostatus;
	}

	public int getGpsmode() {
		return gpsmode;
	}

	public void setGpsmode(int gpsmode) {
		this.gpsmode = gpsmode;
	}

	public int getTxnMode() {
		return txnMode;
	}

	public void setTxnMode(int txnMode) {
		this.txnMode = txnMode;
	}

	public double getLat() {
		return lat;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public String getLatdir() {
		return latdir;
	}

	public void setLatdir(String latdir) {
		this.latdir = latdir;
	}

	public double getLon() {
		return lon;
	}

	public void setLon(double lon) {
		this.lon = lon;
	}

	public String getLondir() {
		return londir;
	}

	public void setLondir(String londir) {
		this.londir = londir;
	}

	public String getGpsstatus() {
		return gpsstatus;
	}

	public void setGpsstatus(String gpsstatus) {
		this.gpsstatus = gpsstatus;
	}

	public String getGpsinfo() {
		return gpsinfo;
	}

	public void setGpsinfo(String gpsinfo) {
		this.gpsinfo = gpsinfo;
	}

	public int getRawrssi() {
		return rawrssi;
	}

	public void setRawrssi(int rawrssi) {
		this.rawrssi = rawrssi;
	}

	public String getRssi() {
		return rssi;
	}

	public void setRssi(String rssi) {
		this.rssi = rssi;
	}

	public int getLastpkt() {
		return lastpkt;
	}

	public void setLastpkt(int lastpkt) {
		this.lastpkt = lastpkt;
	}

	public long getGateway() {
		return gateway;
	}

	public void setGateway(long gateway) {
		this.gateway = gateway;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}
	
	
	
}
