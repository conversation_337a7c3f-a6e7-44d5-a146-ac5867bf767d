package com.nimble.irisservices.dto;

public class JPetprofileFlutter {
	
	private long id=0;
	
	private String name = "";
	
	private String sex = "";
	
	private String breed = "";	
	
	private float height=0;	
	
	public JPetprofileFlutter(long user_id, String meid, String qrc, long gatewayId) {
		super();
		this.user_id = user_id;
		this.meid = meid;
		this.qrc = qrc;
		this.gatewayId = gatewayId;
	}

	private float weight=0;	
	
	private String imageurl="NA";
	
	private String specieName="";
	
	private long user_id = 0;
	
	private String birth_date="";
	
	private long speciesid = 1;
	
	private String meid ="";
	
	private String qrc ="";
	
	private long gatewayId =0;
	
	private String gatewayName="";
	
	private long monitortype = 1;
	 
	private String ageMonth="";
	
	private String ageYr="";

	private String mtypename="";

	private boolean enable=true;

	private boolean update=false;

	private String ppConfig = "10";

	private boolean intact = true;

	private String structure = "NA";

	private boolean find_now = false;

	private long activitylevel = 1;

	public JPetprofileFlutter(){
		super();
		
	}

	public JPetprofileFlutter(long user_id, String meid, String qrc, long gatewayId, String gatewayName, long monitortype) {
		super();
		this.user_id = user_id;
		this.meid = meid;
		this.qrc = qrc;
		this.gatewayId = gatewayId;
		this.gatewayName = gatewayName;
		this.monitortype = monitortype;
	}

	public JPetprofileFlutter(long id,long gatewayId ,String name, String sex,	String breed,float height,	float weight,
			String imageurl,long speciesid,	String speciesname,	long user_id,String  qrc,String meid,String gatewayName,
			String ageYr,String ageMonth,long monitortype,String mtypename,boolean enable,String ppConfig) {
		this.id=id;
		this.gatewayId=gatewayId;
		this.name=name;
		this.ageMonth=ageMonth;
		this.ageYr = ageYr;
		this.sex=sex;
		this.breed = breed;
		this.height=height;
		this.weight=weight;
		this.imageurl=imageurl;
		this.speciesid = speciesid;
		this.specieName=speciesname;
		this.user_id = user_id;
		this.qrc = qrc;
		this.meid = meid;
		this.gatewayName=gatewayName;
		this.monitortype = monitortype;
		this.mtypename = mtypename;
		this.enable = enable;
		this.ppConfig = ppConfig;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getBreed() {
		return breed;
	}

	public void setBreed(String breed) {
		this.breed = breed;
	}

	public float getHeight() {
		return height;
	}

	public void setHeight(float height) {
		this.height = height;
	}

	public float getWeight() {
		return weight;
	}

	public void setWeight(float weight) {
		this.weight = weight;
	}

	public String getImageurl() {
		return imageurl;
	}

	public void setImageurl(String imageurl) {
		this.imageurl = imageurl;
	}

	public String getSpecieName() {
		return specieName;
	}

	public void setSpecieName(String specieName) {
		this.specieName = specieName;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getBirth_date() {
		return birth_date;
	}

	public void setBirth_date(String birth_date) {
		this.birth_date = birth_date;
	}

	public long getSpeciesid() {
		return speciesid;
	}

	public void setSpeciesid(long speciesid) {
		this.speciesid = speciesid;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getQrc() {
		return qrc;
	}

	public void setQrc(String qrc) {
		this.qrc = qrc;
	}

	public long getGatewayId() {
		return gatewayId;
	}

	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}

	public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

	public long getMonitortype() {
		return monitortype;
	}

	public void setMonitortype(long monitortype) {
		this.monitortype = monitortype;
	}

	public String getAgeMonth() {
		return ageMonth;
	}

	public void setAgeMonth(String ageMonth) {
		this.ageMonth = ageMonth;
	}

	public String getAgeYr() {
		return ageYr;
	}

	public void setAgeYr(String ageYr) {
		this.ageYr = ageYr;
	}

	public String getMtypename() {
		return mtypename;
	}

	public void setMtypename(String mtypename) {
		this.mtypename = mtypename;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public boolean isUpdate() {
		return update;
	}

	public void setUpdate(boolean update) {
		this.update = update;
	}

	public String getPpConfig() {
		return ppConfig;
	}

	public void setPpConfig(String ppConfig) {
		this.ppConfig = ppConfig;
	}

	public boolean isIntact() {
		return intact;
	}

	public void setIntact(boolean intact) {
		this.intact = intact;
	}

	public String getStructure() {
		return structure;
	}

	public void setStructure(String structure) {
		this.structure = structure;
	}

	public JPetprofileFlutter(long id,long gatewayId ,String name, String sex,	String breed,float height,	float weight,
			String imageurl,long speciesid,	String speciesname,	long user_id,String  qrc,String meid,String gatewayName,
			String ageYr,String ageMonth,long monitortype,String mtypename,boolean enable,String ppConfig, boolean instact, String structure,long activitylevel,boolean find_now) {
		this.id=id;
		this.gatewayId=gatewayId;
		this.name=name;
		this.ageMonth=ageMonth;
		this.ageYr = ageYr;
		this.sex=sex;
		this.breed = breed;
		this.height=height;
		this.weight=weight;
		this.imageurl=imageurl;
		this.speciesid = speciesid;
		this.specieName=speciesname;
		this.user_id = user_id;
		this.qrc = qrc;
		this.meid = meid;
		this.gatewayName=gatewayName;
		this.monitortype = monitortype;	
		this.mtypename = mtypename;
		this.enable = enable;
		this.ppConfig = ppConfig;
		this.intact = instact;
		this.structure = structure;
		this.activitylevel = activitylevel;
		this.find_now = find_now;
	}

	public boolean getFind_now() {
		return find_now;
	}

	public void setFind_now(boolean find_now) {
		this.find_now = find_now;
	}

	public long getActivitylevel() {
		return activitylevel;
	}

	public void setActivitylevel(long activitylevel) {
		this.activitylevel = activitylevel;
	}	
	
	
}
