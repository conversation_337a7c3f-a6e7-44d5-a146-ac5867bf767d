package com.nimble.irisservices.dto;

import java.util.LinkedList;
import java.util.Map;

public class JProductSubResV2 {
    private int periodid = 0;
    private String cancel_popup_content = "NA";
    private String show_warranty_popup_content = "NA";
    private int monitor_id = 1;
    private JCancelSubInfo cancelInfo;
    private String show_warranty_popup_v2_content = "NA";
    private boolean is_priorityuser = false;
    private String stay_with_us_content  = "NA";
    private JSubscriptionPlanReportV2 subscriptionplan;
    private boolean showpausesub = false;
    private boolean pauseStatus = false;
    private boolean show_check_out_popup = false;
    private String nextrenew_date_old = "NA";
    private boolean valid_bundle_subs = true;
    private boolean show_warranty_popup = false;
    private String orderid_later_popup_content = "NA";
    private boolean warranty_popup_later_btn = false;
    private String priorityphone = "NA";
    private boolean immediate_cancel = false;
    private int redirect_inapp = 1;
    private String meariSubKey = "NA";
    private String cur_feature_ui_new = "NA";
    private String show_warranty_popup_v2_image = "NA";
    private boolean show_cancel_reward = false;
    private boolean show_alertlimit = false;
    private String cancel_button_text = "NA";
    private String pausetitle = "NA";
    private String cancelSubUrl = "NA";
    private boolean show_manage_sub = false;
    private boolean warranty_claimed = false;
    private String immediate_cancel_note = "NA";
    private String upcomingrenewal_cancel_note = "NA";
    private int planid = 0;
    private String bundle_check_out_url = "NA";
    private boolean cancel_inapp_redirect = false;
    private String resumeDate = "NA";
    private String pausecontent = "NA";
    private boolean show_upgrade = false;
    private boolean show_bundle_contact_us = false;
    private String bundle_contact_us_content = "NA";
    private String check_out_popup_content = "NA";
    private String check_out_popup_image = "NA";
    private String check_out_popup_image_dark = "NA";
    private String cancel_reward_code = "NA";
    private String cancel_reward_enddate = "NA";
    private String cancel_reward_title = "NA";
    private String cancel_reward_url = "NA";
    private boolean setupActivate = true;
    private Map<String,Object> deviceList;
	private long gateway_id;
	private int days_remaining;
	private String meid;
	private String name = "NA";
	private String show_nextrenewal_popup_content="";
	private boolean is_rec = false;
	private boolean show_pauseoption = true;
	private LinkedList<String> stay_with_us_list;
	private String flexi_plan_start_date = "NA";
	private String flexi_next_billing_date = "NA";
	private boolean is_paused = false;
	private boolean is_started = false;
	private String month_left = "0";
	private int days_left = 0;
	private boolean is_flexi = false;
	private String activated_content = "NA";
	private String pause_content = "NA";
	private String start_content = "NA";
	private boolean show_flexi_pause = false;
	private int remaining_month = 0;
	private int paused_count = 0;
	private String paused_date_content = "";
	private boolean showAddressPopup = false;
	private boolean show_coupon = false;
	private String img_url = "NA";
	private String plan_ver = "NA";
	private boolean is_combo = false;

	public JCouponInfo coupon_info = new JCouponInfo();
	
	public String getMonth_left() {
		return month_left;
	}

	public void setMonth_left(String month_left) {
		this.month_left = month_left;
	}

	public String getActivated_content() {
		return activated_content;
	}

	public void setActivated_content(String activated_content) {
		this.activated_content = activated_content;
	}

	public String getPause_content() {
		return pause_content;
	}

	public void setPause_content(String pause_content) {
		this.pause_content = pause_content;
	}

	public String getStart_content() {
		return start_content;
	}

	public void setStart_content(String start_content) {
		this.start_content = start_content;
	}

	public int getPeriodid() {
		return periodid;
	}

	public void setPeriodid(int periodid) {
		this.periodid = periodid;
	}

	public String getCancel_popup_content() {
		return cancel_popup_content;
	}

	public void setCancel_popup_content(String cancel_popup_content) {
		this.cancel_popup_content = cancel_popup_content;
	}

	public String getShow_warranty_popup_content() {
		return show_warranty_popup_content;
	}

	public void setShow_warranty_popup_content(String show_warranty_popup_content) {
		this.show_warranty_popup_content = show_warranty_popup_content;
	}

	public int getMonitor_id() {
		return monitor_id;
	}

	public void setMonitor_id(int monitor_id) {
		this.monitor_id = monitor_id;
	}

	public JCancelSubInfo getCancelInfo() {
		return cancelInfo;
	}

	public void setCancelInfo(JCancelSubInfo cancelInfo2) {
		this.cancelInfo = cancelInfo2;
	}

	public String getShow_warranty_popup_v2_content() {
		return show_warranty_popup_v2_content;
	}

	public void setShow_warranty_popup_v2_content(String show_warranty_popup_v2_content) {
		this.show_warranty_popup_v2_content = show_warranty_popup_v2_content;
	}

	public String getStay_with_us_content() {
		return stay_with_us_content;
	}

	public void setStay_with_us_content(String stay_with_us_content) {
		this.stay_with_us_content = stay_with_us_content;
	}

	public JSubscriptionPlanReportV2 getSubscriptionplan() {
		return subscriptionplan;
	}

	public void setSubscriptionplan(JSubscriptionPlanReportV2 rpt) {
		this.subscriptionplan = rpt;
	}

	public boolean isShowpausesub() {
		return showpausesub;
	}

	public void setShowpausesub(boolean showpausesub) {
		this.showpausesub = showpausesub;
	}

	public boolean isPauseStatus() {
		return pauseStatus;
	}

	public void setPauseStatus(boolean pauseStatus) {
		this.pauseStatus = pauseStatus;
	}

	public boolean isShow_check_out_popup() {
		return show_check_out_popup;
	}

	public void setShow_check_out_popup(boolean show_check_out_popup) {
		this.show_check_out_popup = show_check_out_popup;
	}

	public boolean isShow_bundle_contact_us() {
		return show_bundle_contact_us;
	}

	public void setShow_bundle_contact_us(boolean show_bundle_contact_us) {
		this.show_bundle_contact_us = show_bundle_contact_us;
	}

	public String getNextrenew_date_old() {
		return nextrenew_date_old;
	}

	public void setNextrenew_date_old(String nextrenew_date_old) {
		this.nextrenew_date_old = nextrenew_date_old;
	}

	public boolean isValid_bundle_subs() {
		return valid_bundle_subs;
	}

	public void setValid_bundle_subs(boolean valid_bundle_subs) {
		this.valid_bundle_subs = valid_bundle_subs;
	}

	public boolean isShow_warranty_popup() {
		return show_warranty_popup;
	}

	public void setShow_warranty_popup(boolean show_warranty_popup) {
		this.show_warranty_popup = show_warranty_popup;
	}

	public String getOrderid_later_popup_content() {
		return orderid_later_popup_content;
	}

	public void setOrderid_later_popup_content(String orderid_later_popup_content) {
		this.orderid_later_popup_content = orderid_later_popup_content;
	}

	public boolean isWarranty_popup_later_btn() {
		return warranty_popup_later_btn;
	}

	public void setWarranty_popup_later_btn(boolean warranty_popup_later_btn) {
		this.warranty_popup_later_btn = warranty_popup_later_btn;
	}

	public String getPriorityphone() {
		return priorityphone;
	}

	public void setPriorityphone(String priorityphone) {
		this.priorityphone = priorityphone;
	}

	public boolean isImmediate_cancel() {
		return immediate_cancel;
	}

	public void setImmediate_cancel(boolean immediate_cancel) {
		this.immediate_cancel = immediate_cancel;
	}

	public int getRedirect_inapp() {
		return redirect_inapp;
	}

	public void setRedirect_inapp(int redirect_inapp) {
		this.redirect_inapp = redirect_inapp;
	}

	public String getMeariSubKey() {
		return meariSubKey;
	}

	public void setMeariSubKey(String meariSubKey) {
		this.meariSubKey = meariSubKey;
	}

	public String getCur_feature_ui_new() {
		return cur_feature_ui_new;
	}

	public void setCur_feature_ui_new(String cur_feature_ui_new) {
		this.cur_feature_ui_new = cur_feature_ui_new;
	}

	public String getShow_warranty_popup_v2_image() {
		return show_warranty_popup_v2_image;
	}

	public void setShow_warranty_popup_v2_image(String show_warranty_popup_v2_image) {
		this.show_warranty_popup_v2_image = show_warranty_popup_v2_image;
	}

	public boolean isShow_cancel_reward() {
		return show_cancel_reward;
	}

	public void setShow_cancel_reward(boolean show_cancel_reward) {
		this.show_cancel_reward = show_cancel_reward;
	}

	public boolean isShow_alertlimit() {
		return show_alertlimit;
	}

	public void setShow_alertlimit(boolean show_alertlimit) {
		this.show_alertlimit = show_alertlimit;
	}

	public String getCancel_button_text() {
		return cancel_button_text;
	}

	public void setCancel_button_text(String cancel_button_text) {
		this.cancel_button_text = cancel_button_text;
	}

	public String getPausetitle() {
		return pausetitle;
	}

	public void setPausetitle(String pausetitle) {
		this.pausetitle = pausetitle;
	}

	public String getCancelSubUrl() {
		return cancelSubUrl;
	}

	public void setCancelSubUrl(String cancelSubUrl) {
		this.cancelSubUrl = cancelSubUrl;
	}

	public boolean isShow_manage_sub() {
		return show_manage_sub;
	}

	public void setShow_manage_sub(boolean show_manage_sub) {
		this.show_manage_sub = show_manage_sub;
	}

	public boolean isWarranty_claimed() {
		return warranty_claimed;
	}

	public void setWarranty_claimed(boolean warranty_claimed) {
		this.warranty_claimed = warranty_claimed;
	}

	public String getImmediate_cancel_note() {
		return immediate_cancel_note;
	}

	public void setImmediate_cancel_note(String immediate_cancel_note) {
		this.immediate_cancel_note = immediate_cancel_note;
	}

	public String getUpcomingrenewal_cancel_note() {
		return upcomingrenewal_cancel_note;
	}

	public void setUpcomingrenewal_cancel_note(String upcomingrenewal_cancel_note) {
		this.upcomingrenewal_cancel_note = upcomingrenewal_cancel_note;
	}

	public int getPlanid() {
		return planid;
	}

	public void setPlanid(int planid) {
		this.planid = planid;
	}

	public String getBundle_check_out_url() {
		return bundle_check_out_url;
	}

	public void setBundle_check_out_url(String bundle_check_out_url) {
		this.bundle_check_out_url = bundle_check_out_url;
	}

	public boolean isCancel_inapp_redirect() {
		return cancel_inapp_redirect;
	}

	public void setCancel_inapp_redirect(boolean cancel_inapp_redirect) {
		this.cancel_inapp_redirect = cancel_inapp_redirect;
	}

	public String getResumeDate() {
		return resumeDate;
	}

	public void setResumeDate(String resumeDate) {
		this.resumeDate = resumeDate;
	}

	public String getPausecontent() {
		return pausecontent;
	}

	public void setPausecontent(String pausecontent) {
		this.pausecontent = pausecontent;
	}

	public boolean isShow_upgrade() {
		return show_upgrade;
	}

	public void setShow_upgrade(boolean show_upgrade) {
		this.show_upgrade = show_upgrade;
	}

	public String getBundle_contact_us_content() {
		return bundle_contact_us_content;
	}

	public void setBundle_contact_us_content(String bundle_contact_us_content) {
		this.bundle_contact_us_content = bundle_contact_us_content;
	}

	public String getCheck_out_popup_content() {
		return check_out_popup_content;
	}

	public void setCheck_out_popup_content(String check_out_popup_content) {
		this.check_out_popup_content = check_out_popup_content;
	}

	public String getCheck_out_popup_image() {
		return check_out_popup_image;
	}

	public void setCheck_out_popup_image(String check_out_popup_image) {
		this.check_out_popup_image = check_out_popup_image;
	}

	public String getCheck_out_popup_image_dark() {
		return check_out_popup_image_dark;
	}

	public void setCheck_out_popup_image_dark(String check_out_popup_image_dark) {
		this.check_out_popup_image_dark = check_out_popup_image_dark;
	}

	public String getCancel_reward_code() {
		return cancel_reward_code;
	}

	public void setCancel_reward_code(String cancel_reward_code) {
		this.cancel_reward_code = cancel_reward_code;
	}

	public String getCancel_reward_enddate() {
		return cancel_reward_enddate;
	}

	public void setCancel_reward_enddate(String cancel_reward_enddate) {
		this.cancel_reward_enddate = cancel_reward_enddate;
	}

	public String getCancel_reward_title() {
		return cancel_reward_title;
	}

	public void setCancel_reward_title(String cancel_reward_title) {
		this.cancel_reward_title = cancel_reward_title;
	}

	public String getCancel_reward_url() {
		return cancel_reward_url;
	}

	public void setCancel_reward_url(String cancel_reward_url) {
		this.cancel_reward_url = cancel_reward_url;
	}

	public boolean isSetupActivate() {
		return setupActivate;
	}

	public void setSetupActivate(boolean setupActivate) {
		this.setupActivate = setupActivate;
	}

	public Map<String, Object> getDeviceList() {
		return deviceList;
	}

	public void setDeviceList(Map<String, Object> deviceList) {
		this.deviceList = deviceList;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public int getDays_remaining() {
		return days_remaining;
	}

	public void setDays_remaining(int days_remaining) {
		this.days_remaining = days_remaining;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getShow_nextrenewal_popup_content() {
		return show_nextrenewal_popup_content;
	}

	public void setShow_nextrenewal_popup_content(String show_nextrenewal_popup_content) {
		this.show_nextrenewal_popup_content = show_nextrenewal_popup_content;
	}

	public boolean isIs_rec() {
		return is_rec;
	}

	public void setIs_rec(boolean is_rec) {
		this.is_rec = is_rec;
	}

	public boolean isShow_pauseoption() {
		return show_pauseoption;
	}

	public void setShow_pauseoption(boolean show_pauseoption) {
		this.show_pauseoption = show_pauseoption;
	}

	public LinkedList<String> getStay_with_us_list() {
		return stay_with_us_list;
	}

	public void setStay_with_us_list(LinkedList<String> stayList) {
		this.stay_with_us_list = stayList;
	}

	public String getFlexi_next_billing_date() {
		return flexi_next_billing_date;
	}

	public void setFlexi_next_billing_date(String flexi_next_billing_date) {
		this.flexi_next_billing_date = flexi_next_billing_date;
	}

	public boolean getIs_paused() {
		return is_paused;
	}

	public void setIs_paused(boolean is_paused) {
		this.is_paused = is_paused;
	}

	public int getDays_left() {
		return days_left;
	}

	public void setDays_left(int days_left) {
		this.days_left = days_left;
	}

	public boolean getIs_flexi() {
		return is_flexi;
	}

	public void setIs_flexi(boolean is_flexi) {
		this.is_flexi = is_flexi;
	}

	public boolean isShow_flexi_pause() {
		return show_flexi_pause;
	}

	public void setShow_flexi_pause(boolean show_flexi_pause) {
		this.show_flexi_pause = show_flexi_pause;
	}

	public boolean isIs_priorityuser() {
		return is_priorityuser;
	}

	public void setIs_priorityuser(boolean is_priorityuser) {
		this.is_priorityuser = is_priorityuser;
	}

	public String getFlexi_plan_start_date() {
		return flexi_plan_start_date;
	}

	public void setFlexi_plan_start_date(String flexi_plan_start_date) {
		this.flexi_plan_start_date = flexi_plan_start_date;
	}

	public boolean isIs_started() {
		return is_started;
	}

	public void setIs_started(boolean is_started) {
		this.is_started = is_started;
	}

	public int getRemaining_month() {
		return remaining_month;
	}

	public void setRemaining_month(int remaining_month) {
		this.remaining_month = remaining_month;
	}

	public int getPaused_count() {
		return paused_count;
	}

	public void setPaused_count(int paused_count) {
		this.paused_count = paused_count;
	}

	public String getPaused_date_content() {
		return paused_date_content;
	}

	public void setPaused_date_content(String paused_date_content) {
		this.paused_date_content = paused_date_content;
	}

	public boolean isShowAddressPopup() {
		return showAddressPopup;
	}

	public void setShowAddressPopup(boolean showAddressPopup) {
		this.showAddressPopup = showAddressPopup;
	}

	public boolean isShow_coupon() {
		return show_coupon;
	}

	public void setShow_coupon(boolean show_coupon) {
		this.show_coupon = show_coupon;
	}

	public JCouponInfo getCoupon_info() {
		return coupon_info;
	}

	public void setCoupon_info(JCouponInfo coupon_info) {
		this.coupon_info = coupon_info;
	}
	
	
	public String getImg_url() {return img_url;}
	
	public void setImg_url(String img_url) {this.img_url = img_url;}

	public String getPlan_ver() {return plan_ver;}

	public void setPlan_ver(String plan_ver) {this.plan_ver = plan_ver;}

	public boolean getIs_combo() {return is_combo;}

	public void setIs_combo(boolean is_combo) {this.is_combo = is_combo;}
}

