package com.nimble.irisservices.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.nimble.irisservices.dto.JTerms.Terms;

public class JSubPlanDetailsV4 implements Serializable {

    private static final long serialVersionUID = 0l;

    private ArrayList<JSubPeriodDetailV1> period_list ;

    private String plan_name = "NA";

    private boolean is_flexiplan = false;

    private List<String> plan_details;

    ArrayList<Terms> term_list = new ArrayList<Terms>();

    private boolean is_combo_plan = false;

    private boolean is_best_seller;

    private boolean show_combo_content =false ;

    private int managePlanId = 0;

    private String display_content = "";

    private ArrayList<Object> product_list = null;

    public ArrayList<JSubPeriodDetailV1> getPeriod_list() {
        return period_list;
    }

    public void setPeriod_list(ArrayList<JSubPeriodDetailV1> period_list) {
        this.period_list = period_list;
    }

    public String getPlan_name() {
        return plan_name;
    }

    public void setPlan_name(String plan_name) {
        this.plan_name = plan_name;
    }

    public boolean isIs_flexiplan() {
        return is_flexiplan;
    }

    public void setIs_flexiplan(boolean is_flexiplan) {
        this.is_flexiplan = is_flexiplan;
    }

    public List<String> getPlan_details() {
        return plan_details;
    }

    public void setPlan_details(List<String> plan_details) {
        this.plan_details = plan_details;
    }

    public ArrayList<Terms> getTerm_list() {
        return term_list;
    }

    public void setTerm_list(ArrayList<Terms> term_list) {
        this.term_list = term_list;
    }

    public boolean isIs_combo_plan() {
        return is_combo_plan;
    }

    public void setIs_combo_plan(boolean is_combo_plan) {
        this.is_combo_plan = is_combo_plan;
    }

    public boolean isIs_best_seller() {
        return is_best_seller;
    }

    public void setIs_best_seller(boolean is_best_seller) {
        this.is_best_seller = is_best_seller;
    }

    public boolean isShow_combo_content() {
        return show_combo_content;
    }

    public void setShow_combo_content(boolean show_combo_content) {
        this.show_combo_content = show_combo_content;
    }

    public int getManagePlanId() {
        return managePlanId;
    }

    public void setManagePlanId(int managePlanId) {
        this.managePlanId = managePlanId;
    }

    public String getDisplay_content() {
        return display_content;
    }

    public void setDisplay_content(String display_content) {
        this.display_content = display_content;
    }

    public ArrayList<Object> getProduct_list() {
        return product_list;
    }

    public void setProduct_list(ArrayList<Object> product_list) {
        this.product_list = product_list;
    }
}
