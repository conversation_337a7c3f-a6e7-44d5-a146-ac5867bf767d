package com.nimble.irisservices.dto;

public class JPetFeedDetails {

	private long pet_feed_details_id = 0;
	
	private String food_name = "NA";
	
	private String meal_content = "NA";
	
	private String meal_time = "NA";
	
	private double kcal = 0;

	public long getPet_feed_details_id() {
		return pet_feed_details_id;
	}

	public void setPet_feed_details_id(long pet_feed_details_id) {
		this.pet_feed_details_id = pet_feed_details_id;
	}

	public String getFood_name() {
		return food_name;
	}

	public void setFood_name(String food_name) {
		this.food_name = food_name;
	}

	public String getMeal_content() {
		return meal_content;
	}

	public void setMeal_content(String meal_content) {
		this.meal_content = meal_content;
	}

	public String getMeal_time() {
		return meal_time;
	}

	public void setMeal_time(String meal_time) {
		this.meal_time = meal_time;
	}

	public double getKcal() {
		return kcal;
	}

	public void setKcal(double kcal) {
		this.kcal = kcal;
	}

	public JPetFeedDetails() {
		super();
	}

	public JPetFeedDetails(long pet_feed_details_id, String food_name, String meal_content, String meal_time, double kcal) {
		super();
		this.pet_feed_details_id = pet_feed_details_id;
		this.food_name = food_name;
		this.meal_content = meal_content;
		this.meal_time = meal_time;
		this.kcal = kcal;
	}
	
	
	
}
