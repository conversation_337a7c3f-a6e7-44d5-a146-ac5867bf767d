package com.nimble.irisservices.dto;

public class JBowlPetProfile {

	private long id=0;
	
	private String name;
	
	private String breed;	
	
	private float weight=0;
	
	private String specieName="";
	
	private boolean intact = true;
	
	private long user_id = 0;
	
	private String birth_date= "1753-01-01";
	
	private long speciesid = 1;
	
	private long gatewayId =0;
	
	private String structure = "ideal";

	private double kcal = 0;
	
	private String activitylevel = "Inactive";
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getBreed() {
		return breed;
	}

	public void setBreed(String breed) {
		this.breed = breed;
	}

	public float getWeight() {
		return weight;
	}

	public void setWeight(float weight) {
		this.weight = weight;
	}

	public String getSpecieName() {
		return specieName;
	}

	public void setSpecieName(String specieName) {
		this.specieName = specieName;
	}

	public boolean isIntact() {
		return intact;
	}

	public void setIntact(boolean intact) {
		this.intact = intact;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getBirth_date() {
		return birth_date;
	}

	public void setBirth_date(String birth_date) {
		this.birth_date = birth_date;
	}

	public long getSpeciesid() {
		return speciesid;
	}

	public void setSpeciesid(long speciesid) {
		this.speciesid = speciesid;
	}

	public long getGatewayId() {
		return gatewayId;
	}

	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}

	public String getStructure() {
		return structure;
	}

	public void setStructure(String structure) {
		this.structure = structure;
	}

	public double getKcal() {
		return kcal;
	}

	public void setKcal(double kcal) {
		this.kcal = kcal;
	}

	public JBowlPetProfile() {
		super();
	}
	
	public JBowlPetProfile(long id, String name, String breed, float weight, String specieName, boolean intact,
			long user_id, String birth_date, long speciesid, long gatewayId, String structure, double kcal) {
		super();
		this.id = id;
		this.name = name;
		this.breed = breed;
		this.weight = weight;
		this.specieName = specieName;
		this.intact = intact;
		this.user_id = user_id;
		this.birth_date = birth_date;
		this.speciesid = speciesid;
		this.gatewayId = gatewayId;
		this.structure = structure;
		this.kcal = kcal;
	}

	public String getActivitylevel() {
		return activitylevel;
	}

	public void setActivitylevel(String activitylevel) {
		this.activitylevel = activitylevel;
	}
	
	
	
}
