package com.nimble.irisservices.dto;

public class JGatewayMonthList {
	private String date = "";
	private float weight = 0;
	private double totalCal = 0;
	private Double reqCalories = 0d;
	private Double reqWeight = 0d;
	private String day = "";
	private boolean show_activate = true;
	
	
	public JGatewayMonthList() {
		super();
	}
	
	public JGatewayMonthList(String date, float weight, Double totalCal, String day) {
		super();
		this.date = date;
		this.weight = weight;
		this.totalCal = totalCal;
		this.day = day;
	}
	public String getDate() {
		return date;
	}
	public void setDate(String date) {
		this.date = date;
	}
	public float getWeight() {
		return weight;
	}
	public void setWeight(float weight) {
		this.weight = weight;
	}
	public double getTotalCal() {
		return totalCal;
	}
	public void setTotalCal(double totalCal) {
		this.totalCal = totalCal;
	}
	public String getDay() {
		return day;
	}
	public void setDay(String day) {
		this.day = day;
	}

	public Double getReqCalories() {
		return reqCalories;
	}

	public void setReqCalories(Double reqCalories) {
		this.reqCalories = reqCalories;
	}

	public Double getReqWeight() {
		return reqWeight;
	}

	public void setReqWeight(Double reqWeight) {
		this.reqWeight = reqWeight;
	}

	public boolean isShow_activate() {
		return show_activate;
	}

	public void setShow_activate(boolean show_activate) {
		this.show_activate = show_activate;
	}

}
