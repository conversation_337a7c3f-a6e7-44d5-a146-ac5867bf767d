package com.nimble.irisservices.dto;

import java.io.Serializable;
import java.util.ArrayList;

public class JSubPlanDetails implements Serializable {

	private static final long serialVersionUID = 0l;
	
	ArrayList<JMenuLst> feature_list;
	
	private ArrayList<JSubPeriodDetail> period_list ;

	public ArrayList<JMenuLst> getFeature_list() {
		return feature_list;
	}

	public void setFeature_list(ArrayList<JMenuLst> feature_list) {
		this.feature_list = feature_list;
	}

	public ArrayList<JSubPeriodDetail> getPeriod_list() {
		return period_list;
	}

	public void setPeriod_list(ArrayList<JSubPeriodDetail> period_list) {
		this.period_list = period_list;
	}	

}
