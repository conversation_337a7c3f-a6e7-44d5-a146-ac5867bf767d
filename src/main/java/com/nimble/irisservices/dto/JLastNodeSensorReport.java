package com.nimble.irisservices.dto;

import java.io.Serializable;

@SuppressWarnings("serial")
public class JLastNodeSensorReport implements Serializable{
	
	private long gatewayid = 0;
	private String gatewayname = "NA";
	private int battery = 0;
	private int wifiRange = 0;
	private String datetime = "NA";
	private String imgUrl = "NA";
	private String qrcode = "NA";
	private long monitortype = 0;
	private boolean show_activate = false;
	private long planId = 0;
	private boolean is_freePlan = true;
	private String sub_id = "NA";
	private String event_name = "NA";
	private String sensor_type = "NA";
	private long sensor_type_code = 0; 
	private String senLocation = "NA";
	private int curr_status = 2;
	private boolean paired = false;
	
	public int getBattery() {
		return battery;
	}
	public void setBattery(int battery) {
		this.battery = battery;
	}
	public int getWifiRange() {
		return wifiRange;
	}
	public void setWifiRange(int wifiRange) {
		this.wifiRange = wifiRange;
	}
	public String getDatetime() {
		return datetime;
	}
	public void setDatetime(String datetime) {
		this.datetime = datetime;
	}
	public String getImgUrl() {
		return imgUrl;
	}
	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}
	public String getQrcode() {
		return qrcode;
	}

	public void setQrcode(String qrcode) {
		this.qrcode = qrcode;
	}
	public long getMonitortype() {
		return monitortype;
	}
	public void setMonitortype(long monitortype) {
		this.monitortype = monitortype;
	}
	public boolean isShow_activate() {
		return show_activate;
	}
	public void setShow_activate(boolean show_activate) {
		this.show_activate = show_activate;
	}
	public long getPlanId() {
		return planId;
	}
	public void setPlanId(long planId) {
		this.planId = planId;
	}
	public boolean isIs_freePlan() {
		return is_freePlan;
	}
	public void setIs_freePlan(boolean is_freePlan) {
		this.is_freePlan = is_freePlan;
	}
	public String getSub_id() {
		return sub_id;
	}
	public void setSub_id(String sub_id) {
		this.sub_id = sub_id;
	}
	public long getGatewayid() {
		return gatewayid;
	}
	public void setGatewayid(long gatewayid) {
		this.gatewayid = gatewayid;
	}
	public String getGatewayname() {
		return gatewayname;
	}
	public void setGatewayname(String gatewayname) {
		this.gatewayname = gatewayname;
	}
	public String getEvent_name() {
		return event_name;
	}
	public void setEvent_name(String event_name) {
		this.event_name = event_name;
	}
	public String getSensor_type() {
		return sensor_type;
	}
	public void setSensor_type(String sensor_type) {
		this.sensor_type = sensor_type;
	}
	public long getSensor_type_code() {
		return sensor_type_code;
	}
	public void setSensor_type_code(long sensor_type_code) {
		this.sensor_type_code = sensor_type_code;
	}
	public String getSenLocation() {
		return senLocation;
	}
	public void setSenLocation(String senLocation) {
		this.senLocation = senLocation;
	}
	public int getCurr_status() {
		return curr_status;
	}
	public void setCurr_status(int curr_status) {
		this.curr_status = curr_status;
	}
	public boolean isPaired() {
		return paired;
	}
	public void setPaired(boolean paired) {
		this.paired = paired;
	}
	
}
