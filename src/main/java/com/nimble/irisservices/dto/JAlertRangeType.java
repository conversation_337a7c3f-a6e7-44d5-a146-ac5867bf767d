package com.nimble.irisservices.dto;

public class JAlertRangeType {
	
	private long id;
	private String name = "NA";
	private String description = "NA";
	private long alerttype_id = 0;
	private float minval;
	private float maxval;
	
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public long getAlerttype_id() {
		return alerttype_id;
	}
	public void setAlerttype_id(long alerttype_id) {
		this.alerttype_id = alerttype_id;
	}
	public float getMinval() {
		return minval;
	}
	public void setMinval(float minval) {
		this.minval = minval;
	}
	public float getMaxval() {
		return maxval;
	}
	public void setMaxval(float maxval) {
		this.maxval = maxval;
	}
	
}
