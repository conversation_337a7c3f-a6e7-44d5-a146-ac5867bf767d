package com.nimble.irisservices.dto;

import java.io.Serializable;

@SuppressWarnings("serial")
public class JSreiAssetLastReport implements Serializable {
	
	private long assetid;
	private String assetname;
//	private String assetgroupname;
	private String groupname;
	private long groupid;
//	private String subgroupname;
	private String datetime;
	private int batt;
	/*private float extsensor;
	private String extsensortype;
	private float humidity;
	private float temperature;
	private int tempseverity;
	private float light;
	private float pressure;
	private String motion;*/
	private String rssi;
	private double lat;
	private String latdir;
	private double lon;
	private String londir;
//	private float speed;
	private String gpsstatus;
/*	private float distance;
	private String heading;*/
	private String eventid;
	/*private String nmeventid;
	private int iostatus;*/
	private String address;
	private boolean alive;
	private JAssetInformation assetInfo;
	private double lastvalidlat;
	private String lastvalidlatdir;
	private double lastvalidlon;
	private String lastvalidlondir;
	private String lastvaliddatetime;
	
	private double cellidlat;
	private double cellidlon;
	private float cellidacc;
	private String eventname;
	private String lastvalidaddress;
	private double lat_d;
	private	double lon_d;
	private double lastvalidlat_d;
	private double lastvalidlon_d;
	
/*	public JSreiAssetLastReport(long assetid, String assetname, String assetgroupname,
			String groupname, String datetime,
			float lat, String latdir, float lon, String londir, float speed,
			String gpsstatus, int batt, float distance, String heading, String eventid, String nmeventid, int iostatus,
			float extsensor,String extsensortype, float humidity, float temperature, int tempseverity,float light,
			float pressure, String motion, String rssi, String address,boolean alive,
			float lastvalidlat, String lastvalidlatdir, float lastvalidlon,String lastvalidlondir,
			Timestamp lastvaliddatetime,AssetInformation assetInformat,long groupid) {*/
	
	public JSreiAssetLastReport(long assetid, String assetname, String groupname, String datetime,double lat, 
			String latdir, double lon, String londir, String gpsstatus, int batt,String eventid, String rssi, 
			String address,boolean alive,double lastvalidlat, String lastvalidlatdir, double lastvalidlon,
			String lastvalidlondir,String lastvaliddatetime,JAssetInformation assetInformat,long groupid,
			double cellidlat,double cellidlon,float cellidacc,String eventname,String lastvalidaddress,
			double lat_d,double lon_d,double lastvalidlat_d,double lastvalidlon_d)  {
		super();
		this.assetid = assetid;
		this.assetname = assetname;
	
		this.groupname = groupname;
		this.datetime = datetime;
		this.lat = lat;
		this.latdir = latdir;
		this.lon = lon;
		this.londir = londir;
		this.gpsstatus = gpsstatus;
		this.batt = batt;
		this.eventid = eventid;
		this.rssi = rssi;
		this.address = address;
		this.alive = alive;
		this.lastvalidlat = lastvalidlat;
		this.lastvalidlatdir = lastvalidlatdir;
		this.lastvalidlon = lastvalidlon;
		this.lastvalidlondir = lastvalidlondir;
		this.lastvaliddatetime = lastvaliddatetime;
		this.assetInfo = assetInformat;
		this.groupid = groupid;
		this.cellidlat = cellidlat;
		this.cellidlon = cellidlon;
		this.cellidacc = cellidacc;
		this.eventname = eventname;
		this.lastvalidaddress = lastvalidaddress;
		this.lat_d = lat_d;
		this.lon_d = lon_d;
		this.lastvalidlat_d = lastvalidlat_d;
		this.lastvalidlon_d = lastvalidlon_d;
		
		/*this.subgroupname = subgroupname;
		this.speed = speed;
		this.distance = distance;
		this.heading = heading;
		this.nmeventid = nmeventid;
		this.iostatus = iostatus;
		this.extsensor = extsensor;
		this.extsensortype = extsensortype;
		this.humidity = humidity;
		this.temperature = temperature;
		this.tempseverity = tempseverity;
		this.light = light;
		this.pressure = pressure;
		this.motion = motion;
		this.assetgroupname = assetgroupname;
		*/
	}

	public long getGroupid() {
		return groupid;
	}

	public void setGroupid(long groupid) {
		this.groupid = groupid;
	}

	public JAssetInformation getAssetInfo() {
		return assetInfo;
	}

	public long getAssetid() {
		return assetid;
	}

	public void setAssetid(long assetid) {
		this.assetid = assetid;
	}

	public String getAssetname() {
		return assetname;
	}

	public void setAssetname(String assetname) {
		this.assetname = assetname;
	}

	/*public String getAssetgroupname() {
		return assetgroupname;
	}
*/


	public String getGroupname() {
		return groupname;
	}



	/*public String getSubgroupname() {
		return subgroupname;
	}*/



	public String getDatetime() {
		return datetime;
	}



	public int getBatt() {
		return batt;
	}



	/*public float getExtsensor() {
		return extsensor;
	}



	public String getExtsensortype() {
		return extsensortype;
	}*/



	/*public float getHumidity() {
		return humidity;
	}



	public float getTemperature() {
		return temperature;
	}

	

	public int getTempseverity() {
		return tempseverity;
	}



	public float getLight() {
		return light;
	}



	public float getPressure() {
		return pressure;
	}



	public String getMotion() {
		return motion;
	}*/



	public String getRssi() {
		return rssi;
	}



	public double getLat() {
		return lat;
	}



	public String getLatdir() {
		return latdir;
	}



	public double getLon() {
		return lon;
	}



	public String getLondir() {
		return londir;
	}



	/*public float getSpeed() {
		return speed;
	}*/



	public String getGpsstatus() {
		return gpsstatus;
	}



	/*public float getDistance() {
		return distance;
	}



	public String getHeading() {
		return heading;
	}*/



	

	public String getEventid() {
		return eventid;
	}



	/*public String getNmeventid() {
		return nmeventid;
	}



	public int getIostatus() {
		return iostatus;
	}
*/


	public String getAddress() {
		return address;
	}



	public boolean getAlive() {
		return alive;
	}




	/*public void setAssetgroupname(String assetgroupname) {
		this.assetgroupname = assetgroupname;
	}*/



	public void setGroupname(String groupname) {
		this.groupname = groupname;
	}



	/*public void setSubgroupname(String subgroupname) {
		this.subgroupname = subgroupname;
	}*/



	public void setDatetime(String datetime) {
		this.datetime = datetime;
	}



	public void setBatt(int batt) {
		this.batt = batt;
	}



	/*public void setExtsensor(float extsensor) {
		this.extsensor = extsensor;
	}



	public void setExtsensortype(String extsensortype) {
		this.extsensortype = extsensortype;
	}



	public void setHumidity(float humidity) {
		this.humidity = humidity;
	}



	public void setTemperature(float temperature) {
		this.temperature = temperature;
	}



	public void setTempseverity(int tempseverity) {
		this.tempseverity = tempseverity;
	}



	public void setLight(float light) {
		this.light = light;
	}



	public void setPressure(float pressure) {
		this.pressure = pressure;
	}



	public void setMotion(String motion) {
		this.motion = motion;
	}


*/
	public void setRssi(String rssi) {
		this.rssi = rssi;
	}



	public void setLat(float lat) {
		this.lat = lat;
	}



	public void setLatdir(String latdir) {
		this.latdir = latdir;
	}



	public void setLon(float lon) {
		this.lon = lon;
	}



	public void setLondir(String londir) {
		this.londir = londir;
	}



	/*public void setSpeed(float speed) {
		this.speed = speed;
	}
*/


	public void setGpsstatus(String gpsstatus) {
		this.gpsstatus = gpsstatus;
	}


/*
	public void setDistance(float distance) {
		this.distance = distance;
	}



	public void setHeading(String heading) {
		this.heading = heading;
	}*/



	public void setEventid(String eventid) {
		this.eventid = eventid;
	}



	/*public void setNmeventid(String nmeventid) {
		this.nmeventid = nmeventid;
	}



	public void setIostatus(int iostatus) {
		this.iostatus = iostatus;
	}*/



	public void setAddress(String address) {
		this.address = address;
	}

	public void setAlive(boolean alive) {
		this.alive = alive;
	}	
	
	public double getLastvalidlat() {
		return lastvalidlat;
	}

	public String getLastvalidlatdir() {
		return lastvalidlatdir;
	}

	public double getLastvalidlon() {
		return lastvalidlon;
	}

	public String getLastvalidlondir() {
		return lastvalidlondir;
	}

	public String getLastvaliddatetime() {
		return lastvaliddatetime;
	}
	
	public double getCellidlat() {
		return cellidlat;
	}

	public double getCellidlon() {
		return cellidlon;
	}

	public float getCellidacc() {
		return cellidacc;
	}
	
	public String getEventname() {
		return eventname;
	}

	public void setEventname(String eventname) {
		this.eventname = eventname;
	}

	public String getLastvalidaddress() {
		return lastvalidaddress;
	}

	public double getLat_d() {
		return lat_d;
	}

	public void setLat_d(double lat_d) {
		this.lat_d = lat_d;
	}

	public double getLon_d() {
		return lon_d;
	}

	public void setLon_d(double lon_d) {
		this.lon_d = lon_d;
	}

	public double getLastvalidlat_d() {
		return lastvalidlat_d;
	}

	public void setLastvalidlat_d(double lastvalidlat_d) {
		this.lastvalidlat_d = lastvalidlat_d;
	}

	public double getLastvalidlon_d() {
		return lastvalidlon_d;
	}

	public void setLastvalidlon_d(double lastvalidlon_d) {
		this.lastvalidlon_d = lastvalidlon_d;
	}
}
