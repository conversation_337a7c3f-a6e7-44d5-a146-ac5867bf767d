package com.nimble.irisservices.dto;

import java.io.Serializable;

public class JGatewaySubSetup implements Serializable {
	private static final long serialVersionUID = 0l;

	private boolean setupActivate;
	private long gateway_id;
	private long gateway;
	private int days_remaining;
	private String meid;
	private String name = "NA";
	private String show_nextrenewal_popup_content="";

	public JGatewaySubSetup() {
		super();
	}

	public JGatewaySubSetup(long gateway_id, boolean setupActivate, int days_remaining,String meid, String show_nextrenewal_popup_content) {
		super();
		this.gateway = gateway_id;
		this.gateway_id = gateway_id;
		this.setupActivate = setupActivate;
		this.days_remaining = days_remaining;
		this.meid = meid;
		this.show_nextrenewal_popup_content = show_nextrenewal_popup_content;
	}
	
	public JGatewaySubSetup(long gateway_id, boolean setupActivate, int days_remaining,String meid, String name, String show_nextrenewal_popup_content) {
		super();
		this.gateway = gateway_id;
		this.gateway_id = gateway_id;
		this.setupActivate = setupActivate;
		this.days_remaining = days_remaining;
		this.meid = meid;
		this.name = name;
		this.show_nextrenewal_popup_content = show_nextrenewal_popup_content;
	}

	public boolean isSetupActivate() {
		return setupActivate;
	}

	public void setSetupActivate(boolean setupActivate) {
		this.setupActivate = setupActivate;
	}	

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public long getGateway() {
		return gateway;
	}

	public void setGateway(long gateway) {
		this.gateway = gateway;
	}

	public int getDays_remaining() {
		return days_remaining;
	}

	public void setDays_remaining(int days_remaining) {
		this.days_remaining = days_remaining;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getShow_nextrenewal_popup_content() {
		return show_nextrenewal_popup_content;
	}

	public void setShow_nextrenewal_popup_content(String show_nextrenewal_popup_content) {
		this.show_nextrenewal_popup_content = show_nextrenewal_popup_content;
	}
	

}
