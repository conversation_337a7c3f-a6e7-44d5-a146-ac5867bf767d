package com.nimble.irisservices.dto;

import com.nimble.irisservices.entity.AssetInformation;
import com.nimble.irisservices.entity.Gateway;
import com.nimble.irisservices.entity.Groups;
import com.nimble.irisservices.entity.LastGatewayRpt;

public class JLastGatewayAndReport {

	private Gateway gateway;
	private LastGatewayRpt lastGatewayRpt;
	private Groups grps;
	private AssetInformation assetInfo;
	
	
	public JLastGatewayAndReport(Gateway gateway,
			LastGatewayRpt lastGatewayRpt, Groups grps,
			AssetInformation assetInfo) {
		super();
		this.gateway = gateway;
		this.lastGatewayRpt = lastGatewayRpt;
		this.grps = grps;
		this.assetInfo = assetInfo;
	}
	public Gateway getGateway() {
		return gateway;
	}
	public LastGatewayRpt getLastGatewayRpt() {
		return lastGatewayRpt;
	}
	public Groups getGrps() {
		return grps;
	}
	public AssetInformation getAssetInfo() {
		return assetInfo;
	}
}
