package com.nimble.irisservices.dto;

public class CompanyConfigResponse {
	
	private long CmpCfg_id ;
	
	private long Cmp_id ;
	
	private String gatewayname="";
	
	private String optgatewayname ="";
	
	private boolean realtimemonitor = true;
	
	private String temperatureunit = "F";
	
	private String humidityunit = "%";
	
	private String sensorenable = "01100001";
	
	private boolean heatindex_enable = true;
	
	private boolean petservice_enable = false;
	
	private boolean livetrack_enable = false;
	
	private boolean mapview = false;
	
	private long mobileNos;
	
	private long emailIds;
	
	private long throttsettings_id  ;
	
	private long cmptype_id ;
	
	private boolean is_vpm = false;
	
	private boolean show_referralmsg = false;
	
	private boolean appnotifyenable = false;
	
	private boolean geofence_enable = false;
	
	public CompanyConfigResponse() {
		// TODO Auto-generated constructor stub
		super();
	}

	public boolean isRealtimemonitor() {
		return realtimemonitor;
	}

	public void setRealtimemonitor(boolean realtimemonitor) {
		this.realtimemonitor = realtimemonitor;
	}

	public String getTemperatureunit() {
		return temperatureunit;
	}

	public void setTemperatureunit(String temperatureunit) {
		this.temperatureunit = temperatureunit;
	}

	public String getHumidityunit() {
		return humidityunit;
	}

	public void setHumidityunit(String humidityunit) {
		this.humidityunit = humidityunit;
	}

	public String getSensorenable() {
		return sensorenable;
	}

	public void setSensorenable(String sensorenable) {
		this.sensorenable = sensorenable;
	}

	public boolean isHeatindex_enable() {
		return heatindex_enable;
	}

	public void setHeatindex_enable(boolean heatindex_enable) {
		this.heatindex_enable = heatindex_enable;
	}

	public boolean isPetservice_enable() {
		return petservice_enable;
	}

	public void setPetservice_enable(boolean petservice_enable) {
		this.petservice_enable = petservice_enable;
	}

	public boolean isLivetrack_enable() {
		return livetrack_enable;
	}

	public void setLivetrack_enable(boolean livetrack_enable) {
		this.livetrack_enable = livetrack_enable;
	}

	public long getCmpCfg_id() {
		return CmpCfg_id;
	}

	public void setCmpCfg_id(long cmpCfg_id) {
		CmpCfg_id = cmpCfg_id;
	}

	public long getCmp_id() {
		return Cmp_id;
	}

	public void setCmp_id(long cmp_id) {
		Cmp_id = cmp_id;
	}

	public String getGatewayname() {
		return gatewayname;
	}

	public void setGatewayname(String gatewayname) {
		this.gatewayname = gatewayname;
	}

	public String getOptgatewayname() {
		return optgatewayname;
	}

	public void setOptgatewayname(String optgatewayname) {
		this.optgatewayname = optgatewayname;
	}

	public boolean isMapview() {
		return mapview;
	}

	public void setMapview(boolean mapview) {
		this.mapview = mapview;
	}
	
	public long getMobileNos() {
		return mobileNos;
	}

	public void setMobileNos(long mobileNos) {
		this.mobileNos = mobileNos;
	}

	public long getEmailIds() {
		return emailIds;
	}

	public void setEmailIds(long emailIds) {
		this.emailIds = emailIds;
	}

	public long getThrottsettings_id() {
		return throttsettings_id;
	}

	public void setThrottsettings_id(long throttsettings_id) {
		this.throttsettings_id = throttsettings_id;
	}

	public long getCmptype_id() {
		return cmptype_id;
	}

	public void setCmptype_id(long cmptype_id) {
		this.cmptype_id = cmptype_id;
	}

	public boolean isIs_vpm() {
		return is_vpm;
	}

	public void setIs_vpm(boolean is_vpm) {
		this.is_vpm = is_vpm;
	}

	public boolean isAppnotifyenable() {
		return appnotifyenable;
	}

	public void setAppnotifyenable(boolean appnotifyenable) {
		this.appnotifyenable = appnotifyenable;
	}

	public boolean isShow_referralmsg() {
		return show_referralmsg;
	}

	public void setShow_referralmsg(boolean show_referralmsg) {
		this.show_referralmsg = show_referralmsg;
	}

	public boolean isGeofence_enable() {
		return geofence_enable;
	}

	public void setGeofence_enable(boolean geofence_enable) {
		this.geofence_enable = geofence_enable;
	}
	
	
}
