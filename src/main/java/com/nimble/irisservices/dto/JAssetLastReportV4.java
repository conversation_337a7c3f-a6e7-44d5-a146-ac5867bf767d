package com.nimble.irisservices.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("serial")
public class JAssetLastReportV4 implements Serializable {
	
	private long assetid;
	private String assetname;
	private String assetgroupname;
	private String groupname;
	private long groupid;
	private String datetime;
	private int batt;
	private float extsensor;
	private String extsensortype;
	private float humidity;
	private float temperature;
	private int tempseverity;
	private float light;
	private float pressure;
	private String motion;
	private String rssi;
	private double lat;
	private String latdir;
	private double lon;
	private String londir;
	private float speed;
	private String gpsstatus;
	private String gpsinfo;
	private int gpsmode;
	private float distance;
	private String heading;
	private String eventid;
	private String nmeventid;
	private int iostatus;
	private String address;
	private boolean alive;			
	private JAssetInformation assetInfo;	
	private double lastvalidlat;
	private String lastvalidlatdir;
	private double lastvalidlon;
	private String lastvalidlondir;
	private String lastvaliddatetime;	
	private double cellidlat;
	private double cellidlon;
	private float cellidacc;
	private String eventname;
	private String lastvalidaddress;
	private int rawrssi;
	private double lat_d;
	private	double lon_d;
	private double lastvalidlat_d;
	private double lastvalidlon_d;
	private float lastvalidtemp;
	private String rpt_timezone;
	private float heat_index;
	private boolean enableLocation;	
	private String probeType;
	private boolean onoffstatus;
	private String updatedon;
	private boolean is_upgrade;	
	private boolean ishumidity;
	private int default_report;
	private String default_rpt_msg;
	private String default_rpt_label;
	private boolean show_battery = true;
	private String bat_info="NA";
	private boolean isN13 = false;
	private boolean show_temp_video = false;
	private boolean show_N13Poff = false;
	private long prev_wakeup_time=0;
	private int aqi=0;
	private int voc=0;
	private int co2=0;
	private String co2_info="";
	private String aqi_info="";
	private String aqi_desc="";
	private String voc_info="";
	private String default_rpt_msg_watch = "NA";
	private String device_type = "NA";
	private Double foodCal = 0d;
	private float foodIntake = 0;
	private Double caloriesConsumed = 0d;
	private Double actualCalories = 0d;
	private int battery = 0;
	private int wifiRange = 0;
	private long profileId = 0;
	private String imgUrl = "NA";
	private List<BluetoothDeviceList> bledevicelist = new ArrayList<>();

	private boolean show_aqi = false;

	
	public JAssetLastReportV4() {
		super();
	}
	
	public JAssetLastReportV4(long assetid, String assetname, String assetgroupname,String groupname, String datetime,
			double lat, String latdir, double lon, String londir, float speed,String gpsstatus, String gpsInfo, 
			int gpsMode,int batt, float distance, String heading, String eventid, String nmeventid, int iostatus,
			float extsensor,String extsensortype, float humidity, float temperature, int tempseverity,float light,			
			float pressure, String motion, String rssi, String address,boolean alive,double lastvalidlat, 			
			String lastvalidlatdir, double lastvalidlon,String lastvalidlondir,String lastvaliddatetime,
			JAssetInformation assetInformat,long groupid,double cellidlat,double cellidlon,float cellidacc,
			String eventname,String lastvalidaddress,int rawrssi,double lat_d,double lon_d,double lastvalidlat_d,
			double lastvalidlon_d,float lastvalidtemp,String rpt_timezone, float heat_index,String probeType,
			boolean onoffstatus) {
		super();
		this.assetid = assetid;
		this.assetname = assetname;
		this.assetgroupname = assetgroupname;
		this.groupname = groupname;
/*		this.subgroupname = subgroupname;
*/		this.datetime = datetime;
		this.lat = lat;
		this.latdir = latdir;
		this.lon = lon;
		this.londir = londir;
		this.speed = speed;
		this.gpsstatus = gpsstatus;
		this.gpsinfo = gpsInfo;
		this.gpsmode = gpsMode;
		this.batt = batt;
		this.distance = distance;
		this.heading = heading;
		this.eventid = eventid;
		this.nmeventid = nmeventid;
		this.iostatus = iostatus;
		this.extsensor = extsensor;
		this.extsensortype = extsensortype;
		this.humidity = humidity;
		this.temperature = temperature;
		this.tempseverity = tempseverity;
		this.light = light;
		this.pressure = pressure;
		this.motion = motion;
		this.rssi = rssi;
		this.address = address;
		this.alive = alive;
		this.lastvalidlat = lastvalidlat;
		this.lastvalidlatdir = lastvalidlatdir;
		this.lastvalidlon = lastvalidlon;
		this.lastvalidlondir = lastvalidlondir;
		this.lastvaliddatetime = lastvaliddatetime;
		this.assetInfo = assetInformat;
		this.groupid = groupid;
		this.cellidlat = cellidlat;
		this.cellidlon = cellidlon;
		this.cellidacc = cellidacc;
		this.eventname = eventname;
		this.lastvalidaddress = lastvalidaddress;
		this.rawrssi = rawrssi;
		this.lat_d = lat_d;
		this.lon_d = lon_d;
		this.lastvalidlat_d = lastvalidlat_d;
		this.lastvalidlon_d = lastvalidlon_d;
		this.lastvalidtemp 	= lastvalidtemp;
		this.rpt_timezone = rpt_timezone;
		this.heat_index = heat_index;
		this.probeType = probeType;
		this.onoffstatus = onoffstatus;
	}

	/**
	 * @return the assetid
	 */
	public long getAssetid() {
		return assetid;
	}

	/**
	 * @param assetid the assetid to set
	 */
	public void setAssetid(long assetid) {
		this.assetid = assetid;
	}

	/**
	 * @return the assetname
	 */
	public String getAssetname() {
		return assetname;
	}

	/**
	 * @param assetname the assetname to set
	 */
	public void setAssetname(String assetname) {
		this.assetname = assetname;
	}

	/**
	 * @return the assetgroupname
	 */
	public String getAssetgroupname() {
		return assetgroupname;
	}

	/**
	 * @param assetgroupname the assetgroupname to set
	 */
	public void setAssetgroupname(String assetgroupname) {
		this.assetgroupname = assetgroupname;
	}

	/**
	 * @return the groupname
	 */
	public String getGroupname() {
		return groupname;
	}

	/**
	 * @param groupname the groupname to set
	 */
	public void setGroupname(String groupname) {
		this.groupname = groupname;
	}

	/**
	 * @return the groupid
	 */
	public long getGroupid() {
		return groupid;
	}

	/**
	 * @param groupid the groupid to set
	 */
	public void setGroupid(long groupid) {
		this.groupid = groupid;
	}

	/**
	 * @return the datetime
	 */
	public String getDatetime() {
		return datetime;
	}

	/**
	 * @param datetime the datetime to set
	 */
	public void setDatetime(String datetime) {
		this.datetime = datetime;
	}

	/**
	 * @return the batt
	 */
	public int getBatt() {
		return batt;
	}

	/**
	 * @param batt the batt to set
	 */
	public void setBatt(int batt) {
		this.batt = batt;
	}

	/**
	 * @return the extsensor
	 */
	public float getExtsensor() {
		return extsensor;
	}

	/**
	 * @param extsensor the extsensor to set
	 */
	public void setExtsensor(float extsensor) {
		this.extsensor = extsensor;
	}

	/**
	 * @return the extsensortype
	 */
	public String getExtsensortype() {
		return extsensortype;
	}

	/**
	 * @param extsensortype the extsensortype to set
	 */
	public void setExtsensortype(String extsensortype) {
		this.extsensortype = extsensortype;
	}

	/**
	 * @return the humidity
	 */
	public float getHumidity() {
		return humidity;
	}

	/**
	 * @param humidity the humidity to set
	 */
	public void setHumidity(float humidity) {
		this.humidity = humidity;
	}

	/**
	 * @return the temperature
	 */
	public float getTemperature() {
		return temperature;
	}

	/**
	 * @param temperature the temperature to set
	 */
	public void setTemperature(float temperature) {
		this.temperature = temperature;
	}

	/**
	 * @return the tempseverity
	 */
	public int getTempseverity() {
		return tempseverity;
	}

	/**
	 * @param tempseverity the tempseverity to set
	 */
	public void setTempseverity(int tempseverity) {
		this.tempseverity = tempseverity;
	}

	/**
	 * @return the light
	 */
	public float getLight() {
		return light;
	}

	/**
	 * @param light the light to set
	 */
	public void setLight(float light) {
		this.light = light;
	}

	/**
	 * @return the pressure
	 */
	public float getPressure() {
		return pressure;
	}

	/**
	 * @param pressure the pressure to set
	 */
	public void setPressure(float pressure) {
		this.pressure = pressure;
	}

	/**
	 * @return the motion
	 */
	public String getMotion() {
		return motion;
	}

	/**
	 * @param motion the motion to set
	 */
	public void setMotion(String motion) {
		this.motion = motion;
	}

	/**
	 * @return the rssi
	 */
	public String getRssi() {
		return rssi;
	}

	/**
	 * @param rssi the rssi to set
	 */
	public void setRssi(String rssi) {
		this.rssi = rssi;
	}

	/**
	 * @return the lat
	 */
	public double getLat() {
		return lat;
	}

	/**
	 * @param lat the lat to set
	 */
	public void setLat(double lat) {
		this.lat = lat;
	}

	/**
	 * @return the latdir
	 */
	public String getLatdir() {
		return latdir;
	}

	/**
	 * @param latdir the latdir to set
	 */
	public void setLatdir(String latdir) {
		this.latdir = latdir;
	}

	/**
	 * @return the lon
	 */
	public double getLon() {
		return lon;
	}

	/**
	 * @param lon the lon to set
	 */
	public void setLon(double lon) {
		this.lon = lon;
	}

	/**
	 * @return the londir
	 */
	public String getLondir() {
		return londir;
	}

	/**
	 * @param londir the londir to set
	 */
	public void setLondir(String londir) {
		this.londir = londir;
	}

	/**
	 * @return the speed
	 */
	public float getSpeed() {
		return speed;
	}

	/**
	 * @param speed the speed to set
	 */
	public void setSpeed(float speed) {
		this.speed = speed;
	}

	/**
	 * @return the gpsstatus
	 */
	public String getGpsstatus() {
		return gpsstatus;
	}

	/**
	 * @param gpsstatus the gpsstatus to set
	 */
	public void setGpsstatus(String gpsstatus) {
		this.gpsstatus = gpsstatus;
	}

	/**
	 * @return the gpsinfo
	 */
	public String getGpsinfo() {
		return gpsinfo;
	}

	/**
	 * @param gpsinfo the gpsinfo to set
	 */
	public void setGpsinfo(String gpsinfo) {
		this.gpsinfo = gpsinfo;
	}

	/**
	 * @return the gpsmode
	 */
	public int getGpsmode() {
		return gpsmode;
	}

	/**
	 * @param gpsmode the gpsmode to set
	 */
	public void setGpsmode(int gpsmode) {
		this.gpsmode = gpsmode;
	}

	/**
	 * @return the distance
	 */
	public float getDistance() {
		return distance;
	}

	/**
	 * @param distance the distance to set
	 */
	public void setDistance(float distance) {
		this.distance = distance;
	}

	/**
	 * @return the heading
	 */
	public String getHeading() {
		return heading;
	}

	/**
	 * @param heading the heading to set
	 */
	public void setHeading(String heading) {
		this.heading = heading;
	}

	/**
	 * @return the eventid
	 */
	public String getEventid() {
		return eventid;
	}

	/**
	 * @param eventid the eventid to set
	 */
	public void setEventid(String eventid) {
		this.eventid = eventid;
	}

	/**
	 * @return the nmeventid
	 */
	public String getNmeventid() {
		return nmeventid;
	}

	/**
	 * @param nmeventid the nmeventid to set
	 */
	public void setNmeventid(String nmeventid) {
		this.nmeventid = nmeventid;
	}

	/**
	 * @return the iostatus
	 */
	public int getIostatus() {
		return iostatus;
	}

	/**
	 * @param iostatus the iostatus to set
	 */
	public void setIostatus(int iostatus) {
		this.iostatus = iostatus;
	}

	/**
	 * @return the address
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * @param address the address to set
	 */
	public void setAddress(String address) {
		this.address = address;
	}

	/**
	 * @return the alive
	 */
	public boolean isAlive() {
		return alive;
	}

	/**
	 * @param alive the alive to set
	 */
	public void setAlive(boolean alive) {
		this.alive = alive;
	}

	/**
	 * @return the assetInfo
	 */
	public JAssetInformation getAssetInfo() {
		return assetInfo;
	}

	/**
	 * @param assetInfo the assetInfo to set
	 */
	public void setAssetInfo(JAssetInformation assetInfo) {
		this.assetInfo = assetInfo;
	}

	/**
	 * @return the lastvalidlat
	 */
	public double getLastvalidlat() {
		return lastvalidlat;
	}

	/**
	 * @param lastvalidlat the lastvalidlat to set
	 */
	public void setLastvalidlat(double lastvalidlat) {
		this.lastvalidlat = lastvalidlat;
	}

	/**
	 * @return the lastvalidlatdir
	 */
	public String getLastvalidlatdir() {
		return lastvalidlatdir;
	}

	/**
	 * @param lastvalidlatdir the lastvalidlatdir to set
	 */
	public void setLastvalidlatdir(String lastvalidlatdir) {
		this.lastvalidlatdir = lastvalidlatdir;
	}

	/**
	 * @return the lastvalidlon
	 */
	public double getLastvalidlon() {
		return lastvalidlon;
	}

	/**
	 * @param lastvalidlon the lastvalidlon to set
	 */
	public void setLastvalidlon(double lastvalidlon) {
		this.lastvalidlon = lastvalidlon;
	}

	/**
	 * @return the lastvalidlondir
	 */
	public String getLastvalidlondir() {
		return lastvalidlondir;
	}

	/**
	 * @param lastvalidlondir the lastvalidlondir to set
	 */
	public void setLastvalidlondir(String lastvalidlondir) {
		this.lastvalidlondir = lastvalidlondir;
	}

	/**
	 * @return the lastvaliddatetime
	 */
	public String getLastvaliddatetime() {
		return lastvaliddatetime;
	}

	/**
	 * @param lastvaliddatetime the lastvaliddatetime to set
	 */
	public void setLastvaliddatetime(String lastvaliddatetime) {
		this.lastvaliddatetime = lastvaliddatetime;
	}

	/**
	 * @return the cellidlat
	 */
	public double getCellidlat() {
		return cellidlat;
	}

	/**
	 * @param cellidlat the cellidlat to set
	 */
	public void setCellidlat(double cellidlat) {
		this.cellidlat = cellidlat;
	}

	/**
	 * @return the cellidlon
	 */
	public double getCellidlon() {
		return cellidlon;
	}

	/**
	 * @param cellidlon the cellidlon to set
	 */
	public void setCellidlon(double cellidlon) {
		this.cellidlon = cellidlon;
	}

	/**
	 * @return the cellidacc
	 */
	public float getCellidacc() {
		return cellidacc;
	}

	/**
	 * @param cellidacc the cellidacc to set
	 */
	public void setCellidacc(float cellidacc) {
		this.cellidacc = cellidacc;
	}

	/**
	 * @return the eventname
	 */
	public String getEventname() {
		return eventname;
	}

	/**
	 * @param eventname the eventname to set
	 */
	public void setEventname(String eventname) {
		this.eventname = eventname;
	}

	/**
	 * @return the lastvalidaddress
	 */
	public String getLastvalidaddress() {
		return lastvalidaddress;
	}

	/**
	 * @param lastvalidaddress the lastvalidaddress to set
	 */
	public void setLastvalidaddress(String lastvalidaddress) {
		this.lastvalidaddress = lastvalidaddress;
	}

	/**
	 * @return the rawrssi
	 */
	public int getRawrssi() {
		return rawrssi;
	}

	/**
	 * @param rawrssi the rawrssi to set
	 */
	public void setRawrssi(int rawrssi) {
		this.rawrssi = rawrssi;
	}

	/**
	 * @return the lat_d
	 */
	public double getLat_d() {
		return lat_d;
	}

	/**
	 * @param lat_d the lat_d to set
	 */
	public void setLat_d(double lat_d) {
		this.lat_d = lat_d;
	}

	/**
	 * @return the lon_d
	 */
	public double getLon_d() {
		return lon_d;
	}

	/**
	 * @param lon_d the lon_d to set
	 */
	public void setLon_d(double lon_d) {
		this.lon_d = lon_d;
	}

	/**
	 * @return the lastvalidlat_d
	 */
	public double getLastvalidlat_d() {
		return lastvalidlat_d;
	}

	/**
	 * @param lastvalidlat_d the lastvalidlat_d to set
	 */
	public void setLastvalidlat_d(double lastvalidlat_d) {
		this.lastvalidlat_d = lastvalidlat_d;
	}

	/**
	 * @return the lastvalidlon_d
	 */
	public double getLastvalidlon_d() {
		return lastvalidlon_d;
	}

	/**
	 * @param lastvalidlon_d the lastvalidlon_d to set
	 */
	public void setLastvalidlon_d(double lastvalidlon_d) {
		this.lastvalidlon_d = lastvalidlon_d;
	}

	/**
	 * @return the lastvalidtemp
	 */
	public float getLastvalidtemp() {
		return lastvalidtemp;
	}

	/**
	 * @param lastvalidtemp the lastvalidtemp to set
	 */
	public void setLastvalidtemp(float lastvalidtemp) {
		this.lastvalidtemp = lastvalidtemp;
	}

	/**
	 * @return the rpt_timezone
	 */
	public String getRpt_timezone() {
		return rpt_timezone;
	}

	/**
	 * @param rpt_timezone the rpt_timezone to set
	 */
	public void setRpt_timezone(String rpt_timezone) {
		this.rpt_timezone = rpt_timezone;
	}

	/**
	 * @return the heat_index
	 */
	public float getHeat_index() {
		return heat_index;
	}

	/**
	 * @param heat_index the heat_index to set
	 */
	public void setHeat_index(float heat_index) {
		this.heat_index = heat_index;
	}

	/**
	 * @return the enableLocation
	 */
	public boolean isEnableLocation() {
		return enableLocation;
	}

	/**
	 * @param enableLocation the enableLocation to set
	 */
	public void setEnableLocation(boolean enableLocation) {
		this.enableLocation = enableLocation;
	}

	public String getProbeType() {
		return probeType;
	}

	public void setProbeType(String probeType) {
		this.probeType = probeType;
	}

	public boolean isOnoffstatus() {
		return onoffstatus;
	}

	public void setOnoffstatus(boolean onoffstatus) {
		this.onoffstatus = onoffstatus;
	}

	public String getUpdatedon() {
		return updatedon;
	}

	public void setUpdatedon(String updatedon) {
		this.updatedon = updatedon;
	}

	public boolean isIs_upgrade() {
		return is_upgrade;
	}

	public void setIs_upgrade(boolean is_upgrade) {
		this.is_upgrade = is_upgrade;
	}

	public boolean isIshumidity() {
		return ishumidity;
	}

	public void setIshumidity(boolean ishumidity) {
		this.ishumidity = ishumidity;
	}

	public int getDefault_report() {
		return default_report;
	}

	public void setDefault_report(int default_report) {
		this.default_report = default_report;
	}

	public String getDefault_rpt_msg() {
		return default_rpt_msg;
	}

	public void setDefault_rpt_msg(String default_rpt_msg) {
		this.default_rpt_msg = default_rpt_msg;
	}

	public String getDefault_rpt_label() {
		return default_rpt_label;
	}

	public void setDefault_rpt_label(String default_rpt_label) {
		this.default_rpt_label = default_rpt_label;
	}

	public boolean isShow_battery() {
		return show_battery;
	}

	public void setShow_battery(boolean show_battery) {
		this.show_battery = show_battery;
	}

	public String getBat_info() {
		return bat_info;
	}

	public void setBat_info(String bat_info) {
		this.bat_info = bat_info;
	}

	public boolean isN13() {
		return isN13;
	}

	public void setN13(boolean isN13) {
		this.isN13 = isN13;
	}

	public boolean isShow_temp_video() {
		return show_temp_video;
	}

	public void setShow_temp_video(boolean show_temp_video) {
		this.show_temp_video = show_temp_video;
	}

	public boolean isShow_N13Poff() {
		return show_N13Poff;
	}

	public void setShow_N13Poff(boolean show_N13Poff) {
		this.show_N13Poff = show_N13Poff;
	}
	public int getAqi() {
		return aqi;
	}

	public void setAqi(int aqi) {
		this.aqi = aqi;
	}

	public int getVoc() {
		return voc;
	}

	public void setVoc(int voc) {
		this.voc = voc;
	}

	public int getCo2() {
		return co2;
	}

	public void setCo2(int co2) {
		this.co2 = co2;
	}	

	public long getPrev_wakeup_time() {
		return prev_wakeup_time;
	}

	public void setPrev_wakeup_time(long prev_wakeup_time) {
		this.prev_wakeup_time = prev_wakeup_time;
	}

	public String getCo2_info() {
		return co2_info;
	}

	public void setCo2_info(String co2_info) {
		this.co2_info = co2_info;
	}

	public boolean isShow_aqi() {
		return show_aqi;
	}

	public void setShow_aqi(boolean show_aqi) {
		this.show_aqi = show_aqi;
	}

	public String getAqi_info() {
		return aqi_info;
	}

	public void setAqi_info(String aqi_info) {
		this.aqi_info = aqi_info;
	}

	public String getAqi_desc() {
		return aqi_desc;
	}

	public void setAqi_desc(String aqi_desc) {
		this.aqi_desc = aqi_desc;
	}

	public String getVoc_info() {
		return voc_info;
	}

	public void setVoc_info(String voc_info) {
		this.voc_info = voc_info;
	}

	public String getDefault_rpt_msg_watch() {
		return default_rpt_msg_watch;
	}

	public void setDefault_rpt_msg_watch(String default_rpt_msg_watch) {
		this.default_rpt_msg_watch = default_rpt_msg_watch;
	}

	public String getDevice_type() {
		return device_type;
	}

	public void setDevice_type(String device_type) {
		this.device_type = device_type;
	}

	public Double getFoodCal() {
		return foodCal;
	}

	public void setFoodCal(Double foodCal) {
		this.foodCal = foodCal;
	}

	public float getFoodIntake() {
		return foodIntake;
	}

	public void setFoodIntake(float foodIntake) {
		this.foodIntake = foodIntake;
	}

	public Double getCaloriesConsumed() {
		return caloriesConsumed;
	}

	public void setCaloriesConsumed(Double caloriesConsumed) {
		this.caloriesConsumed = caloriesConsumed;
	}

	public Double getActualCalories() {
		return actualCalories;
	}

	public void setActualCalories(Double actualCalories) {
		this.actualCalories = actualCalories;
	}

	public int getBattery() {
		return battery;
	}

	public void setBattery(int battery) {
		this.battery = battery;
	}

	public int getWifiRange() {
		return wifiRange;
	}

	public void setWifiRange(int wifiRange) {
		this.wifiRange = wifiRange;
	}

	public long getProfileId() {
		return profileId;
	}

	public void setProfileId(long profileId) {
		this.profileId = profileId;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public List<BluetoothDeviceList> getBledevicelist() {
		return bledevicelist;
	}

	public void setBledevicelist(List<BluetoothDeviceList> bledevicelist) {
		this.bledevicelist = bledevicelist;
	}


}

