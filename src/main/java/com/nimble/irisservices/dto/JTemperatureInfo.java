package com.nimble.irisservices.dto;

public class JTemperatureInfo {

	private double temperature = 0.0;
	
	private double humidity = 0.0;
	
	private double heat_index = 0.0;
	
	private int min_value = 5;
	
	private int max_value = 32;
	
	private int min_monitor = 10;
	
	private int max_monitor = 45;
	
	private int  min_value_humidity = 40;
	
	private int  max_value_humidity = 60;
	
	private int  min_monitor_humidity = 1;
	
	private int  max_monitor_humidity = 100;
	
	private int  min_value_battery = 0;
	
	private int  max_value_battery = 80;
	
	private int  low_battery_value = 30;
	
	private boolean is_celcius = false;
	
    private String reported_time = "NA";//dd MMM yyyy, hh:mm a
    
    private int battery_percent = 0;
    
    private boolean all_alerts_disabled = false;
    
    private boolean is_fdnr = false;
    
    private boolean is_dnr = false;

	public double getTemperature() {
		return temperature;
	}

	public void setTemperature(double temperature) {
		this.temperature = temperature;
	}

	public double getHumidity() {
		return humidity;
	}

	public void setHumidity(double humidity) {
		this.humidity = humidity;
	}

	public double getHeat_index() {
		return heat_index;
	}

	public void setHeat_index(double heat_index) {
		this.heat_index = heat_index;
	}

	public int getMin_value() {
		return min_value;
	}

	public void setMin_value(int min_value) {
		this.min_value = min_value;
	}

	public int getMax_value() {
		return max_value;
	}

	public void setMax_value(int max_value) {
		this.max_value = max_value;
	}

	public int getMin_monitor() {
		return min_monitor;
	}

	public void setMin_monitor(int min_monitor) {
		this.min_monitor = min_monitor;
	}

	public int getMax_monitor() {
		return max_monitor;
	}

	public void setMax_monitor(int max_monitor) {
		this.max_monitor = max_monitor;
	}

	public boolean isIs_celcius() {
		return is_celcius;
	}

	public void setIs_celcius(boolean is_celcius) {
		this.is_celcius = is_celcius;
	}

	public String getReported_time() {
		return reported_time;
	}

	public void setReported_time(String reported_time) {
		this.reported_time = reported_time;
	}

	public int getBattery_percent() {
		return battery_percent;
	}

	public void setBattery_percent(int battery_percent) {
		this.battery_percent = battery_percent;
	}

	public boolean isAll_alerts_disabled() {
		return all_alerts_disabled;
	}

	public void setAll_alerts_disabled(boolean all_alerts_disabled) {
		this.all_alerts_disabled = all_alerts_disabled;
	}

	public int getMin_value_humidity() {
		return min_value_humidity;
	}

	public void setMin_value_humidity(int min_value_humidity) {
		this.min_value_humidity = min_value_humidity;
	}

	public int getMax_value_humidity() {
		return max_value_humidity;
	}

	public void setMax_value_humidity(int max_value_humidity) {
		this.max_value_humidity = max_value_humidity;
	}

	public int getMin_monitor_humidity() {
		return min_monitor_humidity;
	}

	public void setMin_monitor_humidity(int min_monitor_humidity) {
		this.min_monitor_humidity = min_monitor_humidity;
	}

	public int getMax_monitor_humidity() {
		return max_monitor_humidity;
	}

	public void setMax_monitor_humidity(int max_monitor_humidity) {
		this.max_monitor_humidity = max_monitor_humidity;
	}

	public int getMin_value_battery() {
		return min_value_battery;
	}

	public void setMin_value_battery(int min_value_battery) {
		this.min_value_battery = min_value_battery;
	}

	public int getMax_value_battery() {
		return max_value_battery;
	}

	public void setMax_value_battery(int max_value_battery) {
		this.max_value_battery = max_value_battery;
	}

	public int getLow_battery_value() {
		return low_battery_value;
	}

	public void setLow_battery_value(int low_battery_value) {
		this.low_battery_value = low_battery_value;
	}

	public boolean isIs_fdnr() {
		return is_fdnr;
	}

	public void setIs_fdnr(boolean is_fdnr) {
		this.is_fdnr = is_fdnr;
	}

	public boolean isIs_dnr() {
		return is_dnr;
	}

	public void setIs_dnr(boolean is_dnr) {
		this.is_dnr = is_dnr;
	}

	public JTemperatureInfo() {
		super();
	}
	
	public JTemperatureInfo(double temperature, double humidity, double heat_index, int min_value, int max_value,
			int min_monitor, int max_monitor, boolean is_celcius, String reported_time, int battery_percent,
			boolean all_alerts_disabled) {
		super();
		this.temperature = temperature;
		this.humidity = humidity;
		this.heat_index = heat_index;
		this.min_value = min_value;
		this.max_value = max_value;
		this.min_monitor = min_monitor;
		this.max_monitor = max_monitor;
		this.is_celcius = is_celcius;
		this.reported_time = reported_time;
		this.battery_percent = battery_percent;
		this.all_alerts_disabled = all_alerts_disabled;
	}
	
    
    
}

