package com.nimble.irisservices.dto;

public class PowerModeConfig {

	private long id;
	
	private String powermode;
	
	private String description;
	
	private int prptinterval;

	
	
	public PowerModeConfig() {
		super();
		// TODO Auto-generated constructor stub
	}

	public PowerModeConfig(long id, String powermode, String description, int prptinterval) {
		super();
		this.id = id;
		this.powermode = powermode;
		this.description = description;
		this.prptinterval = prptinterval;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getPowermode() {
		return powermode;
	}

	public void setPowermode(String powermode) {
		this.powermode = powermode;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public int getPrptinterval() {
		return prptinterval;
	}

	public void setPrptinterval(int prptinterval) {
		this.prptinterval = prptinterval;
	}
	
	
}
