package com.nimble.irisservices.dto;

public class Minicamshipping {

        private long user_id = 0;

        private String address1 = "NA";

        private String address2 = "NA";

        private String zipcode = "NA";

        private String city = "NA";

        private String state = "NA";

        private boolean isCancellation = false;

        private long gatewayId=0;

        private long monitortypeId=0;

        private String subId="NA";


    public boolean getIsCancellation() {
        return isCancellation;
    }

    public void setCancellation(boolean cancellation) {
        this.isCancellation = isCancellation;
    }

    public long getUser_id() {
        return user_id;
    }

    public void setUser_id(long user_id) {
        this.user_id = user_id;
    }

    public String getAddress1() {
        return address1;
    }

    public void setAddress1(String address1) {
        this.address1 = address1;
    }

    public String getAddress2() {
        return address2;
    }

    public void setAddress2(String address2) {
        this.address2 = address2;
    }

    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public long getGatewayId() {
        return gatewayId;
    }

    public void setGatewayId(long gatewayId) {
        this.gatewayId = gatewayId;
    }


    public long getMonitortypeId() {
        return monitortypeId;
    }

    public void setMonitortypeId(long monitortypeId) {
        this.monitortypeId = monitortypeId;
    }

    public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }
}
