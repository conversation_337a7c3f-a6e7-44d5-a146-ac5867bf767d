package com.nimble.irisservices.dto;

import java.io.Serializable;

public class sensorReportData implements Serializable {
	private String reportdatetime;
	private String status;
	private String sensor_name = "Sensor";
	private String color_code="#000000";
	private int curr_status = 1;
	
	
	public sensorReportData() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	public String getReportdatetime() {
		return reportdatetime;
	}
	public void setReportdatetime(String reportdatetime) {
		this.reportdatetime = reportdatetime;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}

	public String getSensor_name() {
		return sensor_name;
	}

	public void setSensor_name(String sensor_name) {
		this.sensor_name = sensor_name;
	}

	public String getColor_code() {
		return color_code;
	}

	public void setColor_code(String color_code) {
		this.color_code = color_code;
	}

	public int getCurr_status() {
		return curr_status;
	}

	public void setCurr_status(int curr_status) {
		this.curr_status = curr_status;
	}
	
}
