package com.nimble.irisservices.dto;

public class JGatewayToAlexa {

	
	private long user_id = 0;
	
	private long gateway_id = 0;
	
	private String meid = "NA";
	
	private String first_name = "NA";
	
	private String last_name = "NA";

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getFirst_name() {
		return first_name;
	}

	public void setFirst_name(String first_name) {
		this.first_name = first_name;
	}

	public String getLast_name() {
		return last_name;
	}

	public void setLast_name(String last_name) {
		this.last_name = last_name;
	}

	public JGatewayToAlexa() {
		super();
	}
	
	public JGatewayToAlexa(long user_id, long gateway_id, String meid, String first_name, String last_name) {
		super();
		this.user_id = user_id;
		this.gateway_id = gateway_id;
		this.meid = meid;
		this.first_name = first_name;
		this.last_name = last_name;
	}
	
	
	
}
