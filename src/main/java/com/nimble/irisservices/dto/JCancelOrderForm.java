package com.nimble.irisservices.dto;

public class JCancelOrderForm {

	
	private String email = "NA";
	
	private String name = "NA";
	
	private String order_id = "NA";
	
	private String cancel_reason = "NA";
	
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getOrder_id() {
		return order_id;
	}
	
	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}

	public String getCancel_reason() {
		return cancel_reason;
	}

	public void setCancel_reason(String cancel_reason) {
		this.cancel_reason = cancel_reason;
	}

	public JCancelOrderForm() {
		super();
	}
	
	public JCancelOrderForm(String email, String name, String order_id, String cancel_reason) {
		super();
		this.email = email;
		this.name = name;
		this.order_id = order_id;
		this.cancel_reason = cancel_reason;
	}
	
	public String printAllValues() {
		return "email :: "+this.email+" :: name : "+ this.name +" :: order_id : "+this.order_id;
	}
	
	
}
