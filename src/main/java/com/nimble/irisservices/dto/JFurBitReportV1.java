package com.nimble.irisservices.dto;

public class JFurBitReportV1 {

	private String fromDatetime;
	private String toDatetime;
	private long totalStepCount;
	private long totalIdleSecs;
	private long totalWalkSecs;
	private long totalRunSecs;
	private long totalActiveSecs;
	private long gatewayId;
	private String gatewayName;
	
	private int defaultStepGoal;
	private String goalAchievedPercentage;
	private String batterylife = "-1";
	private double caloriesburnt = 0; 
	private String caloriesAchievedPercentage;
	/**
	 * @return the fromDatetime
	 */
	public String getFromDatetime() {
		return fromDatetime;
	}
	/**
	 * @param fromDatetime the fromDatetime to set
	 */
	public void setFromDatetime(String fromDatetime) {
		this.fromDatetime = fromDatetime;
	}
	/**
	 * @return the toDatetime
	 */
	public String getToDatetime() {
		return toDatetime;
	}
	/**
	 * @param toDatetime the toDatetime to set
	 */
	public void setToDatetime(String toDatetime) {
		this.toDatetime = toDatetime;
	}
	/**
	 * @return the totalStepCount
	 */
	public long getTotalStepCount() {
		return totalStepCount;
	}
	/**
	 * @param totalStepCount the totalStepCount to set
	 */
	public void setTotalStepCount(long totalStepCount) {
		this.totalStepCount = totalStepCount;
	}
	/**
	 * @return the totalIdleSecs
	 */
	public long getTotalIdleSecs() {
		return totalIdleSecs;
	}
	/**
	 * @param totalIdleSecs the totalIdleSecs to set
	 */
	public void setTotalIdleSecs(long totalIdleSecs) {
		this.totalIdleSecs = totalIdleSecs;
	}
	/**
	 * @return the totalWalkSecs
	 */
	public long getTotalWalkSecs() {
		return totalWalkSecs;
	}
	/**
	 * @param totalWalkSecs the totalWalkSecs to set
	 */
	public void setTotalWalkSecs(long totalWalkSecs) {
		this.totalWalkSecs = totalWalkSecs;
	}
	/**
	 * @return the totalRunSecs
	 */
	public long getTotalRunSecs() {
		return totalRunSecs;
	}
	/**
	 * @param totalRunSecs the totalRunSecs to set
	 */
	public void setTotalRunSecs(long totalRunSecs) {
		this.totalRunSecs = totalRunSecs;
	}

	/**
	 * @return the gatewayId
	 */
	public long getGatewayId() {
		return gatewayId;
	}
	/**
	 * @param gatewayId the gatewayId to set
	 */
	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}
	/**
	 * @return the totalActiveSecs
	 */
	public long getTotalActiveSecs() {
		return totalActiveSecs;
	}
	/**
	 * @param totalActiveSecs the totalActiveSecs to set
	 */
	public void setTotalActiveSecs(long totalActiveSecs) {
		this.totalActiveSecs = totalActiveSecs;
	}
	/**
	 * @return the gatewayName
	 */
	public String getGatewayName() {
		return gatewayName;
	}
	/**
	 * @param gatewayName the gatewayName to set
	 */
	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}
	/**
	 * @return the goalAchievedPercentage
	 */
	public String getGoalAchievedPercentage() {
		return goalAchievedPercentage;
	}
	/**
	 * @param goalAchievedPercentage the goalAchievedPercentage to set
	 */
	public void setGoalAchievedPercentage(String goalAchievedPercentage) {
		this.goalAchievedPercentage = goalAchievedPercentage;
	}
	/**
	 * @return the defaultStepGoal
	 */
	public int getDefaultStepGoal() {
		return defaultStepGoal;
	}
	/**
	 * @param defaultStepGoal the defaultStepGoal to set
	 */	
	public void setDefaultStepGoal(int defaultStepGoal) {
		this.defaultStepGoal = defaultStepGoal;
	}
	
	public String getBatterylife() {
		return batterylife;
	}
	
	public void setBatterylife(String batterylife) {
		this.batterylife = batterylife;
	}
	public double getCaloriesburnt() {
		return caloriesburnt;
	}
	public void setCaloriesburnt(double caloriesburnt) {
		this.caloriesburnt = caloriesburnt;
	}
	
	public String getCaloriesAchievedPercentage() {
		return caloriesAchievedPercentage;
	}
	public void setCaloriesAchievedPercentage(String caloriesAchievedPercentage) {
		this.caloriesAchievedPercentage = caloriesAchievedPercentage;
	}
	
	
}

