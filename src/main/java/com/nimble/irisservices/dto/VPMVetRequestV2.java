package com.nimble.irisservices.dto;

public class VPMVetRequestV2 {
	private String api_key;
	private String first_name;
	private String last_name;
	private String email;
	private String phone_no;
	private String conversation_request = "chat";
	private String is_protect = "users";
	private String dev_type;
	private String your_registration_date="";
	private VPMPetDataV2 pet;
	
	public VPMVetRequestV2() {
	}

	public VPMVetRequestV2(String api_key, String first_name, String last_name, String email, String phone_no,
			String dev_type, String your_registration_date, VPMPetDataV2 pet) {
		super();
		this.api_key = api_key;
		this.first_name = first_name;
		this.last_name = last_name;
		this.email = email;
		this.phone_no = phone_no;
		this.dev_type = dev_type;
		this.your_registration_date = your_registration_date;
		this.pet = pet;
	}

	public String getApi_key() {
		return api_key;
	}

	public void setApi_key(String api_key) {
		this.api_key = api_key;
	}

	public String getFirst_name() {
		return first_name;
	}

	public void setFirst_name(String first_name) {
		this.first_name = first_name;
	}

	public String getLast_name() {
		return last_name;
	}

	public void setLast_name(String last_name) {
		this.last_name = last_name;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getPhone_no() {
		return phone_no;
	}

	public void setPhone_no(String phone_no) {
		this.phone_no = phone_no;
	}

	public String getConversation_request() {
		return conversation_request;
	}

	public void setConversation_request(String conversation_request) {
		this.conversation_request = conversation_request;
	}

	public String getIs_protect() {
		return is_protect;
	}

	public void setIs_protect(String is_protect) {
		this.is_protect = is_protect;
	}

	public String getDev_type() {
		return dev_type;
	}

	public void setDev_type(String dev_type) {
		this.dev_type = dev_type;
	}

	public String getYour_registration_date() {
		return your_registration_date;
	}

	public void setYour_registration_date(String your_registration_date) {
		this.your_registration_date = your_registration_date;
	}

	public VPMPetDataV2 getPet() {
		return pet;
	}

	public void setPet(VPMPetDataV2 pet) {
		this.pet = pet;
	}
	
}
