package com.nimble.irisservices.dto;

public class JUpdateRemoveGateway {

	/* Remove reason type id */
	private int remove_gateway_type_id = 0;
	
	private long gateway_id = 0;
	
	private String customer_review = "NA";
	
	long monitortype_id = 1;

	public int getRemove_gateway_type_id() {
		return remove_gateway_type_id;
	}

	public void setRemove_gateway_type_id(int remove_gateway_type_id) {
		this.remove_gateway_type_id = remove_gateway_type_id;
	}

	public String getCustomer_review() {
		return customer_review;
	}

	public void setCustomer_review(String customer_review) {
		this.customer_review = customer_review;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}	

	public long getMonitortype_id() {
		return monitortype_id;
	}

	public void setMonitortype_id(long monitortype_id) {
		this.monitortype_id = monitortype_id;
	}

	public JUpdateRemoveGateway() {
		super();
	}

	public JUpdateRemoveGateway(int remove_gateway_type_id, long gateway_id, String customer_review,
			long monitortype_id) {
		super();
		this.remove_gateway_type_id = remove_gateway_type_id;
		this.gateway_id = gateway_id;
		this.customer_review = customer_review;
		this.monitortype_id = monitortype_id;
	}

	
}
