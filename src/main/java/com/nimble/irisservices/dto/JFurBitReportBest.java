package com.nimble.irisservices.dto;

public class JFurBitReportBest {
	
	
	private long bestIdleSecs= 0;
	private long bestRunSecs =0;
	private long beststepcount =0;
	private long bestWalkSecs =0;
	private long bestActiveSecs = 0;
	private long bestCaloriesBurnt=0;
	
	private long gatewayId;
	private String gatewayName;
	
	private String bestIdlefromDatetime="1753-01-01 00:00:00";
	private String bestIdletoDatetime="1753-01-01 00:00:00";
	
	private String bestRunfromDatetime="1753-01-01 00:00:00";
	private String bestRuntoDatetime="1753-01-01 00:00:00";
	
	private String beststepfromDatetime="1753-01-01 00:00:00";
	private String beststeptoDatetime="1753-01-01 00:00:00";
	
	private String bestWalkfromDatetime="1753-01-01 00:00:00";
	private String bestWalktoDatetime="1753-01-01 00:00:00";
	
	private String bestActivefromDatetime="1753-01-01 00:00:00";
	private String bestActivetoDatetime="1753-01-01 00:00:00";
	
	private String bestCaloriesBurntfromDatetime="1753-01-01 00:00:00";
	private String bestCaloriesBurnttoDatetime="1753-01-01 00:00:00";
	
	public String getBestIdlefromDatetime() {
		return bestIdlefromDatetime;
	}
	public void setBestIdlefromDatetime(String bestIdlefromDatetime) {
		this.bestIdlefromDatetime = bestIdlefromDatetime;
	}
	public String getBestIdletoDatetime() {
		return bestIdletoDatetime;
	}
	public void setBestIdletoDatetime(String bestIdletoDatetime) {
		this.bestIdletoDatetime = bestIdletoDatetime;
	}
	public String getBestRunfromDatetime() {
		return bestRunfromDatetime;
	}
	public void setBestRunfromDatetime(String bestRunfromDatetime) {
		this.bestRunfromDatetime = bestRunfromDatetime;
	}
	public String getBestRuntoDatetime() {
		return bestRuntoDatetime;
	}
	public void setBestRuntoDatetime(String bestRuntoDatetime) {
		this.bestRuntoDatetime = bestRuntoDatetime;
	}
	public String getBeststepfromDatetime() {
		return beststepfromDatetime;
	}
	public void setBeststepfromDatetime(String beststepfromDatetime) {
		this.beststepfromDatetime = beststepfromDatetime;
	}
	public String getBeststeptoDatetime() {
		return beststeptoDatetime;
	}
	public void setBeststeptoDatetime(String beststeptoDatetime) {
		this.beststeptoDatetime = beststeptoDatetime;
	}
	public String getBestWalkfromDatetime() {
		return bestWalkfromDatetime;
	}
	public void setBestWalkfromDatetime(String bestWalkfromDatetime) {
		this.bestWalkfromDatetime = bestWalkfromDatetime;
	}
	public String getBestWalktoDatetime() {
		return bestWalktoDatetime;
	}
	public void setBestWalktoDatetime(String bestWalktoDatetime) {
		this.bestWalktoDatetime = bestWalktoDatetime;
	}
	public String getBestActivefromDatetime() {
		return bestActivefromDatetime;
	}
	public void setBestActivefromDatetime(String bestActivefromDatetime) {
		this.bestActivefromDatetime = bestActivefromDatetime;
	}
	public String getBestActivetoDatetime() {
		return bestActivetoDatetime;
	}
	public void setBestActivetoDatetime(String bestActivetoDatetime) {
		this.bestActivetoDatetime = bestActivetoDatetime;
	}
	public long getBestIdleSecs() {
		return bestIdleSecs;
	}
	public void setBestIdleSecs(long bestIdleSecs) {
		this.bestIdleSecs = bestIdleSecs;
	}
	public long getBestRunSecs() {
		return bestRunSecs;
	}
	public void setBestRunSecs(long bestRunSecs) {
		this.bestRunSecs = bestRunSecs;
	}
	public long getBeststepcount() {
		return beststepcount;
	}
	public void setBeststepcount(long beststepcount) {
		this.beststepcount = beststepcount;
	}
	public long getBestWalkSecs() {
		return bestWalkSecs;
	}
	public void setBestWalkSecs(long bestWalkSecs) {
		this.bestWalkSecs = bestWalkSecs;
	}
	public long getGatewayId() {
		return gatewayId;
	}
	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}
	public String getGatewayName() {
		return gatewayName;
	}
	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}
	public long getBestActiveSecs() {
		return bestActiveSecs;
	}
	public void setBestActiveSecs(long bestActiveSecs) {
		this.bestActiveSecs = bestActiveSecs;
	}
	public long getBestCaloriesBurnt() {
		return bestCaloriesBurnt;
	}
	public void setBestCaloriesBurnt(long bestCaloriesBurnt) {
		this.bestCaloriesBurnt = bestCaloriesBurnt;
	}
	public String getBestCaloriesBurntfromDatetime() {
		return bestCaloriesBurntfromDatetime;
	}
	public void setBestCaloriesBurntfromDatetime(String bestCaloriesBurntfromDatetime) {
		this.bestCaloriesBurntfromDatetime = bestCaloriesBurntfromDatetime;
	}
	public String getBestCaloriesBurnttoDatetime() {
		return bestCaloriesBurnttoDatetime;
	}
	public void setBestCaloriesBurnttoDatetime(String bestCaloriesBurnttoDatetime) {
		this.bestCaloriesBurnttoDatetime = bestCaloriesBurnttoDatetime;
	}


}
