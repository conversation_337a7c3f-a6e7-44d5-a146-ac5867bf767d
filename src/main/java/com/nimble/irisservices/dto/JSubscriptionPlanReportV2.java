package com.nimble.irisservices.dto;

import java.io.Serializable;

public class JSubscriptionPlanReportV2 implements Serializable {

	private static final long serialVersionUID = 0l;

	String cbPlanId;
	String cbPlanName;
	String period;
	String nextrenew_date;
	int days_remaining;
	String status;
	String autoRenewalStatus;
	String cbSubId;
	String chargebeeid;
	boolean freetrial = false;
	boolean freePlan = false;
	boolean show_cancel_sub = false;
	boolean payment_due = false;
	String price;

	public JSubscriptionPlanReportV2() {
		super();
	}

	public JSubscriptionPlanReportV2(String planid, String planname,  String period, String nextrenew_date,
			int days_remaining, String status, String autoRenewalStatus, String cbSubId, 
			String chargebeeid, boolean freetrial, boolean show_cancel_sub, boolean freePlan, boolean payment_due,String price) {
		super();
		this.cbPlanId = planid;
		this.cbPlanName = planname;
		this.period = period;
		this.nextrenew_date = nextrenew_date;
		this.days_remaining = days_remaining;
		this.status = status;
		this.autoRenewalStatus = autoRenewalStatus;
		this.cbSubId = cbSubId;
		this.chargebeeid = chargebeeid;
		this.freetrial = freetrial;
		this.show_cancel_sub = show_cancel_sub;
		this.freePlan = freePlan;
		this.payment_due = payment_due;
		this.price = price;
	}

	public String getCbPlanId() {
		return cbPlanId;
	}

	public void setCbPlanId(String cbPlanId) {
		this.cbPlanId = cbPlanId;
	}

	public String getCbPlanName() {
		return cbPlanName;
	}

	public void setCbPlanName(String cbPlanName) {
		this.cbPlanName = cbPlanName;
	}

	public String getPeriod() {
		return period;
	}

	public void setPeriod(String period) {
		this.period = period;
	}

	public String getNextrenew_date() {
		return nextrenew_date;
	}

	public void setNextrenew_date(String nextrenew_date) {
		this.nextrenew_date = nextrenew_date;
	}

	public int getDays_remaining() {
		return days_remaining;
	}

	public void setDays_remaining(int days_remaining) {
		this.days_remaining = days_remaining;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getCbSubId() {
		return cbSubId;
	}

	public void setCbSubId(String cbSubId) {
		this.cbSubId = cbSubId;
	}

	public String getChargebeeid() {
		return chargebeeid;
	}

	public void setChargebeeid(String chargebeeid) {
		this.chargebeeid = chargebeeid;
	}

	public boolean isFreetrial() {
		return freetrial;
	}

	public void setFreetrial(boolean freetrial) {
		this.freetrial = freetrial;
	}

	public boolean isShow_cancel_sub() {
		return show_cancel_sub;
	}

	public void setShow_cancel_sub(boolean show_cancel_sub) {
		this.show_cancel_sub = show_cancel_sub;
	}

	public String getAutoRenewalStatus() {
		return autoRenewalStatus;
	}

	public void setAutoRenewalStatus(String autoRenewalStatus) {
		this.autoRenewalStatus = autoRenewalStatus;
	}

	public boolean isFreePlan() {
		return freePlan;
	}

	public void setFreePlan(boolean freePlan) {
		this.freePlan = freePlan;
	}

	public boolean isPayment_due() {
		return payment_due;
	}

	public void setPayment_due(boolean payment_due) {
		this.payment_due = payment_due;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

}
