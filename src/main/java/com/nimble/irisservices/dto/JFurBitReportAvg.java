package com.nimble.irisservices.dto;

public class JFurBitReportAvg {
	
	private long AvgIdleSecs= 0;
	private long AvgRunSecs =0;
	private long Avgstepcount =0;
	private long AvgWalkSecs =0;
	private long AvgActivesec=0;
	private long AvgCaloriesBurnt=0;
	
	private int totalpacket=0;
	
	private long gatewayId;
	private String gatewayName;
	
	public long getAvgActivesec() {
		return AvgActivesec;
	}
	public void setAvgActivesec(long avgActivesec) {
		AvgActivesec = avgActivesec;
	}
	public int getTotalpacket() {
		return totalpacket;
	}
	public void setTotalpacket(int totalpacket) {
		this.totalpacket = totalpacket;
	}
	public long getGatewayId() {
		return gatewayId;
	}
	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}
	public String getGatewayName() {
		return gatewayName;
	}
	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}
	public long getAvgIdleSecs() {
		return AvgIdleSecs;
	}
	public void setAvgIdleSecs(long avgIdleSecs) {
		AvgIdleSecs = avgIdleSecs;
	}
	public long getAvgRunSecs() {
		return AvgRunSecs;
	}
	public void setAvgRunSecs(long avgRunSecs) {
		AvgRunSecs = avgRunSecs;
	}
	public long getAvgstepcount() {
		return Avgstepcount;
	}
	public void setAvgstepcount(long avgstepcount) {
		Avgstepcount = avgstepcount;
	}
	public long getAvgWalkSecs() {
		return AvgWalkSecs;
	}
	public void setAvgWalkSecs(long avgWalkSecs) {
		AvgWalkSecs = avgWalkSecs;
	}
	public long getAvgCaloriesBurnt() {
		return AvgCaloriesBurnt;
	}
	public void setAvgCaloriesBurnt(long avgCaloriesBurnt) {
		AvgCaloriesBurnt = avgCaloriesBurnt;
	}

}
