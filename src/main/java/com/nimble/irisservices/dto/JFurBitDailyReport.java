package com.nimble.irisservices.dto;

public class JFurBitDailyReport {

	private String fromDatetime;
	private String toDatetime;
	private long totalStepCount;
	private long totalIdleSecs;
	private long totalWalkSecs;
	private long totalRunSecs;
	private long runStepCount;
	private long walkStepCount;
	private long gatewayId;
	private long totalActiveSecs;
	
	/**
	 * @return the fromDatetime
	 */
	public String getFromDatetime() {
		return fromDatetime;
	}
	/**
	 * @param fromDatetime the fromDatetime to set
	 */
	public void setFromDatetime(String fromDatetime) {
		this.fromDatetime = fromDatetime;
	}
	/**
	 * @return the toDatetime
	 */
	public String getToDatetime() {
		return toDatetime;
	}
	/**
	 * @param toDatetime the toDatetime to set
	 */
	public void setToDatetime(String toDatetime) {
		this.toDatetime = toDatetime;
	}
	/**
	 * @return the totalStepCount
	 */
	public long getTotalStepCount() {
		return totalStepCount;
	}
	/**
	 * @param totalStepCount the totalStepCount to set
	 */
	public void setTotalStepCount(long totalStepCount) {
		this.totalStepCount = totalStepCount;
	}
	/**
	 * @return the totalIdleSecs
	 */
	public long getTotalIdleSecs() {
		return totalIdleSecs;
	}
	/**
	 * @param totalIdleSecs the totalIdleSecs to set
	 */
	public void setTotalIdleSecs(long totalIdleSecs) {
		this.totalIdleSecs = totalIdleSecs;
	}
	/**
	 * @return the totalWalkSecs
	 */
	public long getTotalWalkSecs() {
		return totalWalkSecs;
	}
	/**
	 * @param totalWalkSecs the totalWalkSecs to set
	 */
	public void setTotalWalkSecs(long totalWalkSecs) {
		this.totalWalkSecs = totalWalkSecs;
	}
	/**
	 * @return the totalRunSecs
	 */
	public long getTotalRunSecs() {
		return totalRunSecs;
	}
	/**
	 * @param totalRunSecs the totalRunSecs to set
	 */
	public void setTotalRunSecs(long totalRunSecs) {
		this.totalRunSecs = totalRunSecs;
	}
	/**
	 * @return the runStepCount
	 */
	public long getRunStepCount() {
		return runStepCount;
	}
	/**
	 * @param runStepCount the runStepCount to set
	 */
	public void setRunStepCount(long runStepCount) {
		this.runStepCount = runStepCount;
	}
	/**
	 * @return the walkStepCount
	 */
	public long getWalkStepCount() {
		return walkStepCount;
	}
	/**
	 * @param walkStepCount the walkStepCount to set
	 */
	public void setWalkStepCount(long walkStepCount) {
		this.walkStepCount = walkStepCount;
	}
	/**
	 * @return the gatewayId
	 */
	public long getGatewayId() {
		return gatewayId;
	}
	/**
	 * @param gatewayId the gatewayId to set
	 */
	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}
	/**
	 * @return the totalActiveSecs
	 */
	public long getTotalActiveSecs() {
		return totalActiveSecs;
	}
	/**
	 * @param totalActiveSecs the totalActiveSecs to set
	 */
	public void setTotalActiveSecs(long totalActiveSecs) {
		this.totalActiveSecs = totalActiveSecs;
	}
	
}
