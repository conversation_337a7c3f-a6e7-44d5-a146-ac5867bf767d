package com.nimble.irisservices.dto;

public class NodeSensorSetting {
	
	private long gateway_id = 0;
	
	private String updated_for = "NA";
	
	private String updated_value1 = "NA";
	
	private String updated_value2 = "NA";

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public String getUpdated_for() {
		return updated_for;
	}

	public void setUpdated_for(String updated_for) {
		this.updated_for = updated_for;
	}

	public String getUpdated_value1() {
		return updated_value1;
	}

	public void setUpdated_value1(String updated_value1) {
		this.updated_value1 = updated_value1;
	}

	public String getUpdated_value2() {
		return updated_value2;
	}

	public void setUpdated_value2(String updated_value2) {
		this.updated_value2 = updated_value2;
	}
	
}
