package com.nimble.irisservices.dto;

import java.util.ArrayList;

import com.nimble.irisservices.dto.JTerms.Terms;

public class JVetPlanDetails {

	public long plan_id = 0;
	private String plan_name = "";
	ArrayList<JMenuLst> plans = new ArrayList<JMenuLst>();
	ArrayList<JVetMenuLst> cover = new ArrayList<JVetMenuLst>();
	ArrayList<JVetMenuLst> don_cover = new ArrayList<JVetMenuLst>();
	ArrayList<Terms> term_list = new ArrayList<Terms>();

	public long getPlan_id() {
		return plan_id;
	}
	public void setPlan_id(long plan_id) {
		this.plan_id = plan_id;
	}
	public String getPlan_name() {
		return plan_name;
	}
	public void setPlan_name(String plan_name) {
		this.plan_name = plan_name;
	}
	public ArrayList<JMenuLst> getPlans() {
		return plans;
	}
	public void setPlans(ArrayList<JMenuLst> plans) {
		this.plans = plans;
	}
	public ArrayList<JVetMenuLst> getCover() {
		return cover;
	}
	public void setCover(ArrayList<JVetMenuLst> cover) {
		this.cover = cover;
	}
	public ArrayList<JVetMenuLst> getDon_cover() {
		return don_cover;
	}
	public void setDon_cover(ArrayList<JVetMenuLst> don_cover) {
		this.don_cover = don_cover;
	}
	public ArrayList<Terms> getTerm_list() {
		return term_list;
	}
	public void setTerm_list(ArrayList<Terms> term_list) {
		this.term_list = term_list;
	}

}
