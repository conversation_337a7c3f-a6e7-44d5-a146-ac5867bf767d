package com.nimble.irisservices.dto;

public class JFurbitAlertCfgRequest {

	private String alertcfgids ;
	private float minval 	= -70.0f;
	private float maxval 	= 170.0f;
	private int notifyfreq 	= 86400;
	private String mobilenos = "";
	private String emailids = "";
    private long assetid;
    private String alerttypeids;
    private String updatefor="";
    private boolean enable;
    private String os;
    
	public JFurbitAlertCfgRequest() {
		super();
		// TODO Auto-generated constructor stub
	}
	public String getAlertcfgids() {
		return alertcfgids;
	}
	public void setAlertcfgids(String alertcfgids) {
		this.alertcfgids = alertcfgids;
	}
	public float getMinval() {
		return minval;
	}
	public void setMinval(float minval) {
		this.minval = minval;
	}
	public float getMaxval() {
		return maxval;
	}
	public void setMaxval(float maxval) {
		this.maxval = maxval;
	}
	public int getNotifyfreq() {
		return notifyfreq;
	}
	public void setNotifyfreq(int notifyfreq) {
		this.notifyfreq = notifyfreq;
	}
	public String getMobilenos() {
		return mobilenos;
	}
	public void setMobilenos(String mobilenos) {
		this.mobilenos = mobilenos;
	}
	public String getEmailids() {
		return emailids;
	}
	public void setEmailids(String emailids) {
		this.emailids = emailids;
	}
	public long getAssetid() {
		return assetid;
	}
	public void setAssetid(long assetid) {
		this.assetid = assetid;
	}
	public String getAlerttypeids() {
		return alerttypeids;
	}
	public void setAlerttypeids(String alerttypeids) {
		this.alerttypeids = alerttypeids;
	}
	public String getUpdatefor() {
		return updatefor;
	}
	public void setUpdatefor(String updatefor) {
		this.updatefor = updatefor;
	}
	public boolean isEnable() {
		return enable;
	}
	public void setEnable(boolean enable) {
		this.enable = enable;
	}
	public String getOs() {
		return os;
	}
	public void setOs(String os) {
		this.os = os;
	}
    
    
}
