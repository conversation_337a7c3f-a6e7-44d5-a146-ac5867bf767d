package com.nimble.irisservices.dto;

public class JPetprofileWatch {

	private String imageurl = "NA";

	private long gatewayId = 0;

	private String gatewayName = "";

	public JPetprofileWatch() {
		super();
	}

	public JPetprofileWatch(long gatewayId, String gatewayName, String imageurl) {
		super();
		this.imageurl = imageurl;
		this.gatewayId = gatewayId;
		this.gatewayName = gatewayName;
	}

	public String getImageurl() {
		return imageurl;
	}

	public void setImageurl(String imageurl) {
		this.imageurl = imageurl;
	}

	public long getGatewayId() {
		return gatewayId;
	}

	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}

	public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

}
