package com.nimble.irisservices.dto;

public class AmazonUserReview {

	private long user_id;

	private int rateus_count;
	
	private String comments;
	
	private boolean app_rateus;
	
	private boolean amazon_rateus;
	
	private String pagename ="";
	
	private boolean skip = false;
	
	private int skip_count = 3;
	
	private String updatedon ="NA";
	
	public AmazonUserReview() {
	}

	public AmazonUserReview(long user_id,int rateus_count,String comments, boolean app_rateus,boolean amazon_rateus,String pagename) {
		this.user_id = user_id;
		this.rateus_count = rateus_count;
		this.comments = comments;
		this.app_rateus = app_rateus;
		this.amazon_rateus = amazon_rateus;
	}
	
	public AmazonUserReview(long user_id,int rateus_count,String comments, boolean app_rateus,boolean amazon_rateus,String pagename, int skip_count, String updatedon) {
		this.user_id = user_id;
		this.rateus_count = rateus_count;
		this.comments = comments;
		this.app_rateus = app_rateus;
		this.amazon_rateus = amazon_rateus;
		this.skip_count = skip_count;
		this.updatedon = updatedon;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public int getRateus_count() {
		return rateus_count;
	}

	public void setRateus_count(int rateus_count) {
		this.rateus_count = rateus_count;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public boolean isApp_rateus() {
		return app_rateus;
	}

	public void setApp_rateus(boolean app_rateus) {
		this.app_rateus = app_rateus;
	}

	public boolean isAmazon_rateus() {
		return amazon_rateus;
	}

	public void setAmazon_rateus(boolean amazon_rateus) {
		this.amazon_rateus = amazon_rateus;
	}

	public String getPagename() {
		return pagename;
	}

	public void setPagename(String pagename) {
		this.pagename = pagename;
	}

	public boolean isSkip() {
		return skip;
	}

	public void setSkip(boolean skip) {
		this.skip = skip;
	}

	public int getSkip_count() {
		return skip_count;
	}

	public void setSkip_count(int skip_count) {
		this.skip_count = skip_count;
	}

	public String getUpdatedon() {
		return updatedon;
	}

	public void setUpdatedon(String updatedon) {
		this.updatedon = updatedon;
	}
	
	
}
