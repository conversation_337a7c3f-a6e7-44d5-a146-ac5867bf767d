package com.nimble.irisservices.dto;

import java.util.ArrayList;

public class JAlertRemaining {

	private long alerttype_id;
	private String alertname;
	private int alerttype_total = 0;
	private int alerttype_remaining = 0;
	private ArrayList<JATCount> atCountList ;
	
	public JAlertRemaining(long alerttype_id, String alertname) {
		super();
		this.alerttype_id = alerttype_id;
		this.alertname = alertname;
	}
	
	public long getAlerttype_id() {
		return alerttype_id;
	}
	
	public void setAlerttype_id(long alerttype_id) {
		this.alerttype_id = alerttype_id;
	}
	
	public String getAlertname() {
		return alertname;
	}
	
	public void setAlertname(String alertname) {
		this.alertname = alertname;
	}

	public ArrayList<JATCount> getAtCountList() {
		return atCountList;
	}

	public void setAtCountList(ArrayList<JATCount> atCountList) {
		this.atCountList = atCountList;
	}

	public int getAlerttype_total() {
		return alerttype_total;
	}

	public void setAlerttype_total(int alerttype_total) {
		this.alerttype_total = alerttype_total;
	}

	public int getAlerttype_remaining() {
		return alerttype_remaining;
	}

	public void setAlerttype_remaining(int alerttype_remaining) {
		this.alerttype_remaining = alerttype_remaining;
	}	
}
