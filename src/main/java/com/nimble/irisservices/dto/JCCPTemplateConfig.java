package com.nimble.irisservices.dto;

import com.nimble.irisservices.entity.User;

public class JCCPTemplateConfig {
	
	private long 		templateId;
	private User        user;
	
	private String 		name;
	private boolean 	bTemplateEnable;
	private boolean 	bFreqEnable;
	private String 		startTime1;
	private String 		endTime1;
	private String 		startTime2;
	private String 		endTime2;
	private String 		startTime3;
	private String 		endTime3;
	private String 		startTime4;
	private String 		endTime4;
	
	public JCCPTemplateConfig(){
		super();
	}
	
	public JCCPTemplateConfig(long id,String name,boolean templEnable,boolean freqEnable,
								String startTime1,String endTime1,String startTime2,String endTime2,
								String startTime3,String endTime3,String startTime4,String endTime4,
								User user){
		super();
		this.setTemplateId(id);
		this.setName(name);
		this.setbTemplateEnable(templEnable);
		this.setbFreqEnable(freqEnable);
		this.setStartTime1(startTime1);
		this.setEndTime1(endTime1);
		this.setStartTime2(startTime2);
		this.setEndTime2(endTime2);
		this.setStartTime3(startTime3);
		this.setEndTime3(endTime3);
		this.setStartTime4(startTime4);		
		this.setEndTime4(endTime4);
		this.user = user;
		
	}

	

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public boolean isbTemplateEnable() {
		return bTemplateEnable;
	}

	public void setbTemplateEnable(boolean bTemplateEnable) {
		this.bTemplateEnable = bTemplateEnable;
	}

	public boolean isbFreqEnable() {
		return bFreqEnable;
	}

	public void setbFreqEnable(boolean bFreqEnable) {
		this.bFreqEnable = bFreqEnable;
	}

	public String getStartTime1() {
		return startTime1;
	}

	public void setStartTime1(String startTime1) {
		this.startTime1 = startTime1;
	}

	public String getEndTime1() {
		return endTime1;
	}

	public void setEndTime1(String endTime1) {
		this.endTime1 = endTime1;
	}

	public String getStartTime2() {
		return startTime2;
	}

	public void setStartTime2(String startTime2) {
		this.startTime2 = startTime2;
	}

	public String getEndTime2() {
		return endTime2;
	}

	public void setEndTime2(String endTime2) {
		this.endTime2 = endTime2;
	}

	public String getStartTime3() {
		return startTime3;
	}

	public void setStartTime3(String startTime3) {
		this.startTime3 = startTime3;
	}

	public String getEndTime3() {
		return endTime3;
	}

	public void setEndTime3(String endTime3) {
		this.endTime3 = endTime3;
	}

	public String getStartTime4() {
		return startTime4;
	}

	public void setStartTime4(String startTime4) {
		this.startTime4 = startTime4;
	}

	public String getEndTime4() {
		return endTime4;
	}

	public void setEndTime4(String endTime4) {
		this.endTime4 = endTime4;
	}

	public User getUser() {
		return user;
	}

	public void setUserid(User user) {
		this.user = user;
	}

	public long getTemplateId() {
		return templateId;
	}

	public void setTemplateId(long templateId) {
		this.templateId = templateId;
	}
	

}
