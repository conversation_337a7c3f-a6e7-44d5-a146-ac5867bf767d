package com.nimble.irisservices.dto;

public class JProductWithSubCategory {

    private int monitortype = 0;

    private String product_name = "NA";

    private String img_url = "NA";

    public void setMonitortype(int monitortype) {
		this.monitortype = monitortype;
	}

	public void setProduct_name(String product_name) {
		this.product_name = product_name;
	}

	public void setImg_url(String img_url) {
		this.img_url = img_url;
	}

	public int getMonitortype() {
		return monitortype;
	}

	public String getProduct_name() {
		return product_name;

	}

	public String getImg_url() {
		return img_url;
	}

	public JProductWithSubCategory(int monitortype, String product_name, String img_url) {
		super();
		this.monitortype = monitortype;
		this.product_name = product_name;
		this.img_url = img_url;
	}
}
