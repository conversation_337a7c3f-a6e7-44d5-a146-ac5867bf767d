package com.nimble.irisservices.dto;

public class JRegisterDevice {

	private String first_name = "NA";
	
	private String last_name = "NA";
	
	private String pet_name = "NA";
	
	private String email = "NA";
	
	private boolean already_user = false;
	
	private String country = "NA";
	
	private String mobile_no = "NA";
	
	private String purchased_from = "NA";
	
	private String remarks = "NA";
	
	private String password = "NA";
	
	private boolean show_order_id = false;
	
	private String order_id = "NA";
	
	private String qrc = "NA";

	private boolean check_recall_qrc = false;
	
	private double lat = 270.0;
	
	private double lon = 270.0;
	
	private long monitor_type = 0;
	
	private boolean device_upgrade = false;
	
	private long upgrade_gateway_id = 0;
	
	private boolean order_id_match = false; 
	
	private boolean order_id_match_error = false;
	
	private int sensor_type_id = 0;
	
	private int sensor_location_type_id = 0;
	
	private long order_channel = 0;

	private boolean iswithoutsub = false;

	private boolean isnewverdevice = false;

	public String getFirst_name() {
		return first_name;
	}

	public void setFirst_name(String first_name) {
		this.first_name = first_name;
	}

	public String getLast_name() {
		return last_name;
	}

	public void setLast_name(String last_name) {
		this.last_name = last_name;
	}

	public String getPet_name() {
		return pet_name;
	}

	public void setPet_name(String pet_name) {
		this.pet_name = pet_name;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public boolean isAlready_user() {
		return already_user;
	}

	public void setAlready_user(boolean already_user) {
		this.already_user = already_user;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getMobile_no() {
		return mobile_no;
	}

	public void setMobile_no(String mobile_no) {
		this.mobile_no = mobile_no;
	}

	public String getPurchased_from() {
		return purchased_from;
	}

	public void setPurchased_from(String purchased_from) {
		this.purchased_from = purchased_from;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public boolean isShow_order_id() {
		return show_order_id;
	}

	public void setShow_order_id(boolean show_order_id) {
		this.show_order_id = show_order_id;
	}

	public String getOrder_id() {
		return order_id;
	}

	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}

	public String getQrc() {
		return qrc;
	}

	public void setQrc(String qrc) {
		this.qrc = qrc;
	}

	public boolean isCheck_recall_qrc() {
		return check_recall_qrc;
	}

	public void setCheck_recall_qrc(boolean check_recall_qrc) {
		this.check_recall_qrc = check_recall_qrc;
	}

	public double getLat() {
		return lat;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public double getLon() {
		return lon;
	}

	public void setLon(double lon) {
		this.lon = lon;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public long getMonitor_type() {
		return monitor_type;
	}

	public void setMonitor_type(long monitor_type) {
		this.monitor_type = monitor_type;
	}

	public boolean isDevice_upgrade() {
		return device_upgrade;
	}

	public void setDevice_upgrade(boolean device_upgrade) {
		this.device_upgrade = device_upgrade;
	}

	public long getUpgrade_gateway_id() {
		return upgrade_gateway_id;
	}

	public void setUpgrade_gateway_id(long upgrade_gateway_id) {
		this.upgrade_gateway_id = upgrade_gateway_id;
	}

	public boolean isOrder_id_match() {
		return order_id_match;
	}

	public void setOrder_id_match(boolean order_id_match) {
		this.order_id_match = order_id_match;
	}

	public boolean isOrder_id_match_error() {
		return order_id_match_error;
	}

	public void setOrder_id_match_error(boolean order_id_match_error) {
		this.order_id_match_error = order_id_match_error;
	}

	public int getSensor_type_id() {
		return sensor_type_id;
	}

	public void setSensor_type_id(int sensor_type_id) {
		this.sensor_type_id = sensor_type_id;
	}

	public int getSensor_location_type_id() {
		return sensor_location_type_id;
	}

	public void setSensor_location_type_id(int sensor_location_type_id) {
		this.sensor_location_type_id = sensor_location_type_id;
	}

	public long getOrder_channel() {
		return order_channel;
	}

	public void setOrder_channel(long order_channel) {
		this.order_channel = order_channel;
	}

	public JRegisterDevice() {
		super();
	}

	public boolean isIswithoutsub() {
		return iswithoutsub;
	}

	public void setIswithoutsub(boolean iswithoutsub) {
		this.iswithoutsub = iswithoutsub;
	}

	public boolean isIsnewverdevice() {
		return isnewverdevice;
	}

	public void setIsnewverdevice(boolean isnewverdevice) {
		this.isnewverdevice = isnewverdevice;
	}
}
