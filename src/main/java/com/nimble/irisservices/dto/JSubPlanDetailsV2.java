package com.nimble.irisservices.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.nimble.irisservices.dto.JTerms.Terms;

public class JSubPlanDetailsV2 implements Serializable {

	private static final long serialVersionUID = 0l;
	
	ArrayList<JMenuLst> feature_list;
	
	private ArrayList<JSubPeriodDetail> period_list ;
	
	private String plan_name = "NA";
	
	private boolean is_flexiplan = false;
	
	private List<String> plan_details;

	ArrayList<Terms> term_list = new ArrayList<Terms>();

	private boolean is_new_plan;

	public ArrayList<JMenuLst> getFeature_list() {
		return feature_list;
	}

	public void setFeature_list(ArrayList<JMenuLst> feature_list) {
		this.feature_list = feature_list;
	}

	public ArrayList<JSubPeriodDetail> getPeriod_list() {
		return period_list;
	}

	public void setPeriod_list(ArrayList<JSubPeriodDetail> period_list) {
		this.period_list = period_list;
	}

	public String getPlan_name() {
		return plan_name;
	}

	public void setPlan_name(String plan_name) {
		this.plan_name = plan_name;
	}

	public boolean isIs_flexiplan() {
		return is_flexiplan;
	}

	public void setIs_flexiplan(boolean is_flexiplan) {
		this.is_flexiplan = is_flexiplan;
	}

	public List<String> getPlan_details() {
		return plan_details;
	}

	public void setPlan_details(List<String> plan_details) {
		this.plan_details = plan_details;
	}

	public ArrayList<Terms> getTerm_list() {
		return term_list;
	}

	public void setTerm_list(ArrayList<Terms> term_list) {
		this.term_list = term_list;
	}

	public boolean isIs_new_plan() {
		return is_new_plan;
	}

	public void setIs_new_plan(boolean is_new_plan) {
		this.is_new_plan = is_new_plan;
	}
}
