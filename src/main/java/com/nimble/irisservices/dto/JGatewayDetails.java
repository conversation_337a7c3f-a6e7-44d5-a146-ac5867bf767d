package com.nimble.irisservices.dto;

import java.sql.Timestamp;

public class JGatewayDetails {

	private long id;
	private String carrier = "NA";
	private String description;
	private String extsensortype;
	private boolean alive  = false;
	private boolean enable = true;
	private String location;
	private String mdn;
	private String meid;
	private String name;
	private long asset_id;
    private long assetgroupid;
    private long cmp_id;
    private long groupid;
	private long modelid;
	private long subgroupid;
	private String sensorEnable = "110000000";	
	private String owner;
	private long assetinfo_id;
	private long groups_id;
	private float minval;
	private float maxval;
	private Timestamp lastrptdatetime;
    private long passwordtype	= 1;
    private boolean stopreport	= false;
    private String  starttime 	= "0";
    private String stoptime 	= "0";
    private Timestamp installed_date;    
    private String qrcode= "NA";
	private boolean onoffstatus=true;
	private String gatewayConfig="0000000000";
	private String onsleeptime="1800";
	private String offsleeptime="86400";
	private long default_goal=500;	
	private long calories_goal=100;
	
	
	public JGatewayDetails() {
		super();
		// TODO Auto-generated constructor stub
	}


	public JGatewayDetails(long id, String carrier, String description, boolean alive, boolean enable, String location,
			String mdn, String meid, String name, long asset_id, long assetgroupid, long cmp_id, long groupid,
			long modelid, long subgroupid, String sensorEnable, String owner, long assetinfo_id, long groups_id,
			float minval, float maxval, Timestamp lastrptdatetime, long passwordtype, boolean stopreport,
			String starttime, String stoptime, Timestamp installed_date, String qrcode, boolean onoffstatus,
			String gatewayConfig, String onsleeptime, String offsleeptime, long default_goal, long calories_goal) {
		super();
		this.id = id;
		this.carrier = carrier;
		this.description = description;
		this.alive = alive;
		this.enable = enable;
		this.location = location;
		this.mdn = mdn;
		this.meid = meid;
		this.name = name;
		this.asset_id = asset_id;
		this.assetgroupid = assetgroupid;
		this.cmp_id = cmp_id;
		this.groupid = groupid;
		this.modelid = modelid;
		this.subgroupid = subgroupid;
		this.sensorEnable = sensorEnable;
		this.owner = owner;
		this.assetinfo_id = assetinfo_id;
		this.groups_id = groups_id;
		this.minval = minval;
		this.maxval = maxval;
		this.lastrptdatetime = lastrptdatetime;
		this.passwordtype = passwordtype;
		this.stopreport = stopreport;
		this.starttime = starttime;
		this.stoptime = stoptime;
		this.installed_date = installed_date;
		this.qrcode = qrcode;
		this.onoffstatus = onoffstatus;
		this.gatewayConfig = gatewayConfig;
		this.onsleeptime = onsleeptime;
		this.offsleeptime = offsleeptime;
		this.default_goal = default_goal;
		this.calories_goal = calories_goal;
	}


	public long getId() {
		return id;
	}


	public void setId(long id) {
		this.id = id;
	}


	public String getCarrier() {
		return carrier;
	}


	public void setCarrier(String carrier) {
		this.carrier = carrier;
	}


	public String getDescription() {
		return description;
	}


	public void setDescription(String description) {
		this.description = description;
	}


	public boolean isAlive() {
		return alive;
	}


	public void setAlive(boolean alive) {
		this.alive = alive;
	}


	public boolean isEnable() {
		return enable;
	}


	public void setEnable(boolean enable) {
		this.enable = enable;
	}


	public String getLocation() {
		return location;
	}


	public void setLocation(String location) {
		this.location = location;
	}


	public String getMdn() {
		return mdn;
	}


	public void setMdn(String mdn) {
		this.mdn = mdn;
	}


	public String getMeid() {
		return meid;
	}


	public void setMeid(String meid) {
		this.meid = meid;
	}


	public String getName() {
		return name;
	}


	public void setName(String name) {
		this.name = name;
	}


	public long getAsset_id() {
		return asset_id;
	}


	public void setAsset_id(long asset_id) {
		this.asset_id = asset_id;
	}


	public long getAssetgroupid() {
		return assetgroupid;
	}


	public void setAssetgroupid(long assetgroupid) {
		this.assetgroupid = assetgroupid;
	}


	public long getCmp_id() {
		return cmp_id;
	}


	public void setCmp_id(long cmp_id) {
		this.cmp_id = cmp_id;
	}


	public long getGroupid() {
		return groupid;
	}


	public void setGroupid(long groupid) {
		this.groupid = groupid;
	}


	public long getModelid() {
		return modelid;
	}


	public void setModelid(long modelid) {
		this.modelid = modelid;
	}


	public long getSubgroupid() {
		return subgroupid;
	}


	public void setSubgroupid(long subgroupid) {
		this.subgroupid = subgroupid;
	}


	public String getSensorEnable() {
		return sensorEnable;
	}


	public void setSensorEnable(String sensorEnable) {
		this.sensorEnable = sensorEnable;
	}


	public String getOwner() {
		return owner;
	}


	public void setOwner(String owner) {
		this.owner = owner;
	}


	public long getAssetinfo_id() {
		return assetinfo_id;
	}


	public void setAssetinfo_id(long assetinfo_id) {
		this.assetinfo_id = assetinfo_id;
	}


	public long getGroups_id() {
		return groups_id;
	}


	public void setGroups_id(long groups_id) {
		this.groups_id = groups_id;
	}


	public float getMinval() {
		return minval;
	}


	public void setMinval(float minval) {
		this.minval = minval;
	}


	public float getMaxval() {
		return maxval;
	}


	public void setMaxval(float maxval) {
		this.maxval = maxval;
	}


	public Timestamp getLastrptdatetime() {
		return lastrptdatetime;
	}


	public void setLastrptdatetime(Timestamp lastrptdatetime) {
		this.lastrptdatetime = lastrptdatetime;
	}


	public long getPasswordtype() {
		return passwordtype;
	}


	public void setPasswordtype(long passwordtype) {
		this.passwordtype = passwordtype;
	}


	public boolean isStopreport() {
		return stopreport;
	}


	public void setStopreport(boolean stopreport) {
		this.stopreport = stopreport;
	}


	public String getStarttime() {
		return starttime;
	}


	public void setStarttime(String starttime) {
		this.starttime = starttime;
	}


	public String getStoptime() {
		return stoptime;
	}


	public void setStoptime(String stoptime) {
		this.stoptime = stoptime;
	}


	public Timestamp getInstalled_date() {
		return installed_date;
	}


	public void setInstalled_date(Timestamp installed_date) {
		this.installed_date = installed_date;
	}


	public String getQrcode() {
		return qrcode;
	}


	public void setQrcode(String qrcode) {
		this.qrcode = qrcode;
	}


	public boolean isOnoffstatus() {
		return onoffstatus;
	}


	public void setOnoffstatus(boolean onoffstatus) {
		this.onoffstatus = onoffstatus;
	}


	public String getGatewayConfig() {
		return gatewayConfig;
	}


	public void setGatewayConfig(String gatewayConfig) {
		this.gatewayConfig = gatewayConfig;
	}


	public String getOnsleeptime() {
		return onsleeptime;
	}


	public void setOnsleeptime(String onsleeptime) {
		this.onsleeptime = onsleeptime;
	}


	public String getOffsleeptime() {
		return offsleeptime;
	}


	public void setOffsleeptime(String offsleeptime) {
		this.offsleeptime = offsleeptime;
	}


	public long getDefault_goal() {
		return default_goal;
	}


	public void setDefault_goal(long default_goal) {
		this.default_goal = default_goal;
	}


	public long getCalories_goal() {
		return calories_goal;
	}


	public void setCalories_goal(long calories_goal) {
		this.calories_goal = calories_goal;
	}


	public String getExtsensortype() {
		return extsensortype;
	}


	public void setExtsensortype(String extsensortype) {
		this.extsensortype = extsensortype;
	}

    
}

