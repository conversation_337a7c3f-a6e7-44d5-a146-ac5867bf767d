package com.nimble.irisservices.dto;



public class JPetFitDailyReport{
	
	private long id ;

    private String gateway_id;
    
    private String gatewayName;
	
	
	private String utcDate;
	
	
	private int totalStepCount;
	
	
	private int totalIdleSecs;
	
	
	private int totalwalksecs;
	
	
	private int totalRunSecs;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}


	public String getUtcDate() {
		return utcDate;
	}

	public void setUtcDate(String utcDate) {
		this.utcDate = utcDate;
	}

	public int getTotalStepCount() {
		return totalStepCount;
	}

	public void setTotalStepCount(int totalStepCount) {
		this.totalStepCount = totalStepCount;
	}

	public int getTotalIdleSecs() {
		return totalIdleSecs;
	}

	public void setTotalIdleSecs(int totalIdleSecs) {
		this.totalIdleSecs = totalIdleSecs;
	}

	public int getTotalwalksecs() {
		return totalwalksecs;
	}

	public void setTotalwalksecs(int totalwalksecs) {
		this.totalwalksecs = totalwalksecs;
	}

	public int getTotalRunSecs() {
		return totalRunSecs;
	}

	public void setTotalRunSecs(int totalRunSecs) {
		this.totalRunSecs = totalRunSecs;
	}

	public String getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(String gateway_id) {
		this.gateway_id = gateway_id;
	}

	public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

	public JPetFitDailyReport(String gateway_id, String gatewayName, String utcDate, int totalStepCount,
			int totalIdleSecs, int totalwalksecs, int totalRunSecs) {
		super();
		this.gateway_id = gateway_id;
		this.gatewayName = gatewayName;
		this.utcDate = utcDate;
		this.totalStepCount = totalStepCount;
		this.totalIdleSecs = totalIdleSecs;
		this.totalwalksecs = totalwalksecs;
		this.totalRunSecs = totalRunSecs;
	}
	
	
	
}
