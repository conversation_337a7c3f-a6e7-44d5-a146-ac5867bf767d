package com.nimble.irisservices.dto;

public class AmazonReviewList {

	private long user_id = 0;
	private int rateus_count = 0;
	private String comments = "NA";
	private boolean app_rateus = false;
	private boolean amazon_rateus = false;
	private String username = "NA";
	private String email = "NA";
	private String createdOn = "NA";

	public AmazonReviewList() {
		super();
		// TODO Auto-generated constructor stub
	}

	public AmazonReviewList(long user_id, int rateus_count, String comments, boolean app_rateus, boolean amazon_rateus,
			String username, String email, String createdOn) {
		super();
		this.user_id = user_id;
		this.rateus_count = rateus_count;
		this.comments = comments;
		this.app_rateus = app_rateus;
		this.amazon_rateus = amazon_rateus;
		this.username = username;
		this.email = email;
		this.createdOn = createdOn;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public int getRateus_count() {
		return rateus_count;
	}

	public void setRateus_count(int rateus_count) {
		this.rateus_count = rateus_count;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public boolean isApp_rateus() {
		return app_rateus;
	}

	public void setApp_rateus(boolean app_rateus) {
		this.app_rateus = app_rateus;
	}

	public boolean isAmazon_rateus() {
		return amazon_rateus;
	}

	public void setAmazon_rateus(boolean amazon_rateus) {
		this.amazon_rateus = amazon_rateus;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}

}
