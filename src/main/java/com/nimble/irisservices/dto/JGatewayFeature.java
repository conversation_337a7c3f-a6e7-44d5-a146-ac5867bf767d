package com.nimble.irisservices.dto;

public class JGatewayFeature {

	private long gateway_id;
	
	private long feature_id;
	
	private int enable;
	
	private int txn_limit;
	
	private int remaining_limit;
	
	private int unlimited_cr;
	
	private int plan_id;
	
	private String sub_id = "NA";
	
	private int period_id;

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public long getFeature_id() {
		return feature_id;
	}

	public void setFeature_id(long feature_id) {
		this.feature_id = feature_id;
	}

	public int getEnable() {
		return enable;
	}

	public void setEnable(int enable) {
		this.enable = enable;
	}

	public int getTxn_limit() {
		return txn_limit;
	}

	public void setTxn_limit(int txn_limit) {
		this.txn_limit = txn_limit;
	}

	public int getRemaining_limit() {
		return remaining_limit;
	}

	public void setRemaining_limit(int remaining_limit) {
		this.remaining_limit = remaining_limit;
	}

	public int getUnlimited_cr() {
		return unlimited_cr;
	}

	public void setUnlimited_cr(int unlimited_cr) {
		this.unlimited_cr = unlimited_cr;
	}

	public int getPlan_id() {
		return plan_id;
	}

	public void setPlan_id(int plan_id) {
		this.plan_id = plan_id;
	}

	public String getSub_id() {
		return sub_id;
	}

	public void setSub_id(String sub_id) {
		this.sub_id = sub_id;
	}

	public int getPeriod_id() {
		return period_id;
	}

	public void setPeriod_id(int period_id) {
		this.period_id = period_id;
	}
}
