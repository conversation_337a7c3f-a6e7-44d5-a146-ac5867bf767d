package com.nimble.irisservices.dto;

public class UserPlanPreference {

    private long userId;

    private long gatewayId;

    private String planPref;

    private String updatedOn;

    public long getUserId() {

        return userId;
    }

    public void setUserId(long userId) {

        this.userId = userId;
    }

    public long getGatewayId() {

        return gatewayId;
    }

    public void setGatewayId(long gatewayId) {

        this.gatewayId = gatewayId;
    }

    public String getPlanPref() {

        return planPref;
    }

    public void setPlanPref(String planPref) {

        this.planPref = planPref;
    }

    public String getUpdatedOn() {

        return updatedOn;
    }

    public void setUpdatedOn(String updatedOn) {

        this.updatedOn = updatedOn;
    }
}
