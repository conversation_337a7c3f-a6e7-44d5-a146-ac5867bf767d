package com.nimble.irisservices.dto;

import java.io.Serializable;
import java.util.List;

public class JSubscriptionPlanReport implements Serializable {

	private static final long serialVersionUID = 0l;

	String planid;
	String planname;
	String price;
	String period;
	String nextrenew_date;
	int days_remaining;
	String status;
	private boolean setupAutoRenewal;
	List<JGatewaySubSetup> listJGatewaySubSetup;
	String desc;
	String autoRenewalStatus;
	String createDate;
	String updateDate;
	String cbSubId;
	String cbSubStatus;
	String availCredit;
	private boolean alert_setting;
	String chargebeeid;
	String startedAt;
	String product_id;
	String periodid;
	boolean cancel = false;
	int status_code = 0;
	boolean payment_due = false;
	boolean freetrial = false;
	String cur_feature = "";
	boolean freeplan = false;
	String btn_label = "";
	String btn_desc = "";
	String sub_label = "";
	boolean show_cancel_sub = false;
	String cancelSubUrl = "NA";
	String cur_feature_flutter = "";
	String cur_feature_flutter_dark = "";
	boolean cancel_inapp_redirect=false;
	boolean show_benefits  = false;
	String cur_feature_ui_new = "";
	
	public JSubscriptionPlanReport() {
		super();
	}

	public JSubscriptionPlanReport(String planid, String planname, String price, String period, String nextrenew_date,
			int days_remaining, String status, boolean setupAutoRenewal, List<JGatewaySubSetup> listJGatewaySubSetup,
			String desc, String autoRenewalStatus, String createDate, String updateDate, String cbSubId,
			String cbSubStatus, String availCredit, boolean alert_setting, String chargebeeid, String startedAt,
			String product_id, String periodid, boolean cancel, int status_code, boolean payment_due) {
		super();
		this.planid = planid;
		this.planname = planname;
		this.price = price;
		this.period = period;
		this.nextrenew_date = nextrenew_date;
		this.days_remaining = days_remaining;
		this.status = status;
		this.setupAutoRenewal = setupAutoRenewal;
		this.listJGatewaySubSetup = listJGatewaySubSetup;
		this.desc = desc;
		this.autoRenewalStatus = autoRenewalStatus;
		this.createDate = createDate;
		this.updateDate = updateDate;
		this.cbSubId = cbSubId;
		this.cbSubStatus = cbSubStatus;
		this.availCredit = availCredit;
		this.alert_setting = alert_setting;
		this.chargebeeid = chargebeeid;
		this.startedAt = startedAt;
		this.product_id = product_id;
		this.periodid = periodid;
		this.cancel = cancel;
		this.status_code = status_code;
		this.payment_due = payment_due;
	}

	public String getPlanid() {
		return planid;
	}

	public void setPlanid(String planid) {
		this.planid = planid;
	}

	public String getPlanname() {
		return planname;
	}

	public void setPlanname(String planname) {
		this.planname = planname;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getPeriod() {
		return period;
	}

	public void setPeriod(String period) {
		this.period = period;
	}

	public String getNextrenew_date() {
		return nextrenew_date;
	}

	public void setNextrenew_date(String nextrenew_date) {
		this.nextrenew_date = nextrenew_date;
	}

	public int getDays_remaining() {
		return days_remaining;
	}

	public void setDays_remaining(int days_remaining) {
		this.days_remaining = days_remaining;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public boolean isSetupAutoRenewal() {
		return setupAutoRenewal;
	}

	public void setSetupAutoRenewal(boolean setupAutoRenewal) {
		this.setupAutoRenewal = setupAutoRenewal;
	}

	public List<JGatewaySubSetup> getListJGatewaySubSetup() {
		return listJGatewaySubSetup;
	}

	public void setListJGatewaySubSetup(List<JGatewaySubSetup> listJGatewaySubSetup) {
		this.listJGatewaySubSetup = listJGatewaySubSetup;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getAutoRenewalStatus() {
		return autoRenewalStatus;
	}

	public void setAutoRenewalStatus(String autoRenewalStatus) {
		this.autoRenewalStatus = autoRenewalStatus;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(String updateDate) {
		this.updateDate = updateDate;
	}

	public String getCbSubId() {
		return cbSubId;
	}

	public void setCbSubId(String cbSubId) {
		this.cbSubId = cbSubId;
	}

	public String getCbSubStatus() {
		return cbSubStatus;
	}

	public void setCbSubStatus(String cbSubStatus) {
		this.cbSubStatus = cbSubStatus;
	}

	public String getAvailCredit() {
		return availCredit;
	}

	public void setAvailCredit(String availCredit) {
		this.availCredit = availCredit;
	}

	public boolean isAlert_setting() {
		return alert_setting;
	}

	public void setAlert_setting(boolean alert_setting) {
		this.alert_setting = alert_setting;
	}

	public String getChargebeeid() {
		return chargebeeid;
	}

	public void setChargebeeid(String chargebeeid) {
		this.chargebeeid = chargebeeid;
	}

	public String getStartedAt() {
		return startedAt;
	}

	public void setStartedAt(String startedAt) {
		this.startedAt = startedAt;
	}

	public String getProduct_id() {
		return product_id;
	}

	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}

	public String getPeriodid() {
		return periodid;
	}

	public void setPeriodid(String periodid) {
		this.periodid = periodid;
	}

	public boolean isCancel() {
		return cancel;
	}

	public void setCancel(boolean cancel) {
		this.cancel = cancel;
	}

	public int getStatus_code() {
		return status_code;
	}

	public void setStatus_code(int status_code) {
		this.status_code = status_code;
	}

	public boolean isPayment_due() {
		return payment_due;
	}

	public void setPayment_due(boolean payment_due) {
		this.payment_due = payment_due;
	}

	public boolean isFreetrial() {
		return freetrial;
	}

	public void setFreetrial(boolean freetrial) {
		this.freetrial = freetrial;
	}

	public String getCur_feature() {
		return cur_feature;
	}

	public void setCur_feature(String cur_feature) {
		this.cur_feature = cur_feature;
	}

	public boolean isFreeplan() {
		return freeplan;
	}

	public void setFreeplan(boolean freeplan) {
		this.freeplan = freeplan;
	}

	public String getBtn_label() {
		return btn_label;
	}

	public void setBtn_label(String btn_label) {
		this.btn_label = btn_label;
	}

	public String getBtn_desc() {
		return btn_desc;
	}

	public void setBtn_desc(String btn_desc) {
		this.btn_desc = btn_desc;
	}

	public String getSub_label() {
		return sub_label;
	}

	public void setSub_label(String sub_label) {
		this.sub_label = sub_label;
	}

	public boolean isShow_cancel_sub() {
		return show_cancel_sub;
	}

	public void setShow_cancel_sub(boolean show_cancel_sub) {
		this.show_cancel_sub = show_cancel_sub;
	}

	public String getCancelSubUrl() {
		return cancelSubUrl;
	}

	public void setCancelSubUrl(String cancelSubUrl) {
		this.cancelSubUrl = cancelSubUrl;
	}

	public String getCur_feature_flutter() {
		return cur_feature_flutter;
	}

	public void setCur_feature_flutter(String cur_feature_flutter) {
		this.cur_feature_flutter = cur_feature_flutter;
	}

	public String getCur_feature_flutter_dark() {
		return cur_feature_flutter_dark;
	}

	public void setCur_feature_flutter_dark(String cur_feature_flutter_dark) {
		this.cur_feature_flutter_dark = cur_feature_flutter_dark;
	}

	public boolean isCancel_inapp_redirect() {
		return cancel_inapp_redirect;
	}

	public void setCancel_inapp_redirect(boolean cancel_inapp_redirect) {
		this.cancel_inapp_redirect = cancel_inapp_redirect;
	}

	public boolean isShow_benefits() {
		return show_benefits;
	}

	public void setShow_benefits(boolean show_benefits) {
		this.show_benefits = show_benefits;
	}

	public String getCur_feature_ui_new() {
		return cur_feature_ui_new;
	}

	public void setCur_feature_ui_new(String cur_feature_ui_new) {
		this.cur_feature_ui_new = cur_feature_ui_new;
	}

}
