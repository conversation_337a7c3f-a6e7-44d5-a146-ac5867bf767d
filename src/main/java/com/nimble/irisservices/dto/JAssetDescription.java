package com.nimble.irisservices.dto;

import java.io.Serializable;

@SuppressWarnings("serial")
public class JAssetDescription implements Serializable{

	private String field_des_1;
	private String field_des_2;
	private String field_des_3;
	private String field_des_4;
	private String field_des_5;
	private String field_des_6;
	private String field_des_7;
	private String field_des_8;
	private long cmpId;	

	public JAssetDescription(String field_des_1, String field_des_2, String field_des_3,
			String field_des_4, String field_des_5, String field_des_6, String field_des_7,
			String field_des_8,long cmpId) {
		super();
		this.field_des_1 = field_des_1;
		this.field_des_2 = field_des_2;
		this.field_des_3 = field_des_3;
		this.field_des_4 = field_des_4;
		this.field_des_5 = field_des_5;
		this.field_des_6 = field_des_6;
		this.field_des_7 = field_des_7;
		this.field_des_8 = field_des_8;
		this.cmpId 		 = cmpId;
	}

	public String getfield_des_1() {
		return field_des_1;
	}

	public String getfield_des_2() {
		return field_des_2;
	}

	public String getfield_des_3() {
		return field_des_3;
	}

	public String getfield_des_4() {
		return field_des_4;
	}
	
	public String getfield_des_5() {
		return field_des_5;
	}
	
	public String getfield_des_6() {
		return field_des_6;
	}
	
	public String getfield_des_7() {
		return field_des_7;
	}
	
	public String getfield_des_8() {
		return field_des_8;
	}

	public long getCmpId() {
		return cmpId;
	}

}
