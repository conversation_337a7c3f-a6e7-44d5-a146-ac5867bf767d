package com.nimble.irisservices.dto;

import java.io.Serializable;
import java.util.ArrayList;

public class JSubscriptionPlan implements Serializable {

	private static final long serialVersionUID = 0l;
	
	private long planid;
	
	private String planname;
	
	private String feature_list;
	
	private ArrayList<JSubscriptionPeriod> period_list ;
	
	private boolean freetrial_avail = false;
	
	private boolean is_best_seller = false; 
	
	String feature_list_flutter = "NA";
	
	String feature_list_dark_flutter = "NA";
	
	String feature_list_ui_new = "NA";
	
	String cur_feature_ui_new = "NA";
	
	ArrayList<JFeatureList> feature_list_ui_new_1 = new ArrayList<>();

	public JSubscriptionPlan()
	{
		super();
	}

	public long getPlanid() {
		return planid;
	}

	public void setPlanid(long planid) {
		this.planid = planid;
	}

	public String getPlanname() {
		return planname;
	}

	public void setPlanname(String planname) {
		this.planname = planname;
	}

	public String getFeature_list() {
		return feature_list;
	}

	public void setFeature_list(String feature_list) {
		this.feature_list = feature_list;
	}

	public ArrayList<JSubscriptionPeriod> getPeriod_list() {
		return period_list;
	}

	public void setPeriod_list(ArrayList<JSubscriptionPeriod> period_list) {
		this.period_list = period_list;
	}

	public boolean isFreetrial_avail() {
		return freetrial_avail;
	}

	public void setFreetrial_avail(boolean freetrial_avail) {
		this.freetrial_avail = freetrial_avail;
	}

	public boolean isIs_best_seller() {
		return is_best_seller;
	}

	public void setIs_best_seller(boolean is_best_seller) {
		this.is_best_seller = is_best_seller;
	}

	public String getFeature_list_flutter() {
		return feature_list_flutter;
	}

	public void setFeature_list_flutter(String feature_list_flutter) {
		this.feature_list_flutter = feature_list_flutter;
	}

	public String getFeature_list_dark_flutter() {
		return feature_list_dark_flutter;
	}

	public void setFeature_list_dark_flutter(String feature_list_dark_flutter) {
		this.feature_list_dark_flutter = feature_list_dark_flutter;
	}

	public String getFeature_list_ui_new() {
		return feature_list_ui_new;
	}

	public void setFeature_list_ui_new(String feature_list_ui_new) {
		this.feature_list_ui_new = feature_list_ui_new;
	}

	public String getCur_feature_ui_new() {
		return cur_feature_ui_new;
	}

	public void setCur_feature_ui_new(String cur_feature_ui_new) {
		this.cur_feature_ui_new = cur_feature_ui_new;
	}

	public ArrayList<JFeatureList> getFeature_list_ui_new_1() {
		return feature_list_ui_new_1;
	}

	public void setFeature_list_ui_new_1(ArrayList<JFeatureList> feature_list_ui_new_1) {
		this.feature_list_ui_new_1 = feature_list_ui_new_1;
	}
	
}
