package com.nimble.irisservices.dto;

public class AmazonReviewStatus {

	private String redirecturl;
	
	private boolean show_dialog;
	
	private int amazon_redirect_cnt;
	
	private String app_title = "";
	
	private String app_msg = "Please tell us how can we improve";
	
	private String amazon_title = "";
	
	private String amazon_msg = "Please rate us on Amazon!";
	
	private String redirect_type = "internal";
	
	public AmazonReviewStatus() {
	}

	public AmazonReviewStatus( String redirecturl, boolean show_dialog,int amazon_redirect_cnt,	 String redirect_type) {
		this.redirecturl = redirecturl;
		this.show_dialog = show_dialog;
		this.amazon_redirect_cnt = amazon_redirect_cnt;
		this.redirect_type = redirect_type;
	}

	public String getRedirecturl() {
		return redirecturl;
	}

	public void setRedirecturl(String redirecturl) {
		this.redirecturl = redirecturl;
	}

	public boolean isShow_dialog() {
		return show_dialog;
	}

	public void setShow_dialog(boolean show_dialog) {
		this.show_dialog = show_dialog;
	}

	public int getAmazon_redirect_cnt() {
		return amazon_redirect_cnt;
	}

	public void setAmazon_redirect_cnt(int amazon_redirect_cnt) {
		this.amazon_redirect_cnt = amazon_redirect_cnt;
	}

	public String getApp_title() {
		return app_title;
	}

	public void setApp_title(String app_title) {
		this.app_title = app_title;
	}

	public String getApp_msg() {
		return app_msg;
	}

	public void setApp_msg(String app_msg) {
		this.app_msg = app_msg;
	}

	public String getAmazon_title() {
		return amazon_title;
	}

	public void setAmazon_title(String amazon_title) {
		this.amazon_title = amazon_title;
	}

	public String getAmazon_msg() {
		return amazon_msg;
	}

	public void setAmazon_msg(String amazon_msg) {
		this.amazon_msg = amazon_msg;
	}

	public String getRedirect_type() {
		return redirect_type;
	}

	public void setRedirect_type(String redirect_type) {
		this.redirect_type = redirect_type;
	}
	
}
