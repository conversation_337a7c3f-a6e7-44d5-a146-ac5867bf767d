package com.nimble.irisservices.dto;

public class JFurBitReportTotal {
	
	private long totalIdleSecs= 0;
	private long totalRunSecs =0;
	private long totalstepcount =0;
	private long totalWalkSecs =0;
	private long totalActiveSecs = 0;
	private long totalCaloriesBurnt=0;
	
	private int totalpacket=0;
	
	private long gatewayId;
	private String gatewayName;
	
	
	public long getTotalIdleSecs() {
		return totalIdleSecs;
	}
	public void setTotalIdleSecs(long totalIdleSecs) {
		this.totalIdleSecs = totalIdleSecs;
	}
	public long getTotalRunSecs() {
		return totalRunSecs;
	}
	public void setTotalRunSecs(long totalRunSecs) {
		this.totalRunSecs = totalRunSecs;
	}
	public long getTotalstepcount() {
		return totalstepcount;
	}
	public void setTotalstepcount(long totalstepcount) {
		this.totalstepcount = totalstepcount;
	}
	public long getTotalWalkSecs() {
		return totalWalkSecs;
	}
	public void setTotalWalkSecs(long totalWalkSecs) {
		this.totalWalkSecs = totalWalkSecs;
	}
	public long getTotalActiveSecs() {
		return totalActiveSecs;
	}
	public void setTotalActiveSecs(long totalActiveSecs) {
		this.totalActiveSecs = totalActiveSecs;
	}
	public long getGatewayId() {
		return gatewayId;
	}
	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}
	public String getGatewayName() {
		return gatewayName;
	}
	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}
	public long getTotalCaloriesBurnt() {
		return totalCaloriesBurnt;
	}
	public void setTotalCaloriesBurnt(long totalCaloriesBurnt) {
		this.totalCaloriesBurnt = totalCaloriesBurnt;
	}
	public int getTotalpacket() {
		return totalpacket;
	}
	public void setTotalpacket(int totalpacket) {
		this.totalpacket = totalpacket;
	}

	
	
}
