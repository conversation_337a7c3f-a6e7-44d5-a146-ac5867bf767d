package com.nimble.irisservices.dto;

import java.util.ArrayList;

public class JFcmNotification {

	private String title = "";
	private String body = "";
	private String redirectUrl = "NA";
	private String imageUrl = "NA";
	private ArrayList<String> userTokenList;
	private String source = "";
	private String shortDescription = "NA";
	private int monitor_type_id;
	
	public JFcmNotification() {
		super();
	}
	public JFcmNotification(String title, String body, ArrayList<String> userTokenList) {
		super();
		this.title = title;
		this.body = body;
		this.userTokenList = userTokenList;
	}
	
	public JFcmNotification(String title, String body, String redirectUrl, String imageUrl,
			ArrayList<String> userTokenList, String source, String shortDescription, int monitor_type_id) {
		super();
		this.title = title;
		this.body = body;
		this.redirectUrl = redirectUrl;
		this.imageUrl = imageUrl;
		this.userTokenList = userTokenList;
		this.source = source;
		this.shortDescription = shortDescription;
		this.monitor_type_id = monitor_type_id;
	}
	
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getBody() {
		return body;
	}
	public void setBody(String body) {
		this.body = body;
	}
	public ArrayList<String> getUserTokenList() {
		return userTokenList;
	}
	public void setUserTokenList(ArrayList<String> userTokenList) {
		this.userTokenList = userTokenList;
	}
	public String getRedirectUrl() {
		return redirectUrl;
	}
	public void setRedirectUrl(String redirectUrl) {
		this.redirectUrl = redirectUrl;
	}
	public String getImageUrl() {
		return imageUrl;
	}
	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public String getShortDescription() {
		return shortDescription;
	}
	public void setShortDescription(String shortDescription) {
		this.shortDescription = shortDescription;
	}
	public int getMonitor_type_id() {
		return monitor_type_id;
	}
	public void setMonitor_type_id(int monitor_type_id) {
		this.monitor_type_id = monitor_type_id;
	}
}
