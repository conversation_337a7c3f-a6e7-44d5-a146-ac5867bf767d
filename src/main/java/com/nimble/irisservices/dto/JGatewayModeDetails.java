package com.nimble.irisservices.dto;

public class JGatewayModeDetails {
	
	private String powerModes; 				
	private long prev_mode;				
	private long cur_mode;				
	private String mode_status;
		
	public JGatewayModeDetails() {
		super();
		// TODO Auto-generated constructor stub
	}
		
	public String getPowerModes() {
		return powerModes;
	}
	public void setPowerModes(String powerModes) {
		this.powerModes = powerModes;
	}
	public long getPrev_mode() {
		return prev_mode;
	}
	public void setPrev_mode(long prev_mode) {
		this.prev_mode = prev_mode;
	}
	public long getCur_mode() {
		return cur_mode;
	}
	public void setCur_mode(long cur_mode) {
		this.cur_mode = cur_mode;
	}
	public String getMode_status() {
		return mode_status;
	}
	public void setMode_status(String mode_status) {
		this.mode_status = mode_status;
	}
	
}
