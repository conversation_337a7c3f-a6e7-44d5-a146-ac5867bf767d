package com.nimble.irisservices.dto;

import java.io.Serializable;

@SuppressWarnings("serial")
public class LastGatewayReport implements Serializable {
	
	private String assettype;
	private String meid;
	private String assetid;
	private String reportdate;   /*local date*/
	private String reporttime;   /*local time*/
	private String timeoffset;
	private String gpsstatus;
	private String lat;
	private String latdir;
	private String lon;
	private String londir;
	private float heading ;
	private String battery;
	private String probetype;
	private String temperature;
	private float speed;
	private float distance;
	private String tdisplayunit;
	private String eventid;
	private String heat_index;
	public String getAssettype() {
		return assettype;
	}
	public void setAssettype(String assettype) {
		this.assettype = assettype;
	}
	public String getMeid() {
		return meid;
	}
	public void setMeid(String meid) {
		this.meid = meid;
	}
	public String getAssetid() {
		return assetid;
	}
	public void setAssetid(String assetid) {
		this.assetid = assetid;
	}
	public String getReportdate() {
		return reportdate;
	}
	public void setReportdate(String reportdate) {
		this.reportdate = reportdate;
	}
	public String getReporttime() {
		return reporttime;
	}
	public void setReporttime(String reporttime) {
		this.reporttime = reporttime;
	}
	public String getTimeoffset() {
		return timeoffset;
	}
	public void setTimeoffset(String timeoffset) {
		this.timeoffset = timeoffset;
	}
	public String getGpsstatus() {
		return gpsstatus;
	}
	public void setGpsstatus(String gpsstatus) {
		this.gpsstatus = gpsstatus;
	}
	public String getLat() {
		return lat;
	}
	public void setLat(String lat) {
		this.lat = lat;
	}
	public String getLatdir() {
		return latdir;
	}
	public void setLatdir(String latdir) {
		this.latdir = latdir;
	}
	public String getLon() {
		return lon;
	}
	public void setLon(String lon) {
		this.lon = lon;
	}
	public String getLondir() {
		return londir;
	}
	public void setLondir(String londir) {
		this.londir = londir;
	}
	public float getHeading() {
		return heading;
	}
	public void setHeading(float heading) {
		this.heading = heading;
	}
	public String getBattery() {
		return battery;
	}
	public void setBattery(String battery) {
		this.battery = battery;
	}
	public String getProbetype() {
		return probetype;
	}
	public void setProbetype(String probetype) {
		this.probetype = probetype;
	}
	public String getTemperature() {
		return temperature;
	}
	public void setTemperature(String temperature) {
		this.temperature = temperature;
	}
	public float getSpeed() {
		return speed;
	}
	public void setSpeed(float speed) {
		this.speed = speed;
	}
	public float getDistance() {
		return distance;
	}
	public void setDistance(float distance) {
		this.distance = distance;
	}
	public String getTdisplayunit() {
		return tdisplayunit;
	}
	public void setTdisplayunit(String tdisplayunit) {
		this.tdisplayunit = tdisplayunit;
	}
	public String getEventid() {
		return eventid;
	}
	public void setEventid(String eventid) {
		this.eventid = eventid;
	}
	public String getHeat_index() {
		return heat_index;
	}
	public void setHeat_index(String heat_index) {
		this.heat_index = heat_index;
	}
	
}
