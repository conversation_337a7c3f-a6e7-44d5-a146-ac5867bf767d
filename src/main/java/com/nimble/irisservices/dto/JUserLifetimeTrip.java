package com.nimble.irisservices.dto;

public class JUserLifetimeTrip {
	
	private int no_of_trips = 0;
	private int days_rv = 0;
	private String from_addr = "NA";
	private String to_addr = "NA";
	private String image_path = "NA";
	private long trip_id = 0;
	private String shortFromAddr = "NA";
	private String shortToAddr = "NA";
	
	
	public JUserLifetimeTrip() {
		super();
	}

	public JUserLifetimeTrip(int no_of_trips, int days_rv, String from_addr, String to_addr, String image_path,
			long trip_id,String shortFromAddr,String shortToAddr) {
		super();
		this.no_of_trips = no_of_trips;
		this.days_rv = days_rv;
		this.from_addr = from_addr;
		this.to_addr = to_addr;
		this.image_path = image_path;
		this.trip_id = trip_id;
		this.shortToAddr = shortFromAddr;
		this.shortToAddr = shortToAddr;
	}

	public int getNo_of_trips() {
		return no_of_trips;
	}

	public void setNo_of_trips(int no_of_trips) {
		this.no_of_trips = no_of_trips;
	}

	public int getDays_rv() {
		return days_rv;
	}

	public void setDays_rv(int days_rv) {
		this.days_rv = days_rv;
	}

	public String getFrom_addr() {
		return from_addr;
	}

	public void setFrom_addr(String from_addr) {
		this.from_addr = from_addr;
	}

	public String getTo_addr() {
		return to_addr;
	}

	public void setTo_addr(String to_addr) {
		this.to_addr = to_addr;
	}

	public String getImage_path() {
		return image_path;
	}

	public void setImage_path(String image_path) {
		this.image_path = image_path;
	}

	public long getTrip_id() {
		return trip_id;
	}

	public void setTrip_id(long trip_id) {
		this.trip_id = trip_id;
	}

	public String getShortFromAddr() {
		return shortFromAddr;
	}

	public void setShortFromAddr(String shortFromAddr) {
		this.shortFromAddr = shortFromAddr;
	}

	public String getShortToAddr() {
		return shortToAddr;
	}

	public void setShortToAddr(String shortToAddr) {
		this.shortToAddr = shortToAddr;
	}
	
}
