package com.nimble.irisservices.dto;

public class JAdventureReport {

	private String startdatetime;
	
	private String enddatetime;
	
	private double lat;
	
	private double lon;

	private long gatewayId;
	
	private String gatewayName;
	
	private String timeago="15min ago";

	public JAdventureReport (String startdatetime,String enddatetime,double lat,double lon,long gatewayId,String gatewayName,String time){
		this.startdatetime = startdatetime;
		this.enddatetime = enddatetime;
		this.lat = lat;
		this.lon = lon;
		this.gatewayId = gatewayId;		
		this.gatewayName = gatewayName;
		this.timeago = time;
	}

	public String getStartdatetime() {
		return startdatetime;
	}

	public void setStartdatetime(String startdatetime) {
		this.startdatetime = startdatetime;
	}

	public String getEnddatetime() {
		return enddatetime;
	}

	public void setEnddatetime(String enddatetime) {
		this.enddatetime = enddatetime;
	}

	public double getLat() {
		return lat;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public double getLon() {
		return lon;
	}

	public void setLon(double lon) {
		this.lon = lon;
	}

	public long getGatewayId() {
		return gatewayId;
	}

	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}

	public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

	public String getTimeago() {
		return timeago;
	}

	public void setTimeago(String timeago) {
		this.timeago = timeago;
	}	
}
