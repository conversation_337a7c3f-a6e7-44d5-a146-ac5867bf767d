package com.nimble.irisservices.dto;

import java.util.ArrayList;

public class ShipmentDetailData {
	public long id = 0;
	public String delivery_status = "";	
	public String delivery_category = "";	
	public String order_id;
	public String tracking_number;
	public String shipped_date = "";	
	public String order_date = "";	
	public String order_price = "";	
	public String product_name = "";
	public String order_email = "";
	public String order_quantity = "";
	public String tracking_link = "";
	public String sales_channel ="";
	private ArrayList<OrderTrackingData> tracking_status = new ArrayList<>();
	


	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getTracking_link() {
		return tracking_link;
	}

	public void setTracking_link(String tracking_link) {
		this.tracking_link = tracking_link;
	}

	public ArrayList<OrderTrackingData> getTracking_status() {
		return tracking_status;
	}

	public void setOrder_date(String order_date) {
		this.order_date = order_date;
	}

	public String getOrder_id() {
		return order_id;
	}

	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}
	
	public ArrayList<OrderTrackingData> tracking_status() {
		return tracking_status;
	}

	public void setTracking_status(ArrayList<OrderTrackingData> tracking_status) {
		this.tracking_status = tracking_status;
	}
	
	public String getTracking_number() {
		return tracking_number;
	}

	public void setTracking_number(String tracking_number) {
		this.tracking_number = tracking_number;
	}
	
	public String getDelivery_status() {
		return delivery_status;
	}

	public void setDelivery_status(String delivery_status) {
		this.delivery_status = delivery_status;
	}
	
	public String getDelivery_category() {
		return delivery_category;
	}

	public void setDelivery_category(String delivery_category) {
		this.delivery_category = delivery_category;
	}
	
	public String getShipped_date() {
		return shipped_date;
	}

	public void setShipped_date(String shipped_date) {
		this.shipped_date = shipped_date;
	}
	
	public String getOrder_date() {
		return order_date;
	}

	public void setgetOrder_date(String order_date) {
		this.order_date = order_date;
	}
	
	public String getOrder_price() {
		return order_price;
	}

	public void setOrder_price(String order_price) {
		this.order_price = order_price;
	}
	
	public String getProduct_name() {
		return product_name;
	}

	public void setProduct_name(String product_name) {
		this.product_name = product_name;
	}
	
	public String getOrder_email() {
		return order_email;
	}

	public void setOrder_email(String order_email) {
		this.order_email = order_email;
	}
	
	public String getOrder_quantity() {
		return order_quantity;
	}

	public void setOrder_quantity(String order_quantity) {
		this.order_quantity = order_quantity;
	}
	public String tracking_link() {
		return tracking_link;
	}

	public void tracking_link(String tracking_link) {
		this.tracking_link = tracking_link;
	}

	public String getSales_channel() {
		return sales_channel;
	}

	public void setSales_channel(String sales_channel) {
		this.sales_channel = sales_channel;
	}

}
