package com.nimble.irisservices.dto;

public class JGatewayOverview {
	
	private long gatewayCnt;
	private long WgatewayCnt;
	private long NWgatewayCnt;
	
	public JGatewayOverview(long gatewayCnt, long wgatewayCnt, long nWgatewayCnt) {
		super();
		this.gatewayCnt = gatewayCnt;
		WgatewayCnt = wgatewayCnt;
		NWgatewayCnt = nWgatewayCnt;
	}

	public long getGatewayCnt() {
		return gatewayCnt;
	}

	public long getWgatewayCnt() {
		return WgatewayCnt;
	}

	public long getNWgatewayCnt() {
		return NWgatewayCnt;
	}
	
	
	
}
