package com.nimble.irisservices.dto;

public class JLocationBasedTemperature {

	private String unit;
	private String temperature;
	private String description;
	private String icon;
	private String iconUrl;
	
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getIcon() {
		return icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}
	public String getIconUrl() {
		return iconUrl;
	}
	public void setIconUrl(String iconUrl) {
		this.iconUrl = iconUrl;
	}
	public String getUnit() {
		return unit;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}
	public String getTemperature() {
		return temperature;
	}
	public void setTemperature(String temperature) {
		this.temperature = temperature;
	}
	public JLocationBasedTemperature(String unit, String temperature) {
		super();
		this.unit = unit;
		this.temperature = temperature;
	}
	public JLocationBasedTemperature() {
		// TODO Auto-generated constructor stub
	}

	

}
