package com.nimble.irisservices.dto;

import java.io.Serializable;

public class JVpmSubscription implements Serializable {

	private static final long serialVersionUID = 0l;
	
	String price="NA";
	int remaining_cnt=0;
	int used_cnt =0;
	int total_cnt=0;
	String status="INACTIVE";
	String nextRenewalDate="NA";
	String str_total_cnt="NA";
	int status_code = 0;
	String autoRenewalStatus = "Disabled";
	String billingPeriod = "Monthly";
	boolean vetchat_cancel = false;
	String cbvet_cancel_id = "";
	String cbvet_product_id = "";
	boolean cb_vetchat =false;
	public JVpmSubscription()
	{
		super();
	}
	
	public JVpmSubscription(String price,int total_cnt,int available,int used,String status,String nextRenewalDate,
			String str_total_cnt,int status_code,String autoRenewalStatus,String billingPeriod,boolean vetchat_cancel,
			String cbvet_cancel_id,String cbvet_product_id,boolean cb_vetchat)
	{
		super();		
		this.price = price;
		this.total_cnt = total_cnt;
		this.remaining_cnt = available;
		this.used_cnt = used;
		this.status = status;
		this.nextRenewalDate = nextRenewalDate;
		this.str_total_cnt=str_total_cnt;
		this.status_code = status_code;
		this.autoRenewalStatus = autoRenewalStatus;
		this.billingPeriod = billingPeriod;
		this.vetchat_cancel = vetchat_cancel;
		this.cbvet_cancel_id = cbvet_cancel_id;
		this.cbvet_product_id = cbvet_product_id;
		this.cb_vetchat = cb_vetchat;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public int getRemaining_cnt() {
		return remaining_cnt;
	}

	public void setRemaining_cnt(int remaining_cnt) {
		this.remaining_cnt = remaining_cnt;
	}

	public int getUsed_cnt() {
		return used_cnt;
	}

	public void setUsed_cnt(int used_cnt) {
		this.used_cnt = used_cnt;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public int getTotal_cnt() {
		return total_cnt;
	}

	public void setTotal_cnt(int total_cnt) {
		this.total_cnt = total_cnt;
	}

	public String getStr_total_cnt() {
		return str_total_cnt;
	}

	public void setStr_total_cnt(String str_total_cnt) {
		this.str_total_cnt = str_total_cnt;
	}

	public String getNextRenewalDate() {
		return nextRenewalDate;
	}

	public void setNextRenewalDate(String nextRenewalDate) {
		this.nextRenewalDate = nextRenewalDate;
	}

	public int getStatus_code() {
		return status_code;
	}

	public void setStatus_code(int status_code) {
		this.status_code = status_code;
	}

	public String getAutoRenewalStatus() {
		return autoRenewalStatus;
	}

	public void setAutoRenewalStatus(String autoRenewalStatus) {
		this.autoRenewalStatus = autoRenewalStatus;
	}

	public String getBillingPeriod() {
		return billingPeriod;
	}

	public void setBillingPeriod(String billingPeriod) {
		this.billingPeriod = billingPeriod;
	}

	public boolean isVetchat_cancel() {
		return vetchat_cancel;
	}

	public void setVetchat_cancel(boolean vetchat_cancel) {
		this.vetchat_cancel = vetchat_cancel;
	}

	public String getCbvet_cancel_id() {
		return cbvet_cancel_id;
	}

	public void setCbvet_cancel_id(String cbvet_cancel_id) {
		this.cbvet_cancel_id = cbvet_cancel_id;
	}

	public String getCbvet_product_id() {
		return cbvet_product_id;
	}

	public void setCbvet_product_id(String cbvet_product_id) {
		this.cbvet_product_id = cbvet_product_id;
	}

	public boolean isCb_vetchat() {
		return cb_vetchat;
	}

	public void setCb_vetchat(boolean cb_vetchat) {
		this.cb_vetchat = cb_vetchat;
	}

}
