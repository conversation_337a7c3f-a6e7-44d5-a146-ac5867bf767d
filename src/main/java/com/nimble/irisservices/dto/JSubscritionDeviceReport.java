package com.nimble.irisservices.dto;

import java.io.Serializable;

import com.nimble.irisservices.entity.Subscription;

public class JSubscritionDeviceReport implements Serializable {

	private long id;
	private String meid;
	private String assetid;
	private String reportdatetime; /* local date */
	private String timeoffset;
	private String gpsstatus;
	private double lat;
	private String latdir;
	private double lon;
	private String londir;
	private String heading;
	private int battery;
	private String probetype;
	private float temperature;
	private float speed;
	private float distance;
	private String Tdisplayunit;

	private String eventid;
	private String signalstrength;

	private String subcriptionStatus;

	private String subscriptionMessage;

	private String qrcCode;

	private Subscription sub;

	private String daysRemaining;

	private boolean setupAutoRenewal;
	
	private boolean setupActivate;

	private String autoRenewalURL;
	
	private String upgradeSubscriptionUrl;
	
	private String activateSubscriptionUrl;
	
	
	public JSubscritionDeviceReport() {
		super();
		// TODO Auto-generated constructor stub
	}

	public JSubscritionDeviceReport(long id, String meid, String assetid, String reportdatetime, String timeoffset,
			String gpsstatus, double lat, String latdir, double lon, String londir, String heading, int battery,
			String probetype, float temperature, float speed, float distance, String tdisplayunit, String eventid,
			String signalstrength) {
		super();
		this.id = id;
		this.meid = meid;
		this.assetid = assetid;
		this.reportdatetime = reportdatetime;
		this.timeoffset = timeoffset;
		this.gpsstatus = gpsstatus;
		this.lat = lat;
		this.latdir = latdir;
		this.lon = lon;
		this.londir = londir;
		this.heading = heading;
		this.battery = battery;
		this.probetype = probetype;
		this.temperature = temperature;
		this.speed = speed;
		this.distance = distance;
		this.Tdisplayunit = tdisplayunit;
		this.eventid = eventid;
		this.signalstrength = signalstrength;

	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getAssetid() {
		return assetid;
	}

	public void setAssetid(String assetid) {
		this.assetid = assetid;
	}

	public String getReportdatetime() {
		return reportdatetime;
	}

	public void setReportdatetime(String reportdatetime) {
		this.reportdatetime = reportdatetime;
	}

	public String getTimeoffset() {
		return timeoffset;
	}

	public void setTimeoffset(String timeoffset) {
		this.timeoffset = timeoffset;
	}

	public String getGpsstatus() {
		return gpsstatus;
	}

	public void setGpsstatus(String gpsstatus) {
		this.gpsstatus = gpsstatus;
	}

	public double getLat() {
		return lat;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public String getLatdir() {
		return latdir;
	}

	public void setLatdir(String latdir) {
		this.latdir = latdir;
	}

	public double getLon() {
		return lon;
	}

	public void setLon(double lon) {
		this.lon = lon;
	}

	public String getLondir() {
		return londir;
	}

	public void setLondir(String londir) {
		this.londir = londir;
	}

	public String getHeading() {
		return heading;
	}

	public void setHeading(String heading) {
		this.heading = heading;
	}

	public int getBattery() {
		return battery;
	}

	public void setBattery(int battery) {
		this.battery = battery;
	}

	public String getProbetype() {
		return probetype;
	}

	public void setProbetype(String probetype) {
		this.probetype = probetype;
	}

	public float getTemperature() {
		return temperature;
	}

	public void setTemperature(float temperature) {
		this.temperature = temperature;
	}

	public float getSpeed() {
		return speed;
	}

	public void setSpeed(float speed) {
		this.speed = speed;
	}

	public float getDistance() {
		return distance;
	}

	public void setDistance(float distance) {
		this.distance = distance;
	}

	public String getTdisplayunit() {
		return Tdisplayunit;
	}

	public void setTdisplayunit(String tdisplayunit) {
		Tdisplayunit = tdisplayunit;
	}

	public String getEventid() {
		return eventid;
	}

	public void setEventid(String eventid) {
		this.eventid = eventid;
	}

	public String getSignalstrength() {
		return signalstrength;
	}

	public void setSignalstrength(String signalstrength) {
		this.signalstrength = signalstrength;
	}

	public Subscription getSub() {
		return sub;
	}

	public void setSub(Subscription sub) {
		this.sub = sub;
	}

	public String getSubcriptionStatus() {
		return subcriptionStatus;
	}

	public void setSubcriptionStatus(String subcriptionStatus) {
		this.subcriptionStatus = subcriptionStatus;
	}

	public String getSubscriptionMessage() {
		return subscriptionMessage;
	}

	public void setSubscriptionMessage(String subscriptionMessage) {
		this.subscriptionMessage = subscriptionMessage;
	}

	public String getQrcCode() {
		return qrcCode;
	}

	public void setQrcCode(String qrcCode) {
		this.qrcCode = qrcCode;
	}

	public String getDaysRemaining() {
		return daysRemaining;
	}

	public void setDaysRemaining(String daysRemaining) {
		this.daysRemaining = daysRemaining;
	}

	public boolean isSetupAutoRenewal() {
		return setupAutoRenewal;
	}

	public void setSetupAutoRenewal(boolean setupAutoRenewal) {
		this.setupAutoRenewal = setupAutoRenewal;
	}

	public String getAutoRenewalURL() {
		return autoRenewalURL;
	}

	public void setAutoRenewalURL(String autoRenewalURL) {
		this.autoRenewalURL = autoRenewalURL;
	}

	public String getUpgradeSubscriptionUrl() {
		return upgradeSubscriptionUrl;
	}

	public void setUpgradeSubscriptionUrl(String upgradeSubscriptionUrl) {
		this.upgradeSubscriptionUrl = upgradeSubscriptionUrl;
	}

	public String getActivateSubscriptionUrl() {
		return activateSubscriptionUrl;
	}

	public void setActivateSubscriptionUrl(String activateSubscriptionUrl) {
		this.activateSubscriptionUrl = activateSubscriptionUrl;
	}

	public boolean isSetupActivate() {
		return setupActivate;
	}

	public void setSetupActivate(boolean setupActivate) {
		this.setupActivate = setupActivate;
	}



	


}
