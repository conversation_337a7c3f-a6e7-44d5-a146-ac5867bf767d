package com.nimble.irisservices.dto;

public class JGatewayGeneralInfo {
	
	private float foodIntake = 0;
	private Double caloriesConsumed = 0d;
	private Double actualCalories = 0d;
	private float currentWeight = 0;
	private float initialPortion = 0;
	private Double equvalentCalory = 0d;
	private String lastUpdated = "";
	private Double foodCal = 0d;
	private long gatewayId = 0;
	private int battery = 0;
	private int wifiRange = 0;
	private String content_1 = "Food Logging";
	private String content_2 = "View Detailed Report";
	private String content_3 = "Food Intake";
	private String content_4 = "Calories Consumed";
	private String content_5 = "Current Weight";
	private String content_6 = "Last Updated";
	private String content_7 = "Initial Portion";
	private String content_8 = "Equivalent Calories";
	private String content_9 = "grams";
	private String content_10 = "kcal";

	
	public int getBattery() {
		return battery;
	}
	public void setBattery(int battery) {
		this.battery = battery;
	}
	public int getWifiRange() {
		return wifiRange;
	}
	public void setWifiRange(int wifiRange) {
		this.wifiRange = wifiRange;
	}
	public String getContent_9() {
		return content_9;
	}
	public void setContent_9(String content_9) {
		this.content_9 = content_9;
	}
	public String getContent_10() {
		return content_10;
	}
	public void setContent_10(String content_10) {
		this.content_10 = content_10;
	}
	public float getFoodIntake() {
		return foodIntake;
	}
	public void setFoodIntake(float foodIntake) {
		this.foodIntake = foodIntake;
	}
	public Double getCaloriesConsumed() {
		return caloriesConsumed;
	}
	public void setCaloriesConsumed(Double caloriesConsumed) {
		this.caloriesConsumed = caloriesConsumed;
	}
	public Double getActualCalories() {
		return actualCalories;
	}
	public void setActualCalories(Double actualCalories) {
		this.actualCalories = actualCalories;
	}
	public float getCurrentWeight() {
		return currentWeight;
	}
	public void setCurrentWeight(float currentWeight) {
		this.currentWeight = currentWeight;
	}
	public float getInitialPortion() {
		return initialPortion;
	}
	public void setInitialPortion(float initialPortion) {
		this.initialPortion = initialPortion;
	}
	public Double getEquvalentCalory() {
		return equvalentCalory;
	}
	public void setEquvalentCalory(Double equvalentCalory) {
		this.equvalentCalory = equvalentCalory;
	}
	public String getLastUpdated() {
		return lastUpdated;
	}
	public void setLastUpdated(String dateStr) {
		this.lastUpdated = dateStr;
	}
	public Double getFoodCal() {
		return foodCal;
	}
	public void setFoodCal(Double lastReport) {
		this.foodCal = lastReport;
	}
	public long getGatewayId() {
		return gatewayId;
	}
	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}
	public String getContent_1() {
		return content_1;
	}


	public void setContent_1(String content_1) {
		this.content_1 = content_1;
	}


	public String getContent_2() {
		return content_2;
	}


	public void setContent_2(String content_2) {
		this.content_2 = content_2;
	}


	public String getContent_3() {
		return content_3;
	}


	public void setContent_3(String content_3) {
		this.content_3 = content_3;
	}


	public String getContent_4() {
		return content_4;
	}


	public void setContent_4(String content_4) {
		this.content_4 = content_4;
	}


	public String getContent_5() {
		return content_5;
	}


	public void setContent_5(String content_5) {
		this.content_5 = content_5;
	}


	public String getContent_6() {
		return content_6;
	}


	public void setContent_6(String content_6) {
		this.content_6 = content_6;
	}


	public String getContent_7() {
		return content_7;
	}


	public void setContent_7(String content_7) {
		this.content_7 = content_7;
	}


	public String getContent_8() {
		return content_8;
	}


	public void setContent_8(String content_8) {
		this.content_8 = content_8;
	}

	
}
