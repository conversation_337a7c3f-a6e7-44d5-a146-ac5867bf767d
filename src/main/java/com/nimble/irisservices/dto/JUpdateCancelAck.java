package com.nimble.irisservices.dto;

public class JUpdateCancelAck {

	
	private String chargebee_plan_id = "NA" ;
	
	private String offer = "NA";
	
	private long cancel_feedback_id = 0;
	
	private String customer_review = "NA";
	
	private boolean is_cancel = true;
	
	private boolean btn_cancel = false;
	
	private boolean apply_coupon = false;
	
	private long planid = 0;
	
	private long periodid = 0;
	
	private String subs_id = "NA";
	
	private String offer_device = "NA";

	public String getChargebee_plan_id() {
		return chargebee_plan_id;
	}

	public void setChargebee_plan_id(String chargebee_plan_id) {
		this.chargebee_plan_id = chargebee_plan_id;
	}

	public String getOffer() {
		return offer;
	}

	public void setOffer(String offer) {
		this.offer = offer;
	}
	
	public long getCancel_feedback_id() {
		return cancel_feedback_id;
	}

	public void setCancel_feedback_id(long cancel_feedback_id) {
		this.cancel_feedback_id = cancel_feedback_id;
	}

	public String getCustomer_review() {
		return customer_review;
	}

	public void setCustomer_review(String customer_review) {
		this.customer_review = customer_review;
	}

	public boolean isIs_cancel() {
		return is_cancel;
	}

	public void setIs_cancel(boolean is_cancel) {
		this.is_cancel = is_cancel;
	}

	public boolean isBtn_cancel() {
		return btn_cancel;
	}

	public void setBtn_cancel(boolean btn_cancel) {
		this.btn_cancel = btn_cancel;
	}

	public boolean isApply_coupon() {
		return apply_coupon;
	}

	public void setApply_coupon(boolean apply_coupon) {
		this.apply_coupon = apply_coupon;
	}

	public long getPlanid() {
		return planid;
	}

	public void setPlanid(long planid) {
		this.planid = planid;
	}

	public long getPeriodid() {
		return periodid;
	}

	public void setPeriodid(long periodid) {
		this.periodid = periodid;
	}

	public String getSubs_id() {
		return subs_id;
	}

	public void setSubs_id(String subs_id) {
		this.subs_id = subs_id;
	}

	public String getOffer_device() {
		return offer_device;
	}

	public void setOffer_device(String offer_device) {
		this.offer_device = offer_device;
	}

	public JUpdateCancelAck() {
		super();
	}
	
	public JUpdateCancelAck(String chargebee_plan_id, String offer, String customer_review, long cancel_feedback_id) {
		super();
		this.chargebee_plan_id = chargebee_plan_id;
		this.offer = offer;
		this.customer_review = customer_review;
		this.cancel_feedback_id = cancel_feedback_id;
	}
	
	
}
