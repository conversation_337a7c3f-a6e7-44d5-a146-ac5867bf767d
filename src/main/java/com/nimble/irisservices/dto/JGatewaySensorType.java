package com.nimble.irisservices.dto;

import java.io.Serializable;

@SuppressWarnings("serial")
public class JGatewaySensorType implements Serializable {
	private long gatewayId = 0;
	private long sensorId = 0;
	private String sensorName = "NA";
	private long monitorId = 0;
	public long getGatewayId() {
		return gatewayId;
	}
	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}
	public long getSensorId() {
		return sensorId;
	}
	public void setSensorId(long sensorId) {
		this.sensorId = sensorId;
	}
	public String getSensorName() {
		return sensorName;
	}
	public void setSensorName(String sensorName) {
		this.sensorName = sensorName;
	}
	public long getMonitorId() {
		return monitorId;
	}
	public void setMonitorId(long monitorId) {
		this.monitorId = monitorId;
	}

	
}
