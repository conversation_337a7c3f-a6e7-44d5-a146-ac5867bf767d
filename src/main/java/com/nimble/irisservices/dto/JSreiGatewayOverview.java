package com.nimble.irisservices.dto;

public class JSreiGatewayOverview {

	private long sGatewayG;
	private long sGatewayY;
	private long sGatewayR;
	
	
	public JSreiGatewayOverview(long sGatewayG, long sGatewayY, long sGatewayR) {
		super();
		this.sGatewayG = sGatewayG;
		this.sGatewayY = sGatewayY;
		this.sGatewayR = sGatewayR;
	}
	
	public long getsGatewayG() {
		return sGatewayG;
	}
	public long getsGatewayY() {
		return sGatewayY;
	}
	public long getsGatewayR() {
		return sGatewayR;
	}

}
