package com.nimble.irisservices.dto;

import java.util.ArrayList;
import java.util.List;

public class JPowerModeConfig {

	private long id;
	private String shortName;
	private int reportInterval;
	private String description;
	private int orderNo;
	private String modeName;
	private String img_selected;
	private String img_unselected;
	private String description_2;
	private List<String> update_info_list = new ArrayList<>();
	
	public JPowerModeConfig() {
		super();
		// TODO Auto-generated constructor stub
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public int getReportInterval() {
		return reportInterval;
	}

	public void setReportInterval(int reportInterval) {
		this.reportInterval = reportInterval;
	}

	public int getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(int orderNo) {
		this.orderNo = orderNo;
	}

	public String getModeName() {
		return modeName;
	}

	public void setModeName(String modeName) {
		this.modeName = modeName;
	}

	public String getImg_selected() {
		return img_selected;
	}

	public void setImg_selected(String img_selected) {
		this.img_selected = img_selected;
	}

	public String getImg_unselected() {
		return img_unselected;
	}

	public void setImg_unselected(String img_unselected) {
		this.img_unselected = img_unselected;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getDescription_2() {
		return description_2;
	}

	public void setDescription_2(String description_2) {
		this.description_2 = description_2;
	}

	public List<String> getUpdate_info_list() {
		return update_info_list;
	}

	public void setUpdate_info_list(List<String> update_info_list) {
		this.update_info_list = update_info_list;
	}



}

