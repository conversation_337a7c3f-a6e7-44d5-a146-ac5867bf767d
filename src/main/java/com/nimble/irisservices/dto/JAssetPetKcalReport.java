package com.nimble.irisservices.dto;

import java.io.Serializable;

@SuppressWarnings("serial")
public class JAssetPetKcalReport implements Serializable {
	
	private Double actualCalories = 0d;
	private long profileId = 0;
	private String imgUrl = "NA";
	private String content_1 = "Calories needed per day";
	private String shop_url = "https://www.wagglemerch.com/products/smart-ai-bowl";
	private String product_link = "NA";
	private String profileName = "NA";
	
	public Double getActualCalories() {
		return actualCalories;
	}
	public void setActualCalories(Double actualCalories) {
		this.actualCalories = actualCalories;
	}
	public long getProfileId() {
		return profileId;
	}
	public void setProfileId(long profileId) {
		this.profileId = profileId;
	}
	public String getImgUrl() {
		return imgUrl;
	}
	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}
	public String getContent_1() {
		return content_1;
	}
	public void setContent_1(String content_1) {
		this.content_1 = content_1;
	}
	public String getShop_url() {
		return shop_url;
	}
	public void setShop_url(String shop_url) {
		this.shop_url = shop_url;
	}
	public String getProduct_link() {
		return product_link;
	}
	public void setProduct_link(String product_link) {
		this.product_link = product_link;
	}
	public String getProfileName() {
		return profileName;
	}
	public void setProfileName(String profileName) {
		this.profileName = profileName;
	}

}
