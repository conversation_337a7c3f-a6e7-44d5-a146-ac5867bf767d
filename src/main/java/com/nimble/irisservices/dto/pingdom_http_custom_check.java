package com.nimble.irisservices.dto;


import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement
public class pingdom_http_custom_check {
	
	private String status;
	private double response_time;
	
	
	public String getStatus() {
		return status;
	}
	public void setStatus(String string) {
		this.status = string;
	}
	public double getResponse_time() {
		return response_time;
	}
	public void setResponse_time(double response_time) {
		this.response_time = response_time;
	}
	
//	Map<String,Object> response;
//
//	
//	public JResponse() {
//		super();
//		 response = new HashMap<String, Object>();
//		// TODO Auto-generated constructor stub
//	}
//
//	/*public JResponse(Map<String, Map<String, Object>> response) {
//		super();
//		this.response = response;
//	}
//*/
//	public Map<String, Object> getResponse() {
//		return response;
//	}
//
//	public void setResponse(Map<String, Object> response) {
//		this.response = response;
//	}
//
//	public void put(String string,  Object success) {
//		
//		response.put(string, success);
//	}

	}
