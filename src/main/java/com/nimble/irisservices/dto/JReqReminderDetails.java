package com.nimble.irisservices.dto;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

public class JReqReminderDetails {

	private long reminderid;

	private String remindername;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date scheduletime;

	private String repeatedtype;
	
	private long repeatedid;

	private String remindermsg;

	private String timezone;

	public String getRemindername() {
		return remindername;
	}

	public void setRemindername(String remindername) {
		this.remindername = remindername;
	}

	public Date getScheduletime() {
		return scheduletime;
	}

	public void setScheduletime(Date scheduletime) {
		this.scheduletime = scheduletime;
	}

	public String getRepeatedtype() {
		return repeatedtype;
	}

	public void setRepeatedtype(String repeatedtype) {
		this.repeatedtype = repeatedtype;
	}

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}

	public long getReminderid() {
		return reminderid;
	}

	public void setReminderid(long reminderid) {
		this.reminderid = reminderid;
	}

	public String getRemindermsg() {
		return remindermsg;
	}

	public void setRemindermsg(String remindermsg) {
		this.remindermsg = remindermsg;
	}

	public long getRepeatedid() {
		return repeatedid;
	}

	public void setRepeatedid(long repeatedid) {
		this.repeatedid = repeatedid;
	}

}