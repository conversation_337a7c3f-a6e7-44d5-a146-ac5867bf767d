package com.nimble.irisservices.dto;

import java.io.Serializable;

@SuppressWarnings("serial")
public class JAssetLastSensorReport implements Serializable{
	
	private long assetid = 0;
	private String assetname = "NA";
	private float foodIntake = 0;
	private Double caloriesConsumed = 0d;
	private Double actualCalories = 0d;
	private int battery = 0;
	private int wifiRange = 0;
	private String datetime = "NA";
	private Double foodCal = 0d;
	private String device_type = "NA";
	private long profileId = 0;
	private String imgUrl = "NA";
	private String qrcode = "NA";
	private BluetoothDeviceList bledevicelist = null; 
	private JPetprofileFlutter pet_profile = null;
	private long monitortype = 0;
	private boolean add_on = true;
	private boolean pb_notification = false;
	private Double reqGrams = 0d;
	private int perVal = 1;
	private boolean show_activate = false;
	private long planId = 0;
	private boolean pb_habit_notification = false;
	private boolean is_freePlan = true;
	private String sub_id = "NA";
	private String title="Bite plan exclusive";
	private String desc="Unlock Bite Plan and Get Notified on Very Bite it Takes.";
	private String btn="Upgrade Now";
	private String paid_title="Activate Your Plan";
	private String paid_desc="Are you worried about your pet's well-being? Try our Bite Plan and keep your pets healthy.";
	private String paid_btn="Say Yes to Pet Healthy";
	private long periodId = 0;
	private int default_report = 0;
	private String default_rpt_msg = "NA";
	private String default_rpt_label = "NA";
	private String lastUpdated = "NA";
	public int getBattery() {
		return battery;
	}
	public void setBattery(int battery) {
		this.battery = battery;
	}
	public int getWifiRange() {
		return wifiRange;
	}
	public void setWifiRange(int wifiRange) {
		this.wifiRange = wifiRange;
	}
	public float getFoodIntake() {
		return foodIntake;
	}
	public void setFoodIntake(float foodIntake) {
		this.foodIntake = foodIntake;
	}
	public Double getCaloriesConsumed() {
		return caloriesConsumed;
	}
	public void setCaloriesConsumed(Double caloriesConsumed) {
		this.caloriesConsumed = caloriesConsumed;
	}
	public Double getActualCalories() {
		return actualCalories;
	}
	public void setActualCalories(Double actualCalories) {
		this.actualCalories = actualCalories;
	}
	public long getAssetid() {
		return assetid;
	}
	public void setAssetid(long assetid) {
		this.assetid = assetid;
	}
	public String getAssetname() {
		return assetname;
	}
	public void setAssetname(String assetname) {
		this.assetname = assetname;
	}
	public String getDatetime() {
		return datetime;
	}
	public void setDatetime(String datetime) {
		this.datetime = datetime;
	}
	public Double getFoodCal() {
		return foodCal;
	}
	public void setFoodCal(Double foodCal) {
		this.foodCal = foodCal;
	}
	public String getDevice_type() {
		return device_type;
	}
	public void setDevice_type(String device_type) {
		this.device_type = device_type;
	}
	public long getProfileId() {
		return profileId;
	}
	public void setProfileId(long profileId) {
		this.profileId = profileId;
	}
	public String getImgUrl() {
		return imgUrl;
	}
	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}
	public String getQrcode() {
		return qrcode;
	}

	public void setQrcode(String qrcode) {
		this.qrcode = qrcode;
	}
	
	public JPetprofileFlutter getPet_profile() {
		return pet_profile;
	}
	public void setPet_profile(JPetprofileFlutter pet_profile) {
		this.pet_profile = pet_profile;
	}
	public long getMonitortype() {
		return monitortype;
	}
	public void setMonitortype(long monitortype) {
		this.monitortype = monitortype;
	}
	public BluetoothDeviceList getBledevicelist() {
		return bledevicelist;
	}
	public void setBledevicelist(BluetoothDeviceList bledevicelist) {
		this.bledevicelist = bledevicelist;
	}
	public boolean getAdd_on() {
		return add_on;
	}
	public void setAdd_on(boolean add_on) {
		this.add_on = add_on;
	}
	public boolean isPb_notification() {
		return pb_notification;
	}
	public void setPb_notification(boolean pb_notification) {
		this.pb_notification = pb_notification;
	}
	public Double getReqGrams() {
		return reqGrams;
	}
	public void setReqGrams(Double reqGrams) {
		this.reqGrams = reqGrams;
	}
	public int getPerVal() {
		return perVal;
	}
	public void setPerVal(int perVal) {
		this.perVal = perVal;
	}
	public boolean isShow_activate() {
		return show_activate;
	}
	public void setShow_activate(boolean show_activate) {
		this.show_activate = show_activate;
	}
	public long getPlanId() {
		return planId;
	}
	public void setPlanId(long planId) {
		this.planId = planId;
	}
	public boolean isPb_habit_notification() {
		return pb_habit_notification;
	}
	public void setPb_habit_notification(boolean pb_habit_notification) {
		this.pb_habit_notification = pb_habit_notification;
	}
	public boolean isIs_freePlan() {
		return is_freePlan;
	}
	public void setIs_freePlan(boolean is_freePlan) {
		this.is_freePlan = is_freePlan;
	}
	public String getSub_id() {
		return sub_id;
	}
	public void setSub_id(String sub_id) {
		this.sub_id = sub_id;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getDesc() {
		return desc;
	}
	public void setDesc(String desc) {
		this.desc = desc;
	}
	public String getBtn() {
		return btn;
	}
	public void setBtn(String btn) {
		this.btn = btn;
	}
	public String getPaid_title() {
		return paid_title;
	}
	public void setPaid_title(String paid_title) {
		this.paid_title = paid_title;
	}
	public String getPaid_desc() {
		return paid_desc;
	}
	public void setPaid_desc(String paid_desc) {
		this.paid_desc = paid_desc;
	}
	public String getPaid_btn() {
		return paid_btn;
	}
	public void setPaid_btn(String paid_btn) {
		this.paid_btn = paid_btn;
	}
	public long getPeriodId() {
		return periodId;
	}
	public void setPeriodId(long periodId) {
		this.periodId = periodId;
	}
	public int getDefault_report() {
		return default_report;
	}
	public void setDefault_report(int default_report) {
		this.default_report = default_report;
	}
	public String getDefault_rpt_msg() {
		return default_rpt_msg;
	}
	public void setDefault_rpt_msg(String default_rpt_msg) {
		this.default_rpt_msg = default_rpt_msg;
	}
	public String getDefault_rpt_label() {
		return default_rpt_label;
	}
	public void setDefault_rpt_label(String default_rpt_label) {
		this.default_rpt_label = default_rpt_label;
	}
	public String getLastUpdated() {
		return lastUpdated;
	}
	public void setLastUpdated(String lastUpdated) {
		this.lastUpdated = lastUpdated;
	}
}
