package com.nimble.irisservices.dto;

import java.util.ArrayList;
import java.util.List;

public class BluetoothDeviceList {

	public String ble_id = "";
	public String ble_version = "";
	public String meid;
	public String gatewayname;
	public String qrcode;
	public int remainingwificount = -1;
	public int totalwificount = -1;
	public boolean debugenable = false;
	public String debugcmd = "";
	public long gatewayid;
	public long userid;
	public String autoconnect = "The app and the Smart AI Bowl will be paired via Bluetooth,"
			+ " and the available WiFi networks will be fetched and listed.";
	public String manualconnect = "The app and the Smart AI Bowl will be paired via Bluetooth,"
			+ " and the WiFi router connection should be established manually.";
	public String imageurl = "NA";

	public boolean is_wifidelete = false;

	List<Wifiinfo> wifiinfolist = new ArrayList<Wifiinfo>();

	public boolean is_wifi_connection = false;

	public String wifi_ssid = "NA";

	public String wifi_password = "NA";

	public boolean is_manual_connection = false;

	public boolean is_static_wifi = false;
	
	public long pet_id = 0;
	
	public long food_id = 0;
	
	public BluetoothDeviceList() {
		super();
	}

	public BluetoothDeviceList(String ble_id, String ble_version, String meid, String gatewayname, String qrcode,
			int remainingwificount, int totalwificount, long gatewayId, long userid, List<Wifiinfo> wifiinfolist,
			boolean debugenable, String debugcmd, boolean is_wifidelete) {
		super();
		this.ble_id = ble_id;
		this.ble_version = ble_version;
		this.meid = meid;
		this.gatewayname = gatewayname;
		this.qrcode = qrcode;
		this.remainingwificount = remainingwificount;
		this.totalwificount = totalwificount;
		this.gatewayid = gatewayId;
		this.userid = userid;
		this.wifiinfolist = wifiinfolist;
		this.debugenable = debugenable;
		this.debugcmd = debugcmd;
		this.is_wifidelete = is_wifidelete;
	}

	public int getRemainingwificount() {
		return remainingwificount;
	}

	public void setRemainingwificount(int remainingwificount) {
		this.remainingwificount = remainingwificount;
	}

	public long getGatewayid() {
		return gatewayid;
	}

	public void setGatewayid(long gatewayid) {
		this.gatewayid = gatewayid;
	}

	public long getUserid() {
		return userid;
	}

	public void setUserid(long userid) {
		this.userid = userid;
	}

	public String getBle_id() {
		return ble_id;
	}

	public void setBle_id(String ble_id) {
		this.ble_id = ble_id;
	}

	public String getBle_version() {
		return ble_version;
	}

	public void setBle_version(String ble_version) {
		this.ble_version = ble_version;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getGatewayname() {
		return gatewayname;
	}

	public void setGatewayname(String gatewayname) {
		this.gatewayname = gatewayname;
	}

	public String getQrcode() {
		return qrcode;
	}

	public void setQrcode(String qrcode) {
		this.qrcode = qrcode;
	}

	public int getTotalwificount() {
		return totalwificount;
	}

	public void setTotalwificount(int totalwificount) {
		this.totalwificount = totalwificount;
	}

	public List<Wifiinfo> getWifiinfolist() {
		return wifiinfolist;
	}

	public void setWifiinfolist(List<Wifiinfo> wifiinfolist) {
		this.wifiinfolist = wifiinfolist;
	}

	public boolean isDebugenable() {
		return debugenable;
	}

	public void setDebugenable(boolean debugenable) {
		this.debugenable = debugenable;
	}

	public String getDebugcmd() {
		return debugcmd;
	}

	public void setDebugcmd(String debugcmd) {
		this.debugcmd = debugcmd;
	}

	public String getAutoconnect() {
		return autoconnect;
	}

	public void setAutoconnect(String autoconnect) {
		this.autoconnect = autoconnect;
	}

	public String getManualconnect() {
		return manualconnect;
	}

	public void setManualconnect(String manualconnect) {
		this.manualconnect = manualconnect;
	}

	public String getImageurl() {
		return imageurl;
	}

	public void setImageurl(String imageurl) {
		this.imageurl = imageurl;
	}

	public boolean isIs_wifidelete() {
		return is_wifidelete;
	}

	public void setIs_wifidelete(boolean is_wifidelete) {
		this.is_wifidelete = is_wifidelete;
	}

	public boolean isIs_wifi_connection() {
		return is_wifi_connection;
	}

	public void setIs_wifi_connection(boolean is_wifi_connection) {
		this.is_wifi_connection = is_wifi_connection;
	}

	public String getWifi_ssid() {
		return wifi_ssid;
	}

	public void setWifi_ssid(String wifi_ssid) {
		this.wifi_ssid = wifi_ssid;
	}

	public String getWifi_password() {
		return wifi_password;
	}

	public void setWifi_password(String wifi_password) {
		this.wifi_password = wifi_password;
	}

	public boolean isIs_manual_connection() {
		return is_manual_connection;
	}

	public void setIs_manual_connection(boolean is_manual_connection) {
		this.is_manual_connection = is_manual_connection;
	}

	public boolean isIs_static_wifi() {
		return is_static_wifi;
	}

	public void setIs_static_wifi(boolean is_static_wifi) {
		this.is_static_wifi = is_static_wifi;
	}

	public long getPet_id() {
		return pet_id;
	}

	public void setPet_id(long pet_id) {
		this.pet_id = pet_id;
	}

	public long getFood_id() {
		return food_id;
	}

	public void setFood_id(long food_id) {
		this.food_id = food_id;
	}
	
}
