package com.nimble.irisservices.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class JSubPeriodDetail implements Serializable {

	private static final long serialVersionUID = 0l;

	private long freeplan_id=0;

	private String free_buynow = "";

	private long period_id=0;

	private long paidplan_id=0;

	private String period_name="";

	private String paid_buynow = "";

	private String strike_price="";

	private String plan_price = "";

	private String display_price="";

	private String display_msg ="";

	private String billed_price = "";

	private String month_price = "";

	private boolean is_best_seller;

	private String compare_image="";

	private String product_image="";

	private List<String> description;

	private boolean free_minicam_avil = false;

	private boolean isFlexiPlan=false;

	private String planName = "NA";

	private List<Integer> monitorIdList = new ArrayList<>();

	private List<Object> productList = new ArrayList<>();
	
	public JSubPeriodDetail() {
		
	}

	public JSubPeriodDetail(long freeplan_id, long period_id, String free_buynow, long paidplan_id,
			String period_name,	String paid_buynow, String strike_price, String plan_price, String display_price,String display_msg,String billed_price,String month_price,boolean is_best_seller) {
		super();
		this.freeplan_id = freeplan_id;
		this.period_id = period_id;
		this.free_buynow = free_buynow;
		this.paidplan_id = paidplan_id;
		this.period_name = period_name;
		this.paid_buynow = paid_buynow;
		this.strike_price = strike_price;
		this.plan_price = plan_price;
		this.display_price = display_price;
		this.display_msg = display_msg;
		this.billed_price = billed_price;
		this.month_price = month_price;
		this.is_best_seller = is_best_seller;
	}

	public JSubPeriodDetail(long freeplan_id, long period_id, String free_buynow, long paidplan_id,
							String period_name,	String paid_buynow, String strike_price, String plan_price, String display_price,String display_msg,String billed_price,String month_price,boolean is_best_seller,
							List<String> description, String compare_image, String product_image) {
		super();
		this.freeplan_id = freeplan_id;
		this.period_id = period_id;
		this.free_buynow = free_buynow;
		this.paidplan_id = paidplan_id;
		this.period_name = period_name;
		this.paid_buynow = paid_buynow;
		this.strike_price = strike_price;
		this.plan_price = plan_price;
		this.display_price = display_price;
		this.display_msg = display_msg;
		this.billed_price = billed_price;
		this.month_price = month_price;
		this.is_best_seller = is_best_seller;
		this.description = description;
		this.compare_image = compare_image;
		this.product_image = product_image;
	}

	public JSubPeriodDetail(long freeplan_id, long period_id, String free_buynow, long paidplan_id,
							String period_name,	String paid_buynow, String strike_price, String plan_price, String display_price,String display_msg,String billed_price,String month_price,boolean is_best_seller,
							List<String> description, String compare_image, String product_image,boolean free_minicam_avil) {
		super();
		this.freeplan_id = freeplan_id;
		this.period_id = period_id;
		this.free_buynow = free_buynow;
		this.paidplan_id = paidplan_id;
		this.period_name = period_name;
		this.paid_buynow = paid_buynow;
		this.strike_price = strike_price;
		this.plan_price = plan_price;
		this.display_price = display_price;
		this.display_msg = display_msg;
		this.billed_price = billed_price;
		this.month_price = month_price;
		this.is_best_seller = is_best_seller;
		this.description = description;
		this.compare_image = compare_image;
		this.product_image = product_image;
		this.free_minicam_avil = free_minicam_avil;
	}

	public String getStrike_price() {
		return strike_price;
	}

	public void setStrike_price(String strike_price) {
		this.strike_price = strike_price;
	}

	public String getPlan_price() {
		return plan_price;
	}

	public void setPlan_price(String plan_price) {
		this.plan_price = plan_price;
	}

	public String getDisplay_price() {
		return display_price;
	}

	public void setDisplay_price(String display_price) {
		this.display_price = display_price;
	}

	public long getFreeplan_id() {
		return freeplan_id;
	}

	public void setFreeplan_id(long freeplan_id) {
		this.freeplan_id = freeplan_id;
	}

	public long getPeriod_id() {
		return period_id;
	}

	public void setPeriod_id(long period_id) {
		this.period_id = period_id;
	}

	public String getFree_buynow() {
		return free_buynow;
	}

	public void setFree_buynow(String free_buynow) {
		this.free_buynow = free_buynow;
	}

	public long getPaidplan_id() {
		return paidplan_id;
	}

	public void setPaidplan_id(long paidplan_id) {
		this.paidplan_id = paidplan_id;
	}

	public String getPeriod_name() {
		return period_name;
	}

	public void setPeriod_name(String period_name) {
		this.period_name = period_name;
	}

	public String getPaid_buynow() {
		return paid_buynow;
	}

	public void setPaid_buynow(String paid_buynow) {
		this.paid_buynow = paid_buynow;
	}

	public String getDisplay_msg() {
		return display_msg;
	}

	public void setDisplay_msg(String display_msg) {
		this.display_msg = display_msg;
	}

	public String getBilled_price() {
		return billed_price;
	}

	public void setBilled_price(String billed_price) {
		this.billed_price = billed_price;
	}

	public String getMonth_price() {
		return month_price;
	}

	public void setMonth_price(String month_price) {
		this.month_price = month_price;
	}

	public boolean isIs_best_seller() {
		return is_best_seller;
	}

	public void setIs_best_seller(boolean is_best_seller) {
		this.is_best_seller = is_best_seller;
	}

	public String getCompare_image() {
		return compare_image;
	}

	public void setCompare_image(String compare_image) {
		this.compare_image = compare_image;
	}

	public String getProduct_image() {
		return product_image;
	}

	public void setProduct_image(String product_image) {
		this.product_image = product_image;
	}

	public List<String> getDescription() {
		return description;
	}

	public void setDescription(List<String> description) {
		this.description = description;
	}

	public boolean isFree_minicam_avil() {
		return free_minicam_avil;
	}

	public void setFree_minicam_avil(boolean free_minicam_avil) {
		this.free_minicam_avil = free_minicam_avil;
	}

	public boolean isFlexiPlan() {
		return isFlexiPlan;
	}

	public void setFlexiPlan(boolean flexiPlan) {
		isFlexiPlan = flexiPlan;
	}

	public List<Integer> getMonitorIdList() {
		return monitorIdList;
	}

	public void setMonitorIdList(List<Integer> monitorIdList) {
		this.monitorIdList = monitorIdList;
	}

	public List<Object> getProductList() {
		return productList;
	}

	public void setProductList(List<Object> productList) {
		this.productList = productList;
	}

	public String getPlanName() {
		return planName;
	}

	public void setPlanName(String planName) {
		this.planName = planName;
	}
}
