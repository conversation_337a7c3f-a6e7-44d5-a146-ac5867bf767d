package com.nimble.irisservices.dto;

public class JPushNotifications {
	
	private long notificationTypeId;

	private String shortDescription;

	private String title;

	private String message;
	
	private String bannerImageUrl;

	private String imageUrl;

	private String expiryOn;
	
	private String hyperLink;

	/*
	 * @ManyToMany(mappedBy = "gateways") //@JsonIgnore private Set<User> users
	 * = new HashSet<User>();
	 */

	public JPushNotifications() {
		super();
		// TODO Auto-generated constructor stub
	}



	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getBannerImageUrl() {
		return bannerImageUrl;
	}

	public void setBannerImageUrl(String bannerImageUrl) {
		this.bannerImageUrl = bannerImageUrl;
	}

	public String getImageUrl() {
		return imageUrl;
	}

	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}

	public String getExpiryOn() {
		return expiryOn;
	}

	public void setExpiryOn(String expiryOn) {
		this.expiryOn = expiryOn;
	}

	public long getNotificationTypeId() {
		return notificationTypeId;
	}

	public void setNotificationTypeId(long notificationTypeId) {
		this.notificationTypeId = notificationTypeId;
	}




	public String getShortDescription() {
		return shortDescription;
	}



	public void setShortDescription(String shortDescription) {
		this.shortDescription = shortDescription;
	}



	public String getHyperLink() {
		return hyperLink;
	}



	public void setHyperLink(String hyperLink) {
		this.hyperLink = hyperLink;
	}





}
