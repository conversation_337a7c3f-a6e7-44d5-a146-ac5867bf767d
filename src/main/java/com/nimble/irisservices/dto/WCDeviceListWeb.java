package com.nimble.irisservices.dto;

import java.util.ArrayList;
import java.util.List;

import com.nimble.irisservices.entity.WifiInfo;

public class WCDeviceListWeb {
	
	public String meid;
	public String macid;
	public String gatewayname;
	public String qrcode;
	public int remainingwificount = -1;
	public int totalwificount = -1;
	public long gatewayid;
	public long userid;
	private String installed_date = "NA";
	private long monitortype = 0; 
	
	public String manualconnect = "NA";
	public String imageurl = "NA";

	public boolean is_wifidelete = false;

	List<WifiInfo> wifiinfolist = new ArrayList<WifiInfo>();

	public String wifi_ssid = "NA";

	public String wifi_password = "NA";
	
	public boolean is_wifi_connection = true;

	public boolean is_manual_connection = false;

	public boolean is_static_wifi = false;
	
	public String wifi_pairing_img = "https://tempcubeapi-images.s3.us-west-2.amazonaws.com/mobileappimages/ble_pairing_default.png";
	
	public boolean warranty_claimed;
	
	public boolean purchased_from_others;
	
	public String warranty_claim_msg = "NA";
	
	private boolean streaming = false;
	
	private boolean night_vision = false;
	
	private boolean mic_status = false;
	
	private int speaker_volume = 100;
	
	private String quality = "hd";
	
	private int device_angle = 0;
	
	private int min_rotation = 0;
	
	private int max_rotation = 0;
	
	private boolean auto_update = false;
	
	private String fota_status = "NA";
	
	private String current_fota_version = "NA";
	
	private boolean auto_night_vision = false;
	
	private boolean show_hd = false;
	
	private String device_for = "NA";
	
	private boolean is_sub_user_gateway = false;
	
	private boolean user_time_zone = false;
	
	private boolean is_motion_detection = false;
	
	private boolean show_motion_detection = false;
	
	private boolean show_stored_video = false;
	
	private boolean is_online = false;

	private boolean show_debug = false;
	
	private boolean is_debug = false;
	
	private boolean livetracking_status = false;
	
	private boolean livetracking_person = false;
	
	private boolean livetracking_boundingbox = false;
	
	private boolean livetracking_rotation = false;
	
	private boolean show_live_tracking = false;
	
	private JPetprofileFlutter pet_profile = null;
	
	private String order_date = "NA";
	
	private String model_name = "NA";
	
	private long model_id = 0;
	
	
	public WCDeviceListWeb() {
		super();
	}

	public WCDeviceListWeb(String meid, String gatewayname, String qrcode,
			int remainingwificount, int totalwificount, long gatewayId, long userid, List<WifiInfo> wifiinfolist,
			boolean is_wifidelete, boolean is_wifi_connection, String wifi_pairing_img) {
		super();
		this.meid = meid;
		this.gatewayname = gatewayname;
		this.qrcode = qrcode;
		this.remainingwificount = remainingwificount;
		this.totalwificount = totalwificount;
		this.gatewayid = gatewayId;
		this.userid = userid;
		this.wifiinfolist = wifiinfolist;
		this.is_wifidelete = is_wifidelete;
		this.is_wifi_connection = is_wifi_connection;
		this.wifi_pairing_img = wifi_pairing_img;
	}

	public int getRemainingwificount() {
		return remainingwificount;
	}

	public void setRemainingwificount(int remainingwificount) {
		this.remainingwificount = remainingwificount;
	}

	public long getGatewayid() {
		return gatewayid;
	}

	public void setGatewayid(long gatewayid) {
		this.gatewayid = gatewayid;
	}

	public long getUserid() {
		return userid;
	}

	public void setUserid(long userid) {
		this.userid = userid;
	}

	public String getMeid() {
		return meid;
	}

	public void setMeid(String meid) {
		this.meid = meid;
	}

	public String getGatewayname() {
		return gatewayname;
	}

	public void setGatewayname(String gatewayname) {
		this.gatewayname = gatewayname;
	}

	public String getQrcode() {
		return qrcode;
	}

	public void setQrcode(String qrcode) {
		this.qrcode = qrcode;
	}

	public int getTotalwificount() {
		return totalwificount;
	}

	public void setTotalwificount(int totalwificount) {
		this.totalwificount = totalwificount;
	}

	public List<WifiInfo> getWifiinfolist() {
		return wifiinfolist;
	}

	public void setWifiinfolist(List<WifiInfo> wifiinfolist) {
		this.wifiinfolist = wifiinfolist;
	}

	public String getManualconnect() {
		return manualconnect;
	}

	public void setManualconnect(String manualconnect) {
		this.manualconnect = manualconnect;
	}

	public String getImageurl() {
		return imageurl;
	}

	public void setImageurl(String imageurl) {
		this.imageurl = imageurl;
	}

	public boolean isIs_wifidelete() {
		return is_wifidelete;
	}

	public void setIs_wifidelete(boolean is_wifidelete) {
		this.is_wifidelete = is_wifidelete;
	}

	public String getWifi_ssid() {
		return wifi_ssid;
	}

	public void setWifi_ssid(String wifi_ssid) {
		this.wifi_ssid = wifi_ssid;
	}

	public String getWifi_password() {
		return wifi_password;
	}

	public void setWifi_password(String wifi_password) {
		this.wifi_password = wifi_password;
	}

	public boolean isIs_manual_connection() {
		return is_manual_connection;
	}

	public void setIs_manual_connection(boolean is_manual_connection) {
		this.is_manual_connection = is_manual_connection;
	}

	public boolean isIs_static_wifi() {
		return is_static_wifi;
	}

	public void setIs_static_wifi(boolean is_static_wifi) {
		this.is_static_wifi = is_static_wifi;
	}	
	
	public boolean isIs_wifi_connection() {
		return is_wifi_connection;
	}

	public void setIs_wifi_connection(boolean is_wifi_connection) {
		this.is_wifi_connection = is_wifi_connection;
	}

	public String getWifi_pairing_img() {
		return wifi_pairing_img;
	}

	public void setWifi_pairing_img(String wifi_pairing_img) {
		this.wifi_pairing_img = wifi_pairing_img;
	}

	public boolean isWarranty_claimed() {
		return warranty_claimed;
	}

	public void setWarranty_claimed(boolean warranty_claimed) {
		this.warranty_claimed = warranty_claimed;
	}

	public boolean isPurchased_from_others() {
		return purchased_from_others;
	}

	public void setPurchased_from_others(boolean purchased_from_others) {
		this.purchased_from_others = purchased_from_others;
	}

	public String getWarranty_claim_msg() {
		return warranty_claim_msg;
	}

	public void setWarranty_claim_msg(String warranty_claim_msg) {
		this.warranty_claim_msg = warranty_claim_msg;
	}

	public String getMacid() {
		return macid;
	}

	public void setMacid(String macid) {
		this.macid = macid;
	}

	public boolean isStreaming() {
		return streaming;
	}

	public void setStreaming(boolean streaming) {
		this.streaming = streaming;
	}

	public boolean isNight_vision() {
		return night_vision;
	}

	public void setNight_vision(boolean night_vision) {
		this.night_vision = night_vision;
	}

	public boolean isMic_status() {
		return mic_status;
	}

	public void setMic_status(boolean mic_status) {
		this.mic_status = mic_status;
	}

	public int getSpeaker_volume() {
		return speaker_volume;
	}

	public void setSpeaker_volume(int speaker_volume) {
		this.speaker_volume = speaker_volume;
	}

	public String getQuality() {
		return quality;
	}

	public void setQuality(String quality) {
		this.quality = quality;
	}

	public int getDevice_angle() {
		return device_angle;
	}

	public void setDevice_angle(int device_angle) {
		this.device_angle = device_angle;
	}

	public int getMin_rotation() {
		return min_rotation;
	}

	public void setMin_rotation(int min_rotation) {
		this.min_rotation = min_rotation;
	}

	public int getMax_rotation() {
		return max_rotation;
	}

	public void setMax_rotation(int max_rotation) {
		this.max_rotation = max_rotation;
	}

	public boolean isAuto_update() {
		return auto_update;
	}

	public void setAuto_update(boolean auto_update) {
		this.auto_update = auto_update;
	}

	public String getFota_status() {
		return fota_status;
	}

	public void setFota_status(String fota_status) {
		this.fota_status = fota_status;
	}

	public String getCurrent_fota_version() {
		return current_fota_version;
	}

	public void setCurrent_fota_version(String current_fota_version) {
		this.current_fota_version = current_fota_version;
	}

	public boolean isAuto_night_vision() {
		return auto_night_vision;
	}

	public void setAuto_night_vision(boolean auto_night_vision) {
		this.auto_night_vision = auto_night_vision;
	}

	public boolean isShow_hd() {
		return show_hd;
	}

	public void setShow_hd(boolean show_hd) {
		this.show_hd = show_hd;
	}

	public String getDevice_for() {
		return device_for;
	}

	public void setDevice_for(String device_for) {
		this.device_for = device_for;
	}

	public boolean isIs_sub_user_gateway() {
		return is_sub_user_gateway;
	}

	public void setIs_sub_user_gateway(boolean is_sub_user_gateway) {
		this.is_sub_user_gateway = is_sub_user_gateway;
	}

	public boolean isUser_time_zone() {
		return user_time_zone;
	}

	public void setUser_time_zone(boolean user_time_zone) {
		this.user_time_zone = user_time_zone;
	}

	public boolean isIs_motion_detection() {
		return is_motion_detection;
	}

	public void setIs_motion_detection(boolean is_motion_detection) {
		this.is_motion_detection = is_motion_detection;
	}

	public boolean isShow_motion_detection() {
		return show_motion_detection;
	}

	public void setShow_motion_detection(boolean show_motion_detection) {
		this.show_motion_detection = show_motion_detection;
	}

	public boolean isShow_stored_video() {
		return show_stored_video;
	}

	public void setShow_stored_video(boolean show_stored_video) {
		this.show_stored_video = show_stored_video;
	}

	public boolean isIs_online() {
		return is_online;
	}

	public void setis_online(boolean is_online) {
		this.is_online = is_online;
	}
	public boolean isShow_debug() {
		return show_debug;
	}

	public void setShow_debug(boolean show_debug) {
		this.show_debug = show_debug;
	}

	public boolean isIs_debug() {
		return is_debug;
	}

	public void setIs_debug(boolean is_debug) {
		this.is_debug = is_debug;
	}

	public JPetprofileFlutter getPet_profile() {
		return pet_profile;
	}

	public void setPet_profile(JPetprofileFlutter pet_profile) {
		this.pet_profile = pet_profile;
	}

	public long getMonitortype() {
		return monitortype;
	}

	public void setMonitortype(long monitortype) {
		this.monitortype = monitortype;
	}

	public boolean isLivetracking_status() {
		return livetracking_status;
	}

	public void setLivetracking_status(boolean livetracking_status) {
		this.livetracking_status = livetracking_status;
	}

	public boolean isLivetracking_person() {
		return livetracking_person;
	}

	public void setLivetracking_person(boolean livetracking_person) {
		this.livetracking_person = livetracking_person;
	}

	public boolean isLivetracking_boundingbox() {
		return livetracking_boundingbox;
	}

	public void setLivetracking_boundingbox(boolean livetracking_boundingbox) {
		this.livetracking_boundingbox = livetracking_boundingbox;
	}

	public boolean isLivetracking_rotation() {
		return livetracking_rotation;
	}

	public void setLivetracking_rotation(boolean livetracking_rotation) {
		this.livetracking_rotation = livetracking_rotation;
	}

	public boolean isShow_live_tracking() {
		return show_live_tracking;
	}

	public void setShow_live_tracking(boolean show_live_tracking) {
		this.show_live_tracking = show_live_tracking;
	}

	public void setIs_online(boolean is_online) {
		this.is_online = is_online;
	}

	public String getOrder_date() {
		return order_date;
	}

	public void setOrder_date(String order_date) {
		this.order_date = order_date;
	}

	public String getInstalled_date() {
		return installed_date;
	}

	public void setInstalled_date(String installed_date) {
		this.installed_date = installed_date;
	}

	public String getModel_name() {
		return model_name;
	}

	public void setModel_name(String model_name) {
		this.model_name = model_name;
	}

	public long getModel_id() {
		return model_id;
	}

	public void setModel_id(long model_id) {
		this.model_id = model_id;
	}
	
	
	
}
