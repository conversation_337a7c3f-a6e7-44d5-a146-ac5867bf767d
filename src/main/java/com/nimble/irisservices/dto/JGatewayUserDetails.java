package com.nimble.irisservices.dto;

public class JGatewayUserDetails {
	
	private String gatewayName;
	private long gatewayid;
	private String authkey;
	private String userName;
	private String password;
	private long userid;
	
	

	public JGatewayUserDetails(){
		super();
	}
	
	public JGatewayUserDetails(String gatewayName,
	 String userName,
	 String password,
	 long userid,
	 long gatewayid,
	 String authkey){
		this.gatewayName = gatewayName;
		this.userName = userName;
		this.password=password;
		this.userid=userid;
		this.gatewayid=gatewayid;
		this.authkey=authkey;
	}

	public String getGatewayName() {
		return gatewayName;
	}

	public void setGatewayName(String gatewayName) {
		this.gatewayName = gatewayName;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public long getUserid() {
		return userid;
	}

	public void setUserid(long userid) {
		this.userid = userid;
	}

	public long getGatewayid() {
		return gatewayid;
	}

	public void setGatewayid(long gatewayid) {
		this.gatewayid = gatewayid;
	}

	public String getAuthkey() {
		return authkey;
	}

	public void setAuthkey(String authkey) {
		this.authkey = authkey;
	}
	
	
}
