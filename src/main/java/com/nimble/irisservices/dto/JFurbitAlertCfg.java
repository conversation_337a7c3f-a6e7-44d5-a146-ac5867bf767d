package com.nimble.irisservices.dto;

import java.util.HashSet;
import java.util.Set;

import javax.validation.constraints.Size;

import org.hibernate.validator.constraints.NotEmpty;
import org.hibernate.validator.constraints.Range;

import com.nimble.irisservices.entity.AlertType;
import com.nimble.irisservices.entity.Asset;

public class JFurbitAlertCfg {

	private long id ;
	@NotEmpty(message = "Name should not be empty")
	@Size(min=3,max=25 , message = "Name size must be between 3 and 25")
	private String name;
	private float minval 	= -70.0f;
	private float maxval 	= 170.0f;
	private String rule;
	private double lat 		= 270.0;
	private double lon 		= 270.0;
	private float radius;
	private float fencetype;
	@Range(min=1,max=4)
	private int severity 	= 1;
	private boolean enable 	= false;
	private String levelpattern;
	
	@Range(min=600,max=432000)
	private int notifyfreq 	= 86400;
	private String mobilenos = "";
	private String country	= "NA";
	private String emailids;
	private String alertmsg;
	@Range(min=1,max=1000)
	private int intermittentfreq 	= 1;
	@NotEmpty(message = "Please select notificationtype")
	@Size(min=3,max=6 , message = "notificationtype size must be between 3 and 6")
	private String notificationtype = "000";
	private String alertstarttime;
	private String alertendtime;
	private String voicealertstarttime;
	private String voicealertstoptime;
    private AlertType alerttype;
    private Set<Asset> assets = new HashSet<Asset>();
    private Long[] assetids;
    private long alerttypeid;
    
	public JFurbitAlertCfg() {
		super();
		// TODO Auto-generated constructor stub
	}

	public JFurbitAlertCfg(long id,
			@NotEmpty(message = "Name should not be empty") @Size(min = 3, max = 25, message = "Name size must be between 3 and 25") String name,
			float minval, float maxval, String rule, double lat, double lon, float radius, float fencetype,
			@Range(min = 1, max = 4) int severity, boolean enable, String levelpattern,
			@Range(min = 600, max = 432000) int notifyfreq, String mobilenos, String country, String emailids,
			String alertmsg, @Range(min = 1, max = 1000) int intermittentfreq,
			@NotEmpty(message = "Please select notificationtype") @Size(min = 3, max = 6, message = "notificationtype size must be between 3 and 6") String notificationtype,
			String alertstarttime, String alertendtime, String voicealertstarttime, String voicealertstoptime,
			AlertType alerttype, Set<Asset> assets) {
		super();
		this.id = id;
		this.name = name;
		this.minval = minval;
		this.maxval = maxval;
		this.rule = rule;
		this.lat = lat;
		this.lon = lon;
		this.radius = radius;
		this.fencetype = fencetype;
		this.severity = severity;
		this.enable = enable;
		this.levelpattern = levelpattern;
		this.notifyfreq = notifyfreq;
		this.mobilenos = mobilenos;
		this.country = country;
		this.emailids = emailids;
		this.alertmsg = alertmsg;
		this.intermittentfreq = intermittentfreq;
		this.notificationtype = notificationtype;
		this.alertstarttime = alertstarttime;
		this.alertendtime = alertendtime;
		this.voicealertstarttime = voicealertstarttime;
		this.voicealertstoptime = voicealertstoptime;
		this.alerttype = alerttype;
		this.assets = assets;
//		this.assetids = assetids;
//		this.alerttypeid = alerttypeid;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public float getMinval() {
		return minval;
	}

	public void setMinval(float minval) {
		this.minval = minval;
	}

	public float getMaxval() {
		return maxval;
	}

	public void setMaxval(float maxval) {
		this.maxval = maxval;
	}

	public String getRule() {
		return rule;
	}

	public void setRule(String rule) {
		this.rule = rule;
	}

	public double getLat() {
		return lat;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public double getLon() {
		return lon;
	}

	public void setLon(double lon) {
		this.lon = lon;
	}

	public float getRadius() {
		return radius;
	}

	public void setRadius(float radius) {
		this.radius = radius;
	}

	public float getFencetype() {
		return fencetype;
	}

	public void setFencetype(float fencetype) {
		this.fencetype = fencetype;
	}

	public int getSeverity() {
		return severity;
	}

	public void setSeverity(int severity) {
		this.severity = severity;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public String getLevelpattern() {
		return levelpattern;
	}

	public void setLevelpattern(String levelpattern) {
		this.levelpattern = levelpattern;
	}

	public int getNotifyfreq() {
		return notifyfreq;
	}

	public void setNotifyfreq(int notifyfreq) {
		this.notifyfreq = notifyfreq;
	}

	public String getMobilenos() {
		return mobilenos;
	}

	public void setMobilenos(String mobilenos) {
		this.mobilenos = mobilenos;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getEmailids() {
		return emailids;
	}

	public void setEmailids(String emailids) {
		this.emailids = emailids;
	}

	public String getAlertmsg() {
		return alertmsg;
	}

	public void setAlertmsg(String alertmsg) {
		this.alertmsg = alertmsg;
	}

	public int getIntermittentfreq() {
		return intermittentfreq;
	}

	public void setIntermittentfreq(int intermittentfreq) {
		this.intermittentfreq = intermittentfreq;
	}

	public String getNotificationtype() {
		return notificationtype;
	}

	public void setNotificationtype(String notificationtype) {
		this.notificationtype = notificationtype;
	}

	public String getAlertstarttime() {
		return alertstarttime;
	}

	public void setAlertstarttime(String alertstarttime) {
		this.alertstarttime = alertstarttime;
	}

	public String getAlertendtime() {
		return alertendtime;
	}

	public void setAlertendtime(String alertendtime) {
		this.alertendtime = alertendtime;
	}

	public String getVoicealertstarttime() {
		return voicealertstarttime;
	}

	public void setVoicealertstarttime(String voicealertstarttime) {
		this.voicealertstarttime = voicealertstarttime;
	}

	public String getVoicealertstoptime() {
		return voicealertstoptime;
	}

	public void setVoicealertstoptime(String voicealertstoptime) {
		this.voicealertstoptime = voicealertstoptime;
	}

	public AlertType getAlerttype() {
		return alerttype;
	}

	public void setAlerttype(AlertType alerttype) {
		this.alerttype = alerttype;
	}

	public Set<Asset> getAssets() {
		return assets;
	}

	public void setAssets(Set<Asset> assets) {
		this.assets = assets;
	}

	public Long[] getAssetids() {
		return assetids;
	}

	public void setAssetids(Long[] assetids) {
		this.assetids = assetids;
	}

	public long getAlerttypeid() {
		return alerttypeid;
	}

	public void setAlerttypeid(long alerttypeid) {
		this.alerttypeid = alerttypeid;
	}

    
}
