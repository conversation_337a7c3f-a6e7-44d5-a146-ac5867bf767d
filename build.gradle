buildscript {
    repositories {
       // maven { url "https://repo.spring.io/libs-release" }
        maven { url "https://repo.spring.io/milestone" }
        mavenLocal()
        mavenCentral()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:2.1.3.RELEASE")
    }
}
apply plugin: 'java'
apply plugin: 'war'
apply plugin: 'jaco<PERSON>'
apply plugin: 'eclipse'
apply plugin: 'org.springframework.boot'
sourceCompatibility = '1.8'
targetCompatibility = '1.8'
[compileJava, compileTestJava]*.options*.encoding = 'UTF-8'
//wrapper(type: Wrapper) {
//  gradleVersion = '5.6.2'
//}
war {
    enabled = true
    baseName = 'irisservices'
    //version =  '3.0.0-SNAPSHOT'
}
if (!hasProperty('mainClass')) {
    ext.mainClass = 'com.nimble.irisservices.Application'
}
repositories {
	mavenCentral()
}
dependencies {
   // Upgrade all the dependencies to Latest version
    compile group: 'org.springframework.boot', name: 'spring-boot-starter-web', version: '2.1.3.RELEASE'
    compile group: 'org.springframework.boot', name: 'spring-boot-starter-web-services', version: '2.1.3.RELEASE'
    compile group: 'org.springframework.boot', name: 'spring-boot-starter-aop', version: '2.1.3.RELEASE'
	compile group: 'org.springframework', name: 'spring-orm', version: '4.3.17.RELEASE'
    compile group: 'org.springframework.boot', name: 'spring-boot-starter-freemarker', version: '2.1.3.RELEASE'
    compile group: 'org.hibernate', name: 'hibernate-core', version: '4.3.6.Final'
    compile group: 'mysql', name: 'mysql-connector-java', version: '8.0.33'
    //compile group: 'com.mysema.querydsl', name: 'querydsl-hql', version: '1.8.2'
    compile group: 'commons-lang', name: 'commons-lang', version: '2.4'
    compile group: 'org.apache.commons', name: 'commons-io', version: '1.3.2'                   	         
	compile group: 'org.apache.commons', name: 'commons-lang3', version: '3.1'
    compile group: 'org.apache.commons', name: 'commons-io', version: '1.3.2'
  	compile group: 'org.apache.httpcomponents', name: 'httpclient', version: '4.5.2'
  	compile group: 'org.apache.httpcomponents', name: 'httpcore', version: '4.4.4'  	
  	compile group: 'commons-codec', name: 'commons-codec', version: '1.6'
    compile group: 'org.apache.logging.log4j', name: 'log4j-api', version: '2.17.1'
    compile group: 'org.apache.logging.log4j', name: 'log4j-core', version: '2.17.1'
    compile group: 'org.apache.logging.log4j', name: 'log4j-web', version: '2.17.1'
  	compile group: 'org.apache.poi', name: 'poi', version: '3.6'	
	//junit
	testCompile group: 'org.springframework.boot', name: 'spring-boot-starter-test', version: '2.1.3.RELEASE'
    testCompile group: 'org.junit.jupiter', name: 'junit-jupiter-params', version: '5.7.0'
	testCompile group: 'org.junit.jupiter', name: 'junit-jupiter-engine', version: '5.7.0'
	testCompile group: 'org.junit.platform', name: 'junit-platform-engine', version: '1.7.0'
	testCompile group: 'org.junit.platform', name: 'junit-platform-launcher', version: '1.7.0'
	testCompile group: 'org.junit.platform', name: 'junit-platform-runner', version: '1.7.0'
	testCompile group: 'org.junit.platform', name: 'junit-platform-surefire-provider', version: '1.3.2'
	testCompile 'org.springframework.boot:spring-boot-starter-test:2.1.3.RELEASE'
	testRuntime('org.junit.vintage:junit-vintage-engine:5.7.0')	
	//compile group: 'log4j', name: 'log4j', version: '1.2.16'
	compile group: 'com.google.code.gson', name: 'gson', version: '2.2.2'
    compile group: 'com.lmax', name: 'disruptor', version: '3.4.2'
    compile group: 'com.mchange', name: 'c3p0', version: '0.9.5'
	compile group: 'org.jvnet.mimepull', name: 'mimepull', version: '1.9.10'
  	compile group: 'org.json', name: 'json', version: '20140107'
  	compile group: 'org.ocpsoft.prettytime', name: 'prettytime', version: '3.2.7.Final'
  	compile group: 'org.codehaus.jackson', name: 'jackson-mapper-asl', version: '1.9.12'
  	  	
  	compile group: 'com.sun.mail', name: 'javax.mail', version: '1.6.2'
  	
  	compile group: 'com.twilio.sdk', name: 'twilio', version: '7.21.6'
  	compile group: 'com.plivo', name: 'plivo-java', version: '4.8.0'
  	
  	compile group: 'com.chargebee', name: 'chargebee-java', version: '2.7.4'
  	
  	compile group: 'com.google.maps', name: 'google-maps-services', version: '0.10.2'
  	
  	//Amazon AWS Dependencies
  	compile group: 'com.amazonaws', name: 'aws-java-sdk-s3', version: '1.11.376'  	
  	compile group: 'com.amazonaws', name: 'aws-java-sdk-sqs', version: '1.11.106'  	
  	compile group: 'com.amazonaws', name: 'aws-java-sdk-core',version:'1.11.376'
    compile group: 'com.amazonaws', name: 'aws-java-sdk-pinpoint', version: '1.12.210'
	compile 'software.amazon.awssdk:secretsmanager:2.20.81'
	compile 'software.amazon.awssdk:auth:2.20.81'

  	compile group: 'com.google.firebase', name: 'firebase-admin', version: '9.2.0'
  	
  	compile group: 'com.google.guava', name:'guava', version:'28.0-jre'
	
	//Apple signin

	compile group:'org.bouncycastle', name: 'bcpkix-jdk15on',version:'1.63'

	compile group:'io.jsonwebtoken', name: 'jjwt-api',version:'0.10.7'
	compile group:'io.jsonwebtoken', name: 'jjwt-impl',version:'0.10.7'
	compile group:'io.jsonwebtoken', name: 'jjwt-jackson',version:'0.10.7'

	compile group: 'com.mashape.unirest',name: 'unirest-java', version: '1.4.9'
	compile group: 'org.apache.httpcomponents',name: 'httpclient' , version: '4.3.6'
	compile group: 'org.apache.httpcomponents',name: 'httpasyncclient', version: '4.0.2'
	
	// Dependencies for Quartz
	compile group: 'org.quartz-scheduler', name: 'quartz', version: '2.3.0'
	compile group: 'org.quartz-scheduler', name: 'quartz-jobs', version: '2.3.0'
	
	// Dependencies for spring security
	//compile group: 'org.springframework.security.oauth', name: 'spring-security-oauth2', version: '2.5.1.RELEASE'
	compile group: 'org.springframework.cloud', name: 'spring-cloud-starter-oauth2', version: '2.1.3.RELEASE'
	compile group: 'org.springframework.security', name: 'spring-security-oauth2-resource-server', version: '5.6.1'

	compile group: 'org.springframework.boot', name: 'spring-boot-starter-security', version: '2.1.3'
	compile group: 'org.springframework.security', name: 'spring-security-jwt', version: '1.1.1.RELEASE'
	compile group: 'org.springframework.boot', name: 'spring-boot-starter-jdbc', version: '2.1.3.RELEASE'
	compile group: 'org.springframework.boot', name: 'spring-boot-starter-thymeleaf', version: '2.1.3.RELEASE'
	//Dependency for sensorreport csv to email.......
	compile group: 'com.opencsv', name: 'opencsv', version: '4.1'

	compile group: 'io.springfox', name: 'springfox-boot-starter', version: '3.0.0'
	compile group: 'io.springfox', name: 'springfox-swagger2', version: '3.0.0'
	compile group: 'io.springfox', name: 'springfox-swagger-ui', version: '3.0.0'

	// Dependencies for Alexa
	compile ("com.amazon.alexa:alexa-skills-kit:1.8.1") {
	    exclude group: 'org.eclipse.jetty:jetty-server'
	    exclude group: 'org.slf4j:slf4j-api'
	    exclude group: 'org.slf4j:slf4j-log4j12'
	    exclude group: 'log4j:log4j'
	}	
	providedCompile group: 'javax.servlet', name: 'javax.servlet-api', version: '4.0.1'

	compile 'com.github.ben-manes.caffeine:caffeine:2.9.3'

	compile group: 'com.nimbusds', name: 'nimbus-jose-jwt', version: '10.3'

	testImplementation 'org.springframework.security:spring-security-test:5.1.4.RELEASE'
}

configurations.all {
 exclude module: 'log4j-to-slf4j'
 exclude module: 'slf4j-log4j12'
 exclude module: 'junit'
 exclude module: 'junit-vintage-engine'
}

test {
	//exclude '**/*'
    useJUnitPlatform()
	maxHeapSize = "512m"

	// Optimize parallel execution
	maxParallelForks = Math.min(3, Runtime.runtime.availableProcessors())
	forkEvery = 50

	testLogging {
		events "passed", "skipped", "failed"
		exceptionFormat "full"
		showStandardStreams = false
	}

	finalizedBy jacocoTestReport
}

// To improve build performance
gradle.projectsEvaluated {
	tasks.withType(JavaCompile).tap {
		configureEach {
			options.fork = true
			options.forkOptions.jvmArgs << '-Xmx512m'
		}
	}
}

jacoco {
	toolVersion = '0.8.12'
}

jacocoTestReport {
	dependsOn test

	reports {
		html.enabled = true
		html.destination file("$buildDir/reports/jacoco/html")
	}
}
